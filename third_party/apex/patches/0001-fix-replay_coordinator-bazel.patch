commit 868ef67f6c13dfa0ebb8a979cb577c332f203e9f
Author: <PERSON><PERSON> <<EMAIL>>
Date:   Tue <PERSON> 10 21:19:45 2025 -0700

    [56] Update replay coordinator fix

diff --git a/grace/recording/replay/BUILD.bazel b/grace/recording/replay/BUILD.bazel
index f5df4a2c..aa897aa5 100644
--- a/grace/recording/replay/BUILD.bazel
+++ b/grace/recording/replay/BUILD.bazel
@@ -1,5 +1,9 @@
 load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
 load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
+load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_binary")
+load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
+load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
+load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library")
 load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
 load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
 load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
@@ -97,7 +101,7 @@ apex_cc_library(
 )
 
 cc_binary(
-    name = "replay_coordinator",
+    name = "replay_coordinator_exe",
     srcs = ["src/coordinator_main.cpp"],
     tags = ["exclude_sca"],
     target_compatible_with = [
@@ -124,6 +128,39 @@ cc_binary(
     ],
 )
 
+msgs_library(
+    name = "interfaces_with_ament_resources",
+    ament_runfiles = True,
+    deps = ["//grace/interfaces"],
+)
+
+py_msgs_library(
+    name = "py_interfaces",
+    msgs = ":interfaces_with_ament_resources",
+)
+
+cpp_msgs_introspection_library(
+    name = "cpp_interfaces",
+    ament_runfiles = True,
+    msgs = ":interfaces_with_ament_resources",
+)
+
+configured_binary(
+    name = "replay_coordinator",
+    data = [
+        ":cpp_interfaces",
+        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
+        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
+    ],
+    executable = ":replay_coordinator_exe",
+    visibility = ["//visibility:public"],
+    deps = [
+        ":py_interfaces",
+        "@apex//grace/recording/rosbag2/rosbag2_storage_mcap:ros2bag_mcap_cli",
+        "@apex//grace/recording/rosbag2/rosbag2_storage_sqlite3:ros2bag_sqlite3_cli",
+    ],
+)
+
 apex_cc_test(
     name = "test_replay_misc",
     srcs = [
diff --git a/grace/recording/replay/src/coordinator_data_source_rosbag.cpp b/grace/recording/replay/src/coordinator_data_source_rosbag.cpp
index 3cb1510f..7e6d333e 100644
--- a/grace/recording/replay/src/coordinator_data_source_rosbag.cpp
+++ b/grace/recording/replay/src/coordinator_data_source_rosbag.cpp
@@ -79,7 +79,7 @@ void coordinator_data_source_rosbag::create()
 
   StorageOptions storage_options{};
   storage_options.uri = m_rosbag_path;
-  storage_options.storage_id = "sqlite3";
+  storage_options.storage_id = "";
 
   m_player = std::make_unique<Player>(storage_options, play_options);
   m_player->pause();
