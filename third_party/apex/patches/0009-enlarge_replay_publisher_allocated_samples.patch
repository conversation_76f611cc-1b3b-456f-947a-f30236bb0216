diff --git a/grace/recording/rosbag2/rosbag2_transport/src/rosbag2_transport/player.cpp b/grace/recording/rosbag2/rosbag2_transport/src/rosbag2_transport/player.cpp
index 38583fdc..9a032cbf 100644
--- a/grace/recording/rosbag2/rosbag2_transport/src/rosbag2_transport/player.cpp
+++ b/grace/recording/rosbag2/rosbag2_transport/src/rosbag2_transport/player.cpp
@@ -1577,7 +1577,8 @@ void PlayerImpl::prepare_publishers()
         owner_->get_logger());
       try {
         std::shared_ptr<rclcpp::GenericPublisher> pub =
-          owner_->create_generic_publisher(topic.name, topic.type, topic_qos);
+          owner_->create_generic_publisher(topic.name, topic.type,
+           topic_qos.resource_limits_max_allocated_samples(128));
         std::shared_ptr<PlayerPublisher> player_pub =
           std::make_shared<PlayerPublisher>(std::move(pub), play_options_.disable_loan_message);
         publishers_.insert(std::make_pair(topic.name, player_pub));
