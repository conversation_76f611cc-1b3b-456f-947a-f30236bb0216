commit 580bac01d95bede66d602a0a37b7f9be3e269d58
Author: <PERSON><PERSON> <<EMAIL>>
Date:   Thu Jun 19 16:17:49 2025 -0700

    [61] Fix waitset_and_timer example

diff --git a/grace/examples/executor_examples/src/waitset/waitset_and_timer.cpp b/grace/examples/executor_examples/src/waitset/waitset_and_timer.cpp
index 0d5b3506..ed2af0ac 100644
--- a/grace/examples/executor_examples/src/waitset/waitset_and_timer.cpp
+++ b/grace/examples/executor_examples/src/waitset/waitset_and_timer.cpp
@@ -107,6 +107,7 @@ void example()
   dw::Waitset ws;
 
   ws.add(timer->to_sub_ptr(), [&] {
+    timer->test_and_reset();
     node1.execute();
     node2.execute();
   });  // (7)!
