load("//common/integrity:defs.bzl", "license_policy")
load("//common/integrity:defs.bzl", "apex_quality_level")
load("//tools/bazel/rules_docs:defs.bzl", "bzl_docs")

# ""
package(
    default_applicable_licenses = [],
    default_visibility = ["//visibility:public"],
)

bzl_docs(
    name = "bzl_docs",
    srcs = [
        # do not sort
        "provider.bzl",
        "defs.bzl",
    ],
    detail_srcs = [
        "//common/integrity/detail:bzl_files",
        "@rules_license//:docs_deps",
    ],
)

apex_quality_level(
    name = "unspecified",
    conditions = [
        "apex_integrity.type.none",
    ],
)

embedded_bronze = apex_quality_level(
    name = "embedded_bronze",
    conditions = [
        "apex_integrity.type.embedded",
    ],
)

license_policy(
    name = "embedded_bronze_policy",
    conditions = [
        "apex_integrity.type.embedded",
    ],
)

embedded_silver = apex_quality_level(
    name = "embedded_silver",
    conditions = [
        "apex_integrity.documentation.basic",
        "apex_integrity.dependencies.tps_tracked",
        "apex_integrity.release.vulnerabilities_gated",
    ],
    extends = [embedded_bronze],
)

license_policy(
    name = "embedded_silver_policy",
    conditions = [
        "apex_integrity.type.embedded",
        "apex_integrity.documentation.basic",
        "apex_integrity.release.tps_tracked",
        "apex_integrity.release.vulnerabilities_gated",
    ],
)

embedded_gold = apex_quality_level(
    name = "embedded_gold",
    conditions = [
        "apex_integrity.documentation.complete",
        "apex_integrity.traceability.complete",
        "apex_integrity.code_analysis.no_medium_sca_violations",
        "apex_integrity.code_analysis.no_sanitizer_issues",
        "apex_integrity.testing.regular_tests",
        "apex_integrity.testing.basic_line_coverage",
        "apex_integrity.dependencies.tps_managed",
        "apex_integrity.release.slsa_level_1",
        "apex_integrity.release.lts",
        "apex_integrity.release.vulnerabilities_managed",
    ],
    extends = [embedded_silver],
)

license_policy(
    name = "embedded_gold_policy",
    conditions = [
        "apex_integrity.type.embedded",
        "apex_integrity.documentation.complete",
        "apex_integrity.traceability.complete",
        "apex_integrity.code_analysis.no_medium_sca_violations",
        "apex_integrity.code_analysis.no_sanitizer_issues",
        "apex_integrity.testing.regular_tests",
        "apex_integrity.testing.basic_line_coverage",
        "apex_integrity.dependencies.tps_managed",
        "apex_integrity.release.slsa_level_1",
        "apex_integrity.release.lts",
        "apex_integrity.release.vulnerabilities_managed",
    ],
)

embedded_platinum = apex_quality_level(
    name = "embedded_platinum",
    conditions = [
        "apex_integrity.code_analysis.justified_sca_violations",
        "apex_integrity.testing.fault_injection",
        "apex_integrity.testing.basic_branch_coverage",
        "apex_integrity.dependencies.restricted_stl",
    ],
    extends = [embedded_gold],
)

license_policy(
    name = "embedded_platinum_policy",
    conditions = [
        "apex_integrity.type.embedded",
        "apex_integrity.documentation.complete",
        "apex_integrity:tps.tracked",
        "apex_integrity.traceability.complete",
        "apex_integrity.code_analysis.justified_sca_violations",
        "apex_integrity.code_analysis.no_sanitizer_issues",
        "apex_integrity.testing.regular_tests",
        "apex_integrity.testing.basic_line_coverage",
        "apex_integrity.testing.fault_injection",
        "apex_integrity.testing.basic_branch_coverage",
        "apex_integrity.dependencies.tps_managed",
        "apex_integrity.dependencies.restricted_stl",
        "apex_integrity.release.slsa_level_1",
        "apex_integrity.release.lts",
        "apex_integrity.release.vulnerabilities_managed",
    ],
)

embedded_diamond = apex_quality_level(
    name = "embedded_diamond",
    conditions = [
        "apex_integrity.testing.mc_dc_coverage",
        "apex_integrity.dependencies.no_stl",
    ],
    extends = [embedded_platinum],
)

license_policy(
    name = "embedded_diamond_policy",
    conditions = [
        "apex_integrity.type.embedded",
        "apex_integrity.documentation.complete",
        "apex_integrity:tps.tracked",
        "apex_integrity.traceability.complete",
        "apex_integrity.code_analysis.justified_sca_violations",
        "apex_integrity.code_analysis.no_sanitizer_issues",
        "apex_integrity.testing.regular_tests",
        "apex_integrity.testing.basic_line_coverage",
        "apex_integrity.testing.fault_injection",
        "apex_integrity.testing.basic_branch_coverage",
        "apex_integrity.testing.mc_dc_coverage",
        "apex_integrity.dependencies.tps_managed",
        "apex_integrity.dependencies.no_stl",
        "apex_integrity.release.slsa_level_1",
        "apex_integrity.release.lts",
        "apex_integrity.release.vulnerabilities_managed",
    ],
)

apex_quality_level(
    name = "no_code",
    conditions = [
        "apex_integrity.skip.no_code",
    ],
)

apex_quality_level(
    name = "detail",
    conditions = [
        "apex_integrity.skip.detail",
    ],
)

apex_quality_level(
    name = "generated_code",
    conditions = [
        "apex_integrity.skip.generated_code",
    ],
)

license_policy(
    name = "skip_policy",
    conditions = [
        "apex_integrity.skip.detail",
        "apex_integrity.skip.no_code",
        "apex_integrity.skip.generated_code",
    ],
)
