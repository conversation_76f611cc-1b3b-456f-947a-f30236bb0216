load("@apex//common/integrity/detail:license_compliance_report.bzl", _license_compliance_report = "license_compliance_report")
load("@apex//common/integrity/detail:license_policy.bzl", _license_policy = "license_policy")
load("@rules_license//rules:license.bzl", "license")
load("@rules_license//rules:license_kind.bzl", "license_kind")

# rules_license based rules
license_compliance_report = _license_compliance_report
license_policy = _license_policy

_QUALITY_CONDITIONS = dict(
    type = [
        "embedded",
        "cloud",
        "sdk_tool",
        "documentation",
        "internal",
        "release",
        "none",
    ],
    documentation = [
        "basic",
        "complete",
    ],
    testing = [
        "regular_tests",
        "fault_injection",
        "basic_line_coverage",
        "basic_branch_coverage",
        "mc_dc_coverage",
    ],
    code_analysis = [
        "no_medium_sca_violations",
        "justified_sca_violations",
        "no_sca_violations",
        "no_sanitizer_issues",
    ],
    dependencies = [
        "restricted_stl",
        "no_stl",
        "tps_tracked",
        "tps_managed",
    ],
    traceability = [
        "complete",
    ],
    release = [
        "vulnerabilities_gated",
        "vulnerabilities_managed",
        "slsa_level_1",
        "lts",
    ],
    skip = [
        "no_code",
        "detail",
        "generated_code",
    ],
)

_ERROR_MSG = """
Condition must be of format "apex_integrity.<category>.<condition>"
with <category> from {categories}.{more}
Got "{condition}" instead"""

_ERROR_MSG2 = """
and <condition> from {conditions}."""

def _validate(condition):
    """Ensures that the condition has the required format.

    The format is "apex_integrity.<type>.<condition>".

    Arguments:
        condition: string to be validated.
    """
    parts = condition.split(".")
    if len(parts) != 3 or parts[0] != "apex_integrity" or parts[1] not in _QUALITY_CONDITIONS:
        fail(_ERROR_MSG.format(categories = _QUALITY_CONDITIONS.keys(), more = "", condition = condition))
    if parts[2] not in _QUALITY_CONDITIONS[parts[1]]:
        fail(_ERROR_MSG.format(
            categories = _QUALITY_CONDITIONS.keys(),
            more = _ERROR_MSG2.format(conditions = _QUALITY_CONDITIONS[parts[1]]),
            condition = condition,
        ))
    return condition

def apex_quality_level(
        *,
        name,
        conditions,
        extends = None,
        **kwargs):
    """Quality level helper.

    This macro validates the license conditions and then creates two targets,
    a `license_kind` target and a `license` target. The name of the license_kind
    target will be "Apex.AI-<name>" where <name> is given as parameter.
    It returns a struct of the form:
    ```
    struct(
       name = <name>,
       conditions = <list_of_conditions>,
    )
    ```
    The return value can be forwarded to other apex_quality_level targets that extend this quality level.

    Arguments:
      name: name of the license target
      conditions: List of license condition strings
      extends: an optional list of structs in the form as returned by this macro
      kwargs: additional keyword parameters that will be forwarded to both, the license and license_kind rule
    """
    license_kind_name = "Apex.AI-{}".format(name.replace("_", "-"))
    conditions = [_validate(cond) for cond in conditions]
    for ext in extends or []:
        conditions.extend(ext.conditions)
    license_kind(
        name = license_kind_name,
        conditions = conditions,
        **kwargs
    )
    license(
        name = name,
        license_kind = license_kind_name,
        **kwargs
    )
    return struct(
        name = license_kind_name,
        conditions = conditions,
    )
