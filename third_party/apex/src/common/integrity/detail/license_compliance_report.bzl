load("@rules_license//rules:gather_licenses_info.bzl", "gather_licenses_info")
load("@rules_license//rules:providers.bzl", "TransitiveLicensesInfo")
load("//common/integrity:provider.bzl", "LicensePolicyInfo")
load("//common/integrity:provider.bzl", "LicenseComplianceInfo")

def _flatten(list_of_tuples):
    flat_list = []
    for item in list_of_tuples:
        flat_list.extend(item)
    return flat_list

def _make_unique(list_with_duplicates):
    return {k: None for k in list_with_duplicates}.keys()

def get_all_conditions(licenses, all_licenses):
    licenses = licenses.split(",")
    license_kinds = _flatten([item.license_kinds for item in all_licenses if str(item.label) in licenses])
    return _make_unique(_flatten([lc.conditions for lc in license_kinds]))

def _license_compliance_report_impl(ctx):
    """Implementation function of the license_compliance_report rule."""
    targets_ok = []
    targets_not_ok = {}
    policy_conditions = ctx.attr.policy[LicensePolicyInfo].conditions
    for target in ctx.attr.targets:
        all_licenses = target[TransitiveLicensesInfo].licenses.to_list()
        for dep in target[TransitiveLicensesInfo].deps.to_list():
            all_conditions = get_all_conditions(dep.licenses, all_licenses)
            if any([condition in ctx.attr._skip_policy[LicensePolicyInfo].conditions for condition in all_conditions]):
                continue
            mc = [c for c in policy_conditions if c not in all_conditions]

            if len(mc) > 0:
                targets_not_ok[str(dep.target_under_license)] = mc
            else:
                targets_ok.append(str(dep.target_under_license))

    if ctx.attr.out:
        output = ctx.attr.out
    else:
        output = ctx.actions.declare_file(ctx.label.name + ".json")

    result = {"compliant": targets_ok, "unmet_conditions": targets_not_ok}
    ctx.actions.write(output, content = json.encode_indent(result))

    providers = [
        DefaultInfo(files = depset([output])),
        LicenseComplianceInfo(policy = ctx.attr.policy[LicensePolicyInfo], **result),
    ]

    if ctx.attr.fail_on_violation and len(targets_not_ok):
        validation_output_name = ctx.label.name + "_validate.txt"
        validation_output = ctx.actions.declare_file(validation_output_name)
        ctx.actions.run_shell(
            outputs = [validation_output],
            command = "echo 'Found non-compliant dependencies' > {}; exit 1".format(validation_output.path),
        )
        providers.append(OutputGroupInfo(_validation = depset([validation_output])))

    return providers

license_compliance_report = rule(
    doc = """Validate code quality compliance.

        Generate a report on compliance of all targets with respect to the
        required policy. Can be configured to fail on non-compliance.
    """,
    implementation = _license_compliance_report_impl,
    attrs = {
        "out": attr.output(
            doc = "Optional output file name (Default is `name`+'.json')",
        ),
        "targets": attr.label_list(
            doc = """List of targets that shall be checked for compliance. All dependencies of these
            targets will also be checked.""",
            aspects = [gather_licenses_info],
        ),
        "policy": attr.label(
            doc = "A license_policy target, defining required conditions (see license_kind).",
            mandatory = True,
            providers = [LicensePolicyInfo],
        ),
        "_skip_policy": attr.label(
            default = "@apex//common/integrity:skip_policy",
        ),
        "fail_on_violation": attr.bool(
            doc = """Bool, if set to true will trigger a build failure in case a non-compliant
                dependency is found.""",
            default = True,
        ),
    },
)
