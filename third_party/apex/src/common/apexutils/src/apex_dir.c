// Copyright 2017-2018 Apex.AI, Inc.
// All rights reserved.

//lint -emacro(*, S_ISDIR) NOLINT GCC's S_ISDIR uses octals and hex w/o 'U' suffix
//lint -emacro(*, S_ISREG) NOLINT GCC's S_ISREG uses octals and hex w/o 'U' suffix

#include <sys/types.h>
#include <sys/stat.h>
#include <errno.h>

#include "apexutils/apex_dir.h"

#ifdef APEX_WINDOWS
  #include <windows.h>
  #include <direct.h>
  #define S_ISDIR(mode) (_S_IFDIR == ((mode) & _S_IFDIR))
  #define S_ISREG(mode) (_S_IFREG == ((mode) & _S_IFREG))
#elif defined(APEX_LINUX) || defined(APEX_OSX) || defined(APEX_QNX)
  #include <unistd.h>
#endif  // APEX_WINDOWS

#include "apexutils/apexutils.h"

///////////////////////////////////////////////////////////////////////////////
apex_ret_t apex_chdrive(const char * const disk_name)
{
  apex_ret_t retval = APEX_RET_ERROR;
  if (NULL != disk_name) {
#ifdef APEX_WINDOWS
    int32_t const letter_A_code = (int32_t)'A';
    int32_t const disk_letter_code = toupper((int32_t)(*disk_name));
    int const disk_number = (disk_letter_code - letter_A_code) + 1;
    if (0 == _chdrive(disk_number)) {
      retval = APEX_RET_OK;
    }
#else  // APEX_WINDOWS
    retval = APEX_RET_OK;
#endif  // APEX_WINDOWS
  }
  return retval;
}

apex_ret_t apex_chdir(const char * const dir_name)
{
  apex_ret_t retval = APEX_RET_ERROR;
  if (NULL != dir_name) {
    if (0 == chdir(dir_name)) {
      retval = APEX_RET_OK;
    }
  }
  return retval;
}

apex_ret_t apex_mkdir(const char * const dir_name)
{
  apex_ret_t retval = APEX_RET_ERROR;
  if (NULL != dir_name) {
#ifdef APEX_WINDOWS
    int32_t ret = mkdir(dir_name);
#else  // APEX_WINDOWS
       // GCC's S_IRWXU | S_IRWXG | S_IRWXO constants are not MISRA-compatible
       // 0777 is also prohibited by MISRA
       // 511 = 0777, MISRA prohibits octal constants
    int32_t ret = mkdir(dir_name, 511U);
#endif  // APEX_WINDOWS
    if (0 == ret) {
      retval = APEX_RET_OK;
    } else {
      /*
       AXIVION Next Line MisraC2023-22.10: defensive code.
       */
      if (EEXIST == errno) {
        retval = APEX_RET_RESOURCE_EXIST;
      }
    }
  }
  return retval;
}

apex_ret_t apex_rmdir(const char * const dir_name)
{
  apex_ret_t retval = APEX_RET_ERROR;
  if (NULL != dir_name) {
    if (0 == rmdir(dir_name)) {
      retval = APEX_RET_OK;
    }
  }
  return retval;
}

bool apex_is_dir_present(const char * const dir_name)
{
  bool retval = false;
  if (NULL != dir_name) {
    struct stat s;
    if (0 == stat(dir_name, &s)) {
      /*
       AXIVION Next Line MisraC2023-7.1, MisraC2023-10.1, MisraC2023-10.4:
       Reason: Code Quality (Functional suitability), Justification:
       MACRO is defined in stat.h linux library is accepted.
       */
      if (S_ISDIR(s.st_mode)) {
        retval = true;
      }
    }
  }
  return retval;
}
