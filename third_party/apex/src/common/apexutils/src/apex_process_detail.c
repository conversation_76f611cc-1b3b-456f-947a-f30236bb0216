// Copyright 2019 Apex.AI, Inc.
// All rights reserved.

#include <string.h>
#include <errno.h>
#include "apexutils/apexutils.h"

#ifdef APEX_WINDOWS
#include <windows.h>
#elif defined(APEX_LINUX) || defined(APEX_OSX) || defined(APEX_QNX)
#include <stdlib.h>
#else  // defined(APEX_LINUX)
#error unsupported OS
#endif  // APEX_WINDOWS

#include "apexutils/apex_process_detail.h"

#ifndef APEXUTILS_TEST_DIR
#pragma message("APEXUTILS_TEST_DIR is not defined")
#define APEXUTILS_TEST_DIR "/tmp/"
#endif


/*
 AXIVION Next Line MisraC2023-5.9: Reason: Code Quality (Functional suitability), Justification:
 static g_failure_mode variables are intentionally used
 */
APEX_DEFINE_FAILURE_INJECTION(apex_process_detail)

apex_ret_t get_cmdline_with_no_spaces(
  const char * const cmdline_string,
  char * const cmdline_no_spaces_buf,
  const size_t max_len)
{
  apex_ret_t ret = APEX_RET_ERROR;

  if ((cmdline_string != NULL) &&
    (cmdline_no_spaces_buf != NULL) &&
    (max_len <= APEX_PROCESS_CMDLINE_SIZE) &&
    (max_len > 0U))
  {
    // get length of the cmdline string
    size_t cmdline_len = strnlen(cmdline_string, max_len - 1U);

    // if the process is not zombie
    // /proc/[pid]/cmdline
    // holds the complete command line for the process, unless the process is a zombie. In the
    // latter case, there is nothing in this file: that is, a read on this file will return 0
    // characters.  (http://man7.org/linux/man-pages/man5/proc.5.html)
    if (cmdline_len > 0U) {
      // get the cmdline with out spaces
      uint32_t i = 0;
      uint32_t j = 0;

      while (i != cmdline_len) {
        if (cmdline_string[i] != ' ') {
          cmdline_no_spaces_buf[j] = cmdline_string[i];
          j++;
        }
        i++;
      }

      cmdline_no_spaces_buf[j] = '\0';
      ret = APEX_RET_OK;
    }
  }

  return ret;
}

apex_ret_t get_cmdline_with_no_params(
  const char * const cmdline_string,
  char * const cmdline_string_no_params_buff,
  const size_t max_len)
{
  apex_ret_t ret = APEX_RET_ERROR;

  if ((cmdline_string != NULL) &&
    (cmdline_string_no_params_buff != NULL) &&
    (max_len <= APEX_PROCESS_CMDLINE_SIZE))
  {
    char cmdline_no_spaces[APEX_PROCESS_CMDLINE_SIZE];
    (void)memset(cmdline_no_spaces, 0, sizeof(cmdline_no_spaces));
    if (APEX_RET_OK == get_cmdline_with_no_spaces(cmdline_string, cmdline_no_spaces,
      APEX_PROCESS_CMDLINE_SIZE))
    {
      // check if there are any parameters
      // check for the string "--" which takes the params
      const char * pn_param_p = strstr(cmdline_no_spaces, "--");

      size_t process_path_len;
      // if there are parameters
      if (NULL != pn_param_p) {
        // length of the string till parameters (which is the length for process path)
        process_path_len = strnlen(cmdline_no_spaces, APEX_PROCESS_CMDLINE_SIZE) -
          strnlen(pn_param_p, APEX_PROCESS_CMDLINE_SIZE);
      } else {  // no parameters
        process_path_len = strnlen(cmdline_no_spaces, APEX_PROCESS_CMDLINE_SIZE);
      }

      if (max_len > process_path_len) {
        // initialise buffer to copy
        (void)memset(cmdline_string_no_params_buff, 0, process_path_len + 1U);
        // process path with out any parameters
        (void) memmove(cmdline_string_no_params_buff, cmdline_no_spaces, process_path_len);

        cmdline_string_no_params_buff[process_path_len] = '\0';
        ret = APEX_RET_OK;
      }
    }
  }
  return ret;
}

read_ret_t get_process_cmdline(
  const char * const pid,
  char * const cmdline_buf,
  const size_t max_len)
{
  read_ret_t ret = READ_ERROR;

  if ((pid != NULL) &&
    (cmdline_buf != NULL) &&
    (max_len <= APEX_PROCESS_CMDLINE_SIZE))
  {
    apex_string_t path = apex_string_create();
    char buf[APEX_PROCESS_CMDLINE_SIZE];
    (void)memset(buf, 0, sizeof(buf));
    apex_ret_t string_add_ret = APEX_RET_ERROR;

    APEX_SKIP_IF(FI_MODE_0,
      (string_add_ret = apex_string_add3_strict(&path, "/proc/", pid,
      "/cmdline")));
    APEX_DO_IF(FI_MODE_0,
      (string_add_ret = apex_string_add4_strict(&path, APEXUTILS_TEST_DIR, "/",
      pid, "/cmdline")));

    if (APEX_RET_OK == string_add_ret) {
      int32_t fd = apex_open_file_ro(path.c_str);
      if (fd >= 0) {
        ret = apex_read_till_delimeter(fd, buf, max_len, '\n', true, 5);
        if (apex_is_read_successful(ret)) {
          (void) memmove(cmdline_buf, buf, max_len);
        }
        (void)apex_close_file(fd);
      }
    }
  }
  return ret;
}

int64_t apex_get_long_from_str(const char * str)
{
  errno = 0;
  char * endptr = NULL;
  int64_t retval = 0;
  if (str != NULL) {
    retval = strtol(str, &endptr, 10);
    if (errno != 0) {
      retval = 0;
    } else if (*endptr != '\0') {
      // conversion is valid only if all characters are read
      retval = 0;
    } else { /* Do nothing */}
  }
  return retval;
}
