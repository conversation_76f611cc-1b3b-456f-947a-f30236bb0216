// Copyright 2017-2018 Apex.AI, Inc.
// All rights reserved.

#include "apexutils/apex_rt_detail.h"
#if defined(QNX)
#include <sys/neutrino.h>
#include <sys/syspage.h>
#include <stdlib.h>
#include <string.h>
#endif  // defined(QNX)

#if defined(APEX_LINUX)
apex_ret_t apex_set_cpu_affinity_mask(pid_t pid, const cpu_set_t * mask)
{
  apex_ret_t retval = APEX_RET_SET_CPU_AFFINITY_ERR;
  // Anup Pemmaiah:
  // See PC Lint Description-3
  //lint -e{746} NOLINT
  //lint -efunc(718,apex_set_cpu_affinity_mask)  NOLINT
  if (sched_setaffinity(pid, sizeof(cpu_set_t), mask) >= 0) {
    retval = APEX_RET_OK;
  }
  return retval;
}

apex_ret_t apex_get_cpu_affinity_mask(pid_t pid, cpu_set_t * mask)
{
  apex_ret_t retval = APEX_RET_GET_CPU_AFFINITY_ERR;
  if (sched_getaffinity(pid, sizeof(cpu_set_t), mask) >= 0) {
    retval = APEX_RET_OK;
  }
  return retval;
}

void apex_cpu_set_macro_wrapper(uint32_t cpu_core, cpu_set_t * set)
{
  // Anup Pemmaiah:
  // See Description-3
  //lint -e{746} NOLINT
  //lint -efunc(718,apex_cpu_set_macro_wrapper)  NOLINT
  /*
   AXIVION Next Line Rule MisraC2023-10.4
   AXIVION Next Line Rule MisraC2023-12.1
   AXIVION Next Line Rule MisraC2023-13.4
   AXIVION Next Line Rule MisraC2023-21.2: Reason: Code Quality (Functional suitability),
   Justification: MACRO in sched.h linux is accepted
   */
  CPU_SET(cpu_core, set);
}

cpu_set_t apex_cpuset_to_cpu_set(apex_cpuset_t set_in)
{
  cpu_set_t set_out;
  //lint -efunc(718,apex_cpuset_to_cpu_set)  NOLINT
  //lint -e{746} NOLINT
  /*
   AXIVION Next Line Rule MisraC2023-14.4, MisraC2023-17.7: Reason: Code Quality (Functional
   suitability), Justification: CPU_ZERO is part of sched.h system header and is ok
   */
  CPU_ZERO(&set_out);
  for (uint32_t i = 0U; i < APEX_CPUSET_SIZE; i++) {
    if (apex_cpu_is_set(i, &set_in)) {
      apex_cpu_set_macro_wrapper(i, &set_out);
    }
  }
  return set_out;
}

void cpu_set_to_apex_cpuset(cpu_set_t set_in, apex_cpuset_t * set_out)
{
  apex_cpu_zero(set_out);
  for (uint32_t i = 0U; i < APEX_CPUSET_SIZE; i++) {
    if (CPU_ISSET(i, &set_in)) {
      apex_cpu_set(i, set_out);
    }
  }
}

#endif  // defined(APEX_LINUX)

#ifdef APEX_QNX
void * apex_cpuset_to_runmask(apex_cpuset_t set_in)
{
  uint32_t num_elements = 0u;
  int32_t * rsizep, * rmaskp, * imaskp;
  uint64_t masksize_bytes, size;
  void * runmask = NULL;

  // http://www.qnx.com/developers/docs/7.0.0/index.html#com.qnx.doc.neutrino.prog/topic/multicore_thread_affinity.html
  // Determine the number of array elements required to hold
  // the runmasks, based on the number of CPUs in the system.

  num_elements = (uint32_t)RMSK_SIZE(_syspage_ptr->num_cpu);

  // Determine the size of the runmask, in bytes.
  masksize_bytes = num_elements * sizeof(uint32_t);

  // Allocate memory for the data structure that we'll pass
  // to ThreadCtl(). We need space for an integer (the number
  // of elements in each mask array) and the two masks
  // (runmask and inherit mask).

  size = sizeof(int32_t) + 2u * masksize_bytes;
  if (NULL != (runmask = malloc(size))) {
    memset(runmask, 0x00, size);

    // Set up pointers to the "members" of the structure.
    rsizep = (int32_t *)runmask;
    rmaskp = rsizep + 1u;
    imaskp = rmaskp + num_elements;

    // Set the size.
    *rsizep = (int32_t)num_elements;

    // Set the runmask and inherit mask. Call this macro once for each processor
    // the thread and its children can run on.
    for (uint32_t i = 0UL; i < APEX_CPUSET_SIZE; i++) {
      if (apex_cpu_is_set(i, &set_in)) {
        RMSK_SET(i, rmaskp);
        RMSK_SET(i, imaskp);
      }
    }
  }

  return runmask;
}
#endif  // APEX_QNX
