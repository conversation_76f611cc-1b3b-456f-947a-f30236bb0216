// Copyright 2017-2018 Apex.AI, Inc.
// All rights reserved.
#include <math.h>
#include "apexutils/apex_time.h"
#include "apexutils/apexutils.h"
#ifdef APEX_WINDOWS
  #include <windows.h>
#elif defined(APEX_LINUX) || defined(APEX_OSX) || defined(APEX_QNX)
  #include <time.h>
#else  // defined(APEX_LINUX) || defined(APEX_OSX)
#error unsupported OS
#endif  // APEX_WINDOWS

///////////////////////////////////////////////////////////////////////////////
#define TICKS_PER_NS  (APEX_TIME_TICKS_PER_NANOSECOND)
#define NS_PER_TICK  (1.0 / (double)TICKS_PER_NS)
#define TICKS_PER_US  (TICKS_PER_NS * 1000LL)
#define TICKS_PER_MS  (TICKS_PER_US * 1000LL)
#define TICKS_PER_S  (TICKS_PER_MS * 1000LL)
#define TICKS_PER_MIN  (TICKS_PER_S * 60LL)
#define TICKS_PER_HR  (TICKS_PER_MIN * 60LL)
#define TICKS_PER_DAY  (TICKS_PER_HR * 24LL)

#define US_PER_DAY (86400000000LL)  // (24ULL * 60LL * 60LL * 1000LL * 1000LL)
#define NS_PER_DAY (US_PER_DAY * 1000)

#ifdef APEX_WINDOWS
#define NSEC100_TO_UNIX_EPOCH (116444736000000000ULL)
#endif  // APEX_WINDOWS

#define TIMESPAN_1AC_1970AC_US (62135596800000000LL)
#define TIMESPAN_1AC_1970AC_DAYS (719162)  // (TIMESPAN_1AC_1970AC_US / US_PER_DAY)

#define INVALID_TIME_TICKS_VALUE (INT64_MAX)

#define INVALID_TIMESPAN_VALUE  (INT64_MAX)

///////////////////////////////////////////////////////////////////////////////
enum TIME_PARTS
{
  DAY_OF_YEAR_PART,
  DAY_OF_MONTH_PART,
  MONTH_PART,
  YEAR_PART
};

///////////////////////////////////////////////////////////////////////////////
// Number of days in a non-leap year
#define DAYS_PER_YEAR (365)

// Number of days in 4 years
#define DAYS_PER_4_YEARS ((DAYS_PER_YEAR * 4) + 1)

// Number of days in 100 years
#define DAYSIN_100_YEARS ((DAYS_PER_4_YEARS * 25) - 1)

// Number of days in 400 years
#define TOTAL_DAYS_IN_400_YEARS ((DAYSIN_100_YEARS * 4) + 1)

static const int32_t days_to_month_365[] =
{
  0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334, 365
};

#define DAYS_TO_MONTH_365_LEN ((sizeof(days_to_month_365) / sizeof(days_to_month_365[0])))

/*
 AXIVION Next Line MisraC2023-5.9: Reason: Code Quality (Functional suitability), Justification:
 static g_failure_mode variables are intentionally used
 */
APEX_DEFINE_FAILURE_INJECTION(apex_time)
APEX_DEFINE_GET_INJECTED_FAILURE(apex_time)

///////////////////////////////////////////////////////////////////////////////
static double get_silent_nan(void)
{
#ifdef APEX_OSX
  return nan("0x7fc0000000000000");
#else  // APEX_OSX
  return nan("");
#endif  // APEX_OSX
}

///////////////////////////////////////////////////////////////////////////////
apex_time_t apex_time_now(void)
{
  apex_time_t retval = apex_time_get_invalid();
#ifdef APEX_WINDOWS
  APEX_SKIP_IF(FI_MODE_2,
  {
    // FILETIME is a number of 100s ticks between 1/1/1601 and Now
    FILETIME ft;
    GetSystemTimePreciseAsFileTime(&ft);
    // A standard procedure to convert 100's ticks from 1601 to total_us from 1970
    // One of many refs:
    // http://stackoverflow.com/questions/6161776/convert-windows-filetime-to-second-in-unix-linux#6161842
    ULARGE_INTEGER lv_Large;
    lv_Large.LowPart = ft.dwLowDateTime;
    lv_Large.HighPart = ft.dwHighDateTime;
    lv_Large.QuadPart -= NSEC100_TO_UNIX_EPOCH;  // shift from 1601 to 1970
    APEX_DO_IF(FI_MODE_3 | FI_MODE_4, (lv_Large.QuadPart = UINT64_MAX));
    if (!apex_mul_will_overflow_u64(lv_Large.QuadPart, 100ULL)) {
      lv_Large.QuadPart *= 100ULL;  // Convert from 100s ns to ns
      retval = (apex_time_t)(lv_Large.QuadPart);
    }
  });
#else  // APEX_WINDOWS
  struct timespec timespec_now = {0};
  int32_t posix_retval = -1;
  APEX_SKIP_IF(FI_MODE_2, (posix_retval = clock_gettime(CLOCK_REALTIME, &timespec_now)));
  if (posix_retval == 0) {
    APEX_DO_IF(FI_MODE_3, (timespec_now.tv_sec = INT64_MAX));
    if (!apex_mul_will_overflow_i64(timespec_now.tv_sec, APEX_BILLION)) {
      apex_time_t total_seconds_in_ns = timespec_now.tv_sec * APEX_BILLION;
      APEX_DO_IF(FI_MODE_4, (timespec_now.tv_nsec = apex_time_get_max()));
      APEX_DO_IF(FI_MODE_4, (total_seconds_in_ns = apex_time_get_max()));
      if (!apex_add_will_wraparound_i64(total_seconds_in_ns, timespec_now.tv_nsec)) {
        retval = total_seconds_in_ns + timespec_now.tv_nsec;
      }
    }
  }
#endif  // defined(APEX_LINUX)
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex_time_span_t apex_time_span_from_then(const apex_time_t then)
{
  apex_time_span_t retval = apex_time_span_get_invalid();
  if (apex_time_is_valid(then)) {
    apex_time_t now = apex_time_now();
    if (apex_time_is_valid(now)) {
      if (!apex_sub_will_wraparound_i64(now, then)) {
        retval = (now - then);
      }
    }
  }
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex_time_t apex_time_from_s_us(const int64_t seconds, const int64_t microseconds)
{
  apex_time_t retval = apex_time_get_invalid();
  if (!apex_mul_will_overflow_i64(seconds, APEX_MILLION)) {
    const int64_t us_in_seconds = seconds * APEX_MILLION;
    if (!apex_add_will_wraparound_i64(us_in_seconds, microseconds)) {
      const int64_t total_us = us_in_seconds + microseconds;
      if (!apex_mul_will_overflow_i64(total_us, TICKS_PER_US)) {
        retval = total_us * TICKS_PER_US;
      }
    }
  }
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex_time_t apex_time_from_s_ns(const int64_t seconds, const int64_t nanoseconds)
{
  apex_time_t retval = apex_time_get_invalid();
  if (!apex_mul_will_overflow_i64(seconds, APEX_BILLION)) {
    const int64_t ns_in_seconds = seconds * APEX_BILLION;
    if (!apex_add_will_wraparound_i64(ns_in_seconds, nanoseconds)) {
      const int64_t total_ns = ns_in_seconds + nanoseconds;
#if TICKS_PER_NS == 1
      // As long as TICKS_PER_NS == 1, there won't be overflow
      // so we don't need to check for overflow
      retval = total_ns;
#else  // TICKS_PER_NS == 1
      // if (!apex_mul_will_overflow_i64(total_ns, TICKS_PER_NS)) {
      //   retval = total_ns * TICKS_PER_NS;
      // }
      #error "Create MC/DC test ro TICKS_PER_NS != 1"
#endif  // TICKS_PER_NS == 1
    }
  }
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
bool apex_time_is_valid(const apex_time_t time_to_check)
{
  bool retval = false;
  APEX_SKIP_IF(FI_MODE_1, (retval = time_to_check != apex_time_get_invalid()));
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex_time_t apex_time_get_invalid(void)
{
  return INVALID_TIME_TICKS_VALUE;
}

///////////////////////////////////////////////////////////////////////////////
static int32_t get_days_since_1ac(const apex_time_t time_ticks)
{
  // Convert ticks to microseconds, otherwise we risk overflow or underflow
  const int64_t ticks_per_usec = TICKS_PER_US;
  const int64_t us_since_1970ac = time_ticks / ticks_per_usec;
  const int64_t us_since_1c = (us_since_1970ac + TIMESPAN_1AC_1970AC_US);

  // days = number of days from 1/1/0001
  int32_t total_days_since_1ac = (int32_t)(us_since_1c / US_PER_DAY);

  if ((time_ticks < 0) && (us_since_1970ac == 0)) {
    // Correct for rounded up negative small nanoseconds, e.g. (epoch - 1ns)
    total_days_since_1ac--;
  }
  return total_days_since_1ac;
}

///////////////////////////////////////////////////////////////////////////////
// See MS open sourced .Net DateTime class
static int32_t get_date_part(const enum TIME_PARTS part, const apex_time_t time_ticks)
{
  static const int32_t days_to_month_366[DAYS_TO_MONTH_365_LEN] =
  {
    0, 31, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335, 366
  };
  int32_t retval = -1;
  if (apex_time_is_valid(time_ticks)) {
    // Move origin to 1/1/1 and use Microsoft's algorithm
    // https://referencesource.microsoft.com/#mscorlib/system/datetime.cs,753
    // days = number of days from 1/1/0001
    const int32_t total_days_since_1ac = get_days_since_1ac(time_ticks);

    // y400 = number of whole 400-year periods from 1/1/1
    const int32_t y400 = total_days_since_1ac / TOTAL_DAYS_IN_400_YEARS;

    // days_in_400_years = day number within 400-year period
    const int32_t days_in_400_years = total_days_since_1ac - (y400 * TOTAL_DAYS_IN_400_YEARS);

    // y100 = number of whole 100-year periods within 400-year period
    int32_t y100 = days_in_400_years / DAYSIN_100_YEARS;

    // Last 100-year period has an extra day, so decrement result if 4
    if (y100 == 4) {
      y100 = 3;
    }

    // days_in_100_years = day number within 100-year period
    const int32_t days_in_100_years = days_in_400_years - (y100 * DAYSIN_100_YEARS);

    // y4 = number of whole 4-year periods within 100-year period
    const int32_t y4 = days_in_100_years / DAYS_PER_4_YEARS;

    // days_in_4_years = day number within 4-year period
    const int32_t days_in_4_years = days_in_100_years - (y4 * DAYS_PER_4_YEARS);

    // y1 = number of whole years within 4-year period
    int32_t years_in_4 = days_in_4_years / DAYS_PER_YEAR;

    // Last year has an extra day, so decrement result if 4
    if (years_in_4 == 4) {
      years_in_4 = 3;
    }

    // If year was requested, compute and return it
    retval = (((y400 * 400) + (y100 * 100)) + (y4 * 4)) + (years_in_4 + 1);
    if (part != YEAR_PART) {
      // day_in_year = day number within year
      // day_in_year is always < DAYS_PER_YEAR from the above
      int32_t day_in_year = days_in_4_years - (years_in_4 * DAYS_PER_YEAR);
      // If day-of-year was requested, return it
      retval = day_in_year + 1;
      if (part != DAY_OF_YEAR_PART) {
        // Leap year calculation looks different from IsLeapYear from y1, y4,
        // and y100 are relative to year 1, not year 0
        const bool is_leap_year = (years_in_4 == 3) && ((y4 != 24) || (y100 == 3));
        const int32_t * days_to_month = is_leap_year ? days_to_month_366 : days_to_month_365;

        // All months have less than 32 days, so n >> 5 is a good conservative
        // estimate for the month
        // month = 1-based month number, so add one
        // Original code: int32_t m = (n >> 5) + 1;
        uint32_t month = ((uint32_t)day_in_year / 32U) + 1U;
        // day_in_year is [0..DAYS_PER_YEAR)
        // Since day_in_year <365/366, the loop will always exit at month < DAYS_TO_MONTH_365_LEN
        // We don't need to check month boundary and we can't do MC/DC for it,
        // when (month >= DAYS_TO_MONTH_365_LEN)
        for (; (day_in_year >= days_to_month[month]); ++month) {
        }
        // If month was requested, return it
        retval = (int32_t)month;
        if (part != MONTH_PART) {
          // It was a day request
          // Return 1-based day-of-month
          // month is always >= 1. From the above: m = (n >> 5) + 1
          retval = (day_in_year - days_to_month[month - 1U]) + 1;
        }
      }
    }
  }
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
// Shift time by days to a positive value to compute time of day
static apex_time_t shift_apex_time_to_positive(const apex_time_t wall_time)
{
  apex_time_t retval = wall_time;
  if (retval < 0) {
    const int64_t days_since_1ac = get_days_since_1ac(wall_time);
    const int64_t days_to_1970ac = TIMESPAN_1AC_1970AC_DAYS - days_since_1ac;
    const int64_t ticks_per_one_day = (NS_PER_DAY * TICKS_PER_NS);
    retval += (days_to_1970ac + 1) * ticks_per_one_day;
  }
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
int32_t apex_time_get_ns_part(const apex_time_t wall_time)
{
  int32_t retval = -1;
  if (apex_time_is_valid(wall_time)) {
    const int64_t ticks_per_nsec = TICKS_PER_NS;
    const apex_time_t shifted_wall_time = shift_apex_time_to_positive(wall_time);
    const int64_t nanoseconds = ((shifted_wall_time / ticks_per_nsec) % 1000LL);
    retval = (int32_t)nanoseconds;
  }
  return retval;
}

int32_t apex_time_get_us_part(const apex_time_t wall_time)
{
  int32_t retval = -1;
  if (apex_time_is_valid(wall_time)) {
    const int64_t ticks_per_usec = TICKS_PER_US;
    const apex_time_t shifted_wall_time = shift_apex_time_to_positive(wall_time);
    const int64_t microseconds = ((shifted_wall_time / ticks_per_usec) % 1000LL);
    retval = (int32_t)microseconds;
  }
  return retval;
}

int32_t apex_time_get_ms_part(const apex_time_t wall_time)
{
  int32_t retval = -1;
  if (apex_time_is_valid(wall_time)) {
    const int64_t ticks_per_msec = TICKS_PER_MS;
    const apex_time_t shifted_wall_time = shift_apex_time_to_positive(wall_time);
    const int64_t milliseconds = ((shifted_wall_time / ticks_per_msec) % 1000LL);
    retval = (int32_t)milliseconds;
  }
  return retval;
}

int32_t apex_time_get_s_part(const apex_time_t wall_time)
{
  int32_t retval = -1;
  if (apex_time_is_valid(wall_time)) {
    const int64_t ticks_per_sec = TICKS_PER_S;
    const apex_time_t shifted_wall_time = shift_apex_time_to_positive(wall_time);
    const int64_t seconds = ((shifted_wall_time / ticks_per_sec) % 60LL);
    retval = (int32_t)seconds;
  }
  return retval;
}

int32_t apex_time_get_min_part(const apex_time_t wall_time)
{
  int32_t retval = -1;
  if (apex_time_is_valid(wall_time)) {
    const int64_t ticks_per_minute = TICKS_PER_MIN;
    const apex_time_t shifted_wall_time = shift_apex_time_to_positive(wall_time);
    const int64_t minutes = ((shifted_wall_time / ticks_per_minute) % 60LL);
    retval = (int32_t)minutes;
  }
  return retval;
}

int32_t apex_time_get_hrs_part(const apex_time_t wall_time)
{
  int32_t retval = -1;
  if (apex_time_is_valid(wall_time)) {
    const int64_t ticks_per_hour = TICKS_PER_HR;
    const apex_time_t shifted_wall_time = shift_apex_time_to_positive(wall_time);
    const int64_t hours = ((shifted_wall_time / ticks_per_hour) % 24LL);
    retval = (int32_t)hours;
  }
  return retval;
}

int32_t apex_time_get_day_of_week_part(const apex_time_t wall_time)
{
  int32_t retval = -1;
  if (apex_time_is_valid(wall_time)) {
    const int64_t days_since_1ac = get_days_since_1ac(wall_time);
    // Date 1/1/1 is Monday
    const int64_t day = ((days_since_1ac + 1LL) % 7LL);
    retval = (int32_t)day;
  }
  return retval;
}

int32_t apex_time_get_day_of_month_part(const apex_time_t wall_time)
{
  return get_date_part(DAY_OF_MONTH_PART, wall_time);
}

int32_t apex_time_get_day_of_year_part(const apex_time_t wall_time)
{
  return get_date_part(DAY_OF_YEAR_PART, wall_time);
}

int32_t apex_time_get_month_part(const apex_time_t wall_time)
{
  return get_date_part(MONTH_PART, wall_time);
}

int32_t apex_time_get_year_part(const apex_time_t wall_time)
{
  return get_date_part(YEAR_PART, wall_time);
}

///////////////////////////////////////////////////////////////////////////////
int64_t apex_time_get_as_ns(const apex_time_t wall_time)
{
  return wall_time / TICKS_PER_NS;
}

int64_t apex_time_get_as_us(const apex_time_t wall_time)
{
  return wall_time / TICKS_PER_US;
}

int64_t apex_time_get_as_ms(const apex_time_t wall_time)
{
  return wall_time / TICKS_PER_MS;
}

int64_t apex_time_get_as_s(const apex_time_t wall_time)
{
  return wall_time / TICKS_PER_S;
}

///////////////////////////////////////////////////////////////////////////////
bool apex_time_span_is_valid(const apex_time_span_t time_span)
{
  bool retval = false;
  APEX_SKIP_IF(FI_MODE_0, (retval = time_span != apex_time_span_get_invalid()));
  return retval;
}

apex_time_span_t apex_time_span_get_invalid(void)
{
  return INVALID_TIMESPAN_VALUE;
}

///////////////////////////////////////////////////////////////////////////////
double apex_time_span_get_total_ns(const apex_time_span_t time_span)
{
  double retval = get_silent_nan();
  if (apex_time_span_is_valid(time_span)) {
    retval = (double)(time_span) * NS_PER_TICK;
  }
  return retval;
}

double apex_time_span_get_total_us(const apex_time_span_t time_span)
{
  double retval = get_silent_nan();
  if (apex_time_span_is_valid(time_span)) {
    const int64_t ticks_per_usec = TICKS_PER_US;
    const double us_per_tick = 1.0 / (double)ticks_per_usec;
    retval = (double)(time_span) * us_per_tick;
  }
  return retval;
}

double apex_time_span_get_total_ms(const apex_time_span_t time_span)
{
  double retval = get_silent_nan();
  if (apex_time_span_is_valid(time_span)) {
    const int64_t ticks_per_msec = TICKS_PER_MS;
    const double ms_per_tick = 1.0 / (double)ticks_per_msec;
    retval = (double)(time_span) * ms_per_tick;
  }
  return retval;
}

double apex_time_span_get_total_s(const apex_time_span_t time_span)
{
  double retval = get_silent_nan();
  if (apex_time_span_is_valid(time_span)) {
    const int64_t ticks_per_sec = TICKS_PER_S;
    const double s_per_tick = 1.0 / (double)ticks_per_sec;
    retval = (double)(time_span) * s_per_tick;
  }
  return retval;
}

double apex_time_span_get_total_min(const apex_time_span_t time_span)
{
  double retval = get_silent_nan();
  if (apex_time_span_is_valid(time_span)) {
    const int64_t ticks_per_minute = TICKS_PER_MIN;
    const double min_per_tick = 1.0 / (double)ticks_per_minute;
    retval = (double)(time_span) * min_per_tick;
  }
  return retval;
}

double apex_time_span_get_total_hrs(const apex_time_span_t time_span)
{
  double retval = get_silent_nan();
  if (apex_time_span_is_valid(time_span)) {
    const int64_t ticks_per_hour = TICKS_PER_HR;
    const double hrs_per_tick = 1.0 / (double)ticks_per_hour;
    retval = (double)(time_span) * hrs_per_tick;
  }
  return retval;
}

double apex_time_span_get_total_days(const apex_time_span_t time_span)
{
  double retval = get_silent_nan();
  if (apex_time_span_is_valid(time_span)) {
    const int64_t ticks_per_one_day = TICKS_PER_DAY;
    const double days_per_tick = 1.0 / (double)ticks_per_one_day;
    retval = (double)(time_span) * days_per_tick;
  }
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
static apex_time_span_t time_span_from_units(const int64_t units, const int64_t ticks_per_unit)
{
  apex_time_span_t retval = apex_time_span_get_invalid();
  if (!apex_mul_will_overflow_i64(units, ticks_per_unit)) {
    retval = units * ticks_per_unit;
  }
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex_time_span_t apex_time_span_from_ns(const int64_t nanoseconds)
{
  return time_span_from_units(nanoseconds, TICKS_PER_NS);
}

apex_time_span_t apex_time_span_from_us(const int64_t microseconds)
{
  return time_span_from_units(microseconds, TICKS_PER_US);
}

apex_time_span_t apex_time_span_from_ms(const int64_t milliseconds)
{
  return time_span_from_units(milliseconds, TICKS_PER_MS);
}

apex_time_span_t apex_time_span_from_s(const int64_t seconds)
{
  return time_span_from_units(seconds, TICKS_PER_S);
}

apex_time_span_t apex_time_span_from_min(const int64_t minutes)
{
  return time_span_from_units(minutes, TICKS_PER_MIN);
}

apex_time_span_t apex_time_span_from_hrs(const int64_t hours)
{
  return time_span_from_units(hours, TICKS_PER_HR);
}

apex_time_span_t apex_time_span_from_days(const int64_t days)
{
  return time_span_from_units(days, TICKS_PER_DAY);
}

///////////////////////////////////////////////////////////////////////////////
int64_t apex_time_span_get_as_ns(const apex_time_span_t time_span)
{
  return time_span / TICKS_PER_NS;
}

int64_t apex_time_span_get_as_us(const apex_time_span_t time_span)
{
  return time_span / TICKS_PER_US;
}

int64_t apex_time_span_get_as_ms(const apex_time_span_t time_span)
{
  return time_span / TICKS_PER_MS;
}

int64_t apex_time_span_get_as_s(const apex_time_span_t time_span)
{
  return time_span / TICKS_PER_S;
}

///////////////////////////////////////////////////////////////////////////////
APEXUTILS_PUBLIC apex_time_t apex_time_add(
  const apex_time_t wall_time,
  const apex_time_span_t time_span)
{
  int64_t retval = apex_time_get_invalid();
  const bool is_wall_time_valid = apex_time_is_valid(wall_time);
  const bool is_time_span_valid = apex_time_span_is_valid(time_span);
  if (is_wall_time_valid && is_time_span_valid) {
    if (!apex_add_will_wraparound_i64(wall_time, time_span)) {
      retval = wall_time + time_span;
    }
  }
  return retval;
}

APEXUTILS_PUBLIC apex_time_t apex_time_sub(
  const apex_time_t wall_time,
  const apex_time_span_t time_span)
{
  int64_t retval = apex_time_get_invalid();
  const bool is_wall_time_valid = apex_time_is_valid(wall_time);
  const bool is_time_span_valid = apex_time_span_is_valid(time_span);
  if (is_wall_time_valid && is_time_span_valid) {
    if (!apex_sub_will_wraparound_i64(wall_time, time_span)) {
      retval = wall_time - time_span;
    }
  }
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex_time_t apex_time_from_now(const apex_time_span_t timespan)
{
  apex_time_t retval = apex_time_get_invalid();
  if (apex_time_span_is_valid(timespan)) {
    apex_time_t time_from_now = apex_time_now();
    if (apex_time_is_valid(time_from_now)) {
      time_from_now = apex_time_add(time_from_now, timespan);
      if (apex_time_is_valid(time_from_now)) {
        retval = time_from_now;
      }
    }
  }
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
APEXUTILS_PUBLIC apex_time_t apex_time_get_max(void)
{
  return INT64_MAX - 1;
}

///////////////////////////////////////////////////////////////////////////////
apex_time_t apex_time_get_min(void)
{
  return INT64_MIN;
}

apex_time_t apex_time_span_get_max(void)
{
  return INT64_MAX - 1;
}

apex_time_t apex_time_span_get_min(void)
{
  return INT64_MIN;
}
