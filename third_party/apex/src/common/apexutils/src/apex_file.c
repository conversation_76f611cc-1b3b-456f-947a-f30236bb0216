// Copyright 2017-2018 Apex.AI, Inc.
// All rights reserved.

//lint -emacro(*, S_ISREG) NOLINT GCC's S_ISREG uses octals and hex w/o 'U' suffix
//lint -emacro(*, O_RDONLY, O_NONBLOCK) NOLINT GCC's open uses octals and hex w/o 'U' suffix
/*
 AXIVION DISABLE STYLE MisraC2023-7.1
 AXIVION DISABLE STYLE MisraC2023-10.1: Reason: Code Quality (Functional suitability),
 Justification: The constants are defined in linux header file
 */

#include <sys/types.h>
#include <sys/stat.h>
#include <errno.h>
#include <fcntl.h>
#include <stdio.h>
#include <apexutils/apexutils.h>
#ifdef APEX_WINDOWS
#include <windows.h>
  #include <direct.h>
  #define S_ISREG(mode) (_S_IFREG == ((mode) & _S_IFREG))
#elif defined(APEX_LINUX) || defined(APEX_OSX) || defined(APEX_QNX)
#include <unistd.h>
#endif  // APEX_WINDOWS

bool apex_is_file_present(const char * const file_name)
{
  bool retval = false;
  if (NULL != file_name) {
    struct stat s;
    if (0 == stat(file_name, &s)) {
      /*
       AXIVION Next Line MisraC2023-10.4: Reason: Code Quality (Functional suitability),
       Justification: MACRO in stat.h linux is accepted.
       */
      if (S_ISREG(s.st_mode)) {
        retval = true;
      }
    }
  }
  return retval;
}

int32_t apex_open_file_ro(const char * const file_name)
{
  int32_t retval = -1;
  if (NULL != file_name) {
    if (apex_is_file_present(file_name)) {
      //lint -e{9027} -e{835} NOLINT - The constants are defined as int in fcntl-linux.h
      retval = open(file_name, O_RDONLY | O_NONBLOCK);
    }
  }
  return retval;
}
// AXIVION ENABLE STYLE MisraC2023-7.1
// AXIVION ENABLE STYLE MisraC2023-10.1

apex_ret_t apex_close_file(const int32_t fd)
{
  apex_ret_t retval = APEX_RET_ERROR;
  if (fd >= 0) {
    if (0 == close(fd)) {
      retval = APEX_RET_OK;
    }
  }
  return retval;
}

int64_t apex_read_bytes(
  const int32_t fd, char * const read_buffer, const size_t
  read_size_bytes, const size_t retry_parameter)
{
  ssize_t result = -1;

  if ((fd <= 0) ||
    (read_buffer == NULL) ||
    ((uint64_t)read_size_bytes == SIZE_MAX) ||
    (read_size_bytes < 1U) ||
    (retry_parameter < 1U) ||
    ((uint64_t)retry_parameter == SIZE_MAX))
  {
    return (int64_t)result;
  }

  size_t total_bytes = 0;

  for (size_t i = 0; (i < retry_parameter) && (total_bytes != read_size_bytes); ++i) {
    size_t remaining_bytes = read_size_bytes - total_bytes;
    result = read(fd, &(read_buffer[total_bytes]), remaining_bytes);
    if (result < 1) {
      break;
    }
    total_bytes += (size_t)result;
    result = (ssize_t)total_bytes;
  }

  read_buffer[total_bytes] = '\0';

  return (int64_t)result;
}

read_ret_t apex_read_till_delimeter(
  const int32_t fd, char * read_buffer, const size_t max_buffer_bytes,
  const char delimiter, const bool consider_new_line,
  const size_t retry_parameter)
{
  read_ret_t read_ret = READ_ERROR;
  size_t bytes_read = 0;
  size_t idx = 0;

  bool is_buffer_valid = true;
  bool is_read_error = false;
  bool is_end_of_file = false;

  if ((fd >= 0) &&
    (read_buffer != NULL) && (max_buffer_bytes > 1U) && (max_buffer_bytes < SIZE_MAX) &&
    (retry_parameter > 0U) && (retry_parameter < SIZE_MAX))
  {
    do {
      is_buffer_valid = ((bytes_read + 1U) <= max_buffer_bytes) ? true : false;
      if (is_buffer_valid) {
        char char_byte[2];
        int64_t ret = apex_read_bytes(fd, &char_byte[0], 1, retry_parameter);
        if (ret == 1) {  // read successfully
          if ((char_byte[0] == delimiter) ||
            (consider_new_line ? (char_byte[0] == '\n') : false))
          {
            read_buffer[idx] = '\0';
            read_ret = READ_COMPLETE;
            break;
          }
          read_buffer[idx] = char_byte[0];
          bytes_read += (size_t)(ret);
          idx++;
        } else if (ret == 0) {  // end of file reached
          is_end_of_file = true;
          read_buffer[idx] = '\0';
          read_ret = READ_REACHED_END_OF_FILE;
        } else {  // error scenario
          is_read_error = true;
        }
      } else {
        read_buffer[max_buffer_bytes - 1U] = '\0';
        read_ret = READ_INCOMPLETE;
        // Rollback file descriptor to one position back, since we read one symbol but it's not a
        // delimiter nor an end of the line or file and it doesn't fit in the our buffer since we
        // need to put null terminator to it yet.
        if (lseek(fd, -1, SEEK_CUR) < 0) {
          read_ret = READ_ERROR;
        }
      }
    } while (!is_end_of_file && is_buffer_valid && !is_read_error);
  }

  return read_ret;
}

apex_ret_t apex_seek_fd_to_start(const int32_t fd)
{
  apex_ret_t retval = APEX_RET_ERROR;
  if (fd >= 0) {
    if (lseek(fd, 0, SEEK_SET) >= 0) {
      retval = APEX_RET_OK;
    }
  }

  return retval;
}

apex_ret_t apex_seek_fd_to_end(const int32_t fd)
{
  apex_ret_t retval = APEX_RET_ERROR;
  if (fd >= 0) {
    if (lseek(fd, 0, SEEK_END) >= 0) {
      retval = APEX_RET_OK;
    }
  }

  return retval;
}

bool apex_is_read_successful(const read_ret_t read_ret)
{
  return (read_ret == READ_COMPLETE) || (read_ret == READ_REACHED_END_OF_FILE);
}
