// Copyright 2019 Apex.AI, Inc.
// All rights reserved.

#include <string.h>
#include "apexutils/apexutils.h"

#ifdef APEX_WINDOWS
#include <windows.h>
#elif defined(APEX_LINUX) || defined(APEX_OSX) || defined(APEX_QNX)
#include <unistd.h>
#include <libgen.h>
#else  // defined(APEX_LINUX)
#error unsupported OS
#endif  // APEX_WINDOWS

#include "apexutils/apex_process_detail.h"

#ifndef APEXUTILS_TEST_DIR
#pragma message("APEXUTILS_TEST_DIR is not defined")
#define APEXUTILS_TEST_DIR "/tmp/"
#endif


/*
 AXIVION Next Line MisraC2023-5.9: Reason: Code Quality (Functional suitability), Justification:
 static g_failure_mode variables are intentionally used
 */
APEX_DEFINE_FAILURE_INJECTION(apex_process)

int32_t apex_get_pid(void)
{
  return getpid();
}

apex_ret_t apex_get_executable_name_with_path_params(
  const char * const pid,
  char * const exe_name_buf,
  size_t exe_name_max_len)
{
  apex_ret_t ret = APEX_RET_ERROR;
  // input parameters are validated in the calling function below
  read_ret_t read_ret = get_process_cmdline(pid, exe_name_buf, exe_name_max_len);
  if ((read_ret == READ_REACHED_END_OF_FILE) || (read_ret == READ_COMPLETE)) {
    ret = APEX_RET_OK;
  }
  return ret;
}

apex_ret_t apex_get_executable_name_with_path(
  const char * const pid,
  char * const exe_name_buf,
  size_t exe_name_max_len)
{
  apex_ret_t ret = APEX_RET_ERROR;
  // input parameters are validated in the calling function below
  char cmdline_with_path_params[APEX_PROCESS_CMDLINE_SIZE];
  (void)memset(cmdline_with_path_params, 0, sizeof(cmdline_with_path_params));
  if (exe_name_buf != NULL) {
    if (apex_get_executable_name_with_path_params(pid, cmdline_with_path_params,
      APEX_PROCESS_CMDLINE_SIZE) == APEX_RET_OK)
    {
      if (APEX_RET_OK ==
        get_cmdline_with_no_params(cmdline_with_path_params, exe_name_buf, exe_name_max_len))
      {
        ret = APEX_RET_OK;
      }
    }
  }
  return ret;
}

apex_ret_t apex_get_executable_name(
  const char * const pid,
  char * const exe_name_buf,
  size_t exe_name_max_len)
{
  apex_ret_t ret = APEX_RET_ERROR;
  // input parameters are validated in the calling function below
  char cmdline_with_path[APEX_PROCESS_CMDLINE_SIZE];
  (void)memset(cmdline_with_path, 0, sizeof(cmdline_with_path));
  if (exe_name_buf != NULL) {
    if (apex_get_executable_name_with_path(pid, cmdline_with_path, APEX_PROCESS_CMDLINE_SIZE) ==
      APEX_RET_OK)
    {
      const char * bptr = basename(cmdline_with_path);
      size_t baselen = strnlen(bptr, APEX_PROCESS_CMDLINE_SIZE);
      if (exe_name_max_len > baselen) {
        (void) memmove(exe_name_buf, bptr, baselen);
        exe_name_buf[baselen] = '\0';
        ret = APEX_RET_OK;
      }
    }
  }
  return ret;
}

apex_ret_t apex_get_pid_from_process_name(const char * const process_name, uint32_t * pid)
{
  apex_ret_t ret_value = APEX_RET_ERROR;

  // if the process name and pid are not null
  if ((process_name != NULL) && (pid != NULL)) {
    DIR * proc_dir_ptr = NULL;

    // When MC/DC testing we make the directory pointer to point the binary directory where test
    // cmdline proc files will be generated
    APEX_SKIP_IF(FI_MODE_0, (proc_dir_ptr = get_proc_dir_ptr()));
    APEX_DO_IF(FI_MODE_0, (proc_dir_ptr = opendir(APEXUTILS_TEST_DIR)));
    APEX_DO_IF(FI_MODE_1, (proc_dir_ptr = NULL));

    if (proc_dir_ptr != NULL) {
      // Enumerate all entries in directory until process found
      const struct dirent * dirp;
      bool found_pid = false;
      // loop through entries
      do {
        dirp = readdir(proc_dir_ptr);
        if (NULL != dirp) {
          // if the directory is numeric (which is PID of the process)
          int64_t dir_num = apex_get_long_from_str(dirp->d_name);
          if (dir_num > 0) {
            // read the cmdline path
            char process_path[APEX_PROCESS_CMDLINE_SIZE];
            (void)memset(process_path, 0, sizeof(process_path));
            if (APEX_RET_OK ==
              apex_get_executable_name(dirp->d_name, process_path, APEX_PROCESS_CMDLINE_SIZE))
            {
              // compare the process name with the requested one
              size_t process_path_len = strnlen(process_path, APEX_PROCESS_CMDLINE_SIZE);
              if (strncmp(process_path, process_name, process_path_len) == 0) {
                if (!found_pid) {
                  found_pid = true;
                  *pid = (uint32_t)dir_num;
                  ret_value = APEX_RET_OK;
                  // Continue to search to see if we will catch another process with the same name
                } else {
                  // We found two processes with the same name and don't know which PID
                  // corresponds to which one.
                  ret_value = APEX_RET_MULTIPLE_PID;
                  break;
                }
              }
            }
          }
        }
      } while (dirp != NULL);
      // rewind the directory pointer
      rewinddir(proc_dir_ptr);
    }
    APEX_DO_IF(FI_MODE_0, ((void)closedir(proc_dir_ptr)));
  }
  return ret_value;
}
