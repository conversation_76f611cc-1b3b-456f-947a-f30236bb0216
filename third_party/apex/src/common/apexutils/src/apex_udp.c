// Copyright 2017-2018 Apex.AI, Inc.
// All rights reserved.

#include <stdio.h>
#include <stdlib.h>
#include "apexutils/apexdef.h"
#define I_NEED_NETWORK_DEFS (1)  // Enable network compatibility macros definitions
#include "apexutils/apexutils_local.h"
#include "apexutils/apex_string.h"
#include "apexutils/apex_time.h"
#include "apexutils/apex_udp.h"
#include "apexutils/apexutils.h"

///////////////////////////////////////////////////////////////////////////////
struct udp_implementation_s
{
  uint16_t m_udp_port;
  SOCKET_T m_socket;
  bool m_is_receiver;
  struct sockaddr_in m_addr_to_send_to;
  // Source of received UDP message.
  uint16_t m_receiver_source_port;
  apex_time_span_t m_last_time_span;
#ifndef APEX_WINDOWS
  int32_t m_last_fcntl;
#endif  // APEX_WINDOWS
};

/*
 AXIVION Next Line MisraC2023-5.9: Reason: Code Quality (Functional suitability), Justification:
 static g_failure_mode variables are intentionally used
 */
APEX_DEFINE_FAILURE_INJECTION(apex_udp)

///////////////////////////////////////////////////////////////////////////////
static apex_ret_t create_sender_or_receiver(
  apex_udp_t ** const udp_ptr_ptr,
  const uint32_t ip4_address,
  const uint16_t udp_port,
  const bool is_receiver)
{
  apex_ret_t retval = APEX_RET_ERROR;
  if (udp_ptr_ptr != NULL) {
    struct udp_implementation_s * udp_ptr = NULL;
    //lint -e{9048} NOLINT unsigned without a 'U' in 3d party constant INADDR_NONE
    /*
     AXIVION Next Line MisraC2023-7.2: Reason: Code Quality (Functional suitability),
     Justification: unsigned without a 'U' in 3d party constant INADDR_NONE
     */
    if (ip4_address != INADDR_NONE) {
      //lint -e{586} NOLINT calloc is intended to be used here
      APEX_SKIP_IF(FI_MODE_1, (udp_ptr = calloc(1U, sizeof(*udp_ptr))));
      if (udp_ptr != NULL) {
        udp_ptr->m_udp_port = udp_port;
        udp_ptr->m_is_receiver = is_receiver;
        udp_ptr->m_last_time_span = apex_time_span_get_invalid();

        udp_ptr->m_addr_to_send_to.sin_family = AF_INET;
        udp_ptr->m_addr_to_send_to.sin_port = htons(udp_port);
        udp_ptr->m_addr_to_send_to.sin_addr.s_addr = ip4_address;
        udp_ptr->m_socket = APEX_UTILS_INVALID_SOCKET;
        udp_ptr->m_receiver_source_port = 0;
        APEX_SKIP_IF(
          FI_MODE_2,
          (udp_ptr->m_socket = socket(PF_INET, (int32_t)SOCK_DGRAM, (int32_t)IPPROTO_UDP)));
        if (udp_ptr->m_socket != APEX_UTILS_INVALID_SOCKET) {
          int32_t posix_error = -1;
          APEX_SKIP_IF(FI_MODE_0, (posix_error = 0));
          if (posix_error == 0) {
            if (udp_ptr->m_is_receiver) {
              // bind socket to the port
              struct sockaddr_in bind_addr;
#ifdef APEX_WINDOWS
              const int32_t bind_addr_size = (int32_t)sizeof(bind_addr);
#else  // APEX_WINDOWS
              const uint32_t bind_addr_size = (uint32_t)sizeof(bind_addr);
#endif  // APEX_WINDOWS
              // Ignore unneeded return pointer to destination
              (void)memset(&bind_addr, 0, sizeof(bind_addr));

              // host byte order
#ifdef APEX_OSX
              bind_addr.sin_family = AF_INET;
#else  // APEX_OSX
              bind_addr.sin_family = (uint16_t)AF_INET;
#endif  // APEX_OSX

              // listen on this port
              // network byte order
              bind_addr.sin_port = htons(udp_ptr->m_udp_port);
              void * bind_addr_ptr = &bind_addr;
              posix_error = -1;
              APEX_SKIP_IF(FI_MODE_3,
                (posix_error = bind(
                  udp_ptr->m_socket,
                  bind_addr_ptr,
                  bind_addr_size)));
              if (posix_error == 0) {
                retval = APEX_RET_OK;
              }
            } else {
              retval = APEX_RET_OK;
            }
          }
          if (retval != APEX_RET_OK) {
            posix_error = -1;
            APEX_SKIP_IF(FI_MODE_0, (posix_error = closesocket(udp_ptr->m_socket)));
            if (posix_error != 0) {
              retval = APEX_RET_RESOURCE_LEAK;
            }
          }
        }

        if (retval != APEX_RET_OK) {
          //lint -e{586} NOLINT free is intended to be used here
          free(udp_ptr);
          udp_ptr = NULL;
        }
      }
    }
    *udp_ptr_ptr = udp_ptr;
  }
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex_ret_t apex_udp_create_receiver_by_ip4(
  apex_udp_t ** const udp_ptr_ptr,
  const uint32_t ip4_address,
  const uint16_t udp_port)
{
  return create_sender_or_receiver(
    udp_ptr_ptr,
    ip4_address,
    udp_port,
    true);
}

///////////////////////////////////////////////////////////////////////////////
apex_ret_t apex_udp_create_receiver(
  apex_udp_t ** const udp_ptr_ptr,
  const char * const ip4_address_str,
  const uint16_t udp_port)
{
  uint32_t ip4_address = inet_addr(ip4_address_str);
  apex_ret_t retval = apex_udp_create_receiver_by_ip4(
    udp_ptr_ptr,
    ip4_address,
    udp_port);
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex_ret_t apex_udp_create_sender_by_ip4(
  apex_udp_t ** const udp_ptr_ptr,
  const uint32_t ip4_address,
  const uint16_t udp_port)
{
  return create_sender_or_receiver(
    udp_ptr_ptr,
    ip4_address,
    udp_port,
    false);
}

///////////////////////////////////////////////////////////////////////////////
APEXUTILS_PUBLIC apex_ret_t apex_udp_create_sender(
  apex_udp_t ** const udp_ptr_ptr,
  const char * const ip4_address_str,
  const uint16_t udp_port)
{
  uint32_t ip4_address = inet_addr(ip4_address_str);
  apex_ret_t retval = apex_udp_create_sender_by_ip4(
    udp_ptr_ptr,
    ip4_address,
    udp_port);
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
bool apex_udp_is_receiver(const apex_udp_t * const udp_ptr)
{
  bool retval = (udp_ptr != NULL) && (udp_ptr->m_is_receiver);
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
bool apex_udp_is_sender(const apex_udp_t * const udp_ptr)
{
  bool retval = (udp_ptr != NULL) && (!udp_ptr->m_is_receiver);
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex_ret_t apex_udp_delete(apex_udp_t * const udp_ptr)
{
  apex_ret_t retval = APEX_RET_ERROR;
  if (udp_ptr != NULL) {
    retval = APEX_RET_OK;
    int32_t posix_error = -1;
    APEX_SKIP_IF(FI_MODE_0, (posix_error = closesocket(udp_ptr->m_socket)));
    if (posix_error != 0) {
      retval = APEX_RET_RESOURCE_LEAK;
    }
    //lint -e{586} NOLINT free is intended to be used here
    free(udp_ptr);
  }
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
/// \brief Sets the socket timeout configuration.
/// It will set the socket flag to O_NONBLOCK if the timeout is zero
/// It will reset the socket flag to non O_NONBLOCK if the timeout is non-zero
static apex_ret_t set_socket_timeout(
  struct udp_implementation_s * const udp_ptr,
  const apex_time_span_t receive_timeout)
{
  apex_ret_t retval = APEX_RET_ERROR;

  // Set up the timeout first, if it was not set up before that
  if (udp_ptr->m_last_time_span != receive_timeout) {
    struct timeval timeout;
    timeout.tv_sec = (time_t)apex_time_span_get_as_s(receive_timeout);
    uint32_t timeout_size = (uint32_t)sizeof(timeout);
#if defined(APEX_OSX) || defined(APEX_QNX)
    timeout.tv_usec = (int32_t)(apex_time_span_get_as_us(receive_timeout) % APEX_MILLION);
#else  // APEX_OSX
    timeout.tv_usec = apex_time_span_get_as_us(receive_timeout) % APEX_MILLION;
#endif  // APEX_OSX

    int32_t error_code = -1;
    APEX_SKIP_IF(FI_MODE_0,
      (error_code = setsockopt(
        udp_ptr->m_socket,
        SOL_SOCKET,
        SO_RCVTIMEO,
        (char *)&timeout,
        timeout_size)));

    if (error_code == 0) {
      // Set/reset O_NONBLOCK for zero timeout
      if (receive_timeout == 0) {
        udp_ptr->m_last_fcntl = fcntl(udp_ptr->m_socket, F_GETFL);
        error_code = -1;
        APEX_SKIP_IF(FI_MODE_1,
          //lint --e{9001, 9027}  // octal constant used in 3d party constant NOLINT
          /*
           AXIVION Comment Same Line MisraC2023-7.1: Reason: Code Quality (Functional
           suitability), Justification:
           octal constant used in 3d party constant NOLINT
           */
          (error_code = fcntl(udp_ptr->m_socket, F_SETFL,
          udp_ptr->m_last_fcntl | O_NONBLOCK)));
      } else if (udp_ptr->m_last_time_span == 0) {
        udp_ptr->m_last_fcntl = fcntl(udp_ptr->m_socket, F_GETFL);
        //lint --e{9001, 9027}  // octal constant used in 3d party constant NOLINT
        /*
         AXIVION Comment Same Line MisraC2023-7.1: Reason: Code Quality (Functional
         suitability), Justification:
         octal constant used in 3d party constant NOLINT
         */
        error_code = fcntl(udp_ptr->m_socket, F_SETFL, udp_ptr->m_last_fcntl & (~O_NONBLOCK));
      } else {
        error_code = 0;
      }
      // Finalize settings
      if (error_code == 0) {
        retval = APEX_RET_OK;
        udp_ptr->m_last_time_span = receive_timeout;
      }
    }
  } else {
    // udp_ptr->m_last_time_span == receive_timeout
    retval = APEX_RET_OK;
  }

  return retval;
}

static apex_ret_t update_socket_timeout(
  const struct udp_implementation_s * const udp_ptr,
  const apex_time_t deadline)
{
  apex_ret_t retval = APEX_RET_OK;
  struct timeval timeout;
  uint32_t timeout_size = (uint32_t)sizeof(timeout);
  apex_time_span_t remaining_time = apex_time_span_from_then(deadline);
  if (!apex_time_span_is_valid(remaining_time)) {
    retval = APEX_RET_ERROR;
  } else {
    // remaining_time = -(now - deadline)
    remaining_time = -remaining_time;
    if (remaining_time <= 0) {
      retval = APEX_RET_TIMEOUT;
    }
  }
  if (retval == APEX_RET_OK) {
    timeout.tv_sec = (time_t)apex_time_span_get_as_s(remaining_time);
#if defined(APEX_OSX) || defined(APEX_QNX)
    timeout.tv_usec = (int32_t)(apex_time_span_get_as_us(remaining_time) % APEX_MILLION);
#else  // APEX_OSX
    timeout.tv_usec = apex_time_span_get_as_us(remaining_time) % APEX_MILLION;
#endif  // APEX_OSX

    int32_t error_code = -1;
    APEX_SKIP_IF(FI_MODE_0,
      (error_code = setsockopt(
        udp_ptr->m_socket,
        SOL_SOCKET,
        SO_RCVTIMEO,
        (char *)&timeout,
        timeout_size)));

    if (error_code != 0) {
      retval = APEX_RET_ERROR;
    }
  }
  return retval;
}

static apex_ret_t handle_recv_error(
  const struct udp_implementation_s * const udp_ptr,
  const ssize_t recv_retval,
  const size_t recvd_bytes,
  const struct sockaddr * recv_addr
)
{
  apex_ret_t retval = APEX_RET_ERROR;
  uint32_t ip4_addr = 0U;
  const struct sockaddr_in * address_ptr = NULL;
  if (recv_retval != SOCKET_ERROR) {
    if (recvd_bytes > 0U) {
      // cast from sockaddr to sockaddr_in is valid only if sockaddr.sa_family is AF_INET
      // otherwise may cause undefined behavior
      /*
       AXIVION Next Line MisraC2023-10.4: Reason: Code Quality (Functional suitability),
       Justification: No side-effects with unsigned and a signed value comparison
       */
      if (recv_addr->sa_family == AF_INET) { /*lint !e9029 !e644 */
        const void * recv_addr_void_ptr = recv_addr;
        address_ptr = recv_addr_void_ptr;
        ip4_addr = address_ptr->sin_addr.s_addr;
        if (ip4_addr != udp_ptr->m_addr_to_send_to.sin_addr.s_addr) {
          retval = APEX_RET_WRONG_IP4;
        } else {
          retval = APEX_RET_OK;
        }
      } else {
        retval = APEX_RET_ERROR;
      }
    }
    //  return value will be 0 when the peer has performed an orderly shutdown
    if (recv_retval == 0) {
      retval = APEX_RET_ERROR;
    }
    APEX_DO_IF(FI_MODE_2, (retval = APEX_RET_ERROR));
  } else {
    int32_t last_error = EINVAL;
    APEX_SKIP_IF(FI_MODE_3 | FI_MODE_4, (last_error = WSAGetLastError()));
    APEX_DO_IF(FI_MODE_4, (last_error = WSAETIMEDOUT));
    if ((last_error == WSAEWOULDBLOCK) ||
        #if EAGAIN != WSAEWOULDBLOCK
      (last_error == EAGAIN) ||
        #endif  // EAGAIN != WSAEWOULDBLOCK
      (last_error == WSAETIMEDOUT))
    {
      retval = APEX_RET_TIMEOUT;
    } else if (last_error == EINTR) {
      // If receive was interrupted by delivery of a signal, do not exit and try to read again
      retval = APEX_RET_OK;
    } else {
      retval = APEX_RET_ERROR;
    }
  }
  return retval;
}

static apex_ret_t do_receive_buffer(
  struct udp_implementation_s * const udp_ptr,
  char * const receive_buffer,
  const size_t receive_size_bytes,
  const apex_time_span_t receive_timeout)
{
  bool is_timeout_valid = apex_time_span_is_valid(receive_timeout);
  apex_ret_t retval = APEX_RET_ERROR;
  if ((udp_ptr != NULL) &&
    udp_ptr->m_is_receiver &&
    is_timeout_valid &&
    (receive_buffer != NULL) &&
    (receive_size_bytes < 0xFFFFFFFFU) &&
    (receive_size_bytes > 0U) &&
    (receive_timeout >= 0LL))
  {
    // Calculate the maximum allowed deadline for this function
    apex_time_t deadline = apex_time_from_now(receive_timeout);
    if (!apex_time_is_valid(deadline)) {
      retval = APEX_RET_ERROR;
    } else {
      retval = set_socket_timeout(udp_ptr, receive_timeout);
    }

    // If timeout setup was successful, read the buffer
    if (retval == APEX_RET_OK) {
      struct sockaddr recv_addr;
      size_t recvd_bytes = 0U;
      ssize_t recv_retval = 0;
      while ((recvd_bytes < receive_size_bytes) &&
        (recv_retval != SOCKET_ERROR) &&
        (retval == APEX_RET_OK))
      {
        socklen_t addr_len = (uint32_t)sizeof(recv_addr);
        const size_t bytes_to_recv = receive_size_bytes - recvd_bytes;
        char * recv_ptr = &(receive_buffer[recvd_bytes]);
        APEX_SKIP_IF(FI_MODE_2 | FI_MODE_3 | FI_MODE_4,
          (recv_retval = recvfrom(
            udp_ptr->m_socket,
            recv_ptr,
            bytes_to_recv,
            0,
            &recv_addr,
            &addr_len)));

        const void * recv_addr_void_ptr = &recv_addr;
        const struct sockaddr_in * address_ptr = recv_addr_void_ptr;
        udp_ptr->m_receiver_source_port = ntohs(address_ptr->sin_port);

        APEX_DO_IF(FI_MODE_3 | FI_MODE_4, (recv_retval = SOCKET_ERROR));
        if (recv_retval != SOCKET_ERROR) {
          recvd_bytes += (size_t)recv_retval;
        }
        retval = handle_recv_error(udp_ptr, recv_retval, recvd_bytes, &recv_addr);
        // update timeout if O_NONBLOCK is not set
        if ((retval == APEX_RET_OK) && (receive_timeout != 0)) {
          retval = update_socket_timeout(udp_ptr, deadline);
        }
      }
    }
  }
  return retval;
}

static apex_ret_t receive_one_packet(
  struct udp_implementation_s * const udp_ptr,
  size_t * const packet_size,
  char * const receive_buffer,
  const size_t receive_size_bytes,
  const apex_time_span_t receive_timeout)
{
  bool is_timeout_valid = apex_time_span_is_valid(receive_timeout);
  apex_ret_t retval = APEX_RET_ERROR;
  if ((udp_ptr != NULL) &&
    udp_ptr->m_is_receiver &&
    is_timeout_valid &&
    (receive_buffer != NULL) &&
    (receive_size_bytes < 0xFFFFFFFFU) &&
    (receive_size_bytes > 0U) &&
    (receive_timeout >= 0LL))
  {
    // Calculate the maximum allowed deadline for this function
    apex_time_t deadline = apex_time_from_now(receive_timeout);
    if (!apex_time_is_valid(deadline)) {
      retval = APEX_RET_ERROR;
    } else {
      retval = set_socket_timeout(udp_ptr, receive_timeout);
    }

    // If timeout setup was successful, read the buffer
    if (retval == APEX_RET_OK) {
      struct sockaddr recv_addr;
      ssize_t recv_retval = 0;
      socklen_t addr_len = (uint32_t)sizeof(recv_addr);
      char * recv_ptr = &(receive_buffer[0U]);
      APEX_SKIP_IF(FI_MODE_2 | FI_MODE_3 | FI_MODE_4,
        (recv_retval = recvfrom(
          udp_ptr->m_socket,
          recv_ptr,
          receive_size_bytes,
          0,
          &recv_addr,
          &addr_len)));

      const void * recv_addr_void_ptr = &recv_addr;
      const struct sockaddr_in * address_ptr = recv_addr_void_ptr;
      udp_ptr->m_receiver_source_port = ntohs(address_ptr->sin_port);

      APEX_DO_IF(FI_MODE_3 | FI_MODE_4, (recv_retval = SOCKET_ERROR));
      if (recv_retval != SOCKET_ERROR) {
        *packet_size = (size_t)recv_retval;
      }
      retval = handle_recv_error(udp_ptr, recv_retval, (size_t)recv_retval, &recv_addr);
      // update timeout if O_NONBLOCK is not set
      if ((retval == APEX_RET_OK) && (receive_timeout != 0)) {
        retval = update_socket_timeout(udp_ptr, deadline);
      }
    }
  }
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex_ret_t apex_udp_receive(
  apex_udp_t * const udp_ptr,
  void * const receive_buffer,
  const size_t receive_size_bytes,
  const apex_time_span_t receive_timeout)
{
  apex_ret_t retval =
    do_receive_buffer(udp_ptr, receive_buffer, receive_size_bytes, receive_timeout);
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex_ret_t apex_udp_receive_one_packet(
  apex_udp_t * const udp_ptr,
  size_t * const packet_size,
  void * const receive_buffer,
  const size_t receive_size_bytes,
  const apex_time_span_t receive_timeout)
{
  apex_ret_t retval =
    receive_one_packet(udp_ptr, packet_size, receive_buffer, receive_size_bytes, receive_timeout);
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex_ret_t apex_udp_receive_ms(
  apex_udp_t * const udp_ptr,
  void * const receive_buffer,
  const size_t receive_size_bytes,
  const int64_t receive_timeout_ms
)
{
  return apex_udp_receive(
    udp_ptr,
    receive_buffer,
    receive_size_bytes,
    apex_time_span_from_ms(receive_timeout_ms)
  );
}

///////////////////////////////////////////////////////////////////////////////
apex_ret_t apex_udp_receive_one_packet_ms(
  apex_udp_t * const udp_ptr,
  size_t * const packet_size,
  void * const receive_buffer,
  const size_t receive_size_bytes,
  const int64_t receive_timeout_ms
)
{
  return apex_udp_receive_one_packet(
    udp_ptr,
    packet_size,
    receive_buffer,
    receive_size_bytes,
    apex_time_span_from_ms(receive_timeout_ms)
  );
}

///////////////////////////////////////////////////////////////////////////////
apex_ret_t apex_udp_send(
  apex_udp_t * const udp_ptr,
  const void * const send_buffer_ptr,
  const size_t bytes_number_to_send)
{
  apex_ret_t retval = APEX_RET_ERROR;
  if ((udp_ptr != NULL) &&
    (!udp_ptr->m_is_receiver) &&
    (send_buffer_ptr != NULL) &&
    (bytes_number_to_send < 0xFFFFFFFFU) &&
    (bytes_number_to_send > 0U))
  {
    void * void_ptr = &udp_ptr->m_addr_to_send_to;
    struct sockaddr * sock_addr_to_ptr = void_ptr;
#ifdef APEX_WINDOWS
    const int32_t actual_bytes_number_to_send = (int32_t)bytes_number_to_send;
    const int32_t m_addr_to_send_to_size = (int32_t)sizeof(udp_ptr->m_addr_to_send_to);
#else  // !APEX_WINDOWS
    const size_t actual_bytes_number_to_send = bytes_number_to_send;
    const uint32_t m_addr_to_send_to_size = (uint32_t)sizeof(udp_ptr->m_addr_to_send_to);
#endif  // APEX_WINDOWS

    ssize_t posix_retval = -1;
    APEX_SKIP_IF(FI_MODE_0 | FI_MODE_1,
      (posix_retval = sendto(
        udp_ptr->m_socket,
        send_buffer_ptr,
        actual_bytes_number_to_send,
        MSG_NOSIGNAL,
        sock_addr_to_ptr,
        m_addr_to_send_to_size)));
    APEX_DO_IF(FI_MODE_0, (posix_retval = -1));
    APEX_DO_IF(FI_MODE_1, (posix_retval = 1));

    if ((posix_retval > 0) &&
      ((size_t)(uint32_t)posix_retval == bytes_number_to_send))
    {
      retval = APEX_RET_OK;
    }
  }
  return retval;
}

apex_socket_t apex_udp_get_socket(const apex_udp_t * const udp_ptr)
{
  apex_socket_t retval = APEX_UTILS_INVALID_SOCKET;
  if (udp_ptr != NULL) {
    retval = udp_ptr->m_socket;
  }
  return retval;
}

uint16_t apex_udp_get_receiver_source_port(const apex_udp_t * const udp_ptr)
{
  uint16_t retval = 0;
  if (udp_ptr != NULL) {
    retval = udp_ptr->m_receiver_source_port;
  }

  return retval;
}

void apex_udp_set_destination_port(apex_udp_t * const udp_ptr, const uint16_t udp_port)
{
  if (udp_ptr != NULL) {
    udp_ptr->m_addr_to_send_to.sin_port = htons(udp_port);
  }
}
