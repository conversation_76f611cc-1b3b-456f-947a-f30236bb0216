// Copyright 2017-2018 Apex.AI, Inc.
// All rights reserved.

#include "apexutils/apexdef.h"
#include "apexutils/apex_rt.h"
#if defined(APEX_LINUX) || defined(APEX_QNX)
#include <sys/mman.h>
#include <malloc.h>
#include <unistd.h>
#include <sys/types.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <sched.h>
#include <pthread.h>
#if defined(APEX_LINUX)
#elif defined(QNX)
#include <sys/neutrino.h>
#include <sys/syspage.h>
#endif  // defined(APEX_LINUX)
#endif  // defined(APEX_LINUX) || defined(APEX_QNX)
#include "apexutils/apex_rt_detail.h"

/// \brief Perform memory related real-time initialization
///
/// Real-time initialization includes locking memory, disabling heap movement, and disabling mmap
/// \param[in] approx_max_mem_reqd_sz Approximate maximum memory size required for the process
/// \return APEX_RET_OK on success, error code on failure
APEXUTILS_PUBLIC apex_ret_t apex_proc_mem_rt_init(
  const size_t approx_max_mem_reqd_sz)
{
  apex_ret_t retval = APEX_RET_MEM_LOCK_ERR;
  // If ASan is enabled, skip memory locking and heap trimming
#if (defined(APEX_LINUX) || defined(APEX_QNX)) && !defined(ADDRESS_SANITIZER)
  void * buf = NULL;
  const int64_t pg_sz = sysconf(_SC_PAGESIZE);

  //
  // Lock the current and future memory in RAM.
  // NOTE: The entire process mem should be allocated during init phase.
  // Since that is not the case now, locking the future memory
  //
  // Anup Pemmaiah:
  // PCLint Description-1

  // (note: 9027) a signed value is not an appropriate left operand
  // to | [MISRA 2012 Rule 10.1, required]

  // mlockall is a Linux operating system system call. MCL_CURRENT and MCL_FUTURE are
  // the flags exported by the system call. We cannot change or cast them, as this might
  // interfere with the system call. In the future if there is a compliant system call
  // exist in Linux to lock memory, will switch to it
  //lint -e{838} NOLINT
  /*
   AXIVION Next Line MisraC2023-10.1: Reason: Code Quality (Functional suitability),
   Justification: mlockall is a Linux operating system system call.
   MCL_CURRENT and MCL_FUTURE are the flags exported by the system call.
   We cannot change or cast them, as this might
   interfere with the system call. In the future if there is a compliant system call
   exist in Linux to lock memory, will switch to it
   */
  if (0 == mlockall(MCL_CURRENT | MCL_FUTURE)) {  //lint !e9027 NOLINT
#if defined(APEX_LINUX)
    //
    // Disable all the heap trimming operation using the following option.
    // This avoid releasing of free mem back to the system
    //
    retval = APEX_RET_MEM_ALLOC_OPT_ERR;
    if (1 == mallopt(M_TRIM_THRESHOLD, -1)) {
      //
      // Disable mmap(). Because, memory allocated by mmap is outside the heap region
      // and when the memory is freed, it does not go back to the free list to be later
      // used by allocations. Also mmap() is an expensive task. Based on M_MAP_THRESOLD,
      // the kernel will either use mmap() or sbrk() to get the requested memory.
      // More info look at "man mallopt"
      //
      if (1 == mallopt(M_MMAP_MAX, 0)) {
        retval = APEX_RET_OK;
      }
    }
#elif defined(APEX_QNX)
    retval = APEX_RET_MEM_ALLOC_OPT_ERR;
    //
    // Request the QNX memory allocator to not release memory back to the OS
    //
    if (0 == mallopt(MALLOC_MEMORY_HOLD, 1)) {
      retval = APEX_RET_OK;
    }
#else  // APEX_LINUX
    retval = APEX_RET_OK;
#endif  // APEX_LINUX
  }

  if (APEX_RET_OK == retval) {
    if (approx_max_mem_reqd_sz > 0U) {
      //
      // Since all the memory is not statically allocated yet, Allocate and free
      // the max memory a process is planning to use so all pages gets mapped and locked in
      // the process addr space
      //
      if (pg_sz > 0) {
        retval = APEX_RET_MEM_ALIGN_ERR;
        if (0 == posix_memalign(&buf, (size_t)pg_sz, approx_max_mem_reqd_sz)) {
          (void)memset(buf, 0, approx_max_mem_reqd_sz);
          // Anup Pemmaiah:
          // PCLint Description-2

          // (warning: 586) function 'free' is deprecated. [MISRA 2012 Directive 4.12, required],
          // [MISRA 2012 Rule 21.3, required]
          //
          // This area of memory is not used while it is allocated. The mem is alloc'd, zeroed
          // and freed so that it is in the linux kernel system resource pool for future
          // allocations

          //lint -e{586, 424} NOLINT
          /*
           This area of memory is not used while it is allocated. The mem is alloc'd, zeroed
           and freed so that it is in the linux kernel system resource pool for future
           allocations
           */
          free(buf);
          retval = APEX_RET_OK;
        }
      }
    }
  }
#else  // !APEX_LINUX || !APEX_QNX
#if defined(ADDRESS_SANITIZER)
  retval = APEX_RET_OK;
#endif
  (void)approx_max_mem_reqd_sz;
#endif  // APEX_LINUX || APEX_QNX
  return retval;
}

///
/// \brief Perform cpu related real-time initialization such as setting process
/// level cpu affinity
/// \param[in]cpu_bit_mask_in cpu affinity of all the threads in the process
/// \return APEX_RET_OK on success, error code on failure
APEXUTILS_PUBLIC apex_ret_t apex_proc_cpu_rt_init(
  const uint64_t cpu_bit_mask_in)
{
  apex_ret_t retval = APEX_RET_ERROR;

  //
  // Set thread-cpu affinity
  //

  if (cpu_bit_mask_in > 0U) {
#ifdef APEX_LINUX
    cpu_set_t set;
    uint64_t cpu_bit_mask = cpu_bit_mask_in;
    uint32_t cpu_cnt = 0U;
    // Anup Pemmaiah:
    // PC Lint Descritpion-3
    // This description is regarding the following MISRA rules
    //
    // (info: 718) function 'CPU_ZERO' undeclared, assumed to return int
    // [MISRA 2012 Rule 17.3, mandatory]
    // (info: 746) call to function 'CPU_ZERO()' not made in the presence of a prototype
    //
    // PCLint should be able to find these instances
    // CPU_ZERO, CPU_SET and sched_setaffinity is declared as part of sched.h linux
    // system file. "sched.h" is exposed as part of feature test macros
    // (feature_test_macros(7)) _GNU_SOURCE
    // sched.h is a very stable Linux system file
    // Since this is a Linux Operating System call, we cannot modify them.
    // We might in the future switch to a more compliant cpu affinity function if there
    // exists one as part of the linux operating system
    //lint -efunc(718,apex_proc_cpu_rt_init)  NOLINT
    //lint -e{746} NOLINT
    /*
     AXIVION Next Line Rule MisraC2023-14.4, MisraC2023-17.7: Reason: Code Quality (Functional
     suitability), Justification: CPU_ZERO is part of sched.h system header and is ok
     */
    CPU_ZERO(&set);
    while (cpu_bit_mask > 0U) {
      if ((cpu_bit_mask & 0x1U) > 0U) {
        apex_cpu_set_macro_wrapper(cpu_cnt, &set);
      }
      cpu_bit_mask = (cpu_bit_mask >> 1U);
      cpu_cnt++;
    }
    retval = apex_set_cpu_affinity_mask(getpid(), &set);
#elif defined(QNX)
    uint32_t num_elements = 0u;
    int32_t * rsizep, * rmaskp, * imaskp;
    uint64_t masksize_bytes, size;
    void * my_data;

    // http://www.qnx.com/developers/docs/7.0.0/index.html#com.qnx.doc.neutrino.prog/topic/multicore_thread_affinity.html
    // Determine the number of array elements required to hold
    // the runmasks, based on the number of CPUs in the system.

    num_elements = (uint32_t)RMSK_SIZE(_syspage_ptr->num_cpu);

    // Determine the size of the runmask, in bytes.
    masksize_bytes = num_elements * sizeof(uint32_t);

    // Allocate memory for the data structure that we'll pass
    // to ThreadCtl(). We need space for an integer (the number
    // of elements in each mask array) and the two masks
    // (runmask and inherit mask).

    size = sizeof(int32_t) + 2u * masksize_bytes;
    if (NULL != (my_data = malloc(size))) {
      memset(my_data, 0x00, size);

      // Set up pointers to the "members" of the structure.
      rsizep = (int32_t *)my_data;
      rmaskp = rsizep + 1u;
      imaskp = rmaskp + num_elements;

      // Set the size.
      *rsizep = (int32_t)num_elements;

      // Set the runmask and inherit mask. Call this macro once for each processor
      // the thread and its children can run on.
      uint64_t cmask = cpu_bit_mask_in;
      uint32_t cpu = 0u;
      while (cmask > 0u) {
        if (cmask & 1u) {
          RMSK_SET(cpu, rmaskp);
          RMSK_SET(cpu, imaskp);
        }
        cpu++;
        cmask = (cmask >> 1u);
      }

      if (ThreadCtl(_NTO_TCTL_RUNMASK_GET_AND_SET_INHERIT, my_data) != -1) {
        // Successfully set the cpu affinity
        retval = APEX_RET_OK;
      }
    }
#endif  // APEX_LINUX
  }
  return retval;
}

APEXUTILS_PUBLIC apex_ret_t apex_set_cpu_affinity(const apex_cpuset_t mask)
{
  return apex_set_cpu_affinity_for_pid(getpid(), mask);
}

APEXUTILS_PUBLIC apex_ret_t apex_set_cpu_affinity_for_pid(pid_t pid, const apex_cpuset_t mask)
{
  if (apex_cpu_is_empty(&mask)) {
    return APEX_RET_SET_CPU_AFFINITY_ERR;
  }

  apex_ret_t retval = APEX_RET_SET_CPU_AFFINITY_ERR;
#ifdef APEX_LINUX
  cpu_set_t set = apex_cpuset_to_cpu_set(mask);
  retval = apex_set_cpu_affinity_mask(pid, &set);
#elif defined(QNX)
  int tid = 1;  // In QNX the thread 1 is the main thread of the process
  void * runmask = apex_cpuset_to_runmask(mask);
  if (runmask != NULL) {
    if (ThreadCtlExt(pid, tid, _NTO_TCTL_RUNMASK_GET_AND_SET_INHERIT, runmask) != -1) {
      // Successfully set the cpu affinity
      retval = APEX_RET_OK;
    }
  }
#else
  // error
#endif  // APEX_LINUX
  return retval;
}

APEXUTILS_PUBLIC apex_ret_t apex_get_cpu_affinity_for_pid(pid_t pid, apex_cpuset_t * mask)
{
  apex_ret_t retval = APEX_RET_GET_CPU_AFFINITY_ERR;
#ifdef APEX_LINUX
  cpu_set_t set;
  retval = apex_get_cpu_affinity_mask(pid, &set);
  if (retval == APEX_RET_OK) {
    cpu_set_to_apex_cpuset(set, mask);
  }
#elif defined(QNX)
  int tid = 1;  // In QNX the thread 1 is the main thread of the process
  // set runmask to zero to get the current value
  unsigned int runmask = 0;
  if (ThreadCtlExt(pid, tid, _NTO_TCTL_RUNMASK_GET_AND_SET, &runmask) != -1) {
    apex_cpu_setmask(runmask, mask);
    retval = APEX_RET_OK;
  }
#else
  // error
#endif  // APEX_LINUX

  return retval;
}

APEXUTILS_PUBLIC apex_ret_t apex_set_priority(int32_t process_priority, int32_t scheduling_policy)
{
  return apex_set_priority_for_pid(getpid(), process_priority, scheduling_policy);
}

APEXUTILS_PUBLIC
apex_ret_t apex_set_priority_for_pid(pid_t pid, int32_t process_priority, int32_t scheduling_policy)
{
  struct sched_param sp;
  sp.sched_priority = process_priority;
  int32_t posix_ret = -1;
  int32_t min = sched_get_priority_min(scheduling_policy);
  int32_t max = sched_get_priority_max(scheduling_policy);
  if ((min <= process_priority) && (process_priority <= max)) {
    posix_ret = sched_setscheduler(pid, scheduling_policy, &sp);
  }

  apex_ret_t retval = (0 == posix_ret) ? APEX_RET_OK : APEX_RET_ERROR;
  return retval;
}
