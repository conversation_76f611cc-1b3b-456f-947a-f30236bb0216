// Copyright 2017-2018 Apex.AI, Inc.
// All rights reserved.
#include <string.h>
#include "apexutils/apexutils.h"
#include "apexutils/apexutils_detail.h"

#ifdef APEX_WINDOWS
  #include <windows.h>
#elif defined(APEX_LINUX) || defined(APEX_OSX) || defined(APEX_QNX)
  #include <unistd.h>
  #include <sys/stat.h>
  #include <sched.h>
#elif defined(APEX_MICRO)
#include <stdio.h>
#else  // defined(APEX_LINUX)
#error unsupported OS
#endif  // APEX_WINDOWS

///////////////////////////////////////////////////////////////////////////////
static volatile uint64_t apex_init_flags_storage = 0ULL;

#ifndef APEX_MICRO
static DIR * proc_dir_p = NULL;
#endif

///////////////////////////////////////////////////////////////////////////////
apex_ret_t apex_initialize(
  int32_t argc,
  const char * const * argv,
  uint64_t apex_init_flags,
  int32_t process_priority)
{
  (void)argc;
  (void)argv;
  apex_init_flags_storage = apex_init_flags;
  apex_ret_t retval = APEX_RET_ERROR;

  // If process priority is not set by default it will be 0. In that case set the
  // error code to be OK
  if (0 == process_priority) {
    retval = APEX_RET_OK;
  }

#ifndef APEX_MICRO
  //lint -e{931} we know apex_get_os_priority_min doesn't have side effect NOLINT
  /*
   AXIVION Next Line MisraC2023-13.5: we know apex_get_os_priority_min doesn't have side effect
   */
  int32_t policy = get_apex_sched();
  if (apex_in_range_i32(process_priority,
    apex_get_os_priority_min(policy),
    apex_get_os_priority_max(policy)))
  {
    retval = set_current_proc_prio(process_priority);
  }

  // initialise the proc directory pointer
  proc_dir_p = opendir("/proc");
#endif

  return retval;
}

bool apex_in_range_i32(const int32_t x, const int32_t a, const int32_t b)
{
  return (a <= x) && (x <= b);
}

bool apex_in_range_f64(const double x, const double a, const double b)
{
  return (a <= x) && (x <= b);
}

bool apex_is_bit_u64(const uint64_t x, const uint32_t bit_n)
{
  if (bit_n >= 64U) {
    return false;
  } else {
    return ((x >> bit_n) & 1ULL) != 0ULL;
  }
}

uint64_t apex_bit_clear_u64(const uint64_t x, const uint32_t bit_n)
{
  return x & (~apex_bit_mask_u64(bit_n));
}

uint64_t apex_bit_mask_u64(const uint32_t bit_n)
{
  if (bit_n >= 64U) {
    return 0ULL;
  } else {
    return 1ULL << bit_n;
  }
}

uint64_t apex_bit_u64(const uint64_t x, const uint32_t bit_n)
{
  return x & apex_bit_mask_u64(bit_n);
}

bool apex_is_init_bit(const uint64_t bit_mask)
{
  return (apex_init_flags_storage & bit_mask) != 0ULL;
}

uint64_t apex_get_init_flags(void)
{
  return apex_init_flags_storage;
}

#ifndef APEX_MICRO
uint64_t apex_atomic_add_u64(volatile uint64_t * const addend_ptr, const uint64_t value)
{
  volatile void * void_ptr = addend_ptr;
  volatile int64_t * int64_t_ptr = (volatile int64_t *)void_ptr;
  int64_t ivalue = (int64_t)value;
#ifdef APEX_WINDOWS
  return (uint64_t)InterlockedAdd64(int64_t_ptr, ivalue);
#else  // APEX_WINDOWS
  //lint -e{746} NOLINT GCC intrinsic
  __sync_synchronize();
  /*
   AXIVION Next Line Rule Compiler-#668, MisraC2023-1.2: Reason: Code
   Quality (Functional suitability), Justification: Used GCC
   atomic memory access built-in function
   */
  return (uint64_t)__sync_add_and_fetch(int64_t_ptr, ivalue);
#endif  // APEX_WINDOWS
}

uint64_t apex_atomic_inc_u64(volatile uint64_t * const addend_ptr)
{
  return apex_atomic_add_u64(addend_ptr, 1ULL);
}

uint64_t apex_atomic_cmp_and_swap_u64(
  volatile uint64_t * const dest_ptr,
  const uint64_t comparand,
  const uint64_t new_value)
{
#ifdef APEX_WINDOWS
  int64_t icomparand = (int64_t)comparand;
  int64_t inew_value = (int64_t)new_value;
  volatile void * void_ptr = dest_ptr;
  volatile int64_t * int64_t_ptr = void_ptr;
  return (uint64_t)InterlockedCompareExchange64(int64_t_ptr, inew_value, icomparand);
#else  // APEX_WINDOWS
  //lint -e{746} NOLINT GCC intrinsic
  __sync_synchronize();
  //lint -e{9034} -e{713} False positive of implicit conversion NOLINT
  return __sync_val_compare_and_swap_8(dest_ptr, comparand, new_value);
#endif  // APEX_WINDOWS
}

uint64_t apex_atomic_set_u64(volatile uint64_t * const dest_ptr, const uint64_t new_value)
{
#ifdef APEX_WINDOWS
  int64_t inew_value = (int64_t)new_value;
  volatile void * void_ptr = dest_ptr;
  volatile int64_t * int64_t_ptr = void_ptr;
  return (uint64_t)InterlockedExchange64(int64_t_ptr, inew_value);
#else  // APEX_WINDOWS
  //lint -e{746} NOLINT GCC intrinsic
  __sync_synchronize();
  //lint -e{9034} -e{713} False positive of implicit conversion NOLINT
  return __sync_lock_test_and_set_8(dest_ptr, new_value);
#endif  // APEX_WINDOWS
}
#endif

apex_ret_t apex_get_host_name(char * const read_buffer, const size_t read_size)
{
#ifdef APEX_MICRO
  snprintf(read_buffer, read_size, "%s", "apex_micro");
  return APEX_RET_OK;
#else
  apex_ret_t retval = APEX_RET_ERROR;
  if (read_buffer != NULL) {
    if (0 == gethostname(read_buffer, read_size)) {
      retval = APEX_RET_OK;
    }
  }

  return retval;
#endif
}

void apex_copy_string_tail(
  char * const dst,
  const size_t dst_size,
  const char * const src,
  const size_t max_src_len)
{
  if ((dst_size > 0ULL) && (max_src_len > 0ULL) && (NULL != dst) && (NULL != src)) {
    const char * src_ptr = src;
    size_t copy_size = strnlen(src_ptr, max_src_len);
    if (copy_size >= dst_size) {
      /*
       AXIVION Next Line MisraC2023-18.4: Reason: Code Quality (Functional suitability),
       Justification:
       There are no other way to avoid pointer arithmetic in this case
       */
      src_ptr += (copy_size - (dst_size - 1UL));
      copy_size = dst_size - 1UL;
    }
    if (copy_size > 0UL) {
      // Ignore unneeded returned dst pointer
      (void)memmove(dst, src_ptr, copy_size);
    }
    dst[copy_size] = '\0';
  }
}

apex_string_t apex_u32_to_string(const uint32_t value)
{
  apex_string_t retval = apex_string_create();
  static const uint32_t dividers[10U] = {
    1000000000U,
    100000000U,
    10000000U,
    1000000U,
    100000U,
    10000U,
    1000U,
    100U,
    10U,
    1U
  };

  static const char digits[10U] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};

  char * ptr = &retval.c_str[0U];
  uint32_t my_value = value;
  bool digit_happened = false;
  for (uint32_t i = 0; i < 10U; ++i) {
    uint32_t digit = (my_value / dividers[i]) % 10U;
    if ((digit != 0U) || (i == 9U) || digit_happened) {
      *ptr = digits[digit];
      ptr++;
      my_value -= (digit * dividers[i]);
      digit_happened = true;
    }
  }
  return retval;
}

int64_t apex_get_ticks_per_second(void)
{
#ifdef APEX_QNX
  // In QNX _SC_CLK_TCK is defined as an int, unlike in Linux which is enum
  // Static cast is removed because the compiler complains about
  // useless cast to type 'int32_t {aka int}'
  return sysconf(_SC_CLK_TCK);
#elif defined(APEX_LINUX)
  return sysconf((int32_t) _SC_CLK_TCK);
#else
  return -1;
#endif
}

#ifndef APEX_MICRO
DIR * get_proc_dir_ptr(void)
{
  return proc_dir_p;
}

// Moved from apex_task after it's being deprecated

int32_t get_apex_sched(void)
{
  // I use reversed logic here to increase test coverage
  int32_t retval = SCHED_RR;
  if (!apex_is_init_bit(APEX_INIT_BIT_SCHED_RR)) {
    retval = SCHED_FIFO;
    if (!apex_is_init_bit(APEX_INIT_BIT_SCHED_FIFO)) {
      retval = SCHED_OTHER;
    }
  }
  return retval;
}

int32_t apex_get_os_priority_min(int32_t scheduling_policy)
{
  int32_t retval = INT_MIN;
  int32_t os_prio_min = -1;
  //lint -e{838} Allow default value being overridden immediately and not being used NOLINT
  os_prio_min = sched_get_priority_min(scheduling_policy);
  if (-1 != os_prio_min) {
    retval = os_prio_min;
  }
  return retval;
}

int32_t apex_get_os_priority_max(int32_t scheduling_policy)
{
  int32_t retval = INT_MIN;
  int32_t os_prio_max = -1;
  //lint -e{838} Allow default value being overridden immediately and not being used NOLINT
  os_prio_max = sched_get_priority_max(scheduling_policy);
  if (-1 != os_prio_max) {
    retval = os_prio_max;
  }
  return retval;
}
#endif
