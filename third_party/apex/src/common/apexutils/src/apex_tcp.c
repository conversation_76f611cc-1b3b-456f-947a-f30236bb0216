// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include <assert.h>
#include <stdlib.h>
#include "apexutils/apexdef.h"
#include "apexutils/apex_string.h"
#include "apexutils/apex_time.h"
#include "apexutils/apex_tcp.h"
#include "apexutils/apexutils.h"

APEX_DEFINE_FAILURE_INJECTION(apex_tcp)

APEXUTILS_PUBLIC apex_ret_t apex_tcp_connect(
  apex_tcp_t * const tcp_ptr,
  const char * const ip4_address_str,
  const uint16_t tcp_port)
{
  assert(tcp_ptr != NULL);

  uint32_t ip4_address = inet_addr(ip4_address_str);
  apex_ret_t ret_val = APEX_RET_ERROR;

  //lint -e{9048} NOLINT unsigned without a 'U' in 3d party constant INADDR_NONE
  /*
   AXIVION Next Line MisraC2023-7.2: Reason: Code Quality (Functional suitability),
   Justification: unsigned without a 'U' in 3d party constant INADDR_NONE
   */
  if (ip4_address != INADDR_NONE) {
    tcp_ptr->m_tcp_port = tcp_port;
    tcp_ptr->m_last_time_span = apex_time_span_get_invalid();
    tcp_ptr->m_ip_addr.sin_family = AF_INET;
    tcp_ptr->m_ip_addr.sin_port = htons(tcp_port);
    tcp_ptr->m_ip_addr.sin_addr.s_addr = ip4_address;
    tcp_ptr->m_socket = APEX_UTILS_INVALID_SOCKET;

    APEX_SKIP_IF(FI_MODE_2,
      (tcp_ptr->m_socket = socket(PF_INET, (int32_t)SOCK_STREAM, (int32_t)IPPROTO_TCP)));

    if (tcp_ptr->m_socket != APEX_UTILS_INVALID_SOCKET) {
      const uint32_t m_ip_addr_size = (uint32_t)sizeof(tcp_ptr->m_ip_addr);
      void * void_ptr = &tcp_ptr->m_ip_addr;
      struct sockaddr * sock_addr_ptr = void_ptr;

      APEX_SKIP_IF(FI_MODE_0 | FI_MODE_1,
        (ret_val = connect(tcp_ptr->m_socket, sock_addr_ptr, m_ip_addr_size)));
    }
  }

  return ret_val;
}

APEXUTILS_PUBLIC apex_ret_t apex_tcp_listen(
  apex_tcp_listener_t * const tcp_ptr,
  const char * const ip4_address_str,
  const uint16_t tcp_port)
{
  assert(tcp_ptr != NULL);

  uint32_t ip4_address = inet_addr(ip4_address_str);
  apex_ret_t ret_val = APEX_RET_ERROR;

  //lint -e{9048} NOLINT unsigned without a 'U' in 3d party constant INADDR_NONE
  /*
   AXIVION Next Line MisraC2023-7.2: Reason: Code Quality (Functional suitability),
   Justification: unsigned without a 'U' in 3d party constant INADDR_NONE
   */
  if (ip4_address != INADDR_NONE) {
    tcp_ptr->m_tcp_port = tcp_port;
    tcp_ptr->m_last_time_span = apex_time_span_get_invalid();
    tcp_ptr->m_ip_addr.sin_family = AF_INET;
    tcp_ptr->m_ip_addr.sin_port = htons(tcp_port);
    tcp_ptr->m_ip_addr.sin_addr.s_addr = ip4_address;
    tcp_ptr->m_socket = APEX_UTILS_INVALID_SOCKET;

    APEX_SKIP_IF(FI_MODE_2,
      (tcp_ptr->m_socket = socket(PF_INET, (int32_t)SOCK_STREAM, (int32_t)IPPROTO_TCP)));

    if (tcp_ptr->m_socket != APEX_UTILS_INVALID_SOCKET) {
      void * void_ptr = &tcp_ptr->m_ip_addr;
      struct sockaddr * sock_addr_ptr = void_ptr;

      APEX_SKIP_IF(FI_MODE_0 | FI_MODE_1,
        (ret_val = bind(
          tcp_ptr->m_socket, sock_addr_ptr, (uint32_t)sizeof(tcp_ptr->m_ip_addr))));
      if (ret_val >= 0) {
        if (listen(tcp_ptr->m_socket, 0) == APEX_RET_OK) {
          ret_val = APEX_RET_OK;
        }
      }
    }
  }

  return ret_val;
}

apex_tcp_t apex_tcp_try_accept_client(const apex_tcp_listener_t * const tcp_ptr)
{
  assert(tcp_ptr != NULL);

  struct sockaddr_in peer_addr;
  socklen_t peer_addr_size = (socklen_t)sizeof(struct sockaddr_in);

  int32_t error_code = -1;
  APEX_SKIP_IF(FI_MODE_1,
    //lint --e{9001, 9027}  // octal constant used in 3d party constant NOLINT
    /*
     AXIVION Comment Same Line MisraC2023-7.1: Reason: Code Quality (Functional suitability),
     Justification: octal constant used in 3d party constant NOLINT
     */
    (error_code = fcntl(tcp_ptr->m_socket, F_SETFL, O_NONBLOCK)));

  apex_tcp_t ret_val;
  ret_val.m_socket = SOCKET_ERROR;

  if (error_code == 0) {
    void * void_ptr = &peer_addr;
    struct sockaddr * sock_addr_ptr = void_ptr;

    SOCKET_T accepted_socket = SOCKET_ERROR;
    APEX_SKIP_IF(FI_MODE_0 | FI_MODE_1,
      (accepted_socket = accept(tcp_ptr->m_socket, sock_addr_ptr, &peer_addr_size)));

    ret_val.m_ip_addr = peer_addr;
    ret_val.m_socket = accepted_socket;
  }

  return ret_val;
}

apex_ret_t apex_tcp_send(
  apex_tcp_t * const tcp_ptr,
  const void * const send_buffer_ptr,
  const size_t bytes_number_to_send)
{
  assert(tcp_ptr != NULL);
  assert(send_buffer_ptr != NULL);

  apex_ret_t ret_val = APEX_RET_ERROR;
  if ((bytes_number_to_send < 0x7FFFFFFFU)) {
    void * void_ptr = &tcp_ptr->m_ip_addr;
    struct sockaddr * sock_addr_to_ptr = void_ptr;
#ifdef APEX_WINDOWS
    const int32_t actual_bytes_number_to_send = (int32_t)bytes_number_to_send;
    const int32_t m_ip_addr_size = (int32_t)sizeof(tcp_ptr->m_ip_addr);
#else  // !APEX_WINDOWS
    const size_t actual_bytes_number_to_send = bytes_number_to_send;
    const uint32_t m_ip_addr_size = (uint32_t)sizeof(tcp_ptr->m_ip_addr);
#endif  // APEX_WINDOWS

    ssize_t posix_ret_val = -1;
    APEX_SKIP_IF(FI_MODE_0 | FI_MODE_1,
      (posix_ret_val = sendto(
        tcp_ptr->m_socket,
        send_buffer_ptr,
        actual_bytes_number_to_send,
        MSG_NOSIGNAL,
        sock_addr_to_ptr,
        m_ip_addr_size)));
    APEX_DO_IF(FI_MODE_0, (posix_ret_val = -1));
    APEX_DO_IF(FI_MODE_1, (posix_ret_val = 1));

    if ((posix_ret_val > 0) &&
      ((size_t)(uint32_t)posix_ret_val == bytes_number_to_send))
    {
      ret_val = APEX_RET_OK;
    }
  }
  return ret_val;
}

static apex_ret_t tcp_handle_recv_error(
  const ssize_t recv_ret_val,
  const size_t recvd_bytes
)
{
  apex_ret_t ret_val = APEX_RET_ERROR;
  if (recv_ret_val != SOCKET_ERROR) {
    if (recvd_bytes > 0U) {
      ret_val = APEX_RET_OK;
    }
    //  return value will be 0 when the peer has performed an orderly shutdown
    if (recv_ret_val == 0) {
      ret_val = APEX_RET_ERROR;
    }
    APEX_DO_IF(FI_MODE_2, (ret_val = APEX_RET_ERROR));
  } else {
    int32_t last_error = EINVAL;
    APEX_SKIP_IF(FI_MODE_3 | FI_MODE_4, (last_error = WSAGetLastError()));
    APEX_DO_IF(FI_MODE_4, (last_error = WSAETIMEDOUT));
    if ((last_error == WSAEWOULDBLOCK) ||
        #if EAGAIN != WSAEWOULDBLOCK
      (last_error == EAGAIN) ||
        #endif  // EAGAIN != WSAEWOULDBLOCK
      (last_error == WSAETIMEDOUT))
    {
      // EAGAIN is not quite reducible to a timeout error
      ret_val = APEX_RET_TIMEOUT;
    } else if (last_error == EINTR) {
      // If receive was interrupted by delivery of a signal, do not exit and try to read again
      ret_val = APEX_RET_OK;
    } else {
      ret_val = APEX_RET_ERROR;
    }
  }
  return ret_val;
}

static apex_ret_t tcp_update_socket_timeout(
  const apex_time_t deadline, SOCKET_T socket)
{
  apex_ret_t ret_val = APEX_RET_OK;
  struct timeval timeout;
  uint32_t timeout_size = (uint32_t)sizeof(timeout);
  apex_time_span_t remaining_time = apex_time_span_from_then(deadline);
  if (!apex_time_span_is_valid(remaining_time)) {
    ret_val = APEX_RET_ERROR;
  } else {
    // remaining_time = -(now - deadline)
    remaining_time = -remaining_time;
    if (remaining_time <= 0) {
      ret_val = APEX_RET_TIMEOUT;
    }
  }
  if (ret_val == APEX_RET_OK) {
    timeout.tv_sec = (time_t)apex_time_span_get_as_s(remaining_time);
#if defined(APEX_OSX) || defined(APEX_QNX)
    timeout.tv_usec = (int32_t)(apex_time_span_get_as_us(remaining_time) % APEX_MILLION);
#else  // APEX_OSX
    timeout.tv_usec = apex_time_span_get_as_us(remaining_time) % APEX_MILLION;
#endif  // APEX_OSX

    int32_t error_code = -1;
    APEX_SKIP_IF(FI_MODE_0,
      (error_code = setsockopt(
        socket,
        SOL_SOCKET,
        SO_RCVTIMEO,
        (char *)&timeout,
        timeout_size)));

    if (error_code != 0) {
      ret_val = APEX_RET_ERROR;
    }
  }
  return ret_val;
}

static apex_ret_t tcp_set_socket_timeout(
  struct tcp_implementation_s * const tcp_ptr,
  const apex_time_span_t receive_timeout, SOCKET_T socket)
{
  assert(tcp_ptr != NULL);
  apex_ret_t ret_val = APEX_RET_ERROR;

  // Set up the timeout first, if it was not set up before that
  if (tcp_ptr->m_last_time_span != receive_timeout) {
    struct timeval timeout;
    timeout.tv_sec = (time_t)apex_time_span_get_as_s(receive_timeout);
    uint32_t timeout_size = (uint32_t)sizeof(timeout);
#if defined(APEX_OSX) || defined(APEX_QNX)
    timeout.tv_usec = (int32_t)(apex_time_span_get_as_us(receive_timeout) % APEX_MILLION);
#else  // APEX_OSX
    timeout.tv_usec = apex_time_span_get_as_us(receive_timeout) % APEX_MILLION;
#endif  // APEX_OSX

    int32_t error_code = -1;
    APEX_SKIP_IF(FI_MODE_0,
      (error_code = setsockopt(
        socket,
        SOL_SOCKET,
        SO_RCVTIMEO,
        (char *)&timeout,
        timeout_size)));

    if (error_code == 0) {
      // Set/reset O_NONBLOCK for zero timeout
      if (receive_timeout == 0) {
        tcp_ptr->m_last_fcntl = fcntl(socket, F_GETFL);
        error_code = -1;
        APEX_SKIP_IF(FI_MODE_1,
          //lint --e{9001, 9027}  // octal constant used in 3d party constant NOLINT
          /*
           AXIVION Comment Same Line MisraC2023-7.1: Reason: Code Quality (Functional
           suitability), Justification: octal constant used in 3d party constant NOLINT
           */
          (error_code = fcntl(socket, F_SETFL,
          tcp_ptr->m_last_fcntl | O_NONBLOCK)));
      } else if (tcp_ptr->m_last_time_span == 0) {
        tcp_ptr->m_last_fcntl = fcntl(socket, F_GETFL);
        //lint --e{9001, 9027}  // octal constant used in 3d party constant NOLINT
        /*
         AXIVION Comment Same Line MisraC2023-7.1: Reason: Code Quality (Functional
         suitability), Justification: octal constant used in 3d party constant NOLINT
         */
        error_code = fcntl(socket, F_SETFL, tcp_ptr->m_last_fcntl & (~O_NONBLOCK));
      } else {
        error_code = 0;
      }
      // Finalize settings
      if (error_code == 0) {
        ret_val = APEX_RET_OK;
        tcp_ptr->m_last_time_span = receive_timeout;
      }
    }
  } else {
    ret_val = APEX_RET_OK;
  }

  return ret_val;
}

apex_ret_t apex_tcp_receive(
  apex_tcp_t * const tcp_ptr,
  char * receive_buffer,
  const size_t receive_size_bytes,
  const int64_t receive_timeout_ms)
{
  assert(tcp_ptr != NULL);
  assert(receive_buffer != NULL);

  apex_time_span_t receive_timeout = apex_time_span_from_ms(receive_timeout_ms);
  bool is_timeout_valid = apex_time_span_is_valid(receive_timeout);
  apex_ret_t ret_val = APEX_RET_ERROR;

  if (is_timeout_valid &&
    (receive_timeout >= 0LL))
  {
    // Calculate the maximum allowed deadline for this function
    apex_time_t deadline = apex_time_from_now(receive_timeout);
    if (!apex_time_is_valid(deadline)) {
      ret_val = APEX_RET_ERROR;
    } else {
      ret_val = tcp_set_socket_timeout(tcp_ptr, receive_timeout, tcp_ptr->m_socket);
    }

    size_t recvd_bytes = 0U;
    ssize_t recv_ret_val = 0;
    while ((recvd_bytes < receive_size_bytes) &&
      (recv_ret_val != SOCKET_ERROR) &&
      (ret_val == APEX_RET_OK))
    {
      const size_t bytes_to_recv = receive_size_bytes - recvd_bytes;
      char * recv_ptr = &(receive_buffer[recvd_bytes]);
      APEX_SKIP_IF(FI_MODE_2 | FI_MODE_3 | FI_MODE_4,
        (recv_ret_val = recv(
          tcp_ptr->m_socket,
          recv_ptr,
          bytes_to_recv,
          0
      )));

      APEX_DO_IF(FI_MODE_3 | FI_MODE_4, (recv_ret_val = SOCKET_ERROR));
      if (recv_ret_val != SOCKET_ERROR) {
        recvd_bytes += (size_t)recv_ret_val;
      }
      ret_val = tcp_handle_recv_error(recv_ret_val, recvd_bytes);
      // update timeout if O_NONBLOCK is not set
      if ((ret_val == APEX_RET_OK) && (receive_timeout != 0)) {
        ret_val = tcp_update_socket_timeout(deadline, tcp_ptr->m_socket);
      }
    }
  }

  return ret_val;
}

apex_ret_t apex_tcp_close_socket(apex_socket_t socket)
{
  apex_ret_t ret_val = APEX_RET_OK;
  int32_t posix_error = -1;
  if (socket != APEX_UTILS_INVALID_SOCKET) {
    ret_val = APEX_RET_ERROR;
    APEX_SKIP_IF(FI_MODE_0, (posix_error = closesocket(socket)));
    if (posix_error == 0) {
      ret_val = APEX_RET_OK;
    }
  }

  return ret_val;
}

uint32_t apex_tcp_htonl(uint32_t host_long)
{
  return htonl(host_long);
}

uint16_t apex_tcp_htons(uint16_t host_short)
{
  return htons(host_short);
}

//lint -e{9046} NOLINT function name is the same as wrapped POSIX function.
uint32_t apex_tcp_ntohl(uint32_t net_long)
{
  return ntohl(net_long);
}

//lint -e{9046} NOLINT function name is the same as wrapped POSIX function.
uint16_t apex_tcp_ntohs(uint16_t net_short)
{
  return ntohs(net_short);
}
