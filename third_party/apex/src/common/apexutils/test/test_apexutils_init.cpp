// Copyright 2017-2018 Apex.AI, Inc.
// All rights reserved.

#include <sys/types.h>
#include <dirent.h>
#include <vector>
#include <string>

#include "gtest/gtest.h"
#include "apexutils/apexutils.h"

uint64_t apex_get_current_sched(void)
{
  const int32_t current_sched = get_apex_sched();
  uint64_t retval;
  if (SCHED_RR == current_sched) {
    retval = APEX_INIT_BIT_SCHED_RR;
  } else if (SCHED_FIFO == current_sched) {
    retval = APEX_INIT_BIT_SCHED_FIFO;
  } else {
    retval = 0U;
  }
  return retval;
}

/// @test{
/// "req" : ["SWRQ_APEXUTILS_498"]
/// }
TEST(test_apexutils_init, test_apexutils_init) {
  EXPECT_NE(apex_initialize(0U, nullptr, 0U, INT_MIN), APEX_RET_OK);
  (void)closedir(get_proc_dir_ptr());

  // Test get_apex_sched for SCHED_OTHER
  EXPECT_NE(
    apex_initialize(0U, nullptr, 0U, INT_MIN), APEX_RET_OK);
  EXPECT_EQ(apex_get_current_sched(), 0U);
  (void)closedir(get_proc_dir_ptr());

  // Test get_apex_sched for SCHED_FIFO
  EXPECT_EQ(
    apex_initialize(0U, nullptr, APEX_INIT_BIT_SCHED_FIFO, 0),
    APEX_RET_OK);
  EXPECT_EQ(apex_get_current_sched(), APEX_INIT_BIT_SCHED_FIFO);
  (void)closedir(get_proc_dir_ptr());

  // Test get_apex_sched for SCHED_RR
  EXPECT_EQ(
    apex_initialize(0U, nullptr, APEX_INIT_BIT_SCHED_RR, 0), APEX_RET_OK);
  EXPECT_EQ(apex_get_current_sched(), APEX_INIT_BIT_SCHED_RR);
  (void)closedir(get_proc_dir_ptr());

  uint64_t apex_init_flags = (APEX_INIT_BIT_NO_CONSOLE | APEX_INIT_BIT_TEST);
  EXPECT_EQ(
    apex_initialize(0U, nullptr,
    APEX_INIT_BIT_SCHED_RR,
    0), APEX_RET_OK);
  (void)closedir(get_proc_dir_ptr());

  EXPECT_EQ(
    apex_initialize(0U, nullptr, apex_init_flags, 0),
    APEX_RET_OK);
  (void)closedir(get_proc_dir_ptr());

  EXPECT_FALSE(apex_is_init_bit(APEX_INIT_BIT_SCHED_FIFO));
  EXPECT_FALSE(apex_is_init_bit(APEX_INIT_BIT_SCHED_RR));
  EXPECT_EQ(apex_get_init_flags(), apex_init_flags);
}

// dirfd does not exist for QNX
#ifndef QNX
TEST(test_apexutils_init, initializes_proc_dir_ptr) {
  uint64_t apex_init_flags = (APEX_INIT_BIT_NO_CONSOLE | APEX_INIT_BIT_TEST);
  ASSERT_EQ(
    apex_initialize(0U, nullptr, apex_init_flags, 0),
    APEX_RET_OK);

  DIR * dir1 = get_proc_dir_ptr();
  DIR * dir2 = opendir("/proc");

  struct stat stat1, stat2;
  bool same_file =
    (fstat(dirfd(dir1), &stat1) == 0) &&
    (fstat(dirfd(dir2), &stat2) == 0) &&
    (stat1.st_dev == stat2.st_dev) &&
    (stat1.st_ino == stat2.st_ino);

  EXPECT_TRUE(same_file);

  (void)closedir(dir1);
  (void)closedir(dir2);
}
#endif
