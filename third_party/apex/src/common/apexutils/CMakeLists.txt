cmake_minimum_required(VERSION 3.5)

project(apexutils)

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()

include_directories(include)
include_directories(include_detail)

set(apexutils_sources
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:include/apexutils/apex_cpuset.h>
  include/apexutils/apex_failure_injection.h
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:include/apexutils/apex_dir.h>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:include/apexutils/apex_file.h>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:include/apexutils/apex_process.h>
  include/apexutils/apex_string.h
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:include/apexutils/apex_time.h>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:include/apexutils/apex_udp.h>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:include/apexutils/apex_tcp.h>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:include/apexutils/apex_pipe2.h>
  include/apexutils/apex_rt.h
  include/apexutils/apex_wraparound.h
  include/apexutils/apexdef.h
  include/apexutils/apexutils.h
  include/apexutils/apexutils_local.h
  include/apexutils/visibility_control.h
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:include_detail/apexutils/apex_rt_detail.h>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:include_detail/apexutils/apex_process_detail.h>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:include_detail/apexutils/apexutils_detail.h>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/apex_cpuset.c>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/apex_dir.c>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/apex_file.c>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/apex_process.c>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/apex_process_detail.c>
  src/apex_strings.c
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/apex_time.c>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/apex_udp.c>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/apex_tcp.c>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/apex_rt.c>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/apex_pipe2.c>
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/apex_rt_detail.c>
  src/apex_wraparound.c
  src/apexutils.c
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/apexutils_detail.c>
)

set_source_files_properties(
  ${apexutils_sources}
  PROPERTIES language "C")

add_library(
  ${PROJECT_NAME}
  SHARED
  ${apexutils_sources})

if(VECTORCAST_BUILD)
  vcast_set_io_cover_cxx(${PROJECT_NAME})
endif()
apex_set_compile_options(${PROJECT_NAME})

if(CMAKE_SYSTEM_PROCESSOR STREQUAL "tricore")
  target_compile_options(${PROJECT_NAME} PRIVATE
    -Wno-error=conversion -Wno-error=sign-conversion
  )
endif()

set(APEXUTILS_TEST_DIR "/tmp/")

if(WIN32)
  target_link_libraries(${PROJECT_NAME} "Ws2_32.lib")
  # use https://sourceforge.net/projects/pthreads4w/
  include_directories("C:/PTHREADS-BUILT/include")
  target_link_libraries(${PROJECT_NAME} "C:/PTHREADS-BUILT/lib/libpthreadVCE3d.lib")
elseif(${APPLE})
  target_link_libraries(${PROJECT_NAME} "dl")
  target_link_libraries(${PROJECT_NAME} "m")
  target_link_libraries(${PROJECT_NAME} "pthread")
  target_compile_options(${PROJECT_NAME} PRIVATE -Werror)
elseif(${QNX})
  target_link_libraries(${PROJECT_NAME} "m")
  target_compile_options(${PROJECT_NAME} PRIVATE -Werror -Wno-deprecated-declarations)
else()
  target_link_libraries(${PROJECT_NAME} "rt")
  target_link_libraries(${PROJECT_NAME} "m")
  target_link_libraries(${PROJECT_NAME} "pthread")
  target_compile_options(${PROJECT_NAME} PRIVATE -Werror -D_GNU_SOURCE -Wno-deprecated-declarations)
endif()
target_compile_definitions(${PROJECT_NAME} PRIVATE
    APEXUTILS_TEST_DIR="${APEXUTILS_TEST_DIR}")

if(BUILD_TESTING)
  find_package(ament_cmake_gmock REQUIRED)
  find_package(ament_cmake_nose REQUIRED)
  find_package(ament_lint_auto REQUIRED)
  find_package(apex_test_tools REQUIRED)

  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()

  ament_add_gmock(test_apex_file_gmock
    test/test_apex_file_gmock.cpp)
  if(VECTORCAST_BUILD)
    vcast_set_io_cover_cxx(test_apex_file_gmock)
  endif()
  target_link_libraries(test_apex_file_gmock ${PROJECT_NAME})
  target_compile_options(test_apex_file_gmock PRIVATE
    -Wno-missing-field-initializers
  )
  target_compile_definitions(test_apex_file_gmock PRIVATE
    APEXUTILS_TEST_DIR="${APEXUTILS_TEST_DIR}")

  ament_add_gmock(test_apex_udp_gmock
      test/test_apex_udp_gmock.cpp)
  if(VECTORCAST_BUILD)
    vcast_set_io_cover_cxx(test_apex_udp_gmock)
  endif()
  target_link_libraries(test_apex_udp_gmock ${PROJECT_NAME})
  target_compile_options(test_apex_udp_gmock PRIVATE
    -Wno-missing-field-initializers
  )
  target_compile_definitions(test_apex_udp_gmock PRIVATE
    APEXUTILS_TEST_DIR="${APEXUTILS_TEST_DIR}")

  ament_add_gtest(test_apexutils_init
    test/test_apexutils_init.cpp)
  if(VECTORCAST_BUILD)
    vcast_set_io_cover_cxx(test_apexutils_init)
  endif()
  target_link_libraries(test_apexutils_init ${PROJECT_NAME})
  target_compile_options(test_apexutils_init PRIVATE
    -Wno-missing-field-initializers
  )
  target_compile_definitions(test_apexutils_init PRIVATE
    APEXUTILS_TEST_DIR="${APEXUTILS_TEST_DIR}")

  apex_test_tools_add_gtest(test_apexutils
    test/test_apex_cpuset.cpp
    test/test_apex_dir.cpp
    test/test_apex_file.cpp
    test/test_apex_pipe2.cpp
    test/test_apex_process.cpp
    test/test_apex_string.cpp
    test/test_apex_time.cpp
    test/test_apexutils.cpp
    test/test.cpp)
  if(VECTORCAST_BUILD)
    vcast_set_io_cover_cxx(test_apexutils)
  endif()
  target_link_libraries(test_apexutils ${PROJECT_NAME})
  target_compile_options(test_apexutils PRIVATE
    -Wno-missing-field-initializers
    -Wno-unknown-pragmas
    -Wno-deprecated-declarations)
  target_compile_definitions(test_apexutils PRIVATE
    APEXUTILS_TEST_DIR="${APEXUTILS_TEST_DIR}")

  ament_add_gtest(test_apexutils_udp_and_rt
    test/test_apex_failure_injection.cpp
    test/test_apex_udp.cpp
    test/test_apex_wraparound.cpp
    test/test_apex_rt.cpp)
  target_compile_options(test_apexutils_udp_and_rt PRIVATE
    -Wno-missing-field-initializers
    -Wno-deprecated-declarations
  )
  target_include_directories(test_apexutils_udp_and_rt PRIVATE include_detail)
  if(VECTORCAST_BUILD)
    vcast_set_io_cover_cxx(test_apexutils_udp_and_rt)
  endif()
  target_link_libraries(test_apexutils_udp_and_rt ${PROJECT_NAME})

endif()
list(APPEND ${PROJECT_NAME}_LIBRARIES
  "${PROJECT_NAME}"
)
ament_auto_package()
