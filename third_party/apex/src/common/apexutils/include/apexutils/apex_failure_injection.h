/// \copyright Copyright 2017-2018 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file contains failure injection macros

#ifndef APEXUTILS__APEX_FAILURE_INJECTION_H_
#define APEXUTILS__APEX_FAILURE_INJECTION_H_

/// \defgroup apex-failure-injection Failure Injection macros
/// \ingroup apexos-macros
/// \brief A grouping of definitions for Apex.OS Failure Injection.
/// @{

/// \def APEX_IS_BIT
/// \brief APEX_IS_BIT is a C99-only fast bit checker (MISRA C++ prohibits function-like macros)
/// \param[in] val is the value to check
/// \param[in] bit is a bit number in [0..(N-1)] range
/// \return The return value is either logical true or false
#define APEX_IS_BIT(val, bit) ((((val) >> (bit)) & 1U) != 0U)
//lint -emacro(835, APEX_IS_BIT, FI_MODE_0, APEX_SKIP_IF, APEX_BIT) NOLINT allow zero bit

/// \def APEX_BIT
/// \brief APEX_BIT is a C99-only bit setter macro (MISRA C++ prohibits function-like macros)
/// \param[in] bit is a bit number in [0..(N-1)] range
/// \return The return value is a bit mask with `bit` set to 1 and all other bits zeroed
#define APEX_BIT(bit) (1LLU << (bit))

#define FI_MODE_0 APEX_BIT(0U)  ///< Failure Injection Mode 0
#define FI_MODE_1 APEX_BIT(1U)  ///< Failure Injection Mode 1
#define FI_MODE_2 APEX_BIT(2U)  ///< Failure Injection Mode 2
#define FI_MODE_3 APEX_BIT(3U)  ///< Failure Injection Mode 3
#define FI_MODE_4 APEX_BIT(4U)  ///< Failure Injection Mode 4
#define FI_MODE_5 APEX_BIT(5U)  ///< Failure Injection Mode 5
#define FI_MODE_6 APEX_BIT(6U)  ///< Failure Injection Mode 6
#define FI_MODE_7 APEX_BIT(7U)  ///< Failure Injection Mode 7
#define FI_MODE_8 APEX_BIT(8U)  ///< Failure Injection Mode 8
#define FI_MODE_9 APEX_BIT(9U)  ///< Failure Injection Mode 9
#define FI_MODE_10 APEX_BIT(10U)  ///< Failure Injection Mode 10
#define FI_MODE_11 APEX_BIT(11U)  ///< Failure Injection Mode 11
#define FI_MODE_12 APEX_BIT(12U)  ///< Failure Injection Mode 12
#define FI_MODE_13 APEX_BIT(13U)  ///< Failure Injection Mode 13
#define FI_MODE_14 APEX_BIT(14U)  ///< Failure Injection Mode 14
#define FI_MODE_15 APEX_BIT(15U)  ///< Failure Injection Mode 15
#define FI_MODE_16 APEX_BIT(16U)  ///< Failure Injection Mode 16
#define FI_MODE_17 APEX_BIT(17U)  ///< Failure Injection Mode 17
#define FI_MODE_18 APEX_BIT(18U)  ///< Failure Injection Mode 18
#define FI_MODE_19 APEX_BIT(19U)  ///< Failure Injection Mode 19
#define FI_MODE_20 APEX_BIT(20U)  ///< Failure Injection Mode 20
#define FI_MODE_21 APEX_BIT(21U)  ///< Failure Injection Mode 21
#define FI_MODE_22 APEX_BIT(22U)  ///< Failure Injection Mode 22
#define FI_MODE_23 APEX_BIT(23U)  ///< Failure Injection Mode 23
#define FI_MODE_24 APEX_BIT(24U)  ///< Failure Injection Mode 24
#define FI_MODE_25 APEX_BIT(25U)  ///< Failure Injection Mode 25
#define FI_MODE_26 APEX_BIT(26U)  ///< Failure Injection Mode 26
#define FI_MODE_27 APEX_BIT(27U)  ///< Failure Injection Mode 27
#define FI_MODE_28 APEX_BIT(28U)  ///< Failure Injection Mode 28
#define FI_MODE_29 APEX_BIT(29U)  ///< Failure Injection Mode 29
#define FI_MODE_30 APEX_BIT(30U)  ///< Failure Injection Mode 30
#define FI_MODE_31 APEX_BIT(31U)  ///< Failure Injection Mode 31
#define FI_MODE_32 APEX_BIT(32U)  ///< Failure Injection Mode 32
#define FI_MODE_33 APEX_BIT(33U)  ///< Failure Injection Mode 33
#define FI_MODE_34 APEX_BIT(34U)  ///< Failure Injection Mode 34
#define FI_MODE_35 APEX_BIT(35U)  ///< Failure Injection Mode 35
#define FI_MODE_36 APEX_BIT(36U)  ///< Failure Injection Mode 36
#define FI_MODE_37 APEX_BIT(37U)  ///< Failure Injection Mode 37
#define FI_MODE_38 APEX_BIT(38U)  ///< Failure Injection Mode 38
#define FI_MODE_39 APEX_BIT(39U)  ///< Failure Injection Mode 39
#define FI_MODE_40 APEX_BIT(40U)  ///< Failure Injection Mode 40
#define FI_MODE_41 APEX_BIT(41U)  ///< Failure Injection Mode 41
#define FI_MODE_42 APEX_BIT(42U)  ///< Failure Injection Mode 42
#define FI_MODE_43 APEX_BIT(43U)  ///< Failure Injection Mode 43
#define FI_MODE_44 APEX_BIT(44U)  ///< Failure Injection Mode 44
#define FI_MODE_45 APEX_BIT(45U)  ///< Failure Injection Mode 45
#define FI_MODE_46 APEX_BIT(46U)  ///< Failure Injection Mode 46
#define FI_MODE_47 APEX_BIT(47U)  ///< Failure Injection Mode 47
#define FI_MODE_48 APEX_BIT(48U)  ///< Failure Injection Mode 48
#define FI_MODE_49 APEX_BIT(49U)  ///< Failure Injection Mode 49
#define FI_MODE_50 APEX_BIT(50U)  ///< Failure Injection Mode 50
#define FI_MODE_51 APEX_BIT(51U)  ///< Failure Injection Mode 51
#define FI_MODE_52 APEX_BIT(52U)  ///< Failure Injection Mode 52
#define FI_MODE_53 APEX_BIT(53U)  ///< Failure Injection Mode 53
#define FI_MODE_54 APEX_BIT(54U)  ///< Failure Injection Mode 54
#define FI_MODE_55 APEX_BIT(55U)  ///< Failure Injection Mode 55
#define FI_MODE_56 APEX_BIT(56U)  ///< Failure Injection Mode 56
#define FI_MODE_57 APEX_BIT(57U)  ///< Failure Injection Mode 57
#define FI_MODE_58 APEX_BIT(58U)  ///< Failure Injection Mode 58
#define FI_MODE_59 APEX_BIT(59U)  ///< Failure Injection Mode 59
#define FI_MODE_60 APEX_BIT(60U)  ///< Failure Injection Mode 60
#define FI_MODE_61 APEX_BIT(61U)  ///< Failure Injection Mode 61
#define FI_MODE_62 APEX_BIT(62U)  ///< Failure Injection Mode 62
#define FI_MODE_63 APEX_BIT(63U)  ///< Failure Injection Mode 63

// Add Failure Modes here as you need them

#define FI_MODE_CLEAR (0U)  ///< Disable failure mode (set the non-failed state)

#define FI_MODE_ALL (0xFFFFFFFFFFFFFFFFULL)  ///< Activate all failure modes

/// \def APEX_FAILURE_VARIABLE_NAME
/// \brief Supplemental macro for internal use
#define APEX_FAILURE_VARIABLE_NAME g_failure_mode

/// \def APEX_FAILURE_GETTER_NAME
/// \brief Supplemental macro for internal use
#define APEX_FAILURE_GETTER_NAME(module) module ## _get_inject_failure

/// \def APEX_GET_FAILURE_IN_MODULE
// \brief Macro for failure getter. It returns the current failure mode of a `module`
/// \param[in] module to get failure mode for
#define APEX_GET_FAILURE_IN_MODULE(module) APEX_FAILURE_GETTER_NAME(module)()

/// \def APEX_DECLARE_FAILURE_INJECTION
/// \brief Declare a failure injector. Use this macro in a module header file
/// \param[in] module to declare the failure setter for
#define APEX_DECLARE_FAILURE_INJECTION(module) \
  void module ## _inject_failure(const uint64_t injected_failure)

/// \def APEX_DECLARE_FAILURE_INJECTION_GETTER
/// \brief Declare a failure getter. Use this macro in a module header file
/// \param[in] module to declare the failure getter for
#define APEX_DECLARE_FAILURE_INJECTION_GETTER(module) \
  uint64_t APEX_FAILURE_GETTER_NAME(module)(void)

/// \def APEX_DEFINE_FAILURE_INJECTION
/// \brief Declare a failure injector function. Use this macro in a module source file
/// \param[in] module to define failure mode setter for
#define APEX_DEFINE_FAILURE_INJECTION(module) \
  static uint64_t APEX_FAILURE_VARIABLE_NAME = 0U; \
  void module ## _inject_failure(const uint64_t injected_failure) \
  { \
    APEX_FAILURE_VARIABLE_NAME = injected_failure; \
  }

/// \def APEX_DEFINE_GET_INJECTED_FAILURE
/// \brief Declare a failure getter function. Use this macro in a module source file
/// \param[in] module to define the failure getter for
#define APEX_DEFINE_GET_INJECTED_FAILURE(module) \
  uint64_t APEX_FAILURE_GETTER_NAME(module)(void) \
  { \
    return APEX_FAILURE_VARIABLE_NAME; \
  }

/// \def APEX_INJECT_FAILURE_IN_MODULE
/// \brief Inject failure into a `module`. `failure_mode` is a set of OR-ed failure modes
/// \param[in] module to set failure mode in
/// \param[in] failure_mode is a bitmask of failure modes
#define APEX_INJECT_FAILURE_IN_MODULE(module, failure_mode) \
  module ## _inject_failure(failure_mode)

/*
 AXIVION DISABLE STYLE MisraC2023-20.7: Reason: Code Quality (Functional suitability),
 Justification: parameter [code] is used only once inside macro.
 */
/// \def APEX_SKIP_IF
/// \brief Skip `code` in if the current's module's failure mode includes provided failure_modes
/// \param[in] failure_modes bitmask of failure modes to check
/// \param[inout] code to execute if the current failure mode is not in the bitmask
#define APEX_SKIP_IF(failure_modes, code) \
  if ((APEX_FAILURE_VARIABLE_NAME & (failure_modes)) == 0U) { \
    code; \
  }

/// \def APEX_SKIP_IF_IN_MODULE
/// \brief Skip code if failure mode in `module` includes provided failure_modes
/// \param[in] module to check
/// \param[in] failure_modes is a bitmask of failure modes to check
/// \param[inout] code to execute if the current failure mode is not in the bitmask
#define APEX_SKIP_IF_IN_MODULE(module, failure_modes, code) \
  if ((APEX_GET_FAILURE_IN_MODULE(module) & (failure_modes)) == 0U) { \
    code; \
  }

/// \def APEX_DO_IF
/// \brief Execute `code` in if the current's module's failure mode includes provided failure_modes
/// \param[in] failure_modes is a bitmask of failure modes to check
/// \param[inout] code to execute if the current failure mode coincides with the bitmask
#define APEX_DO_IF(failure_modes, code) \
  if ((APEX_FAILURE_VARIABLE_NAME & (failure_modes)) != 0U) { \
    code; \
  }

/// \def APEX_DO_IF_IN_MODULE
/// \brief Execute code if failure mode in `module` includes provided failure_modes
/// \param[in] module to check
/// \param[in] failure_modes is a bitmask of failure modes to check
/// \param[inout] code to execute if the current failure mode coincides with the bitmask
#define APEX_DO_IF_IN_MODULE(module, failure_modes, code) \
  if ((APEX_GET_FAILURE_IN_MODULE(module) & (failure_modes)) != 0U) { \
    code; \
  }
// AXIVION ENABLE STYLE MisraC2023-20.7:
/// @}  End of apex-failure-injection

#endif  // APEXUTILS__APEX_FAILURE_INJECTION_H_
