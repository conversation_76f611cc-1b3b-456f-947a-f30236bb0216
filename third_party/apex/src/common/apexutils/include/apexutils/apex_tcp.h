/// \copyright Copyright 2022 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file declares TCP API used in Apex.OS applications

#ifndef APEXUTILS__APEX_TCP_H_
#define APEXUTILS__APEX_TCP_H_

#include <apexutils/visibility_control.h>
#include <apexutils/apex_time.h>

#ifndef I_NEED_NETWORK_DEFS
  #define I_NEED_NETWORK_DEFS (1)  // Enable network compatibility macros definitions
#endif  // I_NEED_NETWORK_DEFS

#include <apexutils/apexutils_local.h>
#include "apexutils/apexdef.h"

#ifdef __cplusplus
extern "C"
{
#endif  // __cplusplus

struct tcp_implementation_s
{
  uint16_t m_tcp_port;
  SOCKET_T m_socket;
  struct sockaddr_in m_ip_addr;
  apex_time_span_t m_last_time_span;
#ifndef APEX_WINDOWS
  int32_t m_last_fcntl;
#endif  // APEX_WINDOWS
};
/// \typedef apex_tcp_t
/// \brief apex_tcp_t is a type used for TCP client
typedef struct tcp_implementation_s apex_tcp_t;

/// \typedef apex_tcp_t
/// \brief apex_tcp_t is a type used for TCP server
typedef struct tcp_implementation_s apex_tcp_listener_t;

/// \brief create an Apex.OS TCP client object
/// \param[in, out] tcp_ptr is a pointer to tcp client connection data
/// \param[in] ip4_address is IP4 server address
/// \param[in] tcp_port is IP4 TCP port to receive the data from
/// \return APEX_RET_OK if created, APEX_RET_ERROR
APEXUTILS_PUBLIC apex_ret_t apex_tcp_connect(
  apex_tcp_t * const tcp_ptr,
  const char * const ip4_address_str,
  const uint16_t tcp_port);

/// \brief create an Apex.OS TCP server object
/// \param[in, out] tcp_ptr is a pointer to tcp server connection data
/// \param[in] ip4_address is IP4 server address
/// \param[in] tcp_port is IP4 TCP port to receive the data from
/// \return APEX_RET_OK if created, APEX_RET_ERROR
APEXUTILS_PUBLIC apex_ret_t apex_tcp_listen(
  apex_tcp_listener_t * const tcp_ptr,
  const char * const ip4_address_str,
  const uint16_t tcp_port);

/// \brief try to accept new TCP client
/// \param[in, out] tcp_ptr is a pointer to tcp server
/// \return correct socket wrapper if accepted, if not socket wrapper keeps INVALID socket
APEXUTILS_PUBLIC apex_tcp_t apex_tcp_try_accept_client(
  const apex_tcp_listener_t * const tcp_ptr);

/// \brief send data via the tcp object.
/// \param[in] tcp_ptr is an Apex.OS TCP object
/// \param[in] send_buffer_ptr is a buffer that contains the data to send
/// The buffer should be at least of bytes_number_to_send size
/// \param[in] bytes_number_to_send is number of bytes to send
/// Number of bytes should be less than 0xFFFFFFFFU (4GB - 1).
/// \return Return APEX_RET_OK if the the data was sent,
/// \return APEX_RET_ERROR if other error happened
APEXUTILS_PUBLIC apex_ret_t apex_tcp_send(
  apex_tcp_t * const tcp_ptr,
  const void * const send_buffer_ptr,
  const size_t bytes_number_to_send);

/// \brief receive data via the tcp object. In Windows, received_timeout should be less than
/// UINT_MAX milliseconds (about 50 days)
/// \param[in] tcp_ptr is an Apex.OS TCP object
/// \param[in, out] receive_buffer is a buffer that will contain the received data
/// The buffer should be at least of receive_size_bytes size
/// \param[in] receive_size_bytes is number of bytes to receive
/// Number of bytes should be less than 0xFFFFFFFFU (4GB - 1).
/// \param[in] receive_timeout_ms is max time duration to receive the required data.
/// If the timeout is zero the OS socket switches to O_NONBLOCK mode.
/// \return Return APEX_RET_OK if the the data was received,
/// \return APEX_RET_TIMEOUT if we did not get the data in received_timeout_ms
/// \return APEX_RET_ERROR if other error happened
/// \return APEX_RET_WRONG_IP4 the TCP client got data from unexpected IP4 server
APEXUTILS_PUBLIC apex_ret_t apex_tcp_receive(
  apex_tcp_t * const tcp_ptr,
  char * receive_buffer,
  const size_t receive_size_bytes,
  const int64_t receive_timeout_ms);

/// \brief close TCP client socket
/// \param[in] socket to close
/// \return Return APEX_RET_OK if the object was closed,
/// APEX_RET_ERROR if error
APEXUTILS_PUBLIC apex_ret_t apex_tcp_close_socket(apex_socket_t socket);

/// \brief simple wrapper for htonl system function.
/// \param[in] host_long value to change the endian.
/// \return Return value in network endian format.
//lint -e{9046} NOLINT function name is the same as wrapped POSIX function.
/*
 AXIVION Next Line MisraC2023Directive-4.5 see comment above.
 */
APEXUTILS_PUBLIC uint32_t apex_tcp_htonl(uint32_t host_long);

/// \brief simple wrapper for htons system function.
/// \param[in] host_long value to change the endian.
/// \return Return value in network endian format.
//lint -e{9046} NOLINT function name is the same as wrapped POSIX function.
/*
 AXIVION Next Line MisraC2023Directive-4.5 see comment above.
 */
APEXUTILS_PUBLIC uint16_t apex_tcp_htons(uint16_t host_short);

/// \brief simple wrapper for ntohl system function.
/// \param[in] host_long value to change the endian.
/// \return Return value in host endian format.
//lint -e{9046} NOLINT function name is the same as wrapped POSIX function.
APEXUTILS_PUBLIC uint32_t apex_tcp_ntohl(uint32_t net_long);

/// \brief simple wrapper for ntohs system function.
/// \param[in] host_long value to change the endian.
/// \return Return value in host endian format.
//lint -e{9046} NOLINT function name is the same as wrapped POSIX function.
APEXUTILS_PUBLIC uint16_t apex_tcp_ntohs(uint16_t net_short);

APEXUTILS_PUBLIC APEX_DECLARE_FAILURE_INJECTION(apex_tcp);

#ifdef __cplusplus
}
#endif  // __cplusplus

#endif  // APEXUTILS__APEX_TCP_H_
