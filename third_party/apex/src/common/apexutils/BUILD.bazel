load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

filegroup(
    name = "apexutil_hdrs",
    srcs = [
        "include/apexutils/apex_failure_injection.h",
        "include/apexutils/apex_rt.h",
        "include/apexutils/apex_string.h",
        "include/apexutils/apex_time.h",
        "include/apexutils/apex_wraparound.h",
        "include/apexutils/apexdef.h",
        "include/apexutils/apexutils.h",
        "include/apexutils/apexutils_local.h",
        "include/apexutils/visibility_control.h",
    ] + select({
        "//:apex_micro": [],
        "//conditions:default": [
            "include/apexutils/apex_cpuset.h",
            "include/apexutils/apex_dir.h",
            "include/apexutils/apex_file.h",
            "include/apexutils/apex_pipe2.h",
            "include/apexutils/apex_process.h",
            "include/apexutils/apex_tcp.h",
            "include/apexutils/apex_udp.h",
        ],
    }),
    visibility = [":__subpackages__"],
)

apex_cc_library(
    name = "apexutils",
    srcs = [
        "src/apex_strings.c",
        "src/apex_wraparound.c",
        "src/apexutils.c",
    ] + select({
        "//:apex_micro": [],
        "//conditions:default": [
            "src/apex_cpuset.c",
            "src/apex_dir.c",
            "src/apex_file.c",
            "src/apex_pipe2.c",
            "src/apex_process.c",
            "src/apex_process_detail.c",
            "src/apex_rt.c",
            "src/apex_rt_detail.c",
            "src/apex_tcp.c",
            "src/apex_time.c",
            "src/apex_udp.c",
            "src/apexutils_detail.c",
        ],
    }),
    hdrs = [":apexutil_hdrs"],
    applicable_licenses = ["//common/integrity:embedded_gold"],
    defines = [
        "_GNU_SOURCE",
        "APEXUTILS_TEST_DIR=\\\"/tmp/\\\"",
    ],
    linkstatic = select({
        "//:apex_micro": True,
        "//conditions:default": False,
    }),
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        ":include_detail",
        "@coverage_tool//:coverage_io_lib",
    ],
)

apex_cc_library(
    name = "include_detail",
    hdrs = glob(["include_detail/**/*.h"]),
    strip_include_prefix = "include_detail",
    tags = ["same-ros-pkg-as: apexutils"],
)

filegroup(
    name = "test_apexutils_init_srcs_with_req_ids",
    srcs = ["test/test_apexutils_init.cpp"],
    visibility = ["//common/apexutils/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_apexutils_init",
    srcs = [":test_apexutils_init_srcs_with_req_ids"],
    deps = [
        ":apexutils",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "test_apexutils_srcs_with_req_ids",
    srcs = [
        "test/test_apex_cpuset.cpp",
        "test/test_apex_dir.cpp",
        "test/test_apex_file.cpp",
        "test/test_apex_pipe2.cpp",
        "test/test_apex_process.cpp",
        "test/test_apex_string.cpp",
        "test/test_apex_time.cpp",
        "test/test_apexutils.cpp",
    ],
    visibility = ["//common/apexutils/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_apexutils",
    srcs = [
        "test/test.cpp",
        ":test_apexutils_srcs_with_req_ids",
    ],
    copts = ["-Wno-unknown-pragmas"],
    tags = [
        "constrained_test",
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        ":apexutils",
        "//tools/testing/apex_test_tools",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "test_apexutils_udp_and_rt_srcs_with_req_ids",
    srcs = [
        "test/test_apex_failure_injection.cpp",
        "test/test_apex_rt.cpp",
        "test/test_apex_udp.cpp",
        "test/test_apex_wraparound.cpp",
    ],
    visibility = ["//common/apexutils/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_apexutils_udp_and_rt",
    srcs = [":test_apexutils_udp_and_rt_srcs_with_req_ids"],
    # TODO(kyle.marcey): #24903 Remove line below
    copts = ["-Wno-error"],
    tags = [
        "skip_asan",  # FIXME: fix asan findings and remove this tag
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
    ],
    deps = [
        ":apexutils",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "test_apex_file_gmock_srcs_with_req_ids",
    srcs = ["test/test_apex_file_gmock.cpp"],
    visibility = ["//common/apexutils/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_apex_file_gmock",
    srcs = [":test_apex_file_gmock_srcs_with_req_ids"],
    deps = [
        ":apexutils",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "test_apex_udp_gmock_srcs_with_req_ids",
    srcs = ["test/test_apex_udp_gmock.cpp"],
    visibility = ["//common/apexutils/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_apex_udp_gmock",
    srcs = [":test_apex_udp_gmock_srcs_with_req_ids"],
    tags = [
        "constrained_test",
        "exclusive",
    ],
    deps = [
        ":apexutils",
        "@googletest//:gtest_main",
    ],
)

ros_pkg(
    name = "apexutils_pkg",
    cc_libraries = [
        ":apexutils",
    ],
    description = "Package containing various utility macros, types and functions for Apex.OS code",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Anup Pemmaiah",
    pkg_name = "apexutils",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
