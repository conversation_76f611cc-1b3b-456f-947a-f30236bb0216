cmake_minimum_required(VERSION 3.5)

project(cpputils)

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()

if(APEX_MICRO AND APEX_MICRO_DISABLE_CXX)
  message("C++ support is disabled on ${CMAKE_SYSTEM_PROCESSOR} platform. Skipping the package")
  ament_package()
  return()
endif()

set(cpputils_sources
  include/bounded_vector/bounded_vector.hpp
  include/cpputils/visibility_control.hpp
  include/cpputils/common_exceptions.hpp
  include/cpputils/error_handler.hpp
  include/cpputils/fake_clock.hpp
  include/cpputils/optional.hpp
  include/cpputils/safe_cast.hpp
  include/cpputils/strerror.hpp
  include/cpputils/variant.hpp
  include/cpputils/string_view.hpp
  include/cpputils/time_limit_mutex.hpp
  include/cpputils/tuple.hpp
  include/cpputils/type_traits.hpp
  include/cpputils/callable_traits.hpp
  include/cpputils/outcome.hpp
  include/cpputils/optional_traits.hpp
  include/cpputils/floating.hpp
  include/cpputils/refer_or_create_ptr.hpp
  include/string/detail/base_string.hpp
  include/string/detail/string_impl.hpp
  include/string/string.hpp
  include/string/string_silent.hpp
  include/string/string_strict.hpp
  include/string/to_string.hpp
  include/storage/storage.hpp
  include/storage/storage_exceptions.hpp
  src/axivion.cpp
  src/fake_clock.cpp
  src/strerror.cpp
  src/string/to_string.cpp)

set_source_files_properties(${cpputils_sources} PROPERTIES language "CXX")
add_library(${PROJECT_NAME} SHARED ${cpputils_sources})
target_include_directories(${PROJECT_NAME} PUBLIC
  "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>"
  "$<INSTALL_INTERFACE:include>")
ament_export_include_directories(include)
ament_export_targets(${PROJECT_NAME}Targets HAS_LIBRARY_TARGET)
ament_export_libraries(${PROJECT_NAME})

if(VECTORCAST_BUILD)
  vcast_set_io_cover_cxx(${PROJECT_NAME})
endif()
apex_set_compile_options(${PROJECT_NAME})

ament_target_dependencies(${PROJECT_NAME}
    apexutils
    mpark_variant_vendor)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  find_package(apex_test_tools REQUIRED)
  find_package(mpark_variant_vendor REQUIRED)
  list(APPEND AMENT_LINT_AUTO_EXCLUDE
    ament_cmake_cppcheck
    ament_cmake_cpplint
    ament_cmake_uncrustify)
  ament_lint_auto_find_test_dependencies()
  ament_cppcheck(INCLUDE_DIRS "${CMAKE_SOURCE_DIR}/include")
  if(CMAKE_TI_LINUX OR CMAKE_POKY_LINUX OR CMAKE_NVIDIA_LINUX)
    add_compile_options(-Wno-stringop-truncation)
  endif()

  apex_test_tools_add_gtest(test_cpputils
    test/test_outcome.cpp
    test/test_error_handler.cpp
    test/test_safe_cast.cpp
    test/test_strerror.cpp
    test/test_variant_traits.cpp
    test/test_type_traits.cpp
    test/test_tuple_traits.cpp
    test/test_callable_traits.cpp
    test/test_optional_traits.cpp
    test/test_common_exceptions.cpp
    test/test_floating.cpp
    test/test_refer_or_create.cpp
    test/test_time_limit_mutex.cpp
    test/string/test_string.cpp
    test/string/test_string_strict.cpp
    test/string/test_to_string.cpp
    test/string/test_string_exceptions.cpp
    test/bounded_vector/test_bounded_vector.cpp
  )
  if(CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    target_compile_options(test_cpputils PRIVATE
      -Wno-deprecated-declarations
      # these are added to deal with intentionally overflowed bounds in test_string.cpp.
      # Due to a GCC bug https://gcc.gnu.org/bugzilla/show_bug.cgi?id=91890, these cannot
      # be ignored via pragma until GCC 10, so they need to be added here instead. Once
      # Apex.grace uses a later GCC by default, these should be added as a pragma only
      # where bounds are intentionally overflowed.
      -Wno-array-bounds
    )
  elseif(CMAKE_COMPILER_IS_GNUCXX)
    target_compile_options(test_cpputils PRIVATE
      -Wno-deprecated-declarations
      # these are added to deal with intentionally overflowed bounds in test_string.cpp.
      # Due to a GCC bug https://gcc.gnu.org/bugzilla/show_bug.cgi?id=91890, these cannot
      # be ignored via pragma until GCC 10, so they need to be added here instead. Once
      # Apex.grace uses a later GCC by default, these should be added as a pragma only
      # where bounds are intentionally overflowed.
      -Wno-array-bounds
      -Wno-stringop-overflow
      -Wno-stringop-truncation
    )
  endif()
  if(CMAKE_TI_LINUX)
    # FP from TI aarch64 compiler
    target_compile_options(test_cpputils PRIVATE -Wno-init-list-lifetime)
  endif()
  if(VECTORCAST_BUILD)
    vcast_set_io_cover_cxx(test_cpputils)
  endif()
  ament_target_dependencies(test_cpputils mpark_variant_vendor)

  target_link_libraries(test_cpputils ${PROJECT_NAME})

  apex_test_tools_add_gtest(test_string_allocation
      test/string/test_string_allocation.cpp)
  if(VECTORCAST_BUILD)
    vcast_set_io_cover_cxx(test_string_allocation)
  endif()
  target_link_libraries(test_string_allocation ${PROJECT_NAME})
  target_compile_options(test_string_allocation PRIVATE -fno-builtin)

  apex_test_tools_add_gtest(stack_string_check
      test/stack_string/test_stack_string.cpp
      test/stack_string/test_stack_string_strict.cpp
      test/stack_string/test_to_string_with_stack_string.cpp)
  if(VECTORCAST_BUILD)
    vcast_set_io_cover_cxx(stack_string_check)
  endif()
  target_link_libraries(stack_string_check ${PROJECT_NAME})
endif()

ament_auto_package()
# remove, when #19878 done
install(
  TARGETS ${PROJECT_NAME}
  EXPORT ${PROJECT_NAME}Targets
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)
