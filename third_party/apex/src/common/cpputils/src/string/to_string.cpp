// Copyright 2017-2018 Apex.AI, Inc.
// All rights reserved.
#include "string/to_string.hpp"

#include <algorithm>
#include <array>
#include <cassert>
#include <cmath>
#include <cstdint>
#include <limits>

#ifdef APEX_OSX
namespace std
{
// OS X does not have std::isnan
int isnan(float x)
{
  return x != x;
}
// OS X does not have std::isnan
int isnan(double x)
{
  return x != x;
}
// OS X does not have std::isinf
int isinf(float x)
{
  return !isnan(x) && isnan(x - x);
}
// OS X does not have std::isinf
int isinf(double x)
{
  return !isnan(x) && isnan(x - x);
}
}  // namespace std
#endif  // APEX_OSX

namespace
{
///////////////////////////////////////////////////////////////////////////////
/*
The algo in its unrolled form given a number X (e.g. 254):

  Mod divide the X(254) by 10 (== 4)
  Get a corresponding character from `characters` (= `4`)
  Add the character to the result string (= "4")
  Divide X(254) by 10 and make it a current X value (== 25)

  Mod divide the X(25) by 10 (== 5)
  Get a corresponding character from `characters` (= `5`)
  Add the character to the result string (= "45")
  Divide X(25) by 10 and make it a current X value (== 2)

  Mod divide the X(2) by 10 (== 2)
  Get a corresponding character from `characters` (= `2`)
  Add the character to the result string (= "452")
  Divide X(2) by 10 and make it a current X value (== 0)

  Algorithm stops at X == 0
  Result string is reversed by std::reverse "452" -> "254"
*/

apex::string32_t unsigned_to_string(uint64_t value)
{
  if (value == 0U) {
    return "0";
  }

  const std::array<char, 10> characters{{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'}};
  apex::string32_t result;

  while (value > 0U) {
    // cppcheck-suppress throwInNoexceptFunction
    result.push_back(characters[(value % 10U)]);
    value /= 10U;
  }

  // Pointer arithmetics done only with container size. No risk of going out of bounds.
  // Axivion prevents using pure iterators in this case as well.
  // lint -e{9016} NOLINT performing pointer arithmetic via addition [AUTOSAR Rule M5-0-15]
  std::reverse(result.data(), result.data() + result.size());

  return result;
}

apex::string32_t signed_to_string(int64_t value)
{
  apex::string32_t result;

  if (value < 0) {
    result += '-';
    // Intentional negation of the most negative, because conversion to unsigned will make it right
    // lint -e{2704} NOLINT potentially negating the most negative number
    value = -value;
  }

  /*
   AXIVION DISABLE STYLE MisraC++2023-7.0.5: Reason: Code Quality (Functional suitability),
   Justification: Necessary type conversion to use unsigned_to_string
   */
  result += unsigned_to_string(static_cast<uint64_t>(value));
  return result;
}

///////////////////////////////////////////////////////////////////////////////
// Strip all trailing zeroes from a decimal representation of the input value:
// Convert 123 to 123
// Convert 120 to 12
// Convert 10 to 1
// Convert 10000 to 1
int64_t trim_decimal_zeroes(const int64_t value)
{
  int64_t retval = value;
  while (retval != 0LL) {
    const int64_t new_value = retval / 10LL;
    if ((new_value * 10LL) == retval) {
      retval = new_value;
    } else {
      break;
    }
  }
  return retval;
}

// Number of non-zero positions after decimal point while converting 32-bit floats to a string
static constexpr uint32_t float_32_pod_after_dp = 7U;
// Number of non-zero positions after decimal point while converting 64-bit floats to a string
static constexpr uint32_t float_64_pod_after_dp = 15U;

///////////////////////////////////////////////////////////////////////////////
// Convert an IEEE-754 float value to a normalized scientific notation string
/*
The conversion algo:

Simplified idea:
1. Input value: 123
2. Normalized input value: 1.23 * 10^2
3. String = "1.", scaled mantissa = (1.23 - 1) * 10^2 == 23
3. String += "23" + "e+" + "2" == "1.23e+2"

Detailed algo:
1. Convert the input to a positive value (if needed, add '-' to the result string)
   -123 -> 123, str = "-"
2. Assume the value is zero if it's less than the denormalized minimum value
3. Obtain mantissa in [1..10) range by dividing the value by its order of magnitude
   123 -> 1.23
4. Add the major (the first) number of the mantissa to the result string, add decimal point '.' too
   str =+ "1"
5. Obtain the after-decimal-pont minor mantissa numbers by subtracting the major from the mantissa
   mantissa = 1.23 - 1 == 0.23
   This reminder value is always in the [0..1) range
6. Add leading zeroes to the string, if the reminder contains leading zeroes.
   Shift reminder one decimal position left (*= 10) for every added leading zero
   Do the above only for the number of required positions POS_AFTER_DP or not at all
   if the number of the leading zeroes exceed POS_AFTER_DP
   The normalized_reminder is an original reminder, multiplied by POS_AFTER_DP
   0.23 -> 2300000.0
7. Convert the normalized_reminder into integer reminder_i64 (we always have enough bits for this)
   2300000.0 -> 2300000
8. Strip possible trailing zeros from reminder_i64 by calling trim_decimal_zeroes function
   2300000 -> 23
9. Convert reminder_i64 to string using apex::to_sting(uint64) call and add to the result string
   23 -> "23"
   str += "."
   str += "23"
10. Add "e" exponent and the order of magnitude to the string
   str += "e"
   str += "+2"
*/

// Inputs:
// STR_TYPE is a return fixed string type
// POS_AFTER_DP is number of non-zero digit positions after decimal points
// VAL_TYPE is the actual IEE-754 type (32-bit or 64-bit)
// denom_value is possibly denormalized (e.g. corrupted) IEEE-754 value
template <class STR_TYPE, uint32_t POS_AFTER_DP, typename VAL_TYPE>
STR_TYPE ieee754_to_s(const VAL_TYPE denom_value)
{
  // PCLint fires [MISRA C++ Rule 5-2-1] warning
  // "right side of logical operator '&&' is not a postfix expression"
  // Therefore I have to split the whole logical expression into constant terms
  // PCLint also fires [MISRA C++ Rule 0-1-9] operator == always evaluates to true
  // This one is a false alarm for a compile-time static_assert
  // lint --e{948} NOLINT disable [MISRA C++ Rule 0-1-9] rule check for the following code block
  // { Remove the brace {} to avoid VectorCAST bug when generating MC/DC test cases
  constexpr bool val_is_64_bit = std::is_same<VAL_TYPE, double>::value;
  constexpr bool str_is_32_bytes = std::is_same<STR_TYPE, apex::string32_t>::value;
  constexpr bool after_dp_is_for_64_bits = POS_AFTER_DP == float_64_pod_after_dp;
  constexpr bool val_is_32_bit = std::is_same<VAL_TYPE, float>::value;
  constexpr bool str_is_16_bytes = std::is_same<STR_TYPE, apex::string16_t>::value;
  constexpr bool after_dp_is_for_32_bits = POS_AFTER_DP == float_32_pod_after_dp;
  constexpr bool is_ieee754 = std::numeric_limits<VAL_TYPE>::is_iec559;

  // Allow only supported combinations of the template parameters
  static_assert(is_ieee754 && ((val_is_64_bit && str_is_32_bytes && after_dp_is_for_64_bits) ||
                               (val_is_32_bit && str_is_16_bytes && after_dp_is_for_32_bits)),
                "<string32_t, 15, double> and <string16_t, 7, float> types only");
  // } Remove the brace {} to avoid VectorCAST bug when generating MC/DC test cases

  double value = static_cast<double>(denom_value);
  STR_TYPE retval;
  if (std::isinf(value) && (value < 0.0)) {
    retval = "-inf";
  } else if (std::isinf(value)) {
    retval = "inf";
  } else if (std::isnan(value)) {
    retval = "nan";
  } else {
    if (value < 0.0) {
      // 1. Convert the input to a positive value (if needed, add '-' to the result string)
      retval += "-";
      value = -value;
    }

    // Now the value >= 0

    if (value < static_cast<double>(std::numeric_limits<VAL_TYPE>::denorm_min())) {
      // 2. Assume the value is zero if it's less than the denormalized minimum value
      // That's the float way to do (x == 0) comparison when x is possibly broken
      retval += "0";
    } else {
      // 3. Obtain mantissa in [1..10) range by dividing the value by its order of magnitude
      // Now the value > 0, it's safe to take log10
      int32_t order_of_magnitude = static_cast<int32_t>(std::log10(value));
      /*
       AXIVION Next Construct CertC++-FLP32: False positive, the input argument is always inside
       the domain that pow function is defined since the base is positive constant value (Domain
       of pow(x,y): x > 0 || (x == 0 && y > 0) || ( x < 0 && y is an integer)
       */
      /*
       AXIVION DISABLE STYLE MisraC++2023-7.0.5: Reason: Code Quality (Functional suitability),
       Justification: Necessary type conversion from integral type to double
       */
#if defined(_LIBCPP_VERSION)
      double divider = std::pow(10.0, static_cast<double>(order_of_magnitude));
#else
      double divider = std::pow<>(10.0, static_cast<double>(order_of_magnitude));
#endif
      double mantissa = value / divider;
      // Mantissa should be in [1..10) range
      // Due to IEEE-754 rounding, divider might be 999... instead of 1000...
      // Correct this situation by decrementing the order
      if (mantissa < 1.0) {
        order_of_magnitude--;
        /*
         AXIVION Next Construct CertC++-FLP32: False positive, the input argument is always
         inside the domain that pow function is defined since the base is positive constant
         value (Domain of pow(x,y): x > 0 || (x == 0 && y > 0) || ( x < 0 && y is an integer)
         */
#if defined(_LIBCPP_VERSION)
        divider = std::pow(10.0, static_cast<double>(order_of_magnitude));
#else
        divider = std::pow<>(10.0, static_cast<double>(order_of_magnitude));
#endif
        // divider can't be zero
        mantissa = value / divider;
      }

      // 4. Add the major(the first) number of the mantissa to the result string, add '.' too
      const int32_t major_number = static_cast<int32_t>(mantissa);
      retval += apex::to_string(major_number);
      retval += ".";

      // 5. Obtain the after-decimal-pont minor mantissa numbers by subtracting the major
      double normalized_reminder = mantissa - static_cast<double>(major_number);
      // normalized_reminder is in [0..1) range

      // 6. Add leading zeroes to the string, if the reminder contains leading zeroes.
      // The normalized_reminder is an original reminder, multiplied by POS_AFTER_DP
      if (normalized_reminder > 0.0) {
        const double number_of_positions = std::abs(std::log10(normalized_reminder));
        if (number_of_positions < static_cast<double>(POS_AFTER_DP)) {
          // AXIVION ENABLE STYLE MisraC++2023-7.0.5
          for (uint32_t i = 0U; i < POS_AFTER_DP; ++i) {
            normalized_reminder *= 10.0;
            const int64_t whole_number = static_cast<int64_t>(normalized_reminder);
            if (whole_number == 0) {
              retval += "0";
            }
          }
        }
      }

      // 7. Convert the normalized_reminder into 64-bit (we always have enough bits for this)
      const double normalized_reminder_incremented = normalized_reminder + 0.5;
      // 8. Strip possible trailing zeros from reminder
      int64_t reminder_i64 =
        trim_decimal_zeroes(static_cast<int64_t>(normalized_reminder_incremented));
      const int64_t integer_mantissa2 =
        trim_decimal_zeroes(static_cast<int64_t>(normalized_reminder));
      if (reminder_i64 < integer_mantissa2) {
        // If normalized_reminder is 999... instead of 1000..., we need to correct it back by one
        reminder_i64 = integer_mantissa2;
      }

      // 9. Convert reminder_i64 to string using apex::to_sting call and add to the result string
      retval += apex::to_string(reminder_i64);

      // 10. Add "e" exponent and the order of magnitude to the string
      retval += "e";
      if (order_of_magnitude >= 0) {
        retval += "+";
      }
      retval += apex::to_string(order_of_magnitude);
    }
  }
  return retval;
}

}  // namespace

///////////////////////////////////////////////////////////////////////////////
apex::string16_t apex::to_string(const uint8_t value) noexcept
{
  return unsigned_to_string(static_cast<uint64_t>(value));
}

///////////////////////////////////////////////////////////////////////////////
apex::string16_t apex::to_string(const uint16_t value) noexcept
{
  return unsigned_to_string(static_cast<uint64_t>(value));
}

///////////////////////////////////////////////////////////////////////////////
apex::string16_t apex::to_string(const uint32_t value) noexcept
{
  return unsigned_to_string(static_cast<uint64_t>(value));
}

///////////////////////////////////////////////////////////////////////////////
apex::string32_t apex::to_string(const uint64_t value) noexcept
{
  // cppcheck-suppress throwInNoexceptFunction
  return unsigned_to_string(value);
}

///////////////////////////////////////////////////////////////////////////////
apex::string16_t apex::to_string(const int8_t value) noexcept
{
  return signed_to_string(static_cast<int64_t>(value));
}

///////////////////////////////////////////////////////////////////////////////
apex::string16_t apex::to_string(const int16_t value) noexcept
{
  return signed_to_string(static_cast<int64_t>(value));
}

///////////////////////////////////////////////////////////////////////////////
apex::string16_t apex::to_string(const int32_t value) noexcept
{
  return signed_to_string(static_cast<int64_t>(value));
}

///////////////////////////////////////////////////////////////////////////////
apex::string32_t apex::to_string(const int64_t value) noexcept
{
  return signed_to_string(value);
}

///////////////////////////////////////////////////////////////////////////////
apex::string32_t apex::to_hex_string(const std::uintptr_t value) noexcept
{
  /**
   *    2 (0x)
   * + [num chars for hex representation of max pointer int]
   * +  1 (\0)
   */
  apex::string32_t str;
  static_assert(str.get_buffer_size_static() >= (2 + (2 * sizeof(std::uintptr_t)) + 1),
                "size of return type not big enough for hex string of given int size");
  /*
   AXIVION Next CodeLine MisraC++2023-30.0.1 : Reason: Code Quality (Functional suitability),
   Justification: snprintf required to convert value to hex string
   */
  (void)snprintf(str.data(), str.get_buffer_size(), "0x%016lx", value);
  return str;
}

///////////////////////////////////////////////////////////////////////////////
apex::string16_t apex::to_string(const float value) noexcept
{
  apex::string16_t retval;
  // I specifically want a non-typed check of length:
  // lint -e{1705} non-MISRA: static member could be accessed using a nested name specifier NOLINT
  static_assert(
    // It's from limits.h: "#define FLT_MAX 3.402823466e+38F max value"
    // I renumbered its mantissa to `123..` to easily calculate the number of required positions
    retval.get_buffer_size_static() >= sizeof("-9.123456e+037"),
    "Return type is too small");
  retval = ieee754_to_s<apex::string16_t, float_32_pod_after_dp>(value);
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex::string32_t apex::to_string(const double value) noexcept
{
  apex::string32_t retval;
  // I specifically want a non-typed check of length:
  // lint -e{1705} non-MISRA: static member could be accessed using a nested name specifier NOLINT
  static_assert(
    // It's from limits.h: "#define DBL_MAX 1.7976931348623158e+308 max value"
    // I renumbered its mantissa to `123..` to easily calculate the number of required positions
    retval.get_buffer_size_static() >= sizeof("-1.123456789012345e+308"),
    "Return type is too small");
  retval = ieee754_to_s<apex::string32_t, float_64_pod_after_dp>(value);
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex::string8_t apex::to_string(const bool value) noexcept
{
  const apex::string8_t retval = value ? "true" : "false";
  return retval;
}

///////////////////////////////////////////////////////////////////////////////
apex::string256_t apex::to_string(const char * const c_text_ptr) noexcept
{
  apex::string256_t retval;
  if (nullptr != c_text_ptr) {
    retval = c_text_ptr;
  } else {
    retval = "<nullptr>";
  }
  return retval;
}

apex::string256_t apex::to_string(apex::string_view sv) noexcept
{
  return apex::string256_t{sv};
}
