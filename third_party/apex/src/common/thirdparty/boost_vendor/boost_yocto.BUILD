load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//common/thirdparty/boost_vendor:defs.bzl", "BOOST_VERSION")
load("@apex//common/thirdparty/boost_vendor:defs.bzl", "BOOST_CMAKE_FIND_PACKAGE")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load(":defs.bzl", "PYTHON_VERSION")

apex_cc_library(
    name = "headers",
    hdrs = glob(
        [
            "boost/**",
        ],
    ),
    defines = ["BOOST_ALL_NO_LIB"],
    include_prefix = ".",
    visibility = ["//visibility:public"],
)

apex_cc_library(
    name = "asio",
    linkopts = select({
        "@platforms//os:linux": ["-lpthread"],
        "//conditions:default": [],
    }),
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
    ],
)

apex_cc_library(
    name = "_atomic_detail",
    tags = ["same-ros-pkg-as: boost_pkg"],
    visibility = ["@boost//:__subpackages__"],
)

apex_cc_library(
    name = "atomic",
    visibility = ["//visibility:public"],
    deps = [
        ":_atomic_detail",
        ":atomic_import",
        ":headers",
        ":thread",
    ],
)

cc_import(
    name = "atomic_import",
    interface_library = "@yocto_sysroot//:usr/lib/libboost_atomic.so",
    shared_library = "@yocto_sysroot//:usr/lib/libboost_atomic.so.{version}".format(version = BOOST_VERSION),
    tags = ["same-ros-pkg-as: boost_pkg"],
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

apex_cc_library(
    name = "chrono",
    visibility = ["//visibility:public"],
    deps = [
        ":chrono_import",
        ":headers",
    ],
)

cc_import(
    name = "chrono_import",
    interface_library = "@yocto_sysroot//:usr/lib/libboost_chrono.so",
    shared_library = "@yocto_sysroot//:usr/lib/libboost_chrono.so.{version}".format(version = BOOST_VERSION),
    tags = ["same-ros-pkg-as: boost_pkg"],
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

apex_cc_library(
    name = "program_options",
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
        ":program_options_import",
    ],
)

cc_import(
    name = "program_options_import",
    interface_library = "@yocto_sysroot//:usr/lib/libboost_program_options.so",
    shared_library = "@yocto_sysroot//:usr/lib/libboost_program_options.so.{version}".format(version = BOOST_VERSION),
    tags = ["same-ros-pkg-as: boost_pkg"],
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

apex_cc_library(
    name = "system",
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
        ":system_import",
    ],
)

cc_import(
    name = "system_import",
    interface_library = "@yocto_sysroot//:usr/lib/libboost_system.so",
    shared_library = "@yocto_sysroot//:usr/lib/libboost_system.so.{version}".format(version = BOOST_VERSION),
    tags = ["same-ros-pkg-as: boost_pkg"],
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

apex_cc_library(
    name = "_thread_detail",
    tags = ["same-ros-pkg-as: boost_pkg"],
    visibility = ["@boost//:__subpackages__"],
)

apex_cc_library(
    name = "thread",
    linkopts = select({
        "@platforms//os:linux": ["-lpthread"],
        "//conditions:default": [],
    }),
    visibility = ["//visibility:public"],
    deps = [
        "thread_import",
        ":_thread_detail",
        ":headers",
    ],
)

cc_import(
    name = "thread_import",
    interface_library = "@yocto_sysroot//:usr/lib/libboost_thread.so",
    shared_library = "@yocto_sysroot//:usr/lib/libboost_thread.so.{version}".format(version = BOOST_VERSION),
    tags = ["same-ros-pkg-as: boost_pkg"],
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

apex_cc_library(
    name = "filesystem",
    defines = ["BOOST_FILESYSTEM_NO_CXX20_ATOMIC_REF"],
    visibility = ["//visibility:public"],
    deps = [
        "filesystem_import",
        ":headers",
    ],
)

cc_import(
    name = "filesystem_import",
    interface_library = "@yocto_sysroot//:usr/lib/libboost_filesystem.so",
    shared_library = "@yocto_sysroot//:usr/lib/libboost_filesystem.so.{version}".format(version = BOOST_VERSION),
    tags = ["same-ros-pkg-as: boost_pkg"],
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

apex_cc_library(
    name = "date_time",
    visibility = ["//visibility:public"],
    deps = [
        "date_time_import",
        ":headers",
    ],
)

cc_import(
    name = "date_time_import",
    interface_library = "@yocto_sysroot//:usr/lib/libboost_date_time.so",
    shared_library = "@yocto_sysroot//:usr/lib/libboost_date_time.so.{version}".format(version = BOOST_VERSION),
    tags = ["same-ros-pkg-as: boost_pkg"],
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

# TODO(lander.usategui): Add numpy support 34983
# extract_site_packages_files(
#     name = "numpy_hdrs_files",
#     output_dir = "numpy",
#     python_vendor_pkg = "@python_vendor_numpy//:pkg",
#     site_packages_path = "numpy/core/include/numpy",
# )

# extract_site_packages_files(
#     name = "numpy_lib_files",
#     output_dir = "numpy",
#     python_vendor_pkg = "@python_vendor_numpy//:pkg",
#     site_packages_path = "numpy/core/lib/libnpymath.a",
# )

# apex_cc_library(
#     name = "numpy_lib",
#     srcs = [":numpy_lib_files"],
#     hdrs = [":numpy_hdrs_files"],
#     includes = ["."],
# )

apex_cc_library(
    name = "python",
    visibility = ["//visibility:public"],
    deps = [
        "python_import",
        ":headers",
    ],
)

cc_import(
    name = "python_import",
    interface_library = "@yocto_sysroot//:usr/lib/libboost_python{}.so".format(PYTHON_VERSION),
    shared_library = "@yocto_sysroot//:usr/lib/libboost_python{py_version}.so.{boost_version}".format(
        boost_version = BOOST_VERSION,
        py_version = PYTHON_VERSION,
    ),
    tags = ["same-ros-pkg-as: boost_pkg"],
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

# TODO(lander.usategui): Add numpy support 34983
# apex_cc_library(
#     name = "numpy",
#     visibility = ["//visibility:public"],
#     deps = [
#         ":headers",
#         ":numpy_lib",
#         ":python",
#     ],
# )

# cc_import(
#     name = "numpy_import",
#     interface_library = "@yocto_sysroot//:usr/lib/libboost_numpy.so",
#     shared_library = "@yocto_sysroot//:usr/lib/libboost_numpy.so.{version}".format(version = BOOST_VERSION),
#     tags = ["same-ros-pkg-as: boost_pkg"],
#     target_compatible_with = select({
#         "@apex//common/platforms/os_distro:poky_dunfell": [],
#         "@apex//common/platforms/os_distro:poky_kirkstone": [],
#         "//conditions:default": ["@platforms//:incompatible"],
#     }),
# )

apex_cc_library(
    name = "serialization",
    visibility = ["//visibility:public"],
    deps = [
        "serialization_import",
        ":headers",
        ":system",
    ],
)

cc_import(
    name = "serialization_import",
    interface_library = "@yocto_sysroot//:usr/lib/libboost_serialization.so",
    shared_library = "@yocto_sysroot//:usr/lib/libboost_serialization.so.{version}".format(version = BOOST_VERSION),
    tags = ["same-ros-pkg-as: boost_pkg"],
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

apex_cc_library(
    name = "serialization-mt",
    visibility = ["//visibility:public"],
    deps = [
        "serialization-mt_import",
        ":headers",
        ":system",
    ],
)

cc_import(
    name = "serialization-mt_import",
    interface_library = "@yocto_sysroot//:usr/lib/libboost_serialization-mt.so",
    shared_library = "@yocto_sysroot//:usr/lib/libboost_serialization-mt.so",
    tags = ["same-ros-pkg-as: boost_pkg"],
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

apex_cc_library(
    name = "wserialization",
    visibility = ["//visibility:public"],
    deps = [
        "wserialization_import",
        ":headers",
        ":system",
    ],
)

cc_import(
    name = "wserialization_import",
    interface_library = "@yocto_sysroot//:usr/lib/libboost_wserialization.so",
    shared_library = "@yocto_sysroot//:usr/lib/libboost_wserialization.so.{version}".format(version = BOOST_VERSION),
    tags = ["same-ros-pkg-as: boost_pkg"],
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

ros_pkg(
    name = "boost_pkg",
    bst_substitute_with = select({
        "@apex//common/build_system_transfer:use_cmake_find_package": {
            "name": "boost",
            "cmake_find_package": BOOST_CMAKE_FIND_PACKAGE,
        },
        "//conditions:default": {
        },
    }),
    cc_libraries = [
        ":asio",
        ":headers",
        ":atomic",
        ":_atomic_detail",
        ":chrono",
        ":program_options",
        ":system",
        ":thread",
        ":_thread_detail",
        ":filesystem",
        ":date_time",
        ":serialization",
        ":serialization-mt",
        ":wserialization",
    ] + select({
        "@platforms//os:qnx": [],  # until QNX supports python
        "//conditions:default": [
            ":python",
            # TODO(lander.usategui): Add numpy support 34983
            # ":numpy",
        ],
    }),
    description = "Boost provides free portable peer-reviewed C++ libraries.",
    license = "Boost Software License (BSL1.0)",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "boost_vendor",
    version = BOOST_VERSION,
    visibility = ["//visibility:public"],
)
