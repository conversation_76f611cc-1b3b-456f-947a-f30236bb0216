load("@apex//common/thirdparty/boost_vendor:defs.bzl", "BOOST_VERSION")
load("@apex//tools/bazel/rules_repo:defs.bzl", "configurable_repository", "empty_repository")
load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")
load("@bazel_tools//tools/build_defs/repo:utils.bzl", "maybe")

BOOST_MIRRORS = {
    "default": "github.com",
}

def load_boost_repositories():
    maybe(
        name = "boost-src",
        repo_rule = http_archive,
        urls = [
            "https://{host_name}/boostorg/boost/releases/download/boost-{version}/boost-{version}.tar.gz".format(
                host_name = host_name,
                version = BOOST_VERSION,
                version_underscore = BOOST_VERSION.replace(".", "_"),
            )
            for location_name, host_name in BOOST_MIRRORS.items()
        ],
        # sha256 = "85138e4a185a7e7535e82b011179c5b5fb72185bea9f59fe8e2d76939b2f5c51", # 1.88
        sha256 = "4d27e9efed0f6f152dc28db6430b9d3dfb40c0345da7342eaa5a987dde57bd95",
        strip_prefix = "boost-{version}".format(version = BOOST_VERSION),
        build_file = "@apex//common/thirdparty/boost_vendor:boost_src.BUILD",
        patches = ["@apex//common/thirdparty/boost_vendor:boost_disable_signal_set_qnx.patch"],
        patch_args = ["-p1"],
    )

    maybe(
        name = "boost-yocto-kirkstone-aarch64",
        repo_rule = configurable_repository,
        build_file = "@apex//common/thirdparty/boost_vendor:boost_yocto.BUILD",
        configurable_files_content = {"defs.bzl": "PYTHON_VERSION = '310'"},
        repo_mapping = {"@yocto_sysroot": "@cc_toolchain_linux-ubuntu_jammy-gcc11_yocto-x86_64_linux-poky_kirkstone-aarch64_sysroot"},
    )
    maybe(
        name = "boost-yocto-dunfell-aarch64",
        repo_rule = configurable_repository,
        build_file = "@apex//common/thirdparty/boost_vendor:boost_yocto.BUILD",
        configurable_files_content = {"defs.bzl": "PYTHON_VERSION = '38'"},
        repo_mapping = {"@yocto_sysroot": "@cc_toolchain_linux-ubuntu_focal-gcc9_yocto-x86_64_linux-poky_dunfell-aarch64_sysroot"},
    )
    maybe(
        name = "boost",
        repo_rule = empty_repository,
        build_file = "@apex//common/thirdparty/boost_vendor:boost.BUILD",
    )
