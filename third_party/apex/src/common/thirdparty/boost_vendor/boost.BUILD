[
    alias(
        name = lib,
        actual = select({
            "@apex//common/platforms/apex_detail:yocto-dunfell-aarch64": "@boost-yocto-dunfell-aarch64//:{lib}".format(lib = lib),
            "@apex//common/platforms/apex_detail:yocto-kirkstone-aarch64": "@boost-yocto-kirkstone-aarch64//:{lib}".format(lib = lib),
            "//conditions:default": "@boost-src//:{lib}".format(lib = lib),
        }),
        visibility = ["//visibility:public"],
    )
    for lib in [
        "asio",
        "headers",
        "atomic",
        "_atomic_detail",
        "chrono",
        "program_options",
        "system",
        "thread",
        "_thread_detail",
        "filesystem",
        "date_time",
        "python",
        "numpy",
        "serialization",
        "serialization-mt",
        "wserialization",
        "boost_pkg",
    ]
]
