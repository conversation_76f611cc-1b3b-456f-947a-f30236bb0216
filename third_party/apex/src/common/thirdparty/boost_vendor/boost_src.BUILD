load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//common/bazel/rules_python_extra:defs.bzl", "extract_site_packages_files")
load("@apex//common/thirdparty/boost_vendor:boost_includes.bzl", "BOOST_ALL_INCLUDES")
load("@apex//common/thirdparty/boost_vendor:defs.bzl", "BOOST_VERSION")
load("@apex//common/thirdparty/boost_vendor:defs.bzl", "BOOST_CMAKE_FIND_PACKAGE")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

apex_cc_library(
    name = "headers",
    hdrs = glob([include + "/boost/**" for include in BOOST_ALL_INCLUDES]),
    defines = [
        "BOOST_ALL_NO_LIB",
    ] + select({
        "@platforms//os:qnx": [
            "BOOST_ASIO_HAS_STRING_VIEW",
            "BOOST_ASIO_HAS_STD_STRING_VIEW",
        ],
        "//conditions:default": [],
    }),
    include_prefix = ".",
    includes = BOOST_ALL_INCLUDES,
    visibility = ["//visibility:public"],
)

apex_cc_library(
    name = "asio",
    srcs = glob(
        [
            "libs/asio/src/**",
        ],
    ),
    linkopts = select({
        "@platforms//os:linux": ["-lpthread"],
        "//conditions:default": [],
    }),
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
    ],
)

apex_cc_library(
    name = "_atomic_detail",
    hdrs = ["libs/atomic/src/lock_pool_init1.ipp"],
    strip_include_prefix = "libs/atomic/src",
    tags = ["same-ros-pkg-as: atomic"],
    visibility = ["@boost//:__subpackages__"],
)

apex_cc_library(
    name = "atomic",
    srcs = [
        "libs/atomic/src/lock_pool.cpp",
        "libs/atomic/src/find_address.hpp",
        # If SSE detection is ever added, here would be a pretty good place to use it
        # to conditionally add optimized implementations
    ],
    copts = [
        "-Ilibs/atomic/src",
    ],
    implementation_deps = [
        ":_atomic_detail",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
        ":thread",
    ],
)

apex_cc_library(
    name = "chrono",
    srcs = glob(["libs/chrono/src/**"]),
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

apex_cc_library(
    name = "program_options",
    srcs = glob(["libs/program_options/src/**"]),
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

apex_cc_library(
    name = "system",
    srcs = glob(["libs/system/src/**"]),
    copts = select({
        "@apex//common/build_system_transfer:enabled": ["-Wno-error=overloaded-virtual"],
        "//conditions:default": [],
    }),
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

apex_cc_library(
    name = "_thread_detail",
    hdrs = select({
        "@platforms//os:linux": ["libs/thread/src/pthread/once_atomic.cpp"],
        "//conditions:default": [],
    }),
    strip_include_prefix = "libs/thread/src/pthread",
    tags = ["same-ros-pkg-as: thread"],
    visibility = ["@boost//:__subpackages__"],
)

apex_cc_library(
    name = "thread",
    srcs = glob(
        [
            "libs/thread/src/*.cpp",
        ],
    ) + select({
        "@platforms//os:linux": [
            "libs/thread/src/pthread/once.cpp",
            "libs/thread/src/pthread/thread.cpp",
        ],
        "//conditions:default": [],
    }),
    copts = select({
        "@apex//common/build_system_transfer:enabled": ["-Wno-error=overloaded-virtual"],
        "//conditions:default": [],
    }),
    implementation_deps = [
        ":_thread_detail",
    ],
    linkopts = select({
        "@platforms//os:linux": ["-lpthread"],
        "//conditions:default": [],
    }),
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

apex_cc_library(
    name = "filesystem",
    srcs = glob(["libs/filesystem/src/**"]),
    copts = select({
        "@apex//common/build_system_transfer:enabled": ["-Wno-error=overloaded-virtual"],
        "//conditions:default": [],
    }),
    defines = ["BOOST_FILESYSTEM_NO_CXX20_ATOMIC_REF"],
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

apex_cc_library(
    name = "date_time",
    srcs = ["libs/date_time/src/gregorian/greg_month.cpp"],
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

extract_site_packages_files(
    name = "numpy_hdrs_files",
    output_dir = "numpy_hdrs/numpy",
    python_vendor_pkg = "@python_vendor_numpy//:pkg",
    site_packages_path = "numpy/core/include/numpy",
)

extract_site_packages_files(
    name = "numpy_lib_files",
    output_dir = "numpy_lib",
    python_vendor_pkg = "@python_vendor_numpy//:pkg",
    site_packages_path = "numpy/core/lib/libnpymath.a",
)

apex_cc_library(
    name = "numpy_lib",
    srcs = [":numpy_lib_files"],
    hdrs = [":numpy_hdrs_files"],
    includes = ["numpy_hdrs"],
)

apex_cc_library(
    name = "python",
    srcs = glob(
        ["libs/python/src/**/*.cpp"],
        exclude = [
            "libs/python/src/numpy/**",
        ],
    ),
    copts = select({
        "@apex//common/build_system_transfer:enabled": [
            "-Wno-error=maybe-uninitialized",
            "-Wno-error=missing-field-initializers",
        ],
        "@apex//common/platforms/cc_compiler_version:clang16": [
            "-Wno-error=tautological-constant-out-of-range-compare",
        ],
        "//conditions:default": [],
    }),
    defines = [
        "BOOST_PYTHON_NO_LIB",
        "BOOST_PYTHON_DYN_LINK",
    ],
    local_defines = ["BOOST_PYTHON_SOURCE"],
    target_compatible_with = ["@platforms//os:linux"],
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
        "@python_interpreter//:python_headers",
    ],
)

apex_cc_library(
    name = "numpy",
    srcs = glob(["libs/python/src/numpy/*.cpp"]),
    copts = select({
        "@apex//common/build_system_transfer:enabled": [
            "-Wno-error=maybe-uninitialized",
            "-Wno-error=missing-field-initializers",
        ],
        "@apex//common/platforms/cc_compiler_version:clang16": [
            "-Wno-error=tautological-constant-out-of-range-compare",
        ],
        "//conditions:default": [],
    }),
    defines = [
        "BOOST_NUMPY_NO_LIB",
        "BOOST_NUMPY_DYN_LINK",
    ],
    local_defines = ["BOOST_NUMPY_SOURCE"],
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
        ":numpy_lib",
        ":python",
        "@python_interpreter//:python_headers",
    ],
)

apex_cc_library(
    name = "serialization",
    srcs = glob(["libs/serialization/src/*.cpp"]),
    hdrs = glob(["libs/serialization/src/*.ipp"]),
    copts = select({
        "@apex//common/build_system_transfer:enabled": [
            "-Wno-error=overloaded-virtual",
            "-Wno-error=deprecated-copy",
        ],
        "//conditions:default": [],
    }),
    # To ensure multi-threaded can be used with serialization(libboost_serialization-mt)
    linkopts = select({
        "@platforms//os:linux": ["-lpthread"],
        "//conditions:default": [],
    }),
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
        ":system",
    ],
)

alias(
    name = "serialization-mt",
    actual = ":serialization",
    visibility = ["//visibility:public"],
)

alias(
    name = "wserialization",
    actual = ":serialization",
    visibility = ["//visibility:public"],
)

ros_pkg(
    name = "boost_pkg",
    bst_substitute_with = select({
        "@apex//common/build_system_transfer:use_cmake_find_package": {
            "name": "boost",
            "cmake_find_package": BOOST_CMAKE_FIND_PACKAGE,
        },
        "//conditions:default": {
        },
    }),
    cc_libraries = [
        ":asio",
        ":headers",
        ":atomic",
        ":_atomic_detail",
        ":chrono",
        ":program_options",
        ":system",
        ":thread",
        ":_thread_detail",
        ":filesystem",
        ":date_time",
        ":serialization",
        ":serialization-mt",
        ":wserialization",
    ] + select({
        "@platforms//os:qnx": [],  # until QNX supports python
        "//conditions:default": [
            ":python",
            ":numpy",
        ],
    }),
    description = "Boost provides free portable peer-reviewed C++ libraries.",
    license = "Boost Software License (BSL1.0)",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "boost_vendor",
    version = BOOST_VERSION,
    visibility = ["//visibility:public"],
)
