# Generated by: boost_vendor/internal/generate_boost_include_dirs.py

BOOST_ALL_INCLUDES = [
    "libs/accumulators/include",
    "libs/algorithm/include",
    "libs/align/include",
    "libs/any/include",
    "libs/array/include",
    "libs/asio/include",
    "libs/assert/include",
    "libs/assign/include",
    "libs/atomic/include",
    "libs/beast/include",
    "libs/bimap/include",
    "libs/bind/include",
    "libs/callable_traits/include",
    "libs/chrono/include",
    "libs/circular_buffer/include",
    "libs/cobalt/include",
    "libs/compat/include",
    "libs/compatibility/include",
    "libs/compute/include",
    "libs/concept_check/include",
    "libs/config/include",
    "libs/container/include",
    "libs/container_hash/include",
    "libs/context/include",
    "libs/contract/include",
    "libs/conversion/include",
    "libs/convert/include",
    "libs/core/include",
    "libs/coroutine/include",
    "libs/coroutine2/include",
    "libs/crc/include",
    "libs/date_time/include",
    "libs/describe/include",
    "libs/detail/include",
    "libs/dll/include",
    "libs/dynamic_bitset/include",
    "libs/endian/include",
    "libs/exception/include",
    "libs/fiber/include",
    "libs/filesystem/include",
    "libs/flyweight/include",
    "libs/foreach/include",
    "libs/format/include",
    "libs/function/include",
    "libs/function_types/include",
    "libs/functional/include",
    "libs/fusion/include",
    "libs/fusion/include/boost/fusion/include",
    "libs/geometry/include",
    "libs/gil/include",
    "libs/graph/include",
    "libs/graph_parallel/include",
    "libs/hana/include",
    "libs/headers/include",
    "libs/heap/include",
    "libs/histogram/include",
    "libs/hof/include",
    "libs/icl/include",
    "libs/integer/include",
    "libs/interprocess/include",
    "libs/intrusive/include",
    "libs/io/include",
    "libs/iostreams/include",
    "libs/iterator/include",
    "libs/json/include",
    "libs/lambda/include",
    "libs/lambda2/include",
    "libs/leaf/include",
    "libs/lexical_cast/include",
    "libs/local_function/include",
    "libs/locale/include",
    "libs/lockfree/include",
    "libs/log/include",
    "libs/logic/include",
    "libs/math/include",
    "libs/metaparse/include",
    "libs/move/include",
    "libs/mp11/include",
    "libs/mpi/include",
    "libs/mpl/include",
    "libs/msm/include",
    "libs/multi_array/include",
    "libs/multi_index/include",
    "libs/multiprecision/include",
    "libs/mysql/include",
    "libs/nowide/include",
    "libs/numeric/conversion/include",
    "libs/numeric/interval/include",
    "libs/numeric/odeint/include",
    "libs/numeric/ublas/include",
    "libs/optional/include",
    "libs/outcome/include",
    "libs/parameter/include",
    "libs/parameter_python/include",
    "libs/pfr/include",
    "libs/phoenix/include",
    "libs/poly_collection/include",
    "libs/polygon/include",
    "libs/pool/include",
    "libs/predef/include",
    "libs/preprocessor/include",
    "libs/process/include",
    "libs/program_options/include",
    "libs/property_map/include",
    "libs/property_map_parallel/include",
    "libs/property_tree/include",
    "libs/proto/include",
    "libs/ptr_container/include",
    "libs/python/include",
    "libs/qvm/include",
    "libs/random/include",
    "libs/range/include",
    "libs/ratio/include",
    "libs/rational/include",
    "libs/redis/include",
    "libs/regex/include",
    "libs/safe_numerics/include",
    "libs/scope_exit/include",
    "libs/serialization/include",
    "libs/signals2/include",
    "libs/smart_ptr/include",
    "libs/sort/include",
    "libs/spirit/include",
    "libs/spirit/include/boost/spirit/include",
    "libs/spirit/include/boost/spirit/repository/include",
    "libs/stacktrace/include",
    "libs/statechart/include",
    "libs/static_assert/include",
    "libs/static_string/include",
    "libs/stl_interfaces/include",
    "libs/system/include",
    "libs/thread/include",
    "libs/throw_exception/include",
    "libs/timer/include",
    "libs/tokenizer/include",
    "libs/tti/include",
    "libs/tuple/include",
    "libs/type_erasure/include",
    "libs/type_index/include",
    "libs/type_traits/include",
    "libs/typeof/include",
    "libs/units/include",
    "libs/unordered/include",
    "libs/url/include",
    "libs/utility/include",
    "libs/uuid/include",
    "libs/variant/include",
    "libs/variant2/include",
    "libs/vmd/include",
    "libs/wave/include",
    "libs/winapi/include",
    "libs/xpressive/include",
    "libs/yap/include",
]
