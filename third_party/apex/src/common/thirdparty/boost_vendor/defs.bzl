BOOST_VERSION = "1.84.0"

BOOST_CMAKE_FIND_PACKAGE = """\
find_package(Boost REQUIRED COMPONENTS date_time filesystem program_options system thread)
if (NOT TARGET {pkg_name}::headers)
    add_library({pkg_name}::headers ALIAS Boost::boost)
endif()
if (NOT TARGET {pkg_name}::asio)
    add_library({pkg_name}::asio ALIAS Boost::boost)
endif()
if (NOT TARGET {pkg_name}::filesystem)
    add_library({pkg_name}::filesystem ALIAS Boost::filesystem)
endif()
if (NOT TARGET {pkg_name}::program_options)
    add_library({pkg_name}::program_options ALIAS Boost::program_options)
endif()
if (NOT TARGET {pkg_name}::thread)
    add_library({pkg_name}::thread ALIAS Boost::thread)
endif()
if (NOT TARGET {pkg_name}::date_time)
    add_library({pkg_name}::date_time ALIAS Boost::date_time)
endif()
if (NOT TARGET {pkg_name}::python)
    if (TARGET Boost::python)
        add_library({pkg_name}::python ALIAS Boost::python)
    elseif (TARGET Boost::python3)
        add_library({pkg_name}::python ALIAS Boost::python3)
    endif()
endif()
if (NOT TARGET {pkg_name}::system)
    add_library({pkg_name}::system ALIAS Boost::system)
endif()
"""
