#!/bin/bash
# Copyright 2025 Apex.AI, Inc.
# All rights reserved.

set -e

# --- begin runfiles.bash initialization v3 ---
# Copy-pasted from the Bazel Bash runfiles library v3.
set -uo pipefail; set +e; f=bazel_tools/tools/bash/runfiles/runfiles.bash
source "${RUNFILES_DIR:-/dev/null}/$f" 2>/dev/null || \
  source "$(grep -sm1 "^$f " "${RUNFILES_MANIFEST_FILE:-/dev/null}" | cut -f2- -d' ')" 2>/dev/null || \
  source "$0.runfiles/$f" 2>/dev/null || \
  source "$(grep -sm1 "^$f " "$0.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null || \
  source "$(grep -sm1 "^$f " "$0.exe.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null || \
  { echo>&2 "ERROR: cannot find $f"; exit 1; }; f=; set -e
# --- end runfiles.bash initialization v3 ---
echo "Starting..."

TARGET_CPU=$1
TAR_GZ_ARCHIVE="$(rlocation apex/common/bazel/rules_pkg_extra/test/example_bin.tar.gz)"

EXPECTED_FILES=$(cat <<EOF
example_bin/
example_bin/example_bin
example_bin/example_bin.runfiles/
example_bin/example_bin.runfiles/_repo_mapping
example_bin/example_bin.runfiles/apex/
example_bin/example_bin.runfiles/apex/_solib_${TARGET_CPU}/
example_bin/example_bin.runfiles/apex/_solib_${TARGET_CPU}/_U_S_Scommon_Sbazel_Srules_Upkg_Uextra_Stest_Cshared_Ulib___Ucommon_Sbazel_Srules_Upkg_Uextra_Stest/
example_bin/example_bin.runfiles/apex/_solib_${TARGET_CPU}/_U_S_Scommon_Sbazel_Srules_Upkg_Uextra_Stest_Cshared_Ulib___Ucommon_Sbazel_Srules_Upkg_Uextra_Stest/libsupport.so
example_bin/example_bin.runfiles/apex/common/
example_bin/example_bin.runfiles/apex/common/bazel/
example_bin/example_bin.runfiles/apex/common/bazel/rules_pkg_extra/
example_bin/example_bin.runfiles/apex/common/bazel/rules_pkg_extra/test/
example_bin/example_bin.runfiles/apex/common/bazel/rules_pkg_extra/test/ament_resources.ros_runtime/
example_bin/example_bin.runfiles/apex/common/bazel/rules_pkg_extra/test/ament_resources.ros_runtime/share/
example_bin/example_bin.runfiles/apex/common/bazel/rules_pkg_extra/test/ament_resources.ros_runtime/share/ament_index/
example_bin/example_bin.runfiles/apex/common/bazel/rules_pkg_extra/test/ament_resources.ros_runtime/share/ament_index/resource_index/
example_bin/example_bin.runfiles/apex/common/bazel/rules_pkg_extra/test/ament_resources.ros_runtime/share/ament_index/resource_index/packages/
example_bin/example_bin.runfiles/apex/common/bazel/rules_pkg_extra/test/ament_resources.ros_runtime/share/ament_index/resource_index/packages/test_pkg
example_bin/example_bin.runfiles/apex/common/bazel/rules_pkg_extra/test/example_bin
example_bin/example_bin.runfiles/apex/common/bazel/rules_pkg_extra/test/hello_world.cpp
example_bin/example_bin.runfiles/apex/common/bazel/rules_pkg_extra/test/some_data.txt
example_bin/example_bin.runfiles/root_path/
example_bin/example_bin.runfiles/root_path/some_data.txt
example_bin/example_bin.runfiles/ros_runtime/
example_bin/example_bin.runfiles/ros_runtime/share/
example_bin/example_bin.runfiles/ros_runtime/share/ament_index/
example_bin/example_bin.runfiles/ros_runtime/share/ament_index/resource_index/
example_bin/example_bin.runfiles/ros_runtime/share/ament_index/resource_index/packages/
example_bin/example_bin.runfiles/ros_runtime/share/ament_index/resource_index/packages/test_pkg
example_bin/example_bin.runfiles/ros_runtime/share/test_pkg/
example_bin/example_bin.runfiles/ros_runtime/share/test_pkg/some_data.txt
example_bin/example_bin.runfiles/symlink_path/
example_bin/example_bin.runfiles/symlink_path/some_data.txt
EOF
)

echo "Inspecting file $TAR_GZ_ARCHIVE"
if [ "$(tar -tf $TAR_GZ_ARCHIVE)" != "$EXPECTED_FILES" ]; then
  echo "Expected:"
  echo ""
  echo "$EXPECTED_FILES"
  echo ""
  echo "but found"
  echo ""
  tar -tf $TAR_GZ_ARCHIVE
  exit 1
fi

echo "Success"
