load("@bazel_skylib//rules:expand_template.bzl", "expand_template")
load("@rules_pkg//pkg:mappings.bzl", "pkg_attributes")
load("@rules_pkg//pkg:mappings.bzl", "pkg_files")
load("@rules_pkg//pkg:mappings.bzl", "pkg_filegroup")
load("//common/bazel/rules_pkg_extra:defs.bzl", "self_extracting_archive")
load("//common/bazel/rules_pkg_extra:defs.bzl", "exe_archive")
load("//common/bazel/rules_pkg_extra:defs.bzl", "tar_archives")
load("//common/bazel/rules_pkg_extra:defs.bzl", "tar_package_filegroup")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load(":test_tar_aspect.bzl", "tar_aspect_test")
load(":use_all_runfiles_info_fields.bzl", "use_all_runfiles_info_fields")

ament_pkg_resources(
    name = "ament_resources",
    testonly = True,
    package = "test_pkg",
    resources = {
        ":some_data.txt": "share",
    },
)

cc_binary(
    name = "example_bin",
    testonly = True,
    srcs = ["hello_world.cpp"],
    data = [
        "additional_data",
        "ament_resources",
        "hello_world.cpp",
    ],  # make sure runfiles are handled correctly
    deps = [
        ":shared_lib",
        "//common/bazel/rules_pkg_extra:rpath_padding",
    ],  # depend on a shared library
)

cc_binary(
    name = "support",
    testonly = True,
    srcs = [
        "lib.cpp",
        "lib.hpp",
    ],
    linkshared = True,
    deps = ["@bazel_tools//tools/cpp/runfiles"],
)

cc_import(
    name = "shared_lib",
    testonly = True,
    hdrs = ["lib.hpp"],
    shared_library = ":support",
    deps = ["@bazel_tools//tools/cpp/runfiles"],
)

use_all_runfiles_info_fields(
    name = "additional_data",
    src = "some_data.txt",
)

expand_template(
    name = "some_more_data",
    out = "some_more_data.txt",
    substitutions = {},
    template = "some_data.txt",
)

use_all_runfiles_info_fields(
    name = "more_data",
    src = "some_more_data.txt",
)

exe_archive(
    name = "example_bin_archive",
    testonly = True,
    data = ["more_data"],
    executable = ":example_bin",
    extension = ".tgz",
)

self_extracting_archive(
    name = "example_bin_installer",
    testonly = True,
    executable = ":example_bin",
    expand_rpath_origin = True,
)

pkg_files(
    name = "example_pkg_files_1",
    testonly = True,
    srcs = [
        "lib.cpp",
    ],
    attributes = pkg_attributes(
        mode = "0777",
    ),
)

pkg_files(
    name = "example_pkg_files_2",
    testonly = True,
    srcs = [
        "lib.hpp",
        "some_data.txt",
    ],
    renames = {
        "some_data.txt": "my_data.txt",
    },
)

pkg_filegroup(
    name = "example_pkg_filegroup",
    testonly = True,
    srcs = [
        ":example_pkg_files_1",
        ":example_pkg_files_2",
    ],
)

tar_package_filegroup(
    name = "example_archive",
    testonly = True,
    pkg_filegroup = ":example_pkg_filegroup",
    tar_file_name = "example_archive.tar.bz2",
)

tar_archives(
    name = "example_tar_archive_using_aspect",
    testonly = True,
    archives = [":example_bin"],
)

sh_test(
    name = "test_exe_archive",
    srcs = ["test_exe_archive.bash"],
    args = ["$(TARGET_CPU)"],  # to set the solib_* path properly in the test
    data = [
        ":example_bin_archive",
        "//common/platforms:skip_exec_x86_64_only",
    ],
    deps = ["@bazel_tools//tools/bash/runfiles"],
)

sh_test(
    name = "test_tar_archive",
    srcs = ["test_tar_archive.bash"],
    args = ["$(TARGET_CPU)"],  # to set the solib_* path properly in the test
    data = [":example_tar_archive_using_aspect"],
    deps = ["@bazel_tools//tools/bash/runfiles"],
)

sh_test(
    name = "test_self_extracting_archive",
    srcs = ["test_self_extracting_archive.bash"],
    data = ["example_bin_installer.sh"],
    deps = ["@bazel_tools//tools/bash/runfiles"],
)

sh_test(
    name = "test_tar_package_filegroup",
    srcs = ["test_tar_package_filegroup.bash"],
    data = [":example_archive"],
    deps = ["@bazel_tools//tools/bash/runfiles"],
)

sh_binary(
    name = "check_for_runfiles",
    testonly = True,
    srcs = ["check_for_runfile.bash"],
    data = [
        "some_data.txt",
    ],
    tags = ["manual"],
    deps = ["@bazel_tools//tools/bash/runfiles"],
)

tar_aspect_test(
    name = "test_tar_aspect",
)
