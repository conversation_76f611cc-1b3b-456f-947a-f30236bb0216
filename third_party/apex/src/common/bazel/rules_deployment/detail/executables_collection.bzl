load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "RuleMetaInfo")
load(":providers.bzl", "ConfiguredBinaryInfo")

SEARCHPATH_DEFAULT = "bin"

def _executables_collection_impl(ctx):
    """
    Implementation function of the executables_collection rule.
    """

    root_symlinks = {}

    for executable in ctx.files.executables:
        root_path = "{folder}/{executable}".format(
            name = ctx.label.name,
            folder = ctx.attr.runfiles_folder,
            executable = executable.basename,
        )
        root_symlinks[root_path] = executable

    for target in ctx.attr.executables:
        executable = target[DefaultInfo].files_to_run.executable
        if not executable:
            fail("'{}' must be executable when added to '{}'".format(target.label, ctx.label))
        root_symlinks["{folder}/{executable}".format(
            folder = ctx.attr.runfiles_folder,
            executable = executable.basename,
        )] = executable

    transitive_runfiles = [
        target[DefaultInfo].default_runfiles
        for target in (ctx.attr.executables + ctx.attr.data)
    ]

    return [
        DefaultInfo(
            files = depset(ctx.files.executables + ctx.files.data),
            runfiles = ctx.runfiles(
                files = ctx.files.executables + ctx.files.data,
                root_symlinks = root_symlinks,
            ).merge_all(transitive_runfiles),
        ),
        ConfiguredBinaryInfo(executable = None, executable_file = None, searchpaths = [ctx.attr.runfiles_folder]),
        RuleMetaInfo(),
    ]

executables_collection = rule(
    implementation = _executables_collection_impl,
    doc = """
    Create folder `bin` with symlinks to executables in downstream rules.

    The purpose of this runfiles collection is having a single folder that can
    easily be added to the PATH environment variable. It can be added as a data
    dependency to arbitrary targets that are forwarding runfiles in the default way.
    """,
    attrs = {
        "executables": attr.label_list(
            mandatory = True,
            allow_files = True,
            doc = "Targets or files to be added to the runfiles with a link in the `bin` folder",
        ),
        "runfiles_folder": attr.string(
            default = SEARCHPATH_DEFAULT,
            doc = "Folder path created in the runfiles with links to all executables",
        ),
        "data": attr.label_list(
            doc = "Additional runfiles",
            allow_files = True,
            allow_empty = True,
        ),
    },
)
