#!/bin/sh
set -e

# POSIX shell compatible version of exporting runfiles envvars. This will enable the executable to use the cc version
# of the runfiles library

if [ -z "$RUNFILES_DIR" ] && [ -d "$0.runfiles" ]; then
  RUNFILES_DIR="$0.runfiles"
fi

if ! [ -d "$RUNFILES_DIR" ]; then
  >&2 echo "ERROR: Not able to locate runfiles folder. These are possible causes:
  1. This script was called from another executable without having set the RUNFILES_DIR correctly.
  2. This script was called from within the runfiles folder instead of the entry-point script
     '$(basename $0)' located next to the runfiles folder '$(basename $0).runfiles'."
  exit 1
fi

case $RUNFILES_DIR in
  /*) ;;
  *)
    RUNFILES_DIR="$(pwd)/$RUNFILES_DIR" ;;
esac
export RUNFILES_DIR

{{env}}

__executable__="$RUNFILES_DIR/{{executable}}"
__target_workspace__={{workspace_name}}
__use_runfiles__="{{use_runfiles}}"

if [ -z "${BUILD_WORKING_DIRECTORY:-}" ] ; then # called outside of bazel run
  if [ -n "$__use_runfiles__" ]; then
    cd $RUNFILES_DIR/$__target_workspace__
  fi
  if [ -n "{{prelaunch_script}}" ]; then
    . "$RUNFILES_DIR/{{prelaunch_script}}"
  fi
  exec $__executable__ {{always_args}} {{args}} "$@"
else # called through bazel run
  if [ -z "$__use_runfiles__" ]; then
    cd $BUILD_WORKING_DIRECTORY
  fi
  unset BUILD_WORKING_DIRECTORY
  if [ -n "{{prelaunch_script}}" ]; then
    . "$RUNFILES_DIR/{{prelaunch_script}}"
  fi
  exec $__executable__ {{always_args}}"$@"
fi
