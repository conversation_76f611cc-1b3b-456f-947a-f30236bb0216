"""
Configure a plugin-based tool with custom commands and runtime dependencies.

Today this is only for python tools, but could be extended to also work for any other
language as well.
"""

load("@apex//common/bazel/rules_python_extra:detail/py_info_extractor.bzl", "py_info_extractor")
load("@bazel_skylib//rules:expand_template.bzl", "expand_template")
load("@rules_python//python:defs.bzl", "py_binary")
load(":detail/configured_binary.bzl", "configured_binary")

def configured_tool(
        *,
        name,
        framework = "Apex.OS",
        template = "@apex//common/bazel/rules_deployment:configured_tool_template",
        template_suffix = "py",
        rule = py_binary,
        data = [],
        deps = [],
        searchpath_executables = [],
        args = None,
        env = None,
        use_runfiles_as_working_dir = False,
        searchpaths = None,
        **kwargs):
    """
    EXPERIMENTAL: Generate the main file for the tool and create an executable with all supplied commands.

    All of [configured_binary](#configured_binary) features concerning env, args, use_runfiles_as_working_dir also apply here.

    Args:
        name: The name of the tool
        framework: The name of the framework
        template: The template file to use for the main.py of the tool
        rule: The rule to use for the actual tool binary
        data: List of runtime dependencies for the tool
        deps: List of build time dependencies for the tool (for python tools only)
        env:  see [configured_binary](#configured_binary) rule
        args: see [configured_binary](#configured_binary) rule
        use_runfiles_as_working_dir: see [configured_binary](#configured_binary) rule
        searchpath_executables: see [configured_binary](#configured_binary) rule
        searchpaths: see [configured_binary](#configured_binary) rule
        **kwargs:  Additional arguments to the executable
    """
    generated_main_file = "{name}_binary.{suffix}".format(
        name = name,
        suffix = template_suffix,
    )
    expand_template(
        name = "generate_{name}_main".format(name = name),
        out = generated_main_file,
        substitutions = {
            "{{tool_name}}": name,
            "{{tool_framework}}": framework,
        },
        template = template,
        tags = ["manual"],
        visibility = ["//visibility:private"],
    )

    if rule == py_binary:
        extracted_deps = ["{name}_py_info".format(name = name)]
        py_deps_name = []
        py_info_extractor(
            name = "{name}_py_info".format(name = name),
            deps = deps,
            visibility = ["//visibility:private"],
        )
    else:
        extracted_deps = deps

    rule(
        name = "{name}_binary".format(name = name),
        srcs = [generated_main_file],
        deps = extracted_deps,
        tags = ["manual"],
        visibility = ["//visibility:private"],
    )

    configured_binary(
        name = name,
        executable = "{name}_binary".format(name = name),
        data = data,
        deps = deps,
        env = env,
        args = args,
        searchpaths = searchpaths,
        searchpath_executables = searchpath_executables,
        use_runfiles_as_working_dir = use_runfiles_as_working_dir,
        **kwargs
    )
