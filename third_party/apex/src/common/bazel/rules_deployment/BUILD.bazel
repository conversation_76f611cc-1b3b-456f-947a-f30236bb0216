load("//tools/bazel/rules_docs:defs.bzl", "bzl_docs")

exports_files([
    "detail/launch_template.sh",
])

filegroup(
    name = "configured_tool_template",
    srcs = [":detail/main.py.in"],
    visibility = ["//visibility:public"],
)

bzl_docs(
    name = "bzl_docs",
    srcs = [
        # do not sort
        "providers.bzl",
        "defs.bzl",
    ],
    bzl_docs_deps = [
        "//common/bazel/aspects/rule_meta_aspect:bzl_docs",
        "//common/bazel/rules_python_extra:bzl_docs",
    ],
    detail_srcs = glob(["detail/*.bzl"]),
    deps = [
        "@bazel_skylib//rules:expand_template",
        "@rules_cc//cc/common",
        "@rules_cc//cc/toolchains:toolchain_rules",
        "@rules_python//python:defs_bzl",
    ],
)
