/// \copyright Copyright 2024 Apex.AI, Inc.
/// All rights reserved.
#ifndef CONFIGURED_ENV__CONFIGURED_ENV_HPP
#define CONFIGURED_ENV__CONFIGURED_ENV_HPP
#include <string>

#include "configured_env/runfiles/apex.hpp"

namespace apex
{
namespace configured_env
{

const char DEFAULT_ENV_VAR[] = "PATH";
const char DEFAULT_SEPARATOR[] = ":";

class ConfiguredEnv
{
public:
  ConfiguredEnv();

  /// @brief Modify a given environment variable by appending the new string to it using
  /// the specified separator.
  /// @param new_str the new string to add to the given environment variable
  /// @param env_var the environment variable to modify
  /// @param separator
  void modify_env_var(const std::string & new_str,
                      const std::string & env_var = std::string(DEFAULT_ENV_VAR),
                      const std::string & separator = std::string(DEFAULT_SEPARATOR));

  /// @brief Adds the given runfiles subfolder to the given environment variable
  /// @param runfiles_subfolder the subfolder relative to the runfiles directory that will be added
  ///   to the given environment variable.
  void add_runfiles_folder_to_env_var(
    const std::string & runfiles_subfolder,
    const std::string & env_var = std::string(DEFAULT_ENV_VAR),
    const std::string & separator = std::string(DEFAULT_SEPARATOR));

private:
  runfiles::ApexRunfiles m_apex_runfiles{};
};

}  // namespace configured_env
}  // namespace apex

#endif  //  CONFIGURED_ENV__CONFIGURED_ENV_HPP
