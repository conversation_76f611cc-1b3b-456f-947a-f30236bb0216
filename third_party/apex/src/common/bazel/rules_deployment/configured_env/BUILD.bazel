load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("@rules_python//python:defs.bzl", "py_library")
load("//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "configured_env_pkg",
    cc_libraries = [
        ":configured_env",
        ":runfiles",
    ],
    description = "Helper libraries to configure the runtime environment",
    license = "Apex AI, Inc. License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

apex_cc_shared_library(
    name = "cpputils_shared",
    apex_cc_library = "//common/cpputils",
)

apex_cc_library(
    name = "runfiles",
    srcs = glob(["src/runfiles/*.cpp"]),
    hdrs = glob(["include/configured_env/runfiles/*.hpp"]),
    copts = select({
        # this is the "default" BST value, so when BST is not enabled
        "@apex//common/build_system_transfer:invalid": [""],
        "//conditions:default": ["-D_NO_BAZEL_RUNFILES=1"],
    }),
    dynamic_deps = [
        ":cpputils_shared",
    ] + select({
        # this is the "default" BST value, so when BST is not enabled
        "@apex//common/build_system_transfer:invalid": ["//tools/bazel:runfiles"],
        "//conditions:default": [],
    }),
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils",
    ] + select({
        # this is the "default" BST value, so when BST is not enabled
        "@apex//common/build_system_transfer:invalid": ["@bazel_tools//tools/cpp/runfiles"],
        "//conditions:default": [],
    }),
)

apex_cc_library(
    name = "configured_env",
    srcs = ["src/configured_env.cpp"],
    hdrs = ["include/configured_env/configured_env.hpp"],
    copts = select({
        # this is the "default" BST value, so when BST is not enabled
        "@apex//common/build_system_transfer:invalid": [""],
        "//conditions:default": ["-D_NO_BAZEL_RUNFILES=1"],
    }),
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        ":runfiles",
        "//common/cpputils",
    ],
)

py_library(
    name = "configured_env_py",
    srcs = [
        "__init__.py",
        "configured_env.py",
    ],
    visibility = ["//visibility:public"],
    deps = ["@rules_python//python/runfiles"],
)
