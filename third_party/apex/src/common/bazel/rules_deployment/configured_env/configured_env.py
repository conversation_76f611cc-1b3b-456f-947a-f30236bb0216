import os

try:
    from rules_python.python.runfiles import runfiles

    bazel_runfiles = runfiles.Create()
except ImportError as e:
    runfiles = None
    bazel_runfiles = e


def add_runfiles_folder_to_env_var(
    path: str = "bin", env_var: str = "PATH", separator: str = os.pathsep
):
    if isinstance(bazel_runfiles, ImportError):
        raise bazel_runfiles
    bin_folder = bazel_runfiles._python_runfiles_root + "/" + path
    os.environ[
        env_var
    ] = f"{os.path.expandvars(bin_folder)}{separator}{os.environ[env_var]}"
