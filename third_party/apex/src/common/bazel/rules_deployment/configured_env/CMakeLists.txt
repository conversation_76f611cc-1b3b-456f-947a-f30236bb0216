cmake_minimum_required(VERSION 3.5)

project(configured_env)

find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

add_library(apex_runfiles SHARED
  src/runfiles/apex.cpp
)

ament_target_dependencies(apex_runfiles cpputils)
target_compile_definitions(apex_runfiles
    PUBLIC _NO_BAZEL_RUNFILES=1)

target_include_directories(apex_runfiles PUBLIC
  "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>"
  "$<INSTALL_INTERFACE:include>")

if(QNX)
  target_link_libraries(apex_runfiles "c++fs")
endif()

add_library(${PROJECT_NAME} SHARED
  src/configured_env.cpp
)

ament_target_dependencies(${PROJECT_NAME} cpputils)
target_compile_definitions(${PROJECT_NAME}
    PRIVATE "CONFIGURED_ENV_BUILDING_DLL")
target_include_directories(${PROJECT_NAME} PUBLIC
  "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>"
  "$<INSTALL_INTERFACE:include>")
target_link_libraries(${PROJECT_NAME} apex_runfiles)
ament_export_dependencies(cpputils)

ament_export_include_directories(include)
ament_export_libraries(apex_runfiles)
ament_export_libraries(${PROJECT_NAME})
ament_export_targets(export_apex_runfiles HAS_LIBRARY_TARGET)
ament_export_targets(export_${PROJECT_NAME} HAS_LIBRARY_TARGET)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
  ament_add_gtest(test_${PROJECT_NAME} test/test_configured_env.cpp)
  if(TARGET test_${PROJECT_NAME})
    target_include_directories(test_${PROJECT_NAME} PUBLIC include)
    target_link_libraries(test_${PROJECT_NAME} ${PROJECT_NAME})
  endif()
endif()

ament_package()

install(DIRECTORY include/
  DESTINATION include)

install(TARGETS apex_runfiles
  EXPORT export_apex_runfiles
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin)

install(TARGETS ${PROJECT_NAME}
  EXPORT export_${PROJECT_NAME}
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin)
