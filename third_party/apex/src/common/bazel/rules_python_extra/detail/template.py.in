
import sys
from importlib.metadata import entry_points as eps

if __name__ == "__main__":
    entry_points = list(eps().select(group="{{group}}"))
    final_ep = None
    for ep in entry_points:
        if ep.name == "{{entry_point}}":
            final_ep = ep.load()
    if final_ep is None:
        raise ImportError("Entry point {{entry_point}} not found in group {{group}}")
    sys.exit(final_ep())