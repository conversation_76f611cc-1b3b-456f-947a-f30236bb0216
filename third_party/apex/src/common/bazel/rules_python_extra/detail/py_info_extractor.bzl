load("@apex//common/bazel/rules_deployment:providers.bzl", "ConfiguredBinaryInfo")
load("@rules_python//python:defs.bzl", "PyInfo")

def merge_py_infos(deps):
    """
    Extract all the PyInfos from the given deps, returning one single merged PyInfo.
    """
    py_infos = []
    for target in deps:
        if type(target) == "PyInfo" or (type(target) == "struct" and hasattr(target, "direct_pyc_files")):
            py_infos.append(target)
            continue
        if PyInfo in target:
            py_infos.append(target[PyInfo])
        if ConfiguredBinaryInfo in target and PyInfo in target[ConfiguredBinaryInfo].executable:
            py_infos.append(target[ConfiguredBinaryInfo].executable[PyInfo])
    return PyInfo(
        transitive_sources = depset(transitive = [py_i.transitive_sources for py_i in py_infos]),
        uses_shared_libraries = any([py_i.uses_shared_libraries for py_i in py_infos]),
        imports = depset(transitive = [py_i.imports for py_i in py_infos]),
        has_py2_only_sources = any([py_i.has_py2_only_sources for py_i in py_infos]),
        has_py3_only_sources = any([py_i.has_py3_only_sources for py_i in py_infos]),
    )

def _py_info_extractor_impl(ctx):
    return [merge_py_infos(ctx.attr.deps)]

py_info_extractor = rule(
    implementation = _py_info_extractor_impl,
    attrs = {
        "deps": attr.label_list(
            default = [],
        ),
    },
)
