"""
Helper functions for data structure operations.
"""

def merge_default_info(providers):
    """Merges several `DefaultInfo` providers.

    Args:
        providers: _list of DefaultInfo_

    Returns:
        The merged `DefaultInfo`
    """
    files = [p.files for p in providers if p.files]
    default_runfiles = [
        p.default_runfiles
        for p in providers
        if p.default_runfiles
    ]
    runfiles = None
    if len(default_runfiles) != 0:
        runfiles = default_runfiles[0]
        if len(default_runfiles) > 1:
            runfiles = runfiles.merge_all(default_runfiles[1:])

    return DefaultInfo(files = depset(transitive = files), runfiles = runfiles)

def merge_disjoint_dicts(dicts):
    """
    Merges several dictionaries without clobbering.

    If the dictionaries contain the same key twice, the function will fail.

    Args:
        dicts:  _list of dicts_ - Dictionaries of any type

    Returns:
        The merged dict.
    """
    result = {}
    for d in dicts:
        for key, value in d.items():
            if key in result:
                fail("Duplicate value for key {}: {} vs {}".format(key, value, result[key]))
            result[key] = value
    return result

def remove_duplicates(list_with_duplicates):
    return {item: None for item in list_with_duplicates}.keys()
