#! [load_apex_cc_library]
load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
#! [load_apex_cc_library]

load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")

#! [example_apex_cc_libraries]
apex_cc_library(
    name = "base",
    testonly = True,
    hdrs = ["include/types.hpp"],
    shared_lib_name = "libbase.so",
    strip_include_prefix = "include",
    visibility = ["//common/bazel/rules_cc/test:__pkg__"],
)

apex_cc_library(
    name = "foo",
    testonly = True,
    srcs = ["src/foo.cpp"],
    hdrs = ["include/foo.hpp"],
    shared_lib_name = "libfoo.so",
    strip_include_prefix = "include",
    visibility = ["//common/bazel/rules_cc/test:__pkg__"],
    deps = [":base"],
)

apex_cc_library(
    name = "baz",
    testonly = True,
    srcs = ["src/baz.cpp"],
    hdrs = [
        "include/baz.hpp",
        "include/types.hpp",
    ],
    shared_lib_name = "libbaz.so",
    strip_include_prefix = "include",
    visibility = ["//common/bazel/rules_cc/test:__pkg__"],
    deps = [":foo"],
)
#! [example_apex_cc_libraries]

apex_cc_shared_library(
    name = "foo_shared",
    testonly = True,
    apex_cc_library = "//common/bazel/rules_cc/test/test_cc_libs:foo",
)

apex_cc_shared_library(
    name = "baz_shared",
    testonly = True,
    apex_cc_library = "//common/bazel/rules_cc/test/test_cc_libs:baz",
)

# This target is to test that two shared libraries
# do not statically link to the same library
cc_binary(
    name = "combined",
    testonly = True,
    srcs = ["src/main.cpp"],
    dynamic_deps = [
        ":foo_shared",
        ":baz_shared",
    ],
    visibility = ["//common/bazel/rules_cc/test:__pkg__"],
    deps = [
        ":baz",
        ":foo",
    ],
)

filegroup(
    name = "doc_files",
    srcs = ["BUILD.bazel"],
    visibility = ["//common/bazel/rules_cc/doc:__pkg__"],
)
