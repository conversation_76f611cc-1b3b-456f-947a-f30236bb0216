#! [load_apex_cc_shared_library]
load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")

#! [load_apex_cc_shared_library]
load(":test_apex_cc_library.bzl", "test_apex_cc_library")

test_apex_cc_library(
    name = "test_cc_libs",
    testonly = True,
)

#! [extract_apex_cc_shared_libraries]
apex_cc_shared_library(
    name = "base_shared",
    testonly = True,
    apex_cc_library = "//common/bazel/rules_cc/test/test_cc_libs:base",
)

apex_cc_shared_library(
    name = "foo_shared",
    testonly = True,
    apex_cc_library = "//common/bazel/rules_cc/test/test_cc_libs:foo",
)

apex_cc_shared_library(
    name = "baz_shared",
    testonly = True,
    apex_cc_library = "//common/bazel/rules_cc/test/test_cc_libs:baz",
)
#! [extract_apex_cc_shared_libraries]

#! [using_apex_cc_shared_libraries]
cc_test(
    name = "test_dl_open_basic",
    testonly = True,
    srcs = ["test_dl_open.cpp"],
    data = [":foo_shared"],  # (1)!
    dynamic_deps = [
        ":base_shared",  # (2)!
    ],
    linkopts = select({
        "@apex//common/platforms/os_distro:poky_dunfell": ["-ldl"],
        "//conditions:default": [],
    }),
    deps = [
        "//common/bazel/rules_cc/test/test_cc_libs:base",  # (3)!
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "test_dl_open_nested",
    testonly = True,
    srcs = ["test_dl_open_nested.cpp"],
    data = [
        ":baz_shared",  # (4)!
        # this combined target is not used in this test
        # adding it here just ensures it is built (which tests
        # if the two shared libraries foo and baz link statically
        # to the same underlying library)
        "//common/bazel/rules_cc/test/test_cc_libs:combined",
    ],
    dynamic_deps = [
        ":base_shared",
    ],
    linkopts = select({
        "@apex//common/platforms/os_distro:poky_dunfell": ["-ldl"],
        "//conditions:default": [],
    }),
    deps = [
        "//common/bazel/rules_cc/test/test_cc_libs:base",
        "@googletest//:gtest_main",
    ],
)
#! [using_apex_cc_shared_libraries]

filegroup(
    name = "doc_files",
    srcs = ["BUILD.bazel"],
    visibility = ["//common/bazel/rules_cc/doc:__pkg__"],
)
