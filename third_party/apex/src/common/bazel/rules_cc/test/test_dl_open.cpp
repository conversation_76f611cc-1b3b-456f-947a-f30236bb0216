
#include <memory>

#ifndef _WIN32
  #include <dlfcn.h>
#endif

#include <gtest/gtest.h>

#include "types.hpp"


const std::string TEST_RUNFILES_PATH = "common/bazel/rules_cc/test/test_cc_libs/";

TEST(test_apex_cc_library, dl_open_so_file)
{
  auto shared_lib_path = TEST_RUNFILES_PATH + "libfoo.so";
  void * handle = dlopen(shared_lib_path.c_str(), RTLD_NOW);
  ASSERT_NE(handle, nullptr);
  auto create_foo = (base_factory_t *)(dlsym(handle, "create_foo"));
  auto destroy_foo = (base_dumpster_t *)(dlsym(handle, "destroy_foo"));
  ASSERT_NE(create_foo, nullptr);
  ASSERT_NE(destroy_foo, nullptr);
  auto foo = create_foo("Foo");
  auto foofoo = create_foo("FooFoo");
  ASSERT_EQ(foo->say_hello(), "Hello from Foo!");
  ASSERT_EQ(foofoo->say_hello(), "Hello from FooFoo!");
  ASSERT_EQ(foo->say_goodbye(), "Goodbye!");
  ASSERT_EQ(foofoo->say_goodbye(), "Goodbye!");
  destroy_foo(foo);
  destroy_foo(foofoo);

  dlclose(handle);
}


TEST(test_apex_cc_library, dl_open_invalid_so_file)
{
  auto shared_lib_path = TEST_RUNFILES_PATH + "test_cc_libs/libunknown.so";
  void * handle = dlopen(shared_lib_path.c_str(), RTLD_NOW);
  ASSERT_EQ(handle, nullptr);
}
