load("//tools/bazel/rules_docs:defs.bzl", "bzl_docs")

bzl_docs(
    name = "constants_bzl_docs",
    srcs = ["constants.bzl"],
    detail_srcs = ["detail/constants.bzl"],
)

bzl_docs(
    name = "bzl_docs",
    srcs = [
        # do not sort
        "providers.bzl",
        "defs.bzl",
    ],
    bzl_docs_deps = [
        ":constants_bzl_docs",
        "@apex//common/bazel/aspects/dependencies:bzl_docs",
        "@apex//common/bazel/aspects/rule_meta_aspect:bzl_docs",
        "@apex//common/bazel/helpers:bzl_docs",
        "@apex//tools/ament/rules_ament:bzl_providers_docs",
    ],
    detail_srcs = [
        "detail/cc_library_with_hdrs_extracted_from_srcs.bzl",
        "detail/cc_shared_library.bzl",
        "detail/merge_cc_infos.bzl",
        "detail/merge_dynamic_deps.bzl",
        "detail/apex_cc_library.bzl",
        "detail/apex_cc_shared_library.bzl",
        "detail/helpers.bzl",
        "@rules_cc//cc:bzl_srcs",
        "@bazel_tools//tools/cpp:toolchain_utils.bzl",
    ],
    deps = [
        "@rules_cc//cc/common",
        "@rules_cc//cc/toolchains:toolchain_rules",
    ],
)
