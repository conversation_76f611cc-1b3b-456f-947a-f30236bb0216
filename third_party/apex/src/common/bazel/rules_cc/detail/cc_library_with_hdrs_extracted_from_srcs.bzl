load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "RuleMetaInfo")
load(":detail/apex_cc_library.bzl", "apex_cc_library")

def _filtered_filegroup_impl(ctx):
    return [
        DefaultInfo(
            files = depset([f for f in ctx.files.files_to_filter if f.extension in ctx.attr.suffixes]),
        ),
        RuleMetaInfo(),
    ]

_filtered_filegroup = rule(
    implementation = _filtered_filegroup_impl,
    attrs = {
        "files_to_filter": attr.label_list(
            mandatory = True,
            allow_files = True,
        ),
        "suffixes": attr.string_list(
            mandatory = True,
        ),
    },
)

def cc_library_with_hdrs_extracted_from_srcs(*, name, srcs, tags = [], testonly = False, **kwargs):
    """Wrapper around #apex_cc_library.

    Creates an apex_cc_library where all files passed in to `srcs` with one of the following endings
    will be exported as header files: `.h`, `.hh`, `.hxx`, `.inc`, `.inl`, `.H`

    Arguments are the same as for [rule `apex_cc_library`](common_Sbazel_Srules_Ucc_Cdefs.md#apex_cc_library).
    """
    tags = tags or []
    tags_with_manual = (tags + ["manual"]) if not "manual" in tags else tags
    _filtered_filegroup(
        name = "_%s_hdrs" % name,
        visibility = ["//visibility:private"],
        files_to_filter = srcs,
        suffixes = ["h", "hh", "hpp", "hxx", "inc", "inl", "H"],
        tags = tags_with_manual,
        testonly = testonly,
        applicable_licenses = kwargs.get("applicable_licenses", []),
    )

    apex_cc_library(
        name = name,
        srcs = srcs,
        hdrs = [":_%s_hdrs" % name],
        tags = tags,
        testonly = testonly,
        **kwargs
    )
