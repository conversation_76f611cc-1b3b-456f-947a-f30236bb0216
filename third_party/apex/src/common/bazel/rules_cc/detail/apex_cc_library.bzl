load("@apex//common/bazel/aspects/dependencies:locator.bzl", "locator_for_label")
load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "DynamicLinkingHint")
load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "RuleMetaInfo")
load("@apex//common/bazel/helpers:defs.bzl", "remove_duplicates")
load("@apex//common/bazel/helpers:name_mangling.bzl", "label_to_mangled_name")
load("@rules_cc//cc:defs.bzl", "cc_library")
load(":detail/constants.bzl", "PLAIN_TARGET_SUFFIX")
load(":detail/constants.bzl", "SHARED_TARGET_SUFFIX")
load(":detail/constants.bzl", "MERGED_SHARED_LIBRARY_INFO_TARGET_SUFFIX")
load(":detail/constants.bzl", "SHARED_LIBRARY_NAME_TAG_PREFIX")
load(":detail/helpers.bzl", "extract_dynamic_dep_libraries")
load(":detail/merge_dynamic_deps.bzl", "merge_dynamic_deps")
load(":providers.bzl", "FusedCcLibraryInfo")
load(":providers.bzl", "ApexCcSharedLibraryInfo")

def _cc_shared_library_with_runfiles_impl(ctx):
    shared_library_info = ctx.attr.cc_shared_library[CcSharedLibraryInfo]
    default_info = ctx.attr.cc_shared_library[DefaultInfo]

    # TODO(evan.flynn): seems like the DefaultInfo from cc_shared_library doesn't
    # include all dynamic deps .so files in its runfiles, so extract
    # and add them here
    files = extract_dynamic_dep_libraries(shared_library_info)

    shared_lib_name = shared_library_info.linker_input.libraries[0].dynamic_library.short_path
    if ctx.attr.shared_lib_name:
        shared_lib_name = ctx.attr.shared_lib_name
    return [
        DefaultInfo(
            files = default_info.files,
            runfiles = ctx.runfiles(files = files).merge_all([
                default_info.data_runfiles,
            ]),
        ),
        shared_library_info,
        ApexCcSharedLibraryInfo(
            repo_name = ctx.workspace_name,
            shared_lib_name = shared_lib_name,
        ),
        RuleMetaInfo(
            dynamic_linking_hint = DynamicLinkingHint(),
            deps = [locator_for_label(ctx.attr, "cc_shared_library")],
        ),
    ]

_cc_shared_library_with_runfiles = rule(
    implementation = _cc_shared_library_with_runfiles_impl,
    doc = """cc_shared_library wrapper that amends missing runfiles.""",
    attrs = {
        "cc_shared_library": attr.label(
            providers = [DefaultInfo, CcSharedLibraryInfo],
            doc = "A target with the CcSharedLibraryInfo provider (e.g. a `cc_shared_library`)",
        ),
        "shared_lib_name": attr.string(),
    },
    provides = [DefaultInfo, CcSharedLibraryInfo, ApexCcSharedLibraryInfo, RuleMetaInfo],
)

def _cc_shared_library(name, **kwargs):
    tags = kwargs.pop("tags", default = [])
    testonly = kwargs.pop("testonly", default = False)
    visibility = kwargs.pop("visibility", default = [])
    shared_lib_name = kwargs.pop("shared_lib_name", default = None)
    cc_shared_library_name = name + "__cc_shared_library"
    native.cc_shared_library(
        name = cc_shared_library_name,
        tags = tags,
        testonly = testonly,
        shared_lib_name = shared_lib_name,
        **kwargs
    )

    _cc_shared_library_with_runfiles(
        name = name,
        cc_shared_library = cc_shared_library_name,
        shared_lib_name = shared_lib_name,
        tags = tags,
        testonly = testonly,
        visibility = visibility,
    )

def _apex_cc_library_impl(ctx):
    """The apex_cc_library implementation function."""
    default_info = ctx.attr.cc_library[DefaultInfo]
    cc_info = ctx.attr.cc_library[CcInfo]

    rule_meta_info = RuleMetaInfo(
        dynamic_linking_hint = DynamicLinkingHint(),
        deps = [locator_for_label(ctx.attr, "cc_library")],
    )

    return [
        default_info,
        cc_info,
        rule_meta_info,
        FusedCcLibraryInfo(
            repo_name = ctx.workspace_name,
            cc_library = ctx.attr.cc_library,
            cc_shared_library = ctx.attr.cc_shared_library,
        ),
    ]

_apex_cc_library = rule(
    implementation = _apex_cc_library_impl,
    doc =
        """
        This rule acts just the same as a regular cc_library but has a "hidden"
        shared library that can be extracted as needed.
    
        Default to only provide the regular CcInfo provider and
        hide the CcSharedLibraryInfo within the FusedCcLibraryInfo.
        """,
    attrs = {
        "cc_library": attr.label(
            providers = [CcInfo],
            doc = "A cc_library target",
        ),
        "cc_shared_library": attr.label(
            providers = [CcSharedLibraryInfo],
            doc = "A cc_shared_library target",
        ),
        "user_provided_lib_name": attr.string(),
        # The rest of these attrs are provided so that the apex_cc_library can act as a drop-in replacement for all
        # aspects inspecting the rules attributes.
        "srcs": attr.label_list(
            allow_files = True,
        ),
        "hdrs": attr.label_list(allow_files = True),
        "includes": attr.string_list(),
        "implementation_deps": attr.label_list(allow_files = True),
        "strip_include_prefix": attr.string(),
        "defines": attr.string_list(),
        "deps": attr.label_list(
            providers = [CcInfo],
            doc = "The deps from the cc_library",
        ),
        "include_prefix": attr.string(),
        "copts": attr.string_list(),
        "conlyopts": attr.label_list(),
        "linkstatic": attr.bool(),
        "alwayslink": attr.bool(),
        "linkopts": attr.string_list(),
        "additional_compiler_inputs": attr.label_list(),
        "additional_linker_inputs": attr.label_list(),
        "data": attr.label_list(allow_files = True),
        "linkshared": attr.bool(),
        "local_defines": attr.string_list(),
        "textual_hdrs": attr.label_list(allow_files = True),
    },
    provides = [CcInfo, RuleMetaInfo, FusedCcLibraryInfo],
)

def apex_cc_library(
        *,
        name,
        deps = [],
        dynamic_deps = None,
        linkstatic = False,
        exports_filter = [],
        disallow_undefined_symbols = False,
        user_link_flags = [],
        linkopts = [],
        shared_lib_name = None,  # TODO(32040): remove shared_lib_name attr from here
        applicable_licenses = [],
        **kwargs):
    """
    A cc_library implementation with improved support for defining shared libraries.

    The default behavior of this rule is to provide a library for static linkage. For this use case,
    it can be considered a drop-in replacement for `cc_library`. However, in some
    cases, it is required to provide shared libraries, e.g. when used as a runtime plugin. In this
    case, it offers the following benefits.

    When linking the same symbol in two shared libraries, this may lead to undefined behavior
    (see [the cc_shared_library documentation](https://bazel.build/reference/be/c-cpp#cc_shard_library_examples)
    for more details). This UB can be prevented by defining a shared library for all
    libraries that potentially will be linked twice in two different shared libraries.

    The [rule `apex_cc_library`](common_Sbazel_Srules_Ucc_Cdefs.md#apex_cc_library) supports this
    by providing necessary meta-data to downstream dependencies for building shared libraries in an
    efficient way without detailed analysis of the dependency tree (see [apex_cc_shared_library](
    common_Sbazel_Srules_Ucc_Cdefs.md#apex_cc_shared_library)).
    """
    if not kwargs.get("hrds") and not kwargs.get("srcs") and not applicable_licenses:
        applicable_licenses = ["@apex//common/integrity:no_code"]

    static_target_name = name + PLAIN_TARGET_SUFFIX
    shared_target_name = name + SHARED_TARGET_SUFFIX
    merged_dynamic_deps_name = name + MERGED_SHARED_LIBRARY_INFO_TARGET_SUFFIX

    # TODO(32040): uncomment this after follow-up fix
    # if "shared_lib_name" in kwargs:
    #     err_str = "\n\tCannot set `shared_lib_name` for apex_cc_library: %s\n" % name
    #     err_str += "\tInstead set `shared_lib_name = %s` in extractor `apex_cc_shared_library` where needed" % kwargs.pop("shared_lib_name")
    #     fail(err_str)
    tags = kwargs.pop("tags", default = []) or []  # or [] to handle if someone sets tags = None

    user_provided_lib_name = shared_lib_name

    # BACKWARDS COMPATIBILITY, can be deleted after install spaces are gone
    if shared_lib_name == None and ("Use short library name in install space" in tags or "Python extension module" in tags):
        shared_lib_name = "lib{name}.so".format(name = name.rstrip("__lib"))

    # End of backwards compatibility code

    # TODO(32040): remove the `if`, always set shared_lib_name to the mangled name
    if not shared_lib_name:
        label = native.package_relative_label(name)
        shared_lib_name = "lib{mangled_name}.so".format(mangled_name = label_to_mangled_name(label))

    implementation_deps = kwargs.pop("implementation_deps", default = [])
    tags_with_manual = remove_duplicates(tags + ["manual"])
    visibility = kwargs.pop("visibility", default = [])
    testonly = kwargs.pop("testonly", default = False)

    if dynamic_deps == None:
        dynamic_deps = []
        dynamic_deps += deps
        dynamic_deps += [d for d in implementation_deps if d not in deps]

    merge_dynamic_deps(
        name = merged_dynamic_deps_name,
        dynamic_deps = dynamic_deps,
        tags = tags_with_manual,
        testonly = testonly,
        applicable_licenses = ["@apex//common/integrity:detail"],
        visibility = ["//visibility:private"],
    )

    cc_library(
        name = static_target_name,
        linkstatic = linkstatic,
        deps = deps,
        implementation_deps = implementation_deps,
        tags = tags_with_manual + [SHARED_LIBRARY_NAME_TAG_PREFIX + shared_lib_name],
        linkopts = linkopts,
        testonly = testonly,
        applicable_licenses = ["@apex//common/integrity:detail"],
        visibility = ["//visibility:private"],
        **kwargs
    )

    # TODO: switch to loading this rule from @rules_cc//cc:defs.bzl once
    # it is available
    _cc_shared_library(
        name = shared_target_name,
        shared_lib_name = shared_lib_name,
        deps = [static_target_name],
        dynamic_deps = [merged_dynamic_deps_name],
        tags = tags_with_manual,
        user_link_flags = user_link_flags + linkopts + (["-Wl,--no-undefined"] if disallow_undefined_symbols else []),
        testonly = testonly,
        visibility = ["//visibility:private"],
        applicable_licenses = ["@apex//common/integrity:detail"],
        exports_filter = exports_filter,
    )

    _apex_cc_library(
        name = name,
        cc_library = static_target_name,
        cc_shared_library = shared_target_name,
        user_provided_lib_name = user_provided_lib_name,
        deps = deps,
        implementation_deps = implementation_deps,
        tags = tags,
        testonly = testonly,
        visibility = visibility,
        linkstatic = linkstatic,
        linkopts = linkopts,
        applicable_licenses = applicable_licenses,
        **kwargs
    )
