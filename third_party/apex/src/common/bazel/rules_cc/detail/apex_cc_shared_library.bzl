load("@apex//common/bazel/aspects/dependencies:locator.bzl", "locator_for_label")
load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "RuleMetaInfo")
load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "DynamicLinkingHint")
load("@apex//common/bazel/helpers:defs.bzl", "merge_default_info")
load("@apex//common/bazel/rules_cc:providers.bzl", "FusedCcLibraryInfo")
load("@apex//common/bazel/rules_cc:providers.bzl", "ApexCcSharedLibraryInfo")
load("@apex//tools/ament/rules_ament:providers.bzl", "AmentInfo")

def _apex_cc_shared_library_impl(ctx):
    """
    Extract the cc_shared_library target from a FusedCcLibraryInfo provider.

    Args:
        ctx: the current context
    """
    target = ctx.attr.apex_cc_library[FusedCcLibraryInfo].cc_shared_library
    repo_name = ctx.attr.apex_cc_library[FusedCcLibraryInfo].repo_name
    files = []

    providers = []
    default_infos = []
    providers.append(target[CcSharedLibraryInfo])
    default_infos.append(target[DefaultInfo])
    target_lib = target[DefaultInfo].files.to_list()[0]
    shared_lib_name = target_lib.basename
    if ctx.attr.shared_lib_name:
        symlinked_shared_lib = ctx.actions.declare_file(ctx.attr.shared_lib_name)
        ctx.actions.symlink(output = symlinked_shared_lib, target_file = target_lib)
        files.append(symlinked_shared_lib)
        shared_lib_name = ctx.attr.shared_lib_name

    providers.append(ApexCcSharedLibraryInfo(
        repo_name = repo_name,
        shared_lib_name = shared_lib_name,
    ))

    data_default_runfiles = []
    if ctx.attr.ament_runfiles and AmentInfo in ctx.attr.apex_cc_library and ctx.attr.apex_cc_library[AmentInfo].default_info:
        data_default_runfiles.append(ctx.attr.apex_cc_library[AmentInfo].default_info.default_runfiles)
    for data in ctx.attr.data:
        if DefaultInfo in data:
            data_default_runfiles.append(data[DefaultInfo].runfiles)
        if ctx.attr.ament_runfiles and AmentInfo in data and data[AmentInfo].default_info.default_runfiles:
            data_default_runfiles.append(data[AmentInfo].default_info)

    default_infos.append(DefaultInfo(
        runfiles = ctx.runfiles(ctx.files.data).merge_all(data_default_runfiles),
    ))
    merged_default_info = merge_default_info(default_infos)

    return providers + [
        merged_default_info,
        RuleMetaInfo(
            dynamic_linking_hint = DynamicLinkingHint(),
            deps = [locator_for_label(ctx.attr, "apex_cc_library")],
        ),
    ]

apex_cc_shared_library = rule(
    implementation = _apex_cc_shared_library_impl,
    doc = """
    Extract a shared library from an apex_cc_library.

    All (transitive) dependencies of this rule that are `apex_cc_library` targets will be linked
    dynamically. This will prevent any UB due to symbols being linked twice in different shared
    libraries.

    Use this as a replacement for `cc_shared_library` in combination with replacing `cc_library` with
    `apex_cc_library`. If certain `cc_library` targets in the dependency tree cannot be replaced (e.g.
    due to third-party dependencies, then this might still work. There is a build time check in place
    for preventing UB due to multiple symbols.

    Arguments:
        apex_cc_library: A target with the FusedCcLibraryInfo provider (e.g. a `apex_cc_library`)
        shared_lib_name: Optional: create symlink with this name to underlying shared library
    """,
    attrs = {
        "apex_cc_library": attr.label(
            providers = [FusedCcLibraryInfo],
            mandatory = True,
            doc = "A target with the FusedCcLibraryInfo provider (e.g. a `apex_cc_library`)",
        ),
        "shared_lib_name": attr.string(
            doc = "Optional: create symlink with this name to underlying shared library",
            mandatory = False,
        ),
        "data": attr.label_list(
            doc = "Additional runfiles",
            allow_files = True,
            mandatory = False,
        ),
        "ament_runfiles": attr.bool(
            doc = "Add any optional ament runfiles, provided by the apex_cc_library or data",
            default = False,
        ),
    },
    provides = [CcSharedLibraryInfo, ApexCcSharedLibraryInfo, RuleMetaInfo],
)
