"""Helper functions for make variable expansion in custom rules."""

load("@rules_cc//cc/common:cc_helper.bzl", "cc_helper")

def expand_make_variable(ctx, var):
    """Expand a string with make variable expansion.

    Args:
        ctx: The rule context.
        var: The variable to expand. It can be a string, list, or dict.
    """
    if var == None:
        return None
    elif type(var) == type(""):
        env = {"key": var}
    elif type(var) == type([]):
        env = {
            str(i): item
            for i, item in enumerate(var)
        }
    elif type(var) == type({}):
        env = var
    else:
        fail("Unsupported type for make variable expansion: %s" % type(var))

    ctx_for_cc_helper = struct(
        var = ctx.var,
        attr = struct(
            env = env,
            data = getattr(ctx.attr, "data", []),
        ),
        expand_location = ctx.expand_location,
    )

    if type(var) == type(""):
        return cc_helper.get_expanded_env(ctx_for_cc_helper, {})["key"]
    elif type(var) == type([]):
        return cc_helper.get_expanded_env(ctx_for_cc_helper, {}).values()
    else:
        return cc_helper.get_expanded_env(ctx_for_cc_helper, {})
