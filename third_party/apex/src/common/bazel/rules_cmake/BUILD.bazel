load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_binary")
load("//tools/bazel/rules_docs:defs.bzl", "bzl_docs")

py_binary(
    name = "cmake_configure_file",
    srcs = ["cmake_configure_file.py"],
    imports = ["."],
    python_version = "PY3",
    srcs_version = "PY3",
    visibility = ["//visibility:public"],
    deps = [
        requirement("click"),
    ],
)

bzl_docs(
    name = "bzl_docs",
    srcs = [],
    detail_srcs = [
        "defs.bzl",
        "functions.bzl",
        "detail/cmake_configure_file.bzl",
    ],
)
