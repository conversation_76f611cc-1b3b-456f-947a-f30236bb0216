# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

# ======================================================================
# CMake Like Configure File
#
# Implemented CMake's configure file in python
# Functionality is described in
#   https://cmake.org/cmake/help/latest/command/configure_file.html
#
# ======================================================================

import sys
import click
import re


class CMakeLikeConfigureError(Exception):
    def __init__(self, kind, message):
        self.kind = kind
        self.message = message

    def __str__(self):
        return f"{self.kind}: {self.message}"


class CMakeLikeConfigureFile:
    def __is_true(self, key) -> bool:
        if key not in self.values:
            return False
        value = self.values[key]
        if value is None:
            return False
        if isinstance(value, int) and value == 0:
            return False
        if isinstance(value, str) and value.upper() in ["", "0", "OFF", "FALSE"]:
            return False
        return True

    def __get_value(self, key) -> str:
        value = self.values.get(key, None)
        if value is None:
            if self.strict:
                raise CMakeLikeConfigureError(
                    "undefined value in strict mode", f'"{key}" is not defined'
                )
            return ""
        return value

    # Handle #cmakedefine
    _re_hash_cmakedefine = re.compile(r"^(\s*)#cmakedefine\s+(\w+)(.*)")

    def _replace_hash_cmakedefine(self, line) -> str:
        m = self._re_hash_cmakedefine.match(line)
        if m:
            if self.__is_true(m[2]):
                return f"{m[1]}#define {m[2]}{m[3]}"
            else:
                return f"{m[1]}/* #undef {m[2]} */"
        else:
            return line

    # Handle #cmakedefine01
    _re_hash_cmakedefine01 = re.compile(r"^(\s*)#cmakedefine01\s+(\w+)(.*)")

    def _replace_hash_cmakedefine01(self, line) -> str:
        m = self._re_hash_cmakedefine01.match(line)
        if m:
            return f"{m[1]}#define {m[2]} {1 if self.__is_true(m[2]) else 0}{m[3]}"
        else:
            return line

    # Handle String replace
    _re_variables_shell_style = re.compile(r".*(\$\{(\w+)\})")

    def _replace_variables_shell_style(self, line) -> str:
        m = self._re_variables_shell_style.match(line)
        while m:
            r = self.__get_value(m[2])
            line = line.replace(m[1], r)
            m = self._re_variables_shell_style.match(line)
        return line

    # Handle AtOnly replace
    _re_variables_at_style = re.compile(r".*(@(\w+)@)")

    def _replace_variables_at_style(self, line) -> str:
        m = self._re_variables_at_style.match(line)
        while m:
            r = self.__get_value(m[2])
            line = line.replace(m[1], r)
            m = self._re_variables_at_style.match(line)
        return line

    # Handle all
    def substitute(self, line) -> str:
        for fn in [
            fn
            for fn in dir(self)
            if fn.startswith("_replace_")
            and (not self.atonly or fn != "_replace_variables_shell_style")
        ]:
            fc = getattr(self, fn)
            line = fc(line)
        return line

    def __init__(self, values, *, strict=False, atonly=False) -> None:
        self.values = values
        self.strict = strict
        self.atonly = atonly


@click.command()
@click.option(
    "-D",
    "--define",
    "defined_values_list",
    help="Define a value",
    default=[],
    multiple=True,
    metavar="<list>",
)
@click.option(
    "--strict/--no-strict",
    is_flag=True,
    show_default=True,
    default=False,
    help="Use strict mode",
)
@click.option(
    "--atonly/--no-atonly",
    is_flag=True,
    show_default=True,
    default=False,
    help="Use @atonly mode",
)
@click.argument("input_file", type=click.File(mode="rt"), nargs=1, required=True)
@click.argument("output_file", type=click.File(mode="wt"), nargs=1, required=True)
def main(defined_values_list, strict, atonly, input_file, output_file):
    try:
        defined_values = dict([s.split("=", 1) for s in defined_values_list])
    except ValueError:
        print(  # noqa: T201
            'Error: key/valu-pairs arguments delimited by "=" are required for "'
            '"-D/--define"',
            file=sys.stderr,
        )
        sys.exit(1)

    clcf = CMakeLikeConfigureFile(defined_values, atonly=atonly, strict=strict)
    try:
        for line in input_file:
            print(clcf.substitute(line.rstrip()), file=output_file)
    except CMakeLikeConfigureError as e:
        print(f"Error: {e}", file=sys.stderr)  # noqa: T201
        sys.exit(1)


if __name__ == "__main__":
    main()
