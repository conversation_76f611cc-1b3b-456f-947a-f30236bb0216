load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "RuleMetaInfo")
load("@apex//common/bazel/rules_config:defs.bzl", "expand_make_variables")

def _expand_arg(ctx, key, value):
    expanded_value = expand_make_variables(ctx, value)
    return "-D{}={}".format(key, expanded_value)

def generate_configured_file(ctx):
    """Starlark wrapper around cmake_configure_file.py"""
    generated_files = []
    inputs = []
    for t in ctx.attr.data:
        inputs += t[DefaultInfo].files.to_list()
    tmpl, output_file = ctx.file.src, ctx.outputs.out
    args = ctx.actions.args()
    args.add(tmpl)
    args.add(output_file)
    for key, value in ctx.attr.defines.items():
        new_arg = _expand_arg(ctx, key, value)
        args.add(new_arg)
    args.add("--strict" if ctx.attr.strict else "--no-strict")
    args.add("--atonly" if ctx.attr.atonly else "--no-atonly")
    ctx.actions.run(
        executable = ctx.executable._cmake_configure_file,
        arguments = [args],
        inputs = inputs + [tmpl],
        outputs = [output_file],
    )
    generated_files.append(output_file)
    return generated_files

def _cmake_configure_file_impl(ctx):
    outs = generate_configured_file(ctx)
    runfiles = ctx.runfiles(outs)
    return [
        RuleMetaInfo(),
        DefaultInfo(
            files = depset(outs),
            runfiles = runfiles,
        ),
    ]

_cmake_configure_file = rule(
    doc = "Implement [cmake_configure_file](https://cmake.org/cmake/help/latest/command/configure_file.html) from [CMake](cmake.org).",
    attrs = {
        "src": attr.label(
            doc = "Input (source) file",
            allow_single_file = True,
            mandatory = True,
        ),
        "out": attr.output(
            doc = "Output file",
            mandatory = True,
        ),
        "defines": attr.string_dict(
            doc = "Dictionary of defines (use empty values for undefined)",
            default = {},
        ),
        "strict": attr.bool(
            doc = "Undefined values are not allowed in replacements",
            default = False,
        ),
        "atonly": attr.bool(
            doc = "Replacement is done only for keys between @-signs",
            default = False,
        ),
        "data": attr.label_list(
            mandatory = False,
            allow_files = True,
            doc = "Used to specify additional input files that may be required for Make variable replacement",
        ),
        "_cmake_configure_file": attr.label(
            cfg = "exec",
            executable = True,
            default = "@apex//common/bazel/rules_cmake:cmake_configure_file",
        ),
    },
    implementation = _cmake_configure_file_impl,
)

# TODO(33781): remove macro after switch back to keyed_label_dict
# after upgrading to bzlmods and > Bazel 7.5
def cmake_configure_file(*, name, label_defines = {}, **kwargs):
    """
    Implement [cmake_configure_file](https://cmake.org/cmake/help/latest/command/configure_file.html) from [CMake](cmake.org).

    For example a `template.in` file with the contents:

    ```
    @VAR@
    @FILENAME@
    ```

    This rule can be used in the BUILD file with:

    ```
    cmake_config_file(
        name = "foo",
        src = "template.in`,
        out = "output_file",
        defines = {
          "VAR": "bar",
          "FILENAME": "$(rlocationpath file.hpp)",
        },
    )
    ```

    Arguments:
        src: label - input (source) file
        out: output - Output file
        defines: string_dict - Dictionary of defines with a string as the value
    """
    _cmake_configure_file(
        name = name,
        **kwargs
    )
