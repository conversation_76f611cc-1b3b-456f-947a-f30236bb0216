load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "interrupt_pkg",
    cc_libraries = [":interrupt"],
    description = "Package containing Apex interrupt handler helper",
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "interrupt",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils:cpputils_pkg",
    ],
)

apex_cc_library(
    name = "interrupt",
    srcs = [
        "src/interrupt_handler.cpp",
    ],
    hdrs = glob(["include/interrupt/*.hpp"]),
    applicable_licenses = ["//common/integrity:embedded_gold"],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "//common/apexutils",
        "//common/cpputils",
    ],
)

apex_cc_test(
    name = "interrupt_tests",
    srcs = [
        "test/test.cpp",
        "test/test_interrupt.cpp",
    ],
    deps = [
        ":interrupt",
        "//common/apexutils",
        "//common/cpputils",
        "@googletest//:gtest",
    ],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
