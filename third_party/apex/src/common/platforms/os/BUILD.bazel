load("//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

constraint_value(
    name = "freertos",
    constraint_setting = "@platforms//os",
)

alias(
    name = "os_abstraction",
    actual = select({
        "@platforms//os:linux": "@apex//common/platforms/os/linux:os_abstraction_linux",
        "@platforms//os:osx": "@apex//common/platforms/os/mac:os_abstraction_mac",
        "@platforms//os:qnx": "@apex//common/platforms/os/qnx:os_abstraction_qnx",
        "@platforms//os:freebsd": "@apex//common/platforms/os/unix:os_abstraction_unix",
        "@platforms//os:windows": "@apex//common/platforms/os/win:os_abstraction_win",
        "//conditions:default": "@apex//common/platforms/os/linux:os_abstraction_linux",
    }),
    visibility = ["//visibility:public"],
)

ros_pkg(
    name = "os_abstraction_pkg",
    cc_libraries = [
        ":os_abstraction",
    ],
    description = "Abstraction Library of OS functions (e.g. POSIX)",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "os_abstraction",
    version = "2.90.0",
    visibility = ["//visibility:public"],
)
