// Copyright (c) 2023 by Apex.AI Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// SPDX-License-Identifier: Apache-2.0
#ifndef IOX_HOOFS_LINUX_PLATFORM_EPOLL_HPP
#define IOX_HOOFS_LINUX_PLATFORM_EPOLL_HPP

#include <sys/epoll.h>

#include <array>

using iox_epoll_event_t = struct epoll_event;
using iox_epoll_result = std::array<iox_epoll_event_t, 128>;

inline int iox_epoll_create()
{
  return ::epoll_create1(EPOLL_CLOEXEC);
}

inline int iox_epoll_add(int epoll_fd, int fd_to_add, int flags = EPOLLIN)
{
  iox_epoll_event_t ev;
  ev.events = flags;
  ev.data.fd = fd_to_add;
  return ::epoll_ctl(epoll_fd, EPOLL_CTL_ADD, fd_to_add, &ev);
}

inline int iox_epoll_delete(int epoll_fd, int fd_to_delete)
{
  return ::epoll_ctl(epoll_fd, EPOLL_CTL_DEL, fd_to_delete, nullptr);
}

inline int iox_epoll_wait(int epoll_fd, iox_epoll_result & events, int timeout_ms = -1)
{
  return ::epoll_wait(epoll_fd, events.data(), events.size(), timeout_ms);
}

#endif  // IOX_HOOFS_LINUX_PLATFORM_EPOLL_HPP
