# ""
package(
    default_package_metadata = [],
    default_visibility = ["//visibility:public"],
)

alias(
    name = "skip_exec_x86_64_only",
    actual = "//common/platforms/detail:skip_exec_x86_64_only_lib",
)

# Ubuntu 22.04 Jammy sysroot platforms

platform(
    name = "gcc9-linux_jammy-x86_64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_jammy",
        "@platforms//cpu:x86_64",
        "@bazel_tools//tools/cpp:gcc",
        "@apex//common/platforms/cc_compiler_version:gcc9",
    ],
)

platform(
    name = "gcc9-linux_jammy-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_jammy",
        "@platforms//cpu:aarch64",
        "@bazel_tools//tools/cpp:gcc",
        "@apex//common/platforms/cc_compiler_version:gcc9",
    ],
)

platform(
    name = "gcc10-linux_jammy-x86_64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_jammy",
        "@platforms//cpu:x86_64",
        "@bazel_tools//tools/cpp:gcc",
        "@apex//common/platforms/cc_compiler_version:gcc10",
    ],
)

platform(
    name = "gcc10-linux_jammy-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_jammy",
        "@platforms//cpu:aarch64",
        "@bazel_tools//tools/cpp:gcc",
        "@apex//common/platforms/cc_compiler_version:gcc10",
    ],
)

platform(
    name = "gcc11-linux_jammy-x86_64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_jammy",
        "@platforms//cpu:x86_64",
        "@bazel_tools//tools/cpp:gcc",
        "@apex//common/platforms/cc_compiler_version:gcc11",
    ],
)

platform(
    name = "gcc11-linux_jammy-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_jammy",
        "@platforms//cpu:aarch64",
        "@bazel_tools//tools/cpp:gcc",
        "@apex//common/platforms/cc_compiler_version:gcc11",
    ],
)

platform(
    name = "clang16-linux_jammy-x86_64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_jammy",
        "@platforms//cpu:x86_64",
        "@bazel_tools//tools/cpp:clang",
        "@apex//common/platforms/cc_compiler_version:clang16",
    ],
)

platform(
    name = "clang16-linux_jammy-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_jammy",
        "@platforms//cpu:aarch64",
        "@bazel_tools//tools/cpp:clang",
        "@apex//common/platforms/cc_compiler_version:clang16",
    ],
)

# Ubuntu 20.04 Focal sysroot platforms

platform(
    name = "gcc9-linux_focal-x86_64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_focal",
        "@platforms//cpu:x86_64",
        "@bazel_tools//tools/cpp:gcc",
        "@apex//common/platforms/cc_compiler_version:gcc9",
    ],
)

platform(
    name = "gcc10-linux_focal-x86_64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_focal",
        "@platforms//cpu:x86_64",
        "@bazel_tools//tools/cpp:gcc",
        "@apex//common/platforms/cc_compiler_version:gcc10",
    ],
)

platform(
    name = "clang16-linux_focal-x86_64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_focal",
        "@platforms//cpu:x86_64",
        "@bazel_tools//tools/cpp:clang",
        "@apex//common/platforms/cc_compiler_version:clang16",
    ],
)

platform(
    name = "gcc9-linux_focal-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_focal",
        "@platforms//cpu:aarch64",
        "@bazel_tools//tools/cpp:gcc",
        "@apex//common/platforms/cc_compiler_version:gcc9",
    ],
)

platform(
    name = "gcc10-linux_focal-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_focal",
        "@platforms//cpu:aarch64",
        "@bazel_tools//tools/cpp:gcc",
        "@apex//common/platforms/cc_compiler_version:gcc10",
    ],
)

platform(
    name = "clang16-linux_focal-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@apex//common/platforms/os_distro:ubuntu_focal",
        "@platforms//cpu:aarch64",
        "@bazel_tools//tools/cpp:clang",
        "@apex//common/platforms/cc_compiler_version:clang16",
    ],
)

platform(
    name = "gcc11-poky-kirkstone-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@platforms//cpu:aarch64",
        "@bazel_tools//tools/cpp:gcc",
        "@apex//common/platforms/cc_compiler_version:gcc11",
        "@apex//common/platforms/features:yocto",
        "@apex//common/platforms/os_distro:poky_kirkstone",
    ],
)

#####
# Legacy platform names, recommended to use platform names with os_distro
# constraint in the name instead

alias(
    name = "gcc9-linux-x86_64",
    actual = "gcc9-linux_focal-x86_64",
)

alias(
    name = "gcc10-linux-x86_64",
    actual = "gcc10-linux_focal-x86_64",
)

alias(
    name = "gcc11-linux-x86_64",
    actual = "gcc11-linux_jammy-x86_64",
)

alias(
    name = "gcc11-linux-aarch64",
    actual = "gcc11-linux_jammy-aarch64",
)

alias(
    name = "clang16-linux-x86_64",
    actual = "clang16-linux_focal-x86_64",
)

alias(
    name = "gcc9-linux-aarch64",
    actual = "gcc9-linux_focal-aarch64",
)

alias(
    name = "gcc10-linux-aarch64",
    actual = "gcc10-linux_focal-aarch64",
)

alias(
    name = "clang16-linux-aarch64",
    actual = "clang16-linux_focal-aarch64",
)

platform(
    name = "qcc71_nvidia-qnx-aarch64",
    constraint_values = [
        "@platforms//os:qnx",
        "@apex//common/platforms/os_distro:qnx71_qos22",
        "@platforms//cpu:aarch64",
        "@apex//common/platforms/cc_compiler:qcc",
        "@apex//common/platforms/cc_compiler_version:qcc71",
        "@apex//common/platforms/features:nvidia",
    ],
)

platform(
    name = "gcc9-linux_dunfell-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@platforms//cpu:aarch64",
        "@apex//common/platforms/features:yocto",
        "@apex//common/platforms/os_distro:poky_dunfell",
        "@bazel_tools//tools/cpp:gcc",
        "@apex//common/platforms/cc_compiler_version:gcc9",
    ],
)

platform(
    name = "gcc11-linux_kirkstone-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@platforms//cpu:aarch64",
        "@apex//common/platforms/features:yocto",
        "@apex//common/platforms/os_distro:poky_kirkstone",
        "@bazel_tools//tools/cpp:gcc",
        "@apex//common/platforms/cc_compiler_version:gcc11",
    ],
)

platform(
    name = "qcc71-qnx-x86_64",
    constraint_values = [
        "@platforms//os:qnx",
        "@apex//common/platforms/os_distro:qnx71_qos22",
        "@platforms//cpu:x86_64",
        "@apex//common/platforms/cc_compiler:qcc",
        "@apex//common/platforms/cc_compiler_version:qcc71",
    ],
)

platform(
    name = "qcc71-qnx-aarch64",
    constraint_values = [
        "@platforms//os:qnx",
        "@apex//common/platforms/os_distro:qnx71_qos22",
        "@platforms//cpu:aarch64",
        "@apex//common/platforms/cc_compiler:qcc",
        "@apex//common/platforms/cc_compiler_version:qcc71",
    ],
)

platform(
    name = "no-compiler-qnx-aarch64",
    constraint_values = [
        "@platforms//os:qnx",
        "@apex//common/platforms/os_distro:qnx71_qos22",
        "@platforms//cpu:aarch64",
        "@apex//common/platforms/cc_compiler:no-compiler",
        "@apex//common/platforms/cc_compiler_version:no-version",
    ],
)
