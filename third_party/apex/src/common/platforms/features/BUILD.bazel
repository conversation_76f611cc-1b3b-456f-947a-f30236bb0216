# ""
package(default_visibility = ["//visibility:public"])

#============================================================
# Vendor constraint: _platform_vendor
#
constraint_setting(
    name = "platform_vendor",
    default_constraint_value = ":undefined_platform_vendor",
)

constraint_value(
    name = "undefined_platform_vendor",
    constraint_setting = ":platform_vendor",
)

constraint_value(
    name = "nvidia",
    constraint_setting = ":platform_vendor",
)

constraint_value(
    name = "yocto",
    constraint_setting = ":platform_vendor",
)

constraint_value(
    name = "driveos",
    constraint_setting = ":platform_vendor",
)
#
#============================================================
