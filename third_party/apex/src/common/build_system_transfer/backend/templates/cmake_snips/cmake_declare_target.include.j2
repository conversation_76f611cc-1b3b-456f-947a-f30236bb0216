
#
# {{target.kind|lower}} Target {{target.name}}
#
{% if target.kind == "LIBRARY" -%}
add_library({{target.name}}{% if generator_mode %} SHARED{% endif%}
{% for src in target.srcs -%}
{##}    {{src}}
{% endfor -%}
)
{% elif target.kind == "INTERFACE_LIBRARY" -%}
add_library({{target.name}}
{##}    INTERFACE
)
{% if target.name == "headers" %}
target_include_directories({{target.name}}
  INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/__repo_root__>
    $<INSTALL_INTERFACE:include/${PROJECT_NAME}/__repo_root__>
    ${BOOST_ALL_INCLUDE_PATHS}
    ${BOOST_ALL_INSTALL_PATHS}
)
{% endif %}
{% elif target.kind == "FILES_LIBRARY" -%}
add_library({{target.name}}
{##}    INTERFACE
{% for src in target.srcs -%}
{##}        ${CMAKE_CURRENT_SOURCE_DIR}/{{src}}
{% endfor -%} 
)
{% elif target.kind == "EXECUTABLE" -%}
add_executable({{target.name}}
{% for src in target.srcs -%}
{##}    {{src}}
{% endfor -%} 
)
{% elif target.kind == "MODULE" or target.kind == "PYMODULE" -%}
add_library({{target.name}} MODULE
{% for src in target.srcs -%}
{##}    {{src}}
{% endfor -%}
)
{% endif -%} {# if target.kind == "LIBRARY" #}

{% if target.alwayslink %}
if(QNX)
        target_link_options({{target.name}}
        PRIVATE
                "LINKER:--as-needed"
        )
else()
        target_link_options({{target.name}}
        PRIVATE
                "LINKER:--no-as-needed"
        )
endif()
{% else %}
{% endif %}

{% if target.alwayslink %}
if(QNX)
        target_link_options({{target.name}}
        PRIVATE
                "LINKER:--as-needed"
        )
else()
        target_link_options({{target.name}}
        PRIVATE
                "LINKER:--no-as-needed"
        )
endif()
{% else %}
{% endif %}

{% if target.kind == "EXECUTABLE" or target.kind == "MODULE" or target.kind == "PYMODULE" -%}
{% endif %}

{% if target.kind == "EXECUTABLE" or target.kind == "LIBRARY" or target.kind == "MODULE" or target.kind == "PYMODULE"  -%}
set_target_properties({{target.name}} 
    PROPERTIES
        OUTPUT_NAME "{{target.output_name}}"
        PREFIX ""
        SUFFIX ""
{%- if target.kind == "PYMODULE" %}
        LIBRARY_OUTPUT_DIRECTORY lib/python${Python_VERSION_MAJOR}.${Python_VERSION_MINOR}/site-packages
        ARCHIVE_OUTPUT_DIRECTORY lib/python${Python_VERSION_MAJOR}.${Python_VERSION_MINOR}/site-packages
        RUNTIME_OUTPUT_DIRECTORY lib/python${Python_VERSION_MAJOR}.${Python_VERSION_MINOR}/site-packages
{%- endif %}
        VERSION "${PROJECT_VERSION}"
        SOVERSION "${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}"
)
{%- endif %} {# if target.kind == "EXECUTABLE" or ... #}

{% if target.include_directories|length > 0 -%}
target_include_directories({{target.name}}
{% if target.srcs|length > 0 -%}
{##}    PUBLIC
{% else -%}
{##}    INTERFACE
{% endif -%}
{% for include in target.include_directories -%}
{##}        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}{{"" if include == "" else "/" + include}}>
{% if colcon_mode %}
{##}        $<INSTALL_INTERFACE:{{join_paths('include/${PROJECT_NAME}', include)}}>
{% else %}
{##}        $<INSTALL_INTERFACE:include/{{include}}>
{% endif -%}
{% endfor -%}
)
{% endif -%} {# if target.include_directories|length > 0 #}

{% if target.kind == "FILES_LIBRARY" -%}
target_include_directories({{target.name}}
{##}    INTERFACE
{##}        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/__repo_root__>
)
{% endif -%} {# if target.kind == "FILES_LIBRARY" #}

{% if target.defines|length > 0 or target.local_defines|length > 0-%}
target_compile_definitions({{target.name}}
{% if target.defines|length > 0 -%}
{% if target.srcs|length > 0 -%}
{##}    PUBLIC
{% else -%}
{{-debug_print("Copts found in header-only target " + target.name)-}}
{##}    INTERFACE
{% endif -%}
{% for define in target.defines -%}
{##}        {{define}}
{% endfor -%}
{% endif -%}
{% if target.local_defines|length > 0 -%}
{##}    PRIVATE
{% for define in target.local_defines -%}
{##}        {{define}}
{% endfor -%} 
{% endif -%}
)
{% endif -%} {# if target.defines|length > 0 or ... #}

{% if target.copts|length > 0-%}
{% if target.srcs|length == 0 -%}
{{fail("In target {}: since copts are private to a target in Bazel, they do not make sense for a header-only library".format(target.name))}}
{% endif -%}
target_compile_options({{target.name}}
{% if target.name == "apex__ida__base__nucleus__nucleus" -%}
{##}    PUBLIC
{% else -%}
{##}    PRIVATE
{% endif -%}
{% for copt in target.copts -%}
{##}        {{copt}}
{% endfor -%}
)
{% endif -%} {# if target.copts|length > 0 #}

{% if colcon_mode %}
{# Next 5 lines workaround missing python detection #}
{% for link_library in target.linklibs + target.link_libraries + target.priv_link_libraries -%}
{% if link_library == "Python::Python" %}
if(NOT QNX)
        find_package (Python REQUIRED COMPONENTS Interpreter Development)
endif()
{% endif -%}
{% endfor -%}
{% endif %} {# if colcon_mode #}

{% if target.link_libraries|length > 0 or target.priv_link_libraries|length > 0 -%}
target_link_libraries({{target.name}}
{% if target.srcs|length > 0 -%}
{##}    PUBLIC
{% else -%}
{##}    INTERFACE
{% endif -%}
{% for link_library in target.link_libraries -%}
{##}        {{link_library}}
{% endfor -%}
{% if target.priv_link_libraries|length > 0 -%}
{##}    PRIVATE
{% for link_library in target.priv_link_libraries -%}
{##}        {{link_library}}
{% endfor -%}
{% endif -%}
)
{% endif -%} {# if target.link_libraries|length > 0 or ... #}

{% if target.linkopts|length > 0-%}
target_link_options({{target.name}}
{% if target.srcs|length > 0 -%}
{##}    PUBLIC
        "LINKER:--as-needed"
{% else -%}
{##}    INTERFACE
        "LINKER:--as-needed"
{% endif -%}
{% for linkopt in target.linkopts -%}
{##}        {{linkopt}}
{% endfor -%}
)
{% endif -%} {# if target.linkopts|length > 0 #}

{% if target.linklibs|length > 0-%}
target_link_libraries({{target.name}}
{% if target.srcs|length > 0 -%}
{##}    PUBLIC
{% else -%}
{##}    INTERFACE
{% endif -%}
{% for linklib in target.linklibs -%}
{##}        {{linklib}}
{% endfor -%}
)
{% endif -%} {# if target.linklibs|length > 0 #}
