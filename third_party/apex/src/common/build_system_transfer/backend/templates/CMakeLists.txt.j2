{% if package is not none -%}
#
# Project: {{package.name}}
#
cmake_minimum_required(VERSION {{cmake_minimum_required_version}})
project({{package.name}} VERSION {{package.version}})
{% endif -%}

{% if colcon_mode or package is not none %}
{% include 'cmake_snips/cmake_set_and_check.cmake.j2' %}
{% endif %}

{% if colcon_mode %}
if (NOT DEFINED BUILD_SHARED_LIBS)
    set(BUILD_SHARED_LIBS TRUE)
endif()
{% endif %}

{% if colcon_mode %}
find_package(ament_cmake REQUIRED)
{% endif %}

{% if package is not none -%}
{% if bitbake_mode %}
{% if package.name != "googletest" %}
if (NOT DEFINED BUILD_SHARED_LIBS)
    set(BUILD_SHARED_LIBS TRUE)
endif()
{% endif %}
{% endif -%}

{% endif -%}

{% if not colcon_mode %}
include(GNUInstallDirs)
{% endif %}

{% include 'cmake_snips/cmake_export.package.requirements.j2' %}


{% if package is not none and package.is_ament_python %}
if (NOT Python_FOUND)
    find_package (Python REQUIRED COMPONENTS Interpreter Development) 
endif()
set(_site_packages_dir "${CMAKE_CURRENT_BINARY_DIR}/site-packages")
set(_pip_command "${Python_EXECUTABLE};-m;pip;install;--target=${_site_packages_dir};.")
add_custom_command(
    COMMAND ${CMAKE_COMMAND};-E;echo;${_pip_command}
    COMMAND ${_pip_command}
    COMMAND ${CMAKE_COMMAND};-E;touch;${_site_packages_dir}_installation_marker
    COMMAND_EXPAND_LISTS
    OUTPUT  ${_site_packages_dir}_installation_marker
    DEPENDS setup.py
    WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
)
add_custom_target(
    pip_install ALL
    DEPENDS ${_site_packages_dir}_installation_marker
)
install(DIRECTORY "${_site_packages_dir}" DESTINATION "lib/python${Python_VERSION_MAJOR}.${Python_VERSION_MINOR}")
{% endif %}


{% if not colcon_mode %}
{% if package is not none -%}
{% if package.share_data -%}
install(DIRECTORY _share_data/ DESTINATION "share/{{package.name}}" USE_SOURCE_PERMISSIONS)
{% endif -%}

{% if package.config_extras -%}
install(FILES "{{package.config_extras|join(' ')}}" DESTINATION "share/{{package.name}}/cmake")
{% endif -%}
{% endif -%}
{% endif %}
{% if package is not none and package.name == "boost_vendor" %}
{% include 'cmake_snips/cmake_boost_includes.j2' %}
{% endif %}
{% for target in targets -%}
{% include 'cmake_snips/cmake_declare_target.include.j2' %}
{% endfor -%}

{% include 'cmake_snips/cmake_install.common.j2' %}
{% if colcon_mode %}
{% include 'cmake_snips/cmake_install.colcon.j2' %}
{% else %} {# colcon_mode #}
{% include 'cmake_snips/cmake_install.default.j2' %}
{% endif %} {# colcon_mode #}
{% if generator_mode %}
set({{package.name}}_GENERATED_LIBRARIES "{{ cc_targets|map(attribute='name')|join(';') }}" PARENT_SCOPE)
set({{package.name}}_BUILD_DEPENDENCIES "{{ package.build_dependencies|map(attribute='name')|reject('equalto', 'python_vendor')|join(';') }}" PARENT_SCOPE)
{% endif -%}