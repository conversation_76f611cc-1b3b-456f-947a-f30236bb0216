SUMMARY = "{{package.name}}"
DESCRIPTION = "{{package.description | replace("\n", " ")}}"
LICENSE = "CLOSED"
{% set package_tar_file = package.yocto_pkg_name + "-" + package.version + ".tar.gz" %}
SRC_URI = "file://{{package_tar_file}}"
SRC_URI[sha256sum] = "{{tar_sha256sum}}"
{% if package.name == "apex_generator_ws" %}
DEPENDS += " python3-native python3-pip-native bazel-native openjdk-22-native "

export JAVA_HOME="${STAGING_LIBDIR_NATIVE}/jvm/openjdk-22-native"
WORKDIR = "${TMPDIR}/work-shared/${PN}"
BAZEL_DIR ??= "${WORKDIR}/bazel"
BAZEL_OUTPUTUSERROOT_DIR ??= "${BAZEL_DIR}/output_user_root"
BAZEL_OUTPUTBASE_DIR ??= "${BAZEL_DIR}/output_base"
BAZEL_DISKCACHE_DIR ??= "${BAZEL_DIR}/disk_cache"
export BAZEL_FLAGS = "--output_user_root=${BAZEL_OUTPUTUSERROOT_DIR} --output_base=${BAZEL_OUTPUTBASE_DIR} --batch"
export BAZEL_CMD_FLAGS = "--disk_cache=${BAZEL_DISKCACHE_DIR} --verbose_failures --spawn_strategy=standalone --genrule_strategy=standalone --subcommands"
SSTATE_SKIP_CREATION = "1"

do_install[network] = "1"

do_install:append() {
    unset CC CXX CPP CCLD FC # Might interfere with Bazel
    cp -a ${WORKDIR}/{{package.yocto_pkg_name}}-{{package.version}}/_share_data ${WORKDIR}/minimal_user_ws
    cd ${WORKDIR}/minimal_user_ws
    bazel ${BAZEL_FLAGS} build ${BAZEL_CMD_FLAGS} @apex//tools/thirdparty/python_vendor/... @pypi__pip//...
}

inherit native
{% else %}
{%- if bitbake_depends|length > 0 %}
DEPENDS = " \
{% for dependency in bitbake_depends -%}
{##}    {{dependency}} \
{% endfor -%}
"
{% endif %}
FILES:${PN} += "*"
{%- set msgs_check = namespace(needed=false) -%}
{%- set inherit_from = namespace(classes=[]) -%}
{%- if package.has_setup_py or package.requires_python or package.deps_require_python -%}
{% set inherit_from.classes = inherit_from.classes + ["setuptools3"] %}
{%- endif -%}
{%- if package.has_cmake -%}
{% set inherit_from.classes = inherit_from.classes + ["cmake"] %}
{%- endif -%}
{%- set apex_codegen_check = namespace(needed=false) -%}
{%- for build_dependency in package.build_dependencies  -%}
{%- if build_dependency.name == "codegen" -%}
{%- set apex_codegen_check.needed = true -%}
{%- endif -%}
{%- if build_dependency.yocto_pkg_name == "apex-rosidl-runtime-cpp" -%}
{%- set msgs_check.needed = true -%}
{%- endif -%}
{%- endfor -%}
{%- if apex_codegen_check.needed -%}
{% set inherit_from.classes = inherit_from.classes + ["apex-codegen"] %}
{%- endif -%}
{%- if package.native_only -%}
{% set inherit_from.classes = inherit_from.classes + ["native"] %}
{%- endif -%}
{%- if msgs_check.needed %}
PARALLEL_MAKE = "-j 4"
{% endif -%}
{% if inherit_from.classes|length > 0 %}
inherit{% for cls in inherit_from.classes %} {{cls}}{% endfor %}
{%- endif %}
{% if package.name == "examples_rclpy_minimal_client" %}
# To avoid conflicting binary name
do_install:append() {
    mv ${D}${bindir}/client ${D}${bindir}/client_rclpy_minimal
}
{%- endif %}
# To avoid adding x86_64 from runfiles
do_install:append() {
    find ${D} -type d -name "*-linux-x86_64" -exec rm -rf {} +
}
{% endif %}
