/// \copyright Copyright 2017-2020 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Defines make_unique() and make_shared() which work with allocators

#ifndef ALLOCATOR__MEMORY_HPP_
#define ALLOCATOR__MEMORY_HPP_

#include <allocator/pool_allocator.hpp>
#include <allocator/traits/allocation_traits.hpp>
#include <allocator/details/size_traits_import.hpp>

#include <memory>
#include <type_traits>
#include <utility>
#include <tuple>

/// \namespace apex
namespace apex
{
/// \namespace apex::allocator
namespace allocator
{

/// \class allocator_deleter
/// An allocator-aware deleter for smart pointers
/// Base version for single values
/// \tparam T the type of the object to delete
/// \tparam Alloc Allocator type
template<typename T, typename Alloc>
class allocator_deleter
{
public:
  /// \brief Constructs a deleter
  /// \param alloc The allocator to use on cleanup
  /// \cert
  /// \deterministic
  explicit allocator_deleter(Alloc alloc)
  : m_alloc{alloc}
  {}

  /// \brief Functor operation
  /// \param object The pointer to the object to delete
  /// \cert
  /// \deterministic if deallocating with Alloc and destructor of T are deterministic.
  void operator()(T * object)
  {
    m_alloc.delete_object(object);
  }

private:
  Alloc m_alloc;
};

/// \class allocator_deleter
/// An allocator-aware deleter for smart pointers
/// Specialization for arrays
/// \tparam T the type of the object to delete
/// \tparam Alloc Allocator type
template<typename T, typename Alloc>
class allocator_deleter<T[], Alloc>
{
public:
  /// \brief Constructs a deleter
  /// \param alloc The allocator to use on cleanup
  /// \param size Number of element in the array to be deleted
  /// \cert
  /// \deterministic
  explicit allocator_deleter(Alloc alloc, std::size_t size)
  : m_alloc{alloc},
    m_size{size}
  {}

  /// \brief Functor operation
  /// \param object The pointer to the object array to delete
  /// \cert
  /// \deterministic if deallocating with Alloc and destructor of T are deterministic.
  void operator()(T * object) noexcept
  {
    m_alloc.delete_object_array(m_size, object);
  }

private:
  Alloc m_alloc;
  std::size_t m_size;
};

/// \class allocator_deleter
/// The specialization which prevents instantiations for arrays with the known extent (static types)
/// \tparam T the type of the array
/// \tparam n The static size of the array
/// \tparam Alloc Allocator type
template<typename T, std::size_t n, typename Alloc>
class allocator_deleter<T[n], Alloc>
{
public:
  /// \brief Functor operation explicitly deleted for static array types
  void operator()(T *) noexcept = delete;  // NOLINT  false-positive
};

/// \brief A unique_ptr type which uses a allocator_deleter
/// \tparam T The tracked type
template<class T, class Alloc>
using unique_ptr = std::unique_ptr<T, allocator_deleter<T, Alloc>>;

/// \namespace apex::allocator::memory_pool::details
namespace details
{

/// \brief A type trait for checking whether a type has no array extent
/// \tparam T the type to check
template<typename T>
struct no_extent : std::true_type {};

/// \brief A type trait for checking whether a type has no array extent
/// A specialization for unknown array extent
/// \tparam T the type to check
template<typename T>
struct no_extent<T[]>: std::false_type {};

/// \brief A type trait for checking whether a type has no array extent
/// A specialization for known array extent
/// \tparam T the type to check
/// \tparam n The array extent
template<typename T, std::size_t n>
struct no_extent<T[n]>: std::false_type {};

/// \brief A type trait for checking whether a type has an unknown array extent
/// \tparam T the type to check
template<typename T>
struct unknown_extent : std::false_type {};

/// \brief A type trait for checking whether a type has an unknown array extent
/// \tparam T the type to check
/// A specialization for unknown array extent
template<typename T>
struct unknown_extent<T[]>: std::true_type {};

/// \brief A type trait for checking whether a type has an unknown array extent
/// A specialization for known array extent
/// \tparam T the type to check
/// \tparam n The array extent
template<typename T, std::size_t n>
struct unknown_extent<T[n]>: std::false_type {};

/// \brief A concept for providing a corresponding unique_ptr type if the type is not an array
/// \tparam T a type to check and provide a unique_ptr for on success
/// \tparam Alloc Allocator type
template<typename T, typename Alloc>
using enable_for_unique_single_t = std::enable_if_t<no_extent<T>::value, unique_ptr<T, Alloc>>;

/// \brief A concept for providing a corresponding unique_ptr type if the type
/// is an array with unknown extent
/// \tparam T a type to check and provide a unique_ptr for on success
/// \tparam Alloc Allocator type
template<typename T, typename Alloc>
using enable_for_unique_unknown_extent_t =
  std::enable_if_t<unknown_extent<T>::value, unique_ptr<T, Alloc>>;

/// \brief A concept for providing a corresponding shared_ptr type if the type is not an array
/// \tparam T a type to check and provide a shared_ptr for on success
template<typename T>
using enable_for_shared_single_t = std::enable_if_t<no_extent<T>::value, std::shared_ptr<T>>;

/// \brief A concept for providing a corresponding shared_ptr type if the type
/// is an array with unknown extent
/// \tparam T a type to check and provide a shared_ptr for on success
template<typename T>
using enable_for_shared_unknown_extent_t = std::enable_if_t<unknown_extent<T>::value,
    std::shared_ptr<std::remove_extent_t<T>>>;

}  // namespace details

/// \brief Creates a unique_ptr which is managed through an instance of an allocator
/// Participates only if T is not an array
/// \tparam T the type to allocate and manage by unqiue_ptr
/// \tparam Args The constructor argument types for type T
/// \tparam Alloc Allocator type
/// \param alloc An allocator for memory management
/// \param args The constructor arguments for an object of type T
/// \return A new unique_ptr which manages a new object of type T via provided allocator
/// \cert
/// \deterministic if allocating with Alloc and constructor for T are deterministic.
// return type is not dependent on the type of any parameter
template<typename T, typename Alloc, typename ... Args>
details::enable_for_unique_single_t<T, Alloc>
make_unique(Alloc alloc, Args && ... args)
{
  return unique_ptr<T, Alloc>{alloc.template new_object<T>(std::forward<Args>(args)...),
    allocator_deleter<T, Alloc>{alloc}};
}

/// \brief Creates a unique_ptr which is managed through an instance of an allocator
/// Participates only if T is an array of unknown extent
/// \tparam T the type of array to allocate and manage by unqiue_ptr
/// \tparam Alloc Allocator type
/// \param alloc An allocator for memory management
/// \param n The number of elements in the array of type T
/// \return A new unique_ptr which manages a new array of objects of type T via provided allocator
/// \cert
/// \deterministic if allocating with Alloc and constructor for T are deterministic.
// return type is not dependent on the type of any parameter
template<typename T, typename Alloc>
details::enable_for_unique_unknown_extent_t<T, Alloc>
make_unique(Alloc alloc, size_t n)
{
  using U = std::remove_extent_t<T>;
  return unique_ptr<T, Alloc>(alloc.template new_object_array<U>(
             n), allocator_deleter<T, Alloc>{alloc, n});
}

/// \brief Creates a shared_ptr and its control block
/// which are managed through an instance of an allocator
/// Participates only if T is not an array
/// \tparam T the type to allocate and manage by shared_ptr
/// \tparam Args The constructor argument types for type T
/// \tparam Alloc Allocator type
/// \param alloc An allocator for memory management
/// \param args The constructor arguments for an object of type T
/// \return A new shared_ptr which manages a new object of type T via provided allocator
/// \cert
/// \deterministic if allocating with Alloc and constructor for T are deterministic.
// return type is not dependent on the type of any parameter
template<typename T, typename Alloc, typename ... Args>
details::enable_for_shared_single_t<T>
make_shared(Alloc alloc, Args && ... args)
{
  return std::allocate_shared<T>(alloc, std::forward<Args>(args)...);
}

/// \brief Creates a shared_ptr which is managed through an instance of an allocator
/// Participates only if T is an array of unknown extent
/// \tparam T the type of array to allocate and manage by shared_ptr
/// \tparam Alloc Allocator type
/// \param alloc An allocator for memory management
/// \param n The number of elements in the array of type T
/// \return A new shared_ptr which manages a new array of objects of type T via provided allocator
///
/// Note: Since C++14 has no std::allocate_shared() for array types, there is now way to allocate
/// std::shared_ptr along with the control block so it falls back to two separate allocations
/// \cert
/// \deterministic if allocating with Alloc and constructor for T are deterministic.
// return type is not dependent on the type of any parameter
template<typename T, typename Alloc>
details::enable_for_shared_unknown_extent_t<T>
make_shared(Alloc alloc, size_t n)
{
  using U = std::remove_extent_t<T>;
  return std::shared_ptr<U>(
    alloc.template new_object_array<U>(n), allocator_deleter<T, Alloc>{alloc, n}, alloc
  );
}

/*
 AXIVION Next Construct MisraC++2023-4.1.3: Reason: Code Quality (Functional suitability),
 Justification: It's an empty class for shared_ptr helpers.
 */
/// \brief Base class for shared_ptr helpers
struct memory_query_tag {};

/// \brief Helper for std::allocate_shared
/// Defines allocation traits of control block and data
/// \tparam T The type pointer points to
/// \tparam Alloc Allocator used for shared_ptr
template<typename T, typename Alloc>
struct query_shared_ptr_for_make_shared_inplace : memory_query_tag
{
  using type = std::tuple<pmr::bucket_properties<pmr::shared_ptr_inplace_size<T, Alloc>::value,
      pmr::shared_ptr_inplace_align<T, Alloc>::value>>;
};

/// \brief Helper for regular std::shared_ptr
/// Defines allocation traits of control block only
/// \tparam T The type pointer points to
/// \tparam Deleter The deleter type
/// \tparam Alloc Allocator used for shared_ptr
template<typename T, typename Deleter, typename Alloc>
struct query_shared_ptr : memory_query_tag
{
  using type = std::tuple<pmr::bucket_properties<pmr::shared_ptr_deleter_size<T, Deleter,
      Alloc>::value,
      pmr::shared_ptr_deleter_align<T, Deleter, Alloc>::value>>;
};

/// \brief Helper for make_shared<T[]>()
/// Defines allocation traits of control block and data
/// \tparam T The type pointer points to
/// \tparam Alloc Allocator used for shared_ptr
template<typename T, typename Alloc>
struct query_shared_ptr_for_make_shared_array : memory_query_tag
{
  using Deleter = allocator_deleter<T, Alloc>;
  using type = std::tuple<
    pmr::bucket_properties<
      pmr::shared_ptr_deleter_size<T, Deleter, Alloc>::value,
      pmr::shared_ptr_deleter_align<T, Deleter, Alloc>::value
    >,
    pmr::bucket_properties<sizeof(T), alignof(T)>
  >;
};

namespace pmr
{
/// \brief A specialization adapter for memory_query_tag derived traits
/// \tparam T The value type
template<typename T>
struct query_allocations<T, std::enable_if_t<std::is_base_of<memory_query_tag, T>::value>>
{
  using type = typename T::type;
};
}

}  // namespace allocator
}  // namespace apex

#endif  // ALLOCATOR__MEMORY_HPP_
