/// \copyright Copyright 2018 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief A memory static vector.

#ifndef CONTAINERS__STATIC_VECTOR_HPP_
#define CONTAINERS__STATIC_VECTOR_HPP_

#include <algorithm>
#include <memory>
#include <utility>
#include <vector>

#include <cpputils/common_exceptions.hpp>
namespace apex
{
namespace vector_impl__
{

/**
 * An allocator which can only be used to allocate memory once.
 * \tparam U The type to allocate.
 */
template <class U>
class single_allocator
{
public:
  using base_type = std::allocator<U>;
  using value_type = U;

  using propagate_on_container_copy_assignment = std::true_type;  // for consistency
  using propagate_on_container_move_assignment = std::true_type;  // to avoid the optimization
  using propagate_on_container_swap = std::true_type;  // to avoid the undefined behavior

  /**
   * Constructor
   * \param n The maximum number of allocatable elements.
   */
  /// \cert
  /// \deterministic
  explicit single_allocator(const typename base_type::size_type n) : m_size(n), m_consumed(false)
  {
    // Checking that we are not trying to allocate more than the base allocator supports.
    if (n > m_alloc.max_size()) {
      throw apex::bad_alloc();
    }
  }

  /// Copy constructor.
  /// \cert
  /// \deterministic
  single_allocator(const single_allocator<U> & other)  // NOLINT #2459
  : m_size(other.m_size), m_consumed(other.m_consumed), m_alloc(other.m_alloc){};

  /// Copy assignment operator.
  /// \cert
  /// \deterministic
  /*
   AXIVION DISABLE STYLE MisraC++2023-15.8.1: Self assignment is OK since self-assignment of
   the primitive data members in the class is safe and has no side-effect and the std::allocator
   type member can handle self-assignment correctly.
   */
  /*lint -e{1529}*/  // See the above AXIVION justification.
  single_allocator<U> & operator=(const vector_impl__::single_allocator<U> & other) &
  {
    m_size = other.m_size;
    m_consumed = other.m_consumed;
    m_alloc = other.m_alloc;
    return *this;
  }

  /// Move constructor.
  /// \cert
  /// \deterministic
  single_allocator(single_allocator<U> && other) noexcept
  : m_size(other.m_size), m_consumed(other.m_consumed), m_alloc(std::move(other.m_alloc))
  {
  }

  /// \brief Default destructor
  virtual ~single_allocator() = default;

  /// Move assignment.
  /// \cert
  /// \deterministic
  /*lint -e{1529}*/  // Self assignment is OK since self-assignment of the primitive data members
  // in the class is safe and has no side-effect and the std::allocator type member can handle
  // self-assignment correctly.
  single_allocator<U> & operator=(single_allocator<U> && other) & noexcept
  {
    m_size = other.m_size;
    m_consumed = other.m_consumed;
    m_alloc = std::move(other.m_alloc);
    return *this;
  }

  /**
   * Allocates memory. This special allocator imposes the following constraints:
   * * Memory can only allocated once, subsequent attempts throw std::bad_alloc.
   * * The number allocated must match the size specified when constructed the allocator.
   * \param n The number of elements to allocate memory for.
   * \param hint Pointer to nearby memory location.
   * \return Pointer to the first byte of a memory block suitably aligned and sufficient to hold
   *         an array of n objects of type T.
   * \throw std::bad_alloc if there there is no memory available.
   */
  /// \cert
  /*lint -e{910}*/  // Conversion of NULL to pointer
  typename base_type::pointer allocate(const typename base_type::size_type n, const void * hint = 0)
  {
    if ((n != m_size) || m_consumed) {
      throw std::bad_alloc();
    }
    // Ensuring exception safety by getting the memory before marking the allocator as used.
    auto && ret = m_alloc.allocate(n, hint);
    m_consumed = true;
    return ret;
  }

  /**
   * Deallocates memory.
   * \param ptr Pointer to the memory to deallocate.
   * \param n Number of elements to deallocate.
   */
  /// \cert
  void deallocate(typename base_type::pointer ptr, typename base_type::size_type n)
  {
    m_alloc.deallocate(ptr, n);
  }

  /// The maximum number of elements allocatable.
  /// \cert
  /// \deterministic
  typename base_type::size_type max_size() const
  {
    return m_size;
  }

private:
  typename base_type::size_type m_size;
  bool m_consumed;
  base_type m_alloc;
};  // vector_impl__::single_allocator

/// Equal operator
/// \cert
/// \deterministic
template <typename T, typename U>
inline bool operator==(const single_allocator<T> &, const single_allocator<U> &)
{
  // Memory fom a vector_impl__::single_allocator can always be deallocated by a different
  // single_allocator.
  return true;
}

/**
 * Non-equal operator.
 * This operator function should return false not !(a == b).
 * Because The MC/DC coverage over 90% cannot be reached out.
 * False condition of (a == b) cannot be tested.
 */
/// \cert
/// \deterministic
template <typename T, typename U>
inline bool operator!=(const single_allocator<T> &, const single_allocator<U> &)
{
  return false;
}

}  // namespace vector_impl__

/**
 * Macro which generates noexcept specifier depending on the static vector internal function
 * \param name Member function to use.
 */
#define APEX_STATIC_VECTOR_INTERNAL_HAS_NOEXCEPT(name) \
  noexcept(noexcept(std::declval<vector_type>().name()))

/**
 * Macro which generates perfect forwarding function without parameters.
 * \param member Member name to use.
 * \param name Member function to use.
 */
/// \cert
/// \deterministic
/*
 AXIVION DISABLE STYLE MisraC++2023-19.3.4: Reason: Code Quality (Functional suitability),
 Justification: It's necessary to not quote parameters
 */
#define APEX_STATIC_VECTOR_FWD(member, name)                                 \
  decltype(auto) name() APEX_STATIC_VECTOR_INTERNAL_HAS_NOEXCEPT(name)       \
  {                                                                          \
    return member.name();                                                    \
  }                                                                          \
  decltype(auto) name() const APEX_STATIC_VECTOR_INTERNAL_HAS_NOEXCEPT(name) \
  {                                                                          \
    return member.name();                                                    \
  }

/**
 * Macro which generates perfect forwarding function with parameters.
 * \param member Member name to use.
 * \param name Member function to use.
 */
/// \cert
/// \deterministic
#define APEX_STATIC_VECTOR_FWD_PARAM(member, name)       \
  template <class... Params>                             \
  decltype(auto) name(Params &&... params)               \
  {                                                      \
    return member.name(std::forward<Params>(params)...); \
  }                                                      \
  template <class... Params>                             \
  decltype(auto) name(Params &&... params) const         \
  {                                                      \
    return member.name(std::forward<Params>(params)...); \
  }

/**
 * Macro which generates only perfect forwarding non-const member function without parameters.
 * \param member Member name to use.
 * \param name Member function to use.
 */
/// \cert
/// \deterministic
#define APEX_STATIC_VECTOR_FWD_NON_CONST(member, name)                 \
  decltype(auto) name() APEX_STATIC_VECTOR_INTERNAL_HAS_NOEXCEPT(name) \
  {                                                                    \
    return member.name();                                              \
  }

/**
 * Macro which generates only perfect forwarding non-const member function with parameters.
 * \param member Member name to use.
 * \param name Member function to use.
 */
/// \cert
/// \deterministic
#define APEX_STATIC_VECTOR_FWD_PARAM_NON_CONST(member, name) \
  template <class... Params>                                 \
  decltype(auto) name(Params &&... params)                   \
  {                                                          \
    return member.name(std::forward<Params>(params)...);     \
  }

/**
 * Helper macro to forward definitions.
 * /param type The type to get the definition from.
 * /param name The definition inside the type that should be forwarded.
 */
#define APEX_STATIC_VECTOR_DEFINITIONS_GEN(type, name) using name = typename type::name
// AXIVION ENABLE STYLE MisraC++2023-19.3.4

/*
 AXIVION Next Construct MisraC++2023-15.1.5: Reason: Code Quality (Functional suitability),
 Justification: The constructor taking multiple values is unique.
 */
/**
 * Static vector which only allocated memory on construction. Copying this vector is not possible
 * as this would require additional memory.
 * \tparam T Type to store in the vector.
 */
template <class T>
class static_vector
{
private:
  using vector_type = std::vector<T, vector_impl__::single_allocator<T>>;
  std::vector<T, vector_impl__::single_allocator<T>> m_v;

  /// \brief Checks if an index is inside the storage range and throws if its not. Safety check
  /// used only from at().
  /// \param n The index to check.
  /// \throw apex::out_of_range if the index is out of range.
  void check_index(const typename vector_type::size_type n) const
  {
    if (n >= this->size()) {
      throw apex::out_of_range("Index out of range");
    }
  }

public:
  APEX_STATIC_VECTOR_DEFINITIONS_GEN(vector_type, value_type);
  APEX_STATIC_VECTOR_DEFINITIONS_GEN(vector_type, allocator_type);
  APEX_STATIC_VECTOR_DEFINITIONS_GEN(vector_type, reference);
  APEX_STATIC_VECTOR_DEFINITIONS_GEN(vector_type, const_reference);
  APEX_STATIC_VECTOR_DEFINITIONS_GEN(vector_type, pointer);
  APEX_STATIC_VECTOR_DEFINITIONS_GEN(vector_type, const_pointer);
  APEX_STATIC_VECTOR_DEFINITIONS_GEN(vector_type, iterator);
  APEX_STATIC_VECTOR_DEFINITIONS_GEN(vector_type, const_iterator);
  APEX_STATIC_VECTOR_DEFINITIONS_GEN(vector_type, reverse_iterator);
  APEX_STATIC_VECTOR_DEFINITIONS_GEN(vector_type, const_reverse_iterator);
  APEX_STATIC_VECTOR_DEFINITIONS_GEN(vector_type, difference_type);
  APEX_STATIC_VECTOR_DEFINITIONS_GEN(vector_type, size_type);
  /**
   * Creates an empty vector.
   * \param capacity Capacity of the vector meaning the maximum number of elements it can hold.
   */
  /// \cert
  explicit static_vector(const size_type capacity)
  : m_v(vector_impl__::single_allocator<T>(capacity))
  {
    m_v.reserve(capacity);
  }

  /**
   * Constructs the container with the contents of the range [first, last).
   * \param capacity Capacity of the vector meaning the maximum number of elements it can hold.
   * \param first Begin of the iterator.
   * \param last End of the iterator.
   * \tparam IT Iterator type.
   */
  /// \cert
  template <class IT>
  explicit static_vector(const size_type capacity, const IT first, const IT last)
  : m_v(vector_impl__::single_allocator<T>(capacity))
  {
    const auto dist = std::distance(first, last);
    if ((dist < 0) || (dist > static_cast<decltype(dist)>(capacity))) {
      throw apex::length_error("[first, last) range error");
    }
    m_v.reserve(capacity);
    // Distance is positive per above condition.
    // It has to be casted because std::distance returns an int type
    // and resize shall receive unsigned int.
    m_v.resize(static_cast<size_type>(dist));
    std::copy(first, last, m_v.begin());
  }
  /**
   * Constructs the container from an initialization list.
   * \param capacity Capacity of the vector meaning the maximum number of elements it can hold.
   * \param il Initialization list to copy to the vector.
   */
  /// \cert
  explicit static_vector(const size_type capacity, std::initializer_list<value_type> il)
  : m_v(vector_impl__::single_allocator<T>(capacity))
  {
    if (il.size() > capacity) {
      throw apex::length_error("initializer list too long");
    }
    m_v.reserve(capacity);
    m_v.resize(il.size());
    std::copy(il.begin(), il.end(), m_v.begin());
  }

  static_vector(const static_vector<T> & other) = delete;

  /// \brief Default destructor
  virtual ~static_vector() = default;

  /// Assignment operator.
  /// \cert
  /*lint -e{1529}*/  // Self assignment is OK since the std::vector type member can handle
  // self-assignment correctly.
  static_vector<T> & operator=(const static_vector<T> & other) &
  {
    m_v = other.m_v;
    return *this;
  }

  /// Move constructor.
  /// \cert
  /// \deterministic
  static_vector(static_vector<T> && other) noexcept : m_v(std::move(other.m_v)) {}

  /// Move assignment.
  /// \cert
  /// Note. non-deterministic, because move assignment triggering memory deallocation in the
  /// destructor of the original mv object.
  /*lint -e{1529}*/  // Self assignment is OK since the std::vector type member can handle
  // self-assignment correctly.
  static_vector<T> & operator=(static_vector<T> && other) & noexcept
  {
    m_v = std::move(other.m_v);
    return *this;
  }
  // AXIVION ENABLE STYLE MisraC++2023-15.8.1

  /// Noop. This is just implemented for compatibility reasons.
  /// \cert
  /// \deterministic
  void shrink_to_fit()
  {
    // The C++ standard allows shrink to fit to be a noop which is the case here to prevent any
    // reallocation.
  }

  /**
   * Exchanges the content of the container by the content another vector
   * object of the same type. Sizes may differ.
   * \param x The vector to swap with.
   */
  /// \cert
  /// \deterministic
  void swap(static_vector<T> & x) noexcept
  {
    m_v.swap(x.m_v);
  }

  /// \brief  Provides access to the data contained in the %vector.
  /// \param n The index of the element for which data should be accessed.
  /// \return  Read/write reference to data.
  /// \throw  apex::out_of_range  If @a n is an invalid index.
  /// This function provides for safer data access. The parameter is first checked that it is in
  /// the range of the vector. The function throws apex::out_of_range if the check fails.
  /// \cert
  /// \deterministic
  decltype(auto) at(size_type n)
  {
    check_index(n);
    return m_v[n];
  }

  /// \brief  Provides access to the data contained in the %vector.
  /// \param n The index of the element for which data should be accessed.
  /// \return  Read-only (constant) reference to data.
  /// \throw  apex::out_of_range  If @a n is an invalid index.
  /// This function provides for safer data access. The parameter is first checked that it is in
  /// the range of the vector. The function throws apex::out_of_range if the check fails.
  /// \cert
  /// \deterministic
  decltype(auto) at(size_type n) const
  {
    check_index(n);
    return m_v[n];
  }

  /// \brief Do nothing. Added for interface compatibility with std::vector
  /// \param n
  void reserve(size_type n)
  {
    (void)n;
  }

  /// @brief  Resizes the %vector to the specified number of elements.
  /// @param  new_size  Number of elements the %vector should contain.
  /// This function will %resize the %vector to the specified
  /// number of elements.  If the number is smaller than the
  /// %vector's current size the %vector is truncated, otherwise
  /// default constructed elements are appended.
  /// \cert
  /// \deterministic
  void resize(size_type new_size)
  {
    if (new_size > m_v.capacity()) {
      throw apex::length_error("No space in vector for resize");
    }
    m_v.resize(new_size);
  }

  /// @brief  Add data to the end of the %vector.
  /// @param  x  Data to be added.
  /// This is a typical stack operation.  The function creates an element at the end of the
  /// %vector and assigns the given data to it.  Due to the nature of a %vector this operation
  /// takes constant time.
  /// \cert
  /// \deterministic
  void push_back(const value_type & x)
  {
    if (m_v.size() == m_v.capacity()) {
      throw apex::length_error("No space in vector to add new element");
    }
    m_v.push_back(x);
  }

  /// @brief  Add data to the end of the %vector.
  /// @param  x  Data to be added.
  /// This is a typical stack operation.  The function creates an element at the end of the
  /// %vector and assigns the given data to it.  Due to the nature of a %vector this operation
  /// takes constant time.
  /// \cert
  /// \deterministic
  void push_back(value_type && x)
  {
    if (m_v.size() == m_v.capacity()) {
      throw apex::length_error("No space in vector to add new element");
    }
    m_v.emplace_back(std::move(x));
  }

  APEX_STATIC_VECTOR_FWD(m_v, begin)
  APEX_STATIC_VECTOR_FWD(m_v, end)
  APEX_STATIC_VECTOR_FWD(m_v, rbegin)
  APEX_STATIC_VECTOR_FWD(m_v, rend)
  APEX_STATIC_VECTOR_FWD(m_v, cbegin)
  APEX_STATIC_VECTOR_FWD(m_v, cend)
  APEX_STATIC_VECTOR_FWD(m_v, crbegin)
  APEX_STATIC_VECTOR_FWD(m_v, crend)

  APEX_STATIC_VECTOR_FWD(m_v, size)
  APEX_STATIC_VECTOR_FWD(m_v, max_size)
  APEX_STATIC_VECTOR_FWD(m_v, capacity)
  APEX_STATIC_VECTOR_FWD(m_v, empty)
  APEX_STATIC_VECTOR_FWD_PARAM(m_v, operator[])
  APEX_STATIC_VECTOR_FWD(m_v, front)
  APEX_STATIC_VECTOR_FWD(m_v, back)
  APEX_STATIC_VECTOR_FWD(m_v, data)

  APEX_STATIC_VECTOR_FWD_PARAM_NON_CONST(m_v, assign)
  APEX_STATIC_VECTOR_FWD_NON_CONST(m_v, pop_back)
  APEX_STATIC_VECTOR_FWD_PARAM_NON_CONST(m_v, insert)
  APEX_STATIC_VECTOR_FWD_PARAM_NON_CONST(m_v, erase)
  APEX_STATIC_VECTOR_FWD_NON_CONST(m_v, clear)
  APEX_STATIC_VECTOR_FWD_PARAM_NON_CONST(m_v, emplace)
  APEX_STATIC_VECTOR_FWD_PARAM_NON_CONST(m_v, emplace_back)

  template <class K>
  friend bool operator==(const static_vector<K> & lhs, const static_vector<K> & rhs);
  template <class K>
  friend bool operator!=(const static_vector<K> & lhs, const static_vector<K> & rhs);
  template <class K>
  friend bool operator<(const static_vector<K> & lhs, const static_vector<K> & rhs);
  template <class K>
  friend bool operator<=(const static_vector<K> & lhs, const static_vector<K> & rhs);
  template <class K>
  friend bool operator>(const static_vector<K> & lhs, const static_vector<K> & rhs);
  template <class K>
  friend bool operator>=(const static_vector<K> & lhs, const static_vector<K> & rhs);
};

/**
 * Macro to forward binary operators.
 * /param op Operator to forward.
 * /param member Member to apply the operator on.
 */
/// \cert
/// \deterministic
#define APEX_VECTOR_CMP_OP(op, member)                                         \
  template <class K>                                                           \
  bool operator op(const static_vector<K> & lhs, const static_vector<K> & rhs) \
  {                                                                            \
    return operator op(lhs.member, rhs.member);                                \
  }

APEX_VECTOR_CMP_OP(==, m_v)
APEX_VECTOR_CMP_OP(!=, m_v)
APEX_VECTOR_CMP_OP(<, m_v)  // NOLINT
APEX_VECTOR_CMP_OP(<=, m_v)
APEX_VECTOR_CMP_OP(>, m_v)  // NOLINT
APEX_VECTOR_CMP_OP(>=, m_v)

}  // namespace apex

namespace std
{
/*
 AXIVION Next Construct MisraC++2023-5.10.1:
 It's common to add specialization for swap to std
 */
template <class T>
void swap(apex::static_vector<T> & rhs, apex::static_vector<T> & lhs) noexcept
{
  rhs.swap(lhs);
}
}  // namespace std
#endif  // CONTAINERS__STATIC_VECTOR_HPP_
