load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

apex_cc_library(
    name = "containers",
    srcs = [
        "src/dummy.cpp",
    ],
    hdrs = glob(["include/**/*.hpp"]),
    applicable_licenses = ["//common/integrity:embedded_gold"],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils",
        "@coverage_tool//:coverage_io_lib",
    ],
)

filegroup(
    name = "test_containers_srcs_with_req_ids",
    srcs = [
        "test/containers/test_shared_ptr.cpp",
        "test/containers/test_static_allocators_basic.cpp",
        "test/containers/test_static_allocators_vectors.cpp",
        "test/containers/test_static_queue.cpp",
        "test/containers/test_static_vector_snippets.cpp",
    ],
    visibility = ["//common/containers/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_containers",
    srcs = [":test_containers_srcs_with_req_ids"],
    deps = [
        ":containers",
        "//tools/testing/apex_test_tools",
        "@googletest//:gtest_main",
    ],
)

ros_pkg(
    name = "containers_pkg",
    cc_libraries = [":containers"],
    description = "Containers that allocate only during construction",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = ["//common/cpputils:cpputils_pkg"],
)

filegroup(
    name = "doc_files",
    srcs = [
        "test/containers/test_static_queue.cpp",
        "test/containers/test_static_vector_snippets.cpp",
    ],
    visibility = ["//common/containers/design:__subpackages__"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
