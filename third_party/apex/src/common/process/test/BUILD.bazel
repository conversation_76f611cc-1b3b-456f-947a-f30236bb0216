load("//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")

cc_binary(
    name = "process_tester",
    testonly = True,
    srcs = ["process_tester.cpp"],
    tags = [
        "exclude_sca",
        "skip_coverage",
    ],
)

cc_binary(
    name = "file_descriptor_checker",
    testonly = True,
    srcs = ["file_descriptor_checker.cpp"],
    tags = [
        "exclude_sca",
        "skip_coverage",
    ],
)

apex_cc_test(
    name = "test_ring_buffer",
    srcs = ["test_ring_buffer.cpp"],
    deps = [
        "//common/process",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_process",
    srcs = ["test_process.cpp"],
    data = [
        ":file_descriptor_checker",
        ":process_tester",
    ],
    defines = [
        "TESTER_EXECUTABLE=\\\"$(rootpath :process_tester)\\\"",
        "FD_CHECKER_EXECUTABLE=\\\"$(rootpath :file_descriptor_checker)\\\"",
    ],
    deps = [
        "//common/process",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "process_rt_settings",
    srcs = ["test_process_rt_settings.cpp"],
    data = [
        ":process_tester",
    ],
    defines = [
        "TESTER_EXECUTABLE=\\\"$(rootpath :process_tester)\\\"",
    ],
    deps = [
        "//common/process",
        "@googletest//:gtest_main",
    ],
)
