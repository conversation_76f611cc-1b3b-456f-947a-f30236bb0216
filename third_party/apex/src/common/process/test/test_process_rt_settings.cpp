// Copyright 2023 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>
#include <sys/resource.h>
#include <unistd.h>

#include <chrono>
#include <string>
#include <vector>

#include "apexutils/apex_rt.h"
#include "process/process.hpp"

using apex::process::Process;
using apex::process::StartupConfig;

using namespace std::chrono_literals;
using std::chrono::steady_clock;
using std::chrono::system_clock;

namespace
{
std::bitset<APEX_CPUSET_SIZE> apex_cpu_set_to_bitset(const apex_cpuset_t & cpuset)
{
  std::bitset<APEX_CPUSET_SIZE> bitset;
  for (uint32_t i = 0U; i < bitset.size(); i++) {
    if (apex_cpu_is_set(i, &cpuset)) {
      bitset.set(i, true);
    }
  }
  return bitset;
}

bool system_allows_to_set_realtime_priority()
{
#ifdef APEX_LINUX
  struct rlimit priority_limits;
  const auto ret = getrlimit(RLIMIT_RTPRIO, &priority_limits);
  return ((ret == 0) && (priority_limits.rlim_cur > 0));
#endif  // APEX_LINUX

#ifdef APEX_QNX
  // On QNX unprivileged threads can have a priority ranging from 1 to 63
  // there is no need for special permissions without exceeding that range
  return true;
#endif  // APEX_QNX

  // Default case for unsupported platforms
  return false;
}

}  // namespace

TEST(process_rt_settings, sched_fifo)
{
  if (!system_allows_to_set_realtime_priority()) {
    GTEST_SKIP() << "This test requires privileges to set real-time priority";
  }

  int32_t fifo_priority_min = apex_get_os_priority_min(SCHED_FIFO);
  ASSERT_GE(fifo_priority_min, 1);

  StartupConfig config(TESTER_EXECUTABLE);
  config.set_priority(fifo_priority_min);
  config.set_scheduling_policy(apex::threading::scheduler::fifo);
  Process proc("process1", config);

  proc.start();

  struct sched_param param;
  sched_getparam(proc.get_pid(), &param);

  // check rt parameters are set correctly
  EXPECT_EQ(sched_getscheduler(proc.get_pid()), SCHED_FIFO);
  EXPECT_EQ(param.sched_priority, fifo_priority_min);

  proc.stop_and_collect_exit_code(2s);
}

TEST(process_rt_settings, sched_round_robin)
{
  if (!system_allows_to_set_realtime_priority()) {
    GTEST_SKIP() << "This test requires privileges to set real-time priority";
  }

  int32_t rr_priority_min = apex_get_os_priority_min(SCHED_RR);
  ASSERT_GE(rr_priority_min, 1);

  StartupConfig config(TESTER_EXECUTABLE);
  config.set_priority(rr_priority_min);
  config.set_scheduling_policy(apex::threading::scheduler::round_robin);
  Process proc("process1", config);

  proc.start();

  struct sched_param param;
  sched_getparam(proc.get_pid(), &param);

  // check rt parameters are set correctly
  EXPECT_EQ(sched_getscheduler(proc.get_pid()), SCHED_RR);
  EXPECT_EQ(param.sched_priority, rr_priority_min);

  proc.stop_and_collect_exit_code(2s);
}

TEST(process_rt_settings, cpu_affinity)
{
  // get the number of CPUs in the system, the CPU masks to test are configured based on this value
  auto total_cpus = static_cast<std::int64_t>(sysconf(_SC_NPROCESSORS_CONF));

  ASSERT_GE(total_cpus, 0);
  ASSERT_LE(total_cpus, 64);

  // set the mask only for even CPUs
  std::int64_t even_cpu_mask = 0x0;
  for (uint32_t i = 0U; i < total_cpus; i += 2U) {
    even_cpu_mask |= (1 << i);
  }

  StartupConfig config(TESTER_EXECUTABLE);
  config.set_cpu_affinity_mask(even_cpu_mask);
  Process proc("process1", config);

  proc.start();

  struct sched_param param;
  sched_getparam(proc.get_pid(), &param);

  apex_cpuset_t process_a_cpu_set;
  apex_cpu_zero(&process_a_cpu_set);
  EXPECT_EQ(apex_get_cpu_affinity_for_pid(proc.get_pid(), &process_a_cpu_set), APEX_RET_OK);
  const auto process_a_bitset = apex_cpu_set_to_bitset(process_a_cpu_set);
  EXPECT_EQ(process_a_bitset.to_ulong(), even_cpu_mask);

  proc.stop_and_collect_exit_code(2s);
}
