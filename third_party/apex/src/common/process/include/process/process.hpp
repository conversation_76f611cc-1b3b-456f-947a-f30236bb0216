/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file contains a process descriptor to start and stop a process

#ifndef PROCESS__PROCESS_HPP_
#define PROCESS__PROCESS_HPP_

#include <spawn.h>

#include <array>
#include <chrono>
#include <cstddef>
#include <cstdint>
#include <fstream>
#include <memory>
#include <string>

#include "cpputils/optional.hpp"
#include "process/ring_buffer.hpp"
#include "process/startup_config.hpp"
#include "process/visibility_control.hpp"
#include "string/string.hpp"

/// \namespace apex
namespace apex
{
/// \namespace apex::process
namespace process
{

enum class StreamRedirection : std::uint8_t
{
  /// \brief Streams are redirected to pipes for monitoring
  RedirectToPipe = 0,

  /// \brief Streams are redirected to /dev/null to supress the output
  RedirectToDevNull = 1,

  /// \brief Streams are redirected to the log files (if they are set)
  RedirectToLogFile = 2,

  /// \brief Streams are not redirected
  DoNotRedirect = 3,
};

/// \brief The process pipe configuration
/// This struct contains the configuration to define which pipes for stream and early process
/// termination monitoring
struct ProcessPipeConfig
{
  /// \brief Configures the process stream redirection
  StreamRedirection stream_redirection = StreamRedirection::RedirectToPipe;

  /// \brief If true, it creates the process termination pipe
  bool create_process_termination_monitoring_pipe = false;
};

/// \brief The process logging configuration
/// This struct contains the configuration to define the log files for stdout and stderr
struct LogConfig
{
  /// \brief The log file name for stdout. Empty is disabled.
  std::string stdout_log_file_name;

  /// \brief If false it forces file flushing on each write for stdout
  bool stdout_log_file_buffering = true;

  /// \brief The log file name for stderr. Empty is disabled.
  std::string stderr_log_file_name;

  /// \brief If false it forces file flushing on each write for stderr
  bool stderr_log_file_buffering = true;

  /// \brief The log file name for stderr. Empty is disabled.
  std::string process_log_file_name;

  /// \brief If false it forces file flushing on each write for stdout and stderr
  bool process_log_file_buffering = true;
};

/// \class Process
/// A descriptor of a process being monitored
class PROCESS_PUBLIC Process
{
public:
  /// \brief The standard output stream offset in the internal representation of the process class
  static constexpr auto cout_offset = size_t{0};

  /// \brief The standard error stream offset in the internal representation of the process class
  static constexpr auto cerr_offset = size_t{1};

  /// \brief The stream buffer capacity after reaching which it gets cyclically overwritten
  static constexpr auto stream_buffer_capacity = size_t{255};

  /// \brief The write end fd of the pipe used to monitor the early process termination
  static constexpr auto process_termination_fd = size_t{3};

  /// \brief Constructor
  /// \param name The process name
  explicit Process(apex::string_view name);

  /// \brief Constructor
  /// \param name The process name
  /// \param config The process default start-up configuration
  /// \param log_config The process logging configuration
  Process(apex::string_view name,
          StartupConfig config,
          apex::optional<LogConfig> log_config = apex::nullopt,
          ProcessPipeConfig pipe_config = ProcessPipeConfig{});

  /// \brief Constructor
  /// \param name The process name
  /// \param config The process default start-up configuration
  /// \param pipe_config Configuration for process pipes
  Process(apex::string_view name, StartupConfig config, ProcessPipeConfig pipe_config);

  Process(const Process &) = delete;
  Process & operator=(const Process &) = delete;

  /// \brief Gets the process name
  /// \return The process name
  /// \cert
  /// \deterministic
  const std::string & get_name() const
  {
    return m_name;
  }

  /// \brief Sets the current process start-up configuration
  /// \param config The process start-up configuration
  /// \cert
  /// \deterministic
  void set_config(const StartupConfig & config)
  {
    m_config = config;
  }
  /// \brief Gets the current process start-up configuration
  /// \return The process start-up configuration
  /// \cert
  /// \deterministic
  apex::optional<StartupConfig> get_config() const
  {
    return m_config;
  }

  /// \brief Starts the process using the default start-up config
  /// \cert
  /// \deterministic
  void start();

  /// \brief Starts the process with a given configuration
  /// \param config The process config
  /// \cert
  /// \deterministic
  void start(const StartupConfig & config);

  /// \brief Stores the data arrived from a file descriptor of the process
  /// \param stream_offset The offset of the stream
  /// \return 1 if ok, 0 if closed, -1 if writing to the log failed (non-critical error)
  /// \cert
  /// \deterministic
  std::int32_t process_available_data(size_t stream_offset);

  /// \brief Sends a signal to the process
  /// \param sig A signal to send
  /// \cert
  /// \deterministic
  void send_signal(std::int32_t sig);

  /// \brief Sends SIGTERM to the process
  /// \cert
  /// \deterministic
  void terminate();

  /// \brief Sends SIGKILL to the process
  /// \cert
  /// \deterministic
  void kill();

  /// \brief Registers exit status of the exited processes
  /// \param timeout timeout for the wait
  /// \cert
  /// \deterministic
  void collect_exit_code(const std::chrono::seconds & timeout);

  /// \brief Stops the process
  /// It sends SIGTERM to the process and waits for the process to exit
  /// Then it sends SIGKILL
  /// \cert
  /// \deterministic
  void stop_and_collect_exit_code(const std::chrono::seconds & timeout);

  /// \brief Sends SIGTERM to the process and waits for the process to exit
  /// \return Whether process has exited
  /// \cert
  /// \deterministic
  bool terminate_and_collect_exit_code(const std::chrono::seconds & timeout);

  /// \brief Sends SIGKILL to the process and waits for the process to exit
  /// \cert
  /// \deterministic
  void kill_and_collect_exit_code(const std::chrono::seconds & timeout);

  /// \brief Gets the reading end of the stream pipe
  /// \param stream_offset The offset of the stream
  /// \return The files descriptor of the reading end of the stream pipe
  /// \cert
  /// \deterministic
  std::int32_t get_pipe(size_t stream_offset) const;

  /// \brief Gets the reading end of the process termination pipe
  /// \return The files descriptor of the reading end of the termination pipe
  /// \cert
  /// \deterministic
  std::int32_t get_termination_pipe() const;

  /// \brief Closes the termination pipe
  /// \cert
  void close_termination_pipe();

  /// \brief Gets the log file name for the stream
  /// \param stream_offset The offset of the stream
  /// \return The log file name for the stream
  /// \cert
  /// \deterministic
  const std::string & get_log_name(size_t stream_offset);

  /// \brief Tests whether the process has id (effectively -- whether it is started)
  /// \return Whether the process has id
  /// \cert
  /// \deterministic
  bool has_pid() const noexcept
  {
    return m_pid != apex::nullopt;
  }

  /// \brief Gets the process id
  /// \return The process id
  /// \cert
  /// \deterministic
  std::int32_t get_pid() const noexcept
  {
    return *m_pid;
  }

  /// \brief Waits for process exit
  /// \param timeout timeout for the wait
  /// \return Whether the wait exits when the timeout has elapsed
  /// \cert
  /// \deterministic
  bool wait_for_exit_with_timeout(const std::chrono::seconds & timeout);

  /// \brief Tests whether the process has started
  /// \return Whether the process has started
  /// \cert
  /// \deterministic
  bool has_started() const noexcept
  {
    return has_pid();
  }

  /// \brief Tests whether the process has exited
  /// \return Whether the process has exited
  /// \cert
  /// \deterministic
  bool has_exited() const noexcept
  {
    return m_exited;
  }

  /// \brief Tests whether the process has exit code
  /// \return Whether the process has exit code
  /// \cert
  /// \deterministic
  bool has_exit_code() const noexcept
  {
    return m_exit_code != apex::nullopt;
  }

  /// \brief Gets the process exit code
  /// \return The process exit code
  /// \cert
  /// \deterministic
  std::int32_t get_exit_code() const noexcept
  {
    return *m_exit_code;
  }

  /// \brief Tests whether the process has exit signal
  /// \return Whether the process has exit signal
  /// \cert
  /// \deterministic
  bool has_exit_signal() const noexcept
  {
    return m_exit_signal != apex::nullopt;
  }

  /// \brief Gets the process exit signal
  /// \return The process exit signal
  /// \cert
  /// \deterministic
  std::int32_t get_exit_signal() const noexcept
  {
    return *m_exit_signal;
  }

  /// \brief Gets the current stream data as a string
  /// \param stream_offset The offset of the stream
  /// \return The current stream data as a string
  /// \cert
  /// \deterministic
  [[deprecated("Use read_stream_data instead")]] string256_t get_stream_data(
    size_t stream_offset) const;

  /// \brief Get and remove the current stream data as a string
  /// \param stream_offset The offset of the stream
  /// \return The current stream data as a string
  /// \cert
  /// \deterministic
  string256_t take_stream_data(size_t stream_offset);

  /// \brief Peeks the current stream data as a string.
  /// The content is not removed
  /// \param stream_offset The offset of the stream
  /// \return The current stream data as a string
  /// \cert
  /// \deterministic
  string256_t read_stream_data(size_t stream_offset) const;

  /// \brief Throws if the stream offset is invalid
  /// \param stream_offset The offset of the stream
  /// \cert
  /// \deterministic
  static void validate_stream_offset(size_t stream_offset);

  /// \brief Get the file stream to used to log to a file
  /// \param stream_offset The offset of the stream
  /// \cert
  std::ofstream & get_file_stream(size_t stream_offset);

  /// \brief Waits for process exit
  /// \param flags Flags to pass to POSIX waitpid()
  /// \return Whether process has exited
  /// \cert
  /// \deterministic if flags is WNOHANG.
  bool wait_for_exit(std::int32_t flags = 0);

  /// \brief Resets the exit state from a terminated process
  /// \cert
  /// \deterministic
  void reset_state();

  /// \brief Consumes the stream referenced by 'stream_offset' and writes the content into the
  /// passed 'str' string. Should be used only for testing
  /// \param stream_offset 0 for stdout, 1 for stderr
  /// \param str String to which the content of the stream will be appended
  /// \details It actually by-passes the stream data
  /// \return true if there was something to read, false otherwise
  bool append_stream_to_string(size_t stream_offset, std::string & str);

private:
  /// \brief Creates the file actions for the process when calling posix_spawn
  /// \param cout_from_child_fd The read end of the child's stdout pipe
  /// \param cerr_from_child_fd The read end of the child's stderr pipe
  /// \param termination_from_child_fd The read end of the child's termination pipe
  /// \return The file actions to use for the child process
  posix_spawn_file_actions_t create_file_actions(const std::int32_t cout_from_child_fd,
                                                 const std::int32_t cerr_from_child_fd,
                                                 const std::int32_t termination_from_child_fd);

  /// \brief Configures the child stream and how they are redirected
  /// \param file_actions The file actions to configure
  /// \param stream_fd The stream file descriptor to redirect in the child process (SDTOUT_FILENO or
  /// STDERR_FILENO) \param new_fd The new file descriptor to redirect the child stream to (or -1 if
  /// not redirected) \param file_path The file path to redirect the child stream to (or empty if
  /// not redirected)
  void configure_child_stream(posix_spawn_file_actions_t & file_actions,
                              std::int32_t stream_fd,
                              std::int32_t new_fd,
                              std::string_view file_path);

  /// \brief Sets up receiving end of the pipes, making read descriptors unblocking
  /// and closing write descriptors
  /// \param cout_from_child_fd Read end of the child's stdout pipe
  /// \param cerr_from_child_fd Read end of the child's stderr pipe
  /// \param termination_from_child_fd Read end of the child's termination pipe
  /// \cert
  /// \deterministic
  void setup_receiving_end(const std::int32_t cout_from_child_fd,
                           const std::int32_t cerr_from_child_fd,
                           const std::int32_t termination_from_child_fd);

  /// \brief Throws if process is not launched yet
  /// \cert
  /// \deterministic
  void make_sure_has_pid() const;

  /// \class stream_data
  /// Represents an output stream from the process
  class stream_data final
  {
  public:
    /// \brief The type of cyclic buffer to hold the last stream data in
    /// In order to fit into apex::string256_t it cannot be more than 255 (+terminating 0)
    using stream_buffer_type = ring_buffer<char, stream_buffer_capacity>;

    /// \brief Destroys the buffer closing any open file descriptors
    /// \cert
    ~stream_data();

    stream_data() = default;
    stream_data(const stream_data &) = delete;
    stream_data & operator=(const stream_data &) = delete;
    stream_data(stream_data &&) noexcept = delete;
    stream_data & operator=(stream_data &&) noexcept = delete;

    /// \brief Opens a log file for writing
    /// \param log_file_name The name of the log file to write to
    /// \param buffered Enable or disable stream buffering
    /// \cert
    void open_log(std::string log_file_name, bool buffered = true);

    /// \brief Returns the log file name
    /// \return The name of the associated log file
    /// \cert
    /// \deterministic
    const std::string & get_log_file_name() const noexcept
    {
      return m_log_file_name;
    }

    /// \brief Sets up the read descriptor for the stream
    /// \param fd The read descriptor for the stream
    /// \cert
    /// \deterministic
    void set_read_descriptor(std::int32_t fd) noexcept
    {
      m_fd = fd;
    }

    /// \brief Gets the read descriptor for the stream
    /// \return The read descriptor for the stream
    /// \cert
    /// \deterministic
    std::int32_t get_read_descriptor() const noexcept
    {
      return m_fd;
    }

    /// \brief Reads all available data from the descriptor
    /// Stores it in the cyclic buffer
    /// \return 1 if ok, 0 if closed, -1 if writing to the log failed (non-critical error)
    /// \cert
    /// \deterministic
    std::int32_t read();

    /// \brief Closes all file descriptors
    /// \cert
    /// \deterministic
    void close_all();

    /// \brief Closes log file
    /// \cert
    void close_log() noexcept;

    /// \brief Gets the cyclic stream buffer
    /// \return The cyclic stream buffer
    /// \cert
    /// \deterministic
    stream_buffer_type & get_stream_buffer() noexcept
    {
      return m_last_data;
    }

    std::ofstream & get_file_stream() noexcept
    {
      return m_log_file;
    }

  private:
    /// \brief The stream file descriptor for reading
    std::int32_t m_fd{-1};

    /// \brief The stream cyclic buffer of last read data
    stream_buffer_type m_last_data;

    /// \brief The log file name
    std::string m_log_file_name;

    /// \brief The log file
    std::ofstream m_log_file;
  };

  /// \brief The process name
  std::string m_name;

  /// \brief The process start-up configuration
  apex::optional<StartupConfig> m_config;

  /// \brief The stream data
  std::array<std::unique_ptr<stream_data>, 2> m_stream_data{
    {std::make_unique<stream_data>(), std::make_unique<stream_data>()}};

  /// \brief The process id
  apex::optional<std::int32_t> m_pid;

  /// \brief The process exit code
  apex::optional<std::int32_t> m_exit_code;

  /// \brief The process exit signal
  apex::optional<std::int32_t> m_exit_signal;

  /// \brief Whether the process has exited
  bool m_exited{false};

  /// \brief The process file logging configuration
  apex::optional<LogConfig> m_log_config;

  /// \brief The process pipe configuration.
  ProcessPipeConfig m_process_pipe_config;

  /// \brief The process termination pipe file descriptor
  std::int32_t m_termination_pipe_fd{-1};
};

}  // namespace process
}  // namespace apex


#endif  // PROCESS__PROCESS_HPP_
