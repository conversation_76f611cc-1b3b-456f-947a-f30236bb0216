// Copyright 2023 Apex.AI, Inc.
// All rights reserved.

#include "process/process.hpp"

#include <fcntl.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <unistd.h>

#include <algorithm>
#include <bitset>
#include <cassert>
#include <cerrno>
#include <chrono>
#include <cstdlib>
#include <functional>
#include <iostream>
#include <mutex>
#include <string>
#include <thread>
#include <utility>
#include <vector>

#include "apexutils/apex_pipe2.h"
#include "cpputils/safe_cast.hpp"
#include "cpputils/string_view.hpp"
#include "process/stream_data_utils.hpp"

/// \namespace apex
namespace apex
{
/// \namespace apex::process
namespace process
{
namespace
{
std::vector<char *> to_char_vector(std::vector<std::string> & args)
{
  std::vector<char *> argv;
  argv.reserve(args.size());
  (void)std::transform(
    args.begin(), args.end(), std::back_inserter(argv), [](auto & s) { return &s[0]; });
  return argv;
}

// This function will overwrite the environment variable if it exists, or append it to the end
void overwrite_or_append_env_var(std::vector<char *> & envp, const std::string & var)
{
  const auto delimiter_pos = var.find('=');
  assert(delimiter_pos != std::string::npos);
  const auto it = std::find_if(envp.begin(), envp.end(), [&var, delimiter_pos](const char * env) {
    if (!env) {
      return false;
    }
    std::string_view env_view(env);
    auto equal_pos = env_view.find('=');
    return (equal_pos != std::string_view::npos) && (equal_pos == delimiter_pos) &&
           (env_view.substr(0, delimiter_pos) == var.substr(0, delimiter_pos));
  });
  if (it != envp.end()) {
    // Overwrite the existing environment variable
    /*
     AXIVION Next CodeLine MisraC++2023-8.2.3: Reason: Code Quality (Functional suitability),
     Justification: const cast is safe because the pointed string data will not be modified. The
     caller is responsible for the lifetime and immutability of the string data.
     */
    *it = const_cast<char *>(var.data());
  } else {
    // Append the new environment variable to the vector
    /*
     AXIVION Next CodeLine MisraC++2023-8.2.3: Reason: Code Quality (Functional suitability),
     Justification: const cast is safe because the pointed string data will not be modified. The
     caller is responsible for the lifetime and immutability of the string data.
     */
    envp.push_back(const_cast<char *>(var.data()));
  }
}

std::vector<std::string> get_string_args(const StartupConfig & config)
{
  auto str_args = config.get_args();
  (void)str_args.insert(str_args.begin(), config.get_path());
  return str_args;
}

std::vector<std::string> get_string_env_vars(const StartupConfig & config)
{
  std::vector<std::string> str_env_vars;
  auto env_vars = config.get_env_vars();
  str_env_vars.reserve(env_vars.size());
  (void)std::transform(
    env_vars.begin(), env_vars.end(), std::back_inserter(str_env_vars), [](const auto & var) {
      return var.first + "=" + var.second;
    });
  return str_env_vars;
}

void copy_parent_env_vars(std::vector<char *> & envp)
{
  // go trough parent environment and copy pre-defined variables
  char ** env = environ;
  while (*env != nullptr) {
    envp.emplace_back(*env);
    ++env;
  }
}

#ifdef APEX_LINUX
apex_cpuset_t bitset_to_apex_cpu_set(const std::bitset<APEX_CPUSET_SIZE> & bitset)
{
  apex_cpuset_t set;
  apex_cpu_zero(&set);
  for (uint32_t i = 0U; i < bitset.size(); i++) {
    if (bitset.test(i)) {
      apex_cpu_set(i, &set);
    }
  }
  return set;
}
#endif

posix_spawnattr_t create_spawn_attributes(const StartupConfig & config)
{
  posix_spawnattr_t attr;
  std::int32_t policy;
  struct sched_param param;

  // Initialize the spawn attributes structure
  if (::posix_spawnattr_init(&attr) != 0) {
    throw apex::runtime_error{"Creating spawn attributes: failed to initialize spawn attributes"};
  }

  // creates a new process group for the child process
  if (::posix_spawnattr_setpgroup(&attr, 0) != 0) {
    throw apex::runtime_error{"Failed to set pgroup attributes"};
  }

  if (config.get_scheduling_policy() != std::nullopt) {
    const auto scheduling_policy = config.get_scheduling_policy().value();
    if (scheduling_policy == threading::scheduler::fifo) {
      policy = SCHED_FIFO;
    } else if (scheduling_policy == threading::scheduler::round_robin) {
      policy = SCHED_RR;
    } else {
      policy = SCHED_OTHER;
    }
    if (::posix_spawnattr_setschedpolicy(&attr, policy) != 0) {
      throw apex::runtime_error{"Creating spawn attributes: Error setting scheduling policy"};
    }
  }
  if (config.get_priority() != std::nullopt) {
    param.sched_priority = config.get_priority().value();
    if (param.sched_priority != 0) {
      if (::posix_spawnattr_setschedparam(&attr, &param) != 0) {
        throw apex::runtime_error{"Creating spawn attributes: Error setting scheduling parameters"};
      }
    }
  }

#ifdef APEX_QNX
  const auto cpu_affinity_mask = config.get_cpu_affinity_mask();
  if (cpu_affinity_mask.any()) {
    const auto runmask = apex::cast::safe_cast<uint32_t>(config.get_cpu_affinity_mask().to_ulong());
    if (runmask != 0U) {
      if (::posix_spawnattr_setrunmask(&attr, runmask) != 0) {
        throw apex::runtime_error{"Creating spawn attributes: Error setting CPU affinity"};
      }
    }
  }
#endif

/*
 AXIVION DISABLE STYLE MisraC++2023-7.0.4: Reason: Code Quality (Functional suitability),
 Justification: Allow singed type bit operation since it is a POSIX way
 */
#ifdef APEX_QNX
  const uint32_t flags = POSIX_SPAWN_RESETIDS | POSIX_SPAWN_SETSCHEDULER | POSIX_SPAWN_SETPGROUP |
                         POSIX_SPAWN_EXPLICIT_CPU;
#else
  const uint32_t flags = POSIX_SPAWN_RESETIDS | POSIX_SPAWN_SETSCHEDULER | POSIX_SPAWN_SETPGROUP;
#endif

#ifdef APEX_QNX
  if (::posix_spawnattr_setxflags(&attr, flags) != 0) {
    throw apex::runtime_error{"Creating spawn attributes: Error setting spawn flags"};
  }
#else
  // TODO(carlos): use posix_spawnattr_setcred and PROCMGR_AID_SPAWN_SETUID in QNX
  // posix_spawnattr_setflags requires short int, but safe cast is used

  if (::posix_spawnattr_setflags(&attr, apex::cast::safe_cast<short int>(flags)) != 0) {  // NOLINT
    throw apex::runtime_error{"Creating spawn attributes: Error setting spawn flags"};
  }
#endif
  // AXIVION ENABLE STYLE MisraC++2023-7.0.4
  return attr;
}

void clean_up_on_start_failure(const std::array<int, 2> & cout_fds,
                               const std::array<int, 2> & cerr_fds,
                               const std::array<int, 2> & termination_fds,
                               apex::optional<posix_spawnattr_t> & spawn_attr,
                               apex::optional<posix_spawn_file_actions_t> & file_actions)
{
  if (cout_fds[0] != -1) {
    (void)::close(cout_fds[0]);
  }
  if (cout_fds[1] != -1) {
    (void)::close(cout_fds[1]);
  }
  if (cerr_fds[0] != -1) {
    (void)::close(cerr_fds[0]);
  }
  if (cerr_fds[1] != -1) {
    (void)::close(cerr_fds[1]);
  }
  if (termination_fds[0] != -1) {
    (void)::close(termination_fds[0]);
  }
  if (termination_fds[1] != -1) {
    (void)::close(termination_fds[1]);
  }
  if (spawn_attr != apex::nullopt) {
    (void)::posix_spawnattr_destroy(&spawn_attr.value());
  }
  if (file_actions != apex::nullopt) {
    (void)::posix_spawn_file_actions_destroy(&file_actions.value());
  }
}

}  // namespace

Process::Process(apex::string_view name) : Process(name, StartupConfig(), apex::nullopt) {}

Process::Process(apex::string_view name,
                 StartupConfig config,
                 apex::optional<LogConfig> log_config,
                 ProcessPipeConfig pipe_config)
: m_name(name),
  m_config{std::move(config)},
  m_log_config{std::move(log_config)},
  m_process_pipe_config{pipe_config}
{
}

Process::Process(apex::string_view name, StartupConfig config, ProcessPipeConfig pipe_config)
: Process(name, std::move(config), apex::nullopt, std::move(pipe_config))
{
}

void Process::start(const StartupConfig & config)
{
  set_config(config);
  start();
}

void Process::start()
{
  if (has_started() && !has_exited()) {
    throw apex::runtime_error{"start(): process might be already running: ", m_name};
  }

  if (m_config == apex::nullopt) {
    throw apex::runtime_error{"start(): process configuration is not set : ", m_name};
  }

  // clean-up exit status from any previous launched process
  reset_state();

  pid_t child_pid;
  apex::optional<posix_spawnattr_t> spawn_attr;
  apex::optional<posix_spawn_file_actions_t> file_actions;
  std::vector<char *> envp;
  std::vector<char *> argv;
  std::array<int, 2> cout_fds{{-1, -1}};
  std::array<int, 2> cerr_fds{{-1, -1}};
  std::array<int, 2> termination_fds{{-1, -1}};
  constexpr std::size_t pipe_end_read = 0U;
  constexpr std::size_t pipe_end_write = 1U;

  const auto str_env_vars = get_string_env_vars(m_config.value());
  copy_parent_env_vars(envp);
  for (const auto & env_var : str_env_vars) {
    overwrite_or_append_env_var(envp, env_var);
  }
  envp.push_back(nullptr);

  auto str_args = get_string_args(m_config.value());
  argv = to_char_vector(str_args);
  argv.push_back(nullptr);

  // O_CLOEXEC flag prevents other child processes to keep the pipe open when they are starting
  // concurrently
  if (m_process_pipe_config.stream_redirection == StreamRedirection::RedirectToPipe) {
    if (apex_pipe2(cout_fds.data(), O_CLOEXEC) == -1) {
      throw apex::system_error{errno, "Failed to create cout pipe. Error"};
    }

    if (apex_pipe2(cerr_fds.data(), O_CLOEXEC) == -1) {
      clean_up_on_start_failure(cout_fds, cerr_fds, termination_fds, spawn_attr, file_actions);
      throw apex::system_error{errno, "Failed to create cerr pipe. Error"};
    }
  }

  if (m_process_pipe_config.create_process_termination_monitoring_pipe) {
    if (apex_pipe2(termination_fds.data(), O_CLOEXEC) == -1) {
      clean_up_on_start_failure(cout_fds, cerr_fds, termination_fds, spawn_attr, file_actions);
      throw apex::system_error{errno, "Failed to create process termination pipe. Error"};
    }
  }

  try {
    file_actions = create_file_actions(
      cout_fds[pipe_end_write], cerr_fds[pipe_end_write], termination_fds[pipe_end_write]);
  } catch (const apex::runtime_error & e) {
    clean_up_on_start_failure(cout_fds, cerr_fds, termination_fds, spawn_attr, file_actions);
    throw apex::system_error{errno, "Failed to setup file actions. Error", e.what()};
  }

  try {
    spawn_attr = create_spawn_attributes(m_config.value());
  } catch (const apex::runtime_error & e) {
    clean_up_on_start_failure(cout_fds, cerr_fds, termination_fds, spawn_attr, file_actions);
    throw apex::system_error(errno, "Failed to setup spawn attributes. Error", e.what());
  }

  // The discovery of Apex.Ida opens file descriptors of various resources concurrently, which can
  // lead to "EBADF" on QNX. Hence, we retry the spawn call.
  size_t spawn_retry_count{0};
  constexpr size_t max_retries = 3;
  while (true) {
    const auto ret = ::posix_spawn(&child_pid,
                                   argv[size_t{0}],
                                   &file_actions.value(),
                                   &spawn_attr.value(),
                                   argv.data(),
                                   envp.data());

    if (ret == 0) {
      break;
    }

    if (ret == EBADF && (spawn_retry_count < max_retries)) {
      ++spawn_retry_count;
      continue;
    }
    clean_up_on_start_failure(cout_fds, cerr_fds, termination_fds, spawn_attr, file_actions);
    throw apex::system_error{errno, "Failed to spawn executable. Error"};
  }

  m_pid = child_pid;
  if (cout_fds[pipe_end_write] != -1) {
    if (::close(cout_fds[pipe_end_write]) == -1) {
      throw apex::system_error{errno, "Failed to close cout pipe end write"};
    }
  }
  if (cerr_fds[pipe_end_write] != -1) {
    if (::close(cerr_fds[pipe_end_write]) == -1) {
      throw apex::system_error{errno, "Failed to close cerr pipe end write"};
    }
  }
  if (termination_fds[pipe_end_write] != -1) {
    if (::close(termination_fds[pipe_end_write]) == -1) {
      throw apex::system_error{errno, "Failed to close termination pipe end write"};
    }
  }
  (void)::posix_spawnattr_destroy(&spawn_attr.value());
  (void)::posix_spawn_file_actions_destroy(&file_actions.value());
  setup_receiving_end(
    cout_fds[pipe_end_read], cerr_fds[pipe_end_read], termination_fds[pipe_end_read]);

  // In Linux CPU affinity can be configured only after the process has been spawn
#ifdef APEX_LINUX
  const auto cpu_affinity_mask = m_config->get_cpu_affinity_mask();
  if (cpu_affinity_mask.any()) {
    const auto mask = bitset_to_apex_cpu_set(cpu_affinity_mask);
    if (apex_set_cpu_affinity_for_pid(m_pid.value(), mask) != APEX_RET_OK) {
      throw apex::system_error{errno, "Failed to set process CPU affinity. Error"};
    }
  }
#endif  // APEX_LINUX
}

posix_spawn_file_actions_t Process::create_file_actions(
  const std::int32_t cout_from_child_fd,
  const std::int32_t cerr_from_child_fd,
  const std::int32_t termination_from_child_fd)
{
  posix_spawn_file_actions_t file_actions;

  // initialize a spawn file actions object
  if (::posix_spawn_file_actions_init(&file_actions) != 0) {
    throw apex::runtime_error{"Creating file actions: Failed to initialize file actions"};
  }

  // set child stdout and stderr streams to the pipe, /dev/null or file
  const auto dev_null_file_path = "/dev/null";
  auto new_cout_fd = -1;
  auto new_cerr_fd = -1;
  std::string_view cout_path;
  std::string_view cerr_path;
  if (m_process_pipe_config.stream_redirection == StreamRedirection::RedirectToPipe) {
    new_cout_fd = cout_from_child_fd;
    new_cerr_fd = cerr_from_child_fd;
  } else if (m_process_pipe_config.stream_redirection == StreamRedirection::RedirectToDevNull) {
    cout_path = dev_null_file_path;
    cerr_path = dev_null_file_path;
  } else if (m_process_pipe_config.stream_redirection == StreamRedirection::RedirectToLogFile &&
             (m_log_config != apex::nullopt)) {
    // if a file name is empty then the stream won't be redirected
    cout_path = m_log_config->stdout_log_file_name;
    cerr_path = m_log_config->stderr_log_file_name;
  }

  // configure stdout and stderr redirection
  configure_child_stream(file_actions, STDOUT_FILENO, new_cout_fd, cout_path);
  configure_child_stream(file_actions, STDERR_FILENO, new_cerr_fd, cerr_path);

  if (termination_from_child_fd != -1) {
    // map child predefined fd to the pipe used for early termination monitoring
    if (::posix_spawn_file_actions_adddup2(
          &file_actions, termination_from_child_fd, Process::process_termination_fd) != 0) {
      (void)::posix_spawn_file_actions_destroy(&file_actions);
      throw apex::runtime_error{"Creating file actions: Failed to config termination pipe"};
    }
  }

  return file_actions;
}

void Process::configure_child_stream(posix_spawn_file_actions_t & file_actions,
                                     std::int32_t stream_fd,
                                     std::int32_t new_fd,
                                     std::string_view file_path)
{
  if (new_fd != -1) {
    const auto res = ::posix_spawn_file_actions_adddup2(&file_actions, new_fd, stream_fd);
    if (res != 0) {
      (void)::posix_spawn_file_actions_destroy(&file_actions);
      throw apex::runtime_error{"Creating file actions: Failed to config child stream: ", res};
    }
  } else if (!file_path.empty()) {
    const auto res = posix_spawn_file_actions_addopen(
      &file_actions, stream_fd, file_path.data(), O_WRONLY | O_CREAT, 0644);
    if (res != 0) {
      (void)::posix_spawn_file_actions_destroy(&file_actions);
      throw apex::runtime_error{"Creating file actions: Failed to config child stream: ", res};
    }
  }
}

std::int32_t Process::process_available_data(size_t stream_offset)
{
  validate_stream_offset(stream_offset);
  return m_stream_data[stream_offset]->read();
}

void Process::collect_exit_code(const std::chrono::seconds & timeout)
{
  make_sure_has_pid();

  if (!wait_for_exit_with_timeout(timeout)) {
    throw apex::runtime_error{"collect_exit_code: unexpected waitpid() result"};
  }
}

void Process::terminate()
{
  make_sure_has_pid();
  send_signal(SIGTERM);
}

void Process::kill()
{
  make_sure_has_pid();
  send_signal(SIGKILL);
}

void Process::stop_and_collect_exit_code(const std::chrono::seconds & timeout)
{
  make_sure_has_pid();

  if (!terminate_and_collect_exit_code(timeout)) {
    kill_and_collect_exit_code(timeout);
  }
}

bool Process::terminate_and_collect_exit_code(const std::chrono::seconds & timeout)
{
  make_sure_has_pid();
  send_signal(SIGTERM);

  return wait_for_exit_with_timeout(timeout);
}

void Process::kill_and_collect_exit_code(const std::chrono::seconds & timeout)
{
  make_sure_has_pid();
  send_signal(SIGKILL);

  if (!wait_for_exit_with_timeout(timeout)) {
    throw apex::runtime_error{"kill: unexpected waitpid() result"};
  }
}

bool Process::wait_for_exit_with_timeout(const std::chrono::seconds & timeout)
{
  using namespace std::chrono_literals;

  make_sure_has_pid();

  std::chrono::steady_clock::time_point const start = std::chrono::steady_clock::now();
  while (std::chrono::steady_clock::now() - start < timeout) {
    // WNOHANG checks child processes without causing the caller to be blocked
    if (wait_for_exit(WNOHANG)) {
      return true;
    }
    std::this_thread::sleep_for(1ms);
  }
  return false;
}

bool Process::wait_for_exit(std::int32_t flags /* = 0*/)
{
  assert(m_pid != apex::nullopt);
  std::int32_t exit_code = 0;
  const auto res = ::waitpid(*m_pid, &exit_code, flags);
  if (*m_pid == res) {
    m_exited = true;
    if (WIFEXITED(exit_code)) {
      m_exit_code = WEXITSTATUS(exit_code);
    } else if (WIFSIGNALED(exit_code)) {
      m_exit_signal = WTERMSIG(exit_code);
    } else {
      // unexpectedly stopped
    }
  } else {
    if (res == -1) {
      throw apex::system_error{errno, "waitpid() failed for the process", m_name};
    }

    if (res != 0) {
      throw apex::runtime_error{"waitpid() failed for the process", m_name};
    }
  }

  return m_exited;
}

void Process::send_signal(std::int32_t sig)
{
  assert(m_pid != apex::nullopt);

  if (::kill(*m_pid, sig) == -1) {
    throw apex::system_error{errno, "Failed to send signal to process", *m_pid};
  }
}

void Process::make_sure_has_pid() const
{
  if (m_pid == apex::nullopt) {
    throw apex::runtime_error{"Process id is unknown, for the process", m_name};
  }
}

std::int32_t Process::get_pipe(size_t stream_offset) const
{
  validate_stream_offset(stream_offset);
  return m_stream_data[stream_offset]->get_read_descriptor();
}

std::int32_t Process::get_termination_pipe() const
{
  return m_termination_pipe_fd;
}

void Process::close_termination_pipe()
{
  if (m_termination_pipe_fd != -1) {
    if (::close(m_termination_pipe_fd) == -1) {
      throw apex::system_error{errno, "Failed to close termination pipe fd"};
    }
    m_termination_pipe_fd = -1;
  }
}

const std::string & Process::get_log_name(size_t stream_offset)
{
  validate_stream_offset(stream_offset);
  return m_stream_data[stream_offset]->get_log_file_name();
}

void Process::reset_state()
{
  m_exited = false;
  m_pid = apex::nullopt;
  m_exit_code = apex::nullopt;
  m_exit_signal = apex::nullopt;
  m_stream_data[cout_offset]->close_all();
  m_stream_data[cerr_offset]->close_all();
  close_termination_pipe();
}

bool Process::append_stream_to_string(size_t stream_offset, std::string & str)
{
  const auto fd = m_stream_data[stream_offset]->get_read_descriptor();

  if (fd == -1) {
    throw apex::runtime_error{"Invalid file descriptor stored in stream data"};
  }

  auto has_data_to_read = false;
  /*
   AXIVION Next CodeLine MisraC++2023-11.3.1: Reason: Code Quality (Functional
   suitability), Justification: C-style array required for POSIX functions
   */
  char data[1024];  // NOLINT  not a variable-type array

  while (true) {
    /*
     AXIVION Next CodeLine MisraC++2023-8.2.8: Reason: Code Quality (Functional
     suitability), Justification: Allow pointer type conversion to use read
     function
     */
    const auto nbytes = ::read(fd, &data[0], 1024);
    if (nbytes > 0) {
      if (!has_data_to_read) {
        has_data_to_read = true;  // there was something to read
      }
      str.append(&data[0], static_cast<size_t>(nbytes));
    } else {
      if ((nbytes == 0) || (errno == EWOULDBLOCK)) {
        break;
      } else {
        if (errno != EINTR) {
          throw apex::system_error{errno};
        }
      }
    }
  }

  return has_data_to_read;
}

void Process::validate_stream_offset(size_t stream_offset)
{
  if ((stream_offset != Process::cout_offset) && (stream_offset != Process::cerr_offset)) {
    throw apex::runtime_error{"bad stream type"};
  }
}

std::ofstream & Process::get_file_stream(size_t stream_offset)
{
  validate_stream_offset(stream_offset);
  return m_stream_data[stream_offset]->get_file_stream();
}

void Process::setup_receiving_end(const std::int32_t cout_from_child_fd,
                                  const std::int32_t cerr_from_child_fd,
                                  const std::int32_t termination_from_child_fd)
{
  const auto mark_fd_nonblocking = [](std::int32_t fd) {
    const auto flags = ::fcntl(fd, F_GETFL);
    if (flags == -1) {
      throw apex::system_error{errno, "Launching process: failed to get fd flags for child"};
    }
    /*
     AXIVION Next CodeLine MisraC++2023-7.0.4: Reason: Code Quality (Functional suitability),
     Justification: Allow singed type bit operation since it is a POSIX way
     */
    if (::fcntl(fd, F_SETFL, flags | O_NONBLOCK) == -1) {
      throw apex::system_error{errno, "Launching process: failed to set fd flags for child"};
    }
  };

  if (cout_from_child_fd != -1) {
    mark_fd_nonblocking(cout_from_child_fd);
    m_stream_data[cout_offset]->set_read_descriptor(cout_from_child_fd);
  }
  if (cerr_from_child_fd != -1) {
    mark_fd_nonblocking(cerr_from_child_fd);
    m_stream_data[cerr_offset]->set_read_descriptor(cerr_from_child_fd);
  }
  if (termination_from_child_fd != -1) {
    mark_fd_nonblocking(termination_from_child_fd);
    m_termination_pipe_fd = termination_from_child_fd;
  }
}

string256_t Process::get_stream_data(size_t stream_offset) const
{
  return read_stream_data(stream_offset);
}

string256_t Process::take_stream_data(size_t stream_offset)
{
  validate_stream_offset(stream_offset);
  auto & stream_buffer = m_stream_data[stream_offset]->get_stream_buffer();
  string256_t retval;
  static_assert(decltype(retval)::capacity() >= stream_data::stream_buffer_type::capacity(),
                "return value capacity lower than stream buffer capacity");
  stream_buffer.get(retval.data(), stream_buffer.size());
  return retval;
}

string256_t Process::read_stream_data(size_t stream_offset) const
{
  validate_stream_offset(stream_offset);
  auto & stream_buffer = m_stream_data[stream_offset]->get_stream_buffer();
  string256_t retval;
  static_assert(decltype(retval)::capacity() >= stream_data::stream_buffer_type::capacity(),
                "return value capacity lower than stream buffer capacity");
  stream_buffer.peek(retval.data(), stream_buffer.size());
  return retval;
}

Process::stream_data::~stream_data()
{
  (void)apex_close_file(m_fd);
  close_log();
}


void Process::stream_data::open_log(std::string log_file_name, bool buffered)
{
  if (!buffered) {
    (void)m_log_file.rdbuf()->pubsetbuf(nullptr, 0);
  }

  m_log_file_name = std::move(log_file_name);
  m_log_file.open(m_log_file_name.c_str());
  if (!m_log_file.is_open()) {
    throw apex::runtime_error{"unable to open file", m_log_file_name.c_str()};
  }
}

std::int32_t Process::stream_data::read()
{
  return stream_data_util::copy_from_fd_to_ring_buffer_and_ofstream(m_fd, m_last_data, m_log_file);
}

void Process::stream_data::close_all()
{
  if (m_fd != -1) {
    if (::close(m_fd) == -1) {
      throw apex::system_error{errno, "Failed to close stream fd"};
    }
    m_fd = -1;
  }
}

void Process::stream_data::close_log() noexcept
{
  if (m_log_file.is_open()) {
    try {
      m_log_file.close();
      // prevent throwing on destructor
    } catch (const std::exception & e) {
      std::cerr << e.what() << std::endl;
    } catch (...) {
      std::cerr << "Failed to close log file: " << m_log_file_name << std::endl;
    }
  }
}

}  // namespace process
}  // namespace apex
