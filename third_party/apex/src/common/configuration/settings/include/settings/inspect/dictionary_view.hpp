/// \copyright Copyright 2017-2018 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file contains Apex.OS settings repository inspection API dictionary view

#ifndef SETTINGS__INSPECT__DICTIONARY_VIEW_HPP_
#define SETTINGS__INSPECT__DICTIONARY_VIEW_HPP_

#include <algorithm>
#include <iterator>
#include <utility>

#include <cpputils/optional.hpp>
#include <settings/construct/types.hpp>
#include <settings/error.hpp>
#include <settings/inspect/node_view.hpp>
#include <settings/inspect/traits.hpp>

/// \namespace apex
namespace apex
{
/// \namespace apex::settings
namespace settings
{
/// \namespace apex::settings::inspect
namespace inspect
{
/// \class dictionary_view
/// A read only view on a dictionary
/// The view is valid as long as the dictionary object it is bound to is alive
/// \cert
/// \deterministic
class dictionary_view
{
private:
  using dictionary = construct::dictionary;
  using dictionary_const_iterator = construct::dictionary::const_iterator;

public:
  /*
   AXIVION Next Construct MisraC++2023-15.1.3: Reason: Code Quality (Usability), Justification:
   Intentional converting ctor
   */
  /// \brief Constructs an dictionary view
  /// \param dict A dictionary to create a view for
  /// \cert
  /// \deterministic
  dictionary_view(const dictionary & dict) noexcept  // NOLINT : Intentional converting ctor
  : m_container{&dict}
  {
  }  // Check for determinism instrumented in test `dictionary_view_basic.ctor_dtor`

  template <class T, class C>
  friend inline details::if_container_view_then_supported_non_optional_t<C, T> get(const C &,
                                                                                   const char *);

  template <class T, class C>
  friend inline details::if_container_view_then_supported_optional_t<C, T> get(
    const C &, const char *) noexcept;

  template <class T, class C>
  friend inline details::if_container_view_then_supported_non_optional_t<C, T> get_or_default(
    const C &, const char *, T) noexcept;

  /// \class const_iterator
  /// The input iterator type for the dictionary view
  /// \cert
  /// \deterministic
  class const_iterator final
  {
  public:
    friend class dictionary_view;

    using iterator_category = std::forward_iterator_tag;
    using value_type = std::pair<apex::string_view, node_view>;
    using difference_type = std::ptrdiff_t;
    using pointer = value_type *;
    using reference = value_type &;

    /// \brief Returns a const-ref to an entry
    /// The ref is valid only as long as the iterator lives
    /// \return A const-ref to an entry
    /// \cert
    /// \deterministic
    const value_type & operator*() const noexcept
    {
      m_entry.first = m_iter->first;
      m_entry.second = *m_iter->second;
      return m_entry;
    }

    /// \brief Returns a const pointer to an entry
    /// The pointer is valid only as long as the iterator lives
    /// \return A const pointer to an entry
    /// \cert
    /// \deterministic
    const value_type * operator->() const noexcept
    {
      m_entry.first = m_iter->first;
      m_entry.second = *m_iter->second;
      return &m_entry;
    }

    /// \brief Compares the iterator to another one for equality
    /// \param other Another iterator
    /// \return Whether iterators are equal
    /// \cert
    /// \deterministic
    bool operator==(const const_iterator & other) const noexcept
    {
      return m_iter == other.m_iter;
    }

    /// \brief Compares the iterator to another one for inequality
    /// \param other Another iterator
    /// \return Whether iterators are not equal
    /// \cert
    /// \deterministic
    bool operator!=(const const_iterator & other) const noexcept
    {
      return !operator==(other);
    }

    /// \brief Advances the iterator in the prefix manner
    /// \return The self reference
    /// \cert
    /// \deterministic
    /*
     AXIVION DISABLE STYLE MisraC++2023-6.8.4: Reason: Code Quality (Usability), Justification:
     Allow member function call from r-value reference (temporary objects) since an iterator
     instance (e.g. begin()) can be used. The test cases with high code coverage should detect
     potential problems.
     */
    const_iterator & operator++() noexcept
    {
      // *INDENT-OFF* (prevent uncrustify from making unnecessary whitespace around `++m_iter`)
      ++m_iter;
      // *INDENT-ON*
      return *this;
    }

    /// \brief Advances the iterator in the postfix manner
    /// \return The previous iterator state
    /// \cert
    /// \deterministic
    const_iterator operator++(int) noexcept
    {
      const auto ret = *this;
      ++m_iter;
      return ret;
    }

    /// \brief Assigns the iterator with the value of another one
    /// \return The self reference
    /// \cert
    /// \deterministic
    const_iterator & operator=(const const_iterator & other) & noexcept
    {
      if (this != &other) {
        m_iter = other.m_iter;
        m_entry = other.m_entry;  // mostly to shut up the linter
      }
      return *this;
    }

    /// \brief Copy ctor, assigns the iterator with the value of another one
    /// \cert
    /// \deterministic
    const_iterator(const const_iterator & other) noexcept
    {
      m_iter = other.m_iter;
      m_entry = other.m_entry;  // mostly to shut up the linter
    }

    /*
     AXIVION Next Construct MisraC++2023-18.4.1: Reason: Code Quality (Functional suitability),
     Justification: std::swap is not always noexcept but necessary.
     */
    /// \brief Swaps two iterators
    /// \param lhs The first iterator to swap
    /// \param rhs The second iterator to swap
    /// \cert
    /// \deterministic
    friend void swap(const_iterator & lhs, const_iterator & rhs)
    {
      std::swap(lhs, rhs);
    }
    // AXIVION ENABLE STYLE MisraC++2023-6.8.4
    /// \brief Default destructor
    ~const_iterator() = default;

  private:
    /// \brief Creates an end iterator
    const_iterator() = default;

    /*
     AXIVION Next Construct MisraC++2023-15.1.3: Reason: Code Quality (Usability),
     Justification: Intentional converting ctor
     */
    /// \brief Creates an iterator from a dictionary iterator
    const_iterator(dictionary_const_iterator iter)  // NOLINT : Intentional converting ctor
    : m_iter{std::move(iter)}
    {
    }

    dictionary_const_iterator m_iter{};
    mutable value_type m_entry = std::make_pair(apex::string_view{}, node_view{});
  };

  /// \brief Returns a node view to an element of the dictionary by its key
  /// \param key The key
  /// \return A view to the node
  /// \cert
  /// \deterministic
  node_view operator[](const apex::string_view & key) const
  {
    const auto iter = find(key);
    if (iter == end()) {
      throw settings_error("no such entry");
    }
    return iter->second;
  }

  /// \brief Compares two dictionary views for equality
  /// \param lhs The first dictionary view to compare
  /// \param rhs The second dictionary view to compare
  /// \return Whether two dictionary views are equal
  /// \cert
  /// \deterministic
  friend bool operator==(const dictionary_view & lhs, const dictionary_view & rhs) noexcept
  {
    return *lhs.m_container == *rhs.m_container;
  }

  /// \brief Compares two dictionary views for inequality
  /// \param lhs The first dictionary view to compare
  /// \param rhs The second dictionary view to compare
  /// \return Whether two dictionary views are not equal
  /// \cert
  /// \deterministic
  friend bool operator!=(const dictionary_view & lhs, const dictionary_view & rhs) noexcept
  {
    return !operator==(lhs, rhs);
  }

  /// \cert
  /// \deterministic Deterministic if visitor's class V is deterministic
  template <class C, class V>
  friend inline details::enable_if_container_view_t<C> visit(const C &, V &&);

  /// \brief Returns an iterator to the element corresponding to the key or an end iterator
  /// \param key The key
  /// \return An iterator to the element corresponding to the key or an end iterator
  /// \cert
  /// \deterministic
  const_iterator find(const apex::string_view & key) const
  {
    return m_container->find(key);
  }

  /// \brief Returns an iterator to the beginning of the dictionary
  /// \return An iterator to the beginning of the dictionary
  /// \cert
  /// \deterministic
  const_iterator begin() const noexcept
  {
    return m_container->begin();
  }

  /// \brief Returns an iterator to the end of the dictionary
  /// \return An iterator to the end of the dictionary
  /// \cert
  /// \deterministic
  const_iterator end() const noexcept
  {
    return m_container->end();
  }

  /// \brief Returns an iterator to the beginning of the dictionary
  /// \return An iterator to the beginning of the dictionary
  /// \cert
  /// \deterministic
  const_iterator cbegin() const noexcept
  {
    return m_container->begin();
  }

  /// \brief Returns an iterator to the end of the dictionary
  /// \return An iterator to the end of the dictionary
  /// \cert
  /// \deterministic
  const_iterator cend() const noexcept
  {
    return m_container->end();
  }

  /// \brief Returns the size of the dictionary
  /// \return The size of the dictionary
  /// \cert
  /// \deterministic
  std::size_t size() const noexcept
  {
    return m_container->size();
  }

  /// \brief Returns whether the dictionary is empty
  /// \return Whether the dictionary is empty
  /// \cert
  /// \deterministic
  bool empty() const noexcept
  {
    return m_container->empty();
  }

private:
  const dictionary * m_container;
};

}  // namespace inspect
}  // namespace settings
}  // namespace apex

#endif  // SETTINGS__INSPECT__DICTIONARY_VIEW_HPP_
