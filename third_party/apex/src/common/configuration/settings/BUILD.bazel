load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

asil_d_hdrs = glob([
    "include/settings/construct.hpp",
    "include/settings/construct/*.hpp",
    "include/settings/error.hpp",
    "include/settings/inspect.hpp",
    "include/settings/inspect/*.hpp",
    "include/settings/repository.hpp",
    "include/settings/visibility.hpp",
])

all_hdrs = glob([
    "include/**/*.hpp",
])

asil_d_srcs = glob([
    "src/construct/*.cpp",
    "src/repository.cpp",
])

all_srcs = glob(["src/**"])

apex_cc_library(
    name = "ida_settings",
    srcs = select({
        "//common/asil:d": asil_d_srcs,
        "//common/asil:qm": all_srcs,
    }),
    hdrs = select({
        "//common/asil:d": asil_d_hdrs,
        "//common/asil:qm": all_hdrs,
    }),
    applicable_licenses = ["//common/integrity:embedded_gold"],
    strip_include_prefix = "include",
    tags = ["integrity ASIL-D"],
    visibility = ["//visibility:public"],
    deps = [
        "//common/asil",
        "//common/containers",
        "//common/cpputils",
        "@coverage_tool//:coverage_io_lib",
        "@yaml-cpp//:yaml-cpp",
    ],
)

# To avoid name collision when generating CMake files for BST
alias(
    name = "settings",
    actual = ":ida_settings",
    applicable_licenses = ["//common/integrity:embedded_gold"],
    visibility = ["//visibility:public"],
)

#TODO(18416): remove workaround
apex_cc_library(
    name = "settings_from_yaml_lib",
    srcs = [
        "src/from_yaml.cpp",
    ],
    hdrs = [
        "include/settings/from_yaml.hpp",
    ],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        ":settings",
        "//common/asil",
    ],
)

filegroup(
    name = "asil_d_test_srcs",
    srcs = glob([
        "test/construct/*.cpp",
        "test/inspect/*.cpp",
    ]) + ["test/test_repository.cpp"],
)

filegroup(
    name = "settings_tests_srcs_with_req_ids",
    srcs = [":asil_d_test_srcs"] + [
        "test/test_doc_examples.cpp",
        "test/test_from_yaml.cpp",
        "test/test_to_yaml.cpp",
    ],
    visibility = ["//common/configuration/settings/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "settings_tests",
    srcs = select({
        "//common/asil:d": [":asil_d_test_srcs"],
        "//common/asil:qm": [":settings_tests_srcs_with_req_ids"],
    }),
    data = glob(["test/**/*.yaml"]),
    local_defines = [
        "CMAKE_SOURCE_DIR=\\\"./common/configuration/settings\\\"",
    ],
    tags = ["constrained_test"],
    deps = [
        ":settings",
        "//tools/testing/apex_test_tools",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "settings_tests_gmock",
    srcs = ["test/test_from_yaml_gmock.cpp"],
    deps = [
        ":settings",
        "//common/asil:only_qm",
        "//tools/testing/apex_test_tools",
        "@googletest//:gtest_main",
    ],
)

ros_pkg(
    name = "settings_pkg",
    cc_libraries = [
        ":settings",
        ":settings_from_yaml_lib",  # Workaround for BST
    ],
    description = "Package containing API for working with settings repository",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Misha Shalem",
    pkg_name = "settings",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/asil:asil_pkg",
        "//common/containers:containers_pkg",
        "//common/cpputils:cpputils_pkg",
        "@yaml-cpp//:yaml-cpp_pkg",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "test/settings.yaml",
        "test/test_doc_examples.cpp",
    ],
    visibility = ["//common/configuration/settings/doc:__pkg__"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
