load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

apex_cc_library(
    name = "threading",
    srcs = glob(["src/**"]),
    hdrs = glob([
        "include/threading/**",
    ]),
    applicable_licenses = ["//common/integrity:embedded_gold"],
    linkopts = select({
        "@platforms//os:linux": ["-lpthread"],
        "//conditions:default": [],
    }),
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "//common/apexutils",
        "//common/cpputils",
        "@coverage_tool//:coverage_io_lib",
    ],
)

ros_pkg(
    name = "threading_pkg",
    cc_libraries = [":threading"],
    description = "Package containing threading utilities",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "<PERSON><PERSON>",
    pkg_name = "threading",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/apexutils:apexutils_pkg",
        "//common/cpputils:cpputils_pkg",
    ],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
