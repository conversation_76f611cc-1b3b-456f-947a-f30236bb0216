/// \copyright Copyright 2017-2019 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file declares apex::threading::shared_mutex classes

#ifndef THREADING__SHARED_MUTEX_HPP_
#define THREADING__SHARED_MUTEX_HPP_

#include <threading/threading_details.hpp>
#include <threading/time_translations.hpp>

#ifdef APEX_MICRO
#include "FreeRTOS_POSIX/pthread.h"
#else
#include <pthread.h>
#endif

#include <ctime>

#include <chrono>
#include <memory>
#include <cstdint>

/// \namespace apex
namespace apex
{
/// \namespace apex::threading
namespace threading
{

/// \class apex::threading::shared_mutex
/// The implementation of std::shared_mutex (from C++17)
/// \cert
/*
 AXIVION Next Construct MisraC++2023-15.0.1: Reason: Code Quality (Functional suitability),
 Justification: This class is intentionally movable.
 */
class shared_mutex
{
  using native_type = pthread_rwlock_t;

public:
  using native_handle_type = native_type *;

  /// \brief Creates a shared_mutex
  /// \cert
  shared_mutex()
  {
    details::call_posix_api(pthread_rwlock_init, m_prwlock.get(), nullptr);
  }

  /// \brief The shared mutex can not be copy constructed
  shared_mutex(const shared_mutex &) = delete;

  /// \brief The shared mutex can not be copy assigned
  shared_mutex & operator=(const shared_mutex &) = delete;

  /// \brief The shared mutex has the default move construction semantics
  /// \cert
  /// \deterministic
  /*
   AXIVION Next Construct MisraC++2023-15.0.1: Reason: Code Quality (Functional suitability),
   Justification: This class is intentionally movable.
   */
  shared_mutex(shared_mutex &&) noexcept = default;

  /// \brief The shared mutex has the default move assignment semantics
  /// \cert
  /// \deterministic
  /*
   AXIVION Next Construct MisraC++2023-15.0.1: Reason: Code Quality (Functional suitability),
   Justification: This class is intentionally movable.
   */
  shared_mutex & operator=(shared_mutex &&) noexcept = default;

  /// \brief Destroys the shared mutex if it is not moved from
  /// \cert
  virtual ~shared_mutex() noexcept
  {
    if (m_prwlock) {
      (void)pthread_rwlock_destroy(m_prwlock.get());
    }
  }

  /// \brief Locks the mutex exclusively
  /// \cert
  void lock()
  {
    throw_if_invalid();
    details::call_posix_api(pthread_rwlock_wrlock, m_prwlock.get());
  }

  /// \brief Locks the mutex as shared (reading)
  /// \cert
  void lock_shared()
  {
    throw_if_invalid();
    details::call_posix_api(pthread_rwlock_rdlock, m_prwlock.get());
  }

  /// \brief Tries to lock the mutex exclusively
  /// \return Whether the lock was successful
  /// \cert
  /// \deterministic
  bool try_lock()
  {
    throw_if_invalid();
    return pthread_rwlock_trywrlock(m_prwlock.get()) == 0;
  }

  /// \brief Tries to lock the mutex as shared (reading)
  /// \return Whether the lock was successful
  /// \cert
  /// \deterministic
  bool try_lock_shared()
  {
    throw_if_invalid();
    return pthread_rwlock_tryrdlock(m_prwlock.get()) == 0;
  }

  /// \brief Unlocks the exclusive lock
  /// \cert
  /// \deterministic
  void unlock()
  {
    throw_if_invalid();
    details::call_posix_api(pthread_rwlock_unlock, m_prwlock.get());
  }

  /// \brief Unlocks the shared lock
  /// \cert
  /// \deterministic
  void unlock_shared()
  {
    throw_if_invalid();
    details::call_posix_api(pthread_rwlock_unlock, m_prwlock.get());
  }

  /// \brief Gets the OS handle to the mutex
  /// \return The OS handle to the mutex
  /// \cert
  /// \deterministic
  native_handle_type native_handle()
  {
    throw_if_invalid();
    return m_prwlock.get();
  }

  /// \brief Checks if the mutex was moved from
  /// \return true if mutex is still valid
  /// \cert
  /// \deterministic
  bool valid() const noexcept
  {
    return m_prwlock.get() != nullptr;
  }

protected:
  /// \brief Throws if mutex is moved from
  void throw_if_invalid() const
  {
    if (!valid()) {
      throw thread_error{"the shared mutex object is invalid"};
    }
  }

  /// Using a unique_ptr implements a correct move semantics for the class implicitly
  std::unique_ptr<native_type> m_prwlock = std::make_unique<native_type>();
};

/// \class apex::threading::shared_timed_mutex
/// The implementation of std::shared_timed_mutex
/// \cert
class shared_timed_mutex : public shared_mutex
{
public:
  /// \brief Tries to lock the mutex exclusively until the duration is expired
  /// \tparam Rep std::chrono::duration::rep
  /// \tparam Period std::chrono::duration::period
  /// \param duration the duration starting from now
  /// \return Whether the lock was successful
  /// \cert
  /// \deterministic
  template<class Rep, class Period>
  bool try_lock_for(const std::chrono::duration<Rep, Period> & duration) noexcept(false)
  {
    throw_if_invalid();
    /*lint -e{9093} time is not a reserved identifier in C++ */
    const auto time = posix_deadline_from_now(duration);
    return pthread_rwlock_timedwrlock(m_prwlock.get(), &time) == 0;
  }

  /// \brief Tries to lock the mutex exclusively until the deadline is reached
  /// If the deadline is in the past just calls to try_lock()
  /// \tparam Clock std::time_point::clock
  /// \tparam Duration std::time_point::duration
  /// \param deadline the deadline in absolute time
  /// \return Whether the lock was successful
  /// \cert
  /// \deterministic
  template<class Clock, class Duration>
  bool try_lock_until(const std::chrono::time_point<Clock, Duration> & deadline) noexcept(false)
  {
    throw_if_invalid();
    auto now = Clock::now();
    return (deadline > now) ?
           try_lock_for(deadline - now) :
           try_lock();
  }

  /// \brief Tries to lock the mutex as shared until the duration is expired
  /// \tparam Rep std::chrono::duration::rep
  /// \tparam Period std::chrono::duration::period
  /// \param duration the duration starting from now
  /// \return Whether the lock was successful
  /// \cert
  /// \deterministic
  template<class Rep, class Period>
  bool try_lock_shared_for(const std::chrono::duration<Rep, Period> & duration) noexcept(false)
  {
    throw_if_invalid();
    /*lint -e{9093} time is not a reserved identifier in C++ */
    const auto time = posix_deadline_from_now(duration);
    return pthread_rwlock_timedrdlock(m_prwlock.get(), &time) == 0;
  }

  /// \brief Tries to lock the mutex as shared until the deadline is reached
  /// If the deadline is in the past just calls to try_lock()
  /// \tparam Clock std::time_point::clock
  /// \tparam Duration std::time_point::duration
  /// \param deadline the deadline in absolute time
  /// \return Whether the lock was successful
  /// \cert
  /// \deterministic
  template<class Clock, class Duration>
  bool try_lock_shared_until(
    const std::chrono::time_point<Clock,
    Duration> & deadline) noexcept(false)
  {
    throw_if_invalid();
    auto now = Clock::now();
    return (deadline > now) ?
           try_lock_shared_for(deadline - now) :
           try_lock_shared();
  }
};

}  // namespace threading
}  // namespace apex

#endif  // THREADING__SHARED_MUTEX_HPP_
