load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_tool", "executables_collection")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test")
load("@python_vendor//:requirements.bzl", "requirement")

# ros2 cli target under test
configured_tool(
    name = "ros2",
    testonly = True,
    framework = "Apex.Grace",
    visibility = ["//visibility:private"],
    deps = [
        "//grace/cli:base_cli",
    ],
)

executables_collection(
    name = "executables",
    testonly = True,
    executables = [":ros2"],
)

py_msgs_library(
    name = "std_msgs_py",
    msgs = "@apex//grace/interfaces/std_msgs",
)

launch_test(
    name = "test_api",
    data = [":executables"],
    launch_test_file = "test_api.py",
    tags = [
        "constrained_test",
        # TODO: This test is flaky and skipped when the coverage measurement
        # is enabled. It should be fixed and enabled in #35616
        "skip_coverage",
    ],
    deps = [
        requirement("pytest"),
        ":std_msgs_py",
        "//grace/cli/ros2doctor",
        "@apex//tools/apex_pytest_utils",  # for skipping tests etc.
        "@apex//tools/launch/launch",
    ],
)

launch_test(
    name = "test_cli",
    data = [":executables"],
    launch_test_file = "test_cli.py",
    tags = ["constrained_test"],
    deps = [
        requirement("pytest"),
        ":std_msgs_py",
        "//grace/cli/ros2doctor",
        "@apex//tools/apex_pytest_utils",  # for skipping tests etc.
        "@apex//tools/launch/launch",
    ],
)
