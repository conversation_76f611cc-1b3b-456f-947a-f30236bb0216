/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#include <iostream>
#include <stdexcept>
#include <string_view>
#include <vector>

#include "dispatcher_interfaces/common.hpp"
#include "event_registry/event_registry.hpp"
#include "event_writer/event_handler.hpp"
#include "event_writer/event_parser.hpp"
#include "event_writer/handlers/console_handler.hpp"
#include "event_writer/handlers/file_handler.hpp"
#include "event_writer/handlers/log_handler.hpp"
#include "event_writer/handlers/sql_handler.hpp"
#include "event_writer/handlers/storage_handler.hpp"
#include "event_writer/parsers/csv_parser.hpp"
#include "event_writer/parsers/json_parser.hpp"
#include "event_writer/parsers/plain_text_parser.hpp"
#include "event_writer/service.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "rclcpp/rclcpp.hpp"
#include "rcutils/cmdline_parser.h"
#include "rcutils/format_string.h"

namespace
{
char ** get_option(char ** begin, char ** end, const char * option, const char * until = nullptr)
{
  intptr_t idx = 0;

  intptr_t end_idx = (intptr_t)(end - begin);
  for (; idx < end_idx; ++idx) {
    if (until != nullptr) {
      if (strncmp(begin[idx], until, strnlen(until, RCUTILS_MAX_STRING_LENGTH)) == 0) {
        end_idx = idx;
        break;
      }
    }
    if (strncmp(begin[idx], option, strnlen(option, RCUTILS_MAX_STRING_LENGTH)) == 0) {
      break;
    }
  }

  /*
   AXIVION Next Line MisraC2023-13.5: Reason: Code Quality (Functional suitability),
   Justification: incrementing idx has no side-effects.
   */
  if ((idx < (end_idx - 1)) && (begin[++idx] != nullptr)) {
    return &begin[idx];
  }

  return nullptr;
}

const std::string help_text =
  std::string{"Usage:\n"} +
  "    --help                                        Print usage help\n"
  "    --service-name                                The name of the service\n"
  "    --dispatcher-name (=" +
  apex::dispatcher::common::get_default_instance_name() +
  ")   The name of the dispatcher\n"
  "    --dispatcher-wait-ms (=1000)                  How long to wait for dispatcher\n"
  "    --handler                                     A handler type "
  "(console|log|file|sql|storage)\n"
  "  The 'console' handler arguments:\n"
  "    --format (='txt')                             The output format type (txt|csv|json)\n"
  "  The 'log' handler arguments:\n"
  "    --format (='txt')                             The output format type (txt|csv|json)\n"
  "    --name (='event_writer_logger')               The logger name\n"
  "  The 'file' handler arguments:\n"
  "    --format (='txt')                             The output format type (txt|csv|json)\n"
  "    --file (='events.(txt|csv|json)')             The file name\n"
  "  The 'sql' handler arguments:\n"
  "    --file (='event_writer.db')                   The SQLite file name\n"
  "    --table (='Events')                           The SQL table name\n"
  "  The 'storage' handler arguments:\n"
  "    --instance (='')                              The message storage service instance name\n"
  "    --db (='events')                              The message storage database name\n"
  "    --history-size (=100)                         The history size\n"
  "\nNote, multiple handlers can be created simultaneously.\n"
  "All handler-specific arguments are positional and correspond to the last "
  "defined handler, left-to-right\n";
}  // namespace
namespace apex::event::dump
{

event_parser_ptr get_event_parser(char ** begin, char ** end)
{
  while (begin != nullptr) {
    begin = get_option(begin, end, "--format", "--handler");
    if (begin != nullptr) {
      std::string_view opt{*begin};
      ++begin;
      std::cout << "Formatting the output as: " << opt.data() << std::endl;
      if (opt == plain_text_parser::FileExtension) {
        return std::make_unique<plain_text_parser>();
      } else if (opt == csv_parser::FileExtension) {
        return std::make_unique<csv_parser>();
      } else if (opt == json_parser::FileExtension) {
        return std::make_unique<json_parser>();
      } else {
        throw apex::runtime_error{"unknown output format type:", opt.data()};
      }
    }
  }
  // default
  return std::make_unique<plain_text_parser>();
}

event_handler_ptr create_console_handler(char ** begin, char ** end)
{
  auto parser = get_event_parser(begin, end);
  return std::make_unique<console_handler>(std::move(parser));
}

event_handler_ptr create_log_handler(rclcpp::Node::SharedPtr node, char ** begin, char ** end)
{
  auto parser = get_event_parser(begin, end);
  std::string logger_name = "event_writer_logger";
  if (const auto name = get_option(begin, end, "--name", "--handler"); nullptr != name) {
    logger_name = *name;
  }
  std::cout << "Logger name: " << logger_name << std::endl;
  return std::make_unique<log_handler>(node, logger_name, std::move(parser));
}

event_handler_ptr create_file_handler(char ** begin, char ** end)
{
  auto parser = get_event_parser(begin, end);
  std::string file{"events."};
  file += parser->get_file_extension();
  if (const auto filename = get_option(begin, end, "--file", "--handler"); nullptr != filename) {
    file = *filename;
  }
  std::cout << "File name: " << file << std::endl;
  return std::make_unique<file_handler>(file, std::move(parser));
}

event_handler_ptr create_sql_handler(char ** begin, char ** end)
{
  std::string file_name = "event_writer.db";
  std::string table_name = "Events";
  if (const auto name = get_option(begin, end, "--file", "--handler"); nullptr != name) {
    file_name = *name;
  }
  if (const auto name = get_option(begin, end, "--table", "--handler"); nullptr != name) {
    table_name = *name;
  }
  std::cout << "File name: " << file_name << ", table name: " << table_name << std::endl;
  return std::make_unique<sql_handler>(file_name, std::move(table_name));
}

event_handler_ptr create_storage_handler(rclcpp::Node::SharedPtr node, char ** begin, char ** end)
{
  std::string instance_name = "";
  std::string database_name = "events";
  std::uint32_t history_size = 100U;
  if (const auto name = get_option(begin, end, "--instance", "--handler"); nullptr != name) {
    instance_name = *name;
  }
  if (const auto name = get_option(begin, end, "--db", "--handler"); nullptr != name) {
    database_name = *name;
  }
  if (const auto size = get_option(begin, end, "--history-size", "--handler"); nullptr != size) {
    history_size = std::stoul(*size);
  }
  std::cout << "Service instance: " << (instance_name.empty() ? "<default>" : instance_name)
            << ", database name: " << database_name << ", history length: " << history_size
            << std::endl;
  return std::make_unique<storage_handler>(node, instance_name, database_name, history_size);
}

std::vector<event_handler_ptr> create_event_handlers(rclcpp::Node::SharedPtr node,
                                                     int argc,
                                                     char * argv[])
{
  std::vector<event_handler_ptr> handlers;
  auto begin = argv;
  auto end = &argv[argc];
  while (begin != nullptr) {
    begin = get_option(begin, end, "--handler");
    if (begin != nullptr) {
      std::string_view opt{*begin};
      ++begin;
      std::cout << "Creating a handler of type: " << opt.data() << std::endl;
      if (opt == "console") {
        handlers.push_back(create_console_handler(begin, end));
      } else if (opt == "log") {
        handlers.push_back(create_log_handler(node, begin, end));
      } else if (opt == "file") {
        handlers.push_back(create_file_handler(begin, end));
      } else if (opt == "sql") {
        handlers.push_back(create_sql_handler(begin, end));
      } else if (opt == "storage") {
        handlers.push_back(create_storage_handler(node, begin, end));
      } else {
        throw apex::runtime_error{"unknown handler type:", opt.data()};
      }
    }
  }
  return handlers;
}
}  //  namespace apex::event::dump

int main(int argc, char * argv[])
{
  using namespace apex;  // NOLINT
  using namespace apex::event::dump;  // NOLINT

  if (rcutils_cli_option_exist(argv, &argv[argc], "--help")) {
    std::cerr << help_text;
    return 0;
  }

  std::int32_t retval = 0;
  try {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);

    std::string service_name = "apex_event_writer_service";
    if (const auto name = get_option(argv, &argv[argc], "--service-name"); nullptr != name) {
      service_name = *name;
    }
    std::string dispatcher_name = apex::dispatcher::common::get_default_instance_name();
    if (const auto name = get_option(argv, &argv[argc], "--dispatcher-name"); nullptr != name) {
      dispatcher_name = *name;
    }
    std::uint32_t dispatcher_wait_ms = 1000;
    if (const auto name = get_option(argv, &argv[argc], "--dispatcher-wait-ms"); nullptr != name) {
      dispatcher_wait_ms = std::atoi(*name);
    }
    std::cout << "Service name: " << service_name << std::endl;

    const auto node = std::make_shared<rclcpp::Node>(service_name + "_node");
    (void)apex::event::sync_event_registry(
      *node, dispatcher_name, std::chrono::milliseconds{dispatcher_wait_ms});
    auto hnds = create_event_handlers(node, argc, argv);
    if (hnds.empty()) {
      hnds.push_back(std::make_unique<console_handler>(std::make_unique<plain_text_parser>()));
    }
    const interrupt_handler::installer guard;
    std::cout << "Running the event-writer service, Ctrl-C to quit..." << std::endl;
    service s{node, service_name, std::move(hnds)};
    interrupt_handler::wait();
    s.stop();
  } catch (const std::exception & e) {
    std::cerr << "Error: " << e.what() << "\n";
    retval = 1;
  } catch (...) {
    std::cerr << "Unknown error\n";
    retval = 1;
  }

  (void)rclcpp::shutdown();
  return retval;
}
