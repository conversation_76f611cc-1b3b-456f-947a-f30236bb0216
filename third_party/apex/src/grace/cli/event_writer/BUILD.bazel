load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "event_writer_pkg",
    bin_executables = [
        ":event_writer",
    ],
    cc_libraries = [
        ":event_writer_lib",
    ],
    description = "Package containing an utility for storing events",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/apexutils:apexutils_pkg",
        "@apex//common/cpputils:cpputils_pkg",
        "@apex//common/interrupt:interrupt_pkg",
        "@apex//common/threading:threading_pkg",
        "@apex//grace/configuration/storage/storage:storage_pkg",
        "@apex//grace/monitoring/dispatcher_interfaces:dispatcher_interfaces_pkg",
        "@apex//grace/monitoring/event_registry:event_registry_pkg",
        "@apex//grace/monitoring/logging:logging_pkg",
        "@apex//grace/monitoring/system_status:system_status_pkg",
        "@apex//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "@apex//grace/ros/rcutils:rcutils_pkg",
        "@apex//grace/utils/system_utils:system_utils_pkg",
        "@fmtlib//:fmtlib_pkg",
        "@nlohmann_json//:nlohmann_json_pkg",
        "@sqlite3//:sqlite3_pkg",
    ],
)

cc_library(
    name = "event_writer_lib",
    srcs = glob(
        ["src/**"],
        exclude = [
            "src/ui/**",
        ],
    ),
    hdrs = glob(["include/**"]),
    strip_include_prefix = "include",
    tags = ["integrity QM"],
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/cpputils",
        "@apex//common/threading",
        "@apex//grace/configuration/storage/storage",
        "@apex//grace/monitoring/dispatcher_interfaces",
        "@apex//grace/monitoring/event_registry",
        "@apex//grace/monitoring/logging",
        "@apex//grace/monitoring/system_status:system_status_lib",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@apex//grace/utils/system_utils",
        "@fmtlib",
        "@nlohmann_json",
        "@sqlite3",
    ],
)

cc_binary(
    name = "event_writer",
    srcs = [
        "src/ui/service_main.cpp",
    ],
    tags = ["integrity QM"],
    deps = [
        ":event_writer_lib",
        "@apex//common/interrupt",
        "@apex//grace/monitoring/dispatcher_interfaces:dispatcher_common",
        "@apex//grace/ros/rcutils",
    ],
)

apex_cc_shared_library(
    name = "dispatcher_interfaces_shared",
    apex_cc_library = "//grace/monitoring/dispatcher_interfaces",
)

apex_cc_shared_library(
    name = "rosidl_typesupport_introspection_cpp_shared",
    apex_cc_library = "//grace/rosidl/rosidl_typesupport_introspection_cpp",
)

apex_cc_test(
    name = "event_writer_tests",
    srcs = glob(["test/**"]),
    data = [
        "@apex//grace/monitoring/dispatcher_interfaces:dispatcher_interfaces_ament_resources",
    ],
    dynamic_deps = [
        ":dispatcher_interfaces_shared",
        ":rosidl_typesupport_introspection_cpp_shared",  # Todo #32755: remove after fixing dependencies
    ],
    env = {
        "APEX_GRACE_LOG_LEVEL": "TRACE",
        "IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc",
    },
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        ":event_writer_lib",
        "@apex//common/apexutils",
        "@apex//grace/tools/ros_domain_coordinator:gtest_main",
    ],
)
