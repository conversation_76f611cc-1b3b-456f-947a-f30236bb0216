load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_tool", "executables_collection")
load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")
load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test")
load("@python_vendor//:requirements.bzl", "requirement")

ENTRY_POINTS = {
    "ros2cli.command": [
        "param = ros2param.command.param:ParamCommand",
    ],
    "ros2cli.extension_point": [
        "ros2param.verb = ros2param.verb:VerbExtension",
    ],
    "ros2param.verb": [
        "delete = ros2param.verb.delete:DeleteVerb",
        "describe = ros2param.verb.describe:DescribeVerb",
        "dump = ros2param.verb.dump:DumpVerb",
        "get = ros2param.verb.get:GetVerb",
        "list = ros2param.verb.list:ListVerb",
        "set = ros2param.verb.set:SetVerb",
        "load = ros2param.verb.load:LoadVerb",
    ],
}

ros_pkg(
    name = "ros2param_pkg",
    description = "The param command for ROS 2 command line tools.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    py_libraries = [":ros2param"],
    version = "0.13.0",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Environment :: Console",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: Apache Software License",
        "Programming Language :: Python",
    ],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "@apex//grace/cli/ros2cli:ros2cli_pkg",
        "@apex//grace/cli/ros2node:ros2node_pkg",
        "@apex//grace/interfaces/rcl_interfaces:rcl_interfaces_pkg",
        "@apex//grace/ros/rclpy/rclpy:rclpy_pkg",
    ],
)

py_entry_points_library(
    name = "ros2param",
    srcs = glob(["ros2param/**/*.py"]),
    data = [":ros2param_pkg.wheel_data"],
    entry_points = ENTRY_POINTS,
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        ":rcl_interfaces_py",
        "@apex//grace/cli/ros2cli",
        "@apex//grace/cli/ros2node",
        "@apex//grace/cli/ros2service",
        "@apex//grace/ros/rclpy/rclpy",
        requirement("pyyaml"),
    ],
)

py_msgs_library(
    name = "rcl_interfaces_py",
    msgs = "@apex//grace/interfaces/rcl_interfaces",
)

apex_py_test(
    name = "test_api",
    srcs = [
        "test/test_api.py",
    ],
    deps = [
        ":rcl_interfaces_py",
        ":ros2param",
    ],
)

# ros2 cli target under test
configured_tool(
    name = "ros2",
    testonly = True,
    framework = "Apex.Grace",
    visibility = ["//visibility:private"],
    deps = [
        "//grace/cli:base_cli",
        "//grace/cli/ros2param",
    ],
)

executables_collection(
    name = "executables",
    testonly = True,
    executables = [":ros2"] + glob(["test/fixtures/*.py"]),
)

launch_test(
    name = "test_verb_dump",
    data = [":executables"],
    launch_test_file = "test/test_verb_dump.py",
    tags = [
        "constrained_test",
        # TODO: This test is flaky and skipped when the coverage measurement
        # is enabled. It should be fixed and enabled in #35616
        "skip_coverage",
    ],
    deps = [
        "@apex//grace/cli/ros2cli",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//grace/tools/launch_ros/launch_testing_ros",
        "@apex//tools/apex_pytest_utils",
        requirement("pytest"),
    ],
)

launch_test(
    name = "test_verb_list",
    data = [":executables"],
    launch_test_file = "test/test_verb_list.py",
    tags = [
        "constrained_test",
        # TODO: This test is flaky and skipped when the coverage measurement
        # is enabled. It should be fixed and enabled in #35616
        "skip_coverage",
    ],
    deps = [
        "@apex//grace/cli/ros2cli",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//grace/tools/launch_ros/launch_testing_ros",
        "@apex//tools/apex_pytest_utils",
        requirement("pytest"),
    ],
)

launch_test(
    name = "test_verb_load",
    data = [":executables"],
    launch_test_file = "test/test_verb_load.py",
    tags = [
        "constrained_test",
        # TODO: This test is flaky and skipped when the coverage measurement
        # is enabled. It should be fixed and enabled in #35616
        "skip_coverage",
    ],
    deps = [
        "@apex//grace/cli/ros2cli",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//grace/tools/launch_ros/launch_testing_ros",
        "@apex//tools/apex_pytest_utils",
        requirement("pytest"),
    ],
)

# bazelization_report ignore: test/test_copyright.py
# bazelization_report ignore: test/test_flake8.py
# bazelization_report ignore: test/test_pep257.py
# bazelization_report ignore: test/test_xmllint.py
