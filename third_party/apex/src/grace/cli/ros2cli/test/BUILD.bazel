load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")

py_msgs_library(
    name = "test_msgs_py",
    msgs = "@apex//grace/interfaces/test_msgs",
)

filegroup(
    name = "daemon_test_sources",
    srcs = ["test_daemon_ida.py"],
)

apex_py_test(
    name = "test_daemon",
    srcs = [":daemon_test_sources"],
    data = ["//grace/cli/ros2cli"],
    tags = [
        "constrained_test",
        "exclusive",
    ],
    deps = [
        ":test_msgs_py",
        "//grace/cli/ros2cli",
        "//grace/ros/rclpy/rclpy",
        "//ida/resource_creator:test_resource_creator_py",
        "//tools/apex_pytest_utils",
    ],
)

apex_py_test(
    name = "test_strategy",
    srcs = ["test_strategy.py"],
    data = ["//grace/cli/ros2cli"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "constrained_test",
        # TODO: This test is flaky and skipped when the coverage measurement
        # is enabled. It should be fixed and enabled in #35616
        "skip_coverage",
    ],
    deps = ["//grace/cli/ros2cli"],
)

# bazelization_report ignore: test_copyright.py
# bazelization_report ignore: test_flake8.py
# bazelization_report ignore: test_pep257.py
# bazelization_report ignore: test_xmllint.py
