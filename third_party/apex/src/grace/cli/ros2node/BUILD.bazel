load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_tool", "executables_collection")
load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")
load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test")
load("@rules_python//python:defs.bzl", "py_binary")

ENTRY_POINTS = {
    "ros2cli.command": [
        "node = ros2node.command.node:NodeCommand",
    ],
    "ros2cli.extension_point": [
        "ros2node.verb = ros2node.verb:VerbExtension",
    ],
    "ros2node.verb": [
        "info = ros2node.verb.info:InfoVerb",
        "list = ros2node.verb.list:ListVerb",
    ],
}

ros_pkg(
    name = "ros2node_pkg",
    description = "The node command for ROS 2 command line tools.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    py_libraries = [":ros2node"],
    version = "0.13.0",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Environment :: Console",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: Apache Software License",
        "Programming Language :: Python",
    ],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "@apex//grace/cli/ros2cli:ros2cli_pkg",
        "@apex//grace/ros/rclpy/rclpy:rclpy_pkg",
    ],
)

py_entry_points_library(
    name = "ros2node",
    srcs = glob(["ros2node/**/*.py"]),
    data = [":ros2node_pkg.wheel_data"],
    entry_points = ENTRY_POINTS,
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/cli/ros2cli",
        "@apex//grace/ros/rclpy/rclpy",
    ],
)

msgs_library(
    name = "ros2_msgs",
    ament_runfiles = True,
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/interfaces/test_msgs",
    ],
)

cpp_msgs_introspection_library(
    name = "ros2_msgs_introspection",
    ament_runfiles = True,
    msgs = ":ros2_msgs",
)

py_msgs_library(
    name = "ros2_msgs_py",
    msgs = ":ros2_msgs",
)

# ros2 cli target under test
configured_tool(
    name = "ros2",
    testonly = True,
    data = [":ros2_msgs_introspection"],
    framework = "Apex.Grace",
    visibility = ["//visibility:private"],
    deps = [
        # no sort ToDo #32755 remove this line
        "@apex//grace/cli:base_cli",
        "@apex//grace/cli/ros2node",
        ":ros2_msgs_py",
    ],
)

executables_collection(
    name = "executables",
    testonly = True,
    executables = [":ros2"] + glob(["test/fixtures/*.py"]),
)

py_binary(
    name = "complex_node",
    srcs = ["test/fixtures/complex_node.py"],
    main = "test/fixtures/complex_node.py",
    deps = [
        "@apex//grace/interfaces/test_msgs:test_msgs_py",
        "@apex//grace/ros/rclpy/rclpy",
    ],
)

launch_test(
    name = "test_cli",
    data = ["executables"],
    launch_test_file = "test/test_cli.py",
    tags = [
        "constrained_test",
        # TODO: This test is flaky and skipped when the coverage measurement
        # is enabled. It should be fixed and enabled in #35616
        "skip_coverage",
    ],
    deps = [
        "@apex//grace/interfaces/test_msgs:test_msgs_py",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//grace/tools/launch_ros/launch_testing_ros",
        "@apex//tools/apex_pytest_utils",
    ],
)

launch_test(
    name = "test_cli_duplicate_node_names",
    data = ["executables"],
    launch_test_file = "test/test_cli_duplicate_node_names.py",
    tags = [
        "constrained_test",
        # TODO: This test is flaky and skipped when the coverage measurement
        # is enabled. It should be fixed and enabled in #35616
        "skip_coverage",
    ],
    deps = [
        ":ros2node",
        "@apex//grace/interfaces/test_msgs:test_msgs_py",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//grace/tools/launch_ros/launch_testing_ros",
        "@apex//tools/apex_pytest_utils",
    ],
)

apex_py_test(
    name = "test_node",
    srcs = ["test/test_node.py"],
    deps = [":ros2node"],
)

py_msgs_library(
    name = "test_msgs_py",
    msgs = "@apex//grace/interfaces/test_msgs",
)

# bazelization_report ignore: test/test_copyright.py
# bazelization_report ignore: test/test_flake8.py
# bazelization_report ignore: test/test_pep257.py
# bazelization_report ignore: test/test_xmllint.py
