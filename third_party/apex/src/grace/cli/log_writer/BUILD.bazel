load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "log_writer",
    description = "LogWriter",
    lib_executables = [
        "log_writer_exe",
    ],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "log_writer",
    version = "0.0.0",
    visibility = ["//visibility:public"],
)

cc_library(
    name = "log_writer_lib",
    srcs = [
        "src/log_writer.cpp",
    ],
    hdrs = [
        "include/log_writer/log_writer.hpp",
        "include/log_writer/visibility_control.hpp",
    ],
    strip_include_prefix = "include",
    deps = [
        "//grace/execution/executor2",
        "//grace/interfaces/apex_msgs",
    ],
)

cc_binary(
    name = "log_writer_exe",
    srcs = [
        "src/log_writer_main.cpp",
    ],
    deps = [
        "log_writer_lib",
        "//common/interrupt",
        "//grace/execution/apex_init",
    ],
)

ament_pkg_resources(
    name = "log_writer_resources",
    package = "log_writer",
    resources = {
        ":log_writer_exe": "executable",
    },
    visibility = ["//visibility:public"],
)

apex_cc_test(
    name = "log_writer_test",
    srcs = glob([
        "test/*.cpp",
        "test/*.hpp",
    ]),
    env = {
        "APEX_GRACE_LOG_LEVEL": "TRACE",
        "IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc",
    },
    includes = ["test"],
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
    ],
    deps = [
        "log_writer_lib",
        "@googletest//:gtest",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "include/log_writer/log_writer.hpp",
    ],
    visibility = ["//grace/cli/log_writer/design:__subpackages__"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
