#! [load_typesupport_loader_cli_rules]
load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_tool")
#! [load_typesupport_loader_cli_rules]

load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_binary")

#! [load_typesupport_loader_cli_rules]
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library")
#! [load_typesupport_loader_cli_rules]

load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")

py_entry_points_binary(
    name = "_ros2_daemon",
    py_entry_points_library = "//grace/cli/ros2cli",
)

configured_tool(
    name = "base_cli",
    framework = "Apex.Grace",
    searchpath_executables = [":_ros2_daemon"],
    visibility = ["//visibility:public"],
    deps = ["//grace/cli/ros2cli"],
)

# Wrap the required interfaces in a msgs_library with the ament
# resources enabled
msgs_library(
    name = "cli_interfaces_with_ament_resources",
    ament_runfiles = True,
    deps = ["//grace/interfaces"],
)

py_msgs_library(
    name = "py_cli_interfaces",
    msgs = ":cli_interfaces_with_ament_resources",
)

# Also include the cpp shared libraries in the runfiles for the CLI
cpp_msgs_introspection_library(
    name = "cpp_cli_interfaces",
    ament_runfiles = True,
    msgs = ":cli_interfaces_with_ament_resources",
)

configured_tool(
    name = "cli",
    data = [
        ":cpp_cli_interfaces",
        "//grace/recording/rosbag2/rosbag2_compression_zstd:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    framework = "Apex.Grace",
    visibility = ["//visibility:public"],
    deps = [
        ":base_cli",
        ":py_cli_interfaces",
        "//grace/cli/ros2action",
        "//grace/cli/ros2component",
        "//grace/cli/ros2interface",
        "//grace/cli/ros2node",
        "//grace/cli/ros2pkg",
        "//grace/cli/ros2run",
        "//grace/cli/ros2topic",
        "//grace/recording/rosbag2/ros2bag",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:ros2bag_mcap_cli",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:ros2bag_sqlite3_cli",
        "//grace/tools/launch_ros/ros2launch",
        "//grace/tools/ros2_tracing/ros2trace",
    ],
)

#! [declare_typesupport_loader_library]
# Set `is_typesupport_loader` to True to declare this target as the libtypesupport_loader.so
# with all the interfaces needed
cpp_msgs_introspection_library(
    name = "cpp_cli_typesupport_loader",
    ament_runfiles = True,
    is_typesupport_loader = True,  # (1)!
    msgs = "@apex//grace/interfaces",  # (2)!
    tags = ["exclude_sca"],
)
#! [declare_typesupport_loader_library]

#! [use_typesupport_loader_library_in_cli]
# Note: only one target can have `is_typesupport_loader` set to True
# in the dependency chain
configured_tool(
    name = "loader_cli",
    data = [":cpp_cli_typesupport_loader"],
    framework = "Apex.Grace",
    visibility = ["//visibility:public"],
    deps = [":cli"],  # (1)!
)
#! [use_typesupport_loader_library_in_cli]

filegroup(
    name = "doc_files",
    srcs = ["BUILD.bazel"],
    visibility = ["//grace/doc/communication:__pkg__"],
)
