load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_tool", "executables_collection")
load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test")

ENTRY_POINTS = {
    "ros2cli.command": [
        "lifecycle = ros2lifecycle.command.lifecycle:LifecycleCommand",
    ],
    "ros2cli.extension_point": [
        "ros2lifecycle.verb = ros2lifecycle.verb:VerbExtension",
    ],
    "ros2lifecycle.verb": [
        "get = ros2lifecycle.verb.get:GetVerb",
        "list = ros2lifecycle.verb.list:ListVerb",
        "nodes = ros2lifecycle.verb.nodes:NodesVerb",
        "set = ros2lifecycle.verb.set:SetVerb",
    ],
}

ros_pkg(
    name = "ros2lifecycle_pkg",
    description = "The lifecycle command for ROS 2 command line tools.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    py_libraries = [":ros2lifecycle"],
    version = "0.13.0",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Environment :: Console",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: Apache Software License",
        "Programming Language :: Python",
    ],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "@apex//grace/cli/ros2cli:ros2cli_pkg",
        "@apex//grace/cli/ros2node:ros2node_pkg",
        "@apex//grace/cli/ros2service:ros2service_pkg",
        "@apex//grace/interfaces/lifecycle_msgs:lifecycle_msgs_pkg",
        "@apex//grace/ros/rclpy/rclpy:rclpy_pkg",
    ],
)

py_msgs_library(
    name = "lifecycle_msgs_py",
    msgs = "@apex//grace/interfaces/lifecycle_msgs:lifecycle_msgs",
)

py_entry_points_library(
    name = "ros2lifecycle",
    srcs = glob(["ros2lifecycle/**/*.py"]),
    data = [":ros2lifecycle_pkg.wheel_data"],
    entry_points = ENTRY_POINTS,
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        ":lifecycle_msgs_py",
        "@apex//grace/cli/ros2cli",
        "@apex//grace/cli/ros2node",
        "@apex//grace/cli/ros2service",
        "@apex//grace/ros/rclpy/rclpy",
    ],
)

# ros2 cli target under test
configured_tool(
    name = "ros2",
    testonly = True,
    framework = "Apex.Grace",
    visibility = ["//visibility:private"],
    deps = [
        ":ros2lifecycle",
        "//grace/cli:base_cli",
    ],
)

executables_collection(
    name = "executables",
    testonly = True,
    executables = [":ros2"],
)

launch_test(
    name = "test_cli",
    data = [
        ":executables",
        "//grace/cli/ros2lifecycle_test_fixtures:ament_resources",
    ],
    launch_test_file = "test/test_cli.py",
    tags = [
        "constrained_test",
        # TODO: This test is flaky and skipped when the coverage measurement
        # is enabled. It should be fixed and enabled in #35616
        "skip_coverage",
    ],
    deps = [
        "@apex//grace/ros/rclpy/rclpy",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//grace/tools/launch_ros/launch_testing_ros",
        "@apex//tools/apex_pytest_utils",
    ],
)

# bazelization_report ignore: test/test_copyright.py
# bazelization_report ignore: test/test_flake8.py
# bazelization_report ignore: test/test_pep257.py
# bazelization_report ignore: test/test_xmllint.py
