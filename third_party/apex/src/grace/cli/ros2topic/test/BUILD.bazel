load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_tool", "executables_collection")
load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test")
load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_binary")
load("@rules_python//python:defs.bzl", "py_library")

msgs_library(
    name = "ros2_msgs",
    ament_runfiles = True,
    deps = [
        "@apex//grace/interfaces/geometry_msgs",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/interfaces/test_msgs",
    ],
)

cpp_msgs_introspection_library(
    name = "ros2_msgs_introspection",
    ament_runfiles = True,
    msgs = "ros2_msgs",
)

py_msgs_library(
    name = "ros2_msgs_py",
    msgs = ":ros2_msgs",
)

# ros2 cli target under test
configured_tool(
    name = "ros2",
    testonly = True,
    data = [":ros2_msgs_introspection"],
    framework = "Apex.Grace",
    visibility = ["//visibility:private"],
    deps = [
        ":ros2_msgs_py",
        "@apex//grace/cli:base_cli",
        "@apex//grace/cli/ros2topic",
    ],
)

executables_collection(
    name = "executables",
    testonly = True,
    executables = [":ros2"],
)

py_library(
    name = "fixtures",
    srcs = glob(["fixtures/*.py"]),
    deps = [":ros2_msgs_py"],
)

# For testing purpose only
py_binary(
    name = "talker_node",
    srcs = ["fixtures/talker_node.py"],
    main = "fixtures/talker_node.py",
    deps = [
        ":ros2_msgs_py",
        "@apex//grace/ros/rclpy/rclpy",
    ],
)

# For testing purpose only
py_binary(
    name = "repeater_node",
    srcs = ["fixtures/repeater_node.py"],
    main = "fixtures/repeater_node.py",
    deps = [
        ":ros2_msgs_py",
        "@apex//grace/ros/rclpy/rclpy",
        "@apex//grace/rosidl/rosidl_runtime_py",
    ],
)

# For testing purpose only
py_binary(
    name = "listener_node",
    srcs = ["fixtures/listener_node.py"],
    main = "fixtures/listener_node.py",
    deps = [
        ":ros2_msgs_py",
        "@apex//grace/ros/rclpy/rclpy",
    ],
)

# For testing purpose only

py_binary(
    name = "controller_node",
    srcs = ["fixtures/controller_node.py"],
    main = "fixtures/controller_node.py",
    deps = [
        ":ros2_msgs_py",
        "@apex//grace/ros/rclpy/rclpy",
    ],
)

launch_test(
    name = "test_cli",
    data = [":executables"],
    launch_test_file = "test_cli.py",
    tags = [
        "constrained_test",
    ],
    deps = [
        ":fixtures",
        requirement("pytest"),
        "@apex//grace/ros/rclpy/rclpy",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//grace/tools/launch_ros/launch_testing_ros",
        "@apex//tools/apex_pytest_utils",
        "@apex//tools/launch/launch",
    ],
)

py_msgs_library(
    name = "test_msgs_py",
    msgs = "@apex//grace/interfaces/test_msgs",
)

py_msgs_library(
    name = "std_msgs_py",
    msgs = "@apex//grace/interfaces/std_msgs",
)

launch_test(
    name = "test_echo_pub",
    data = [":executables"],
    env = {"APEX_IDA_LOG_LEVEL": "INFO"},
    launch_test_file = "test_echo_pub.py",
    tags = ["constrained_test"],
    deps = [
        requirement("pytest"),
        ":std_msgs_py",
        ":test_msgs_py",
        "@apex//grace/ros/rclpy/rclpy",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//grace/tools/launch_ros/launch_testing_ros",
        "@apex//tools/apex_pytest_utils",
        "@apex//tools/launch/launch",
    ],
)

apex_py_test(
    name = "test_info",
    srcs = ["test_info.py"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = ["constrained_test"],
    deps = [
        "//grace/cli/ros2topic",
    ],
)

# This is for manual verification, see https://github.com/ros2/ros2cli/pull/238
py_binary(
    name = "qos_subscription",
    testonly = True,
    srcs = ["qos_subscription.py"],
    deps = [
        ":std_msgs_py",
        "@apex//grace/ros/rclpy/rclpy",
    ],
)

apex_py_test(
    name = "test_qos_conversions",
    srcs = ["test_qos_conversions.py"],
    deps = [
        "//grace/cli/ros2topic",
    ],
)

# bazelization_report ignore: test_copyright.py
# bazelization_report ignore: test_flake8.py
# bazelization_report ignore: test_pep257.py
# bazelization_report ignore: test_xmllint.py
