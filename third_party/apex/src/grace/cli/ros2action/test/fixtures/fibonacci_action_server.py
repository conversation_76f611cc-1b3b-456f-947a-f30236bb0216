#!/usr/bin/env python3
# Copyright 2019 Open Source Robotics Foundation, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.import time

import sys
import rclpy
from rclpy.action import ActionServer
from rclpy.node import Node
from rclpy.qos import qos_profile_action_status_default

from test_msgs.action import Fibonacci


class FibonacciActionServer(Node):

    def __init__(self):
        super().__init__('fibonacci_action_server')
        status_pub_qos_profile=qos_profile_action_status_default
        status_pub_qos_profile.max_non_self_contained_type_serialized_size = 256

        self._action_server = ActionServer(
            self,
            <PERSON><PERSON><PERSON><PERSON>,
            'fibon<PERSON><PERSON>',
            self.execute_callback,
            status_pub_qos_profile=status_pub_qos_profile)

    def destroy_node(self):
        self._action_server.destroy()
        super().destroy_node()

    def execute_callback(self, goal_handle):
        self.get_logger().info(f'Received goal request with order: {goal_handle.request.order}')

        # Validate the goal
        if goal_handle.request.order < 0:
            self.get_logger().info('Received invalid goal request: order < 0')
            goal_handle.abort()
            return Fibonacci.Result()

        feedback = Fibonacci.Feedback()
        feedback.sequence = [0, 1]

        # Handle small orders
        if goal_handle.request.order <= 1:
            feedback.sequence = [0]

        # Generate Fibonacci sequence
        for i in range(1, goal_handle.request.order):
            feedback.sequence.append(feedback.sequence[i] + feedback.sequence[i-1])
            self.get_logger().info(f'Publishing feedback: {feedback.sequence}')
            goal_handle.publish_feedback(feedback)

        # Mark the goal as succeeded
        goal_handle.succeed()

        result = Fibonacci.Result()
        result.sequence = feedback.sequence
        self.get_logger().info(f'Goal succeeded with result: {result.sequence}')
        return result

def main(args=None):
    rclpy.init(args=args)

    node = FibonacciActionServer()
    print(f"The node is: {node}")
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        print('Server stopped cleanly')
    except BaseException:
        print('Exception in server:', file=sys.stderr)
        raise
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
