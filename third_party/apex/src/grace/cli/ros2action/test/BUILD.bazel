load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_tool", "executables_collection")
load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test")
load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_binary", "py_library")

msgs_library(
    name = "test_cli_interfaces",
    deps = [
        "//grace/interfaces/geometry_msgs",
        "//grace/interfaces/std_msgs",
        "//grace/interfaces/test_msgs",
    ],
)

cpp_msgs_introspection_library(
    name = "cpp_test_cli_interfaces",
    msgs = ":test_cli_interfaces",
)

py_msgs_library(
    name = "py_test_cli_interfaces",
    msgs = ":test_cli_interfaces",
)

# ros2 cli target under test
configured_tool(
    name = "ros2",
    testonly = True,
    data = [":cpp_test_cli_interfaces"],
    framework = "Apex.Grace",
    visibility = ["//visibility:private"],
    deps = [
        ":py_test_cli_interfaces",
        "@apex//grace/cli:base_cli",
        "@apex//grace/cli/ros2action",
    ],
)

executables_collection(
    name = "executables",
    testonly = True,
    executables = [":ros2"],
)

py_library(
    name = "fixtures",
    srcs = glob(["fixtures/*.py"]),
    deps = [":py_test_cli_interfaces"],
)

py_binary(
    name = "fibonacci_action_server",
    srcs = ["fixtures/fibonacci_action_server.py"],
    main = "fixtures/fibonacci_action_server.py",
    deps = [
        ":py_test_cli_interfaces",
        "@apex//grace/ros/rclpy/rclpy",
    ],
)

launch_test(
    name = "test_cli",
    data = [":executables"],
    launch_test_file = "test_cli.py",
    tags = [
        "constrained_test",
        # TODO: This test is flaky and skipped when the coverage measurement
        # is enabled. It should be fixed and enabled in #35616
        "skip_coverage",
    ],
    deps = [
        ":fixtures",
        requirement("pytest"),
        "@apex//grace/ros/rclpy/rclpy",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//grace/tools/launch_ros/launch_testing_ros",
        "@apex//tools/apex_pytest_utils",
        "@apex//tools/launch/launch",
    ],
)

apex_py_test(
    name = "test_api",
    srcs = ["test_api.py"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = ["constrained_test"],
    deps = [
        "//grace/cli/ros2action",
        "@apex//grace/tools/ros_domain_coordinator:ros_domain_coordinator_py",
    ],
)

# bazelization_report ignore: test_copyright.py
# bazelization_report ignore: test_flake8.py
# bazelization_report ignore: test_pep257.py
# bazelization_report ignore: test_xmllint.py
