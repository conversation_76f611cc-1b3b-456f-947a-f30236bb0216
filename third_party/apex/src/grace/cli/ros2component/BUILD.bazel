load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")
load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ENTRY_POINTS = {
    "ros2cli.command": [
        "component = ros2component.command.component:ComponentCommand",
    ],
    "ros2cli.extension_point": [
        "ros2component.verb = ros2component.verb:VerbExtension",
    ],
    "ros2component.verb": [
        "list = ros2component.verb.list:ListVerb",
        "load = ros2component.verb.load:LoadVerb",
        "standalone = ros2component.verb.standalone:StandaloneVerb",
        "types = ros2component.verb.types:TypesVerb",
        "unload = ros2component.verb.unload:UnloadVerb",
    ],
}

ros_pkg(
    name = "ros2component_pkg",
    description = "The component command for ROS 2 command line tools.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    py_libraries = [":ros2component"],
    version = "0.13.0",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Environment :: Console",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: Apache Software License",
        "Programming Language :: Python",
    ],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "@apex//grace/cli/ros2cli:ros2cli_pkg",
        "@apex//grace/cli/ros2node:ros2node_pkg",
        "@apex//grace/cli/ros2param:ros2param_pkg",
        "@apex//grace/cli/ros2pkg:ros2pkg_pkg",
        "@apex//grace/interfaces/composition_interfaces:composition_interfaces_pkg",
        "@apex//grace/ros/rclpy/rclpy:rclpy_pkg",
        "@apex//tools/ament/ament_index/ament_index_python:ament_index_python_pkg",
    ],
)

py_msgs_library(
    name = "composition_interfaces_py",
    msgs = "//grace/interfaces/composition_interfaces",
)

py_entry_points_library(
    name = "ros2component",
    srcs = glob(["ros2component/**/*.py"]),
    data = [":ros2component_pkg.wheel_data"],
    entry_points = ENTRY_POINTS,
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        ":composition_interfaces_py",
        "@apex//grace/cli/ros2cli",
        "@apex//grace/cli/ros2node",
        "@apex//grace/cli/ros2param",
        "@apex//grace/cli/ros2pkg",
        "@apex//grace/ros/rclpy/rclpy",
        "@apex//tools/ament/ament_index/ament_index_python",
    ],
)

apex_py_test(
    name = "test_api",
    srcs = ["test/test_api.py"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = ["constrained_test"],
    deps = [
        ":ros2component",
        "@apex//grace/cli/ros2cli",
        "@apex//grace/cli/ros2node",
    ],
)

# bazelization_report ignore: test/test_copyright.py
# bazelization_report ignore: test/test_flake8.py
# bazelization_report ignore: test/test_pep257.py
# bazelization_report ignore: test/test_xmllint.py
