load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_tool", "executables_collection")
load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test")
load("@python_vendor//:requirements.bzl", "requirement")

ENTRY_POINTS = {
    "ros2cli.command": [
        "service = ros2service.command.service:ServiceCommand",
    ],
    "ros2cli.extension_point": [
        "ros2service.verb = ros2service.verb:VerbExtension",
    ],
    "ros2service.verb": [
        "call = ros2service.verb.call:CallVerb",
        "find = ros2service.verb.find:FindVerb",
        "list = ros2service.verb.list:ListVerb",
        "type = ros2service.verb.type:TypeVerb",
    ],
}

ros_pkg(
    name = "ros2service_pkg",
    description = "The service command for ROS 2 command line tools.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    py_libraries = [":ros2service"],
    version = "0.13.0",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Environment :: Console",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: Apache Software License",
        "Programming Language :: Python",
    ],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "@apex//grace/cli/ros2cli:ros2cli_pkg",
        "@apex//grace/ros/rclpy/rclpy:rclpy_pkg",
        "@apex//grace/rosidl/rosidl_runtime_py:rosidl_runtime_py_pkg",
    ],
)

py_entry_points_library(
    name = "ros2service",
    srcs = glob(["ros2service/**/*.py"]),
    data = [":ros2service_pkg.wheel_data"],
    entry_points = ENTRY_POINTS,
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/cli/ros2cli",
        "@apex//grace/ros/rclpy/rclpy",
        "@apex//grace/rosidl/rosidl_runtime_py",
        requirement("pyyaml"),
    ],
)

py_msgs_library(
    name = "test_msgs_py",
    msgs = "@apex//grace/interfaces/test_msgs",
)

# ros2 cli target under test
configured_tool(
    name = "ros2",
    testonly = True,
    framework = "Apex.Grace",
    visibility = ["//visibility:private"],
    deps = [
        ":ros2service",
        "//grace/cli:base_cli",
    ],
)

executables_collection(
    name = "executables",
    testonly = True,
    executables = [":ros2"] + glob(["test/fixtures/*.py"]),
)

launch_test(
    name = "test_cli",
    data = [":executables"],
    launch_test_file = "test/test_cli.py",
    tags = [
        "constrained_test",
        # TODO: This test is flaky and skipped when the coverage measurement
        # is enabled. It should be fixed and enabled in #35616
        "skip_coverage",
    ],
    deps = [
        requirement("pytest"),
        ":test_msgs_py",
        "@apex//grace/ros/rclpy/rclpy",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//grace/tools/launch_ros/launch_testing_ros",
        "@apex//tools/apex_pytest_utils",
        "@apex//tools/launch/launch",
    ],
)

# bazelization_report ignore: test/test_copyright.py
# bazelization_report ignore: test/test_flake8.py
# bazelization_report ignore: test/test_pep257.py
# bazelization_report ignore: test/test_xmllint.py
