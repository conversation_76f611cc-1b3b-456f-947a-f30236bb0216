load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_python//python:defs.bzl", "py_library")

ros_pkg(
    name = "ros2topic_cpp_pkg",
    cc_libraries = [
        ":ros2topic_cpp_common",
        ":ros2topic_py",
    ],
    description = "Implementation of ros2topic in C++",
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "ros2topic_cpp",
    py_libraries = [":ros2topic_cpp"],
    version = "0.0.1",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Intended Audience :: Developers",
        "Programming Language :: Python",
    ],
    deps = [
        "//common/interrupt:interrupt_pkg",
        "//grace/execution/executor2:executor2_pkg",
        "//grace/monitoring/logging:logging_pkg",
        "//grace/tools/dynamic_message_introspection/dynmsg:dynmsg_pkg",
        "//grace/utils/apexcpp:apexcpp_pkg",
    ],
)

apex_cc_library(
    name = "ros2topic_cpp_common",
    srcs = glob(
        ["src/**/*.cpp"],
        exclude = ["src/ros2topic_python.cpp"],
    ),
    hdrs = glob(["include/**/*.hpp"]),
    strip_include_prefix = "include",
    tags = [
        "exclude_sca",
        "same-ros-pkg-as: ros2topic_cpp",
    ],
    deps = [
        "//common/interrupt",
        "//grace/execution/executor2",
        "//grace/monitoring/logging",
        "//grace/tools/dynamic_message_introspection/dynmsg",
        "//grace/utils/apexcpp",
    ],
)

apex_cc_library(
    name = "ros2topic_py",
    srcs = ["src/ros2topic_python.cpp"],
    # TODO(32040)
    shared_lib_name = "ros2topic_cpp/_ros2topic_py.so",
    tags = [
        "Python extension module",
        "exclude_sca",
    ],
    deps = [
        ":ros2topic_cpp_common",
        "//tools/thirdparty/python_vendor:python_headers",
    ],
)

apex_cc_shared_library(
    name = "ros2topic_py_shared",
    apex_cc_library = ":ros2topic_py",
)

py_library(
    name = "ros2topic_cpp",
    srcs = glob([
        "ros2topic_cpp/**/*.py",
    ]),
    data = [
        ":ros2topic_cpp_pkg.wheel_data",
        ":ros2topic_py_shared",
    ],
    imports = ["."],
    visibility = ["//visibility:public"],
)
