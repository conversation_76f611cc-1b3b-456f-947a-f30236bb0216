// Copyright 2022 Apex.AI
// All rights reserved.

#ifndef ROS2TOPIC_CPP__ROS2TOPIC_ECHO_NODE_HPP_
#define ROS2TOPIC_CPP__ROS2TOPIC_ECHO_NODE_HPP_

#include <executor2/apex_node_base.hpp>
#include <logging/logging_macros.hpp>

#include <cmath>
#include <cstdint>
#include <string>
#include <utility>
#include <memory>
#include <vector>
#include <unordered_map>

#include "dynmsg/typesupport.hpp"
#include "rclcpp/generic_polling_subscription.hpp"
#include "rclcpp/dynamic_waitset/waitset.hpp"
#include "rcpputils/shared_library.hpp"
#include "ros2topic_cpp/echo_options.hpp"
#include "ros2topic_cpp/hz_options.hpp"
#include "ros2topic_cpp/visibility_control.hpp"

namespace ros2topic_cpp
{

class ROS2TOPIC_CPP_PUBLIC Ros2topicEchoNode : public apex::executor::apex_node_base
{
public:
  using apex::executor::apex_node_base::get_rclcpp_node;
  Ros2topicEchoNode(
    const apex::string_strict256_t & node_name,
    const apex::string_strict256_t & node_namespace,
    const EchoOptions & options);

  void create_subscription(
    const std::string & topic_name,
    const std::string & topic_type,
    const rclcpp::QoS & qos);

private:
  bool execute_impl() override;
  apex::executor::subscription_list get_triggering_subscriptions_impl() const override;
  void handle_echo_msgs(const rmw_serialized_message_t & msg);

  EchoOptions m_echoOptions;
  std::shared_ptr<rclcpp::GenericPollingSubscription> m_topicSubscription;
  const TypeInfo_Cpp * type_info_;
  std::shared_ptr<rcpputils::SharedLibrary> type_info_lib_;
  const rosidl_message_type_support_t * deser_type_support_;
  std::shared_ptr<rcpputils::SharedLibrary> deser_type_support_lib_;
};

}  // namespace ros2topic_cpp

#endif  // ROS2TOPIC_CPP__ROS2TOPIC_ECHO_NODE_HPP_
