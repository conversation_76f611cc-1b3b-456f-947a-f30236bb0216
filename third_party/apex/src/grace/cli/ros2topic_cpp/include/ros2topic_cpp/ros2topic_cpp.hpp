// Copyright 2022 Apex.AI
// All rights reserved.

/// \file
/// \brief This file defines the ros2topic_cpp class.

#ifndef ROS2TOPIC_CPP__ROS2TOPIC_CPP_HPP_
#define ROS2TOPIC_CPP__ROS2TOPIC_CPP_HPP_

#include <ros2topic_cpp/visibility_control.hpp>
#include <iostream>
#include <string>
#include <utility>

#include "rclcpp/rclcpp.hpp"

namespace ros2topic_cpp
{

void ROS2TOPIC_CPP_PUBLIC init();

void ROS2TOPIC_CPP_PUBLIC shutdown();

ROS2TOPIC_CPP_PUBLIC
apex::optional<std::pair<std::string, std::string>>
get_requested_topic(const rclcpp::Node & node, const std::string & topic_name);

ROS2TOPIC_CPP_PUBLIC
apex::optional<std::pair<std::string, std::string>>
wait_for_requested_topic(
  const rclcpp::Node & node, const std::string & topic_name, std::chrono::seconds timeout);

std::pair<const rosidl_message_type_support_t *, const std::shared_ptr<rcpputils::SharedLibrary>>
get_typesupport_handle(const std::string & type_name);

std::pair<std::string, std::string>
get_interface_typename(const std::string & namespaced_message_type);

ROS2TOPIC_CPP_PUBLIC
std::string
convert_message_type_from_options(const std::string & namespace_message_type);

}  // namespace ros2topic_cpp

#endif  // ROS2TOPIC_CPP__ROS2TOPIC_CPP_HPP_
