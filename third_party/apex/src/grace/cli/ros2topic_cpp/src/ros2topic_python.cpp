// Copyright 2022 Apex.AI
// All rights reserved.

#include <Python.h>
#include <chrono>
#include <string>
#include <vector>
#include <memory>
#include <utility>
#include <unordered_map>

#include "ros2topic_cpp/echo_options.hpp"
#include "ros2topic_cpp/hz_options.hpp"
#include "ros2topic_cpp/ros2topic_echo_node.hpp"
#include "ros2topic_cpp/ros2topic_hz_node.hpp"
#include "ros2topic_cpp/ros2topic_cpp.hpp"
#include "ros2topic_cpp/ros2topic_echo_qos.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "interrupt/interrupt_handler.hpp"

using namespace std::chrono_literals;

static PyObject *
ros2topic_cpp_echo(PyObject * Py_UNUSED(self), PyObject * args, PyObject * kwargs)
{
  ros2topic_cpp::EchoOptions options{};

  static const char * kwlist[] = {
    "topic_name",
    "message_type",
    "csv",
    "full_length",
    "truncate_length",
    "qos_reliability",
    "qos_durability",
    "qos_profile",
    "qos_max_allocated_samples",
    "qos_max_dyn_serialized_size",
    nullptr
  };

  char * topic_name;
  char * message_type;
  bool csv = false;
  bool full_length = false;
  size_t truncate_length;
  char * qos_reliability;
  char * qos_durability;
  char * qos_profile;
  size_t qos_max_allocated_samples;
  size_t qos_max_dyn_serialized_size;

  if (!PyArg_ParseTupleAndKeywords(
      args, kwargs, "ss|bbKsssKK", const_cast<char **>(kwlist),
      &topic_name,
      &message_type,
      &csv,
      &full_length,
      &truncate_length,
      &qos_reliability,
      &qos_durability,
      &qos_profile,
      &qos_max_allocated_samples,
      &qos_max_dyn_serialized_size))
  {
    return nullptr;
  }

  options.topic_name = std::string(topic_name);
  options.message_type = std::string(message_type);
  options.csv = csv;
  options.full_length = full_length;
  options.truncate_length = truncate_length;
  std::string qos_reliability_str(qos_reliability);
  std::string qos_durability_str(qos_durability);
  std::string qos_profile_str(qos_profile);

  auto qos = *ros2topic_qos::get_qos_param(
    qos_profile_str,
    qos_reliability_str,
    qos_durability_str,
    qos_max_allocated_samples,
    qos_max_dyn_serialized_size);

  ros2topic_cpp::init();
  const apex::interrupt_handler::installer interrupt_handler_installer{};

  try {
    auto node =
      std::make_shared<ros2topic_cpp::Ros2topicEchoNode>("_ros2cli", "", options);

    apex::optional<std::pair<std::string, std::string>> topics_and_type;
    if (options.message_type.empty()) {
      topics_and_type =
        ros2topic_cpp::wait_for_requested_topic(node->get_rclcpp_node(), options.topic_name, 1s);
    } else {
      auto converted_message_type = ros2topic_cpp::convert_message_type_from_options(
        options.message_type);
      topics_and_type = std::make_pair(options.topic_name, converted_message_type);
    }
    if (!topics_and_type) {
      std::cout << "Could not determine the type for the passed topic" << std::endl;
      Py_RETURN_NONE;
    }

    node->create_subscription(topics_and_type->first, topics_and_type->second, qos);

    const auto executor = apex::executor::executor_factory::create();
    executor->add(std::move(node));
    const apex::executor::executor_runner runner{
      apex::executor::executor_runner::deferred | apex::executor::executor_runner::interrupt,
      *executor};
    runner.issue();
    apex::interrupt_handler::wait();
  } catch (const std::exception & e) {
    std::cerr << e.what() << "\n";
  } catch (...) {
    std::cerr << "Unknown error occurred" << "\n";
  }
  ros2topic_cpp::shutdown();
  Py_RETURN_NONE;
}

static PyObject *
ros2topic_cpp_hz(PyObject * Py_UNUSED(self), PyObject * args, PyObject * kwargs)
{
  ros2topic_cpp::HzOptions options{};

  static const char * kwlist[] = {
    "topic_name",
    "window",
    "filter",
    "wall_time",
    nullptr
  };

  char * topic_name;
  size_t window;
  char * filter;
  bool wall_time = false;

  if (!PyArg_ParseTupleAndKeywords(
      args, kwargs, "s|Ksb", const_cast<char **>(kwlist),
      &topic_name,
      &window,
      &filter,
      &wall_time))
  {
    return nullptr;
  }

  options.topic_name = std::string(topic_name);
  options.window = window;
  options.filter = std::string(filter);
  options.wall_time = wall_time;

  ros2topic_cpp::init();
  const apex::interrupt_handler::installer interrupt_handler_installer{};

  try {
    auto node =
      std::make_shared<ros2topic_cpp::Ros2topicHzNode>("_ros2cli", "", options);

    apex::optional<std::pair<std::string, std::string>> topics_and_type{apex::nullopt};
    std::once_flag print_warning_once;
    while (!apex::interrupt_handler::poll()) {
      topics_and_type = ros2topic_cpp::get_requested_topic(
        node->get_rclcpp_node(), options.topic_name);
      if (topics_and_type) {
        break;
      }
      std::call_once(print_warning_once, [&options]() {
          printf("WARNING: topic [%s] does not appear to be published yet\n",
          options.topic_name.c_str());
        });
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    if (!topics_and_type) {
      Py_RETURN_NONE;
    }

    node->create_subscription(topics_and_type->first, topics_and_type->second);

    const auto executor = apex::executor::executor_factory::create();
    executor->add(std::move(node));
    const apex::executor::executor_runner runner{
      apex::executor::executor_runner::deferred | apex::executor::executor_runner::interrupt,
      *executor};
    runner.issue();
    apex::interrupt_handler::wait();
  } catch (const std::exception & e) {
    std::cerr << e.what() << "\n";
  } catch (...) {
    std::cerr << "Unknown error occurred" << "\n";
  }
  ros2topic_cpp::shutdown();
  Py_RETURN_NONE;
}

/// Define the public methods of this module
#if __GNUC__ >= 8
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wcast-function-type"
#endif
static PyMethodDef ros2topic_methods[] = {
  {
    "echo", reinterpret_cast<PyCFunction>(ros2topic_cpp_echo), METH_VARARGS | METH_KEYWORDS,
    "Output messages from a topic"
  },
  {
    "hz", reinterpret_cast<PyCFunction>(ros2topic_cpp_hz), METH_VARARGS | METH_KEYWORDS,
    "Print the average publishing rate"
  },
  {nullptr, nullptr, 0, nullptr}  /* sentinel */
};
#if __GNUC__ >= 8
# pragma GCC diagnostic pop
#endif

PyDoc_STRVAR(ros2topic__doc__,
  "Python module for ros2topic");

/// Define the Python module
static struct PyModuleDef _ros2topic_module = {
  PyModuleDef_HEAD_INIT,
  "_ros2topic",
  ros2topic__doc__,
  -1,   /* -1 means that the module keeps state in global variables */
  ros2topic_methods,
  nullptr,
  nullptr,
  nullptr,
  nullptr
};

/// Init function of this module
PyMODINIT_FUNC PyInit__ros2topic_py(void)
{
  return PyModule_Create(&_ros2topic_module);
}
