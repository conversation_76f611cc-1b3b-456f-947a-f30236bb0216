// Copyright 2022 Apex.AI
// All rights reserved.

#include <yaml-cpp/yaml.h>

#include <string>
#include <utility>
#include <memory>

#include "dynmsg/message_reading.hpp"
#include "dynmsg/msg_parser.hpp"
#include "dynmsg/yaml_utils.hpp"

#include "ros2topic_cpp/ros2topic_echo_node.hpp"
#include "ros2topic_cpp/ros2topic_hz.hpp"
#include "ros2topic_cpp/ros2topic_cpp.hpp"


using namespace std::chrono_literals;

namespace
{

void print_csv_message(const RosMessage_Cpp & msg, bool full_length, size_t truncate_length)
{
  std::string csv_msg;

  // when full_length is set to true the truncate_length is ignored and set to 0 (no truncation)
  csv_msg = dynmsg::cpp::message_to_csv(msg, full_length ? dynmsg::cpp::NO_LENGTH :
      truncate_length);
  std::cout << csv_msg << std::endl;
}

void print_yaml_message(const RosMessage_Cpp & msg, bool full_length, size_t truncate_length)
{
  YAML::Node yaml_msg;

  // when full_length is set to true the truncate_length is ignored and set to 0 (no truncation)
  yaml_msg = dynmsg::cpp::message_to_yaml(msg, full_length ? dynmsg::cpp::NO_LENGTH :
      truncate_length);
  // Convert the YAML representation to a string
  const std::string yaml_string = dynmsg::yaml_to_string(yaml_msg);
  std::cout << yaml_string << std::endl;
  std::cout << "---" << std::endl;
}
}  // namespace


namespace ros2topic_cpp
{

Ros2topicEchoNode::Ros2topicEchoNode(
  const apex::string_strict256_t & node_name,
  const apex::string_strict256_t & node_namespace,
  const EchoOptions & options)
: apex::executor::apex_node_base{node_name, node_namespace},
  m_echoOptions({options.topic_name, options.message_type, options.csv, options.full_length,
      options.truncate_length, options.qos_reliability, options.qos_durability,
      options.qos_profile})
{}

void Ros2topicEchoNode::create_subscription(
  const std::string & topic_name,
  const std::string & topic_type,
  const rclcpp::QoS & qos)
{
  m_topicSubscription = get_rclcpp_node().create_generic_polling_subscription(
    topic_name,
    topic_type,
    qos);

  // For message introspection (YAML/CSV)
  std::tie(type_info_, type_info_lib_) = dynmsg::cpp::get_type_info(topic_type);

  // For message de-serialization
  std::tie(deser_type_support_, deser_type_support_lib_) = get_typesupport_handle(topic_type);
}

bool Ros2topicEchoNode::execute_impl()
{
  auto msgs = m_topicSubscription->take_serialized();
  for (const auto & msg : msgs) {
    if (msg) {
      handle_echo_msgs(*msg);
    }
  }
  return true;
}

apex::executor::subscription_list Ros2topicEchoNode::get_triggering_subscriptions_impl() const
{
  return {m_topicSubscription};
}

void Ros2topicEchoNode::handle_echo_msgs(const rmw_serialized_message_t & msg)
{
  RosMessage_Cpp ros_msg;

  // init ros message
  rcutils_allocator_t default_allocator = rcutils_get_default_allocator();

  // Load the introspection information and allocate space for the ROS message's binary
  // representation
  if (DYNMSG_RET_OK !=
    dynmsg::cpp::ros_message_with_typeinfo_init(type_info_, &ros_msg, &default_allocator))
  {
    return;
  }

  // deserialize message
  if (rmw_deserialize(&msg, deser_type_support_, ros_msg.data) == RMW_RET_OK) {
    if (m_echoOptions.csv) {
      print_csv_message(ros_msg, m_echoOptions.full_length, m_echoOptions.truncate_length);
    } else {
      print_yaml_message(ros_msg, m_echoOptions.full_length, m_echoOptions.truncate_length);
    }
  }
}

}  // namespace ros2topic_cpp
