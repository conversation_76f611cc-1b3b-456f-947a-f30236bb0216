// Copyright 2022 Apex.AI
// All rights reserved.

#include <string>
#include <utility>

#include "rcpputils/split.hpp"
#include "ros2topic_cpp/ros2topic_cpp.hpp"
#include "ros2topic_cpp/ros2topic_filter.hpp"

namespace ros2topic_cpp
{

void init()
{
  rclcpp::init(0, nullptr);
}

void shutdown()
{
  rclcpp::shutdown();
}

apex::optional<std::pair<std::string, std::string>>
get_requested_topic(const rclcpp::Node & node, const std::string & topic_name)
{
  auto all_topics_and_types = node.get_topic_names_and_types();
  const bool include_hidden_topics = true;
  auto filtered_topics_and_types = filter_topics_with_more_than_one_type(
    all_topics_and_types, include_hidden_topics);
  auto expanded_topic = rclcpp::expand_topic_or_service_name(
    topic_name, node.get_name(), node.get_namespace(), false);

  return filter_topics(expanded_topic, filtered_topics_and_types);
}

apex::optional<std::pair<std::string, std::string>> wait_for_requested_topic(
  const rclcpp::Node & node, const std::string & topic_name, std::chrono::seconds timeout)
{
  auto start = std::chrono::steady_clock::now();
  while (std::chrono::steady_clock::now() - start < timeout) {
    auto request_topics = get_requested_topic(node, topic_name);
    if (request_topics) {
      return request_topics;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
  }

  return apex::nullopt;
}

std::string convert_message_type_from_options(const std::string & namespace_message_type)
{
  // expect format std_msgs/String
  auto tokens = rcpputils::split(namespace_message_type, '/', true);
  if (tokens.size() < 2) {
    throw std::runtime_error("The passed message type is invalid");
  }
  const auto message_namespace = tokens.front();
  const auto message_type = tokens.back();
  return message_namespace + "/msg/" + message_type;
}

std::pair<std::string, std::string>
get_interface_typename(const std::string & namespaced_message_type)
{
  // expected format: std_msgs/msg/String
  auto tokens = rcpputils::split(namespaced_message_type, '/', true);

  // note: the expectation here should actually be tokens.size() != 3, but Apex.OS additions
  // to rosidl relax this requirement. This allows other types to be used with ros2topic.
  if (tokens.size() < 2) {
    throw std::runtime_error("unexpected message type name " + namespaced_message_type);
  }
  return std::make_pair(tokens.front(), tokens.back());
}

std::pair<const rosidl_message_type_support_t *, const std::shared_ptr<rcpputils::SharedLibrary>>
get_typesupport_handle(const std::string & type_name)
{
  auto library = rclcpp::get_typesupport_library_or_nullptr(type_name, "rosidl_typesupport_cpp");
  if (library) {
    auto * ts = rclcpp::get_typesupport_handle(type_name, "rosidl_typesupport_cpp", library);
    return {ts, library};
  }
  // If we didn't find the typesupport lib (e.g., if the first interface name component doesn't
  // correspond to a package name), then try loading the special typesupport loader shared library.
  // If there is one, its interfaces will register with the typesupport registry when it gets
  // loaded. Then we can try looking up the typesupport using the registry, which means NOT passing
  // a lib to rclcpp::get_typesupport_handle().
  library = rclcpp::get_typesupport_loader_library();
  auto * ts = rclcpp::get_typesupport_handle(type_name, "rosidl_typesupport_cpp", nullptr);
  return {ts, library};
}

}  // namespace ros2topic_cpp
