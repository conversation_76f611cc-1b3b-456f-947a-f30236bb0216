# Copyright 2024 Apex.AI, Inc.
# All rights reserved.

load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "syslog_writer",
    description = "Utility for forwarding the logs from the Apex.Grace logging topics to the system storage via a syslog system call.",
    lib_executables = [
        "syslog_writer_exe",
    ],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "syslog_writer",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

cc_library(
    name = "syslog_writer_lib",
    srcs = [
        "src/syslog_writer.cpp",
    ],
    hdrs = [
        "include/syslog_writer/syslog_writer.hpp",
        "include/syslog_writer/visibility_control.hpp",
    ],
    strip_include_prefix = "include",
    deps = [
        "//grace/execution/executor2",
        "//grace/interfaces/apex_msgs",
    ],
)

cc_binary(
    name = "syslog_writer_exe",
    srcs = ["src/syslog_writer_main.cpp"],
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    deps = [
        ":syslog_writer_lib",
        "//common/interrupt",
        "//grace/execution/apex_init",
    ],
)

cc_binary(
    name = "libsyslog_interpose.so",
    testonly = True,
    srcs = [
        "test/syslog_interpose.cpp",
        "test/syslog_interpose.hpp",
    ],
    linkshared = True,
    linkstatic = False,
    tags = [
        "exclude_sca",
    ],
    visibility = ["//visibility:public"],
)

cc_import(
    name = "syslog_interpose",
    testonly = True,
    shared_library = ":libsyslog_interpose.so",
    visibility = ["//visibility:public"],
)

cc_library(
    name = "syslog_interpose_lib",
    testonly = True,
    srcs = [
        "test/syslog_interpose.cpp",
    ],
    hdrs = [
        "test/syslog_interpose.hpp",
    ],
    strip_include_prefix = "test",
    tags = [
        "exclude_sca",
    ],
)

apex_cc_test(
    name = "test_syslog_writer",
    srcs = [
        "test/test_syslog_writer.cpp",
    ],
    data = [":libsyslog_interpose.so"],
    env = {
        "APEX_GRACE_LOG_LEVEL": "TRACE",
        "LD_PRELOAD": "$(rootpath :libsyslog_interpose.so)",
        "IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc",
    },
    tags = [
        "constrained_test",
        "exclude_sca",
        "exclusive",
    ],
    deps = [
        ":syslog_interpose_lib",
        ":syslog_writer_lib",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "param/syslog_writer_settings.yaml",
    ],
    visibility = [":__subpackages__"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
