# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

from apex_verify_docs import (
    integration_doctest,
    start_script_subprocess,
    terminate_subprocess,
    wait_for_output_pattern
)

import unittest


class TestExecutionMonitorExamples(unittest.TestCase):
    def run_execution_monitor_service(self):
        script = """
        #! EXECUTION_MONITOR_SERVICE
        # Terminal 1
        source /opt/ApexGraceBilbo/setup.bash
        param_dir=$(ros2 pkg prefix --share execution_monitor_examples)/param/
        ros2 run execution_monitor_service execution_monitor_service_exe
        #! EXECUTION_MONITOR_SERVICE
        """

        p = start_script_subprocess(script)
        return p

    @integration_doctest
    def test_run_execution_monitor_service(self):
        p = self.run_execution_monitor_service()
        wait_for_output_pattern(p, "Execution monitor service is running. Ctrl-C to stop...",
                                timeout=10)
        terminate_subprocess(p)

    @integration_doctest
    def test_run_monitored_process(self):
        script = """
        #! MONITORED_PROCESS
        # Terminal 2
        source /opt/ApexGraceBilbo/setup.bash
        param_dir=$(ros2 pkg prefix --share execution_monitor_examples)/param/
        ros2 run execution_monitor_examples monitored_executor_task \
          --apex-settings-file $param_dir/fulfilled_expectations.yaml
        #! MONITORED_PROCESS
        """

        mon = self.run_execution_monitor_service()
        proc = None
        try:
            proc = start_script_subprocess(script)
            wait_for_output_pattern(mon, "Execution monitor service is running. Ctrl-C to stop...",
                                    timeout=10)
            try:
                wait_for_output_pattern(mon, "does not satisfy expectation", timeout=10)
                if proc.poll() is not None:
                    raise RuntimeError(f"Monitored process died ({proc.returncode}):" +
                                       proc.stderr.read().decode('utf8'))
                raise RuntimeError("Monitor detected unexpected expectation infraction")
            except TimeoutError:
                pass
        finally:
            terminate_subprocess(mon)
            if proc and proc.poll() is None:
                terminate_subprocess(proc, timeout=5)

    @integration_doctest
    def test_run_failing_monitored_process(self):
        script = """
        #! FAILING_MONITORED_PROCESS
        # Terminal 2
        source /opt/ApexGraceBilbo/setup.bash
        param_dir=$(ros2 pkg prefix --share execution_monitor_examples)/param/
        ros2 run execution_monitor_examples monitored_executor_task \
          --apex-settings-file $param_dir/failing_expectation_too_few_act_per_win.yaml
        #! FAILING_MONITORED_PROCESS
        """

        mon = self.run_execution_monitor_service()
        proc = None
        try:
            proc = start_script_subprocess(script)
            wait_for_output_pattern(mon, "does not satisfy expectation", timeout=10)
        finally:
            terminate_subprocess(mon)
            if proc:
                terminate_subprocess(proc)
