# Copyright 2022 Apex.AI, Inc.
# All rights reserved.

import time

from apex_verify_docs import \
    integration_doctest, \
    start_script_subprocess, \
    terminate_subprocess, \
    verify_subprocess_output

import unittest


class TestApexDocCli(unittest.TestCase):
    @integration_doctest
    def test_wait(self):
        script = """
        source /opt/ApexGraceBilbo/setup.bash
        ros2 run interrupt_examples wait
        """

        expected_output = """
        Hello!
        Interrupted!
        """

        p = start_script_subprocess(script)
        time.sleep(3)
        terminate_subprocess(p)
        verify_subprocess_output(p, expected_output)

    @integration_doctest
    def test_poll(self):
        script = """
        source /opt/ApexGraceBilbo/setup.bash
        ros2 run interrupt_examples poll
        """

        expected_output = """
        Hello!
        Interrupted!
        """

        p = start_script_subprocess(script)
        time.sleep(3)
        terminate_subprocess(p)
        verify_subprocess_output(p, expected_output)

    @integration_doctest
    def test_pollfd(self):
        script = """
        source /opt/ApexGraceBilbo/setup.bash
        ros2 run interrupt_examples pollfd
        """

        expected_output = """
        Hello!
        Interrupted!
        """

        p = start_script_subprocess(script)
        time.sleep(3)
        terminate_subprocess(p)
        verify_subprocess_output(p, expected_output)
