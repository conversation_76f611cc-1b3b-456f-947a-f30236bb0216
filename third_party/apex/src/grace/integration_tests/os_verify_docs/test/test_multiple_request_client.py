# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

import time

from apex_verify_docs import \
    integration_doctest, \
    start_script_subprocess, \
    terminate_subprocess,  \
    verify_subprocess_output

import unittest


class TestApexDocCli(unittest.TestCase):

    @integration_doctest
    def test_run_client_server(self):
        script_client = """
        source /opt/ApexGraceBilbo/setup.bash
        #! RUN_CLIENT
        ros2 run apex_os_minimal_service client_multiple_requests
        #! RUN_CLIENT
        """

        script_server = """
        source /opt/ApexGraceBilbo/setup.bash
        #! RUN_SERVER
        ros2 run apex_os_minimal_service server
        #! RUN_SERVER
        """

        expected_output_client = """
        #! CLIENT_OUTPUT
        ...
        Initializing root logger.
        waiting for matched server...
        Natural numbers: 0
        Powers of two: 2
        Fibonacci numbers: 0, 1
        Natural numbers: 0, 1
        Powers of two: 2, 4
        Fibonacci numbers: 0, 1, 1
        Natural numbers: 0, 1, 2
        Powers of two: 2, 4, 8
        Fibonacci numbers: 0, 1, 1, 2
        Natural numbers: 0, 1, 2, 3
        Powers of two: 2, 4, 8, 16
        Fibonacci numbers: 0, 1, 1, 2, 3
        ...
        #! CLIENT_OUTPUT
        """

        expected_output_server = """
        #! SERVER_OUTPUT
        ...
        Initializing root logger.
        Request: 0 + 1 | Response: 1
        Request: 2 + 2 | Response: 4
        Request: 0 + 1 | Response: 1
        ...
        #! SERVER_OUTPUT
        """

        client_pid = start_script_subprocess(script_client)
        server_pid = start_script_subprocess(script_server)

        time.sleep(5)
        terminate_subprocess(client_pid)
        terminate_subprocess(server_pid)

        verify_subprocess_output(
            client_pid, expected_output_client, compare_whitespaces=False)
        verify_subprocess_output(
            server_pid, expected_output_server, compare_whitespaces=False)
