# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

import time

from apex_verify_docs import \
    integration_doctest, \
    start_script_subprocess, \
    terminate_subprocess,  \
    verify_subprocess_output

import unittest


class TestApexDocCli(unittest.TestCase):

    @integration_doctest
    def test_run_client_server(self):
        script_client = """
        source /opt/ApexGraceBilbo/setup.bash
        #! RUN_CLIENT
        ros2 run apex_os_minimal_service client
        #! RUN_CLIENT
        """

        script_server = """
        source /opt/ApexGraceBilbo/setup.bash
        #! RUN_SERVER
        ros2 run apex_os_minimal_service server
        #! RUN_SERVER
        """

        expected_output_client = """
        #! CLIENT_OUTPUT
        ...
        Initializing root logger.
        waiting for matched server...
        Sending request:
         a:  2  b:  0
        Result of add_two_ints:  2
        Sending request:
         a:  2  b:  1
        Result of add_two_ints:  3
        ...
        #! CLIENT_OUTPUT
        """

        expected_output_server = """
        #! SERVER_OUTPUT
        ...
        Initializing root logger.
        Request: 2 + 0 | Response: 2
        Request: 2 + 1 | Response: 3
        ...
        #! SERVER_OUTPUT
        """

        client_pid = start_script_subprocess(script_client)
        server_pid = start_script_subprocess(script_server)

        time.sleep(5)
        terminate_subprocess(client_pid)
        terminate_subprocess(server_pid)

        verify_subprocess_output(
            client_pid, expected_output_client, compare_whitespaces=False)
        verify_subprocess_output(
            server_pid, expected_output_server, compare_whitespaces=False)
