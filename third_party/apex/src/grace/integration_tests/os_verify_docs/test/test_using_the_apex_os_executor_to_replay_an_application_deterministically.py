# Copyright 2022 Apex.AI, Inc.
# All rights reserved.

from apex_verify_docs import \
    integration_doctest, \
    start_script_subprocess, \
    verify_subprocess_output, \
    wait_for_subprocess

import unittest


class TestApexExecutorReplay(unittest.TestCase):
    @integration_doctest
    def test_run_live_executor(self):
        script = """
        #! RUN_LIVE
        source /opt/ApexGraceBilbo/setup.bash
        ros2 run deterministic_replay_examples sut_exe live
        # Use ROS autocompletion to see all the executables that are available
        #! RUN_LIVE
        """

        expected_output = """
        #! RUN_LIVE_OUTPUT
        Initializing root logger.
        ------------- Live mode -------------
        #! RUN_LIVE_OUTPUT
        """

        p = start_script_subprocess(script)
        wait_for_subprocess(p)
        verify_subprocess_output(p, expected_output)

    @integration_doctest
    def test_play_bag(self):
        script = """
        #! PLAY_BAG
        source /opt/ApexGraceBilbo/setup.bash
        ros2 bag play \
            /opt/ApexGraceBilbo/share/deterministic_replay_examples/rosbag2_deterministic_replay_test/
        #! PLAY_BAG
        """

        expected_output = """
        #! PLAY_BAG_OUTPUT
        Opened database
        #! PLAY_BAG_OUTPUT
        """

        p = start_script_subprocess(script)
        wait_for_subprocess(p)
        verify_subprocess_output(p, expected_output)

    @integration_doctest
    def test_run_replay_executor(self):
        script = """
        #! RUN_REPLAY
        source /opt/ApexGraceBilbo/setup.bash
        ros2 run deterministic_replay_examples sut_exe
        # Use ROS autocompletion to see all the executables that are available
        #! RUN_REPLAY
        """

        expected_output = """
        #! RUN_REPLAY_OUTPUT
        Initializing root logger.
        ------------- Replay mode -------------
        #! RUN_REPLAY_OUTPUT
        """

        p = start_script_subprocess(script)
        wait_for_subprocess(p)
        verify_subprocess_output(p, expected_output)

    @integration_doctest
    def test_run_replay_coordinator(self):
        script = """
        #! RUN_REPLAY_COORDINATOR
        source /opt/ApexGraceBilbo/setup.bash
        ros2 run replay replay_coordinator --rosbag-path \
            /opt/ApexGraceBilbo/share/deterministic_replay_examples/rosbag2_deterministic_replay_test/
        #! RUN_REPLAY_COORDINATOR
        """

        expected_output = """
        #! RUN_REPLAY_COORDINATOR_OUTPUT
        Opened database
        #! RUN_REPLAY_COORDINATOR_OUTPUT
        """

        p = start_script_subprocess(script)
        wait_for_subprocess(p)
        verify_subprocess_output(p, expected_output)
