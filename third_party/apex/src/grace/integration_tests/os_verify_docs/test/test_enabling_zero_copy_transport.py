# Copyright 2022-2023 Apex.AI, Inc.
# All rights reserved.

import time

from apex_verify_docs import \
    integration_doctest, \
    start_script_subprocess, \
    terminate_subprocess, \
    verify_subprocess_output
import unittest


class TestZeroCopyDoc(unittest.TestCase):

    @integration_doctest
    def test_run_pub_sub_node(self):
        script_pub = """
        source /opt/ApexGraceBilbo/setup.bash
        #! PUBLISH
        ros2 run zero_copy_example zero_copy_publisher \
            --apex-settings-file /opt/ApexGraceBilbo/share/zero_copy_example/param/shm_config.param.yaml
        #! PUBLISH
        """

        script_sub = """
        source /opt/ApexGraceBilbo/setup.bash
        #! SUBSCRIBE
        ros2 run zero_copy_example zero_copy_subscriber \
            --apex-settings-file /opt/ApexGraceBilbo/share/zero_copy_example/param/shm_config.param.yaml
        #! SUBSCRIBE
        """

        expected_output_roudi = """
        #! ROUDI_OUTPUT
        RouDi not found - waiting ...
        #! ROUDI_OUTPUT
        """

        expected_output_pub = expected_output_roudi + """
        #! PUBLISH_OUTPUT
        ...
        Publishing message:
            string_values\[0\]: hello 0
            string_values\[1\]: hello 1
            string_values\[2\]: hello 2
        ...
        #! PUBLISH_OUTPUT
        """

        expected_output_sub = expected_output_roudi + """
        #! SUBSCRIBE_OUTPUT
        ...
        Received message:
            string_values\[0\]:  hello 0
            string_values\[1\]:  hello 1
            string_values\[2\]:  hello 2
        ...
        #! SUBSCRIBE_OUTPUT
        """

        pub_pid = start_script_subprocess(script_pub)
        sub_pid = start_script_subprocess(script_sub)

        time.sleep(5)
        script_roudi = """
        source /opt/ApexGraceBilbo/setup.bash
        #! ROUDI
        /opt/ApexIdaBilbo/bin/iox-roudi
        #! ROUDI
        """
        roudi_pid = start_script_subprocess(script_roudi)

        time.sleep(5)
        terminate_subprocess(pub_pid)
        terminate_subprocess(sub_pid)
        terminate_subprocess(roudi_pid)

        verify_subprocess_output(
            pub_pid, expected_output_pub, compare_whitespaces=False)
        verify_subprocess_output(
            sub_pid, expected_output_sub, compare_whitespaces=False)

    @integration_doctest
    def test_run_srv_srv_client_node(self):
        script_srv = """
        source /opt/ApexGraceBilbo/setup.bash
        #! SERVICE
        ros2 run zero_copy_example zero_copy_service \
            --apex-settings-file /opt/ApexGraceBilbo/share/zero_copy_example/param/shm_config.param.yaml
        #! SERVICE
        """

        script_srv_client = """
        source /opt/ApexGraceBilbo/setup.bash
        #! SERVICE_CLIENT
        ros2 run zero_copy_example zero_copy_service_client \
            --apex-settings-file /opt/ApexGraceBilbo/share/zero_copy_example/param/shm_config.param.yaml
        #! SERVICE_CLIENT
        """

        expected_output_srv = """
        #! SERVICE_OUTPUT
        ...
        Sending response:
            data: hello
            string_values\[0\]: hello 10
            string_values\[1\]: hello 11
            string_values\[2\]: hello 12
            string_values\[3\]: hello 13
            string_values\[4\]: hello 14
        ...
        #! SERVICE_OUTPUT
        """

        expected_output_srv_client = """
        #! SERVICE_CLIENT_OUTPUT
        ...
        Received response:
            string_value:  hello
            string_values\[0\]:  hello 10
            string_values\[1\]:  hello 11
            string_values\[2\]:  hello 12
            string_values\[3\]:  hello 13
            string_values\[4\]:  hello 14
        ...
        #! SERVICE_CLIENT_OUTPUT
        """

        script_roudi = """
        source /opt/ApexGraceBilbo/setup.bash
        /opt/ApexIdaBilbo/bin/iox-roudi
        """
        roudi_pid = start_script_subprocess(script_roudi)

        time.sleep(3)
        srv_pid = start_script_subprocess(script_srv)
        time.sleep(3)
        srv_client_pid = start_script_subprocess(script_srv_client)

        time.sleep(3)
        terminate_subprocess(srv_client_pid)
        terminate_subprocess(srv_pid)
        terminate_subprocess(roudi_pid)

        verify_subprocess_output(
            srv_pid, expected_output_srv, compare_whitespaces=False)
        verify_subprocess_output(
            srv_client_pid, expected_output_srv_client, compare_whitespaces=False)
