# Copyright 2022 Apex.AI, Inc.
# All rights reserved.

import unittest
from apex_verify_docs import \
    integration_doctest, \
    start_script_subprocess, \
    verify_subprocess_output, \
    wait_for_subprocess


class TestGettingStartedArticle(unittest.TestCase):
    @integration_doctest
    def test_check_path_when_sourced(self):
        # APEX_VERIFY_DOCS_SCRIPT_SNIPPET
        script = r"""
        #! SOURCE_APEX_OS
        source /opt/ApexGraceBilbo/setup.bash
        #! SOURCE_APEX_OS
        #! ECHO_PATH
        echo $PATH | grep -P "\/opt.*?:"
        #! ECHO_PATH
        """
        # APEX_VERIFY_DOCS_SCRIPT_SNIPPET

        # APEX_VERIFY_DOCS_SCRIPT_SNIPPET_OUTPUT
        r"""
        #! ECHO_PATH_OUTPUT
        /opt/ApexGraceBilbo/bin:/opt/ApexToolsBilbo/bin:/opt/ApexIdaBilbo/bin
        #! ECHO_PATH_OUTPUT
        """
        # APEX_VERIFY_DOCS_SCRIPT_SNIPPET_OUTPUT

        # APEX_VERIFY_DOCS_SCRIPT_SNIPPET_RUN_AND_VERIFICATION
        pid = start_script_subprocess(script)
        wait_for_subprocess(pid)
        os = r"/opt/ApexGraceBilbo/bin"
        mw = r"/opt/ApexToolsBilbo/bin"
        tools = r"/opt/ApexIdaBilbo/bin"
        regex_pattern = "({os}:{mw}:{tools})|({os}:{tools}:{mw})|\
({mw}:{os}:{tools})|({mw}:{tools}:{os})|\
({tools}:{mw}:{tools})|({tools}:{os}:{mw})".format(os=os, mw=mw, tools=tools)

        verify_subprocess_output(pid, regex_pattern)
        # APEX_VERIFY_DOCS_SCRIPT_SNIPPET_RUN_AND_VERIFICATION

    @integration_doctest
    def test_nodes_run_correctly(self):
        pub_script = """
        #! PUBLISH
        source /opt/ApexGraceBilbo/setup.bash
        ros2 run apex_os_minimal_pub_sub publisher
        #! PUBLISH
        """

        pub_expected_output = """
        #! PUBLISH_OUTPUT
        Initializing root logger.
        waiting for matched subscriber...
        Published message: Hello, world! 0
        Published message: Hello, world! 1
        #! PUBLISH_OUTPUT
        """

        sub_script = """
        #! SUBSCRIBE
        source /opt/ApexGraceBilbo/setup.bash
        ros2 run apex_os_minimal_pub_sub subscriber
        #! SUBSCRIBE
        """

        sub_expected_output = """
        #! SUBSCRIBE_OUTPUT
        Initializing root logger.
        waiting for matched publisher...
        Received message: Hello, world! 0
        Received message: Hello, world! 1
        #! SUBSCRIBE_OUTPUT
        """
        pub_pid = start_script_subprocess(pub_script)
        sub_pid = start_script_subprocess(sub_script)

        node_list_script = """
        #! NODE_LIST
        source /opt/ApexGraceBilbo/setup.bash
        ros2 node list
        #! NODE_LIST
        """

        node_list_expected_output = """
        #! NODE_LIST_OUTPUT
        /minimal_pub
        /minimal_sub
        /root_logger_node
        /root_logger_node
        #! NODE_LIST_OUTPUT
        """

        node_list_pid = start_script_subprocess(node_list_script)
        wait_for_subprocess(node_list_pid, 10)
        verify_subprocess_output(node_list_pid, node_list_expected_output)

        topic_info_script = """
        #! TOPIC_INFO
        source /opt/ApexGraceBilbo/setup.bash
        ros2 topic type /minimal_chatter
        #! TOPIC_INFO
        """

        topic_info_expected_output = """
        #! TOPIC_INFO_OUTPUT
        std_msgs/msg/String
        #! TOPIC_INFO_OUTPUT
        """

        topic_info_pid = start_script_subprocess(topic_info_script)
        wait_for_subprocess(topic_info_pid, 10)
        verify_subprocess_output(topic_info_pid, topic_info_expected_output)

        rqt_script = """
        #! RQT_GRAPH
        source /opt/ros_visualization/setup.bash
        ros2 run rqt_graph rqt_graph --force-discover
        #! RQT_GRAPH
        """

        rqt_pid = start_script_subprocess(rqt_script)
        wait_for_subprocess(rqt_pid, 10)

        wait_for_subprocess(pub_pid, 5)
        wait_for_subprocess(sub_pid, 5)
        verify_subprocess_output(pub_pid, pub_expected_output)
        verify_subprocess_output(sub_pid, sub_expected_output)
