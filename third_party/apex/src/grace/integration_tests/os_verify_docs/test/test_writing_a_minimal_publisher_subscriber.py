# Copyright 2022 Apex.AI, Inc.
# All rights reserved.

import os
import shutil
import tempfile

from apex_verify_docs import \
    integration_doctest, \
    start_script_subprocess, \
    verify_subprocess_output, \
    wait_for_subprocess

import unittest


class TestApexDocCli(unittest.TestCase):
    @integration_doctest
    def test_help(self):
        apex_create_pkg_help()

    @integration_doctest
    def test_colcon_test(self):
        with tempfile.TemporaryDirectory() as tmp_path:
            apex_create_pkg(tmp_path)
            colcon_build(tmp_path)
            colcon_test(tmp_path)

    @integration_doctest
    def test_with_ros2_run(self):
        with tempfile.TemporaryDirectory() as tmp_path:
            apex_create_pkg(tmp_path)
            colcon_build(tmp_path)
            run_with_ros2_run(tmp_path)

    @integration_doctest
    def test_with_apex_process_manager_with_ros2_run(self):
        with tempfile.TemporaryDirectory() as tmp_path:
            apex_create_pkg(tmp_path)
            colcon_build(tmp_path)
            run_with_process_manager(tmp_path)


def apex_create_pkg_help():
    script = """
    source /opt/ApexGraceBilbo/setup.bash
    #! HELP
    apex_create_pkg --help
    #! HELP
    """

    expected_output = """
    #! HELP_OUTPUT
    usage: apex_create_pkg
    ...
    #! HELP_OUTPUT
    """

    p = start_script_subprocess(script)
    wait_for_subprocess(p)
    verify_subprocess_output(p, expected_output)


def apex_create_pkg(tmp_path):
    script = """
    source /opt/ApexGraceBilbo/setup.bash
    #! CREATE
    cd minimal_user_ws
    mkdir src
    bazel run @apex//tools/apex_create_pkg:apex_create_pkg_exe -- \  # (1)!
        --pkg-name apex_os_minimal_pub_sub \
        --destination $(pwd)/src/ \
        --description "Apex OS minimal publisher and subscriber example." \
        --maintainer "John Doe" \
        --email "<EMAIL>" \
        --license "apexai" \
        --copyright "Awesome Customer, Inc."
    #! CREATE
    """.replace('~', str(tmp_path))

    expected_output = """
    #! CREATE_OUTPUT
    ...
    Package apex_os_minimal_pub_sub has been generated
    #! CREATE_OUTPUT
    """

    p = start_script_subprocess(script)
    wait_for_subprocess(p)
    verify_subprocess_output(p, expected_output)


def colcon_build(tmp_path):
    script = """
    #! BUILD
    cd ~/workspace
    source /opt/ApexGraceBilbo/setup.bash
    colcon build --merge-install \
        --packages-select apex_os_minimal_pub_sub \
        --cmake-args \
            -DCMAKE_TOOLCHAIN_FILE=/opt/ApexToolsBilbo/share/apex_cmake/cmake/apex_toolchainfile.cmake \
            -DAPEX_PRINT_LOGS_TO_TERMINAL=ON
    #! BUILD
    """.replace('~', str(tmp_path))  # noqa: E501

    expected_output = """
    #! BUILD_OUTPUT
    Starting >>> apex_os_minimal_pub_sub
    ...
    Finished <<< apex_os_minimal_pub_sub
    #! BUILD_OUTPUT
    """

    p = start_script_subprocess(script)
    wait_for_subprocess(p, timeout=240)
    verify_subprocess_output(p, expected_output)


def colcon_test(tmp_path):
    script = """
    source /opt/ApexGraceBilbo/setup.bash
    # Reformat the code to avoid linter errors caused by the long package name.
    ament_clang_format ~/workspace/src/apex_os_minimal_pub_sub/ --reformat
    ament_uncrustify ~/workspace/src/apex_os_minimal_pub_sub/ --reformat
    #! TEST
    cd ~/workspace
    colcon test --merge-install \
        --event-handlers console_cohesion+ \
        --packages-select apex_os_minimal_pub_sub
    #! TEST
    """.replace('~', str(tmp_path))

    expected_output = """
    #! TEST_OUTPUT
    Starting >>> apex_os_minimal_pub_sub
    ...
    100% tests passed, 0 tests failed out of 10
    ...
    Finished <<< apex_os_minimal_pub_sub
    #! TEST_OUTPUT
    """

    p = start_script_subprocess(script)
    wait_for_subprocess(p)
    verify_subprocess_output(p, expected_output)


def run_with_ros2_run(tmp_path):
    script = """
    source /opt/ApexGraceBilbo/setup.bash
    #! RUN_WITH_ROS2_RUN
    source ~/workspace/install/setup.bash
    ros2 run apex_os_minimal_pub_sub pub_sub_exe
    #! RUN_WITH_ROS2_RUN
    """.replace('~', str(tmp_path))

    expected_output = """
    #! RUN_WITH_ROS2_RUN_OUTPUT
    ...
    Published message: Hello, world! 0
    ...
    Received message: Hello, world! 0
    ...
    Published message: Hello, world! 1
    ...
    Received message: Hello, world! 1
    ...
    #! RUN_WITH_ROS2_RUN_OUTPUT
    """

    p = start_script_subprocess(script)
    wait_for_subprocess(p, 5)
    verify_subprocess_output(p, expected_output)


def run_with_process_manager(tmp_path, use_ros2_run=True):
    script1 = """
    cd ~
    source /opt/ApexGraceBilbo/setup.bash
    source ~/workspace/install/setup.bash
    #! RUN_WITH_PROCESS_MANAGER
    mkdir -p ~/my_logs
    ros2 run process_manager process_manager --apex-settings-file \
        workspace/src/apex_os_minimal_pub_sub/launch/apex_os_minimal_pub_sub.launch.yaml
    #! RUN_WITH_PROCESS_MANAGER
    """.replace('~', str(tmp_path))  # noqa: E501

    script2 = """
    cd ~
    source /opt/ApexGraceBilbo/setup.bash
    source ~/workspace/install/setup.bash
    mkdir -p ~/my_logs
    #! RUN_WITH_PROCESS_MANAGER_WITHOUT_PYTHON
    /opt/ApexGraceBilbo/lib/process_manager/process_manager --apex-settings-file \
        workspace/src/apex_os_minimal_pub_sub/launch/apex_os_minimal_pub_sub.launch.yaml
    #! RUN_WITH_PROCESS_MANAGER_WITHOUT_PYTHON
    """.replace('~', str(tmp_path))  # noqa: E501

    if use_ros2_run:
        script = script1
    else:
        script = script2

    expected_output = """
    #! RUN_WITH_PROCESS_MANAGER_OUTPUT
    Starting to monitor processes...
    Change state from  OFF  to  On
    State transition completed: from  OFF  to  On
    Stopping monitoring processes...
    #! RUN_WITH_PROCESS_MANAGER_OUTPUT
    """  # noqa: E501

    launch_file_src = os.path.join(
        fixtures_path, 'apex_os_minimal_pub_sub.launch.yaml')
    launch_file_dst = os.path.join(
        tmp_path,
        'workspace/src/apex_os_minimal_pub_sub/launch/apex_os_minimal_pub_sub.launch.yaml')
    os.makedirs(os.path.dirname(launch_file_dst), exist_ok=True)
    if os.path.exists(launch_file_dst):
        os.remove(launch_file_dst)
    shutil.copyfile(src=launch_file_src, dst=launch_file_dst)

    p = start_script_subprocess(script)
    wait_for_subprocess(p, 5)
    verify_subprocess_output(p, expected_output)


fixtures_path = os.path.join(
    os.path.dirname(os.path.realpath(__file__)),
    'fixtures')
