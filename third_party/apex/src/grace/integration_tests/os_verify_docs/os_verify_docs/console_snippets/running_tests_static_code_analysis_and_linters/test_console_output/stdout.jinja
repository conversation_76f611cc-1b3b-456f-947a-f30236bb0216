{% if not testing %}...{%- endif %}
test 1
      Start  1: test_my_cool_pkg
1: Test command: {% if testing %}PATH_TAG{% else %}/usr/bin{% endif %}/python3 "-u" "{% if testing %}PATH_TAG{% else %}/opt/ApexToolsBilbo{% endif %}/share/ament_cmake_test/cmake/run_test.py" "{{ home|default("~") }}/workspace/build/my_cool_pkg/test_results/my_cool_pkg/test_my_cool_pkg.gtest.xml" "--package-name" "my_cool_pkg" "--output-file" "{{ home|default("~") }}/workspace/build/my_cool_pkg/ament_cmake_gtest/test_my_cool_pkg.txt" "--env" "LD_PRELOAD={% if testing %}PATH_TAG{% else %}/opt/ApexToolsBilbo{% endif %}/lib/libmemory_tools_interpose.so" "--command" "{{ home|default("~") }}/workspace/build/my_cool_pkg/test_my_cool_pkg" "--gtest_output=xml:{{ home|default("~") }}/workspace/build/my_cool_pkg/test_results/my_cool_pkg/test_my_cool_pkg.gtest.xml"
1: Test timeout computed to be: 60
1: Running with ROS_DOMAIN_ID 1
1: -- run_test.py: extra environment variables:
1:  - LD_PRELOAD={% if testing %}PATH_TAG{% else %}/opt/ApexToolsBilbo{% endif %}/lib/libmemory_tools_interpose.so
1: -- run_test.py: invoking following command in '{{ home|default("~") }}/workspace/build/my_cool_pkg':
1:  - {{ home|default("~") }}/workspace/build/my_cool_pkg/test_my_cool_pkg --gtest_output=xml:{{ home|default("~") }}/workspace/build/my_cool_pkg/test_results/my_cool_pkg/test_my_cool_pkg.gtest.xml
1: Running main() from {% if testing %}PATH_TAG{% else %}/opt/ApexToolsBilbo{% endif %}/src/gtest_vendor/src/gtest_main.cc
1: [==========] Running 2 tests from 1 test suite.
1: [----------] Global test environment set-up.
1: [----------] 2 tests from MyCoolPkgNodeFixture
1: [ RUN      ] MyCoolPkgNodeFixture.test_my_cool_pkg_pub
1: [{% if testing %}DATE_TAG{% else %}2022-06-30 13:32:02{% endif %}] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/execution/apex_init/src/apex_init.cpp @  L241:
1: Initializing root logger.
1: [       OK ] MyCoolPkgNodeFixture.test_my_cool_pkg_pub ({% if testing %}INT_TAG{% else %}6{% endif %} ms)
1: [ RUN      ] MyCoolPkgNodeFixture.test_my_cool_pkg_sub
1: [{% if testing %}DATE_TAG{% else %}2022-06-30 13:32:02{% endif %}] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/execution/apex_init/src/apex_init.cpp @  L241:
1: Initializing root logger.
1: [       OK ] MyCoolPkgNodeFixture.test_my_cool_pkg_sub ({% if testing %}INT_TAG{% else %}4{% endif %} ms)
1: [----------] 2 tests from MyCoolPkgNodeFixture ({% if testing %}INT_TAG{% else %}10{% endif %} ms total)
1:
1: [----------] Global test environment tear-down
1: [==========] 2 tests from 1 test suite ran. ({% if testing %}INT_TAG{% else %}10{% endif %} ms total)
1: [  PASSED  ] 2 tests.
1: [----------] Global test environment tear-down
1: [==========] 2 tests from 1 test suite ran. ({% if testing %}INT_TAG{% else %}10{% endif %} ms total)
1: [  PASSED  ] 2 tests.
1: -- run_test.py: return code 0
1: -- run_test.py: inject classname prefix into gtest result file '{{ home|default("~") }}/workspace/build/my_cool_pkg/test_results/my_cool_pkg/test_my_cool_pkg.gtest.xml'
1: -- run_test.py: verify result file '{{ home|default("~") }}/workspace/build/my_cool_pkg/test_results/my_cool_pkg/test_my_cool_pkg.gtest.xml'
 1/10 Test  #1: test_my_cool_pkg .................   Passed    {% if testing %}FLOAT_TAG{% else %}0.10{% endif %} sec
...
100% tests passed, 0 tests failed out of 10

Label Time Summary:
copyright     =   {% if testing %}FLOAT_TAG{% else %}0.36{% endif %} sec*proc (1 test)
cppcheck      =   {% if testing %}FLOAT_TAG{% else %}0.15{% endif %} sec*proc (1 test)
cpplint       =   {% if testing %}FLOAT_TAG{% else %}0.22{% endif %} sec*proc (1 test)
flake8        =   {% if testing %}FLOAT_TAG{% else %}0.22{% endif %} sec*proc (1 test)
gtest         =   {% if testing %}FLOAT_TAG{% else %}0.10{% endif %} sec*proc (1 test)
lint_cmake    =   {% if testing %}FLOAT_TAG{% else %}0.11{% endif %} sec*proc (1 test)
linter        =   {% if testing %}FLOAT_TAG{% else %}1.36{% endif %} sec*proc (8 tests)
pep257        =   {% if testing %}FLOAT_TAG{% else %}1.36{% endif %} sec*proc (1 test)
uncrustify    =   {% if testing %}FLOAT_TAG{% else %}0.12{% endif %} sec*proc (1 test)
xmllint       =   {% if testing %}FLOAT_TAG{% else %}0.39{% endif %} sec*proc (1 test)

Total Test time (real) =   {% if testing %}FLOAT_TAG{% else %}1.46{% endif %} sec
