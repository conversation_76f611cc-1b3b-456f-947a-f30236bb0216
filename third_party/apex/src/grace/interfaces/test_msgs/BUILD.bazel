load("@apex//grace/interfaces/test_interface_files:defs.bzl", "idl_with_custom_package_name")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "c_msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "ROSIDL_COMMON_PKGS")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_python//python:defs.bzl", "py_library")

filegroup(
    name = "idl_msgs",
    srcs = [
        "msg/Builtins.idl",
        "msg/MessageWithId.idl",
    ],
    visibility = ["@apex//grace/rosidl:__subpackages__"],
)

idl_with_custom_package_name(
    name = "test_interface_msgs",
    package_name = "test_msgs",
    interface_files = [
        "//grace/interfaces/test_interface_files:test_interface_files_idl_files",
    ],
)

ros_pkg(
    name = "test_msgs_pkg",
    cc_libraries = [":fixtures"],
    description = "A package containing message definitions and fixtures used exclusively for testing purposes.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    msg_libraries = [
        ":test_msgs",
    ],
    version = "1.0.3",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/interfaces/action_msgs:action_msgs_pkg",
        "@apex//grace/interfaces/builtin_interfaces:builtin_interfaces_pkg",
    ] + ROSIDL_COMMON_PKGS,
)

filegroup(
    name = "test_interface_files_srv_files",
    srcs = select({
        "//common/asil:d": [
            "srv/BasicTypes.srv",
            "srv/Empty.srv",
        ],
        "//conditions:default": [
            "srv/Arrays.srv",
            "srv/BasicTypes.srv",
            "srv/Empty.srv",
        ],
    }),
    visibility = ["//visibility:public"],
)

filegroup(
    name = "ros_msgs",
    srcs = [
        "action/Fibonacci.action",
        "action/NestedMessage.action",
        "msg/LargeFixedMessage.msg",
        "msg/LargeUnboundedMessage.msg",
        "srv/LargeFixedService.srv",
        "srv/LargeUnboundedService.srv",
        "srv/UnboundedRequest.srv",
        "srv/UnboundedResponse.srv",
        "srv/UnboundedSequences.srv",
    ] + [
        ":test_interface_files_srv_files",
    ],
    visibility = ["@apex//grace/rosidl:__subpackages__"],
)

msgs_library(
    name = "test_msgs",
    srcs = [":test_interface_msgs"] + [":idl_msgs"],
    proto_structure = {
        "grace/interfaces/test_msgs/test_msgs/RootDirectory.idl": [
            "test_msgs.msg.RootDirMsgStruct",
            "test_msgs.srv.RootDirSrvStruct_Request",
            "test_msgs.srv.RootDirSrvStruct_Response",
            "test_msgs.srv.RootDirSrvStruct",
        ],
        "grace/interfaces/test_msgs/test_msgs/interface/nested/NestedDirectory.idl": [
            "test_msgs.msg.NestedDirMsgStruct",
            "test_msgs.srv.NestedDirSrvStruct_Request",
            "test_msgs.srv.NestedDirSrvStruct_Response",
            "test_msgs.srv.NestedDirSrvStruct",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/Arrays.idl": [
            "test_msgs.msg.Arrays",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/BasicTypes.idl": [
            "test_msgs.msg.BasicTypes",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/Constants.idl": [
            "test_msgs.msg.Constants",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/Defaults.idl": [
            "test_msgs.msg.Defaults",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/BoundedCollection.idl": [
            "test_msgs.msg.BoundedCollection",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/BoundedSequences.idl": [
            "test_msgs.msg.BoundedSequences",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/CamelCase.idl": [
            "test_msgs.msg.CamelCase",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/Empty.idl": [
            "test_msgs.msg.Empty",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/EnumSequence.idl": [
            "test_msgs.msg.EnumSequence",
            "test_msgs.EnumInSequence",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/EnumsMessage.idl": [
            "test_msgs.msg.EnumsMessage",
            "test_msgs.SomeEnum",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/SameEnumerators.idl": [
            "test_msgs.msg.SameEnumerators",
            "test_msgs.SameEnumeratorsEnum",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/ShortNamespace.idl": [
            "test_msgs.ShortNamespaceStruct",
            "test_msgs.ShortNamespaceSrvStruct_Request",
            "test_msgs.ShortNamespaceSrvStruct_Response",
            "test_msgs.ShortNamespaceSrvStruct",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/FlatSequencesBounded.idl": [
            "test_msgs.msg.FlatSequencesBounded",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/FloatingPointValues.idl": [
            "test_msgs.msg.FloatingPointValues",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/LegacyEnumSequence.idl": [
            "test_msgs.msg.LegacyEnumSequence",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/LegacyEnumsMessage.idl": [
            "test_msgs.msg.LegacyEnumsMessage",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/MultiDeclaration.idl": [
            "test_msgs.msg.MultiDeclaration",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/MultiNested.idl": [
            "test_msgs.msg.MultiNested",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/UnboundedSequences.idl": [
            "test_msgs.msg.UnboundedSequences",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/MultipleStructs.idl": [
            "test_msgs.msg.FirstStruct",
            "test_msgs.msg.SecondStruct",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/MultipleStructsSequence.idl": [
            "test_msgs.msg.SEQ_BOUNDS",
            "test_msgs.msg.SequenceOfMultipleStructs",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/Nested.idl": [
            "test_msgs.msg.Nested",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/NestedType.idl": [
            "test_msgs.msg.NestedType",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/ScopedEnumsMessage.idl": [
            "test_msgs.msg.Enum",
            "test_msgs.msg.ScopedEnumsMessage",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/StringSilent.idl": [
            "test_msgs.msg.StringSilent",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/Strings.idl": [
            "test_msgs.msg.Strings",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/UnionsMessage.idl": [
            "test_msgs.msg.UNION_ARRAY_BOUNDS",
            "test_msgs.msg.UnionsMessage",
            "test_msgs.STRING_BOUNDS",
            "test_msgs.UnionEnum",
            "test_msgs.ExampleUnion",
            "test_msgs.IntegerUnion",
            "test_msgs.UnionWithDefault",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/Builtins.idl": [
            "test_msgs.msg.Builtins",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/MessageWithId.idl": [
            "test_msgs.msg.MessageWithId",
        ],
        "grace/interfaces/test_msgs/msg/LargeFixedMessage.msg": [
            "test_msgs.msg.LargeFixedMessage",
        ],
        "grace/interfaces/test_msgs/msg/LargeUnboundedMessage.msg": [
            "test_msgs.msg.LargeUnboundedMessage",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/CommonConstants.idl": [
            "test_msgs.BOOL_CONST",
            "test_msgs.BYTE_CONST",
            "test_msgs.CHAR_CONST",
            "test_msgs.FLOAT32_CONST",
            "test_msgs.FLOAT64_CONST",
            "test_msgs.INT8_CONST",
            "test_msgs.UINT8_CONST",
            "test_msgs.INT16_CONST",
            "test_msgs.UINT16_CONST",
            "test_msgs.INT32_CONST",
            "test_msgs.UINT32_CONST",
            "test_msgs.INT64_CONST",
            "test_msgs.UINT64_CONST",
            "test_msgs.BOUNDS_SIZE",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/SomeEnum2.idl": [
            "test_msgs.SomeEnum2",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/MultipleNamespacesWithSameStruct.idl": [
            "test_msgs.ns1.SEQ_BOUNDS",
            "test_msgs.ns1.SomeStruct",
            "test_msgs.ns2.SEQ_BOUNDS",
            "test_msgs.ns2.SomeStruct",
            "test_msgs.ns3.ns4.SEQ_BOUNDS",
            "test_msgs.ns3.ns4.SomeStruct",
            "test_msgs.Services.ns5.SEQ_BOUNDS",
            "test_msgs.Services.ns5.SomeStruct_Request",
            "test_msgs.Services.ns5.SomeStruct_Response",
            "test_msgs.Services.ns6.SEQ_BOUNDS",
            "test_msgs.Services.ns6.SomeStruct_Request",
            "test_msgs.Services.ns6.SomeStruct_Response",
            "test_msgs.Services.ns5.SomeStruct",
            "test_msgs.Services.ns6.SomeStruct",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/MultipleNestedMultiNamespaces.idl": [
            "test_msgs.ns1.FirstStruct",
            "test_msgs.ns2.SecondStruct",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/WithUnderscore.idl": [
            "test_msgs.msg.Msg_With_Underscore",
            "test_msgs.srv.Srv_With_Underscore_Request",
            "test_msgs.srv.Srv_With_Underscore_Response",
            "test_msgs.srv.Srv_With_Underscore",
        ],
        "grace/interfaces/test_msgs/srv/Empty.srv": [
            "test_msgs.srv.Empty_Request",
            "test_msgs.srv.Empty_Response",
            "test_msgs.srv.Empty",
        ],
        "grace/interfaces/test_msgs/srv/BasicTypes.srv": [
            "test_msgs.srv.BasicTypes_Request",
            "test_msgs.srv.BasicTypes_Response",
            "test_msgs.srv.BasicTypes",
        ],
        "grace/interfaces/test_msgs/srv/Arrays.srv": [
            "test_msgs.srv.Arrays_Request",
            "test_msgs.srv.Arrays_Response",
            "test_msgs.srv.Arrays",
        ],
        "grace/interfaces/test_msgs/srv/UnboundedSequences.srv": [
            "test_msgs.srv.UnboundedSequences_Request",
            "test_msgs.srv.UnboundedSequences_Response",
            "test_msgs.srv.UnboundedSequences",
        ],
        "grace/interfaces/test_msgs/srv/UnboundedResponse.srv": [
            "test_msgs.srv.UnboundedResponse_Request",
            "test_msgs.srv.UnboundedResponse_Response",
            "test_msgs.srv.UnboundedResponse",
        ],
        "grace/interfaces/test_msgs/srv/UnboundedRequest.srv": [
            "test_msgs.srv.UnboundedRequest_Request",
            "test_msgs.srv.UnboundedRequest_Response",
            "test_msgs.srv.UnboundedRequest",
        ],
        "grace/interfaces/test_msgs/srv/LargeUnboundedService.srv": [
            "test_msgs.srv.LargeUnboundedService_Request",
            "test_msgs.srv.LargeUnboundedService_Response",
            "test_msgs.srv.LargeUnboundedService",
        ],
        "grace/interfaces/test_msgs/srv/LargeFixedService.srv": [
            "test_msgs.srv.LargeFixedService_Request",
            "test_msgs.srv.LargeFixedService_Response",
            "test_msgs.srv.LargeFixedService",
        ],
        "grace/interfaces/test_msgs/test_msgs/msg/SomeWeirdFileName.idl": [
            "Nottest_msgs.interfaces.messages.FirstStruct",
            "Nottest_msgs.interfaces.messages.SecondStruct",
            "Nottest_msgs.interfaces.services.ThirdStruct_Request",
            "Nottest_msgs.interfaces.services.ThirdStruct_Response",
            "Nottest_msgs.interfaces.services.ThirdStruct",
        ],
    },
    rosmsg_srcs = [":ros_msgs"],
    srcs_legacy_single_dir = False,
    visibility = ["//visibility:public"],
    wheel_data = ":test_msgs_pkg.wheel_data",
    deps = [
        "//grace/interfaces/action_msgs",
        "//grace/interfaces/builtin_interfaces",
        "//grace/interfaces/unique_identifier_msgs",
    ],
)

c_msgs_library(
    name = "test_msgs_c",
    msgs = ":test_msgs",
    visibility = ["//visibility:public"],
)

py_msgs_library(
    name = "test_msgs_py",
    msgs = ":test_msgs",
    visibility = ["//visibility:public"],
)

cpp_msgs_introspection_library(
    name = "test_msgs_introspection_cpp",
    ament_runfiles = True,
    msgs = ":test_msgs",
)

cc_library(
    name = "fixtures",
    hdrs = glob([
        "include/**/*.hpp",
    ]),
    data = [":test_msgs_introspection_cpp"],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        ":test_msgs",
    ],
)

py_library(
    name = "test_msgs_fixtures",
    srcs = glob([
        "src/test_msgs_fixtures/*.py",
    ]),
    data = [
        ":test_msgs_ament_resources",
        ":test_msgs_pkg.wheel_data",
    ],
    imports = ["src"],
    target_compatible_with = select({
        "//grace/rosidl/rules_rosidl:no_py_bindings": ["@platforms//:incompatible"],
        "//conditions:default": [],
    }),
    visibility = ["//visibility:public"],
    deps = [
        ":test_msgs_py",
    ],
)
