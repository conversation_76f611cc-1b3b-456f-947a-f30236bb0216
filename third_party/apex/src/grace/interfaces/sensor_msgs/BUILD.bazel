load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "ROSIDL_COMMON_PKGS")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

msgs_library(
    name = "sensor_msgs",
    srcs = glob([
        "msg/*.idl",
    ]),
    proto_structure = {
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/BatteryState.idl": [
            "sensor_msgs.msg.BatteryState",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/CameraInfo.idl": [
            "sensor_msgs.msg.CameraInfo",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/RegionOfInterest.idl": [
            "sensor_msgs.msg.RegionOfInterest",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/ChannelFloat32.idl": [
            "sensor_msgs.msg.ChannelFloat32",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/CompressedImage.idl": [
            "sensor_msgs.msg.CompressedImage",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/FluidPressure.idl": [
            "sensor_msgs.msg.FluidPressure",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/Illuminance.idl": [
            "sensor_msgs.msg.Illuminance",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/Image.idl": [
            "sensor_msgs.msg.Image",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/Imu.idl": [
            "sensor_msgs.msg.Imu",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/JointState.idl": [
            "sensor_msgs.msg.JointState",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/Joy.idl": [
            "sensor_msgs.msg.Joy",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/JoyFeedback.idl": [
            "sensor_msgs.msg.JoyFeedback",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/JoyFeedbackArray.idl": [
            "sensor_msgs.msg.JoyFeedbackArray",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/LaserEcho.idl": [
            "sensor_msgs.msg.LaserEcho",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/LaserScan.idl": [
            "sensor_msgs.msg.LaserScan",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/MagneticField.idl": [
            "sensor_msgs.msg.MagneticField",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/MultiDOFJointState.idl": [
            "sensor_msgs.msg.MultiDOFJointState",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/MultiEchoLaserScan.idl": [
            "sensor_msgs.msg.MultiEchoLaserScan",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/NavSatFix.idl": [
            "sensor_msgs.msg.NavSatFix",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/NavSatStatus.idl": [
            "sensor_msgs.msg.NavSatStatus",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/PointCloud.idl": [
            "sensor_msgs.msg.PointCloud",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/PointCloud2.idl": [
            "sensor_msgs.msg.PointCloud2",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/PointField.idl": [
            "sensor_msgs.msg.PointField",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/Range.idl": [
            "sensor_msgs.msg.Range",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/RelativeHumidity.idl": [
            "sensor_msgs.msg.RelativeHumidity",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/Temperature.idl": [
            "sensor_msgs.msg.Temperature",
        ],
        "grace/interfaces/sensor_msgs/sensor_msgs/msg/TimeReference.idl": [
            "sensor_msgs.msg.TimeReference",
        ],
        "grace/interfaces/sensor_msgs/srv/SetCameraInfo.srv": [
            "sensor_msgs.srv.SetCameraInfo_Request",
            "sensor_msgs.srv.SetCameraInfo_Response",
            "sensor_msgs.srv.SetCameraInfo",
        ],
    },
    rosmsg_srcs = glob([
        "srv/*.srv",
    ]),
    visibility = ["//visibility:public"],
    wheel_data = ":sensor_msgs_pkg.wheel_data",
    deps = [
        "@apex//grace/interfaces/geometry_msgs",
        "@apex//grace/interfaces/std_msgs",
    ],
)

cc_library(
    name = "sensor_msgs_helpers",
    hdrs = glob(["include/**/*.hpp"]),
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
)

apex_cc_test(
    name = "tests",
    srcs = glob(["test/*.cpp"]),
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":sensor_msgs",
        ":sensor_msgs_helpers",
        "@googletest//:gtest_main",
    ],
)

ros_pkg(
    name = "sensor_msgs_pkg",
    cc_libraries = [
        ":sensor_msgs_helpers",
    ],
    description = "A package containing some sensor data related message and service definitions.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    msg_libraries = [
        ":sensor_msgs",
    ],
    version = "2.2.3",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/interfaces/geometry_msgs:geometry_msgs_pkg",
        "@apex//grace/interfaces/std_msgs:std_msgs_pkg",
    ] + ROSIDL_COMMON_PKGS,
)
