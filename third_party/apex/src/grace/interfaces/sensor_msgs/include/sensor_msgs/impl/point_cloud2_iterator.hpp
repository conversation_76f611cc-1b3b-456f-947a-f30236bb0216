// Copyright (c) 2025, Apex.AI, Inc.
// Copyright (c) 2013, Open Source Robotics Foundation, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//
//    * Neither the name of the copyright holder nor the names of its
//      contributors may be used to endorse or promote products derived from
//      this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// This file is originally from:
// https://github.com/ros/common_msgs/blob/50ee957/sensor_msgs/include/sensor_msgs/impl/point_cloud2_iterator.h

#ifndef SENSOR_MSGS__IMPL__POINT_CLOUD2_ITERATOR_HPP_
#define SENSOR_MSGS__IMPL__POINT_CLOUD2_ITERATOR_HPP_

#include <cstdarg>

#include "cpputils/common_exceptions.hpp"

#include "sensor_msgs/msg/point_cloud2.hpp"

/**
 * \brief Private implementation used by PointCloud2Iterator
 * \author Vincent Rabaud
 */
/// @cond DOXYGEN_IGNORE_TO_AVOID_POLLUTING_GENERATED_DOCS
namespace
{
/** Return the size of a datatype (which is an enum of sensor_msgs::msg::PointField::) in bytes
 * @param datatype one of the enums of sensor_msgs::msg::PointField::
 */
inline int sizeOfPointField(int datatype)
{
  if ((datatype == sensor_msgs::msg::PointField::INT8) ||
    (datatype == sensor_msgs::msg::PointField::UINT8))
  {
    return 1;
  } else if ((datatype == sensor_msgs::msg::PointField::INT16) ||  // NOLINT
    (datatype == sensor_msgs::msg::PointField::UINT16))
  {
    return 2;
  } else if ((datatype == sensor_msgs::msg::PointField::INT32) ||  // NOLINT
    (datatype == sensor_msgs::msg::PointField::UINT32) ||
    (datatype == sensor_msgs::msg::PointField::FLOAT32))
  {
    return 4;
  } else if (datatype == sensor_msgs::msg::PointField::FLOAT64) {
    return 8;
  } else {
    throw apex::runtime_error("PointField of type", datatype, "does not exist");
  }
}

/** Private function that adds a PointField to the "fields" member of a PointCloud2
 * @tparam MsgType The variant of the PointCloud2, either flat or non-flat
 * @param cloud_msg the PointCloud2 to add a field to
 * @param name the name of the field
 * @param count the number of elements in the PointField
 * @param datatype the datatype of the elements
 * @param offset the offset of that element
 * @return the offset of the next PointField that will be added to the PointCloud2
 */
template <typename MsgType>
inline typename MsgType::_point_step_type addPointField(
  MsgType & cloud_msg,
  const sensor_msgs::string_type & name,
  sensor_msgs::msg::PointField::_count_type count,
  sensor_msgs::msg::PointField::_datatype_type datatype,
  sensor_msgs::msg::PointField::_offset_type offset)
{
  auto & point_field = cloud_msg.fields.emplace_back();
  point_field.name = name;
  point_field.count = count;
  point_field.datatype = datatype;
  point_field.offset = offset;

  // Update the offset
  return offset + point_field.count * sizeOfPointField(datatype);
}
}  // namespace
/// @endcond DOXYGEN_IGNORE_TO_AVOID_POLLUTING_GENERATED_DOCS
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

namespace sensor_msgs
{
template <typename MsgType>
inline PointCloud2Modifier<MsgType>::PointCloud2Modifier(
  MsgType & cloud_msg)
: cloud_msg_(cloud_msg)
{
}

template <typename MsgType>
inline size_t PointCloud2Modifier<MsgType>::size() const
{
  return cloud_msg_.data.size() / cloud_msg_.point_step;
}

template <typename MsgType>
inline void PointCloud2Modifier<MsgType>::reserve(size_t size)
{
  cloud_msg_.data.reserve(size * cloud_msg_.point_step);
}

template <typename MsgType>
inline void PointCloud2Modifier<MsgType>::resize(size_t size)
{
  cloud_msg_.data.resize(size * cloud_msg_.point_step);

  // Update height/width
  if (cloud_msg_.height == 1) {
    cloud_msg_.width = static_cast<uint32_t>(size);
    cloud_msg_.row_step = static_cast<uint32_t>(size * cloud_msg_.point_step);
  } else {
    if (cloud_msg_.width == 1) {
      cloud_msg_.height = static_cast<uint32_t>(size);
    } else {
      cloud_msg_.height = 1;
      cloud_msg_.width = static_cast<uint32_t>(size);
      cloud_msg_.row_step = static_cast<uint32_t>(size * cloud_msg_.point_step);
    }
  }
}

template <typename MsgType>
inline void PointCloud2Modifier<MsgType>::clear()
{
  cloud_msg_.data.clear();

  // Update height/width
  if (cloud_msg_.height == 1) {
    cloud_msg_.row_step = cloud_msg_.width = 0;
  } else {
    if (cloud_msg_.width == 1) {
      cloud_msg_.height = 0;
    } else {
      cloud_msg_.row_step = cloud_msg_.width = cloud_msg_.height = 0;
    }
  }
}

/**
 * @brief Function setting some fields in a PointCloud and adjusting the
 *        internals of the PointCloud2
 * @param n_fields the number of fields to add. The fields are given as
 *        triplets: name of the field as char*, number of elements in the
 *        field, the datatype of the elements in the field
 *
 * E.g, you create your PointCloud2 message with XYZ/RGB as follows:
 * <PRE>
 *   setPointCloud2FieldsByString(cloud_msg, 4, "x", 1, sensor_msgs::msg::PointField::FLOAT32,
 *                                              "y", 1, sensor_msgs::msg::PointField::FLOAT32,
 *                                              "z", 1, sensor_msgs::msg::PointField::FLOAT32,
 *                                              "rgb", 1, sensor_msgs::msg::PointField::FLOAT32);
 * </PRE>
 * WARNING: THIS DOES NOT TAKE INTO ACCOUNT ANY PADDING AS DONE UNTIL HYDRO
 * For simple usual cases, the overloaded setPointCloud2FieldsByString is what you want.
 */
template <typename MsgType>
inline void PointCloud2Modifier<MsgType>::setPointCloud2Fields(int n_fields, ...)
{
  cloud_msg_.fields.clear();
  cloud_msg_.fields.reserve(n_fields);
  va_list vl;
  va_start(vl, n_fields);
  auto offset = typename MsgType::_point_step_type{};
  for (int i = 0; i < n_fields; ++i) {
    // Create the corresponding PointField
    apex::string_strict128_t name(va_arg(vl, char *));
    const auto count = static_cast<sensor_msgs::msg::PointField::_count_type>(va_arg(vl, int));
    const auto datatype =
      static_cast<sensor_msgs::msg::PointField::_datatype_type>(va_arg(vl, int));
    offset = addPointField(cloud_msg_, name, count, datatype, offset);
  }
  va_end(vl);

  // Resize the point cloud accordingly
  cloud_msg_.point_step = offset;
  cloud_msg_.row_step = cloud_msg_.width * cloud_msg_.point_step;
  cloud_msg_.data.resize(cloud_msg_.height * cloud_msg_.row_step);
}

/**
 * @brief Function setting some fields in a PointCloud and adjusting the
 *        internals of the PointCloud2
 * @param n_fields the number of fields to add. The fields are given as
 *        strings: "xyz" (3 floats), "rgb" (3 uchar stacked in a float),
 *        "rgba" (4 uchar stacked in a float)
 * @return void
 *
 * WARNING: THIS FUNCTION DOES ADD ANY NECESSARY PADDING TRANSPARENTLY
 */
template <typename MsgType>
inline void PointCloud2Modifier<MsgType>::setPointCloud2FieldsByString(int n_fields, ...)
{
  cloud_msg_.fields.clear();
  cloud_msg_.fields.reserve(n_fields);
  va_list vl;
  va_start(vl, n_fields);
  int offset = 0;
  for (int i = 0; i < n_fields; ++i) {
    // Create the corresponding PointFields
    string_type field_name(va_arg(vl, char *));
    if (field_name == "xyz") {
      sensor_msgs::msg::PointField point_field;
      // Do x, y and z
      offset = addPointField(
        cloud_msg_, "x", 1, sensor_msgs::msg::PointField::FLOAT32, offset);
      offset = addPointField(
        cloud_msg_, "y", 1, sensor_msgs::msg::PointField::FLOAT32, offset);
      offset = addPointField(
        cloud_msg_, "z", 1, sensor_msgs::msg::PointField::FLOAT32, offset);
      offset += sizeOfPointField(sensor_msgs::msg::PointField::FLOAT32);
    } else {
      if ((field_name == "rgb") || (field_name == "rgba")) {
        offset = addPointField(
          cloud_msg_, field_name, 1, sensor_msgs::msg::PointField::FLOAT32,
          offset);
        offset += 3 * sizeOfPointField(sensor_msgs::msg::PointField::FLOAT32);
      } else {
        va_end(vl);
        throw apex::runtime_error("Field", field_name, "does not exist");
      }
    }
  }
  va_end(vl);

  // Resize the point cloud accordingly
  cloud_msg_.point_step = offset;
  cloud_msg_.row_step = cloud_msg_.width * cloud_msg_.point_step;
  cloud_msg_.data.resize(cloud_msg_.height * cloud_msg_.row_step);
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

namespace impl
{

/**
 */
template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
PointCloud2IteratorBase<T, TT, U, MsgType, V>::PointCloud2IteratorBase()
: data_char_(nullptr), data_(nullptr), data_end_(nullptr)
{
}

template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
PointCloud2IteratorBase<T, TT, U, MsgType, V>::PointCloud2IteratorBase(
  MsgType & cloud_msg, const string_type & field_name) :
  PointCloud2IteratorBase()
{
  // avoid invalid access
  if (cloud_msg.data.empty()) {
    return;
  }
  auto offset = set_field(cloud_msg, field_name);

  data_char_ = &(cloud_msg.data.front()) + offset;
  data_ = reinterpret_cast<TT *>(data_char_);
  data_end_ = reinterpret_cast<TT *>(&(cloud_msg.data.back()) + 1 + offset);
}

/** Assignment operator
 * @param iter the iterator to copy data from
 * @return a reference to *this
 */
template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
V<T, MsgType> & PointCloud2IteratorBase<T, TT, U, MsgType, V>::operator=(const V<T, std::remove_cv_t<MsgType>> & iter)
{
  if (this != &iter) {
    point_step_ = iter.point_step_;
    data_char_ = iter.data_char_;
    data_ = iter.data_;
    data_end_ = iter.data_end_;
    is_bigendian_ = iter.is_bigendian_;
  }

  return *this;
}

/** Assignment operator
 * @param iter the iterator to copy data from
 * @return a reference to *this
 */
template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
V<T, MsgType> & PointCloud2IteratorBase<T, TT, U, MsgType, V>::operator=(const V<T, const std::remove_cv_t<MsgType>> & iter)
{
  if (this != &iter) {
    point_step_ = iter.point_step_;
    data_char_ = iter.data_char_;
    data_ = iter.data_;
    data_end_ = iter.data_end_;
    is_bigendian_ = iter.is_bigendian_;
  }

  return *this;
}

/** Access the i th element starting at the current pointer (useful when a field has several elements of the same
 * type)
 * @param i
 * @return a reference to the i^th value from the current position
 */
template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
TT & PointCloud2IteratorBase<T, TT, U, MsgType, V>::operator[](size_t i) const
{
  return *(data_ + i);
}

/** Dereference the iterator. Equivalent to accessing it through [0]
 * @return the value to which the iterator is pointing
 */
template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
TT & PointCloud2IteratorBase<T, TT, U, MsgType, V>::operator*() const
{
  return *data_;
}

/** Increase the iterator to the next element
 * @return a reference to the updated iterator
 */
template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
V<T, MsgType> & PointCloud2IteratorBase<T, TT, U, MsgType, V>::operator++()
{
  data_char_ += point_step_;
  data_ = reinterpret_cast<TT *>(data_char_);
  return *static_cast<V<T, MsgType> *>(this);
}

/** Basic pointer addition
 * @param i the amount to increase the iterator by
 * @return an iterator with an increased position
 */
template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
V<T, MsgType> PointCloud2IteratorBase<T, TT, U, MsgType, V>::operator+(int i)
{
  V<T, MsgType> res = *static_cast<V<T, MsgType> *>(this);

  res.data_char_ += i * point_step_;
  res.data_ = reinterpret_cast<TT *>(res.data_char_);

  return res;
}

/** Increase the iterator by a certain amount
 * @return a reference to the updated iterator
 */
template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
V<T, MsgType> & PointCloud2IteratorBase<T, TT, U, MsgType, V>::operator+=(int i)
{
  data_char_ += i * point_step_;
  data_ = reinterpret_cast<TT *>(data_char_);
  return *static_cast<V<T, MsgType> *>(this);
}

/** Compare to another iterator
 * @return whether the current iterator points to a different address than the other one
 */
template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
bool PointCloud2IteratorBase<T, TT, U, MsgType, V>::operator!=(const V<T, std::remove_cv_t<MsgType>> & iter) const
{
  return iter.data_ != data_;
}

template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
bool PointCloud2IteratorBase<T, TT, U, MsgType, V>::operator!=(const V<T, const std::remove_cv_t<MsgType>> & iter) const
{
  return iter.data_ != data_;
}

template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
bool PointCloud2IteratorBase<T, TT, U, MsgType, V>::operator==(const V<T, std::remove_cv_t<MsgType>> & iter) const
{
  return iter.data_ == data_;
}

template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
bool PointCloud2IteratorBase<T, TT, U, MsgType, V>::operator==(const V<T, const std::remove_cv_t<MsgType>> & iter) const
{
  return iter.data_ == data_;
}

/** Return the end iterator
 * @return the end iterator (useful when performing normal iterator processing with ++)
 */
template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
V<T, MsgType> PointCloud2IteratorBase<T, TT, U, MsgType, V>::end() const
{
  V<T, MsgType> res = *static_cast<const V<T, MsgType> *>(this);
  res.data_ = data_end_;
  return res;
}

/** Common code to set the field of the PointCloud2
  * @param cloud_msg the PointCloud2 to modify
  * @param field_name the name of the field to iterate upon
  * @return the offset at which the field is found
  */
template<typename T, typename TT, typename U, typename MsgType, template<typename, typename> class V>
int PointCloud2IteratorBase<T, TT, U, MsgType, V>::set_field(
  const MsgType & cloud_msg, const string_type & field_name)
{
  is_bigendian_ = cloud_msg.is_bigendian;
  point_step_ = cloud_msg.point_step;
  // make sure the channel is valid
  auto field_iter = cloud_msg.fields.cbegin(),
    field_end =
    cloud_msg.fields.cend();
  while ((field_iter != field_end) && (field_iter->name != field_name)) {
    ++field_iter;
  }

  if (field_iter == field_end) {
    // Handle the special case of r,g,b,a (we assume they are understood as the
    // channels of an rgb or rgba field)
    if ((field_name == "r") || (field_name == "g") || (field_name == "b") || (field_name == "a")) {
      // Check that rgb or rgba is present
      field_iter = cloud_msg.fields.begin();
      while ((field_iter != field_end) && (field_iter->name != "rgb") &&
        (field_iter->name != "rgba"))
      {
        ++field_iter;
      }
      if (field_iter == field_end) {
        throw apex::runtime_error("Field", field_name, "does not exist");
      }
      if (field_name == "r") {
        if (is_bigendian_) {
          return field_iter->offset + 1U;
        } else {
          return field_iter->offset + 2U;
        }
      }
      if (field_name == "g") {
        if (is_bigendian_) {
          return field_iter->offset + 2U;
        } else {
          return field_iter->offset + 1U;
        }
      }
      if (field_name == "b") {
        if (is_bigendian_) {
          return field_iter->offset + 3U;
        } else {
          return field_iter->offset + 0U;
        }
      }
      if (field_name == "a") {
        if (is_bigendian_) {
          return field_iter->offset + 0U;
        } else {
          return field_iter->offset + 3U;
        }
      }
    } else {
      throw apex::runtime_error("Field", field_name, "does not exist");
    }
  }

  return field_iter->offset;
}

}  // namespace impl
}  // namespace sensor_msgs

#endif  // SENSOR_MSGS__IMPL__POINT_CLOUD2_ITERATOR_HPP_
