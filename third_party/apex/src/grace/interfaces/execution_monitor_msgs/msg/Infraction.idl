#include "process_manager_interfaces/msg/ProcessMetaData.idl"

//! [InfractionEvent]
module execution_monitor_msgs {
  enum ExpectationType {
    UNDEFINED,
    WALL_CLOCK_RUNTIME,
    CPU_CLOCK_RUNTIME,
    ACTIVATIONS_PER_WINDOW,
    ACTIVATION_DISTANCE
  };
  module msg {
    struct Infraction {
        int32 pid;
        process_manager_interfaces::msg::ProcessMetaData process_manager_metadata;
        string<256> monitor_name;
        string<32> task_id;
        ExpectationType expectation_type;
        string<256> expectation;
        uint32 counter;
        int64 deviation;
        int64 backend_overrun_ns;
    };
  };
};
//! [InfractionEvent]
