load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "ROSIDL_COMMON_PKGS")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

msgs_library(
    name = "action_msgs",
    srcs = glob([
        "msg/*.idl",
    ]),
    proto_structure = {
        "grace/interfaces/action_msgs/action_msgs/msg/GoalInfo.idl": [
            "action_msgs.msg.GoalInfo",
        ],
        "grace/interfaces/action_msgs/action_msgs/msg/GoalStatus.idl": [
            "action_msgs.msg.GoalStatus",
        ],
        "grace/interfaces/action_msgs/action_msgs/msg/GoalStatusArray.idl": [
            "action_msgs.msg.GoalStatusArray",
        ],
        "grace/interfaces/action_msgs/srv/CancelGoal.srv": [
            "action_msgs.srv.CancelGoal_Request",
            "action_msgs.srv.CancelGoal_Response",
            "action_msgs.srv.CancelGoal",
        ],
    },
    rosmsg_srcs = glob([
        "srv/*.srv",
    ]),
    tags = ["integrity QM"],
    visibility = ["//visibility:public"],
    wheel_data = ":action_msgs_pkg.wheel_data",
    deps = [
        "@apex//grace/interfaces/builtin_interfaces",
        "@apex//grace/interfaces/unique_identifier_msgs",
    ],
)

ros_pkg(
    name = "action_msgs_pkg",
    description = "Messages and service definitions common among all ROS actions.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI, Inc.",
    msg_libraries = [
        ":action_msgs",
    ],
    version = "1.0.3",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/interfaces/builtin_interfaces:builtin_interfaces_pkg",
        "@apex//grace/interfaces/unique_identifier_msgs:unique_identifier_msgs_pkg",
    ] + ROSIDL_COMMON_PKGS,
)
