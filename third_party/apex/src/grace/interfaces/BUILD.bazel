load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")

msgs_library(
    name = "interfaces",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/interfaces/common_interfaces",  # contains all the typical interfaces
        "@apex//grace/interfaces/action_msgs",
        "@apex//grace/interfaces/apex_msgs",
        "@apex//grace/interfaces/composition_interfaces",
        "@apex//grace/monitoring/event_registry_interfaces",
        "@apex//grace/interfaces/execution_monitor_msgs",
        "@apex//grace/interfaces/lifecycle_msgs",
        "@apex//grace/interfaces/process_manager_interfaces",
        "@apex//grace/interfaces/rcl_interfaces",
        "@apex//grace/interfaces/replay_msgs",
        "@apex//grace/interfaces/rosgraph_msgs",
        # TODO(22699): "@apex//grace/interfaces/sametype_interfaces",
        "@apex//grace/interfaces/statistics_msgs",
        "@apex//grace/interfaces/storage_msgs",
        # TODO(22699): "@apex//grace/interfaces/test_interface_files",
        "@apex//grace/interfaces/test_msgs",
        "@apex//grace/interfaces/timer_service_msgs",
        "@apex//grace/interfaces/unique_identifier_msgs",
        "@apex//grace/monitoring/dispatcher_interfaces",
        "@apex//grace/recording/rosbag2/rosbag2_interfaces",
        "@apex//grace/ros/rmw_dds_common",
        "@rosbridge_suite//rosbridge_msgs",
        "@apex//grace/automotive_diagnose/uds_msgs",
    ],
)
