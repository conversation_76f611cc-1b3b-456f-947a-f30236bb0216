// Copyright 2022 Apex.AI, Inc
// All rights reserved.

#include "uds_server/services/did/did_pool.hpp"

#include <algorithm>  // To use std::find_if
#include <cstdint>
#include <memory>  // To use std::shared_ptr
#include <utility>  // To use std::move

namespace apex
{
namespace uds_server
{
namespace services
{
namespace did
{

DidPool::DidPool(DidAccessPtr did_access, DidEntries && did_entries)
: m_did_access{did_access}, m_did_entries{std::move(did_entries)}
{
  // the DidAccess cannot be nullptr
  assert(m_did_access != nullptr);
}

DidReaderResult DidPool::find_did_reader(const uint16_t did,
                                         const shared::AccessToken & token,
                                         const shared::UdsSid req_sid) const
{
  DidReaderPtr reader{nullptr};
  auto status = shared::AccessStatus::ALLOWED;

  auto maybe_entry = find_entry(did);
  if (maybe_entry && maybe_entry->m_reader) {
    status = m_did_access->is_did_accessible(token, req_sid, did);
    if (status == shared::AccessStatus::ALLOWED) {
      reader = maybe_entry->m_reader;
    }
  } else {
    status = shared::AccessStatus::RESOURCE_NOT_FOUND;
  }

  return std::make_pair(status, reader);
}

DidWriterResult DidPool::find_did_writer(const uint16_t did,
                                         const shared::AccessToken & token,
                                         const shared::UdsSid req_sid) const
{
  DidWriterPtr writer{nullptr};
  auto status = shared::AccessStatus::ALLOWED;

  auto maybe_entry = find_entry(did);
  if (maybe_entry && maybe_entry->m_writer) {
    status = m_did_access->is_did_accessible(token, req_sid, did);
    if (status == shared::AccessStatus::ALLOWED) {
      writer = maybe_entry->m_writer;
    }
  } else {
    status = shared::AccessStatus::RESOURCE_NOT_FOUND;
  }

  return std::make_pair(status, writer);
}

void DidPool::register_did_readers_observer(const std::shared_ptr<DidReaderObserver> & observer)
{
  uint16_t PeriodicDidPrefix = 0xF200u;
  uint16_t PrefixMask = 0xFF00u;
  for (auto & entry : m_did_entries) {
    if (entry.m_reader) {
      if (PeriodicDidPrefix != (entry.m_did & PrefixMask)) {
        entry.m_reader->register_observer(observer);
      }
    }
  }
}

void DidPool::register_did_periodic_readers_observer(
  const std::shared_ptr<DidReaderObserver> & observer)
{
  uint16_t PeriodicDidPrefix = 0xF200u;
  uint16_t PrefixMask = 0xFF00u;
  for (auto & entry : m_did_entries) {
    if (entry.m_reader) {
      if (PeriodicDidPrefix == (entry.m_did & PrefixMask)) {
        entry.m_reader->register_periodic_observer(observer);
      }
    }
  }
}

void DidPool::register_did_writers_observer(const std::shared_ptr<DidWriterObserver> & observer)
{
  for (auto & entry : m_did_entries) {
    if (entry.m_writer) {
      entry.m_writer->register_observer(observer);
    }
  }
}

apex::optional<DidEntry> DidPool::find_entry(const uint16_t did) const
{
  const auto entry_it = std::find_if(m_did_entries.begin(),
                                     m_did_entries.end(),
                                     [did](const auto & entry) { return entry.m_did == did; });

  return entry_it != m_did_entries.end() ? apex::make_optional(*entry_it) : apex::nullopt;
}

}  // namespace did
}  // namespace services
}  // namespace uds_server
}  // namespace apex
