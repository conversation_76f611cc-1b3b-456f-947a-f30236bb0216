// Copyright 2022 Apex.AI, Inc
// All rights reserved.

#include "uds_server/services/did/async_did_reader.hpp"

#include <cassert>  // To use assert
#include <memory>  // To use std::shared_ptr
#include <utility>  // To use std::move

#include "uds_server/bringup/config.hpp"
#include "uds_server/services/did/observers.hpp"

namespace apex
{
namespace uds_server
{
namespace services
{
namespace did
{

AsyncDidReader::AsyncDidReader(const diagnostic_common::NodeContextRef & node_context,
                               const string_strict256_t & service_name,
                               const uint16_t did)
: DidReader{did},
  ReadServiceClient{
    node_context, service_name, logging::Logger<>{&node_context.node, "AsyncDidReader"}}
{
}

bool AsyncDidReader::is_available() const
{
  return is_ready();
}

void AsyncDidReader::on_response(const ReadResponse & msg)
{
  if (auto observer = m_observer.lock()) {
    if (msg.success) {
      observer->notify_about_read_success(m_request_id, get_did(), msg.payload);
    } else {
      observer->notify_about_read_error(m_request_id, get_did());
    }
  }
  if (auto observer = m_periodicObserver.lock()) {
    if (msg.success) {
      observer->notify_about_read_success(m_request_id, get_did(), msg.payload);
    } else {
      observer->notify_about_read_error(m_request_id, get_did());
    }
  }
}

void AsyncDidReader::on_error()
{
  if (auto observer = m_observer.lock()) {
    observer->notify_about_read_error(m_request_id, get_did());
  }
  if (auto observer = m_periodicObserver.lock()) {
    observer->notify_about_read_error(m_request_id, get_did());
  }
}

void AsyncDidReader::read(const uint64_t request_id)
{
  // make sure the reader is not busy
  assert(!is_busy());
  m_request_id = request_id;

  if (!is_available() ||
      !async_send_request([this](auto & request) { request.did = get_did(); },
                          std::chrono::milliseconds{bringup::ASYNC_SERVICE_REQUEST_TIMEOUT})) {
    on_error();
  }
}

}  // namespace did
}  // namespace services
}  // namespace uds_server
}  // namespace apex
