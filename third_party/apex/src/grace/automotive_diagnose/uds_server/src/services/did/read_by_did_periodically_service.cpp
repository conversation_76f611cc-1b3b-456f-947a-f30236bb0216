// Copyright 2022 Apex.AI, Inc
// All rights reserved.

#include "uds_server/services/did/read_by_did_periodically_service.hpp"

#include <algorithm>  // To use std::find_if
#include <chrono>
#include <cstdint>
#include <memory>  // To use std::shared_ptr<>
#include <utility>  // To use std::move

#include "logging/logging_macros.hpp"  // To use APEX_INFO
#include "rclcpp/qos.hpp"  // To use rclcpp::DefaultQoS
#include "uds_server/bringup/config.hpp"
#include "uds_server/services/shared/format.hpp"


namespace apex::uds_server::services::did
{

void ReadByDidDataPeriodically::take(const shared::ServiceRequest & request,
                                     const std::shared_ptr<DidReadersPool> & did_pool)
{
  // SID(1Byte) + TM(1Byte) + DataID(1Byte)*n

  uint32_t idx = INDEX_DID;
  bool isAnySucced = false;

  // clear nrc for new request
  nrc = apex::nullopt;

  // the DidReadersPool cannot be nullptr
  assert(did_pool != nullptr);

  // is received request is terminate
  isCloseRequested = false;
  // new request received, do not send periodic messages
  freezePeriodicMsg = true;

  request_id = request.request_id;

  tester_address = request.tester_address;

  // Get Requested Mode
  TransmissionModeDataType requested_tm =
    static_cast<TransmissionModeDataType>(request.payload[INDEX_TM]);
  // check received request
  if ((TransmissionModeDataType::TM_RESERVED_BOT == requested_tm) ||
      (TransmissionModeDataType::TM_RESERVED_TOP <= requested_tm)) {
    // terminate operation, undefined  transmission mode
    set_error(shared::access_status_to_nrc(shared::AccessStatus::RESOURCE_NOT_FOUND));
    return;
  } else if (INDEX_DID ==
             request.payload.size())  // if request only including TM, it should be stop sending
  {
    if (TransmissionModeDataType::STOP_SENDING == requested_tm) {
      // If request dont include any DID code, clean all list
      isCloseRequested = true;
      isAnySucced = true;
    } else {
      // request should include also did for read requests
      set_error(shared::access_status_to_nrc(shared::AccessStatus::RESOURCE_NOT_FOUND));
      return;
    }
  } else  // if size is between 3 to 18 >> scan for all requested did
  {
    while (idx < request.payload.size()) {
      auto generatedDID = MASK_PERIODIC_PREID | static_cast<uint16_t>(request.payload[idx]);

      if (TransmissionModeDataType::STOP_SENDING == requested_tm) {
        removeDataID(generatedDID);  // remove requested periodic did from scan list
        isAnySucced = true;
      } else {
        if (periodic_scan_list.size() < MAX_NUM_OF_PERIODIC) {
          // if DataID is found, check conditions
          const auto find_result =
            did_pool->find_did_reader(generatedDID,
                                      request.access_token,
                                      shared::UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ);
          const auto status = find_result.first;
          auto reader = find_result.second;
          const bool access_violation = (status != shared::AccessStatus::ALLOWED) &&
                                        (status != shared::AccessStatus::NOT_SUPPORTED_IN_SESSION);
          if (access_violation) {
            // did specific errors will be considered if "isAnySucced is false" at end of function
          } else {
            addDataID(generatedDID, requested_tm, reader);
            isAnySucced = true;
          }
        } else {
          // list already full, terminate scanning
          break;
        }
      }
      idx++;
    }
  }

  if (true == isAnySucced)  // after scan all, decide response type
  {
    // add positive response SID
    response_data.clear();
    response_data.push_back(
      static_cast<uint8_t>(shared::UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_POS_RESP));
    updateTimeTrigger();
  } else {
    // if conditions are not correct terminate operation
    set_error(shared::access_status_to_nrc(shared::AccessStatus::RESOURCE_NOT_FOUND));
    return;
  }
}

// this will clear all which related with periodic operation
void ReadByDidDataPeriodically::cleanup()
{
  periodic_scan_list.clear();  // Remove all entries
  available = true;
  response_data.clear();
  nrc = apex::nullopt;
  isCloseRequested = true;
}

bool ReadByDidDataPeriodically::is_valid() const
{
  return !nrc;
}

bool ReadByDidDataPeriodically::is_available() const
{
  return available;
}

shared::UdsResponseCode ReadByDidDataPeriodically::error_reason() const
{
  return nrc.value();
}

void ReadByDidDataPeriodically::set_error(shared::UdsResponseCode error)
{
  nrc = error;
}

void ReadByDidDataPeriodically::removeDataID(const uint16_t dataID)
{
  periodic_scan_list.erase(dataID);
}

void ReadByDidDataPeriodically::addDataID(const uint16_t dataID,
                                          const TransmissionModeDataType period,
                                          const std::shared_ptr<DidReader> & reader)
{
  periodic_scan_list[dataID] = {.data_send_period = period, .reader = reader.get()};
}

void ReadByDidDataPeriodically::updateTimeTrigger(void)
{
  m_testerRequestPeriods.slowTimerActive = false;
  m_testerRequestPeriods.midTimerActive = false;
  m_testerRequestPeriods.fastTimerActive = false;

  if (!periodic_scan_list.empty() && !isCloseRequested) {
    for (const auto & [id, item] : periodic_scan_list) {
      switch (item.data_send_period) {
        case TransmissionModeDataType::TM_SLOW:
          m_testerRequestPeriods.slowTimerActive = true;
          break;
        case TransmissionModeDataType::TM_MID:
          m_testerRequestPeriods.midTimerActive = true;
          break;
        case TransmissionModeDataType::TM_FAST:
          m_testerRequestPeriods.fastTimerActive = true;
          break;
        default:
          break;
      }
    }
  }
}

void ReadByDidDataPeriodically::periodicRequestDataApplication(DidReader * reader)
{
  if (reader->is_available()) {
    reader->read(request_id);
  }
}


ReadByDidPeriodicallyService::ReadByDidPeriodicallyService(
  shared::ResponsePublisherPtr response_pub,
  std::shared_ptr<DidReadersPool> did_pool,
  const diagnostic_common::NodeContextRef & context,
  std::shared_ptr<diagnostic_common::timer::TimerBase> timer,
  ReadByDidDataPeriodicallyConfig config)
: DiagnosticService(response_pub),
  m_did_pool{did_pool},
  m_logger{&context.node, "ReadByDidPeriodicallyService"},
  m_timer{timer},
  m_config{config}
{
}

void ReadByDidPeriodicallyService::notify_about_read_success(const uint64_t request_id,
                                                             const uint16_t did,
                                                             const DataRecord & data)
{
  if (false == get_data().freezePeriodicMsg) {
    // if returned data, send it directly, each result should be sent 1 by 1
    // prepare response buffer >> 1byte ID + data
    get_data().response_data.clear();
    get_data().response_data.push_back(static_cast<uint8_t>(did & MASK_PERIODIC_RESID));
    (void)std::copy(data.begin(), data.end(), std::back_inserter(get_data().response_data));

    // send it
    const auto response = to_positive_response();
    m_response_pub->publish_positive_response(response);
  }
}

void ReadByDidPeriodicallyService::notify_about_read_error(const uint64_t request_id,
                                                           const uint16_t did)
{
  APEX_WARN(m_logger, "Sending negative response: Could not read DID=", shared::format_did(did));
  // for 0x2A, if read failed we are not sending any NRC
}

bool ReadByDidPeriodicallyService::has_valid_msg_size(const shared::ServiceRequest & request) const
{
  // size shall be even after subtracting SID byte
  const auto payload_size = request.payload.size();

  return (payload_size >= REQ_MSG_LNGTH_MIN) && (payload_size <= REQ_MSG_LNGTH_MAX);
}

void ReadByDidPeriodicallyService::cancel_active_requests()
{
  if (get_data().slow_timer_id.has_value()) {
    m_timer->remove_timeout(get_data().slow_timer_id.value());
    get_data().slow_timer_id.reset();
  }
  if (get_data().mid_timer_id.has_value()) {
    m_timer->remove_timeout(get_data().mid_timer_id.value());
    get_data().mid_timer_id.reset();
  }
  if (get_data().fast_timer_id.has_value()) {
    m_timer->remove_timeout(get_data().fast_timer_id.value());
    get_data().fast_timer_id.reset();
  }

  clear_data();
}

void ReadByDidPeriodicallyService::remove_timeouts()
{
  // Remove active timers if any
  if (get_data().m_testerRequestPeriods.slowTimerActive) {
    if (get_data().slow_timer_id.has_value()) {
      m_timer->remove_timeout(get_data().slow_timer_id.value());
      get_data().slow_timer_id.reset();
    }
  }

  if (get_data().m_testerRequestPeriods.midTimerActive) {
    if (get_data().mid_timer_id.has_value()) {
      m_timer->remove_timeout(get_data().mid_timer_id.value());
      get_data().mid_timer_id.reset();
    }
  }

  if (get_data().m_testerRequestPeriods.fastTimerActive) {
    if (get_data().fast_timer_id.has_value()) {
      m_timer->remove_timeout(get_data().fast_timer_id.value());
      get_data().fast_timer_id.reset();
    }
  }
}

void ReadByDidPeriodicallyService::do_process_request(const shared::ServiceRequest & request)
{
  // find data buffer
  get_data().take(request, m_did_pool);

  APEX_DEBUG(m_logger, "request id = ", static_cast<int>(get_data().request_id));

  // check result; if we have Nrc, send negative else positive
  if (!get_data().is_valid()) {
    const auto nrc = get_data().error_reason();

    const shared::NegativeResponse response{
      request.request_id, request.tester_address, get_sid(), nrc};
    m_response_pub->publish_negative_response(response);
    APEX_DEBUG(m_logger, "response.error_code: ", static_cast<int>(response.error_code));
    APEX_DEBUG(m_logger, "response.service_id: ", static_cast<int>(response.service_id));

  } else {
    const auto response = to_positive_response();
    m_response_pub->publish_positive_response(response);
    get_data().response_data.clear();
  }

  // can go on, active request is responded
  get_data().freezePeriodicMsg = false;

  // Remove all active timers
  remove_timeouts();

  // if no active periodic DID or close command received
  if ((get_data().periodic_scan_list.empty()) || (true == get_data().isCloseRequested)) {
    clear_data();

  } else {
    if (get_data().m_testerRequestPeriods.slowTimerActive) {
      uint32_t ID = m_timer->add_timeout(
        m_config.slow_read_period_ms, [this](uint32_t ID) { timer_callback_handler(ID); }, false);
      get_data().slow_timer_id = ID;
      m_timerCallbackVector.push_back(
        timerCallback{get_data().slow_timer_id.value(), TransmissionModeDataType::TM_SLOW});
      APEX_DEBUG(
        m_logger, "slow timer  ID: ", static_cast<int>(get_data().slow_timer_id.value_or(-1)));
    }

    if (get_data().m_testerRequestPeriods.midTimerActive) {
      uint32_t ID = m_timer->add_timeout(
        m_config.mid_read_period_ms, [this](uint32_t ID) { timer_callback_handler(ID); }, false);
      get_data().mid_timer_id = ID;
      m_timerCallbackVector.push_back(
        timerCallback{get_data().mid_timer_id.value(), TransmissionModeDataType::TM_MID});
      APEX_DEBUG(
        m_logger, "mid timer  ID:", static_cast<int>(get_data().mid_timer_id.value_or(-1)));
    }

    if (get_data().m_testerRequestPeriods.fastTimerActive) {
      uint32_t ID = m_timer->add_timeout(
        m_config.fast_read_period_ms, [this](uint32_t ID) { timer_callback_handler(ID); }, false);
      get_data().fast_timer_id = ID;
      m_timerCallbackVector.push_back(
        timerCallback{get_data().fast_timer_id.value(), TransmissionModeDataType::TM_FAST});
      APEX_DEBUG(
        m_logger, "fast timer  ID: ", static_cast<int>(get_data().fast_timer_id.value_or(-1)));
    }
    APEX_DEBUG(m_logger, "slow timer  ID", static_cast<int>(get_data().slow_timer_id.value_or(-1)));
    APEX_DEBUG(m_logger, "mid timer  ID", static_cast<int>(get_data().mid_timer_id.value_or(-1)));
    APEX_DEBUG(m_logger, "fast timer  ID", static_cast<int>(get_data().fast_timer_id.value_or(-1)));
    APEX_DEBUG(
      m_logger, "slow timer  period: ", static_cast<int>(m_config.slow_read_period_ms.count()));
    APEX_DEBUG(
      m_logger, "slow timer  period: ", static_cast<int>(m_config.mid_read_period_ms.count()))
    APEX_DEBUG(
      m_logger, "slow timer  period: ", static_cast<int>(m_config.fast_read_period_ms.count()));
  }
}
}  // namespace apex::uds_server::services::did
