// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include "uds_server/services/shared/access_parser.hpp"

#include <memory>  // for std::shared_ptr
#include <utility>  // for std::move, std::make_pair
#include <vector>  // for std::vector

#include "containers/static_vector.hpp"
#include "cpputils/safe_cast.hpp"  // for safe_cast<>
#include "logging/logging_macros.hpp"
#include "settings/construct/getters.hpp"  // for get<>
#include "uds_server/bringup/config.hpp"
#include "uds_server/services/routine/routine_types.hpp"  // for RoutineId
#include "uds_server/services/security/security_access.hpp"  // for SecurityLevel
#include "uds_server/services/shared/format.hpp"


namespace apex
{
namespace uds_server
{
namespace services
{
namespace shared
{
namespace
{
using settings::inspect::array_view;
using settings::inspect::dictionary_view;
using settings::inspect::get;
using settings::inspect::integer;
using settings::inspect::maybe;
using settings::inspect::node_view;
using settings::inspect::string_view;

using config_error = diagnostic_common::config_error;

bool is_sid_supported(const services::shared::UdsSid sid)
{
  using services::shared::UdsSid;

  switch (sid) {
    case UdsSid::DIAGNOSTIC_SESSION_CONTROL_REQ:
      [[fallthrough]];
    case UdsSid::SECURITY_ACCESS_REQ:
      [[fallthrough]];
    case UdsSid::TESTER_PRESENT_REQ:
      [[fallthrough]];
    case UdsSid::CONTROL_DTC_SETTINGS_REQ:
      [[fallthrough]];
    case UdsSid::READ_DATA_BY_IDENTIFIER_REQ:
      [[fallthrough]];
    case UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ:
      [[fallthrough]];
    case UdsSid::WRITE_DATA_BY_IDENTIFIER_REQ:
      [[fallthrough]];
    case UdsSid::CLEAR_DIAGNOSTIC_INFORMATION_REQ:
      [[fallthrough]];
    case UdsSid::READ_DTC_INFORMATION_REQ:
      [[fallthrough]];
    case UdsSid::ROUTINE_CONTROL_REQ:
      [[fallthrough]];
    case UdsSid::INPUT_OUTPUT_CONTROL_BY_IDENTIFIER_REQ:
      return true;
    default:
      return false;
  }
}

bool is_did_supported(const services::shared::UdsSid sid)
{
  constexpr size_t NUM_OF_SRV_WITH_DIDS = 4U;
  using services::shared::UdsSid;

  constexpr std::array<UdsSid, NUM_OF_SRV_WITH_DIDS> SRV_WITH_DIDS{
    UdsSid::READ_DATA_BY_IDENTIFIER_REQ,
    UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ,
    UdsSid::WRITE_DATA_BY_IDENTIFIER_REQ,
    UdsSid::INPUT_OUTPUT_CONTROL_BY_IDENTIFIER_REQ,
  };

  const auto iter = std::find_if(
    SRV_WITH_DIDS.begin(), SRV_WITH_DIDS.end(), [sid](const auto s) { return sid == s; });

  return iter != SRV_WITH_DIDS.end();
}

// TODO(bartosz.burda): just a dummy impl
// this must be changed in Auth Service impl
services::auth::AuthRoles str_to_role(string_view str)
{
  services::auth::AuthRoles role;
  if (str == "ROLE_1") {
    role = services::auth::AuthRoles::ROLE_1;
  } else if (str == "ROLE_2") {
    role = services::auth::AuthRoles::ROLE_2;
  } else if (str == "ROLE_3") {
    role = services::auth::AuthRoles::ROLE_3;
  } else {
    throw config_error("Error: unknown role= ", str);
  }
  return role;
}

services::shared::AccessPolicyPtr parse_access_policy(node_view cfg, uint8_t session_id)
{
  std::vector<services::security::SecurityLevel> sec_lvls;
  auto maybe_sec_lvls = get<maybe<array_view>>(cfg, "security_levels");
  if (maybe_sec_lvls) {
    for (const auto sec_level_view : maybe_sec_lvls.value()) {
      auto maybe_sec_lvl = get<maybe<integer>>(sec_level_view);
      if (!maybe_sec_lvl) {
        throw config_error("Error: Missing value of a \"security_level\" in \"security_levels\"!");
      }
      services::security::SecurityLevel sec_lvl_value;
      try {
        sec_lvl_value = cast::safe_cast<uint8_t>(maybe_sec_lvl.value());
      } catch (const cast::cast_error &) {
        throw config_error("Error: Invalid \"security_level\" value:",
                           maybe_sec_lvl.value(),
                           " The \"security_level\" must be one byte long!");
      }
      if (!services::security::is_security_level_valid(sec_lvl_value)) {
        throw config_error("Error: Invalid \"security_level\" value:",
                           sec_lvl_value,
                           " The \"security_level\" value must be an odd value in range <",
                           services::security::SECURITY_LEVEL_MIN_VALUE,
                           ",",
                           services::security::SECURITY_LEVEL_MAX_VALUE,
                           ">!");
      }
      sec_lvls.push_back(sec_lvl_value);
    }

    if (sec_lvls.empty()) {
      throw config_error(
        "Error: A \"security_level\" array cannot be empty! Specify at least one level or remove "
        "the tag completely for an unrestricted access.");
    }
  }

  auto maybe_role_view = get<maybe<array_view>>(cfg, "role");
  services::auth::AllowedRoles allowed_roles;
  if (maybe_role_view) {
    if (maybe_role_view->empty()) {
      throw config_error("Error: The array with roles can not be empty!");
    }
    // means that only selected roles are available
    for (auto & allowed_role : allowed_roles) {
      allowed_role = false;
    }
    // iterate over defined roles and set true to correct indexes
    for (const auto role : maybe_role_view.value()) {
      auto maybe_role_str = get<maybe<string_view>>(role);
      if (!maybe_role_str) {
        throw config_error("Error: The role value must be a string with the role name!");
      }
      const auto auth_role = str_to_role(maybe_role_str.value());
      allowed_roles[static_cast<uint8_t>(auth_role)] = true;
    }
  } else {
    // no role views means that all roles are available
    for (auto & allowed_role : allowed_roles) {
      allowed_role = true;
    }
  }

  std::vector<env::EnvCondition> env_conditions;
  auto maybe_env_view = get<maybe<array_view>>(cfg, "env");
  if (maybe_env_view) {
    env_conditions.reserve(maybe_env_view->size());
    for (const auto env : maybe_env_view.value()) {
      auto maybe_env_key = get<maybe<integer>>(env, "key");
      if (!maybe_env_key) {
        throw config_error("Error: Missing value of a \"key\" in \"env\"!");
      }
      uint8_t env_key;
      try {
        env_key = cast::safe_cast<uint8_t>(maybe_env_key.value());
      } catch (const cast::cast_error &) {
        throw config_error("Error: Invalid env \"key\":",
                           maybe_env_key.value(),
                           " The env \"key\" must be a single byte long!");
      }
      auto maybe_env_value = get<maybe<integer>>(env, "value");
      if (!maybe_env_value) {
        throw config_error("Error: Missing value of a \"value\" in \"env\"!");
      }
      uint8_t env_value;
      try {
        env_value = cast::safe_cast<uint8_t>(maybe_env_value.value());
      } catch (const cast::cast_error &) {
        throw config_error("Error: Invalid env \"value\":",
                           maybe_env_value.value(),
                           " The env \"value\" must be a single byte long!");
      }
      env_conditions.emplace_back(env_key, env_value);
    }
  }

  using UdsAddress = uds_msgs::msg::UdsAddress;
  std::vector<UdsAddress> allowed_addresses;
  auto maybe_addresses_view = get<maybe<array_view>>(cfg, "allowed_addresses");
  if (maybe_addresses_view) {
    allowed_addresses.reserve(maybe_addresses_view->size());
    for (const auto & address : maybe_addresses_view.value()) {
      try {
        const auto address_value = cast::safe_cast<uint16_t>(get<integer>(address));
        // TODO(bartosz.burda): only physical addresses are supported right now
        allowed_addresses.push_back(
          UdsAddress::create_msg(UdsAddress::UdsAddressTypeEnum::PHYSICAL, address_value));
      } catch (const cast::cast_error &) {
        throw config_error(
          "Error: The \"allowed_addresses\""
          " needs to be an array of 2 bytes data!");
      }
    }
  }

  return std::make_shared<services::shared::AccessPolicy>(
    session_id,
    apex::static_vector<services::security::SecurityLevel>{
      sec_lvls.size(), sec_lvls.begin(), sec_lvls.end()},
    std::move(allowed_roles),
    apex::static_vector<env::EnvCondition>{
      env_conditions.size(), env_conditions.begin(), env_conditions.end()},
    apex::static_vector<UdsAddress>{
      allowed_addresses.size(), allowed_addresses.begin(), allowed_addresses.end()});
}

}  // namespace

services::shared::AccessMap parse_access_control(dictionary_view cfg)
{
  services::shared::AccessMap access_map;
  auto access_ctrl_view = get<maybe<array_view>>(cfg, "diagnostic/uds/access_control");
  // the access control is optional, without it we just return an empty map
  // that will allow everyone to access everything
  if (access_ctrl_view) {
    for (const auto & access_view : access_ctrl_view.value()) {
      auto sid = get<maybe<integer>>(access_view, "sid");
      if (!sid) {
        throw config_error("Error: The \"access_control\" item must define a \"sid\"!");
      }
      services::shared::UdsSid sid_value;
      try {
        // TODO(mateusz.wierzchowski): #23409 Implement enum safe casts of some sorts
        sid_value = static_cast<services::shared::UdsSid>(cast::safe_cast<uint8_t>(sid.value()));
      } catch (const cast::cast_error &) {
        throw config_error("Error! Invalid \"sid\" value:",
                           sid.value(),
                           " The \"sid\" value must be one byte long!");
      }

      if (!is_sid_supported(sid_value)) {
        throw config_error(
          "Error: The \"sid\" value:", format_sid(sid_value), " is not supported!");
      }

      auto session_id = get<maybe<integer>>(access_view, "session_id");
      if (!session_id) {
        throw config_error("Error: The \"access_control\" must have defined a \"session_id\"!");
      }
      uint8_t session_id_value;
      try {
        session_id_value = cast::safe_cast<uint8_t>(session_id.value());
      } catch (const cast::cast_error &) {
        throw config_error("Error: The \"session_id\" value shall be one byte long!");
      }

      // find accesses for this sid
      auto & access = access_map[sid_value];

      const auto srv_access = parse_access_policy(access_view, session_id_value);
      access.service_accesses.push_back(srv_access);

      if (is_did_supported(sid_value)) {
        auto maybe_dids = get<maybe<array_view>>(access_view, "dids");
        if (maybe_dids) {
          for (const auto & did_view : maybe_dids.value()) {
            auto maybe_did_value = get<maybe<integer>>(did_view, "did");
            if (!maybe_did_value) {
              throw config_error(
                "Error: Missing the \"did\" value in access control of the"
                " service \"sid\":",
                format_sid(sid_value),
                "!");
            }

            uint16_t did_value;
            try {
              did_value = cast::safe_cast<uint16_t>(maybe_did_value.value());
            } catch (const cast::cast_error &) {
              throw config_error("Error: Invalid \"did\" value: ",
                                 maybe_did_value.value(),
                                 " The \"did\" value must be two bytes long!");
            }

            auto did_access = parse_access_policy(did_view, session_id_value);
            access.did_accesses[did_value].push_back(std::move(did_access));
          }
        }
      }

      auto maybe_subfunctions = get<maybe<array_view>>(access_view, "subfunctions");
      if (maybe_subfunctions) {
        for (const auto & subfunction_view : maybe_subfunctions.value()) {
          auto maybe_subfunction_value = get<maybe<integer>>(subfunction_view, "subfunction");
          if (!maybe_subfunction_value) {
            throw config_error(
              "Error: Missing the \"subfunction\" value in access control of the"
              " service \"sid\":",
              static_cast<uint8_t>(sid_value),
              "!");
          }

          uint8_t subfunction;
          try {
            subfunction = cast::safe_cast<uint8_t>(maybe_subfunction_value.value());
          } catch (const cast::cast_error &) {
            throw config_error("Error: Invalid \"subfunction\" value: ",
                               maybe_subfunction_value.value(),
                               " The \"subfunction\" value must be one byte long integer!");
          }

          auto subfunction_access = parse_access_policy(subfunction_view, session_id_value);
          access.subfunction_accesses[subfunction].push_back(std::move(subfunction_access));
        }
      }

      auto maybe_routines = get<maybe<array_view>>(access_view, "routines");
      if (maybe_routines) {
        for (const auto & routine_view : maybe_routines.value()) {
          auto maybe_routine_id = get<maybe<integer>>(routine_view, "routine_id");
          if (!maybe_routine_id) {
            throw config_error(
              "Error: Missing the \"routine_id\" value in access control of the"
              " service \"sid\":",
              format_sid(sid_value),
              "!");
          }

          services::routine::RoutineId routine_id;
          try {
            routine_id = cast::safe_cast<services::routine::RoutineId>(maybe_routine_id.value());
          } catch (const cast::cast_error &) {
            throw config_error("Error: Invalid \"routine_id\" value: ",
                               maybe_routine_id.value(),
                               " The \"routine_id\" value must be two bytes long integer!");
          }

          auto routine_access = parse_access_policy(routine_view, session_id_value);
          access.routine_accesses[routine_id].push_back(std::move(routine_access));
        }
      }
    }
  } else {
    if constexpr (!bringup::DISABLING_ACCESS_CONTROLLER_ALLOWED) {
      throw config_error("Error: missing the \"access_control\" definition!");
    }
  }

  if (access_map.empty()) {
    if constexpr (bringup::DISABLING_ACCESS_CONTROLLER_ALLOWED) {
      if constexpr (bringup::DISABLING_ACCESS_CONTROLLER_LOGS_WARNING) {
        APEX_WARN_R(
          "Warning! UDS server AccessController is disabled, since no configuration is provided. "
          "This might be a security concern in a production system!");
      }
    } else {
      throw config_error("Error: Access Control can not be empty!");
    }
  }

  return access_map;
}

}  // namespace shared
}  // namespace services
}  // namespace uds_server
}  // namespace apex
