// Copyright 2022 Apex.AI, Inc
// All rights reserved.

#include "uds_server/com/response_publisher.hpp"

#include <memory>  // To use std::shared_ptr
#include <utility>  // To use std::move

namespace apex
{
namespace uds_server
{
namespace com
{

ResponsePublisher::ResponsePublisher(const diagnostic_common::NodeContextRef & node_context,
                                     const string_strict256_t & publish_topic,
                                     const uint16_t logical_address)
: m_transport_pub{node_context.node.create_publisher<uds_msgs::msg::UdsTransportMessage>(
    publish_topic, rclcpp::DefaultQoS())},
  m_logical_address{logical_address}
{
}

void ResponsePublisher::publish_positive_response(
  const services::shared::PositiveResponse & response)
{
  const std::unique_lock<std::mutex> lck{m_response_pub_mtx};

  auto loaned_msg = m_transport_pub->borrow_loaned_message();
  auto & data = loaned_msg.get();

  data.tester_address = response.tester_address;
  data.ecu_address.address = m_logical_address;
  response.fill_payload_cb(data.payload);

  m_transport_pub->publish(std::move(loaned_msg));
}

void ResponsePublisher::publish_negative_response(
  const services::shared::NegativeResponse & response)
{
  const std::unique_lock<std::mutex> lck{m_response_pub_mtx};

  auto loaned_msg = m_transport_pub->borrow_loaned_message();
  auto & data = loaned_msg.get();

  data.tester_address = response.tester_address;
  data.ecu_address.address = m_logical_address;

  data.payload.push_back(static_cast<uint8_t>(services::shared::UdsSid::NEGATIVE_RESPONSE));
  data.payload.push_back(static_cast<uint8_t>(response.service_id));
  data.payload.push_back(static_cast<uint8_t>(response.error_code));

  m_transport_pub->publish(std::move(loaned_msg));
}

}  // namespace com
}  // namespace uds_server
}  // namespace apex
