// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include "uds_server/com/request_subscriber.hpp"

#include <memory>  // To use std::shared_ptr
#include <utility>  // To use std::move

#include "logging/logging_macros.hpp"  // To use APEX_WARN

namespace apex
{
namespace uds_server
{
namespace com
{

RequestSubscriber::RequestSubscriber(const diagnostic_common::NodeContextRef & node_context,
                                     const string_strict256_t & subscribe_topic,
                                     std::shared_ptr<ServiceHubBase> service_hub,
                                     const uint16_t logical_address)
: executor::executable_item{node_context.node},
  m_transport_sub{get_rclcpp_node().create_polling_subscription<uds_msgs::msg::UdsTransportMessage>(
    subscribe_topic, rclcpp::DefaultQoS())},
  m_service_hub{std::move(service_hub)},
  m_logger{&node_context.node, "RequestSubscriber"},
  m_logical_address{logical_address}
{
}

bool RequestSubscriber::execute_impl()
{
  const auto loaned_msgs = m_transport_sub->take();
  for (const auto & msg : loaned_msgs) {
    const bool msg_valid = msg.info().valid() && !msg.data().payload.empty() &&
                           (msg.data().ecu_address.address == m_logical_address);
    if (msg_valid) {
      m_service_hub->dispatch_request(msg.data(), msg.info().reception_timestamp());
    } else {
      APEX_WARN(m_logger, "Skipping request: invalid message received!");
    }
  }
  return true;
}

}  // namespace com
}  // namespace uds_server
}  // namespace apex
