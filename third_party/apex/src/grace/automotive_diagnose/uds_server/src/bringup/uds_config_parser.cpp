/// \copyright Copyright 2025 Apex.AI, Inc
/// All rights reserved.

#include "uds_server/bringup/uds_config_parser.hpp"

#include <algorithm>  // To use std::find_if, std::any_of
#include <array>  // To use std::array
#include <memory>  // To use std::shared_ptr
#include <string>  // To use std::string
#include <unordered_set>  // To use std::unordered_set
#include <utility>  // To use std::move, std::make_pair
#include <vector>  // To use std::vector

#include "containers/static_vector.hpp"
#include "cpputils/safe_cast.hpp"  // To use safe_cast<>
#include "grace/automotive_diagnose/uds_server/include/uds_server/services/did/read_by_did_periodically_service.hpp"
#include "settings/construct/getters.hpp"  // To use get<>
#include "uds_server/bringup/config.hpp"
#include "uds_server/services/did/async_did_reader.hpp"
#include "uds_server/services/did/async_did_writer.hpp"
#include "uds_server/services/did/static_did_reader.hpp"
#include "uds_server/services/security/security_access.hpp"  // To use SecurityLevel
#include "uds_server/services/shared/format.hpp"
#include "uds_server/session/predefined_session.hpp"  // To use PredefinedSession
#include "uds_server/session/session_base.hpp"  // To use SessionId

namespace apex
{
namespace uds_server
{
namespace bringup
{
namespace
{
using settings::inspect::array_view;
using settings::inspect::boolean;
using settings::inspect::get;
using settings::inspect::integer;
using settings::inspect::maybe;
using settings::inspect::string_view;

using config_error = diagnostic_common::config_error;

bool is_sid_supported(const services::shared::UdsSid sid)
{
  using services::shared::UdsSid;

  switch (sid) {
    case UdsSid::DIAGNOSTIC_SESSION_CONTROL_REQ:
      [[fallthrough]];
    case UdsSid::SECURITY_ACCESS_REQ:
      [[fallthrough]];
    case UdsSid::TESTER_PRESENT_REQ:
      [[fallthrough]];
    case UdsSid::CONTROL_DTC_SETTINGS_REQ:
      [[fallthrough]];
    case UdsSid::READ_DATA_BY_IDENTIFIER_REQ:
      [[fallthrough]];
    case UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ:
      [[fallthrough]];
    case UdsSid::WRITE_DATA_BY_IDENTIFIER_REQ:
      [[fallthrough]];
    case UdsSid::INPUT_OUTPUT_CONTROL_BY_IDENTIFIER_REQ:
      [[fallthrough]];
    case UdsSid::CLEAR_DIAGNOSTIC_INFORMATION_REQ:
      [[fallthrough]];
    case UdsSid::READ_DTC_INFORMATION_REQ:
      [[fallthrough]];
    case UdsSid::ROUTINE_CONTROL_REQ:
      return true;
    default:
      return false;
  }
}

}  // namespace

std::string parse_req_topic(dictionary_view cfg)
{
  auto maybe_req_topic = get<maybe<string_view>>(cfg, "diagnostic/uds/req_topic");

  if (!maybe_req_topic || maybe_req_topic->empty()) {
    throw config_error("Error: Invalid value of a diagnostic/uds/req_topic!");
  }

  return std::string{*maybe_req_topic};
}

std::string parse_res_topic(dictionary_view cfg)
{
  auto maybe_res_topic = get<maybe<string_view>>(cfg, "diagnostic/uds/res_topic");

  if (!maybe_res_topic || maybe_res_topic->empty()) {
    throw config_error("Error: Invalid value of a diagnostic/uds/res_topic!");
  }

  return std::string{*maybe_res_topic};
}

std::string parse_env_topic(dictionary_view cfg)
{
  auto maybe_env_topic = get<maybe<string_view>>(cfg, "diagnostic/uds/env_topic");

  if (!maybe_env_topic || maybe_env_topic->empty()) {
    throw config_error("Error: Invalid value of a diagnostic/uds/env_topic!");
  }

  return std::string{*maybe_env_topic};
}

uint16_t parse_logical_address(dictionary_view cfg)
{
  auto maybe_logical_address = get<maybe<integer>>(cfg, "diagnostic/uds/ecu_logical_address");

  uint16_t logical_address_value{};
  if (!maybe_logical_address) {
    throw config_error("Error: Invalid value of a \"diagnostic/uds/ecu_logical_address\"!");
  }
  try {
    logical_address_value = cast::safe_cast<uint16_t>(maybe_logical_address.value());
  } catch (const cast::cast_error &) {
    throw config_error("Error: Invalid \"ecu_logical_address\" value:",
                       maybe_logical_address.value(),
                       " The \"ecu_logical_address\" value must be two bytes long!");
  }
  return logical_address_value;
}

std::string parse_session_event_topic(dictionary_view cfg)
{
  auto maybe_session_event_topic =
    get<maybe<string_view>>(cfg, "diagnostic/uds/session_event_topic");

  if (!maybe_session_event_topic || maybe_session_event_topic->empty()) {
    throw config_error("Error: Invalid value of a diagnostic/uds/session_event_topic!");
  }

  return std::string{*maybe_session_event_topic};
}

std::pair<DidEntries, ExecutableDids> parse_dids(dictionary_view cfg,
                                                 const diagnostic_common::NodeContextRef & context)
{
  DidEntries did_entries;
  ExecutableDids exec_dids;

  auto dids = get<maybe<array_view>>(cfg, "diagnostic/uds/dids");
  if (!dids) {
    throw config_error("Error: Missing the \"dids\" data!");
  }

  for (const auto & did : dids.value()) {
    DidEntry entry;

    auto value = get<maybe<integer>>(did, "did");
    if (!value) {
      throw config_error("Error: The DID is missing a \"did\" value!");
    }

    uint16_t did_value;
    try {
      did_value = cast::safe_cast<uint16_t>(value.value());
    } catch (const cast::cast_error &) {
      throw config_error("Error: Invalid \"did\" value:",
                         value.value(),
                         " The \"did\" value must be two bytes long!");
    }

    const bool did_already_added =
      std::find_if(did_entries.begin(), did_entries.end(), [did_value](const auto & entry) {
        return entry.m_did == did_value;
      }) != did_entries.end();
    if (did_already_added) {
      throw config_error(
        "Error: Cannot add the \"did\":", services::shared::format_did(did_value), " twice!");
    }

    entry.m_did = did_value;

    auto type = get<maybe<string_view>>(did, "type");
    if (!type) {
      throw config_error("Error: The DID is missing a \"type\"!");
    }

    if (type.value() == "static") {
      auto data = get<maybe<array_view>>(did, "data");
      if (!data) {
        throw config_error("Error: The static \"did\":", did_value, " must have a \"data\" array");
      } else if (!data->empty() && (data->size() > MAX_SIZE_OF_DID_PAYLOAD)) {
        throw config_error("The static \"did\":",
                           services::shared::format_did(did_value),
                           " must have \"data\" record size in range:"
                           " <1,",
                           MAX_SIZE_OF_DID_PAYLOAD,
                           ">!");
      } else {
        services::did::DataRecord data_record{};
        for (auto & d : data.value()) {
          try {
            const auto byte = cast::safe_cast<uint8_t>(get<integer>(d));
            data_record.push_back(byte);
          } catch (const cast::cast_error &) {
            throw config_error("Error: The \"did\":",
                               services::shared::format_did(did_value),
                               " \"data\" record entry must be a byte long!");
          }
        }
        entry.m_reader =
          std::make_shared<services::did::StaticDidReader>(did_value, std::move(data_record));
      }
    } else if (type.value() == "async") {
      auto read_service = get<maybe<string_view>>(did, "read_service");
      auto write_service = get<maybe<string_view>>(did, "write_service");
      if (!read_service && !write_service) {
        throw config_error("Error: Async \"did\":",
                           services::shared::format_did(did_value),
                           " must have at least 1 of the following \"read_service\" or "
                           "\"write_service\"!");
      }

      if (read_service) {
        if (read_service->empty()) {
          throw config_error("Error Async \"did\":",
                             services::shared::format_did(did_value),
                             " cannot have empty \"read_service\"!");
        }
        const auto reader = std::make_shared<services::did::AsyncDidReader>(
          context, std::string{read_service.value()}, did_value);
        entry.m_reader = reader;
        exec_dids.push_back(reader);
      }

      if (write_service) {
        if (write_service->empty()) {
          throw config_error("Error: Async \"did\":",
                             services::shared::format_did(did_value),
                             " cannot have empty \"write_service\"!");
        }
        const auto writer = std::make_shared<services::did::AsyncDidWriter>(
          context, std::string{write_service.value()}, did_value);
        entry.m_writer = writer;
        exec_dids.push_back(writer);
      }
    } else {
      throw config_error(
        "Error Unknown DID \"type\":", *type, " The did \"type\" must be \"async\" or \"static\"!");
    }

    did_entries.push_back(std::move(entry));
  }

  return std::make_pair(std::move(did_entries), std::move(exec_dids));
}

std::vector<services::shared::UdsSid> parse_sids(dictionary_view cfg)
{
  std::vector<services::shared::UdsSid> sids;

  const auto services_view = get<maybe<array_view>>(cfg, "diagnostic/uds/services");
  if (!services_view || services_view->empty()) {
    throw config_error("Error: Missing the \"sid\" definition in the \"services\"!");
  }

  sids.reserve(services_view->size());
  for (const auto & sid_view : services_view.value()) {
    const auto maybe_sid_value = get<maybe<integer>>(sid_view, "sid");
    if (!maybe_sid_value) {
      throw config_error("Error: Missing the \"sid\" definition in the \"services\"!");
    }

    services::shared::UdsSid sid;
    try {
      const uint8_t sid_value = cast::safe_cast<uint8_t>(maybe_sid_value.value());
      // TODO(mateusz.wierzchowski): #23409 Implement enum safe casts of some sorts
      sid = static_cast<services::shared::UdsSid>(sid_value);
    } catch (const cast::cast_error &) {
      throw config_error(
        "Error: Invalid \"sid\" value: ", maybe_sid_value.value(), " Value must be a single byte!");
    }

    const bool is_sid_already_added = std::find_if(sids.begin(), sids.end(), [sid](const auto s) {
                                        return sid == s;
                                      }) != sids.end();

    if (is_sid_already_added) {
      throw config_error(
        "Error: \"sid\":", services::shared::format_sid(sid), " is already defined!");
    }

    if (!is_sid_supported(sid)) {
      throw config_error(
        "Error: \"sid\":", services::shared::format_sid(sid), " is not supported!");
    }
    sids.push_back(sid);
  }

  return sids;
}

Sessions parse_sessions(dictionary_view cfg)
{
  auto sessions = get<maybe<array_view>>(cfg, "diagnostic/uds/sessions");
  if (!sessions || sessions->empty()) {
    throw config_error("Missing the \"sessions\" data");
  }

  DefaultSessionPtr default_session;
  NonDefaultSessions non_default_sessions;
  for (const auto & session : sessions.value()) {
    auto session_id = get<maybe<integer>>(session, "id");
    auto p2 = get<maybe<integer>>(session, "p2");
    auto p2_star = get<maybe<integer>>(session, "p2_star");
    const auto s3 = get<maybe<integer>>(session, "s3");
    const auto max_pending_responses = get<maybe<integer>>(session, "max_pending_responses");

    if (!session_id || !p2 || !p2_star) {
      throw config_error(
        "Error: The session must have defined"
        " \"id\", \"p2\" and \"p2_star\" values!");
    }

    uint8_t id_value;
    uint16_t p2_value;
    uint16_t p2_star_value;
    uint16_t s3_value;
    uint8_t max_pending_responses_value;
    try {
      id_value = cast::safe_cast<uint8_t>(session_id.value());
      p2_value = cast::safe_cast<uint16_t>(p2.value());
      p2_star_value = cast::safe_cast<uint16_t>(p2_star.value());
      s3_value = cast::safe_cast<uint16_t>(s3.value_or(DEFAULT_S3_TIME));
      max_pending_responses_value = cast::safe_cast<uint8_t>(
        max_pending_responses.value_or(bringup::DEFAULT_MAX_PENDING_RESPONSES_SENT));
    } catch (const cast::cast_error &) {
      throw config_error(
        "Error: The session \"id\" must be a 1 byte long,"
        " and the timings parameters \"p2\", \"p2_enh\" and \"s3\""
        " must be at most 2 bytes long and \"max_pending_responses\""
        " must be 1 byte long!");
    }
    session::TimingParameters tparams{std::chrono::milliseconds(p2_value),
                                      std::chrono::milliseconds(p2_star_value),
                                      std::chrono::milliseconds(s3_value),
                                      max_pending_responses_value};

    if (id_value == static_cast<session::SessionId>(session::PredefinedSession::DEFAULT)) {
      if (default_session) {
        throw config_error("Error: Only a single default session \"id\":0x01 can be defined!");
      } else {
        default_session = std::make_shared<session::DefaultSession>(tparams);
      }
    } else {
      const bool session_already_added = std::find_if(non_default_sessions.begin(),
                                                      non_default_sessions.end(),
                                                      [id_value](const SessionPtr session_ptr) {
                                                        return session_ptr->get_id() == id_value;
                                                      }) != non_default_sessions.end();
      if (session_already_added) {
        throw config_error("Error: Cannot add the Session with \"id\"", id_value, " twice!");
      } else {
        const auto non_def_session = std::make_shared<session::Session>(id_value, tparams);
        non_default_sessions.push_back(non_def_session);
      }
    }
  }

  if (!default_session) {
    throw config_error("Error: Missing the default session definition \"id\":0x01!");
  }

  return std::make_pair(default_session, std::move(non_default_sessions));
}

services::security::SecurityAccessSrvConfig parse_security_access_service(dictionary_view cfg)
{
  services::security::SecurityAccessSrvConfig config;

  const auto maybe_uds_services_view = get<maybe<array_view>>(cfg, "diagnostic/uds/services");
  if (maybe_uds_services_view) {
    optional<dictionary_view> maybe_security_access_service_cfg;
    for (const auto & service_view : maybe_uds_services_view.value()) {
      const auto maybe_sid = get<maybe<integer>>(service_view, "sid");
      uint8_t sid_value;
      if (maybe_sid) {
        try {
          sid_value = cast::safe_cast<uint8_t>(maybe_sid.value());
        } catch (const cast::cast_error &) {
          throw config_error("Error: Invalid \"sid\" value in services - expected an uint8!");
        }

        // TODO(mateusz.wierzchowski): #23409 Implement enum safe casts of some sorts
        if (static_cast<services::shared::UdsSid>(sid_value) ==
            services::shared::UdsSid::SECURITY_ACCESS_REQ) {
          maybe_security_access_service_cfg = get<dictionary_view>(service_view);
          break;
        }
      }
    }

    if (maybe_security_access_service_cfg) {
      const auto & security_access_service_cfg = maybe_security_access_service_cfg.value();

      const auto maybe_get_seed_service =
        get<maybe<string_view>>(security_access_service_cfg, "get_seed_service");
      if (!maybe_get_seed_service) {
        throw config_error(
          "Error: Missing the \"get_seed_service\" value in security access service!");
      }
      config.m_get_seed_service_name = *maybe_get_seed_service;

      const auto maybe_compare_key_service_name =
        get<maybe<string_view>>(security_access_service_cfg, "compare_key_service");
      if (!maybe_compare_key_service_name) {
        throw config_error(
          "Error: Missing the \"compare_key_service\" value in security access service!");
      }
      config.m_compare_key_service_name = *maybe_compare_key_service_name;

      const auto maybe_level_availability_service_name =
        get<maybe<string_view>>(security_access_service_cfg, "level_availability_service");
      if (!maybe_level_availability_service_name) {
        throw config_error(
          "Error: Missing the \"level_availability_service\" value in security access service!");
      }
      config.m_level_availability_service_name = *maybe_level_availability_service_name;

      const auto maybe_level_unlocking_result_topic =
        get<maybe<string_view>>(security_access_service_cfg, "level_unlocking_result_topic");
      if (!maybe_level_unlocking_result_topic) {
        throw config_error(
          "Error: Missing the \"level_unlocking_result_topic\" value in security access service!");
      }
      config.m_level_unlocking_result_topic = *maybe_level_unlocking_result_topic;

      const auto maybe_reset_level_timer =
        get<maybe<integer>>(security_access_service_cfg, "reset_level_timer_ms");
      if (maybe_reset_level_timer) {
        try {
          config.m_reset_level_timeout_val_ms =
            cast::safe_cast<uint32_t>(maybe_reset_level_timer.value());
        } catch (const cast::cast_error &) {
          throw config_error(
            "Error: Invalid \"reset_level_timer_ms\" value in security access service - expected "
            "an uint32!");
        }
      }
      config.m_reset_level_timer_enabled = !!maybe_reset_level_timer;

      const auto maybe_static_seed =
        get<maybe<boolean>>(security_access_service_cfg, "static_seed");
      if (!maybe_static_seed) {
        throw config_error("Error: Missing the \"static_seed\" value in security access service!");
      }
      try {
        config.m_static_seed = cast::safe_cast<bool>(maybe_static_seed.value());
      } catch (const cast::cast_error &) {
        throw config_error(
          "Error: Invalid \"static_seed\" value in security access service expected a boolean!");
      }

      const auto maybe_enabled_security_levels =
        get<maybe<array_view>>(security_access_service_cfg, "enabled_security_levels");
      if (maybe_enabled_security_levels) {
        for (const auto & level_view : maybe_enabled_security_levels.value()) {
          const auto maybe_level = get<maybe<integer>>(level_view);
          if (maybe_level) {
            services::security::SecurityLevel level;
            try {
              level = cast::safe_cast<uint8_t>(maybe_level.value());
            } catch (const cast::cast_error &) {
              throw config_error(
                "Error: Invalid security level value in \"enabled_security_levels\" in security "
                "access service - expected an uint8!");
            }

            if (!services::security::is_security_level_valid(level)) {
              throw config_error("Error: Invalid security level value \"",
                                 level,
                                 "\" in \"enabled_security_levels\" in security access service - "
                                 "expected an odd value in range of <",
                                 services::security::SECURITY_LEVEL_MIN_VALUE,
                                 ", ",
                                 services::security::SECURITY_LEVEL_MAX_VALUE,
                                 ">!");
            }

            const auto result = config.m_enabled_levels.insert(level);
            if (!result.second) {
              throw config_error(
                "Error: The same security access level cannot be enabled twice, check "
                "\"enabled_security_levels\" configuration in security access service - repeating "
                "level: \"",
                level,
                "\"!");
            }
          }
        }
      }
    }
  }

  return config;
}
services::did::ReadByDidDataPeriodicallyConfig parse_read_did_periodically_config(
  dictionary_view cfg)
{
  services::did::ReadByDidDataPeriodicallyConfig config;

  maybe<dictionary_view> service_parameters;
  const auto uds_services_view = get<array_view>(cfg, "diagnostic/uds/services");
  for (const auto & service_view : uds_services_view) {
    const auto maybe_sid = get<maybe<integer>>(service_view, "sid");
    uint8_t sid_value;
    if (maybe_sid) {
      try {
        sid_value = cast::safe_cast<uint8_t>(maybe_sid.value());
      } catch (const cast::cast_error &) {
        throw config_error("Error: Invalid \"sid\" value in services - expected an uint8!");
      }

      if (static_cast<services::shared::UdsSid>(sid_value) ==
          services::shared::UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ) {
        service_parameters = get<dictionary_view>(service_view);
        break;
      }
    }
  }

  if (service_parameters) {
    return config;
  }
  const auto & read_did_periodically_config = service_parameters.value();

  const auto maybe_slow_period_ms =
    get<maybe<integer>>(read_did_periodically_config, "slow_period_ms");
  if (maybe_slow_period_ms) {
    uint32_t period;
    try {
      period = cast::safe_cast<uint32_t>(maybe_slow_period_ms.value());
    } catch (const cast::cast_error &) {
      throw config_error(
        "Error: Invalid \"slow_period_ms\" value in read did periodically service - expected "
        "an uint32!");
    }
    config.slow_read_period_ms = std::chrono::milliseconds(period);
  }

  const auto maybe_mid_period_ms =
    get<maybe<integer>>(read_did_periodically_config, "mid_period_ms");
  if (maybe_mid_period_ms) {
    uint32_t period;
    try {
      period = cast::safe_cast<uint32_t>(maybe_mid_period_ms.value());
    } catch (const cast::cast_error &) {
      throw config_error(
        "Error: Invalid \"mid_period_ms\" value in read did periodically service - expected "
        "an uint32!");
    }
    config.mid_read_period_ms = std::chrono::milliseconds(period);
  }

  const auto maybe_fast_period_ms =
    get<maybe<integer>>(read_did_periodically_config, "fast_period_ms");
  if (maybe_fast_period_ms) {
    uint32_t period;
    try {
      period = cast::safe_cast<uint32_t>(maybe_fast_period_ms.value());
    } catch (const cast::cast_error &) {
      throw config_error(
        "Error: Invalid \"fast_period_ms\" value in read did periodically service - expected "
        "an uint32!");
    }
    config.fast_read_period_ms = std::chrono::milliseconds(period);
  }
  return config;
}

}  // namespace bringup
}  // namespace uds_server
}  // namespace apex
