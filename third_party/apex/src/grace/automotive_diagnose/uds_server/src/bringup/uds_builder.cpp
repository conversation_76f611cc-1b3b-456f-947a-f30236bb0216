/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.

#include "uds_server/bringup/uds_builder.hpp"

#include <algorithm>  // To use std::copy
#include <limits>  // To use numeric_limits<>
#include <memory>  // To use std::shared_ptr<>
#include <unordered_map>
#include <utility>  // To use std::move, std::pair
#include <vector>  // To use std::vector<>

#include "cpputils/optional.hpp"  // To use apex::optional
#include "grace/automotive_diagnose/uds_server/include/uds_server/services/did/read_by_did_periodically_service.hpp"
#include "system_utils/system_clock.hpp"
#include "uds_server/bringup/uds_config_parser.hpp"
#include "uds_server/services/ioctrl/input_output_control_service.hpp"
#include "uds_server/services/ioctrl/input_output_control_settings.hpp"
#include "uds_server/services/ioctrl/polling_client_requester.hpp"
// built entities
#include "diagnostic_common/timer/timer.hpp"
#include "uds_server/addons/security/common_level_controller.hpp"
#include "uds_server/addons/security/individual_level_controller.hpp"
#include "uds_server/addons/security/permissive_level_controller.hpp"
#include "uds_server/com/default_server_timeout_p4_handler.hpp"
#include "uds_server/com/request_response_tracker.hpp"
#include "uds_server/com/request_subscriber.hpp"
#include "uds_server/com/response_publisher.hpp"
#include "uds_server/com/service_hub.hpp"
#include "uds_server/env/env_condition_monitor.hpp"
#include "uds_server/services/did/did_pool.hpp"
#include "uds_server/services/did/read_by_did_service.hpp"
#include "uds_server/services/did/write_by_did_service.hpp"
#include "uds_server/services/dtc/async_clear_dtc_client.hpp"
#include "uds_server/services/dtc/async_control_dtc_settings_client.hpp"
#include "uds_server/services/dtc/async_read_dtc_client.hpp"
#include "uds_server/services/dtc/clear_dtc_info_service.hpp"
#include "uds_server/services/dtc/control_dtc_settings_service.hpp"
#include "uds_server/services/dtc/dtc_parser.hpp"
#include "uds_server/services/dtc/read_dtc_info_service.hpp"
#include "uds_server/services/routine/async_routine_client.hpp"
#include "uds_server/services/routine/routine_control_service.hpp"
#include "uds_server/services/routine/routine_parser.hpp"
#include "uds_server/services/routine/routine_registry.hpp"
#include "uds_server/services/security/async_key_comparator.hpp"
#include "uds_server/services/security/async_level_controller.hpp"
#include "uds_server/services/security/async_seed_fetcher.hpp"
#include "uds_server/services/security/level_unlocking_result_publisher.hpp"
#include "uds_server/services/security/security_access_service.hpp"
#include "uds_server/services/security/security_level_controller_parser.hpp"
#include "uds_server/services/session/diagnostic_session_control_service.hpp"
#include "uds_server/services/session/tester_present_service.hpp"
#include "uds_server/services/shared/access_parser.hpp"
#include "uds_server/session/session_event_publisher.hpp"
#include "uds_server/session/session_manager.hpp"

namespace apex
{
namespace uds_server
{
namespace bringup
{

using DidPool = services::did::DidPool;
using DidPoolPtr = std::shared_ptr<DidPool>;
using DidReaderObserver = services::did::DidReaderObserver;
using DidWriterObserver = services::did::DidWriterObserver;
using AsyncSeedFetcher = services::security::AsyncSeedFetcher;
using AsyncKeyComparator = services::security::AsyncKeyComparator;
using AsyncLevelController = services::security::AsyncLevelController;
using LevelUnlockingResultPublisher = services::security::LevelUnlockingResultPublisher;

using LevelControllerConfigBase = addons::security::LevelControllerConfigBase;
using CommonLevelControllerConfig = addons::security::CommonLevelControllerConfig;
using IndividualLevelControllerConfig = addons::security::IndividualLevelControllerConfig;
using LevelControllerConfigVisitor = addons::security::LevelControllerConfigVisitor;
using LevelControllerBase = addons::security::LevelControllerBase;
using PermissiveLevelController = addons::security::PermissiveLevelController;
using CommonLevelController = addons::security::CommonLevelController;
using IndividualLevelController = addons::security::IndividualLevelController<apex::system_clock>;
using Delay = addons::security::Delay<apex::system_clock>;
using LevelDelays = addons::security::LevelDelays<apex::system_clock>;

namespace
{

// Max number of active timers provided by diagnostic_common::Timer that can be used by this
// uds_server instance. Needed parallel timers are for use of: SessionManager (1),
// SecurityAccessService (1) (the sum of which is the MAX_PARALLEL_TIMERS_COMPONENT) and
// RequestResponseTracker (bringup::MAX_REQUESTS_PENDING_SIZE).
constexpr size_t MAX_PARALLEL_TIMERS_COMPONENT{2U};
static_assert((std::numeric_limits<decltype(bringup::MAX_REQUESTS_PENDING_SIZE)>::max() -
               MAX_PARALLEL_TIMERS_COMPONENT) >= bringup::MAX_REQUESTS_PENDING_SIZE,
              "Wrap-around of in the result of sum of bringup::MAX_REQUESTS_PENDING_SIZE and "
              "MAX_PARALLEL_TIMERS_COMPONENT.");
constexpr size_t MAX_PARALLEL_TIMERS{MAX_PARALLEL_TIMERS_COMPONENT +
                                     bringup::MAX_REQUESTS_PENDING_SIZE};

// TODO(mateusz.wierzchowski): #19255 ExecType should be exec_item specific, not service
// specific. This is currently only used by security access and needed for unit tests
// and should be redesigned. ExecType should not be just ignored though, since client could benefit
// from knowing it.
std::pair<std::shared_ptr<services::shared::DiagnosticService>, ExecType> create_service(
  const services::shared::UdsSid sid,
  std::shared_ptr<session::SessionManager> session_manager,
  services::shared::ResponsePublisherPtr res_pub,
  DidPoolPtr did_pool,
  const diagnostic_common::NodeContextRef & context,
  std::shared_ptr<diagnostic_common::timer::TimerBase> timer,
  const settings::inspect::dictionary_view & cfg,
  std::shared_ptr<services::routine::RoutineRegistryBase> routine_registry,
  std::shared_ptr<services::shared::AccessController> access_ctrl)
{
  std::shared_ptr<services::shared::DiagnosticService> service;
  auto exec_type = ExecType::UNKNOWN;

  using Sid = services::shared::UdsSid;
  switch (sid) {
    case Sid::DIAGNOSTIC_SESSION_CONTROL_REQ: {
      service = std::make_shared<services::session::DiagnosticSessionControlService>(
        res_pub, std::move(access_ctrl), std::move(session_manager));
    } break;
    case Sid::READ_DATA_BY_IDENTIFIER_REQ: {
      auto did_service =
        std::make_shared<services::did::ReadByDidService>(res_pub, did_pool, context);
      did_pool->register_did_readers_observer(did_service);
      service = std::move(did_service);
    } break;
    case Sid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ: {
      auto read_did_periodic_config = parse_read_did_periodically_config(cfg);
      auto did_periodic_service = std::make_shared<services::did::ReadByDidPeriodicallyService>(
        res_pub, did_pool, context, std::move(timer), read_did_periodic_config);
      did_pool->register_did_periodic_readers_observer(did_periodic_service);
      service = std::move(did_periodic_service);
    } break;
    case Sid::WRITE_DATA_BY_IDENTIFIER_REQ: {
      auto did_service =
        std::make_shared<services::did::WriteByDidService>(res_pub, did_pool, context);
      did_pool->register_did_writers_observer(did_service);
      service = std::move(did_service);
    } break;

    case Sid::INPUT_OUTPUT_CONTROL_BY_IDENTIFIER_REQ: {
      auto ioctrl_config = services::ioctrl::parse_config(cfg);

      std::unordered_map<uint16_t, std::shared_ptr<services::ioctrl::InputOutputControlRequester>>
        requesters{};
      requesters.reserve(ioctrl_config.dids.size());
      for (const auto & entry : ioctrl_config.dids) {
        auto polling_client = std::make_shared<services::ioctrl::PollingClientRequester>(
          context, entry.control_service, entry.request_timeout);

        requesters.emplace(entry.did, polling_client);
      }

      service = std::make_shared<services::ioctrl::InputOutputControl>(
        res_pub, std::move(access_ctrl), std::move(requesters));
      exec_type = ExecType::IOCTRL;
    } break;

    case Sid::SECURITY_ACCESS_REQ: {
      const auto security_access_service_cfg = parse_security_access_service(cfg);
      auto seed_fetcher = std::make_shared<AsyncSeedFetcher>(
        context, security_access_service_cfg.m_get_seed_service_name);
      auto key_comparator = std::make_shared<AsyncKeyComparator>(
        context, security_access_service_cfg.m_compare_key_service_name);
      auto level_controller = std::make_shared<AsyncLevelController>(
        context, security_access_service_cfg.m_level_availability_service_name);
      auto level_unlocking_result_pub = std::make_shared<LevelUnlockingResultPublisher>(
        context, security_access_service_cfg.m_level_unlocking_result_topic);
      auto sec_service = std::make_shared<services::security::SecurityAccessService>(
        res_pub,
        std::move(access_ctrl),
        std::move(session_manager),
        std::move(seed_fetcher),
        std::move(key_comparator),
        std::move(level_controller),
        std::move(level_unlocking_result_pub),
        context,
        std::move(timer),
        security_access_service_cfg);
      service = std::move(sec_service);
      exec_type = ExecType::SECURITY_ACCESS;
    } break;
    case Sid::TESTER_PRESENT_REQ: {
      service = std::make_shared<services::session::TesterPresentService>(
        res_pub, std::move(access_ctrl), std::move(session_manager));
    } break;
    case Sid::ROUTINE_CONTROL_REQ: {
      service = std::make_shared<services::routine::RoutineControlService>(
        res_pub, access_ctrl, access_ctrl, std::move(routine_registry));
    } break;
    case Sid::READ_DTC_INFORMATION_REQ: {
      auto service_name = services::dtc::parse_read_dtc_service_name(cfg);
      auto client = std::make_shared<services::dtc::AsyncReadDtcClient>(context, service_name);
      service = std::make_shared<services::dtc::ReadDtcInfoService>(
        res_pub, std::move(access_ctrl), std::move(client));
      exec_type = ExecType::DTC;
    } break;
    case Sid::CLEAR_DIAGNOSTIC_INFORMATION_REQ: {
      auto service_name = services::dtc::parse_clear_dtc_service_name(cfg);
      auto client = std::make_shared<services::dtc::AsyncClearDtcClient>(context, service_name);
      service = std::make_shared<services::dtc::ClearDtcInfoService>(res_pub, std::move(client));
      exec_type = ExecType::DTC;
    } break;
    case Sid::CONTROL_DTC_SETTINGS_REQ: {
      auto service_name = services::dtc::parse_control_dtc_service_name(cfg);
      auto client =
        std::make_shared<services::dtc::AsyncControlDtcSettingsClient>(context, service_name);
      service = std::make_shared<services::dtc::ControlDtcSettingsService>(
        res_pub, std::move(access_ctrl), std::move(client));
      exec_type = ExecType::DTC;
    } break;
    default: {
      // shall never happen, parser won't return invalid SID
    } break;
  }

  return {service, exec_type};
}

/// \class SecurityLevelControllerBuilder
/// \brief Security LevelController builder that is a visitor of the LevelController configuration.
/// TODO(mateusz.wierzchowski): #19253 Move this to a separate file.
class SecurityLevelControllerBuilder : public LevelControllerConfigVisitor
{
public:
  explicit SecurityLevelControllerBuilder(const diagnostic_common::NodeContextRef & context)
  : m_context{context}
  {
  }

  /// \brief Visit a base LevelControllerConfig to create a PermissiveLevelController.
  /// \param[in] config The configuration object.
  void visit_base(LevelControllerConfigBase & config) override
  {
    m_level_ctrl = std::make_shared<PermissiveLevelController>(m_context, config);
  }

  /// \brief Visit a common LevelControllerConfig to create a CommonLevelController.
  /// \param[in] config The configuration object.
  void visit_common(CommonLevelControllerConfig & config) override
  {
    m_level_ctrl = std::make_shared<CommonLevelController>(
      m_context,
      config,
      std::make_shared<Delay>(config.m_delay_cfg.m_unlock_failure_threshold,
                              std::chrono::milliseconds{config.m_delay_cfg.m_delay_ms}));
  }

  /// \brief Visit an individual LevelControllerConfig to create a IndividualLevelController.
  /// \param[in] config The configuration object.
  void visit_individual(IndividualLevelControllerConfig & config) override
  {
    m_level_ctrl = std::make_shared<IndividualLevelController>(
      m_context, config, std::make_shared<LevelDelays>());
  }

  /// \brief Create DiagnosticExecutable instance of LevelController.
  /// \param[in] cfg The UDS server configuration.
  /// \return DiagnosticExecutable of LevelController if a proper configuration was provided.
  /// \return apex::nullopt if a proper configuration was not provided.
  apex::optional<DiagnosticExecutable> create(const settings::inspect::dictionary_view & cfg)
  {
    const auto level_ctrl_cfg = services::security::parse_security_level_controller(cfg);
    if (level_ctrl_cfg == nullptr) {
      return apex::nullopt;
    }
    level_ctrl_cfg->accept(*this);

    return DiagnosticExecutable{std::move(m_level_ctrl), ExecType::SECURITY_LEVEL_CTRL};
  }

private:
  /// \brief The Node context reference object.
  const diagnostic_common::NodeContextRef m_context;
  /// \brief The LevelController that is constructed by the `create` method.
  std::shared_ptr<LevelControllerBase> m_level_ctrl{};
};

}  // namespace

std::vector<DiagnosticExecutable> build(const settings::inspect::dictionary_view cfg,
                                        const diagnostic_common::NodeContextPtr & node_context)
{
  return build(cfg, node_context->as_ref());
}

std::vector<DiagnosticExecutable> build(const settings::inspect::dictionary_view cfg,
                                        const diagnostic_common::NodeContextRef & node_context)
{
  // vector of all executable items
  std::vector<DiagnosticExecutable> execs;

  // timer shared across services
  const auto timer = std::make_shared<diagnostic_common::timer::Timer>(
    node_context, MAX_PARALLEL_TIMERS, node_context.services.timer_service());
  execs.push_back({timer, ExecType::TIMER});

  // build session manager
  auto sessions = parse_sessions(cfg);
  const auto session_event_topic = parse_session_event_topic(cfg);
  auto session_manager = std::make_shared<session::SessionManager>(
    sessions.first,
    session::NonDefaultSessions{
      sessions.second.size(), sessions.second.begin(), sessions.second.end()},
    std::make_shared<session::SessionEventPublisher>(node_context, session_event_topic),
    timer);

  // build env monitor
  const auto env_topic = parse_env_topic(cfg);
  const auto env_monitor = std::make_shared<env::EnvConditionMonitor>(env_topic, node_context);
  // add to vector
  execs.emplace_back(env_monitor, ExecType::MONITOR);

  // parse access controls and create access_controller
  auto access_map = services::shared::parse_access_control(cfg);
  auto access_ctrl_ptr =
    std::make_shared<services::shared::AccessController>(env_monitor, std::move(access_map));

  // create and register publisher
  const auto res_topic = parse_res_topic(cfg);
  const auto logical_address = parse_logical_address(cfg);
  const auto res_pub =
    std::make_shared<com::ResponsePublisher>(node_context, res_topic, logical_address);
  // create did pool and add did execs
  const auto did_result = parse_dids(cfg, node_context);
  const auto & did_entries = did_result.first;
  const auto did_pool = std::make_shared<DidPool>(
    access_ctrl_ptr,
    services::did::DidEntries{did_entries.size(), did_entries.begin(), did_entries.end()});

  for (const auto & did_exec : did_result.second) {
    execs.emplace_back(did_exec, ExecType::DID);
  }

  // create routine registry
  auto routines_map_execs_pair = services::routine::parse_routines_map(cfg, node_context);
  // add routines clients to the execs
  for (const auto & routine_exec_ptr : routines_map_execs_pair.second) {
    execs.emplace_back(routine_exec_ptr, ExecType::ROUTINE);
  }
  const auto routines_registry =
    std::make_shared<services::routine::RoutineRegistry>(std::move(routines_map_execs_pair.first));

  // create request response tracker observer
  auto p4_timeout_handler = std::make_shared<com::DefaultServerTimeoutP4Handler>();
  auto request_response_tracker =
    std::make_shared<com::RequestResponseTracker>(res_pub,
                                                  timer,
                                                  std::move(p4_timeout_handler),
                                                  session_manager->get_session_timing_params(),
                                                  bringup::MAX_REQUESTS_PENDING_SIZE);

  // create service_hub
  auto service_hub = std::make_shared<com::ServiceHub>(
    session_manager, res_pub, access_ctrl_ptr, request_response_tracker);

  // parse SIDs and create services
  for (const auto sid : parse_sids(cfg)) {
    // pass ServiceHub as a ResponsePublisher to all services
    // to intercept responses and remove them from tracking
    const auto result = create_service(sid,
                                       session_manager,
                                       service_hub.get(),
                                       did_pool,
                                       node_context,
                                       timer,
                                       cfg,
                                       routines_registry,
                                       access_ctrl_ptr);
    const auto & service = result.first;
    service_hub->add_service(service);
    // Add service exec items to execs list
    std::transform(service->executable_items().begin(),
                   service->executable_items().end(),
                   std::back_inserter(execs),
                   [exec_type = result.second](const auto & exec_item) {
                     return DiagnosticExecutable{exec_item, exec_type};
                   });
  }

  // create request subscriber
  const auto req_topic = parse_req_topic(cfg);
  auto request_sub = std::make_shared<com::RequestSubscriber>(
    node_context, req_topic, std::move(service_hub), logical_address);

  execs.emplace_back(std::move(request_sub), ExecType::SERVER);

  // create the security level controller
  auto level_ctrl = SecurityLevelControllerBuilder(node_context).create(cfg);
  if (level_ctrl) {
    execs.emplace_back(level_ctrl.value());
  }

  return execs;
}

}  // namespace bringup
}  // namespace uds_server
}  // namespace apex
