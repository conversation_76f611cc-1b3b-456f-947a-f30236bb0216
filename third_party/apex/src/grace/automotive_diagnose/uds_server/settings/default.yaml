diagnostic:
  uds:
    # definition of all Diagnostic IDs (DIDs) supported
    # by the UDS Server instance
    req_topic: /uds/req
    res_topic: /uds/rsp
    env_topic: /uds/env
    session_event_topic: /uds/session_event
    ecu_logical_address: 0x2233

    #! [dids]
    dids:
      # type (required): "static" or "async"
      # did (required): uint16 hex value of did
      # data (required if type="static"): the static payload of did
      # read_service (required if no write_service specified):
      # write_service (required if no read_service specified):

      -
        type: "static" # (1)
        did: 0xFF00 # (2)
        data: [0x03, 0x00, 0x00, 0x00] # (3)
      -
        type: "async"
        did: 0xF190
        read_service: "/uds/did/example1/read" # (4)
        write_service: "/uds/did/example1/write" # (5)
      -
        type: "async"
        did: 0xF201
        read_service: "/uds/did/example6/read" # (6)

      -
        type: "async"
        did: 0xF202
        read_service: "/uds/did/example7/read" # (6)
        
    #! [dids]
      -
        type: "async"
        did: 0xF18C
        read_service: "/uds/did/example2/read"
        write_service: "/uds/did/example2/write"
      -
        type: "async"
        did: 0xF18D
        read_service: "/uds/did/example3/read"
        write_service: "/uds/did/example3/write"
      -
        type: "async"
        did: 0xF18E
        read_service: "/uds/did/example4/read"
        write_service: "/uds/did/example4/write"
      -
        type: "async"
        did: 0x0043
        read_service: "/uds/did/example5/read"
        write_service: "/uds/did/example5/write"
    #! [routines]
    routines:
      # routine_id (required): The ID of a routine
      # routine_service (required): The service name that handles this routine.
      # supported_subfunctions (required): the server will response with NRC Resource Out of Range for not listed subfunctions, possible values: "start", "stop", "result"
      # timeout_ms (optional): sets the time to trigger stop routine by the server
      - # the client has Start, Stop, Result methods.
        routine_id: 0x0001 # (1)
        routine_service: "/uds/routine_0001_0002" # (2)
        supported_subfunctions: ["start", "stop", "result"] # (3)
      - # the client has Start, Stop, Result methods. The server will explicitly send stop after the timeout.
        routine_id: 0x0002
        routine_service: "/uds/routine_0001_0002"
        supported_subfunctions: ["start", "stop", "result"]
        timeout_ms: 1000 # (4)
      - # the client has Start and Result methods, the stop is done explicitly by the server after timeout time.
        routine_id: 0x0003
        routine_service: "/uds/routine_0003"
        supported_subfunctions: ["start", "result"]
        timeout_ms: 1000
      - # the client has Start and Result methods, the routine shall be stopped by the application or will run indefinitely.
        routine_id: 0x0004
        routine_service: "/uds/routine_0004"
        supported_subfunctions: ["start", "result"]
    #! [routines]
    # definitions of enabled diagnostic services
    # with their parameters
    #! [services]
    services:
      # sid (required): the ID of a service that shall be enabled
      - # Read Data By Identifier service
        sid: 0x22
      - # Read Data By Periodic Identifier service
        sid: 0x2A
        slow_period_ms: 100
        mid_period_ms: 50
        fast_period_ms: 25

      - # Write Data By Identifier service
        sid: 0x2E
      - # Security Access Service
        sid: 0x27
        get_seed_service: "/get_seed"
        compare_key_service: "/compare_key"
        level_availability_service: "/is_security_level_available"
        level_unlocking_result_topic: "/security_level_unlocking_result"
        static_seed: false
        reset_level_timer_ms: 1000 # [Optional]
        enabled_security_levels: [0x01, 0x03, 0x7D] # [Optional]
      - # Diagnostic Session Control Service
        sid: 0x10
      - # Tester Present Service
        sid: 0x3E
      - # Routine Control Service
        sid: 0x31
      - # Read DTC Service
        sid: 0x19
        read_dtc_service: "DtccReadDtc"
      - # Clear DTC Service
        sid: 0x14
        clear_dtc_service: "DtccClearDtc"
      - # Control DTC Settings Service
        sid: 0x85
        control_dtc_settings_service: "DtccControlDtc"
    #! [services]

    # definitions of all supported diagnostic sessions
    # the default session with id 0x01 is required
    #! [sessions]
    sessions:
      # id (required): the id of a session
      # p2 (required): Server Time P2
      # p2_star (required): Server Time P2*
      # s3 (optional): S3 Time, the session timeout default is 5000ms
      # the possible range is <2000ms, 30000ms>
      # max_pending_responses (optional): Maximum number of pending responses, default value 4
      # The P4 server = P2 + P2* x (max_pending_responses - 1)
      -
        # Default session
        id: 0x01 # (1)
        p2: 1000 # (2)
        p2_star: 2000 # (3)
        max_pending_responses: 5 # (4)
      -
        # Extended session
        id: 0x03
        p2: 500
        p2_star: 1000
        s3: 3000 # (5)
        max_pending_responses: 3
      #! [sessions]

    # Addons configuration
    addons:
      # Security Level Controller configuration
      # use_external_service (required): true/false - tells if UDS server built-in instance of
      # the Security Level Controller should be created or an external controller service shall
      # be used
      #! [security_level_controller]
      security_level_controller:
        use_external_service: false
        built_in_controller_settings: # [Optional], required in case use_external_service is false
          level_availability_service: "/is_security_level_available"
          level_unlocking_result_topic: "/security_level_unlocking_result"
          type: "individual" # possible values: ["permissive", "common", "individual"]
          common_controller_settings: # [Optional], required in case of "common" type
            failure_threshold: 3
            delay_ms: 1000
          individual_controller_settings: # [Optional], required in case of "individual" type
            levels:
              -
                level: 0x01
                failure_threshold: 3
                delay_ms: 1000
              -
                level: 0x03
                failure_threshold: 3
                delay_ms: 1000
              -
                level: 0x1B
                failure_threshold: 3
                delay_ms: 1000
              -
                level: 0x41
                failure_threshold: 3
                delay_ms: 1000
      #! [security_level_controller]

    # definitions of security accesses that enable
    # service and did in a session
    #! [access]
    access_control:
      # session_id (required): the ID of a session for which security access is set
      # sid (required): the ID of a service for which security access is set
      # security_levels (optional): an array of security levels allowed to access; no restrictions by default
      # role (optional): the array of roles that can access this service; if empty - no role is needed
      # env (optional): the map of condition/value that specifies environment conditions needed to access
      # dids (required for SID 0x22 and 0x2E): the map of did and their access restrictions
      # did (required for did access): the did value
      # allowed_addresses (optional for did and service access): the list of allowed tester addresses
      # subfunctions: the map of subfunctions and their access restrictions
      # subfunction: the subfunction byte value
      # routines: the map of routine IDs and their access restrictions
      # routine_id: the routine ID

      # default session services
      - # default session Diagnostic Session Control Service
        session_id: 0x01
        sid: 0x10
        env:
          - # condition required to change session
            key: 0x00
            value: 0x00
        subfunctions:
          -
            subfunction: 0x01
          -
            subfunction: 0x02
          -
            subfunction: 0x03
          -
            subfunction: 0x04
      -  # default session Read By Did Service
        session_id: 0x01
        sid: 0x22
        dids: # default session read dids
          -
            did: 0xFF00
          -
            did: 0xFF01
      -  # default session Read DTC
        session_id: 0x01
        sid: 0x19
        subfunctions:
          -
            subfunction: 0x01
          -
            subfunction: 0x02
          -
            subfunction: 0x03
          -
            subfunction: 0x04
          -
            subfunction: 0x05
          -
            subfunction: 0x06
          -
            subfunction: 0x07
          -
            subfunction: 0x08
          -
            subfunction: 0x09
          -
            subfunction: 0x0A
          -
            subfunction: 0x0B
          -
            subfunction: 0x0C
          -
            subfunction: 0x0D
          -
            subfunction: 0x0E
          -
            subfunction: 0x14
          -
            subfunction: 0x15
          -
            subfunction: 0x16
          -
            subfunction: 0x17
          -
            subfunction: 0x18
          -
            subfunction: 0x19
          -
            subfunction: 0x1A
          -
            subfunction: 0x42
          -
            subfunction: 0x55
          -
            subfunction: 0x56
      -  # default session Clear DTC
        session_id: 0x01
        sid: 0x14
      -  # default session Control DTC Settings
        session_id: 0x01
        sid: 0x85
        subfunctions:
          -
            subfunction: 0x01
          -
            subfunction: 0x02
      # extended session services
      - # extended session Diagnostic Session Control Service
        session_id: 0x03
        sid: 0x10
        subfunctions:
          -
            subfunction: 0x01
          -
            subfunction: 0x02
          -
            subfunction: 0x03
          -
            subfunction: 0x04
      - # extended session Security Access Service
        session_id: 0x03
        sid: 0x27
        subfunctions:
          -
            subfunction: 0x01
          -
            subfunction: 0x02
          -
            subfunction: 0x03
          -
            subfunction: 0x04
          -
            subfunction: 0x7D
          -
            subfunction: 0x7E
        # no conditions are needed to change session while in an extended session
      - # extended session read by did service
        #! [did_access]
        session_id: 0x03 # (1)
        sid: 0x22 # (2)
        dids: # extended session read dids
          -
            did: 0x0043
          -
            did: 0xF190
          -
            did: 0xFF00 # (3)
          -
            did: 0xFF01
          -
            did: 0xFF03
            security_levels: [0x01] # (4)
        #! [periodic_did_access]
        session_id: 0x03 # (1)
        sid: 0x2A # (2)
        dids: # extended session read dids
          -
            did: 0xF201
          -
            did: 0xF202
        #! [did_access]
      - # extended session Write By DID Service
        session_id: 0x03 # (1)
        sid: 0x2E # (2)
        security_levels: [0x01] # (3)
        dids: # extended session write dids
          -
            did: 0x0043
          -
            did: 0xF190
          -
            did: 0xFF01
          -
            did: 0xFF03
            security_levels: [0x01]
            role: ["ROLE_1", "ROLE_2"]
          -
            did: 0xF18E
            allowed_addresses: [0x1122, 0x3344]
      - # extended session Routine Control
        session_id: 0x03
        sid: 0x31
        routines:
          -
            routine_id: 0x0001
          -
            routine_id: 0x0002
          -
            routine_id: 0x0003
          -
            routine_id: 0x0004
      -  # default session Tester Present
        session_id: 0x03
        sid: 0x3E
        subfunctions:
          -
            subfunction: 0x00
    #! [access]
