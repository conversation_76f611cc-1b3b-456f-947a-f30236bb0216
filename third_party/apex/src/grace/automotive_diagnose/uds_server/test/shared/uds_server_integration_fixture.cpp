// Copyright 2022 Apex.AI, Inc
// All rights reserved.

#include "uds_server_integration_fixture.hpp"

#include <algorithm>  // for copy
#include <cstdint>
#include <memory>
#include <utility>
#include <vector>  // for vector

#include "executor2/executor_factory.hpp"
#include "settings/inspect/dictionary_view.hpp"
#include "uds_server/services/ioctrl/input_output_control_service.hpp"

namespace
{

std::shared_ptr<apex::timer_service::sim_timer_service> create_timer_service()
{
  return std::make_shared<apex::timer_service::sim_timer_service>();
}

using ::testing::AssertionFailure;
using ::testing::AssertionResult;
using ::testing::AssertionSuccess;

std::string to_hex_string(const uint8_t byte)
{
  std::ostringstream oss;
  oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
  return oss.str();
}

}  // namespace

namespace shared
{

void append_to_message_payload(MessagePayload & payload, const uint8_t byte)
{
  payload.push_back(byte);
}

void append_to_message_payload(MessagePayload & payload, const uint16_t val)
{
  payload.push_back(static_cast<uint8_t>((val >> 8) & 0xFF));
  payload.push_back(static_cast<uint8_t>(val & 0xFF));
}

UdsServerIntegrationFixture::UdsServerIntegrationFixture()
: DiagnosticTestNodeFixture{"UdsServerIntegrationFixture", create_timer_service()},
  m_req_pub{
    m_context->node().create_publisher<UdsTransportMessage>("/uds/req", rclcpp::DefaultQoS())},
  m_resp_sub{m_context->node().create_polling_subscription<UdsTransportMessage>(
    "/uds/rsp", rclcpp::DefaultQoS())},
  m_executor{apex::executor::executor_factory::create()},
  m_runner{std::make_unique<apex::executor::executor_runner>(
    apex::executor::executor_runner::deferred, *m_executor, std::chrono::seconds{5})},
  m_response_ws{},
  m_timer_service{std::dynamic_pointer_cast<apex::timer_service::sim_timer_service>(
    m_context->services().timer_service())}
{
  m_response_ws.add(m_resp_sub);
  const auto settings = apex::settings::generated::uds::create();
  const auto settings_view = apex::settings::inspect::dictionary_view{settings};
  const auto diag_exec_items = apex::uds_server::bringup::build(settings_view, m_context);
  for (auto & exec : diag_exec_items) {
    if (exec.m_type == apex::uds_server::bringup::ExecType::MONITOR) {
      m_env_monitor =
        std::dynamic_pointer_cast<apex::uds_server::env::EnvConditionMonitor>(exec.m_exec_item);
    }

    m_executor->add(exec.m_exec_item);
  }
}

void UdsServerIntegrationFixture::TearDown()
{
  stop_runner();
}

void UdsServerIntegrationFixture::start_runner()
{
  m_runner->issue();
}

void UdsServerIntegrationFixture::stop_runner()
{
  m_runner->stop();
}

AssertionResult UdsServerIntegrationFixture::wait_for_response(std::chrono::milliseconds wait_time)
{
  if (m_response_ws.wait(wait_time)) {
    return AssertionSuccess();
  } else {
    return AssertionFailure() << "No response received within " << wait_time.count()
                              << " milliseconds.";
  }
}

AssertionResult UdsServerIntegrationFixture::get_response(UdsTransportMessage & response,
                                                          std::chrono::milliseconds wait_time)
{
  if (auto result = wait_for_response(wait_time); !result) {
    return result;
  }

  const auto responses{m_resp_sub->take()};
  switch (responses.size()) {
    case 0:
      return AssertionFailure() << "No response received.";
    case 1:
      break;
    default:
      return AssertionFailure() << "Multiple responses received.";
  }

  const auto & msg = responses[0];
  if (!msg.info().valid()) {
    return AssertionFailure() << "Invalid response received.";
  }

  response = msg.data();
  return AssertionSuccess();
}

void UdsServerIntegrationFixture::publish_env_condition(EnvConditionKey key,
                                                        EnvConditionValue value)
{
  m_env_monitor->change_env_condition(std::move(key), std::move(value));
}

void UdsServerIntegrationFixture::send_request(UdsTransportMessage req_msg)
{
  auto loaned_msg{m_req_pub->borrow_loaned_message()};
  loaned_msg.get().payload = req_msg.payload;
  loaned_msg.get().tester_address = req_msg.tester_address;
  loaned_msg.get().ecu_address = req_msg.ecu_address;

  m_req_pub->publish(std::move(loaned_msg));
}

UdsTransportMessage UdsServerIntegrationFixture::create_req_msg(MessagePayload && data)
{
  UdsTransportMessage req_msg;
  req_msg.tester_address = m_tester_address;
  req_msg.ecu_address = m_ecu_address;
  req_msg.payload = std::move(data);
  return req_msg;
}

UdsTransportMessage UdsServerIntegrationFixture::create_req_msg(const Sid sid,
                                                                MessagePayload && data,
                                                                const bool suppress_pos_rsp)
{
  constexpr uint8_t SUP_POS_RSP_MASK = 0x80U;
  // clear payload before setting things up
  UdsTransportMessage req_msg;
  // set the sid
  append_to_message_payload(req_msg.payload, static_cast<uint8_t>(sid));
  // add data
  append_to_message_payload(req_msg.payload, std::forward<MessagePayload &&>(data));
  // set suppress pos rsp
  if (suppress_pos_rsp) {
    req_msg.payload[1] |= SUP_POS_RSP_MASK;
  }
  // set address
  req_msg.tester_address = m_tester_address;
  req_msg.ecu_address = m_ecu_address;
  return req_msg;
}

void UdsServerIntegrationFixture::change_session(const uint8_t session_id)
{
  auto req = create_req_msg(Sid::DIAGNOSTIC_SESSION_CONTROL_REQ, {session_id}, false);
  send_request(std::move(req));
  wait_for_response();
  ASSERT_TRUE(assert_positive_response(Sid::DIAGNOSTIC_SESSION_CONTROL_POS_RESP));
}

void UdsServerIntegrationFixture::change_session(const PredefinedSession session)
{
  change_session(static_cast<SessionId>(session));
}

void UdsServerIntegrationFixture::unlock_security_level(
  const SecurityLevel level,
  UdsSecurityGetSeedServiceStub & get_seed_srv,
  UdsSecurityCompareKeyServiceStub & compare_key_srv)
{
  {
    send_request_seed(level);
    get_seed_srv.wait_for_request();
    get_seed_srv.send_response([](auto & response) {
      response.success = true;
      response.seed = {0x00U};
    });
    ASSERT_TRUE(assert_positive_response(Sid::SECURITY_ACCESS_POS_RESP));
  }

  send_request_compare_key(level, {0x00U});
  compare_key_srv.wait_for_request();
  compare_key_srv.send_response([](auto & response) { response.success = true; });
  ASSERT_TRUE(assert_positive_response(Sid::SECURITY_ACCESS_POS_RESP));
}

void UdsServerIntegrationFixture::send_request_seed(const SecurityLevel level)
{
  auto req = create_req_msg(Sid::SECURITY_ACCESS_REQ, {level}, false);
  send_request(std::move(req));
}

void UdsServerIntegrationFixture::send_request_compare_key(const SecurityLevel level, KeyData key)
{
  MessagePayload req_payload = to_message_payload(static_cast<uint8_t>(level + 1U));
  append_to_message_payload(req_payload, key);
  auto req = create_req_msg(Sid::SECURITY_ACCESS_REQ, std::move(req_payload), false);
  send_request(std::move(req));
}

void UdsServerIntegrationFixture::send_request_write_did(const uint16_t did, WriteDidData data)
{
  auto req_payload = to_message_payload(did);
  std::copy(data.begin(), data.end(), std::back_inserter(req_payload));
  auto req = create_req_msg(Sid::WRITE_DATA_BY_IDENTIFIER_REQ, std::move(req_payload), false);
  send_request(std::move(req));
}

void UdsServerIntegrationFixture::tester_present()
{
  auto req = create_req_msg(Sid::TESTER_PRESENT_REQ, {0x00U}, false);
  send_request(std::move(req));
  wait_for_response();
  ASSERT_TRUE(assert_positive_response(Sid::TESTER_PRESENT_POS_RESP));
}

void UdsServerIntegrationFixture::send_io_control_req(
  const uint16_t did,
  const uds_msgs::ControlParameter control_parameter,
  std::vector<uint8_t> control_state)
{
  const auto iocp = apex::uds_server::services::ioctrl::from_message_type(control_parameter);
  const size_t payload_size = sizeof(did) + sizeof(iocp) + control_state.size();

  MessagePayload payload;
  payload.reserve(payload_size);

  append_to_message_payload(payload, did);
  append_to_message_payload(payload, static_cast<uint8_t>(iocp));
  for (const auto d : control_state) {
    payload.push_back(d);
  }

  auto req = create_req_msg(Sid::INPUT_OUTPUT_CONTROL_BY_IDENTIFIER_REQ, std::move(payload), false);
  send_request(std::move(req));
}

void UdsServerIntegrationFixture::send_periodic_req(const std::vector<uint16_t> did,
                                                    const uint8_t transmissionMode)
{
  const size_t payload_size = 1 + did.size();

  MessagePayload payload;
  payload.reserve(payload_size);

  append_to_message_payload(payload, transmissionMode);

  std::vector<uint8_t> did_bytes;
  did_bytes.reserve(did.size());

  std::transform(did.begin(), did.end(), std::back_inserter(did_bytes), [](uint16_t val) {
    return static_cast<uint8_t>(val & 0x00FF);
  });

  append_to_message_payload(payload, did_bytes);

  auto req = create_req_msg(Sid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ, std::move(payload), false);
  send_request(std::move(req));
}

void UdsServerIntegrationFixture::routine_control(const uint16_t routine_id,
                                                  const uint8_t control_type,
                                                  std::vector<uint8_t> && control_options)
{
  MessagePayload data{control_type};
  append_to_message_payload(data, routine_id);
  for (const auto d : control_options) {
    data.push_back(d);
  }

  auto req = create_req_msg(Sid::ROUTINE_CONTROL_REQ, std::move(data), false);
  send_request(std::move(req));
}

void UdsServerIntegrationFixture::request_read_dtc(const uint8_t subfunction,
                                                   const std::vector<uint8_t> params)
{
  constexpr size_t MAX_ALLOWED_PARAMS_SIZE{5U};
  ASSERT_LE(params.size(), MAX_ALLOWED_PARAMS_SIZE);

  MessagePayload data{subfunction};
  append_to_message_payload(data, std::move(params));

  auto req = create_req_msg(shared::Sid::READ_DTC_INFORMATION_REQ, std::move(data), false);
  send_request(std::move(req));
}

void UdsServerIntegrationFixture::request_clear_dtc(const std::vector<uint8_t> & group_of_dtc)
{
  MessagePayload data;
  std::copy(group_of_dtc.begin(), group_of_dtc.end(), std::back_inserter(data));

  auto req = create_req_msg(shared::Sid::CLEAR_DIAGNOSTIC_INFORMATION_REQ, std::move(data), false);
  send_request(std::move(req));
}

void UdsServerIntegrationFixture::request_control_dtc_settings(
  const uint8_t subfunction,
  const std::vector<uint8_t> & control_options,
  const bool suppress_response)
{
  constexpr size_t MAX_ALLOWED_OPTIONS_SIZE{128U};
  ASSERT_LE(control_options.size(), MAX_ALLOWED_OPTIONS_SIZE);

  MessagePayload data{subfunction};
  std::copy(control_options.begin(), control_options.end(), std::back_inserter(data));

  auto req =
    create_req_msg(shared::Sid::CONTROL_DTC_SETTINGS_REQ, std::move(data), suppress_response);
  send_request(std::move(req));
}

::testing::AssertionResult UdsServerIntegrationFixture::assert_positive_response(
  const Sid pos_rsp_sid)
{
  UdsTransportMessage msg{};
  if (auto result = get_response(msg); !result) {
    return result;
  }

  return assert_positive_response(msg, pos_rsp_sid);
}

::testing::AssertionResult UdsServerIntegrationFixture::assert_positive_response(
  const UdsTransportMessage & msg, const Sid pos_rsp_sid)
{
  const uint8_t sid = static_cast<uint8_t>(pos_rsp_sid);

  if (msg.payload[0] == static_cast<uint8_t>(Sid::NEGATIVE_RESPONSE)) {
    if (msg.payload.size() < 3U) {
      return ::testing::AssertionFailure()
             << "Received NRC instead of POS RESP! No NRC details in payload!";
    } else {
      return ::testing::AssertionFailure()
             << "Received NRC instead of POS RESP. SID: " << to_hex_string(msg.payload[1])
             << ", NRC: " << to_hex_string(msg.payload[2]);
    }
  }

  if (msg.payload[0] != static_cast<uint8_t>(sid)) {
    return ::testing::AssertionFailure() << "Expected positive response SID: " << to_hex_string(sid)
                                         << ", but got: " << to_hex_string(msg.payload[0]);
  }

  return ::testing::AssertionSuccess();
}

AssertionResult UdsServerIntegrationFixture::assert_negative_response(const Sid sid,
                                                                      const ResponseCode nrc)
{
  UdsTransportMessage msg{};
  if (auto result = get_response(msg); !result) {
    return result;
  }

  if (msg.payload[0] != static_cast<uint8_t>(Sid::NEGATIVE_RESPONSE)) {
    return ::testing::AssertionFailure()
           << "Expected negative response, but got: " << to_hex_string(msg.payload[0]);
  }

  if (msg.payload[1] != static_cast<uint8_t>(sid)) {
    return ::testing::AssertionFailure()
           << "Expected negative response SID: " << to_hex_string(static_cast<uint8_t>(sid))
           << ", but got: " << to_hex_string(msg.payload[1]);
  }

  if (msg.payload[2] != static_cast<uint8_t>(nrc)) {
    return ::testing::AssertionFailure()
           << "Expected negative response NRC: " << to_hex_string(static_cast<uint8_t>(nrc))
           << ", but got: " << to_hex_string(msg.payload[2]);
  }

  return ::testing::AssertionSuccess();
}


void UdsServerIntegrationFixture::advance_time(const std::chrono::milliseconds time)
{
  m_timer_service->now(m_timer_service->now() + time);
}

}  // namespace shared
