/// \copyright Copyright 2022 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The factory methods for common types

#ifndef UDS_SERVER__TEST__SHARED__UDS_SERVER_INTEGRATION_FIXTURE_HPP_
#define UDS_SERVER__TEST__SHARED__UDS_SERVER_INTEGRATION_FIXTURE_HPP_

#include <algorithm>  // for copy
#include <chrono>  // for milliseconds
#include <cstdint>
#include <iterator>  // for next
#include <memory>  // for unique_ptr
#include <type_traits>  // for enable_if
#include <utility>  // for move
#include <vector>  // for vector

#include "executor2/executor_runner.hpp"  // for apex::executor::executor_runner
#include "fixtures/diagnostic_test_node_fixture.hpp"  // for DiagnosticTestNodeFixture
#include "fixtures/diagnostic_test_srv_client_fixture_base.hpp"  // for DiagnosticTestSrvClientFixtureBase
#include "gtest/gtest.h"
#include "rclcpp/node.hpp"  // for Publisher, PollingSubscription
#include "settings/uds_server_settings.hpp"  // for create
#include "timer_service/sim_timer_service.hpp"
#include "uds_server/bringup/uds_builder.hpp"  // for build
#include "uds_server/env/env_condition_monitor.hpp"
#include "uds_server/services/security/security_access.hpp"  // for SecurityLevel
#include "uds_server/services/shared/response_code.hpp"  // for UdsResponseCode
#include "uds_server/services/shared/sid.hpp"  // for UdsSid
#include "uds_server/session/predefined_session.hpp"  // for PredefinedSession
#include "uds_server/session/session_base.hpp"  // for SessionId

#include "uds_msgs/msg/uds_transport_message.hpp"
#include "uds_msgs/srv/uds_input_output_control.hpp"
#include "uds_msgs/srv/uds_read_data_by_identifier.hpp"
#include "uds_msgs/srv/uds_read_dtc.hpp"
#include "uds_msgs/srv/uds_security_compare_key.hpp"
#include "uds_msgs/srv/uds_security_get_seed.hpp"
#include "uds_msgs/srv/uds_write_data_by_identifier.hpp"

namespace uds = apex::uds_server;
namespace diagnostic_common = apex::diagnostic_common;

namespace shared
{

using DiagnosticTestNodeFixture = diagnostic_common::testing::DiagnosticTestNodeFixture;
using UdsTransportMessage = uds_msgs::msg::UdsTransportMessage;
using UdsAddress = uds_msgs::msg::UdsAddress;

using EnvConditionKey = apex::uds_server::env::EnvConditionKey;
using EnvConditionValue = apex::uds_server::env::EnvConditionValue;

using MessagePayload = uds_msgs::msg::UdsTransportMessage::_payload_type;
using Sid = uds::services::shared::UdsSid;
using ResponseCode = uds::services::shared::UdsResponseCode;
using SecurityLevel = uds::services::security::SecurityLevel;
using SessionId = uds::session::SessionId;
using PredefinedSession = uds::session::PredefinedSession;

using SeedData = uds_msgs::srv::UdsSecurityGetSeed_Response::BorrowedType::_seed_type;
using KeyData = uds_msgs::srv::UdsSecurityCompareKey_Request::BorrowedType::_key_type;

using UdsSecurityGetSeed = uds_msgs::srv::UdsSecurityGetSeed;
using UdsSecurityCompareKey = uds_msgs::srv::UdsSecurityCompareKey;
using UdsSecurityGetSeedServiceStub =
  apex::diagnostic_common::testing::DiagnosticTestSrvClientFixtureBase<UdsSecurityGetSeed>;
using UdsSecurityCompareKeyServiceStub =
  apex::diagnostic_common::testing::DiagnosticTestSrvClientFixtureBase<UdsSecurityCompareKey>;

using ReadDidResponsePayloadT = uds_msgs::srv::UdsReadDataByIdentifier_Response::_payload_type;
using ReadDidPayload = apex::BoundedVector<uint8_t, ReadDidResponsePayloadT::capacity()>;

using WriteDidRequestDataT = uds_msgs::srv::UdsWriteDataByIdentifier_Request::_data_type;
using WriteDidData = apex::BoundedVector<uint8_t, WriteDidRequestDataT::capacity()>;

constexpr size_t SEED_BEGIN_BYTE_IN_PAYLOAD{2U};
constexpr size_t READ_DID_DATA_RECORD_BEGIN_BYTE_IN_PAYLOAD{3U};
constexpr size_t WRITE_DID_DATA_RECORD_BEGIN_BYTE_IN_PAYLOAD{3U};
constexpr size_t WRITE_DID_BEGIN_BYTE_IN_PAYLOAD{1U};

const auto DEFAULT_TESTER_ADDRESS = uds_msgs::msg::UdsAddress::create_msg(
  uds_msgs::msg::UdsAddress::UdsAddressTypeEnum::PHYSICAL, 0x34);
const auto DEFAULT_ECU_ADDRESS = uds_msgs::msg::UdsAddress::create_msg(
  uds_msgs::msg::UdsAddress::UdsAddressTypeEnum::PHYSICAL, 0x2233U);


/// \brief Append byte value to the message payload.
/// \param[in, out] payload The message payload to which append the data.
/// \param[in] byte The byte to be appended.
void append_to_message_payload(MessagePayload & payload, const uint8_t byte);

/// \brief Append unsigned short bytes to the message payload.
/// \param[in, out] payload The message payload to which append the data.
/// \param[in] value The value to be appended.
void append_to_message_payload(MessagePayload & payload, const uint16_t value);

/// \brief Append a container of `uint8_t` to a message payload.
/// \param[in, out] payload The message payload to which append the data.
/// \param[in] bytes The payload to be appended.
template <typename Vector>
std::enable_if_t<std::is_same_v<typename Vector::value_type, uint8_t>, void>
append_to_message_payload(MessagePayload & payload, const Vector & bytes)
{
  std::copy(bytes.begin(), bytes.end(), std::back_inserter(payload));
}

template <typename Value, typename... Values>
void append_to_message_payload(MessagePayload & payload, const Value & value, Values &&... rest)
{
  append_to_message_payload(payload, value);
  append_to_message_payload(payload, std::forward<Values>(rest)...);
}

/// \brief Convert data to message payload.
/// \tparam Data The data type.
/// \param data The data to be converted.
/// \return MessagePayload The resulting message payload.
template <typename... Data>
MessagePayload to_message_payload(Data &&... data)
{
  MessagePayload payload;
  append_to_message_payload(payload, std::forward<Data>(data)...);
  return payload;
}

/// \brief The UdsServer Integration Fixture that builds all executable items using default settings
/// and add them to the executor and a runner.
/// TODO(mateusz.wierzchowski): #21417 Split this class into small DiagnosticService client types.
class UdsServerIntegrationFixture : public DiagnosticTestNodeFixture
{
public:
  /// \brief Construct a new Uds Server Integration Fixture object
  /// and setup the executor and the runner (in deferred mode) with all UdsServer executable_items
  /// from a default settings.
  UdsServerIntegrationFixture();

  void TearDown() override;

  /// \brief Start the runner.
  void start_runner();

  /// \brief Stop the runner.
  void stop_runner();

  /// \brief Wait for the UdsServer response.
  /// \param[in] wait_time The max wait time.
  /// \return AssertionResult indicating if the response was received.
  ::testing::AssertionResult wait_for_response(
    std::chrono::milliseconds wait_time = std::chrono::milliseconds{1000});

  /// \brief Wait for a response from the UdsServer and return it.
  /// \param[out] response The UdsTransportMessage with the UdsServer response.
  /// \param[in] wait_time The max wait time.
  /// \return AssertionResult indicating if the response was received.
  ::testing::AssertionResult get_response(
    UdsTransportMessage & response,
    std::chrono::milliseconds wait_time = std::chrono::milliseconds{1000});

  /// \brief Publishes an environment condition.
  /// \param[in] key The key identifying the environment condition.
  /// \param[in] value The value associated with the environment condition.
  void publish_env_condition(EnvConditionKey key, EnvConditionValue value);

  /// \brief Sends the request to the UdsServer.
  /// \param[in] req_msg The request message to send.
  void send_request(UdsTransportMessage req_msg);

  /// \brief Create a massage of a request.
  /// \param[in] data The message data (payload).
  /// \return Created UdsTransportMessage.
  UdsTransportMessage create_req_msg(MessagePayload && data);

  /// \brief Create a massage of a request for a specific diagnostic service.
  /// \param[in] sid The ID of the diagnostic service.
  /// \param[in] data The message data (payload).
  /// \param[in] suppress_pos_rsp The flag if to set suppress pos rsp.
  /// \return Created UdsTransportMessage.
  UdsTransportMessage create_req_msg(const Sid sid,
                                     MessagePayload && data,
                                     const bool suppress_pos_rsp = false);

  /// \brief Change a session and assert a positive response.
  /// \param[in] session_id The ID of a requested session.
  void change_session(const uint8_t session_id);

  /// \brief Change a session and assert a positive response.
  /// \param[in] session The predefined session.
  void change_session(const PredefinedSession session);


  /// \brief Unlock a security level with security handler application stubs.
  /// \param[in] level The security level.
  /// \param[in, out] get_seed_srv The Uds Security GetSeed Service Stub.
  /// \param[in, out] compare_key_srv The Uds Security CompareKey Service Stub.
  void unlock_security_level(const SecurityLevel level,
                             UdsSecurityGetSeedServiceStub & get_seed_srv,
                             UdsSecurityCompareKeyServiceStub & compare_key_srv);

  /// \brief Send a request for a seed without processing the response.
  /// \param[in] level The security level.
  void send_request_seed(const SecurityLevel level);

  /// \brief Send a request to unlock a security level without processing the response.
  /// \param[in] level The security level.
  /// \param[in] key The valid key for obtained seed.
  void send_request_compare_key(const SecurityLevel level, KeyData key);

  /// \brief Send a read data by identifier request without processing the response.
  /// This method is using the DEFAULT_TESTER_ADDRESS.
  /// \tparam Dids The DIDs types.
  /// \param[in] dids The DIDs to read.
  template <typename... Dids>
  void send_request_read_dids(Dids... dids)
  {
    auto req = create_req_msg(
      Sid::READ_DATA_BY_IDENTIFIER_REQ, to_message_payload(std::forward<Dids>(dids)...), false);
    send_request(std::move(req));
  }

  /// \brief Send a write data by identifier request without processing the response.
  /// \param[in] did The DID to write.
  /// \param[in] data The data record to write.
  void send_request_write_did(const uint16_t did, WriteDidData data);

  /// \brief Send a TesterPresent request and assert positive response
  void tester_present();

public:  // Input Output Control By Identidier (0x2F)
  /// \brief Send Input Output Control By Identidier Request
  /// \param[in] did The target Data Identifier.
  /// \param[in] control_type The Input Outpt Control Parameter (IOCP)
  /// \param[in] control_state The control state bytes.
  void send_io_control_req(const uint16_t did,
                           const uds_msgs::ControlParameter control_type,
                           std::vector<uint8_t> control_options = {});

public:  // Read Data By Periodic Identidier (0x2A)
  void send_periodic_req(const std::vector<uint16_t> did, const uint8_t transmissionMode);

public:  // Routine Control Service
  /// \brief Requests RoutineControl from the UDS Server.
  /// \param[in] routine_id The ID of requested routine.
  /// \param[in] control_type The control type used to control routine behavior.
  /// \param[in] control_options The additional routine control options .
  void routine_control(const uint16_t routine_id,
                       const uint8_t control_type,
                       std::vector<uint8_t> && control_options = {});

  /// \brief Request Read DTC Information from the UDS Server.
  /// \param[in] subfunction The subfunction byte value.
  /// \param[in] params The arbitrary data passed to the request which size and content depends on
  /// the subfunction.
  void request_read_dtc(const uint8_t subfunction, const std::vector<uint8_t> params = {});

  /// \brief Request clear of the DTC data using provided group od DTC.
  /// \param[in] group_of_dtc The group of DTC to clear.
  void request_clear_dtc(const std::vector<uint8_t> & group_of_dtc);

  /// \brief Request changing of the DTC Settings with provided control options from the UDS Server.
  /// \param[in] subfunction The subfunction value.
  /// \param[in] control_options The vector of control options.
  /// \param[in] suppress_response The flag that marks if the positive response should be
  /// suppressed.
  void request_control_dtc_settings(const uint8_t subfunction,
                                    const std::vector<uint8_t> & control_options,
                                    const bool suppress_response = false);

  /// \brief Waits for a response and asserts it is positive.
  /// \param[in] pos_rsp_sid The positive response SID.
  /// \return AssertionResult indicating if the response is positive.
  ::testing::AssertionResult assert_positive_response(const Sid pos_rsp_sid);

  /// \brief Assert the positive response.
  /// \param[in] msg The UdsTransportMessage with a UdsServer response.
  /// \param[in] pos_rsp_sid The positive response SID.
  /// \return AssertionResult indicating if the response is positive.
  ::testing::AssertionResult assert_positive_response(const UdsTransportMessage & msg,
                                                      const Sid pos_rsp_sid);

  /// \brief Wait for a response and assert it is negative.
  /// \param[in] sid The SID of a service that should send a response.
  /// \param[in] nrc The NRC with an error code.
  ::testing::AssertionResult assert_negative_response(const Sid sid, const ResponseCode nrc);

  /// \brief Advance the timer_service clock.
  /// \param[in] time The duration to advance from now.
  /// \note This method is using the m_timer_service.
  void advance_time(const std::chrono::milliseconds time);

  /// \brief The publisher of requests to the UdsServer.
  const rclcpp::Publisher<UdsTransportMessage>::SharedPtr m_req_pub;
  /// \brief The subscriber of UdsServer's responses.
  const rclcpp::PollingSubscription<UdsTransportMessage>::SharedPtr m_resp_sub;
  /// \brief The unique pointer with an executor that executes UdsServer exec items.
  apex::executor::executor_ptr m_executor;
  /// \brief The runner which runs the m_executor.
  std::unique_ptr<apex::executor::executor_runner> m_runner;
  /// \brief The waitset to wait on the UdsServer responses.
  rclcpp::dynamic_waitset::Waitset m_response_ws;
  /// \brief The current tester address used in a request message.
  UdsAddress m_tester_address = DEFAULT_TESTER_ADDRESS;
  /// \brief The current ecu address used in a request message.
  UdsAddress m_ecu_address = DEFAULT_ECU_ADDRESS;

  /// \brief Environment Monitor.
  std::shared_ptr<uds::env::EnvConditionMonitor> m_env_monitor;
  /// \brief Timer service that can be manipulated for test purposes
  std::shared_ptr<apex::timer_service::sim_timer_service> m_timer_service;
};

}  // namespace shared

#endif  // UDS_SERVER__TEST__SHARED__UDS_SERVER_INTEGRATION_FIXTURE_HPP_
