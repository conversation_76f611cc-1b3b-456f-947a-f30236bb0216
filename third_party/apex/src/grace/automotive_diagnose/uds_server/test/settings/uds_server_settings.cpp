/// \copyright Copyright 2023 Apex.AI, Inc
/// All rights reserved.

#include "uds_server_settings.hpp"

#include "settings/from_yaml.hpp"

namespace apex::settings::generated::uds
{

using apex::settings::construct::dictionary;

dictionary create()
{
  constexpr auto * const config = R"--(
diagnostic:
  uds:
    access_control:
      - env:
          - key: 0
            value: 0
        session_id: 1
        sid: 16
        subfunctions:
          - subfunction: 1
          - subfunction: 2
          - subfunction: 3
          - subfunction: 4
      - dids:
          - did: 0xFF00
          - did: 0xFF01
        session_id: 1
        sid: 34
      - session_id: 1
        sid: 25
        subfunctions:
          - subfunction: 1
          - subfunction: 2
          - subfunction: 3
          - subfunction: 4
          - subfunction: 5
          - subfunction: 6
          - subfunction: 7
          - subfunction: 8
          - subfunction: 9
          - subfunction: 10
          - subfunction: 11
          - subfunction: 12
          - subfunction: 13
          - subfunction: 14
          - subfunction: 20
          - subfunction: 21
          - subfunction: 22
          - subfunction: 23
          - subfunction: 24
          - subfunction: 25
          - subfunction: 26
          - subfunction: 66
          - subfunction: 85
          - subfunction: 86
      - session_id: 1
        sid: 20
      - session_id: 1
        sid: 133
        subfunctions:
          - subfunction: 1
          - subfunction: 2
      - session_id: 3
        sid: 16
        subfunctions:
          - subfunction: 1
          - subfunction: 2
          - subfunction: 3
          - subfunction: 4
      - session_id: 3
        sid: 39
        subfunctions:
          - subfunction: 1
          - subfunction: 2
          - subfunction: 3
          - subfunction: 4
          - subfunction: 125
          - subfunction: 126
      - dids:
          - did: 0x0043
          - did: 0xF190
          - did: 0xFF00
          - did: 0xFF01
          - did: 0xFF03
            security_levels:
              - 1
        session_id: 3
        sid: 34
      - dids:
          - did: 0xF201
          - did: 0xF202
            security_levels:
              - 1
        session_id: 3
        sid: 42
      - dids:
          - did: 0x0043
          - did: 0xF190
          - did: 0xFF01
          - did: 0xFF03
            role:
              - ROLE_1
              - ROLE_2
            security_levels:
              - 1
          - allowed_addresses:
              - 4386
              - 13124
            did: 0xF18E
        security_levels:
          - 1
        session_id: 3
        sid: 46
      - routines:
          - routine_id: 1
          - routine_id: 2
          - routine_id: 3
          - routine_id: 4
        session_id: 3
        sid: 49
      - session_id: 3
        sid: 62
        subfunctions:
          - subfunction: 0

      - session_id: 1
        sid: 0x2F
        dids:
          - did: 0x0043

    addons:
      security_level_controller:
        built_in_controller_settings:
          common_controller_settings:
            delay_ms: 1000
            failure_threshold: 3
          individual_controller_settings:
            levels:
              - delay_ms: 1000
                failure_threshold: 3
                level: 1
              - delay_ms: 1000
                failure_threshold: 3
                level: 3
              - delay_ms: 1000
                failure_threshold: 3
                level: 27
              - delay_ms: 1000
                failure_threshold: 3
                level: 65
          level_availability_service: /is_security_level_available
          level_unlocking_result_topic: /security_level_unlocking_result
          type: individual
        use_external_service: false
    dids:
      - data:
          - 3
          - 0
          - 0
          - 0
        did: 0xFF00
        type: static
      - did: 0xF190
        read_service: /uds/did/example1/read
        type: async
        write_service: /uds/did/example1/write
      - did: 0xF18C
        read_service: /uds/did/example2/read
        type: async
        write_service: /uds/did/example2/write
      - did: 0xF18D
        read_service: /uds/did/example3/read
        type: async
        write_service: /uds/did/example3/write
      - did: 0xF18E
        read_service: /uds/did/example4/read
        type: async
        write_service: /uds/did/example4/write
      - did: 0x0043
        read_service: /uds/did/example5/read
        type: async
        write_service: /uds/did/example5/write
      - did: 0xF201
        read_service: /uds/did/example6/read
        type: async
      - did: 0xF202
        read_service: /uds/did/example7/read
        type: async
    env_topic: /uds/env
    req_topic: /uds/req
    res_topic: /uds/rsp
    ecu_logical_address: 0x2233
    routines:
      - routine_id: 1
        routine_service: /uds/routine_0001_0002
        supported_subfunctions:
          - start
          - stop
          - result
      - routine_id: 2
        routine_service: /uds/routine_0001_0002
        supported_subfunctions:
          - start
          - stop
          - result
        timeout_ms: 1000
      - routine_id: 3
        routine_service: /uds/routine_0003
        supported_subfunctions:
          - start
          - result
        timeout_ms: 1000
      - routine_id: 4
        routine_service: /uds/routine_0004
        supported_subfunctions:
          - start
          - result

    ioctrl:
      - did: 0x0043
        control_service: /uds/ioctrl_0001_0002
        timeout_ms: 500

    services:
      # Read Data By Identifier service
      - sid: 0x22
      
      # Read Data By Periodic Identifier service
      - sid: 0x2A
        slow_period_ms: 100
        mid_period_ms: 50
        fast_period_ms: 25

      # Write Data By Identifier service
      - sid: 0x2E

      # Input Output Control by Identifier service
      - sid: 0x2F

      # Security Access Service
      - sid: 0x27
        compare_key_service: /compare_key
        enabled_security_levels: [0x01, 0x03, 0x7D]
        get_seed_service: /get_seed
        level_availability_service: /is_security_level_available
        level_unlocking_result_topic: /security_level_unlocking_result
        reset_level_timer_ms: 1000
        static_seed: false

      # Diagnostic Session Control service
      - sid: 0x10

      # Tester Present Service
      - sid: 0x3E

      # Routine Control Service
      - sid: 0x31

      # Read DTC Service
      - sid: 0x19
        read_dtc_service: DtccReadDtc

      # Clear DTC Service
      - sid: 0x14
        clear_dtc_service: DtccClearDtc

      # Control DTC Settings Service
      - sid: 0x85
        control_dtc_settings_service: DtccControlDtc

    session_event_topic: /uds/session_event
    sessions:
      - id: 1
        max_pending_responses: 5
        p2: 1000
        p2_star: 2000
      - id: 3
        max_pending_responses: 3
        p2: 500
        p2_star: 1000
        s3: 3000
  )--";

  dictionary root;
  apex::settings::yaml::from_string(config, root);
  return root;
}

}  // namespace apex::settings::generated::uds
