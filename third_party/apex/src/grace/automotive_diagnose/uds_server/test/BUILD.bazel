load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")

cc_library(
    name = "test_mocks",
    testonly = True,
    srcs = [
        "mocks/negative_response_matcher.cpp",
    ],
    hdrs = [
        "mocks/mock_clear_dtc_client.hpp",
        "mocks/mock_control_dtc_settings_client.hpp",
        "mocks/mock_diagnostic_service.hpp",
        "mocks/mock_did_access.hpp",
        "mocks/mock_did_pool.hpp",
        "mocks/mock_did_reader.hpp",
        "mocks/mock_did_writer.hpp",
        "mocks/mock_env_condition_monitor.hpp",
        "mocks/mock_input_output_control_requester.hpp",
        "mocks/mock_key_comparator.hpp",
        "mocks/mock_level_controller.hpp",
        "mocks/mock_level_delays.hpp",
        "mocks/mock_level_unlocking_result_publisher.hpp",
        "mocks/mock_observers.hpp",
        "mocks/mock_read_dtc_client.hpp",
        "mocks/mock_request_response_tracker.hpp",
        "mocks/mock_response_publisher.hpp",
        "mocks/mock_routine_access.hpp",
        "mocks/mock_routine_client.hpp",
        "mocks/mock_routine_registry.hpp",
        "mocks/mock_security_delay.hpp",
        "mocks/mock_security_observers.hpp",
        "mocks/mock_seed_fetcher.hpp",
        "mocks/mock_server_timeout_p4_handler.hpp",
        "mocks/mock_service_access.hpp",
        "mocks/mock_service_hub.hpp",
        "mocks/mock_session_event_publisher.hpp",
        "mocks/mock_session_manager.hpp",
        "mocks/mock_subfunction_access.hpp",
        "mocks/mock_timer_base.hpp",
        "mocks/negative_response_matcher.hpp",
    ],
    strip_include_prefix = ".",
    deps = [
        "//grace/automotive_diagnose/uds_server:uds_protocol",
        "@googletest//:gtest",
    ],
)

cc_library(
    name = "test_settings",
    testonly = True,
    srcs = ["settings/uds_server_settings.cpp"],
    hdrs = ["settings/uds_server_settings.hpp"],
    strip_include_prefix = ".",
    target_compatible_with = select({
        "//common/asil:d": ["@platforms//:incompatible"],
        "//conditions:default": [],
    }),
    deps = [
        "//common/configuration/settings",
    ],
)

cc_library(
    name = "test_shared",
    testonly = True,
    srcs = [
        "shared/uds_server_integration_fixture.cpp",
    ],
    hdrs = [
        "shared/service_test_fixture.hpp",
        "shared/types_factory.hpp",
        "shared/uds_server_integration_fixture.hpp",
    ],
    strip_include_prefix = ".",
    deps = [
        ":test_settings",
        "//common/cpputils",
        "//grace/automotive_diagnose/common",
        "//grace/automotive_diagnose/common/test:common_test_lib",
        "//grace/execution/executor2",
        "@apex//grace/automotive_diagnose/uds_server:uds_protocol",
        "@googletest//:gtest",
    ],
)

apex_cc_test(
    name = "test_uds_server",
    srcs = [
        "unit/test_access_controller.cpp",
        "unit/test_access_parser.cpp",
        "unit/test_access_types.cpp",
        "unit/test_async_clear_dtc_client.cpp",
        "unit/test_async_control_dtc_settings.cpp",
        "unit/test_async_did_reader.cpp",
        "unit/test_async_did_writer.cpp",
        "unit/test_async_key_comparator.cpp",
        "unit/test_async_level_controller.cpp",
        "unit/test_async_read_dtc_client.cpp",
        "unit/test_async_routine_client.cpp",
        "unit/test_async_seed_fetcher.cpp",
        "unit/test_clear_dtc_info_service.cpp",
        "unit/test_common_level_controller.cpp",
        "unit/test_control_dtc_settings_service.cpp",
        "unit/test_default_session.cpp",
        "unit/test_diagnostic_session_control_service.cpp",
        "unit/test_did_pool.cpp",
        "unit/test_dtc_parser.cpp",
        "unit/test_env_condition_monitor.cpp",
        "unit/test_individual_level_controller.cpp",
        "unit/test_input_output_control_service.cpp",
        "unit/test_level_controller_base.cpp",
        "unit/test_level_unlocking_result_publisher.cpp",
        "unit/test_permissive_level_controller.cpp",
        "unit/test_read_by_did_periodically_service.cpp",
        "unit/test_read_by_did_service.cpp",
        "unit/test_read_dtc_info_service.cpp",
        "unit/test_request_response_tracker.cpp",
        "unit/test_request_subscriber.cpp",
        "unit/test_response_publisher.cpp",
        "unit/test_routine_control_service.cpp",
        "unit/test_routine_parser.cpp",
        "unit/test_routine_registry.cpp",
        "unit/test_security_access_service.cpp",
        "unit/test_security_delay.cpp",
        "unit/test_security_level_controller_parser.cpp",
        "unit/test_service_hub.cpp",
        "unit/test_session.cpp",
        "unit/test_session_event_publisher.cpp",
        "unit/test_session_manager.cpp",
        "unit/test_static_did_reader.cpp",
        "unit/test_tester_present_service.cpp",
        "unit/test_uds_builder.cpp",
        "unit/test_uds_config_parser.cpp",
        "unit/test_write_by_did_service.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":test_mocks",
        ":test_settings",
        ":test_shared",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_uds_server_integration",
    srcs = [
        "integration/test_clear_dtc.cpp",
        "integration/test_control_dtc_settings.cpp",
        "integration/test_input_output_control.cpp",
        "integration/test_read_did.cpp",
        "integration/test_read_did_periodically.cpp",
        "integration/test_read_dtc.cpp",
        "integration/test_routine_control.cpp",
        "integration/test_security_access.cpp",
        "integration/test_write_did.cpp",
    ],
    env = {
        "APEX_GRACE_LOG_LEVEL": "TRACE",
        "IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc",
    },
    tags = [
        "constrained_test",
        "exclusive",
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
    ],
    deps = [
        ":test_mocks",
        ":test_settings",
        ":test_shared",
        "@googletest//:gtest_main",
    ],
)
