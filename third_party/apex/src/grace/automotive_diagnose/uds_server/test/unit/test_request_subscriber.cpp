// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include <memory>  // for std::shared_ptr
#include <utility>  // for std::move

#include "fixtures/diagnostic_test_sub_fixture.hpp"
#include "gtest/gtest.h"
#include "mocks/mock_service_hub.hpp"
#include "uds_server/com/request_subscriber.hpp"  // for RequestSubscriber

#include "uds_msgs/msg/uds_transport_message.hpp"  // for UdsAddress, UdsTransportMessage

namespace uds = apex::uds_server;
namespace diagnostic_common = apex::diagnostic_common;

using UdsAddress = uds_msgs::msg::UdsAddress;
using UdsTransportMessage = uds_msgs::msg::UdsTransportMessage;

using RequestSubscriberBaseFixture =
  diagnostic_common::testing::DiagnosticTestSingleSubNodeFixture<uds::com::RequestSubscriber,
                                                                 UdsTransportMessage>;
using testing::_;

namespace
{
constexpr uint16_t ECU_ADDRESS{0x2233U};
const apex::string256_t SUB_TOPIC = "/uds/req";

struct RequestSubscriberFixture : RequestSubscriberBaseFixture
{
  RequestSubscriberFixture()
  : RequestSubscriberBaseFixture("RequestSubscriberFixture", SUB_TOPIC),
    m_mock_service_hub{std::make_shared<uds::com::MockServiceHub>()}
  {
    set_sut(std::make_unique<uds::com::RequestSubscriber>(
      m_context->as_ref(), SUB_TOPIC, m_mock_service_hub, ECU_ADDRESS));
  }
  // mocks
  std::shared_ptr<uds::com::MockServiceHub> m_mock_service_hub;
};

}  // namespace

TEST_F(RequestSubscriberFixture, shall_do_nothing_on_invalid_msg)
{
  // setup mock
  EXPECT_CALL(*m_mock_service_hub, dispatch_request(_, _)).Times(0);
  // send message
  auto msg = borrow_loaned_message();
  publish(std::move(msg));
  // call execute_impl
  execute_on_sub_received();
}

TEST_F(RequestSubscriberFixture, shall_do_nothing_on_default_ecu_address)
{
  // setup mock
  EXPECT_CALL(*m_mock_service_hub, dispatch_request(_, _)).Times(0);
  // send message
  auto msg = borrow_loaned_message();
  msg.get().payload.push_back(0x22U);
  publish(std::move(msg));
  // call execute_impl
  execute_on_sub_received();
}

TEST_F(RequestSubscriberFixture, shall_do_nothing_on_wrong_ecu_address)
{
  // setup mock
  EXPECT_CALL(*m_mock_service_hub, dispatch_request(_, _)).Times(0);
  // send message
  auto msg = borrow_loaned_message();
  msg->ecu_address.address = ECU_ADDRESS + 1;
  msg.get().payload.push_back(0x22U);
  publish(std::move(msg));
  // call execute_impl
  execute_on_sub_received();
}

TEST_F(RequestSubscriberFixture, shall_dispatch_msg_to_service_hub)
{
  // setup mock
  EXPECT_CALL(*m_mock_service_hub, dispatch_request(_, _)).Times(1);
  // send message
  auto msg = borrow_loaned_message();
  msg->ecu_address.address = ECU_ADDRESS;
  msg.get().payload.push_back(0x22U);
  publish(std::move(msg));
  // call execute_impl
  execute_on_sub_received();
}
