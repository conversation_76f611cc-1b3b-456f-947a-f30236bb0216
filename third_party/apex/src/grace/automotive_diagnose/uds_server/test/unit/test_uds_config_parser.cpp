/// \copyright Copyright 2025 Apex.AI, Inc
/// All rights reserved.

#include <limits>  // for std::numeric_limits<>
#include <memory>  // for std::make_shared<>

#include "diagnostic_common/config_error.hpp"
#include "diagnostic_common/node_context.hpp"
#include "fixtures/diagnostic_test_node_fixture.hpp"
#include "gtest/gtest.h"  // for TEST_F, ASSERT_*, EXPECT_*
#include "settings/construct.hpp"
#include "settings/uds_server_settings.hpp"
#include "uds_server/bringup/config.hpp"
#include "uds_server/bringup/uds_config_parser.hpp"
#include "uds_server/services/shared/access_types.hpp"

namespace uds = apex::uds_server;
namespace bringup = uds::bringup;
namespace diagnostic_common = apex::diagnostic_common;

using apex::settings::construct::array;
using apex::settings::construct::dictionary;
using apex::settings::construct::get;
using apex::settings::construct::integer;
using apex::settings::construct::make_array;
using apex::settings::construct::make_dictionary;
using apex::settings::construct::make_value;
using apex::settings::construct::string;
using apex::settings::inspect::dictionary_view;

namespace
{
struct UdsConfigParserFixture : diagnostic_common::testing::DiagnosticTestNodeFixture
{
  UdsConfigParserFixture()
  : DiagnosticTestNodeFixture{"UdsConfigParserFixture"},
    m_settings{apex::settings::generated::uds::create()},
    m_settings_view{m_settings},
    m_empty_settings{},
    m_empty_settings_view{m_empty_settings},
    m_context_ref{m_context->as_ref()}
  {
  }

  dictionary m_settings;
  dictionary_view m_settings_view;
  dictionary m_empty_settings;
  dictionary_view m_empty_settings_view;

  diagnostic_common::NodeContextRef m_context_ref;
};

}  // namespace

TEST_F(UdsConfigParserFixture, shall_return_req_topic)
{
  auto req_topic = bringup::parse_req_topic(m_settings_view);
  EXPECT_EQ(req_topic, "/uds/req");
}

TEST_F(UdsConfigParserFixture, shall_throw_on_invalid_req_topic)
{
  EXPECT_THROW(bringup::parse_req_topic(m_empty_settings_view), diagnostic_common::config_error);
}

TEST_F(UdsConfigParserFixture, shall_return_res_topic)
{
  auto rsp_topic = bringup::parse_res_topic(m_settings_view);
  EXPECT_EQ(rsp_topic, "/uds/rsp");
}

TEST_F(UdsConfigParserFixture, shall_throw_on_invalid_res_topic)
{
  EXPECT_THROW(bringup::parse_res_topic(m_empty_settings_view), diagnostic_common::config_error);
}

TEST_F(UdsConfigParserFixture, shall_return_env_topic)
{
  auto env_topic = bringup::parse_env_topic(m_settings_view);
  EXPECT_EQ(env_topic, "/uds/env");
}

TEST_F(UdsConfigParserFixture, shall_throw_on_invalid_env_topic)
{
  EXPECT_THROW(bringup::parse_env_topic(m_empty_settings_view), diagnostic_common::config_error);
}

TEST_F(UdsConfigParserFixture, shall_return_session_event_topic)
{
  auto session_event_topic = bringup::parse_session_event_topic(m_settings_view);
  EXPECT_EQ(session_event_topic, "/uds/session_event");
}

TEST_F(UdsConfigParserFixture, shall_throw_on_invalid_session_event_topic)
{
  EXPECT_THROW(bringup::parse_session_event_topic(m_empty_settings_view),
               diagnostic_common::config_error);
}

TEST_F(UdsConfigParserFixture, shall_parse_dids)
{
  auto dids = bringup::parse_dids(m_settings_view, m_context_ref);
  ASSERT_EQ(dids.first.size(), 8u);
  // static reader, no writer
  EXPECT_EQ(dids.first[0].m_did, 0xFF00);
  EXPECT_TRUE(dids.first[0].m_reader);
  EXPECT_FALSE(dids.first[0].m_writer);
  // async reader and writer
  EXPECT_EQ(dids.first[1].m_did, 0xF190);
  EXPECT_TRUE(dids.first[1].m_reader);
  EXPECT_TRUE(dids.first[1].m_writer);
  // async reader and writer
  EXPECT_EQ(dids.first[2].m_did, 0xF18C);
  EXPECT_TRUE(dids.first[2].m_reader);
  EXPECT_TRUE(dids.first[2].m_writer);
  // async reader and writer
  EXPECT_EQ(dids.first[3].m_did, 0xF18D);
  EXPECT_TRUE(dids.first[3].m_reader);
  EXPECT_TRUE(dids.first[3].m_writer);

  EXPECT_EQ(dids.first[4].m_did, 0xF18E);
  EXPECT_TRUE(dids.first[4].m_reader);
  EXPECT_TRUE(dids.first[4].m_writer);

  EXPECT_EQ(dids.first[5].m_did, 0x0043);
  EXPECT_TRUE(dids.first[5].m_reader);
  EXPECT_TRUE(dids.first[5].m_writer);

  EXPECT_EQ(dids.first[6].m_did, 0xF201);
  EXPECT_TRUE(dids.first[6].m_reader);

  EXPECT_EQ(dids.first[7].m_did, 0xF202);
  EXPECT_TRUE(dids.first[7].m_reader);

  EXPECT_EQ(dids.second.size(), 12u);
}

TEST_F(UdsConfigParserFixture, parse_dids_shall_throw_on_invalid_did_value)
{
  // missing did value
  EXPECT_THROW(bringup::parse_dids(m_empty_settings_view, m_context_ref),
               diagnostic_common::config_error);

  // set invalid did value
  auto & dids = get<array>(m_settings, "diagnostic/uds/dids");
  get<integer>(dids[0], "did") = -1;
  EXPECT_THROW(bringup::parse_dids(m_settings_view, m_context_ref),
               diagnostic_common::config_error);
}

TEST_F(UdsConfigParserFixture, parse_dids_shall_throw_on_invalid_did_type)
{
  auto & dids = get<array>(m_settings, "diagnostic/uds/dids");
  dids.clear();
  auto did_dict = make_dictionary();
  dids.push_back(did_dict);

  // missing "did" & "type" value
  EXPECT_THROW(bringup::parse_dids(m_settings_view, m_context_ref),
               diagnostic_common::config_error);
  // missing "type"
  get<dictionary>(did_dict)["did"] = make_value(0xFF00);
  EXPECT_THROW(bringup::parse_dids(m_settings_view, m_context_ref),
               diagnostic_common::config_error);
  // invalid "type"
  get<dictionary>(did_dict)["type"] = make_value("STATICS");
  EXPECT_THROW(bringup::parse_dids(m_settings_view, m_context_ref),
               diagnostic_common::config_error);
  // missing "data" in "static" did
  get<dictionary>(did_dict)["type"] = make_value("static");
  EXPECT_THROW(bringup::parse_dids(m_settings_view, m_context_ref),
               diagnostic_common::config_error);
  // more than 1 byte data type in data record
  auto did_data = make_array();
  get<dictionary>(did_dict)["data"] = did_data;
  get<array>(did_data).push_back(make_value(0xFFFF));
  EXPECT_THROW(bringup::parse_dids(m_settings_view, m_context_ref),
               diagnostic_common::config_error);
  // data byte larger than max data size
  constexpr size_t MORE_THAN_MAX_DID_DATA_SIZE = uds::bringup::MAX_SIZE_OF_DID_PAYLOAD + 1;
  get<array>(did_data).clear();
  for (size_t i = 0; i < MORE_THAN_MAX_DID_DATA_SIZE; ++i) {
    get<array>(did_data).push_back(make_value(0));
  }
  EXPECT_THROW(bringup::parse_dids(m_settings_view, m_context_ref),
               diagnostic_common::config_error);
  // two same dids added
  get<array>(did_data).clear();
  get<array>(did_data).push_back(make_value(0));
  auto second_did_dict = make_dictionary();
  get<array>(m_settings, "diagnostic/uds/dids").push_back(second_did_dict);
  get<dictionary>(second_did_dict)["did"] = make_value(0xFF00);
  get<dictionary>(second_did_dict)["type"] = make_value("static");
  get<dictionary>(second_did_dict)["data"] = did_data;
  EXPECT_THROW(bringup::parse_dids(m_settings_view, m_context_ref),
               diagnostic_common::config_error);
  // missing read and write service in async did
  get<array>(m_settings, "diagnostic/uds/dids").clear();
  auto async_did = make_dictionary();
  get<array>(m_settings, "diagnostic/uds/dids").push_back(async_did);
  get<dictionary>(async_did)["did"] = make_value(0xFF00);
  get<dictionary>(async_did)["type"] = make_value("async");
  EXPECT_THROW(bringup::parse_dids(m_settings_view, m_context_ref),
               diagnostic_common::config_error);
  // empty write service
  get<dictionary>(async_did)["write_service"] = make_value("");
  EXPECT_THROW(bringup::parse_dids(m_settings_view, m_context_ref),
               diagnostic_common::config_error);
  // empty read service
  get<dictionary>(async_did)["write_service"] = make_value("exmaple1");
  get<dictionary>(async_did)["read_service"] = make_value("");
  EXPECT_THROW(bringup::parse_dids(m_settings_view, m_context_ref),
               diagnostic_common::config_error);
}

TEST_F(UdsConfigParserFixture, shall_parse_sids)
{
  using UdsSid = uds::services::shared::UdsSid;

  const auto sids = bringup::parse_sids(m_settings_view);
  EXPECT_EQ(sids.size(), 11U);

  const auto sid_found = [&sids](UdsSid sid) {
    return std::find_if(sids.begin(), sids.end(), [sid](const auto s) { return sid == s; }) !=
           sids.end();
  };

  EXPECT_TRUE(sid_found(UdsSid::WRITE_DATA_BY_IDENTIFIER_REQ));
  EXPECT_TRUE(sid_found(UdsSid::READ_DATA_BY_IDENTIFIER_REQ));
  EXPECT_TRUE(sid_found(UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ));
  EXPECT_TRUE(sid_found(UdsSid::TESTER_PRESENT_REQ));
  EXPECT_TRUE(sid_found(UdsSid::DIAGNOSTIC_SESSION_CONTROL_REQ));
  EXPECT_TRUE(sid_found(UdsSid::INPUT_OUTPUT_CONTROL_BY_IDENTIFIER_REQ));
  EXPECT_TRUE(sid_found(UdsSid::SECURITY_ACCESS_REQ));
  EXPECT_TRUE(sid_found(UdsSid::ROUTINE_CONTROL_REQ));
  EXPECT_TRUE(sid_found(UdsSid::READ_DTC_INFORMATION_REQ));
  EXPECT_TRUE(sid_found(UdsSid::CLEAR_DIAGNOSTIC_INFORMATION_REQ));
  EXPECT_TRUE(sid_found(UdsSid::CONTROL_DTC_SETTINGS_REQ));
}

TEST_F(UdsConfigParserFixture, parse_sids_shall_throw_on_invalid_sid)
{
  // missing the services definition
  EXPECT_THROW(bringup::parse_sids(m_empty_settings_view), diagnostic_common::config_error);
  // empty services definition
  get<array>(m_settings, "diagnostic/uds/services").clear();
  EXPECT_THROW(bringup::parse_sids(m_settings), diagnostic_common::config_error);
  // missing the sid
  auto sid_entry = make_dictionary();
  get<array>(m_settings, "diagnostic/uds/services").push_back(sid_entry);
  EXPECT_THROW(bringup::parse_sids(m_settings), diagnostic_common::config_error);
  // sid too large
  get<dictionary>(sid_entry)["sid"] = make_value(0xFFFF);
  EXPECT_THROW(bringup::parse_sids(m_settings), diagnostic_common::config_error);
  // sid not supported
  get<dictionary>(sid_entry)["sid"] = make_value(0xFE);
  EXPECT_THROW(bringup::parse_sids(m_settings), diagnostic_common::config_error);
  // sid defined twice
  get<dictionary>(sid_entry)["sid"] = make_value(0x22);
  auto second_sid_entry = make_dictionary();
  get<dictionary>(second_sid_entry)["sid"] = make_value(0x22);
  get<array>(m_settings, "diagnostic/uds/services").push_back(sid_entry);
  EXPECT_THROW(bringup::parse_sids(m_settings), diagnostic_common::config_error);
}

TEST_F(UdsConfigParserFixture, shall_parse_sessions)
{
  const auto sessions = bringup::parse_sessions(m_settings_view);
  // default session
  ASSERT_TRUE(sessions.first);
  EXPECT_EQ(sessions.first->get_id(), 0x01u);
  const auto def_session_tparams = sessions.first->get_timing_params();
  EXPECT_EQ(def_session_tparams.m_default_p2_server_max, std::chrono::milliseconds{1000u});
  EXPECT_EQ(def_session_tparams.m_p2_star_server_max, std::chrono::milliseconds{2000u});
  // non-default sessions
  ASSERT_EQ(sessions.second.size(), 1u);
  EXPECT_EQ(sessions.second[0]->get_id(), 0x03u);
  const auto non_def_sess_tparams = sessions.second[0]->get_timing_params();
  EXPECT_EQ(non_def_sess_tparams.m_default_p2_server_max, std::chrono::milliseconds{500u});
  EXPECT_EQ(non_def_sess_tparams.m_p2_star_server_max, std::chrono::milliseconds{1000u});
}

TEST_F(UdsConfigParserFixture, parse_sessions_shall_throw_on_invalid_data)
{
  // missing session data - empty file
  EXPECT_THROW(bringup::parse_sessions(m_empty_settings_view), diagnostic_common::config_error);
  // missing session_id
  get<array>(m_settings, "diagnostic/uds/sessions").clear();
  EXPECT_THROW(bringup::parse_sessions(m_settings), diagnostic_common::config_error);
  // missing p2
  auto def_session = make_dictionary();
  get<array>(m_settings, "diagnostic/uds/sessions").push_back(def_session);
  get<dictionary>(def_session)["id"] = make_value(1);
  EXPECT_THROW(bringup::parse_sessions(m_settings), diagnostic_common::config_error);
  // missing p2*
  get<dictionary>(def_session)["p2"] = make_value(1000);
  EXPECT_THROW(bringup::parse_sessions(m_settings), diagnostic_common::config_error);
  // too large id
  get<dictionary>(def_session)["p2_star"] = make_value(2000);
  get<dictionary>(def_session)["id"] = make_value(0xFFFF);
  EXPECT_THROW(bringup::parse_sessions(m_settings), diagnostic_common::config_error);
  // invalid p2 value
  get<dictionary>(def_session)["p2"] = make_value(-1);
  get<dictionary>(def_session)["id"] = make_value(0x01);
  EXPECT_THROW(bringup::parse_sessions(m_settings), diagnostic_common::config_error);
  // invalid p2* value
  get<dictionary>(def_session)["p2"] = make_value(1000);
  get<dictionary>(def_session)["p2_star"] = make_value(-1);
  EXPECT_THROW(bringup::parse_sessions(m_settings), diagnostic_common::config_error);
  // two default sessions defined
  get<dictionary>(def_session)["p2_star"] = make_value(2000);
  auto def_session_2 = make_dictionary();
  get<dictionary>(def_session_2)["id"] = make_value(1);
  get<dictionary>(def_session_2)["p2"] = make_value(1000);
  get<dictionary>(def_session_2)["p2_star"] = make_value(2000);
  get<array>(m_settings, "diagnostic/uds/sessions").push_back(def_session_2);
  EXPECT_THROW(bringup::parse_sessions(m_settings), diagnostic_common::config_error);
  // no default session defined
  get<array>(m_settings, "diagnostic/uds/sessions").clear();
  auto non_def_session = make_dictionary();
  get<dictionary>(non_def_session)["id"] = make_value(3);
  get<dictionary>(non_def_session)["p2"] = make_value(1000);
  get<dictionary>(non_def_session)["p2_star"] = make_value(2000);
  get<array>(m_settings, "diagnostic/uds/sessions").push_back(non_def_session);
  EXPECT_THROW(bringup::parse_sessions(m_settings), diagnostic_common::config_error);
  // two same non default session IDs
  get<array>(m_settings, "diagnostic/uds/sessions").push_back(def_session);
  auto non_def_session_2 = make_dictionary();
  get<dictionary>(non_def_session_2)["id"] = make_value(3);
  get<dictionary>(non_def_session_2)["p2"] = make_value(1000);
  get<dictionary>(non_def_session_2)["p2_star"] = make_value(2000);
  get<array>(m_settings, "diagnostic/uds/sessions").push_back(non_def_session_2);
  EXPECT_THROW(bringup::parse_sessions(m_settings), diagnostic_common::config_error);
}

TEST_F(UdsConfigParserFixture, shall_parse_security_access_service)
{
  auto security_access_src_cfg = bringup::parse_security_access_service(m_settings_view);

  // service names
  EXPECT_EQ(security_access_src_cfg.m_get_seed_service_name, "/get_seed");
  EXPECT_EQ(security_access_src_cfg.m_compare_key_service_name, "/compare_key");
  EXPECT_EQ(security_access_src_cfg.m_level_availability_service_name,
            "/is_security_level_available");
  // publisher topic names
  EXPECT_EQ(security_access_src_cfg.m_level_unlocking_result_topic,
            "/security_level_unlocking_result");
  // options
  EXPECT_TRUE(security_access_src_cfg.m_reset_level_timer_enabled);
  EXPECT_EQ(security_access_src_cfg.m_reset_level_timeout_val_ms, 1000U);
  EXPECT_FALSE(security_access_src_cfg.m_static_seed);
  // enabled security levels
  EXPECT_EQ(security_access_src_cfg.m_enabled_levels.size(), 3U);
  EXPECT_EQ(security_access_src_cfg.m_enabled_levels.count(0x01), 1U);
  EXPECT_EQ(security_access_src_cfg.m_enabled_levels.count(0x03), 1U);
  EXPECT_EQ(security_access_src_cfg.m_enabled_levels.count(0x7D), 1U);
}

TEST_F(UdsConfigParserFixture, parse_security_access_service_shall_throw_on_invalid_data)
{
  EXPECT_NO_THROW(bringup::parse_security_access_service(m_empty_settings_view));

  // no sids
  auto & services_list = get<array>(m_settings, "diagnostic/uds/services");
  services_list.clear();
  EXPECT_NO_THROW(bringup::parse_security_access_service(m_settings_view));
  // other sid
  auto security_access_sid_cfg = make_dictionary();
  services_list.push_back(security_access_sid_cfg);
  get<dictionary>(security_access_sid_cfg)["sid"] = make_value(0xFF);
  EXPECT_NO_THROW(bringup::parse_security_access_service(m_settings_view));
  // missing service names
  get<dictionary>(security_access_sid_cfg)["sid"] = make_value(0x27);
  EXPECT_THROW(bringup::parse_security_access_service(m_settings_view),
               diagnostic_common::config_error);
  get<dictionary>(security_access_sid_cfg)["get_seed_service"] = make_value("/get_seed");
  EXPECT_THROW(bringup::parse_security_access_service(m_settings_view),
               diagnostic_common::config_error);
  get<dictionary>(security_access_sid_cfg)["compare_key_service"] = make_value("/compare_key");
  EXPECT_THROW(bringup::parse_security_access_service(m_settings_view),
               diagnostic_common::config_error);
  // missing publisher topic name
  get<dictionary>(security_access_sid_cfg)["level_availability_service"] =
    make_value("/is_security_level_available");
  EXPECT_THROW(bringup::parse_security_access_service(m_settings_view),
               diagnostic_common::config_error);
  // missing static seed
  get<dictionary>(security_access_sid_cfg)["level_unlocking_result_topic"] =
    make_value("/security_level_unlocking_result");
  EXPECT_THROW(bringup::parse_security_access_service(m_settings_view),
               diagnostic_common::config_error);
  get<dictionary>(security_access_sid_cfg)["static_seed"] = make_value(false);
  EXPECT_NO_THROW(bringup::parse_security_access_service(m_settings_view));
}
