// Copyright 2022 Apex.AI, Inc
// All rights reserved.

#include <chrono>
#include <cstdint>
#include <memory>  // for std::shared_ptr, std::make_shared, std::make_unique
#include <utility>  // for std::move

#include "diagnostic_common/node_context.hpp"  // for NodeContextPtr
#include "fixtures/diagnostic_test_fixture.hpp"
#include "gtest/gtest.h"  // for TEST, EXPECT_*
#include "mocks/mock_did_pool.hpp"  // for MockDidPool
#include "mocks/mock_did_reader.hpp"  // for MockDidReader
#include "mocks/mock_response_publisher.hpp"  // for MockResponsePublisher
#include "mocks/mock_timer_base.hpp"
#include "shared/service_test_fixture.hpp"
#include "uds_server/services/did/read_by_did_periodically_service.hpp"  // for ReadByDidPeriodicallyService
#include "uds_server/services/shared/response_code.hpp"  // for UdsResponseCode
#include "uds_server/services/shared/sid.hpp"  // for UdsSid

namespace uds = apex::uds_server;
namespace did = uds::services::did;
namespace diagnostic_common = apex::diagnostic_common;

// msgs
using UdsAddress = uds_msgs::msg::UdsAddress;
// uds
using UdsSid = uds::services::shared::UdsSid;
using ReadByDidPeriodicallyService = did::ReadByDidPeriodicallyService;
using ReadByDidDataPeriodicallyConfig =
  apex::uds_server::services::did::ReadByDidDataPeriodicallyConfig;
using UdsResponseCode = uds::services::shared::UdsResponseCode;
using AccessStatus = uds::services::shared::AccessStatus;
// mocks
using MockDidReader = did::MockDidReader;
using MockDidPool = did::MockDidPool;
using MockResponsePublisher = uds::com::MockResponsePublisher;
using MockTimerBase = diagnostic_common::timer::MockTimerBase;
// gtest
using ::testing::_;

namespace
{
constexpr uint16_t DID_LOW_BYTE = 0x00FFU;
constexpr uint16_t TEST_DID_SUPPORTED_1 =
  0xF201U;  // This service only support the range of 0xF200 to 0xF2FF
constexpr uint16_t TEST_DID_SUPPORTED_2 = 0xF202U;
constexpr uint16_t TEST_DID_NOT_SUPPORTED = 0xF205U;
constexpr uint32_t TIMER_1{0U};
constexpr uint8_t TEST_DID_SUPPORTED_1_RESPONSE = 0x51;
constexpr uint8_t TEST_DID_SUPPORTED_2_RESPONSE = 0x52;

struct ReadByDidPeriodicallyNodeFixture : shared::ServiceTestFixture<ReadByDidPeriodicallyService>
{
  ReadByDidPeriodicallyNodeFixture()
  : m_context{std::make_shared<diagnostic_common::NodeContext>("ReadByDidPeriodicallyTestNode")},
    m_mock_rsp_pub{std::make_shared<MockResponsePublisher>()},
    m_mock_did_pool{std::make_shared<did::MockDidPool>()},
    m_timer{std::make_shared<MockTimerBase>()},
    m_config{
      std::chrono::milliseconds(25), std::chrono::milliseconds(50), std::chrono::milliseconds(100)}
  {
    set_sut(std::make_unique<ReadByDidPeriodicallyService>(
      m_mock_rsp_pub.get(), m_mock_did_pool, m_context->as_ref(), m_timer, m_config));
  }

  diagnostic_common::NodeContextPtr m_context;
  std::shared_ptr<MockResponsePublisher> m_mock_rsp_pub;
  std::shared_ptr<did::MockDidPool> m_mock_did_pool;
  std::shared_ptr<MockTimerBase> m_timer;
  did::DataRecord m_did_response_buffer;
  ReadByDidDataPeriodicallyConfig m_config;
};

}  // namespace

TEST_F(ReadByDidPeriodicallyNodeFixture, shall_pub_nrc_on_invalid_msg_size)
{
  const uds::services::shared::NegativeResponse expected_response{
    m_req.request_id,
    shared::DEFAULT_TESTER_ADDRESS,
    UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ,
    UdsResponseCode::INCORRECT_MESSAGE_LENGTH_OR_INVALID_FORMAT};
  // create request
  set_req_msg([](auto & payload) { payload.push_back(0x2AU); });  // no DID in data

  // expected result
  EXPECT_CALL(*m_mock_rsp_pub, publish_negative_response(expected_response)).Times(1);
  // call sut
  call_sut();
}

TEST_F(ReadByDidPeriodicallyNodeFixture, shall_pub_nrc_on_no_did_supported)
{
  const uds::services::shared::NegativeResponse expected_response{
    m_req.request_id,
    shared::DEFAULT_TESTER_ADDRESS,
    UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ,
    UdsResponseCode::REQUEST_OUT_OF_RANGE};

  // create request
  set_req_msg([](auto & payload) {
    payload.push_back(0x2AU);
    payload.push_back(0x01U);  // Set Transmission mode (Slow)
    payload.push_back(static_cast<uint8_t>(TEST_DID_NOT_SUPPORTED & DID_LOW_BYTE));
  });

  // setup mocks
  auto find_did_result = std::make_pair(AccessStatus::RESOURCE_NOT_FOUND, nullptr);

  EXPECT_CALL(
    *m_mock_did_pool,
    find_did_reader(TEST_DID_NOT_SUPPORTED, _, UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ))
    .WillOnce(::testing::Return(find_did_result));

  // // expected result
  EXPECT_CALL(*m_mock_rsp_pub, publish_negative_response(expected_response)).Times(1);

  // call sut
  call_sut();
}

TEST_F(ReadByDidPeriodicallyNodeFixture, shall_pub_nrc_on_invalid_access_reserved_bottom)
{
  const uds::services::shared::NegativeResponse expected_response{
    m_req.request_id,
    shared::DEFAULT_TESTER_ADDRESS,
    UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ,
    UdsResponseCode::REQUEST_OUT_OF_RANGE};
  // create request
  set_req_msg([](auto & payload) {
    payload.push_back(0x2AU);
    payload.push_back(0x00U);  // Set Transmission mode (RESERVED_BOT)
    payload.push_back(TEST_DID_SUPPORTED_1 & DID_LOW_BYTE);
  });

  // expected result
  EXPECT_CALL(*m_mock_rsp_pub, publish_negative_response(expected_response)).Times(1);

  // call sut
  call_sut();
}

TEST_F(ReadByDidPeriodicallyNodeFixture, shall_pub_nrc_on_invalid_access_missing_payload)
{
  const uds::services::shared::NegativeResponse expected_response{
    m_req.request_id,
    shared::DEFAULT_TESTER_ADDRESS,
    UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ,
    UdsResponseCode::REQUEST_OUT_OF_RANGE};
  // create request
  set_req_msg([](auto & payload) {
    payload.push_back(0x2AU);
    payload.push_back(0x01U);  // Set Transmission mode (SLOW)
  });

  // expected result
  EXPECT_CALL(*m_mock_rsp_pub, publish_negative_response(expected_response)).Times(1);

  // call sut
  call_sut();
}

TEST_F(ReadByDidPeriodicallyNodeFixture, shall_pub_pos_rsp)
{
  // create request
  set_req_msg([](auto & payload) {
    payload.push_back(0x2AU);
    payload.push_back(0x01U);  // Set Transmission mode (Slow)
    payload.push_back(static_cast<uint8_t>(TEST_DID_SUPPORTED_1 & DID_LOW_BYTE));
  });

  // setup did pool
  auto first_did_reader = std::make_shared<MockDidReader>(TEST_DID_SUPPORTED_1);
  auto find_first_did_result = std::make_pair(AccessStatus::ALLOWED, first_did_reader);

  EXPECT_CALL(
    *m_mock_did_pool,
    find_did_reader(TEST_DID_SUPPORTED_1, _, UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ))
    .Times(1)
    .WillRepeatedly(::testing::Return(find_first_did_result));

  // //expected result
  EXPECT_CALL(*m_mock_rsp_pub.get(), publish_positive_response(_))
    .WillOnce(::testing::Invoke([&](const auto & positive_response) {
      EXPECT_EQ(positive_response.tester_address, m_req.tester_address);
      EXPECT_EQ(positive_response.request_id, m_req.request_id);
      positive_response.fill_payload_cb(m_buffer);

      // only one did will be returned.
      ASSERT_EQ(m_buffer.size(), 1U);
      EXPECT_EQ(m_buffer[0],
                static_cast<uint8_t>(UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_POS_RESP));
    }));

  std::function<void(uint32_t)> periodic_read_callback;
  EXPECT_CALL(*m_timer.get(), add_timeout(_, _, _))
    .WillOnce(
      ::testing::DoAll(::testing::SaveArg<1>(&periodic_read_callback), ::testing::Return(TIMER_1)));

  EXPECT_CALL(*first_did_reader, is_available()).WillRepeatedly(::testing::Return(true));

  uds_msgs::msg::UdsTransportMessage::BorrowedType::_payload_type buffer_1;
  call_sut();

  EXPECT_CALL(*first_did_reader, read(_)).WillOnce(::testing::Invoke([&](auto request_id) {
    m_did_response_buffer.clear();
    m_did_response_buffer.push_back(TEST_DID_SUPPORTED_1_RESPONSE);
    m_sut->notify_about_read_success(request_id, TEST_DID_SUPPORTED_1, m_did_response_buffer);
  }));


  EXPECT_CALL(*m_mock_rsp_pub.get(), publish_positive_response(_))
    .WillOnce(::testing::Invoke([&](const auto & positive_response) {
      EXPECT_EQ(positive_response.tester_address, m_req.tester_address);
      EXPECT_EQ(positive_response.request_id, m_req.request_id);
      positive_response.fill_payload_cb(buffer_1);

      // only one did will be returned.
      ASSERT_EQ(buffer_1.size(), 2U);
      EXPECT_EQ(buffer_1[0], static_cast<uint8_t>(TEST_DID_SUPPORTED_1 & DID_LOW_BYTE));
      EXPECT_EQ(buffer_1[1], static_cast<uint8_t>(TEST_DID_SUPPORTED_1_RESPONSE));
    }));

  periodic_read_callback(TIMER_1);
}
TEST_F(ReadByDidPeriodicallyNodeFixture, shall_pub_pos_rsp_multiple)
{
  // create request
  set_req_msg([](auto & payload) {
    payload.push_back(0x2AU);
    payload.push_back(0x01U);  // Set Transmission mode (Slow)
    payload.push_back(static_cast<uint8_t>(TEST_DID_SUPPORTED_1 & DID_LOW_BYTE));
    payload.push_back(static_cast<uint8_t>(TEST_DID_SUPPORTED_2 & DID_LOW_BYTE));
  });

  // setup did pool
  auto first_did_reader = std::make_shared<MockDidReader>(TEST_DID_SUPPORTED_1);

  auto find_first_did_result = std::make_pair(AccessStatus::ALLOWED, first_did_reader);

  // setup did pool
  auto second_did_reader = std::make_shared<MockDidReader>(TEST_DID_SUPPORTED_2);
  auto find_second_did_result = std::make_pair(AccessStatus::ALLOWED, second_did_reader);

  {
    // testing::InSequence Seq;
    EXPECT_CALL(
      *m_mock_did_pool,
      find_did_reader(TEST_DID_SUPPORTED_1, _, UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ))
      .WillRepeatedly(::testing::Return(find_first_did_result));

    EXPECT_CALL(
      *m_mock_did_pool,
      find_did_reader(TEST_DID_SUPPORTED_2, _, UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ))
      .WillRepeatedly(::testing::Return(find_second_did_result));

    // //expected result
    EXPECT_CALL(*m_mock_rsp_pub.get(), publish_positive_response(_))
      .WillOnce(::testing::Invoke([&](const auto & positive_response) {
        EXPECT_EQ(positive_response.tester_address, m_req.tester_address);
        EXPECT_EQ(positive_response.request_id, m_req.request_id);
        positive_response.fill_payload_cb(m_buffer);

        // only one did will be returned.
        ASSERT_EQ(m_buffer.size(), 1U);
        EXPECT_EQ(m_buffer[0],
                  static_cast<uint8_t>(UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_POS_RESP));
      }));

    // EXPECT_CALL(*m_timer.get(), add_timeout(_, _, _)).WillOnce(::testing::Invoke(timeout_p2));
    std::function<void(uint32_t)> periodic_read_callback;
    EXPECT_CALL(*m_timer.get(), add_timeout(_, _, _))
      .WillOnce(::testing::DoAll(::testing::SaveArg<1>(&periodic_read_callback),
                                 ::testing::Return(TIMER_1)));

    EXPECT_CALL(*first_did_reader, is_available()).WillRepeatedly(::testing::Return(true));
    EXPECT_CALL(*first_did_reader, read(_)).WillOnce(::testing::Invoke([&](auto request_id) {
      const auto value = did::DataRecord{TEST_DID_SUPPORTED_1_RESPONSE};
      m_sut->notify_about_read_success(request_id, TEST_DID_SUPPORTED_1, value);
    }));

    EXPECT_CALL(*second_did_reader, is_available()).WillRepeatedly(::testing::Return(true));
    EXPECT_CALL(*second_did_reader, read(_)).WillOnce(::testing::Invoke([&](auto request_id) {
      const auto value = did::DataRecord{TEST_DID_SUPPORTED_2_RESPONSE};
      m_sut->notify_about_read_success(request_id, TEST_DID_SUPPORTED_2, value);
    }));

    call_sut();

    EXPECT_CALL(*m_mock_rsp_pub.get(), publish_positive_response(_))
      .WillOnce(::testing::Invoke([&](const auto & positive_response) {
        EXPECT_EQ(positive_response.tester_address, m_req.tester_address);
        EXPECT_EQ(positive_response.request_id, m_req.request_id);
        uds_msgs::msg::UdsTransportMessage::BorrowedType::_payload_type buffer_2;
        positive_response.fill_payload_cb(buffer_2);
        // Only one did will be returned.
        ASSERT_EQ(buffer_2.size(), 2);
        EXPECT_EQ(buffer_2[0], static_cast<uint8_t>(TEST_DID_SUPPORTED_2 & DID_LOW_BYTE));
        EXPECT_EQ(buffer_2[1], static_cast<uint8_t>(TEST_DID_SUPPORTED_2_RESPONSE));
      }))
      .WillOnce(::testing::Invoke([&](const auto & positive_response) {
        EXPECT_EQ(positive_response.tester_address, m_req.tester_address);
        EXPECT_EQ(positive_response.request_id, m_req.request_id);

        uds_msgs::msg::UdsTransportMessage::BorrowedType::_payload_type buffer_1;
        positive_response.fill_payload_cb(buffer_1);

        // Only one did will be returned.
        ASSERT_EQ(buffer_1.size(), 2);
        EXPECT_EQ(buffer_1[0], static_cast<uint8_t>(TEST_DID_SUPPORTED_1 & DID_LOW_BYTE));
        EXPECT_EQ(buffer_1[1], static_cast<uint8_t>(TEST_DID_SUPPORTED_1_RESPONSE));
      }));

    periodic_read_callback(TIMER_1);
  }
}
