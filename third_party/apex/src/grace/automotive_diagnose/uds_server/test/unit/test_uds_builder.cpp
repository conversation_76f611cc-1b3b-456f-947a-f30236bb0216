// Copyright 2022 Apex.AI, Inc
// All rights reserved.
#include <unordered_map>  // for std::unordered_map
#include <utility>  // for std::move
#include <vector>  // for std::vector<>

#include "apex_init/apex_init.hpp"
#include "diagnostic_common/config_error.hpp"
#include "diagnostic_common/node_context.hpp"
#include "fixtures/diagnostic_test_node_fixture.hpp"
#include "gtest/gtest.h"  // for TEST_F, ASSERT_*, EXPECT_*
#include "rclcpp/rclcpp.hpp"
#include "settings/construct/types.hpp"
#include "settings/uds_server_settings.hpp"
#include "uds_server/bringup/uds_builder.hpp"

namespace uds = apex::uds_server;
namespace diagnostic_common = apex::diagnostic_common;

namespace
{
struct UdsBuilderFixture : diagnostic_common::testing::DiagnosticTestNodeFixture
{
  UdsBuilderFixture()
  : DiagnosticTestNodeFixture{"UdsBuilderFixture"},
    m_settings{apex::settings::generated::uds::create()},
    m_settings_view{m_settings},
    m_empty_settings{},
    m_empty_settings_view{m_empty_settings}
  {
  }

  apex::settings::construct::dictionary m_settings;
  apex::settings::inspect::dictionary_view m_settings_view;
  apex::settings::construct::dictionary m_empty_settings;
  apex::settings::inspect::dictionary_view m_empty_settings_view;
};
}  // namespace

TEST_F(UdsBuilderFixture, shall_build_all_executables)
{
  // There is 1 exec for UDS Server, 1 for EnvMonitor, 10 Dids (Read + Write),
  // 3 Security Access, 1 for timer_service, 3 Routines Service, 1 security level controller,
  // 1 Read DTC, 1 Clear DTC, 1 Control DTD, 1 IO Control
  constexpr size_t NUM_OF_EXECS{26U};

  auto execs = uds::bringup::build(m_settings_view, m_context);
  EXPECT_EQ(execs.size(), NUM_OF_EXECS);
  // validate correct exec types number
  using ExecType = apex::uds_server::bringup::ExecType;
  std::unordered_map<ExecType, size_t> exec_counter;
  std::unordered_map<ExecType, size_t> expected_map{{ExecType::SERVER, 1U},
                                                    {ExecType::MONITOR, 1U},
                                                    {ExecType::DID, 12U},
                                                    {ExecType::SECURITY_ACCESS, 3U},
                                                    {ExecType::TIMER, 1U},
                                                    {ExecType::ROUTINE, 3U},
                                                    {ExecType::SECURITY_LEVEL_CTRL, 1U},
                                                    {ExecType::DTC, 3U}};

  // count
  for (const auto & ex : execs) {
    exec_counter[ex.m_type] += 1;
  }
  // verify
  for (const auto & expected_pair : expected_map) {
    EXPECT_EQ(exec_counter[expected_pair.first], expected_pair.second);
  }
}

TEST_F(UdsBuilderFixture, shall_throw_on_invalid_config)
{
  EXPECT_THROW(uds::bringup::build(m_empty_settings_view, m_context),
               diagnostic_common::config_error);
}
