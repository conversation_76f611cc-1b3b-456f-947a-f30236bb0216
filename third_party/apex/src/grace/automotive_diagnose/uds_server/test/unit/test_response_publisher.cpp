// Copyright 2022 Apex.AI, Inc
// All rights reserved.

#include <gtest/gtest.h>

#include <memory>  // for std::shared_ptr, std::make_shared, std::make_unique

#include "diagnostic_common/node_context.hpp"  // for NodeContextPtr
#include "fixtures/diagnostic_test_pub_fixture.hpp"
#include "uds_server/com/response_publisher.hpp"  // for ResponsePublisher

#include "uds_msgs/msg/uds_transport_message.hpp"

namespace uds = apex::uds_server;
namespace diagnostic_common = apex::diagnostic_common;

using UdsTransportMessage = uds_msgs::msg::UdsTransportMessage;
using UdsAddress = uds_msgs::msg::UdsAddress;

namespace
{
const UdsAddress TESTER_ADDRESS =
  UdsAddress::create_msg(UdsAddress::UdsAddressTypeEnum::PHYSICAL, 0xFF);
const UdsAddress ECU_ADDRESS =
  UdsAddress::create_msg(UdsAddress::UdsAddressTypeEnum::PHYSICAL, 0x2233U);
constexpr uint64_t REQUEST_ID = 1U;
const auto PUB_TOPIC = "response_topic";

using ResponsePublisherBaseFixture =
  diagnostic_common::testing::DiagnosticTestSinglePubNodeFixture<uds::com::ResponsePublisher,
                                                                 UdsTransportMessage>;

struct ResponsePublisherFixture : ResponsePublisherBaseFixture
{
  ResponsePublisherFixture() : ResponsePublisherBaseFixture{"ResponsePublisherFixture", PUB_TOPIC}
  {
    set_sut(std::make_unique<uds::com::ResponsePublisher>(
      m_context->as_ref(), PUB_TOPIC, ECU_ADDRESS.address));
  }
};
}  // namespace

TEST_F(ResponsePublisherFixture, shall_publish_positive_response)
{
  const uds::services::shared::PositiveResponse response{
    REQUEST_ID, TESTER_ADDRESS, [](auto & payload) {
      payload.push_back(0x00U);
      payload.push_back(0x01U);
      payload.push_back(0x02U);
    }};
  // publish message and wait for reception
  m_sut->publish_positive_response(response);
  wait_for_message();
  // assert message data
  const auto msgs{m_subscriber->take()};
  ASSERT_EQ(msgs.size(), 1U);
  ASSERT_TRUE(msgs.front().info().valid());
  const auto & msg = msgs.front().data();
  ASSERT_EQ(msg.tester_address, TESTER_ADDRESS);
  ASSERT_EQ(msg.ecu_address, ECU_ADDRESS);
  ASSERT_EQ(msg.payload.size(), 3U);
  EXPECT_EQ(msg.payload[0], 0x00U);
  EXPECT_EQ(msg.payload[1], 0x01U);
  EXPECT_EQ(msg.payload[2], 0x02U);
}

TEST_F(ResponsePublisherFixture, shall_publish_negative_response)
{
  using uds::services::shared::UdsResponseCode;
  using uds::services::shared::UdsSid;

  const uds::services::shared::NegativeResponse response{REQUEST_ID,
                                                         TESTER_ADDRESS,
                                                         UdsSid::DIAGNOSTIC_SESSION_CONTROL_REQ,
                                                         UdsResponseCode::BUSY_REPEAT_REQUEST};
  // publish message and wait for reception
  m_sut->publish_negative_response(response);
  wait_for_message();
  // assert message data
  const auto msgs{m_subscriber->take()};
  ASSERT_EQ(msgs.size(), 1U);
  ASSERT_TRUE(msgs.front().info().valid());
  const auto & msg = msgs.front().data();
  ASSERT_EQ(msg.tester_address, TESTER_ADDRESS);
  ASSERT_EQ(msg.ecu_address, ECU_ADDRESS);
  ASSERT_EQ(msg.payload.size(), 3U);
  EXPECT_EQ(msg.payload[0], static_cast<uint8_t>(UdsSid::NEGATIVE_RESPONSE));
  EXPECT_EQ(msg.payload[1], static_cast<uint8_t>(UdsSid::DIAGNOSTIC_SESSION_CONTROL_REQ));
  EXPECT_EQ(msg.payload[2], static_cast<uint8_t>(UdsResponseCode::BUSY_REPEAT_REQUEST));
}
