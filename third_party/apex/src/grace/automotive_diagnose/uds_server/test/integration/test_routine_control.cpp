// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include <utility>  // for move
#include <vector>  // for vector

#include "gtest/gtest.h"
#include "rclcpp/node.hpp"  // for PollingService, PollingSubscription, Publisher
#include "shared/uds_server_integration_fixture.hpp"

#include "uds_msgs/msg/uds_transport_message.hpp"
#include "uds_msgs/srv/uds_routine_control.hpp"

namespace
{

using UdsRoutineControl = uds_msgs::srv::UdsRoutineControl;
using ControlStatus = uds_msgs::ControlStatus;

using UdsRoutineControlSrvClientFixture =
  apex::diagnostic_common::testing::DiagnosticTestSrvClientFixtureBase<UdsRoutineControl>;

const auto SERVICE_NAME = "/uds/routine_0001_0002";

class RoutineControlFixture : public shared::UdsServerIntegrationFixture,
                              public UdsRoutineControlSrvClientFixture
{
public:
  RoutineControlFixture() : UdsRoutineControlSrvClientFixture{m_context->as_ref(), SERVICE_NAME} {}

  void SetUp() override
  {
    apex::diagnostic_common::testing::DiagnosticTestFixture::SetUp();
    start_runner();
  }

  void send_response(const ControlStatus control_status)
  {
    const auto requests{m_service->take_request()};
    ASSERT_EQ(requests.size(), 1U);
    const auto & req = requests.front();
    ASSERT_TRUE(req.info().valid());
    auto loaned_response{borrow_loaned_response()};
    auto & data = loaned_response.get();
    data.control_type = req->control_type;
    data.status = control_status;
    data.routine_id = req.data().routine_id;
    UdsRoutineControlSrvClientFixture::send_response(req, std::move(loaned_response));
  }
};

}  // namespace


TEST_F(RoutineControlFixture, routine_control_positive_response)
{
  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);
  // send routine start
  std::vector<uint8_t> routine_options{};
  routine_control(0x001U, 0x01U, std::move(routine_options));
  // wait for service request
  wait_for_request();
  // send service response
  send_response(ControlStatus::OK);
  // get response and assert that it is positive
  ASSERT_TRUE(assert_positive_response(shared::Sid::ROUTINE_CONTROL_POS_RESP));
}

TEST_F(RoutineControlFixture, routine_control_negative_response_from_service)
{
  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);
  // send routine start
  std::vector<uint8_t> routine_options{};
  routine_control(0x001U, 0x01U, std::move(routine_options));
  // wait for service request
  wait_for_request();
  // send service response
  send_response(ControlStatus::ERROR);
  // get response and assert NRC General Programming Failure
  ASSERT_TRUE(assert_negative_response(shared::Sid::ROUTINE_CONTROL_REQ,
                                       shared::ResponseCode::GENERAL_PROGRAMMING_FAILURE));
}

TEST_F(RoutineControlFixture, routine_control_negative_response_on_invalid_routine_id)
{
  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);
  // send routine start
  std::vector<uint8_t> routine_options{};
  routine_control(0xFFFF, 0x01U, std::move(routine_options));
  // get response and assert NRC Request out of range
  ASSERT_TRUE(assert_negative_response(shared::Sid::ROUTINE_CONTROL_REQ,
                                       shared::ResponseCode::REQUEST_OUT_OF_RANGE));
}
