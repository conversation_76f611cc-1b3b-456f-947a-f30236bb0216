/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.

#include <memory>
#include <regex>
#include <string>
#include <utility>
#include <vector>

#include "gmock/gmock.h"
#include "grace/automotive_diagnose/uds_server/test/mocks/negative_response_matcher.hpp"
#include "grace/automotive_diagnose/uds_server/test/shared/uds_server_integration_fixture.hpp"
#include "gtest/gtest.h"
#include "rclcpp/dynamic_waitset/waitset.hpp"

#include "uds_msgs/srv/uds_input_output_control.hpp"

namespace
{

using ::shared::UdsServerIntegrationFixture;
using ::test::IsNegativeResponseWithCode;
using ::testing::ElementsAre;
using ::uds_msgs::ControlParameter;

using ControlStatus = ::uds_msgs::InputOutputControlStatus;
using SID = ::apex::uds_server::services::shared::UdsSid;
using NRC = ::apex::uds_server::services::shared::UdsResponseCode;

using PollingServiceFixture =
  ::apex::diagnostic_common::testing::DiagnosticTestSrvClientFixtureBase<
    uds_msgs::srv::UdsInputOutputControl>;

/// These fields are configured in the settings. The integration tests are using the settings
/// from the target //grace/automotive_diagnose/uds_server/test:test_settings
constexpr auto SERVICE_NAME = "/uds/ioctrl_0001_0002";
constexpr uint16_t CONTROLLABLE_DID = 0x0043;
constexpr auto REQUEST_TIMEOUT = std::chrono::milliseconds{500};

class InputOutputControlFixture : public UdsServerIntegrationFixture, public PollingServiceFixture
{
public:
  InputOutputControlFixture()
  : PollingServiceFixture{m_context->as_ref(), SERVICE_NAME},
    log_subscription{m_context->node().create_polling_subscription<apex_msgs::msg::LogMessage>(
      "/logging", rclcpp::QoS{10})}
  {
    log_subscription->wait_for_matched(1);
    log_ws.add(log_subscription);
  }

  void SetUp() override
  {
    apex::diagnostic_common::testing::DiagnosticTestFixture::SetUp();
    UdsServerIntegrationFixture::start_runner();
  }

  void expect_warning_log_with_message(
    const std::string & pattern,
    std::chrono::milliseconds timeout_duration = std::chrono::seconds{1})
  {
    auto start_time = std::chrono::steady_clock::now();
    bool found_entry = false;
    const auto regex_pattern = std::regex{".*" + pattern + ".*"};
    std::vector<std::string> messages;

    while (std::chrono::steady_clock::now() - start_time < timeout_duration) {
      log_ws.wait(std::chrono::milliseconds(100));
      for (const auto & log_entry : log_subscription->take()) {
        if (!log_entry.info().valid() ||
            log_entry->level != static_cast<uint8_t>(apex::logging::LogLevel::WARN)) {
          continue;
        }

        const char * text = log_entry->text.c_str();
        if (std::regex_match(text, regex_pattern)) {
          found_entry = true;
          break;
        } else {
          messages.push_back(text);
        }
      }
    }

    auto error_message = [&]() -> std::string {
      if (messages.empty()) {
        return "No log warnings caught";
      }

      std::ostringstream error_message;
      error_message << "No log warnings matching pattern: " << pattern << "\n"
                    << "Warnings caught:\n";
      for (const auto & msg : messages) {
        error_message << " - " << msg << "\n";
      }

      return error_message.str();
    };

    EXPECT_TRUE(found_entry) << error_message();
  }

  std::shared_ptr<rclcpp::PollingSubscription<apex_msgs::msg::LogMessage>> log_subscription;
  rclcpp::dynamic_waitset::Waitset log_ws;
};

}  // namespace

TEST_F(InputOutputControlFixture, server_responds_positively)
{
  // When an Input Output Control Request is sent.
  send_io_control_req(
    CONTROLLABLE_DID, ControlParameter::SHORT_TERM_ADJUSTMENT, {0xDE, 0xAD, 0xBE, 0xEF});

  // Then In the Server Side
  {
    // The request is received
    wait_for_request();

    const auto requests{m_service->take_request()};
    ASSERT_EQ(requests.size(), 1U);

    const auto & req = requests.front();
    ASSERT_TRUE(req.info().valid());
    EXPECT_EQ(req->did, CONTROLLABLE_DID);
    EXPECT_EQ(req->control_parameter, ControlParameter::SHORT_TERM_ADJUSTMENT);
    EXPECT_THAT(req->control_state, ElementsAre(0xDE, 0xAD, 0xBE, 0xEF));

    // When it is responded.
    auto response = borrow_loaned_response();
    response->did = req->did;
    response->control_parameter = req->control_parameter;
    PollingServiceFixture::send_response(req, std::move(response));
  }

  // Then the response is received by the UDS Server subscriber.
  ASSERT_TRUE(assert_positive_response(SID::INPUT_OUTPUT_CONTROL_BY_IDENTIFIER_POS_RESP));
}

TEST_F(InputOutputControlFixture, server_responds_negatively)
{
  // When an Input Output Control Request is sent.
  send_io_control_req(
    CONTROLLABLE_DID, ControlParameter::SHORT_TERM_ADJUSTMENT, {0xDE, 0xAD, 0xBE, 0xEF});

  // Then In the Server Side
  {
    // The request is received
    wait_for_request();
    const auto requests = m_service->take_request();
    ASSERT_EQ(requests.size(), 1U);
    const auto request = requests.front();

    // But an error is sent instead.
    auto response = borrow_loaned_response();
    response->did = request->did;
    response->control_parameter = request->control_parameter;
    response->status = ControlStatus::ERROR;

    PollingServiceFixture::send_response(request, std::move(response));
  }

  // Then the response is received by the UDS Server subscriber.
  ASSERT_TRUE(
    assert_negative_response(SID::INPUT_OUTPUT_CONTROL_BY_IDENTIFIER_REQ, NRC::GENERAL_REJECT));
}

TEST_F(InputOutputControlFixture, requests_are_proccessed_one_at_a_time)
{
  rclcpp::LoanedSamples<Request> requests;

  // Given a request is sent.
  send_io_control_req(CONTROLLABLE_DID, ControlParameter::SHORT_TERM_ADJUSTMENT, {0xAA});

  // And the server starts to process it
  {
    wait_for_request();
    requests = m_service->take_request();
    ASSERT_EQ(requests.size(), 1U);
  }

  // When a second request is sent
  send_io_control_req(CONTROLLABLE_DID, ControlParameter::SHORT_TERM_ADJUSTMENT, {0xBB});

  // Then the log warning is emitted indicating that the second request is rejected.
  expect_warning_log_with_message("Service 0x2F Request Rejected: ongoing request");

  // Given the server responds to the first request
  {
    const auto & request = requests.front();
    auto response = borrow_loaned_response();
    response->did = request->did;
    response->control_parameter = request->control_parameter;
    response->status = ControlStatus::OK;
    PollingServiceFixture::send_response(request, std::move(response));
  }

  // When the second request is sent again
  send_io_control_req(CONTROLLABLE_DID, ControlParameter::SHORT_TERM_ADJUSTMENT, {0xBB});

  // Then the server processes the second request
  {
    wait_for_request();
    requests = m_service->take_request();
    ASSERT_EQ(requests.size(), 1U);
  }
}

TEST_F(InputOutputControlFixture, requests_time_out)
{
  // Given a request is sent.
  send_io_control_req(CONTROLLABLE_DID, ControlParameter::SHORT_TERM_ADJUSTMENT, {0xAA});

  // And that the server gets the request
  {
    wait_for_request();
    auto requests = m_service->take_request();
    ASSERT_EQ(requests.size(), 1U);
  }

  // When the server does not respond within the timeout duration
  advance_time(REQUEST_TIMEOUT + std::chrono::milliseconds{1});

  // Then the response received UDS Server is that the resource is temporarily not available.
  ASSERT_TRUE(assert_negative_response(SID::INPUT_OUTPUT_CONTROL_BY_IDENTIFIER_REQ,
                                       NRC::RESOURCE_TEMPORARILY_NOT_AVAILABLE));
}
