// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include <utility>  // for std::move
#include <vector>  // for std::vector

#include "fixtures/diagnostic_test_srv_client_fixture_base.hpp"
#include "gtest/gtest.h"
#include "shared/uds_server_integration_fixture.hpp"

#include "uds_msgs/srv/uds_clear_dtc.hpp"

namespace
{

using UdsClearDtc = uds_msgs::srv::UdsClearDtc;
using UdsClearDtcServiceStub =
  apex::diagnostic_common::testing::DiagnosticTestSrvClientFixtureBase<UdsClearDtc>;

constexpr const char * SRV_NAME{"DtccClearDtc"};

class ClearDtcFixture : public shared::UdsServerIntegrationFixture
{
public:
  ClearDtcFixture() : m_service_stub{m_context->as_ref(), SRV_NAME} {}

  void SetUp() override
  {
    apex::diagnostic_common::testing::DiagnosticTestFixture::SetUp();
    start_runner();
  }

  void send_response(std::function<void(const UdsClearDtc::Request::BorrowedType &)> verify_cb,
                     std::function<void(UdsClearDtc::Response::BorrowedType &)> fill_cb)
  {
    const auto requests{m_service_stub.m_service->take_request()};
    ASSERT_EQ(requests.size(), 1U);
    const auto & req = requests.front();
    ASSERT_TRUE(req.info().valid());

    ASSERT_TRUE(verify_cb);
    verify_cb(req.data());

    auto loaned_response{m_service_stub.borrow_loaned_response()};

    fill_cb(loaned_response.get());
    m_service_stub.send_response(req, std::move(loaned_response));
  }

  UdsClearDtcServiceStub m_service_stub;
};
}  // namespace

TEST_F(ClearDtcFixture, clear_dtc_shall_return_positive_response)
{
  const std::vector<uint8_t> dtc_group{0x01U, 0x02U, 0x03U};

  request_clear_dtc(dtc_group);
  m_service_stub.wait_for_request();
  send_response(
    [dtc_group_ptr = &dtc_group](const UdsClearDtc::Request::BorrowedType & request) {
      ASSERT_EQ(dtc_group_ptr->size(), request.parameters.size());
      for (size_t i = 0U; i < request.parameters.size(); ++i) {
        EXPECT_EQ(dtc_group_ptr->at(i), request.parameters.at(i));
      }
    },
    [](UdsClearDtc::Response::BorrowedType & response) {
      response.response_code = UdsClearDtc::Response::BorrowedType::ResponseCode::POSITIVE_ACK;
    });

  ASSERT_TRUE(assert_positive_response(shared::Sid::CLEAR_DIAGNOSTIC_INFORMATION_POS_RESP));
}

TEST_F(ClearDtcFixture, clear_dtc_shall_return_negative_response_on_invalid_msg_size)
{
  const std::vector<uint8_t> dtc_group{};
  request_clear_dtc(dtc_group);
  wait_for_response();
  ASSERT_TRUE(
    assert_negative_response(shared::Sid::CLEAR_DIAGNOSTIC_INFORMATION_REQ,
                             shared::ResponseCode::INCORRECT_MESSAGE_LENGTH_OR_INVALID_FORMAT));
}

TEST_F(ClearDtcFixture, clear_dtc_shall_return_negative_response_on_external_dtc_service_error)
{
  const std::vector<uint8_t> dtc_group{0x01U, 0x02U, 0x03U};
  request_clear_dtc(dtc_group);
  m_service_stub.wait_for_request();
  send_response(
    [](const UdsClearDtc::Request::BorrowedType & request) {
      // empty
    },
    [](UdsClearDtc::Response::BorrowedType & response) {
      response.response_code =
        UdsClearDtc::Response::BorrowedType::ResponseCode::REQUEST_OUT_OF_RANGE;
    });
  ASSERT_TRUE(assert_negative_response(shared::Sid::CLEAR_DIAGNOSTIC_INFORMATION_REQ,
                                       shared::ResponseCode::REQUEST_OUT_OF_RANGE));
}
