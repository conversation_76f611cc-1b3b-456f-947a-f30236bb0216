// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include <utility>  // for std::move
#include <vector>  // for std::vector

#include "fixtures/diagnostic_test_srv_client_fixture_base.hpp"
#include "gtest/gtest.h"
#include "shared/uds_server_integration_fixture.hpp"

#include "uds_msgs/srv/uds_control_dtc_setting.hpp"

namespace
{

using UdsControlDtcSettings = uds_msgs::srv::UdsControlDtcSetting;
using UdsControlDtcSettingsServiceStub =
  apex::diagnostic_common::testing::DiagnosticTestSrvClientFixtureBase<UdsControlDtcSettings>;

constexpr const char * SRV_NAME{"DtccControlDtc"};

class ControlDtcSettingsFixture : public shared::UdsServerIntegrationFixture
{
public:
  ControlDtcSettingsFixture() : m_service_stub{m_context->as_ref(), SRV_NAME} {}

  void SetUp() override
  {
    apex::diagnostic_common::testing::DiagnosticTestFixture::SetUp();
    start_runner();
  }

  void send_response(
    std::function<void(const UdsControlDtcSettings::Request::BorrowedType &)> verify_cb,
    std::function<void(UdsControlDtcSettings::Response::BorrowedType &)> fill_cb)
  {
    const auto requests{m_service_stub.m_service->take_request()};
    ASSERT_EQ(requests.size(), 1U);
    const auto & req = requests.front();
    ASSERT_TRUE(req.info().valid());

    ASSERT_TRUE(verify_cb);
    verify_cb(req.data());

    auto loaned_response{m_service_stub.borrow_loaned_response()};

    fill_cb(*loaned_response);
    m_service_stub.send_response(req, std::move(loaned_response));
  }

  UdsControlDtcSettingsServiceStub m_service_stub;
};
}  // namespace


TEST_F(ControlDtcSettingsFixture, control_dtc_settings_shall_return_positive_response)
{
  constexpr uint8_t subfunction{0x01U};
  const std::vector<uint8_t> option_control{0x01U};

  request_control_dtc_settings(subfunction, option_control);
  m_service_stub.wait_for_request();
  send_response(
    [option_control_ptr =
       &option_control](const UdsControlDtcSettings::Request::BorrowedType & request) {
      ASSERT_EQ(option_control_ptr->size(), request.option_record.size());
      for (size_t i = 0U; i < request.option_record.size(); ++i) {
        EXPECT_EQ(option_control_ptr->at(i), request.option_record.at(i));
      }
    },
    [](UdsControlDtcSettings::Response::BorrowedType & response) {
      response.response_code =
        UdsControlDtcSettings::Response::BorrowedType::ResponseCode::POSITIVE_ACK;
    });
  ASSERT_TRUE(assert_positive_response(shared::Sid::CONTROL_DTC_SETTINGS_POS_RESP));
}

TEST_F(ControlDtcSettingsFixture,
       control_dtc_settings_shall_return_negative_response_on_invalid_subfunction)
{
  constexpr uint8_t subfunction{0x7F};
  const std::vector<uint8_t> option_control{0x01U};

  request_control_dtc_settings(subfunction, option_control);
  wait_for_response();
  ASSERT_TRUE(assert_negative_response(shared::Sid::CONTROL_DTC_SETTINGS_REQ,
                                       shared::ResponseCode::SUBFUNCTION_NOT_SUPPORTED));
}

TEST_F(ControlDtcSettingsFixture,
       control_dtc_settings_shall_return_negative_response_on_external_service_error)
{
  constexpr uint8_t subfunction{0x01U};
  const std::vector<uint8_t> option_control{0x01U};

  request_control_dtc_settings(subfunction, option_control);
  m_service_stub.wait_for_request();
  send_response(
    [](const UdsControlDtcSettings::Request::BorrowedType & request) {
      // empty
    },
    [](UdsControlDtcSettings::Response::BorrowedType & response) {
      response.response_code =
        UdsControlDtcSettings::Response::BorrowedType::ResponseCode::REQUEST_OUT_OF_RANGE;
    });
  ASSERT_TRUE(assert_negative_response(shared::Sid::CONTROL_DTC_SETTINGS_REQ,
                                       shared::ResponseCode::REQUEST_OUT_OF_RANGE));
}
