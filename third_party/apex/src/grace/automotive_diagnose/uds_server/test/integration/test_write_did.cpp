// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include <utility>  // for move
#include <vector>  // for vector

#include "gtest/gtest.h"
#include "rclcpp/node.hpp"  // for PollingService, PollingSubscription, Publisher
#include "shared/uds_server_integration_fixture.hpp"

namespace
{

using UdsWriteDataByIdentifier = uds_msgs::srv::UdsWriteDataByIdentifier;

using UdsWriteDataByIdentifierServiceStub =
  apex::diagnostic_common::testing::DiagnosticTestSrvClientFixtureBase<UdsWriteDataByIdentifier>;

using UdsSecurityGetSeedServiceStub = shared::UdsSecurityGetSeedServiceStub;
using UdsSecurityCompareKeyServiceStub = shared::UdsSecurityCompareKeyServiceStub;

constexpr const char * EXAMPLE1_WRITE_SRV_NAME{"/uds/did/example1/write"};
constexpr const char * UDS_GET_SEED_SRV_NAME{"/get_seed"};
constexpr const char * UDS_COMPARE_KEY_SRV_NAME{"/compare_key"};

const shared::WriteDidData NEW_VIN{
  '2', 'F', 'M', 'D', 'K', '3', '8', 'C', '4', '7', 'B', 'A', '9', '4', '0', '7', '5'};

class WriteDataByIdentifierFixture : public shared::UdsServerIntegrationFixture
{
public:
  WriteDataByIdentifierFixture()
  : m_example1_srv{m_context->as_ref(), EXAMPLE1_WRITE_SRV_NAME},
    m_get_seed_srv{m_context->as_ref(), UDS_GET_SEED_SRV_NAME},
    m_compare_key_srv{m_context->as_ref(), UDS_COMPARE_KEY_SRV_NAME}
  {
  }

  void SetUp() override
  {
    apex::diagnostic_common::testing::DiagnosticTestFixture::SetUp();
    start_runner();
  }

  void TearDown() override
  {
    apex::diagnostic_common::testing::DiagnosticTestFixture::TearDown();
  }

  void send_example1_response()
  {
    const auto requests{m_example1_srv.m_service->take_request()};
    ASSERT_EQ(requests.size(), 1U);
    const auto & req = requests.front();
    ASSERT_TRUE(req.info().valid());
    ASSERT_EQ(req.data().data, NEW_VIN);
    auto loaned_response{m_example1_srv.borrow_loaned_response()};
    auto & data = loaned_response.get();
    data.did = req.data().did;
    data.success = true;
    m_example1_srv.send_response(req, std::move(loaned_response));
  }

  void unlock_first_security_level()
  {
    unlock_security_level(0x01U, m_get_seed_srv, m_compare_key_srv);
  }

  UdsWriteDataByIdentifierServiceStub m_example1_srv;
  UdsSecurityGetSeedServiceStub m_get_seed_srv;
  UdsSecurityCompareKeyServiceStub m_compare_key_srv;
};

}  // namespace

TEST_F(WriteDataByIdentifierFixture, write_did_positive_response)
{
  constexpr uint16_t VIN_DID{0xF190U};

  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);
  // ulock security level 0x01
  unlock_first_security_level();

  // send write did request
  send_request_write_did(VIN_DID, NEW_VIN);
  // wait for service request
  m_example1_srv.wait_for_request();
  // send service response
  send_example1_response();
  // get response and assert that it is positive
  shared::UdsTransportMessage response{};
  ASSERT_TRUE(get_response(response));
  ASSERT_TRUE(assert_positive_response(response, shared::Sid::WRITE_DATA_BY_IDENTIFIER_POS_RESP));

  const auto vin_bytes_hi = static_cast<uint8_t>((VIN_DID >> 8) & 0xFF);
  const auto vin_bytes_lo = static_cast<uint8_t>(VIN_DID & 0xFF);

  EXPECT_EQ(response.payload.at(shared::WRITE_DID_BEGIN_BYTE_IN_PAYLOAD), vin_bytes_hi);
  EXPECT_EQ(response.payload.at(shared::WRITE_DID_BEGIN_BYTE_IN_PAYLOAD + 1), vin_bytes_lo);
}

TEST_F(WriteDataByIdentifierFixture, write_did_negative_response_on_inaccessible_service)
{
  constexpr uint16_t INACCESSIBLE_SERVICE_DID{0xF18CU};

  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);
  // ulock security level 0x01
  unlock_first_security_level();

  // send write did request to an inaccessible service
  send_request_write_did(INACCESSIBLE_SERVICE_DID, {0x00U});
  // wait for response
  wait_for_response();
  // get response and assert NRC Request Out Of Range
  ASSERT_TRUE(assert_negative_response(shared::Sid::WRITE_DATA_BY_IDENTIFIER_REQ,
                                       shared::ResponseCode::REQUEST_OUT_OF_RANGE));
}

TEST_F(WriteDataByIdentifierFixture, write_did_negative_response_on_unresponsive_service)
{
  constexpr uint16_t UNRESPONSIVE_SERVICE_DID{0x0043U};

  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);
  // ulock security level 0x01
  unlock_first_security_level();

  // send write did request to an unresponsive service
  send_request_write_did(UNRESPONSIVE_SERVICE_DID, {0x00U});
  // wait for response
  wait_for_response();
  // get response and assert NRC General Programming Failure
  ASSERT_TRUE(assert_negative_response(shared::Sid::WRITE_DATA_BY_IDENTIFIER_REQ,
                                       shared::ResponseCode::GENERAL_PROGRAMMING_FAILURE));
}
