// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include <algorithm>  // for std::copy
#include <utility>  // for std::move
#include <vector>  // for std::vector

#include "gtest/gtest.h"
#include "rclcpp/node.hpp"  // for PollingService, PollingSubscription, Publisher
#include "shared/uds_server_integration_fixture.hpp"

#include "uds_msgs/srv/uds_read_dtc.hpp"

namespace
{

using UdsTransportMessage = uds_msgs::msg::UdsTransportMessage;
using ReadDtc = uds_msgs::srv::UdsReadDtc;
using ReadDtcResponseCode = ReadDtc::Response::ResponseCode;

using ReadDtcServiceStub =
  apex::diagnostic_common::testing::DiagnosticTestSrvClientFixtureBase<ReadDtc>;

using MessagePayload = uds_msgs::msg::UdsTransportMessage::_payload_type;

using AssertRequestCallback = std::function<void(const ReadDtc::Request &)>;
using FillResponseCallback = std::function<void(ReadDtc::Response &)>;

constexpr const char * READ_DTC_SRV_NAME{"DtccReadDtc"};

class ReadDtcFixture : public shared::UdsServerIntegrationFixture
{
public:
  ReadDtcFixture() : m_read_dtc_srv{m_context->as_ref(), READ_DTC_SRV_NAME} {}

  void SetUp() override
  {
    apex::diagnostic_common::testing::DiagnosticTestFixture::SetUp();
    start_runner();
  }

  ReadDtcServiceStub m_read_dtc_srv;
};

}  // namespace

TEST_F(ReadDtcFixture, read_dtc_shall_return_positive_response)
{
  const std::vector<uint8_t> expected_dtc_value{0x00, 0x01, 0x02};
  constexpr size_t DATA_OFFSET{2U};
  constexpr size_t SUBFUNCTION_INDEX{1U};
  constexpr size_t EXPECTED_SIZE{5U};
  // prepare and send read DTC request
  constexpr uint8_t subfunction = 0x01U;
  std::vector<uint8_t> dtc_params{0x01U};
  request_read_dtc(subfunction, dtc_params);
  // wait for service request
  m_read_dtc_srv.wait_for_request();
  // send service response
  m_read_dtc_srv.send_response(
    [expected_dtc_value_ptr = &expected_dtc_value](ReadDtc::Response::BorrowedType & res) {
      res.response_code = ReadDtc::Response::BorrowedType::ResponseCode::POSITIVE_ACK;
      std::copy(expected_dtc_value_ptr->begin(),
                expected_dtc_value_ptr->end(),
                std::back_inserter(res.payload));
    },
    [subfunction, dtc_params_ptr = &dtc_params](const ReadDtc::Request::BorrowedType & req) {
      ASSERT_EQ(static_cast<uint8_t>(req.subfunction) + 1U, subfunction);
      ASSERT_EQ(dtc_params_ptr->size(), req.parameters.size());
      for (size_t i = 0; i < dtc_params_ptr->size(); ++i) {
        EXPECT_EQ(req.parameters[i], dtc_params_ptr->at(i));
      }
    });
  // get response and assert that it is positive
  shared::UdsTransportMessage response{};
  ASSERT_TRUE(get_response(response));

  assert_positive_response(response, shared::Sid::READ_DTC_INFORMATION_POS_RESP);
  // assert response data
  ASSERT_EQ(response.payload.size(), EXPECTED_SIZE);
  ASSERT_EQ(response.payload[SUBFUNCTION_INDEX], subfunction);
  for (size_t i = 0; i < expected_dtc_value.size(); ++i) {
    ASSERT_EQ(expected_dtc_value[i], response.payload[i + DATA_OFFSET]);
  }
}

TEST_F(ReadDtcFixture, read_dtc_should_return_nrc_on_invalid_subfunction_value)
{
  // prepare and send read DTC request
  constexpr uint8_t subfunction = 0xFFU;
  std::vector<uint8_t> dtc_params{0x01U};
  request_read_dtc(subfunction, dtc_params);
  // get response and assert that it is nrc
  ASSERT_TRUE(assert_negative_response(shared::Sid::READ_DTC_INFORMATION_REQ,
                                       shared::ResponseCode::SUBFUNCTION_NOT_SUPPORTED));
}

TEST_F(ReadDtcFixture, read_dtc_should_return_nrc_on_request_out_of_range)
{
  // prepare and send read DTC request
  constexpr uint8_t subfunction = 0x01U;
  std::vector<uint8_t> dtc_params{0x01U};
  request_read_dtc(subfunction, dtc_params);
  // wait for service request
  m_read_dtc_srv.wait_for_request();
  // send service response
  m_read_dtc_srv.send_response(
    [](ReadDtc::Response::BorrowedType & res) {
      res.response_code = ReadDtc::Response::BorrowedType::ResponseCode::REQUEST_OUT_OF_RANGE;
    },
    [subfunction, dtc_params_ptr = &dtc_params](const ReadDtc::Request & req) {
      ASSERT_EQ(static_cast<uint8_t>(req.subfunction) + 1U, subfunction);
      ASSERT_EQ(dtc_params_ptr->size(), req.parameters.size());
      for (size_t i = 0; i < dtc_params_ptr->size(); ++i) {
        EXPECT_EQ(req.parameters[i], dtc_params_ptr->at(i));
      }
    });
  // get response and assert that it is nrc
  ASSERT_TRUE(assert_negative_response(shared::Sid::READ_DTC_INFORMATION_REQ,
                                       shared::ResponseCode::REQUEST_OUT_OF_RANGE));
}
