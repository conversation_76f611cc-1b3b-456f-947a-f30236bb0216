// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include <utility>  // for move
#include <vector>  // for vector

#include "gtest/gtest.h"
#include "rclcpp/node.hpp"  // for PollingService, PollingSubscription, Publisher
#include "shared/uds_server_integration_fixture.hpp"

namespace
{

using UdsReadDataByIdentifier = uds_msgs::srv::UdsReadDataByIdentifier;

using UdsReadDataByIdentifierServiceStub =
  apex::diagnostic_common::testing::DiagnosticTestSrvClientFixtureBase<UdsReadDataByIdentifier>;

constexpr const char * EXAMPLE1_READ_SRV_NAME{"/uds/did/example1/read"};

constexpr uint16_t UNRESPONSIVE_SERVICE_DID{0x0043U};
constexpr uint16_t VIN_DID{0xF190U};
const shared::ReadDidPayload DEFAULT_VIN{
  'W', 'B', 'S', 'P', 'M', '9', 'C', '5', '2', 'B', 'E', '2', '0', '2', '5', '1', '4'};

class ReadDataByIdentifierFixture : public shared::UdsServerIntegrationFixture
{
public:
  ReadDataByIdentifierFixture() : m_example1_srv{m_context->as_ref(), EXAMPLE1_READ_SRV_NAME} {}

  void SetUp() override
  {
    apex::diagnostic_common::testing::DiagnosticTestFixture::SetUp();
    start_runner();
  }

  void send_example1_response()
  {
    const auto requests{m_example1_srv.m_service->take_request()};
    ASSERT_EQ(requests.size(), 1U);
    const auto & req = requests.front();
    ASSERT_TRUE(req.info().valid());
    auto loaned_response{m_example1_srv.borrow_loaned_response()};
    auto & data = loaned_response.get();
    data.did = req.data().did;
    data.success = true;
    data.payload.reserve(DEFAULT_VIN.size());
    for (const auto & vin : DEFAULT_VIN) {
      data.payload.emplace_back(vin);
    }
    m_example1_srv.send_response(req, std::move(loaned_response));
  }

  UdsReadDataByIdentifierServiceStub m_example1_srv;
};

}  // namespace

TEST_F(ReadDataByIdentifierFixture, read_did_positive_response_static)
{
  constexpr uint16_t VERSION_INFO_DID{0xFF00U};
  constexpr size_t MAJOR_BYTE{shared::READ_DID_DATA_RECORD_BEGIN_BYTE_IN_PAYLOAD};
  constexpr size_t MINOR_BYTE{MAJOR_BYTE + 1U};
  constexpr size_t REVISION_BYTE{MINOR_BYTE + 1U};
  constexpr size_t NA_BYTE{REVISION_BYTE + 1U};


  // send read did request
  send_request_read_dids(VERSION_INFO_DID);
  // wait for response
  wait_for_response();
  // get response and assert that it is positive
  shared::UdsTransportMessage response{};
  ASSERT_TRUE(get_response(response));
  ASSERT_TRUE(assert_positive_response(response, shared::Sid::READ_DATA_BY_IDENTIFIER_POS_RESP));
  // major
  EXPECT_EQ(response.payload.at(MAJOR_BYTE), 0x03U);
  // minor
  EXPECT_EQ(response.payload.at(MINOR_BYTE), 0x00U);
  // revision
  EXPECT_EQ(response.payload.at(REVISION_BYTE), 0x00U);
  // NA
  EXPECT_EQ(response.payload.at(NA_BYTE), 0x00U);
}

TEST_F(ReadDataByIdentifierFixture, read_did_positive_response_async)
{
  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);

  // send read did request
  send_request_read_dids(VIN_DID);
  // wait for service request
  m_example1_srv.wait_for_request();
  // send service response
  send_example1_response();
  // get response and assert that it is positive
  shared::UdsTransportMessage response{};
  ASSERT_TRUE(get_response(response));
  ASSERT_TRUE(assert_positive_response(response, shared::Sid::READ_DATA_BY_IDENTIFIER_POS_RESP));
  for (size_t i = 0U; i < DEFAULT_VIN.size(); i++) {
    EXPECT_EQ(response.payload.at(shared::READ_DID_DATA_RECORD_BEGIN_BYTE_IN_PAYLOAD + i),
              DEFAULT_VIN.at(i));
  }
}

TEST_F(ReadDataByIdentifierFixture, read_did_negative_response_on_inaccessible_service)
{
  constexpr uint16_t INACCESSIBLE_SERVICE_DID{0xF18CU};

  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);

  // send read did request to an inaccessible service
  send_request_read_dids(INACCESSIBLE_SERVICE_DID);
  // wait for response
  wait_for_response();
  // get response and assert NRC Request Out Of Range
  ASSERT_TRUE(assert_negative_response(shared::Sid::READ_DATA_BY_IDENTIFIER_REQ,
                                       shared::ResponseCode::REQUEST_OUT_OF_RANGE));
}

TEST_F(ReadDataByIdentifierFixture, read_did_negative_response_on_unresponsive_service)
{
  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);

  // send read did request to an unresponsive service
  send_request_read_dids(UNRESPONSIVE_SERVICE_DID);
  // wait for response
  wait_for_response();
  // get response and assert NRC Conditions Not Correct
  ASSERT_TRUE(assert_negative_response(shared::Sid::READ_DATA_BY_IDENTIFIER_REQ,
                                       shared::ResponseCode::CONDITIONS_NOT_CORRECT));
}

TEST_F(ReadDataByIdentifierFixture, read_did_negative_response_on_any_failed_request)
{
  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);

  // send read did request to an unresponsive service among a proper one
  send_request_read_dids(VIN_DID, UNRESPONSIVE_SERVICE_DID);
  // wait for response
  wait_for_response();
  // get response and assert NRC Conditions Not Correct
  ASSERT_TRUE(assert_negative_response(shared::Sid::READ_DATA_BY_IDENTIFIER_REQ,
                                       shared::ResponseCode::CONDITIONS_NOT_CORRECT));
}
