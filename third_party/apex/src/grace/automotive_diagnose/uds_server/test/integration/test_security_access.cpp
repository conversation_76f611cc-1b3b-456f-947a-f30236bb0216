// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include <utility>  // for move
#include <vector>  // for vector

#include "gtest/gtest.h"
#include "rclcpp/node.hpp"  // for PollingService, PollingSubscription, Publisher
#include "shared/uds_server_integration_fixture.hpp"

namespace
{

using UdsSecurityGetSeedServiceStub = shared::UdsSecurityGetSeedServiceStub;
using UdsSecurityCompareKeyServiceStub = shared::UdsSecurityCompareKeyServiceStub;

constexpr size_t SID_BYTE_IN_PAYLOAD{0U};
constexpr size_t SUBFUN_BYTE_IN_PAYLOAD{1U};

constexpr const char * UDS_GET_SEED_SRV_NAME{"/get_seed"};
constexpr const char * UDS_COMPARE_KEY_SRV_NAME{"/compare_key"};

constexpr shared::SecurityLevel VALID_SECURITY_LEVEL{0x01U};
constexpr shared::SecurityLevel INVALID_SECURITY_LEVEL{0x00U};
constexpr shared::SecurityLevel DISABLED_SECURITY_LEVEL{0x0BU};
const shared::SeedData SEED{0x01U, 0x02U};
const shared::KeyData VALID_KEY{0x02U, 0x01U};
const shared::KeyData INVALID_KEY{0x01U, 0x01U};
constexpr uint32_t FAILURE_THRESHOLD{3U};

class SecurityAccessFixture : public shared::UdsServerIntegrationFixture
{
public:
  SecurityAccessFixture()
  : m_get_seed_srv{m_context->as_ref(), UDS_GET_SEED_SRV_NAME},
    m_compare_key_srv{m_context->as_ref(), UDS_COMPARE_KEY_SRV_NAME}
  {
  }

  void SetUp() override
  {
    apex::diagnostic_common::testing::DiagnosticTestFixture::SetUp();
    start_runner();
  }

  void send_get_seed_response()
  {
    m_get_seed_srv.send_response([](auto & response) {
      response.success = true;
      response.seed = SEED;
    });
  }

  void send_compare_key_response()
  {
    const auto requests{m_compare_key_srv.m_service->take_request()};
    ASSERT_EQ(requests.size(), 1U);
    const auto & req = requests.front();
    ASSERT_TRUE(req.info().valid());
    auto loaned_response{m_compare_key_srv.borrow_loaned_response()};
    loaned_response.get().success = (SEED == req.data().seed) && (VALID_KEY == req.data().key);
    m_compare_key_srv.send_response(req, std::move(loaned_response));
  }

  UdsSecurityGetSeedServiceStub m_get_seed_srv;
  UdsSecurityCompareKeyServiceStub m_compare_key_srv;
};

}  // namespace

TEST_F(SecurityAccessFixture, security_access_positive_response)
{
  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);

  {
    // send request seed
    send_request_seed(VALID_SECURITY_LEVEL);
    // wait for service request
    m_get_seed_srv.wait_for_request();
    // send service response
    send_get_seed_response();
    // get response and assert that it is positive
    shared::UdsTransportMessage response{};
    ASSERT_TRUE(get_response(response));
    ASSERT_TRUE(assert_positive_response(response, shared::Sid::SECURITY_ACCESS_POS_RESP));
    EXPECT_EQ(response.payload.at(SID_BYTE_IN_PAYLOAD),
              static_cast<uint8_t>(shared::Sid::SECURITY_ACCESS_POS_RESP));
    EXPECT_EQ(response.payload.at(SUBFUN_BYTE_IN_PAYLOAD), VALID_SECURITY_LEVEL);
    EXPECT_EQ(response.payload.at(shared::SEED_BEGIN_BYTE_IN_PAYLOAD), SEED.at(0));
    EXPECT_EQ(response.payload.at(shared::SEED_BEGIN_BYTE_IN_PAYLOAD + 1U), SEED.at(1));
  }

  // send compare key
  send_request_compare_key(VALID_SECURITY_LEVEL, VALID_KEY);
  // wait for service request
  m_compare_key_srv.wait_for_request();
  // send service response
  send_compare_key_response();
  // get response and assert that it is positive
  shared::UdsTransportMessage response{};
  ASSERT_TRUE(get_response(response));
  assert_positive_response(response, shared::Sid::SECURITY_ACCESS_POS_RESP);
  EXPECT_EQ(response.payload.at(SID_BYTE_IN_PAYLOAD),
            static_cast<uint8_t>(shared::Sid::SECURITY_ACCESS_POS_RESP));
  EXPECT_EQ(response.payload.at(SUBFUN_BYTE_IN_PAYLOAD),
            static_cast<uint8_t>(VALID_SECURITY_LEVEL + 1U));
}

TEST_F(SecurityAccessFixture, security_access_negative_response_invalid_security_level)
{
  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);

  // send request seed
  send_request_seed(INVALID_SECURITY_LEVEL);
  // get response and assert NRC Subfunction Not Supported
  ASSERT_TRUE(assert_negative_response(shared::Sid::SECURITY_ACCESS_REQ,
                                       shared::ResponseCode::SUBFUNCTION_NOT_SUPPORTED));
}

TEST_F(SecurityAccessFixture, security_access_negative_response_disabled_security_level)
{
  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);

  // send request seed
  send_request_seed(DISABLED_SECURITY_LEVEL);
  // get response and assert NRC Subfunction Not Supported
  ASSERT_TRUE(assert_negative_response(shared::Sid::SECURITY_ACCESS_REQ,
                                       shared::ResponseCode::SUBFUNCTION_NOT_SUPPORTED));
}

TEST_F(SecurityAccessFixture, security_access_negative_response_invalid_sequence_compare_key_first)
{
  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);

  // givent that the seed request is missing

  // send compare key
  send_request_compare_key(VALID_SECURITY_LEVEL, VALID_KEY);
  // get response and assert NRC Request Sequence Error
  ASSERT_TRUE(assert_negative_response(shared::Sid::SECURITY_ACCESS_REQ,
                                       shared::ResponseCode::REQUEST_SEQUENCE_ERROR));
}

TEST_F(SecurityAccessFixture, security_access_negative_response_invalid_sequence_wrong_level)
{
  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);

  {
    // send request seed
    send_request_seed(VALID_SECURITY_LEVEL);
    // wait for service request
    m_get_seed_srv.wait_for_request();
    // send service response
    send_get_seed_response();
    // get response and assert that it is positive
    shared::UdsTransportMessage response{};
    ASSERT_TRUE(get_response(response));
    ASSERT_TRUE(assert_positive_response(response, shared::Sid::SECURITY_ACCESS_POS_RESP));
    EXPECT_EQ(response.payload.at(SID_BYTE_IN_PAYLOAD),
              static_cast<uint8_t>(shared::Sid::SECURITY_ACCESS_POS_RESP));
    EXPECT_EQ(response.payload.at(SUBFUN_BYTE_IN_PAYLOAD), VALID_SECURITY_LEVEL);
    EXPECT_EQ(response.payload.at(shared::SEED_BEGIN_BYTE_IN_PAYLOAD), SEED.at(0));
    EXPECT_EQ(response.payload.at(shared::SEED_BEGIN_BYTE_IN_PAYLOAD + 1U), SEED.at(1));
  }


  // send compare key for the next (different) security level
  send_request_compare_key(VALID_SECURITY_LEVEL + 2U, VALID_KEY);
  // get response and assert NRC Request Sequence Error
  ASSERT_TRUE(assert_negative_response(shared::Sid::SECURITY_ACCESS_REQ,
                                       shared::ResponseCode::REQUEST_SEQUENCE_ERROR));
}

TEST_F(SecurityAccessFixture, security_access_negative_response_invalid_key)
{
  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);

  {
    // send request seed
    send_request_seed(VALID_SECURITY_LEVEL);
    // wait for service request
    m_get_seed_srv.wait_for_request();
    // send service response
    send_get_seed_response();
    // get response and assert that it is positive
    shared::UdsTransportMessage response{};
    ASSERT_TRUE(get_response(response));
    ASSERT_TRUE(assert_positive_response(response, shared::Sid::SECURITY_ACCESS_POS_RESP));
    EXPECT_EQ(response.payload.at(SID_BYTE_IN_PAYLOAD),
              static_cast<uint8_t>(shared::Sid::SECURITY_ACCESS_POS_RESP));
    EXPECT_EQ(response.payload.at(SUBFUN_BYTE_IN_PAYLOAD), VALID_SECURITY_LEVEL);
    EXPECT_EQ(response.payload.at(shared::SEED_BEGIN_BYTE_IN_PAYLOAD), SEED.at(0));
    EXPECT_EQ(response.payload.at(shared::SEED_BEGIN_BYTE_IN_PAYLOAD + 1U), SEED.at(1));
  }


  // send compare key
  send_request_compare_key(VALID_SECURITY_LEVEL, INVALID_KEY);
  // wait for service request
  m_compare_key_srv.wait_for_request();
  // send service response
  send_compare_key_response();
  // get response and assert NRC Invalid Key
  ASSERT_TRUE(
    assert_negative_response(shared::Sid::SECURITY_ACCESS_REQ, shared::ResponseCode::INVALID_KEY));
}

TEST_F(SecurityAccessFixture, security_access_negative_response_delay_timer)
{
  // change session to extended 0x03
  publish_env_condition(0U, 0U);
  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);

  auto try_to_unlock_sec_lvl_with_invalid_key = [&]() {
    // send request seed
    send_request_seed(VALID_SECURITY_LEVEL);
    // wait for service request
    m_get_seed_srv.wait_for_request();
    // send service response
    send_get_seed_response();
    // get response and assert that it is positive
    shared::UdsTransportMessage response{};
    ASSERT_TRUE(get_response(response));
    ASSERT_TRUE(assert_positive_response(response, shared::Sid::SECURITY_ACCESS_POS_RESP));
    EXPECT_EQ(response.payload.at(SID_BYTE_IN_PAYLOAD),
              static_cast<uint8_t>(shared::Sid::SECURITY_ACCESS_POS_RESP));
    EXPECT_EQ(response.payload.at(SUBFUN_BYTE_IN_PAYLOAD), VALID_SECURITY_LEVEL);
    EXPECT_EQ(response.payload.at(shared::SEED_BEGIN_BYTE_IN_PAYLOAD), SEED.at(0));
    EXPECT_EQ(response.payload.at(shared::SEED_BEGIN_BYTE_IN_PAYLOAD + 1U), SEED.at(1));


    // send compare key
    send_request_compare_key(VALID_SECURITY_LEVEL, INVALID_KEY);
    // wait for service request
    m_compare_key_srv.wait_for_request();
    // send service response
    send_compare_key_response();
  };

  // fail to unlock security level delay timer threshold - 1 times
  for (uint32_t i = 1; i < FAILURE_THRESHOLD; ++i) {
    try_to_unlock_sec_lvl_with_invalid_key();
    // get response and assert NRC Invalid Key
    ASSERT_TRUE(assert_negative_response(shared::Sid::SECURITY_ACCESS_REQ,
                                         shared::ResponseCode::INVALID_KEY));
  }

  {
    // reach the delay timer threshold with another failed unlocking attempt
    try_to_unlock_sec_lvl_with_invalid_key();
    // get response and assert NRC Exceeded Number Of Attempts
    ASSERT_TRUE(assert_negative_response(shared::Sid::SECURITY_ACCESS_REQ,
                                         shared::ResponseCode::EXCEEDED_NUMBER_OF_ATTEMPTS));
  }


  // try to unlock yet again, while the delay timer is active
  // just send request seed
  send_request_seed(VALID_SECURITY_LEVEL);
  // get response already and assert NRC Required Time Delay Not Expired
  ASSERT_TRUE(assert_negative_response(shared::Sid::SECURITY_ACCESS_REQ,
                                       shared::ResponseCode::REQUIRED_TIME_DELAY_NOT_EXPIRED));
}
