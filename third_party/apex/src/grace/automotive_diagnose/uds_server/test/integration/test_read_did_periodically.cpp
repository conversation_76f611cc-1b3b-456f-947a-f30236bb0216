// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include <utility>  // for move
#include <vector>  // for vector

#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "shared/uds_server_integration_fixture.hpp"
#include "uds_server/services/did/read_by_did_periodically_service.hpp"

namespace
{

using UdsReadDataByIdentifier = uds_msgs::srv::UdsReadDataByIdentifier;

using UdsReadDataByPeriodicIdentifierServiceStub =
  apex::diagnostic_common::testing::DiagnosticTestSrvClientFixtureBase<UdsReadDataByIdentifier>;

using UdsSecurityGetSeedServiceStub = shared::UdsSecurityGetSeedServiceStub;
using UdsSecurityCompareKeyServiceStub = shared::UdsSecurityCompareKeyServiceStub;

constexpr const char * UDS_GET_SEED_SRV_NAME{"/get_seed"};
constexpr const char * UDS_COMPARE_KEY_SRV_NAME{"/compare_key"};

constexpr uint8_t TRANSMISSION_MODE_SLOW{0x01U};
constexpr uint16_t EXAMPLE_DID_1{0xF201U};
constexpr uint16_t EXAMPLE_DID_2{0xF102U};
constexpr std::initializer_list<uint8_t> DID_1_VALUE = {0xA1, 0xB1, 0xC1, 0xD1};
constexpr std::initializer_list<uint8_t> DID_2_VALUE = {0xA2, 0xB2, 0xC2, 0xD2};

class ReadDataByPeriodicIdentifierFixture : public shared::UdsServerIntegrationFixture
{
public:
  ReadDataByPeriodicIdentifierFixture()
  : m_example6_srv{m_context->as_ref(), "/uds/did/example6/read"},
    m_example7_srv{m_context->as_ref(), "/uds/did/example7/read"},
    m_get_seed_srv{m_context->as_ref(), UDS_GET_SEED_SRV_NAME},
    m_compare_key_srv{m_context->as_ref(), UDS_COMPARE_KEY_SRV_NAME}
  {
  }

  void SetUp() override
  {
    apex::diagnostic_common::testing::DiagnosticTestFixture::SetUp();
    start_runner();
  }

  void send_example1_response()
  {
    const auto requests{m_example6_srv.m_service->take_request()};
    ASSERT_EQ(requests.size(), 1U);
    const auto & req = requests.front();
    ASSERT_TRUE(req.info().valid());
    auto loaned_response{m_example6_srv.borrow_loaned_response()};
    auto & data = loaned_response.get();
    data.did = req.data().did;
    data.success = true;
    data.payload = shared::ReadDidPayload(DID_1_VALUE);
    m_example6_srv.send_response(req, std::move(loaned_response));
  }
  void send_example2_response()
  {
    const auto requests{m_example7_srv.m_service->take_request()};
    ASSERT_EQ(requests.size(), 1U);
    const auto & req = requests.front();
    ASSERT_TRUE(req.info().valid());
    auto loaned_response{m_example7_srv.borrow_loaned_response()};
    auto & data = loaned_response.get();
    data.did = req.data().did;
    data.success = true;
    data.payload = shared::ReadDidPayload(DID_2_VALUE);
    m_example7_srv.send_response(req, std::move(loaned_response));
  }

  void unlock_first_security_level()
  {
    unlock_security_level(0x01U, m_get_seed_srv, m_compare_key_srv);
  }

  UdsReadDataByPeriodicIdentifierServiceStub m_example6_srv;
  UdsReadDataByPeriodicIdentifierServiceStub m_example7_srv;
  UdsSecurityGetSeedServiceStub m_get_seed_srv;
  UdsSecurityCompareKeyServiceStub m_compare_key_srv;
};

}  // namespace

TEST_F(ReadDataByPeriodicIdentifierFixture, read_did_periodic_positive_response)
{
  const auto dids_to_read = std::vector<uint16_t>{EXAMPLE_DID_1, EXAMPLE_DID_2};

  publish_env_condition(0U, 0U);

  change_session(shared::PredefinedSession::EXTENDED_DIAGNOSTIC);

  unlock_first_security_level();

  send_periodic_req(dids_to_read, TRANSMISSION_MODE_SLOW);

  // wait for response
  wait_for_response();

  // get response and assert that it is positive
  shared::UdsTransportMessage response;
  ASSERT_TRUE(get_response(response));
  assert_positive_response(response, shared::Sid::READ_DATA_BY_PERIODIC_IDENTIFIER_POS_RESP);

  // wait for response
  advance_time(std::chrono::milliseconds{110});

  m_example6_srv.wait_for_request();
  m_example7_srv.wait_for_request();

  {
    send_example1_response();
    wait_for_response();

    shared::UdsTransportMessage response{};
    ASSERT_TRUE(get_response(response));

    const auto payload =
      base::span<const uint8_t>(response.payload.data(), response.payload.size());

    EXPECT_EQ(payload[0], EXAMPLE_DID_1 & 0x00FF);
    EXPECT_THAT(payload.subspan(1), ::testing::ElementsAreArray(DID_1_VALUE));
  }

  {
    send_example2_response();
    wait_for_response();

    shared::UdsTransportMessage response{};
    ASSERT_TRUE(get_response(response));

    const auto payload =
      base::span<const uint8_t>(response.payload.data(), response.payload.size());

    EXPECT_EQ(payload[0], EXAMPLE_DID_2 & 0x00FF);
    EXPECT_THAT(payload.subspan(1), ::testing::ElementsAreArray(DID_2_VALUE));
  }
}
