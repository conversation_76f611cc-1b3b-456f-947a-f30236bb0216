load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

cc_library(
    name = "uds_protocol",
    srcs = [
        "src/addons/security/level_controller_base.cpp",
        "src/bringup/uds_builder.cpp",
        "src/bringup/uds_config_parser.cpp",
        "src/com/default_server_timeout_p4_handler.cpp",
        "src/com/request_response_tracker.cpp",
        "src/com/request_subscriber.cpp",
        "src/com/response_publisher.cpp",
        "src/com/service_hub.cpp",
        "src/env/env_condition_monitor.cpp",
        "src/services/did/async_did_reader.cpp",
        "src/services/did/async_did_writer.cpp",
        "src/services/did/did_pool.cpp",
        "src/services/did/read_by_did_periodically_service.cpp",
        "src/services/did/read_by_did_service.cpp",
        "src/services/did/static_did_reader.cpp",
        "src/services/did/write_by_did_service.cpp",
        "src/services/dtc/async_clear_dtc_client.cpp",
        "src/services/dtc/async_control_dtc_settings_client.cpp",
        "src/services/dtc/async_read_dtc_client.cpp",
        "src/services/dtc/clear_dtc_info_service.cpp",
        "src/services/dtc/control_dtc_settings_service.cpp",
        "src/services/dtc/dtc_parser.cpp",
        "src/services/dtc/read_dtc_info_service.cpp",
        "src/services/ioctrl/input_output_control_service.cpp",
        "src/services/ioctrl/input_output_control_settings.cpp",
        "src/services/ioctrl/polling_client_requester.cpp",
        "src/services/routine/async_routine_client.cpp",
        "src/services/routine/routine_control_service.cpp",
        "src/services/routine/routine_parser.cpp",
        "src/services/routine/routine_registry.cpp",
        "src/services/security/async_key_comparator.cpp",
        "src/services/security/async_level_controller.cpp",
        "src/services/security/async_seed_fetcher.cpp",
        "src/services/security/level_unlocking_result_publisher.cpp",
        "src/services/security/security_access_service.cpp",
        "src/services/security/security_level_controller_parser.cpp",
        "src/services/session/diagnostic_session_control_service.cpp",
        "src/services/session/tester_present_service.cpp",
        "src/services/shared/access_controller.cpp",
        "src/services/shared/access_parser.cpp",
        "src/services/shared/access_types.cpp",
        "src/services/shared/format.cpp",
        "src/services/shared/service_request.cpp",
        "src/session/default_session.cpp",
        "src/session/session.cpp",
        "src/session/session_event_publisher.cpp",
        "src/session/session_manager.cpp",
    ],
    hdrs = [
        "include/uds_server/addons/security/common_level_controller.hpp",
        "include/uds_server/addons/security/delay.hpp",
        "include/uds_server/addons/security/individual_level_controller.hpp",
        "include/uds_server/addons/security/level_controller_base.hpp",
        "include/uds_server/addons/security/level_controller_config.hpp",
        "include/uds_server/addons/security/level_delays.hpp",
        "include/uds_server/addons/security/permissive_level_controller.hpp",
        "include/uds_server/bringup/config.hpp",
        "include/uds_server/bringup/uds_builder.hpp",
        "include/uds_server/bringup/uds_config_parser.hpp",
        "include/uds_server/com/default_server_timeout_p4_handler.hpp",
        "include/uds_server/com/request_response_tracker.hpp",
        "include/uds_server/com/request_response_tracker_base.hpp",
        "include/uds_server/com/request_subscriber.hpp",
        "include/uds_server/com/response_publisher.hpp",
        "include/uds_server/com/response_publisher_base.hpp",
        "include/uds_server/com/server_timeout_p4_handler.hpp",
        "include/uds_server/com/service_hub.hpp",
        "include/uds_server/com/service_hub_base.hpp",
        "include/uds_server/env/env_condition_monitor.hpp",
        "include/uds_server/env/env_condition_monitor_base.hpp",
        "include/uds_server/services/auth/auth_roles.hpp",
        "include/uds_server/services/did/async_did_reader.hpp",
        "include/uds_server/services/did/async_did_writer.hpp",
        "include/uds_server/services/did/did_access.hpp",
        "include/uds_server/services/did/did_pool.hpp",
        "include/uds_server/services/did/did_reader.hpp",
        "include/uds_server/services/did/did_writer.hpp",
        "include/uds_server/services/did/observers.hpp",
        "include/uds_server/services/did/read_by_did_periodically_service.hpp",
        "include/uds_server/services/did/read_by_did_service.hpp",
        "include/uds_server/services/did/static_did_reader.hpp",
        "include/uds_server/services/did/write_by_did_service.hpp",
        "include/uds_server/services/dtc/async_clear_dtc_client.hpp",
        "include/uds_server/services/dtc/async_control_dtc_settings_client.hpp",
        "include/uds_server/services/dtc/async_read_dtc_client.hpp",
        "include/uds_server/services/dtc/clear_dtc_client.hpp",
        "include/uds_server/services/dtc/clear_dtc_info_service.hpp",
        "include/uds_server/services/dtc/control_dtc_settings_client.hpp",
        "include/uds_server/services/dtc/control_dtc_settings_service.hpp",
        "include/uds_server/services/dtc/dtc_parser.hpp",
        "include/uds_server/services/dtc/read_dtc_client.hpp",
        "include/uds_server/services/dtc/read_dtc_info_service.hpp",
        "include/uds_server/services/ioctrl/input_output_control_service.hpp",
        "include/uds_server/services/ioctrl/input_output_control_settings.hpp",
        "include/uds_server/services/ioctrl/polling_client_requester.hpp",
        "include/uds_server/services/ioctrl/requester_interface.hpp",
        "include/uds_server/services/routine/async_routine_client.hpp",
        "include/uds_server/services/routine/routine_access.hpp",
        "include/uds_server/services/routine/routine_control_service.hpp",
        "include/uds_server/services/routine/routine_parser.hpp",
        "include/uds_server/services/routine/routine_registry.hpp",
        "include/uds_server/services/routine/routine_registry_base.hpp",
        "include/uds_server/services/routine/routine_types.hpp",
        "include/uds_server/services/security/async_key_comparator.hpp",
        "include/uds_server/services/security/async_level_controller.hpp",
        "include/uds_server/services/security/async_seed_fetcher.hpp",
        "include/uds_server/services/security/key_comparator.hpp",
        "include/uds_server/services/security/level_controller.hpp",
        "include/uds_server/services/security/level_unlocking_result_publisher.hpp",
        "include/uds_server/services/security/level_unlocking_result_publisher_base.hpp",
        "include/uds_server/services/security/observers.hpp",
        "include/uds_server/services/security/security_access.hpp",
        "include/uds_server/services/security/security_access_service.hpp",
        "include/uds_server/services/security/security_access_service_config.hpp",
        "include/uds_server/services/security/security_level_controller_parser.hpp",
        "include/uds_server/services/security/seed_fetcher.hpp",
        "include/uds_server/services/session/diagnostic_session_control_service.hpp",
        "include/uds_server/services/session/tester_present_service.hpp",
        "include/uds_server/services/shared/access_controller.hpp",
        "include/uds_server/services/shared/access_parser.hpp",
        "include/uds_server/services/shared/access_types.hpp",
        "include/uds_server/services/shared/diagnostic_service.hpp",
        "include/uds_server/services/shared/diagnostic_service_with_context.hpp",
        "include/uds_server/services/shared/format.hpp",
        "include/uds_server/services/shared/response_code.hpp",
        "include/uds_server/services/shared/service_access.hpp",
        "include/uds_server/services/shared/service_request.hpp",
        "include/uds_server/services/shared/service_response.hpp",
        "include/uds_server/services/shared/sid.hpp",
        "include/uds_server/services/shared/subfunction_access.hpp",
        "include/uds_server/session/default_session.hpp",
        "include/uds_server/session/predefined_session.hpp",
        "include/uds_server/session/session.hpp",
        "include/uds_server/session/session_base.hpp",
        "include/uds_server/session/session_event_publisher.hpp",
        "include/uds_server/session/session_event_publisher_base.hpp",
        "include/uds_server/session/session_manager.hpp",
        "include/uds_server/session/session_manager_base.hpp",
        "include/uds_server/session/session_observer.hpp",
        "include/uds_server/visibility_control.hpp",
    ],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/automotive_diagnose/common",
        "@apex//grace/automotive_diagnose/uds_msgs",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "uds_server_node",
    srcs = ["src/uds_server_main.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        ":uds_protocol",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
    ],
)

ament_pkg_resources(
    name = "uds_server_resources",
    package = "uds_server",
    resources = {
        ":uds_server_node": "executable",
    },
    visibility = ["//visibility:public"],
)

ros_pkg(
    name = "uds_server",
    description = "The implementation of the UDS Server ISO-14229.",
    lib_executables = ["uds_server_node"],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "uds_server",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

filegroup(
    name = "doc_files",
    srcs = glob(["include/**"]) + [
        "settings/default.yaml",
    ],
    visibility = ["//visibility:public"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
