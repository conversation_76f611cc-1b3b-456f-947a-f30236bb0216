# Copyright 2022 Apex.AI, Inc
# All rights reserved.

cmake_minimum_required(VERSION 3.5)

project(uds_server)

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()

ament_auto_add_library(uds_protocol
  src/addons/security/level_controller_base.cpp
  src/bringup/uds_builder.cpp
  src/bringup/uds_config_parser.cpp
  src/com/default_server_timeout_p4_handler.cpp
  src/com/request_response_tracker.cpp
  src/com/request_subscriber.cpp
  src/com/response_publisher.cpp
  src/com/service_hub.cpp
  src/env/env_condition_monitor.cpp
  src/services/did/async_did_reader.cpp
  src/services/did/async_did_writer.cpp
  src/services/did/did_pool.cpp
  src/services/did/read_by_did_service.cpp
  src/services/did/read_by_did_periodically_service.cpp
  src/services/did/static_did_reader.cpp
  src/services/did/write_by_did_service.cpp
  src/services/dtc/async_clear_dtc_client.cpp
  src/services/dtc/async_control_dtc_settings_client.cpp
  src/services/dtc/async_read_dtc_client.cpp
  src/services/dtc/clear_dtc_info_service.cpp
  src/services/dtc/control_dtc_settings_service.cpp
  src/services/dtc/dtc_parser.cpp
  src/services/dtc/read_dtc_info_service.cpp
  src/services/ioctrl/input_output_control_service.cpp
  src/services/ioctrl/input_output_control_settings.cpp
  src/services/ioctrl/polling_client_requester.cpp
  src/services/routine/async_routine_client.cpp
  src/services/routine/routine_control_service.cpp
  src/services/routine/routine_parser.cpp
  src/services/routine/routine_registry.cpp
  src/services/security/async_key_comparator.cpp
  src/services/security/async_level_controller.cpp
  src/services/security/async_seed_fetcher.cpp
  src/services/security/level_unlocking_result_publisher.cpp
  src/services/security/security_access_service.cpp
  src/services/security/security_level_controller_parser.cpp
  src/services/session/diagnostic_session_control_service.cpp
  src/services/session/tester_present_service.cpp
  src/services/shared/access_controller.cpp
  src/services/shared/access_parser.cpp
  src/services/shared/access_types.cpp
  src/services/shared/format.cpp
  src/services/shared/service_request.cpp
  src/session/default_session.cpp
  src/session/session.cpp
  src/session/session_event_publisher.cpp
  src/session/session_manager.cpp
)

ament_auto_add_executable(uds_server_node
  src/uds_server_main.cpp
)

apex_set_compile_options(uds_protocol uds_server_node)

# Testing
if(BUILD_TESTING)
    # exclude uncrusfity to use clang-format instead
    # TODO(bartosz.burda): remove axivion from exclude in #19942
    list(APPEND AMENT_LINT_AUTO_EXCLUDE ament_cmake_uncrustify)
    # run common linters
    find_package(ament_lint_auto REQUIRED)
    find_package(ament_cmake_gmock REQUIRED)
    ament_lint_auto_find_test_dependencies()

    # Unit test and component test
    set(UNIT_TEST_UDS_SERVER test_${PROJECT_NAME})
    ament_add_gmock(${UNIT_TEST_UDS_SERVER}
      test/unit/test_access_controller.cpp
      test/unit/test_access_parser.cpp
      test/unit/test_access_types.cpp
      test/unit/test_async_clear_dtc_client.cpp
      test/unit/test_async_control_dtc_settings.cpp
      test/unit/test_async_did_reader.cpp
      test/unit/test_async_did_writer.cpp
      test/unit/test_async_key_comparator.cpp
      test/unit/test_async_level_controller.cpp
      test/unit/test_async_read_dtc_client.cpp
      test/unit/test_async_routine_client.cpp
      test/unit/test_async_seed_fetcher.cpp
      test/unit/test_common_level_controller.cpp
      test/unit/test_control_dtc_settings_service.cpp
      test/unit/test_default_session.cpp
      test/unit/test_diagnostic_session_control_service.cpp
      test/unit/test_did_pool.cpp
      test/unit/test_dtc_parser.cpp
      test/unit/test_env_condition_monitor.cpp
      test/unit/test_individual_level_controller.cpp
      test/unit/test_level_controller_base.cpp
      test/unit/test_level_unlocking_result_publisher.cpp
      test/unit/test_permissive_level_controller.cpp
      test/unit/test_read_by_did_service.cpp
      test/unit/test_read_by_did_periodically_service.cpp
      test/unit/test_read_dtc_info_service.cpp
      test/unit/test_request_response_tracker.cpp
      test/unit/test_request_subscriber.cpp
      test/unit/test_response_publisher.cpp
      test/unit/test_routine_control_service.cpp
      test/unit/test_routine_parser.cpp
      test/unit/test_routine_registry.cpp
      test/unit/test_security_access_service.cpp
      test/unit/test_security_delay.cpp
      test/unit/test_security_level_controller_parser.cpp
      test/unit/test_service_hub.cpp
      test/unit/test_session_event_publisher.cpp
      test/unit/test_session_manager.cpp
      test/unit/test_session.cpp
      test/unit/test_static_did_reader.cpp
      test/unit/test_tester_present_service.cpp
      test/unit/test_uds_builder.cpp
      test/unit/test_uds_config_parser.cpp
      test/unit/test_write_by_did_service.cpp
      # generated settings file
      test/settings/uds_server_settings.cpp
    )

    # include uds_protocol directories and also test dir
    target_include_directories(${UNIT_TEST_UDS_SERVER}
      PRIVATE
      "test")
    target_link_libraries(${UNIT_TEST_UDS_SERVER} uds_protocol)

    # set up integration tests in cpp
    set(TEST_INTEGRATION_UDS_SERVER test_${PROJECT_NAME}_integration)
    apex_test_tools_add_gtest(${TEST_INTEGRATION_UDS_SERVER}
      test/integration/test_clear_dtc.cpp
      test/integration/test_control_dtc_settings.cpp
      test/integration/test_read_did.cpp
      test/integration/test_read_did_periodically.cpp
      test/integration/test_read_dtc.cpp
      test/integration/test_routine_control.cpp
      test/integration/test_security_access.cpp
      test/integration/test_write_did.cpp
      # shared files
      test/shared/uds_server_integration_fixture.cpp
      # generated settings file
      test/settings/uds_server_settings.cpp
    )

    # include uds_protocol directories and also test dir
    target_include_directories(${TEST_INTEGRATION_UDS_SERVER}
      PRIVATE
      "test")
    target_link_libraries(${TEST_INTEGRATION_UDS_SERVER} uds_protocol)
endif()

ament_auto_package(INSTALL_TO_SHARE settings)
