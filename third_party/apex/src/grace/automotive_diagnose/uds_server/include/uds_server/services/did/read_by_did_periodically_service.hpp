/// \copyright Copyright 2022 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The service that handle SID=0x2A Read By DID

#ifndef UDS_SERVER__SERVICES__DID__READ_BY_DID_PERIODICALLY_SERVICE_HPP_
#define UDS_SERVER__SERVICES__DID__READ_BY_DID_PERIODICALLY_SERVICE_HPP_

#include <algorithm>  // for std::copy
#include <chrono>
#include <cstdint>
#include <memory>  // for std::shared_ptr
#include <optional>

#include "diagnostic_common/node_context.hpp"
#include "diagnostic_common/timer/timer_base.hpp"  // for Timer
#include "logging/logging_macros.hpp"
#include "uds_server/bringup/config.hpp"
#include "uds_server/services/did/did_pool.hpp"  // for DidReadersPool
#include "uds_server/services/did/observers.hpp"  // for DidReaderObserver
#include "uds_server/services/shared/diagnostic_service.hpp"  // for DiagnosticService
#include "uds_server/visibility_control.hpp"


namespace apex::uds_server::services::did
{
using UdsTransportMessage = uds_msgs::msg::UdsTransportMessage;

/// @brief Masks for DID parse
constexpr uint16_t MASK_PERIODIC_PREID = 0xF200;
constexpr uint16_t MASK_PERIODIC_RESID = 0x00FF;
/// @brief min and max message length for request
constexpr int REQ_MSG_LNGTH_MIN = 2;  // SID + TM >> only valid for stop request
constexpr int REQ_MSG_LNGTH_MAX = 10;  // SID + TM + 8 DID LOW ID
/// @brief data indexes in request message
constexpr int INDEX_TM = 1;  // TM Index
constexpr int INDEX_DID = 2;  // DID Index
/// @brief max size of periodic element list
constexpr int MAX_NUM_OF_PERIODIC = 256;
/// @brief defined periods for did read
constexpr int PERIOD_TM_FAST_DEFAULT =
  10;  // For "TM_FAST"; as ms, precise timer would be more efficient
constexpr int PERIOD_TM_MID_DEFAULT =
  20;  // For "TM_MID"; as ms, precise timer would be more efficient
constexpr int PERIOD_TM_SLOW_DEFAULT =
  30;  // For "TM_SLOW"; as ms, precise timer would be more efficient
/// @brief maximum size of uds response message
constexpr int RES_MSG_LENGTH_MAX = 128;  // according to uds standart it is 0x0FFF - 4095


/// @brief Data type for transmission mode
enum class TransmissionModeDataType : uint8_t
{
  TM_RESERVED_BOT = 0,
  TM_SLOW = 1,
  TM_MID,
  TM_FAST,
  STOP_SENDING,
  TM_RESERVED_TOP = 5
};

struct ReadByDidDataPeriodicallyConfig
{
  std::chrono::milliseconds slow_read_period_ms = std::chrono::milliseconds(PERIOD_TM_SLOW_DEFAULT);
  std::chrono::milliseconds mid_read_period_ms = std::chrono::milliseconds(PERIOD_TM_MID_DEFAULT);
  std::chrono::milliseconds fast_read_period_ms = std::chrono::milliseconds(PERIOD_TM_FAST_DEFAULT);
};
/// \struct ReadByDidDataPeriodically
/// \brief The DID Data structure that holds current DID readers cache, requested DIDs and response
/// data.
struct ReadByDidDataPeriodically
{
  /// \brief Take the data by setting the internal state using request data and did_pool DidReaders.
  /// \param[in] request The service request with Read DID data.
  /// \param[in] did_pool The DID pool for fetching the requested readers.
  void take(const shared::ServiceRequest & request,
            const std::shared_ptr<DidReadersPool> & did_pool);

  /// \brief Cleanup the data.
  void cleanup();

  /// \brief Check if the data is valid/
  /// \return true If take was called and the request had proper data.
  /// \return false If data is invalid.
  bool is_valid() const;

  /// \brief Check if this data is available to take.
  /// \return true If the data is available to take().
  /// \return false If the data shall not be taken
  bool is_available() const;

  /// \brief If the data is not valid, take the error reason.
  /// \return shared::UdsResponseCode the NRC that should be sent to the tester.
  shared::UdsResponseCode error_reason() const;

  /// \brief Set nrc.
  void set_error(shared::UdsResponseCode error);

  /// \brief The internal buffer for aggregating the response bytes.
  static_vector<uint8_t> response_data{RES_MSG_LENGTH_MAX};

  /// \brief The flag that marks, if the data is available to take.
  bool available{true};

  /// \brief The purpose of this function is remove ID in periodic scan list
  void removeDataID(const uint16_t dataID);

  /// \brief The purpose of this function is add ID into periodic scan list
  void addDataID(const uint16_t dataID,
                 const TransmissionModeDataType period,
                 const std::shared_ptr<DidReader> & reader);

  /// \brief Check DID's periods and set period activation
  void updateTimeTrigger(void);

  /// \brief Set request for application
  void periodicRequestDataApplication(DidReader * reader);

  /// \brief Scan List Data Type
  struct PeriodicScanListType
  {
    TransmissionModeDataType data_send_period;
    DidReader * reader = nullptr;
  };

  /// \brief Did map
  std::unordered_map<uint16_t, PeriodicScanListType> periodic_scan_list;

  /// \brief Boolean to hold if closed request has been received
  bool isCloseRequested = false;

  /// \brief Freeze periodic messages
  bool freezePeriodicMsg = false;

  /// @brief flags for periodic timer starter
  struct testerRequestPeriods
  {
    bool slowTimerActive = false;
    bool midTimerActive = false;
    bool fastTimerActive = false;
  } m_testerRequestPeriods;

  /// @brief ids for periodic
  std::optional<uint32_t> slow_timer_id;
  std::optional<uint32_t> mid_timer_id;
  std::optional<uint32_t> fast_timer_id;

  uint64_t request_id;

  uds_msgs::msg::UdsAddress tester_address;

private:
  /// \brief The optional negative response code that holds value if error occurred.
  apex::optional<shared::UdsResponseCode> nrc{apex::nullopt};
};

/// \class ReadByDidPeriodicallyService
/// \brief TBD
class UDS_SERVER_PUBLIC ReadByDidPeriodicallyService : public shared::DiagnosticService,
                                                       public DidReaderObserver
{
public:
  /// \brief Construct a new Read By Did Service object.
  /// \param[in] response_pub The publisher of a response.
  /// \param[in] did_pool The pool with the DID Readers.
  /// \param[in] context The Node Context for logger creation
  ReadByDidPeriodicallyService(shared::ResponsePublisherPtr response_pub,
                               std::shared_ptr<DidReadersPool> did_pool,
                               const diagnostic_common::NodeContextRef & context,
                               std::shared_ptr<diagnostic_common::timer::TimerBase> timer,
                               ReadByDidDataPeriodicallyConfig config);

  /// \copydoc DiagnosticService::get_sid()
  shared::UdsSid get_sid() const noexcept override
  {
    return shared::UdsSid::READ_DATA_BY_PERIODIC_IDENTIFIER_REQ;
  }

  bool busy() const override
  {
    return false;
  }

  /// \brief Get the reference to the did data.
  /// \return ReadByDidDataPeriodically& The reference to the DID data.
  ReadByDidDataPeriodically & get_data()
  {
    return m_did_data;
  }
  void clear_data()
  {
    ReadByDidDataPeriodically empty_data;
    m_did_data = empty_data;
  }

  /// \copydoc DidReaderObserver::notify_about_read_success()
  void notify_about_read_success(const uint64_t request_id,
                                 const uint16_t did,
                                 const DataRecord & data) override;

  /// \copydoc DidReaderObserver::notify_about_read_error()
  void notify_about_read_error(const uint64_t request_id, const uint16_t did) override;

  bool breakpoint();

  /// \brief Function to be called periodically on timeout
  /// \param[in] timeMode The time periods of DID
  void periodical_timer_callback(const TransmissionModeDataType timeMode)
  {
    APEX_DEBUG(m_logger, "time_mode = ", static_cast<int>(timeMode));

    const auto & periodic_scan_list_local = get_data().periodic_scan_list;

    for (const auto & [data_id, item] : periodic_scan_list_local) {
      if (item.data_send_period == timeMode) {
        const auto didreader = item.reader;
        get_data().periodicRequestDataApplication(didreader);
      }
    }
  }

  /// \brief The timer callback handler
  void timer_callback_handler(uint32_t ID)
  {
    auto Iter = std::find_if(
      m_timerCallbackVector.begin(),
      m_timerCallbackVector.end(),
      [ID](struct timerCallback & timerCallback) { return timerCallback.timerId == ID; });
    timerCallback & timerCallback = *Iter;
    APEX_DEBUG(m_logger, "request id = ", static_cast<int>(get_data().request_id));
    APEX_DEBUG(m_logger, "received ID ", static_cast<int>(ID));
    APEX_DEBUG(m_logger, "slow timer ID ", static_cast<int>(get_data().slow_timer_id.value_or(-1)));
    APEX_DEBUG(m_logger, "mid timer ID ", static_cast<int>(get_data().mid_timer_id.value_or(-1)));
    APEX_DEBUG(m_logger, "fast timer ID ", static_cast<int>(get_data().fast_timer_id.value_or(-1)));

    periodical_timer_callback(timerCallback.timerPace);
  }

protected:
  /// \copydoc shared::DiagnosticService::supports_subfunction
  bool supports_subfunction() const override
  {
    return false;
  }

  /// \copydoc shared::DiagnosticService::has_valid_msg_size
  bool has_valid_msg_size(const shared::ServiceRequest & request) const override;

  /// \copydoc shared::DiagnosticService::do_process_request
  void do_process_request(const shared::ServiceRequest & request) override;

  /// \copydoc shared::DiagnosticService::cancel_active_requests()
  void cancel_active_requests() override;

private:
  /// \brief Remove all active timers
  void remove_timeouts();

  /// \brief The pool with a DID Readers.
  std::shared_ptr<DidReadersPool> m_did_pool;

  /// \brief Node logger.
  logging::Logger<> m_logger;

  /// \brief The timer
  std::shared_ptr<diagnostic_common::timer::TimerBase> m_timer;

  bool m_breakpoint = false;

  struct timerCallback
  {
    uint32_t timerId;
    TransmissionModeDataType timerPace;
  };

  std::vector<timerCallback> m_timerCallbackVector;

  ReadByDidDataPeriodicallyConfig m_config;

  /// \brief Convert the data to the positive response.
  /// \return shared::PositiveResponse The positive response build from this data.
  shared::PositiveResponse to_positive_response() const
  {
    return shared::PositiveResponse{
      m_did_data.request_id,
      m_did_data.tester_address,
      [data_ptr = &m_did_data.response_data](auto & payload) {
        (void)std::copy(data_ptr->begin(), data_ptr->end(), std::back_inserter(payload));
      }};
  }


  /// \brief The reference to the DID Data.
  ReadByDidDataPeriodically m_did_data;
};

}  // namespace apex::uds_server::services::did

#endif  // UDS_SERVER__SERVICES__DID__READ_BY_DID_SPERIODICALLY_ERVICE_HPP_
