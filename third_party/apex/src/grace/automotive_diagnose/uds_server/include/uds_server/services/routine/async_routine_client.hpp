/// \copyright Copyright 2023 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The Async Routine Client used for communication with application that exposes Routine
/// Service via Apex Services.

#ifndef UDS_SERVER__SERVICES__ROUTINE__ASYNC_ROUTINE_CLIENT_HPP_
#define UDS_SERVER__SERVICES__ROUTINE__ASYNC_ROUTINE_CLIENT_HPP_

#include <functional>  // for std::function
#include <memory>  // for std::shared_ptr<>
#include <utility>  // for std::move
#include <vector>  // for std::vector

#include "containers/static_vector.hpp"  // for static_vector
#include "cpputils/optional.hpp"  // for optional
#include "diagnostic_common/com/service_client.hpp"  // for ServiceClient
#include "diagnostic_common/node_context.hpp"  // for NodeContextRef
#include "uds_server/services/routine/routine_types.hpp"  // for RoutineClient, RoutineId, RoutineSubfunction
#include "uds_server/visibility_control.hpp"  // for UDS_SERVER_PUBLIC

#include "uds_msgs/srv/uds_routine_control.hpp"  // for UdsRoutineControl

namespace apex
{
namespace uds_server
{
namespace services
{
namespace routine
{
using RoutineControlResponse = uds_msgs::srv::UdsRoutineControl::Response::BorrowedType;
using RoutineControlServiceClient =
  diagnostic_common::com::ServiceClient<uds_msgs::srv::UdsRoutineControl>;
using StopRoutineCb = std::function<void(const RoutineId)>;

/// \struct RoutineServiceResource
/// \brief The routine service resource with routine data.
struct RoutineServiceResource
{
  /// \brief Construct a new Routine Service Resource object
  /// \param[in] id The ID of a routine.
  /// \param[in] timer A pointer to the timer used for stopping the routine.
  RoutineServiceResource(const RoutineId id,
                         timer_service::timer_ptr timer,
                         StopRoutineCb stop_routine_cb)
  : routine_id{id}, timer{std::move(timer)}, stop_routine_cb{std::move(stop_routine_cb)}
  {
  }

  /// \brief Stop routine
  void stop_routine()
  {
    stop_routine_cb(routine_id);
  }

  /// \brief The ID of a routine.
  const RoutineId routine_id;
  /// \brief The subfunction of the routine.
  RoutineSubfunction subfunction{RoutineSubfunction::UNKNOWN};
  /// \brief The flag that tells if this routine request is in progress.
  bool active{false};
  /// \brief A pointer to the timer used for stopping the routine.
  timer_service::timer_ptr timer;
  /// \brief The timeout value.
  optional<uint64_t> timeout_ms{nullopt};
  /// \brief The stop routine callback to be invoked by the timer.
  StopRoutineCb stop_routine_cb;
  /// \brief the ID of a request
  uint64_t request_id{0U};
};

/// \class AsyncRoutineClient
/// \brief The AsyncRoutineClient that sends request to the remote service to control
/// the given routine.
class UDS_SERVER_PUBLIC AsyncRoutineClient : public RoutineClient,
                                             public RoutineControlServiceClient
{
  using ControlType = uds_msgs::ControlType;
  using ControlStatus = uds_msgs::ControlStatus;

public:
  /// \brief Construct a new Async Routine Client object.
  /// \param[in] node_context The context of a parent node.
  /// \param[in] service_name The routine service name.
  /// \param[in] timer The timer for handling service requests timeouts.
  /// \param[in] supported_routines The vector of routines that this client supports.
  AsyncRoutineClient(const diagnostic_common::NodeContextRef & node_context,
                     const apex::string_strict256_t & service_name,
                     const std::vector<RoutineId> & supported_routines);

  /// \copydoc RoutineClient::request_routine
  void request_routine(const uint64_t request_id,
                       const RoutineId routine_id,
                       const RoutineSubfunction subfunction,
                       const optional<uint64_t> & timeout_ms,
                       WriteRoutineOptionsCallback write_cb) override;

  /// \copydoc RoutineClient::is_available
  bool is_available() const override;

private:
  /// \copydoc diagnostic_common::com::ServiceClient::on_response
  void on_response(const RoutineControlResponse & msg) override;

  /// \brief notify about an error while sending a request.
  void on_error();

  executor::subscription_list get_triggering_subscriptions_impl() const override;

  bool execute_impl() override;

  /// \brief Report error in routine control.
  /// \param[in] request_id The ID of a request for which error occurred.
  /// \param[in] error The routine error reason.
  void report_error(const uint64_t request_id, const RoutineError error);

  /// \brief Find the service resource using the RoutineId.
  /// \param[in] id The routine ID unique for the resource.
  /// \return The pointer to the service resource or nullptr if there is no resource with given ID.
  RoutineServiceResource * find_resource(const RoutineId id);

  /// \brief Find the service resource using the request ID.
  /// \param[in] id The request ID unique for the resource.
  /// \return The pointer to the service resource or nullptr if there is no resource with given ID.
  RoutineServiceResource * find_resource(const uint64_t request_id);

  /// \brief Converts RoutineSubfunction to corresponding ControlType IDL enum.
  /// \param[in] subfunction The routine subfunction.
  /// \return ControlType The IDL control type.
  static ControlType subfunction_to_control_type(const RoutineSubfunction subfunction);

  /// \brief Converts the ControlStatus to corresponding RoutineError enum.
  /// \param[in] control_status The ControlStatus from the IDL.
  /// \return RoutineError The Routine Error
  static RoutineError control_status_to_routine_error(const ControlStatus control_status);

private:
  /// \brief Current Routine Request data.
  static_vector<RoutineServiceResource> m_client_resources;
  /// \brief Currently processing request. If there is no routine actively processing the value is
  /// nullopt.
  optional<uint64_t> m_current_request;
};

}  // namespace routine
}  // namespace services
}  // namespace uds_server
}  // namespace apex

#endif  // UDS_SERVER__SERVICES__ROUTINE__ASYNC_ROUTINE_CLIENT_HPP_
