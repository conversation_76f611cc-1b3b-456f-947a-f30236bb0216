/// \copyright Copyright 2022 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The pool of all DIDs with their respective readers and writers.

#ifndef UDS_SERVER__SERVICES__DID__DID_POOL_HPP_
#define UDS_SERVER__SERVICES__DID__DID_POOL_HPP_

#include <memory>  // for std::shared_ptr
#include <utility>  // for pair

#include "containers/static_vector.hpp"  // for apex::static_vector<>
#include "cpputils/noncopyable.hpp"
#include "uds_server/services/did/did_access.hpp"  // for DidAccess
#include "uds_server/services/did/did_reader.hpp"  // for DidReader
#include "uds_server/services/did/did_writer.hpp"  // for DidWriter
#include "uds_server/services/did/observers.hpp"  // for DidWriterObserver & DidReaderObserver
#include "uds_server/visibility_control.hpp"  // for UDS_SERVER_PUBLIC

namespace apex
{
namespace uds_server
{
namespace services
{
namespace did
{
using DidReaderPtr = std::shared_ptr<DidReader>;
using DidWriterPtr = std::shared_ptr<DidWriter>;
using DidReaderResult = std::pair<shared::AccessStatus, DidReaderPtr>;
using DidWriterResult = std::pair<shared::AccessStatus, DidWriterPtr>;
using DidAccessPtr = std::shared_ptr<DidAccess>;

/// \struct DidEntry
/// \brief The structure that hold did value and its reader
/// and a writer.
struct DidEntry
{
  /// \brief The DID value.
  uint16_t m_did;
  /// \brief The Reader of the DID.
  DidReaderPtr m_reader;
  /// \brief The Writer of the DID.
  DidWriterPtr m_writer;
};
using DidEntries = static_vector<DidEntry>;

/// \class DidReadersPool
/// \brief The interface for a pool of all DidReaders.
class DidReadersPool : private apex::NonCopyable
{
public:
  DidReadersPool() = default;
  /// \brief Destroy the Did Readers Pool object
  virtual ~DidReadersPool() noexcept = default;

  /// \brief Try to find a DID Reader for a given did value
  /// that fulfills security conditions.
  /// \param did The DID value.
  /// \param token The Access token.
  /// \param req_sid The ID of a service which requests the reader.
  /// \return DidReaderResult The pair od AccessStatus and a pointer to the
  /// reader. The pointer is empty if the AccessStatus is not ALLOWED.
  virtual DidReaderResult find_did_reader(const uint16_t did,
                                          const shared::AccessToken & token,
                                          const shared::UdsSid req_sid) const = 0;
};

/// \class DidWritersPool
/// \brief The interface for a pool of all DidWriters
class DidWritersPool : private apex::NonCopyable
{
public:
  DidWritersPool() = default;
  /// \brief Destroy the Did Writers Pool object
  virtual ~DidWritersPool() noexcept = default;

  /// \brief Try to find a DID Writer for a given did value
  /// that fulfills security conditions.
  /// \param did The DID value.
  /// \param token The Access token.
  /// \param req_sid The ID of a service which requests the writer.
  /// \return DidWriterResult The pair od AccessStatus and a pointer to the
  /// writer. The pointer is empty if the AccessStatus is not ALLOWED.
  virtual DidWriterResult find_did_writer(const uint16_t did,
                                          const shared::AccessToken & token,
                                          const shared::UdsSid req_sid) const = 0;
};

/// \class DidPool
/// \brief The pool of all available DIDs Readers and Writers
/// together with their security accesses.
class UDS_SERVER_PUBLIC DidPool : public DidReadersPool, public DidWritersPool
{
public:
  /// \brief Construct a new Did Pool object.
  /// \param[in] did_access The Did Access object that defines
  /// the access to the DIDs.
  /// \param[in] did_entries The vector of DID entries.
  DidPool(DidAccessPtr did_access, DidEntries && did_entries);

  /// \copydoc DidReadersPool::find_did_reader
  DidReaderResult find_did_reader(const uint16_t did,
                                  const shared::AccessToken & token,
                                  const shared::UdsSid req_sid) const override;

  /// \copydoc DidWritersPool::find_did_writer
  DidWriterResult find_did_writer(const uint16_t did,
                                  const shared::AccessToken & token,
                                  const shared::UdsSid req_sid) const override;

  /// \brief Register a DID Reader Observer object for all DID Readers.
  /// \param[in] observer The DID Reader Observer object.
  void register_did_readers_observer(const std::shared_ptr<DidReaderObserver> & observer);

  void register_did_periodic_readers_observer(const std::shared_ptr<DidReaderObserver> & observer);

  /// \brief Register a DID Writer Observer object for all DID Writer.
  /// \param[in] observer The DID Writer Observer object.
  void register_did_writers_observer(const std::shared_ptr<DidWriterObserver> & observer);

private:
  /// \brief Finds the DidEntry in the entries vector.
  /// Returns optional with value if entry was found, or empty
  /// if there is no entry for given did value.
  /// \param did The value of the DID for which entry should be get.
  /// \return apex::optional<DidEntry> The optional with value if entry
  /// was found or empty optional otherwise.
  apex::optional<DidEntry> find_entry(const uint16_t did) const;

private:
  /// \brief The Did Access Controller pointer, used
  /// for checking access to the DID readers and writers.
  const DidAccessPtr m_did_access;
  /// \brief The list of DID entries in the pool.
  const DidEntries m_did_entries;
};

}  // namespace did
}  // namespace services
}  // namespace uds_server
}  // namespace apex

#endif  // UDS_SERVER__SERVICES__DID__DID_POOL_HPP_
