/// \copyright Copyright 2022 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief Class for managing of the UDS Sessions.

#ifndef UDS_SERVER__SESSION__SESSION_MANAGER_HPP_
#define UDS_SERVER__SESSION__SESSION_MANAGER_HPP_

#include <memory>  // for std::shared_ptr<>
#include <mutex>  // for std::mutex

#include "containers/static_vector.hpp"  // for static_vector<>
#include "cpputils/optional.hpp"  // for optional
#include "diagnostic_common/timer/timer_base.hpp"  // for TimerBase
#include "uds_server/session/default_session.hpp"  // for DefaultSession
#include "uds_server/session/predefined_session.hpp"  // for PredefinedSession
#include "uds_server/session/session.hpp"  // for Session
#include "uds_server/session/session_event_publisher_base.hpp"  // for SessionEventPublisherBase
#include "uds_server/session/session_manager_base.hpp"  // for SessionManagerBase

namespace apex
{
namespace uds_server
{
namespace session
{
using NonDefaultSessions = static_vector<std::shared_ptr<Session>>;

/// \class SessionManager
/// \brief The Session Manager class that aggregates all available
/// diagnostic sessions and provide an API to manage them.
class UDS_SERVER_PUBLIC SessionManager : public SessionManagerBase
{
private:
  /// \brief Session change result
  enum class SessionChangeResult : uint8_t
  {
    FAILED,  ///< operation failed
    TRANSITION_SUCCEEDED,  ///< session changed to a different session
    SESSION_RESET,  ///< session reset (changed to the same session)
  };

public:
  /// \brief Construct a new Session Manager object.
  /// \param[in] default_session The default UDS session.
  /// \param[in] non_default_sessions The vector of supported non-default sessions.
  /// \param[in] timer The pointer to the Timer used for a session timeout.
  SessionManager(std::shared_ptr<DefaultSession> default_session,
                 NonDefaultSessions non_default_sessions,
                 std::shared_ptr<SessionEventPublisherBase> session_event_pub,
                 std::shared_ptr<diagnostic_common::timer::TimerBase> timer);

  /// \copydoc SessionManagerBase::change_session
  bool change_session(const SessionId id, const ClientId client_id) override;

  /// \copydoc SessionManagerBase::register_client
  bool register_client(const ClientId client_id) override;

  /// \copydoc SessionManagerBase::generate_access_token
  services::shared::AccessToken generate_access_token(const ClientId client_id) const override;

  /// \copydoc SessionManagerBase::get_session_timing_params
  TimingParameters get_session_timing_params() const override;

  /// \copydoc SessionManagerBase::change_security_level
  void change_security_level(const services::security::SecurityLevel new_level) override;

  /// \copydoc SessionManagerBase::reset_security_level
  void reset_security_level() override;

  /// \copydoc SessionManagerBase::change_auth_role
  void change_auth_role(const ClientId client_id,
                        const services::auth::AuthRoles new_role) override;

  /// \copydoc SessionManagerBase::refresh_active_session
  void refresh_active_session() override;

  /// \copydoc SessionManagerBase::is_default_session_active
  bool is_default_session_active() const override
  {
    return m_active_session_id == static_cast<SessionId>(PredefinedSession::DEFAULT);
  }

  /// \copydoc SessionManagerBase::register_observer
  void register_observer(SessionObserver * observer) override
  {
    m_session_observer = observer;
  }

  /// \copydoc SessionManagerBase::is_session_supported
  bool is_session_supported(const SessionId session_id) const override;

private:
  /// \brief Get a pointer to the active session.
  /// \return std::shared_ptr<SessionBase> with the active session.
  std::shared_ptr<SessionBase> get_active_session() const;

  /// \brief Change the session to the one with given ID.
  /// \param id The ID of a new active session.
  /// \return true if session was successfully changed or false otherwise.
  SessionChangeResult change_session(const SessionId id);

  /// \brief Starts the timer with the current session S3 time.
  void start_timer();

  /// \brief Stops the timer and invalidate the timer_task_id.
  void stop_timer();

private:
  /// \brief The default session.
  const std::shared_ptr<DefaultSession> m_default_session;
  /// \brief The vector of non-default sessions.
  const NonDefaultSessions m_non_default_sessions;
  /// \brief The pointer with a Timer that handles session timeout.
  const std::shared_ptr<diagnostic_common::timer::TimerBase> m_timer;
  /// \brief The ID of active session.
  SessionId m_active_session_id;
  /// \brief The observer of session changed
  SessionObserver * m_session_observer;
  /// \brief The optional, that holds timer task ID if the m_timer is armed.
  optional<uint32_t> m_timer_task_id;
  /// \brief The mutex object to protect session change.
  std::mutex m_mutex;
  /// \brief The session event publisher.
  std::shared_ptr<SessionEventPublisherBase> m_session_event_pub;
};

}  // namespace session
}  // namespace uds_server
}  // namespace apex

#endif  // UDS_SERVER__SESSION__SESSION_MANAGER_HPP_
