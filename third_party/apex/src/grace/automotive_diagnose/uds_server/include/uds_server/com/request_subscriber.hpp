/// \copyright Copyright 2022 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The Transport Layer request subscriber

#ifndef UDS_SERVER__COM__REQUEST_SUBSCRIBER_HPP_
#define UDS_SERVER__COM__REQUEST_SUBSCRIBER_HPP_

#include <memory>  // for std::shared_ptr

#include "diagnostic_common/node_context.hpp"  // for NodeContextPtr
#include "executor2/executable_item.hpp"  // for executable_item
#include "logging/logging.hpp"  // for apex::logging::Logger
#include "rclcpp/polling_subscription.hpp"  // for rclcpp::PollingSubscription
#include "uds_server/com/service_hub_base.hpp"
#include "uds_server/visibility_control.hpp"  // for UDS_SERVER_PUBLIC

#include "uds_msgs/msg/uds_transport_message.hpp"  // for UdsTransportMessage

namespace apex
{
namespace uds_server
{
namespace com
{
/// \class RequestSubscriber
/// \brief The subscriber of the transport layer service's requests.
class UDS_SERVER_PUBLIC RequestSubscriber : public executor::executable_item
{
public:
  /// \brief Construct a new Request Subscriber object.
  /// \param[in] node_context The parent node context.
  /// \param[in] subscribe_topic The subscription topic for receiving the service's requests.
  /// \param[in] service_hub The service hub object ptr, that will dispatch the request to the
  /// service.
  /// \param[in] logical_address The logical address of the server.
  RequestSubscriber(const diagnostic_common::NodeContextRef & node_context,
                    const string_strict256_t & subscribe_topic,
                    std::shared_ptr<ServiceHubBase> service_hub,
                    const uint16_t logical_address);

private:
  bool execute_impl() override;

  executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {m_transport_sub};
  }

private:
  /// \brief The subscriber of the UDS requests.
  const rclcpp::PollingSubscription<uds_msgs::msg::UdsTransportMessage>::SharedPtr m_transport_sub;
  /// \brief The Service Hub responsible for dispatching the request to the correct service.
  const std::shared_ptr<ServiceHubBase> m_service_hub;
  /// \brief The Request Subscriber logger object.
  logging::Logger<> m_logger;
  /// \brief The logical address of the server for identification in a UDS network.
  uint16_t m_logical_address;
};

}  // namespace com
}  // namespace uds_server
}  // namespace apex

#endif  // UDS_SERVER__COM__REQUEST_SUBSCRIBER_HPP_
