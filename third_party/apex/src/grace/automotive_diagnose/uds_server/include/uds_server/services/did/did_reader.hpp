/// \copyright Copyright 2022 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The DIdReader interface.

#ifndef UDS_SERVER__SERVICES__DID__DID_READER_HPP_
#define UDS_SERVER__SERVICES__DID__DID_READER_HPP_

#include <memory>  // for std::shared_ptr

#include "cpputils/noncopyable.hpp"
#include "uds_server/visibility_control.hpp"  // for UDS_SERVER_PUBLIC

namespace apex
{
namespace uds_server
{
namespace services
{
namespace did
{

// forward declaration
class DidReaderObserver;

/// \class DidReadHandler
/// \brief The DID Reader interface.
class UDS_SERVER_PUBLIC DidReader : private apex::NonCopyable
{
public:
  /// \brief Construct the Did handler.
  /// \param[in] did The DID supported by this handler.
  explicit DidReader(const uint16_t did) : m_did{did} {}

  /// \brief Destroy the Did Reader object.
  virtual ~DidReader() noexcept = default;

  /// \brief Check if the read is available.
  /// \return true If DID can be read
  /// \return false If DID can not be read
  virtual bool is_available() const = 0;

  /// \brief Read DID data.
  /// \param[in] request_id The ID of a request.
  virtual void read(const uint64_t request_id) = 0;

  /// \brief Register a DID Reader Observer object.
  /// \param[in] observer The DID Reader Observer object.
  void register_observer(const std::shared_ptr<DidReaderObserver> & observer)
  {
    m_observer = observer;
  }

  void register_periodic_observer(const std::shared_ptr<DidReaderObserver> & observer)
  {
    m_periodicObserver = observer;
  }

  /// \brief Get the DID value.
  /// \return uint16_t DID value.
  uint16_t get_did() const
  {
    return m_did;
  }

protected:
  /// \brief DID Reader Observer object.
  std::weak_ptr<DidReaderObserver> m_observer;
  std::weak_ptr<DidReaderObserver> m_periodicObserver;

private:
  /// \brief Data ID.
  const uint16_t m_did;
};

}  // namespace did
}  // namespace services
}  // namespace uds_server
}  // namespace apex

#endif  // UDS_SERVER__SERVICES__DID__DID_READER_HPP_
