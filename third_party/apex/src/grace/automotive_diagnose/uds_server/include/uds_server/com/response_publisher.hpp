/// \copyright Copyright 2022 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The object that is used to publish service responses
/// on transport topic.

#ifndef UDS_SERVER__COM__RESPONSE_PUBLISHER_HPP_
#define UDS_SERVER__COM__RESPONSE_PUBLISHER_HPP_

#include <memory>  // for std::shared_ptr
#include <mutex>  // for std::mutex

#include "diagnostic_common/node_context.hpp"  // for NodeContext
#include "uds_server/com/response_publisher_base.hpp"
#include "uds_server/session/session_manager_base.hpp"  // for SessionManagerBase
#include "uds_server/visibility_control.hpp"  // for UDS_SERVER_PUBLIC

namespace apex
{
namespace uds_server
{
namespace com
{
/// \class ResponsePublisher
/// \brief The implementation of ResponsePublisherBase
/// interface for publishing responses to the Transport Layer using DDS publisher type.
class UDS_SERVER_PUBLIC ResponsePublisher : public ResponsePublisherBase
{
public:
  /// \brief Construct a new Response Publisher object
  /// \param[in] node_context The context of parent node.
  /// \param[in] publish_topic The topic on which the response is published.
  /// \param[in] logical_address The logical address of the server.
  ResponsePublisher(const diagnostic_common::NodeContextRef & node_context,
                    const string_strict256_t & publish_topic,
                    const uint16_t logical_address);

  /// \copydoc ResponsePublisherBase::publish_positive_response
  void publish_positive_response(const services::shared::PositiveResponse & response) override;

  /// \copydoc ResponsePublisherBase::publish_negative_response
  void publish_negative_response(const services::shared::NegativeResponse & response) override;

private:
  /// \brief The publisher of the UDS responses from a services.
  const rclcpp::Publisher<uds_msgs::msg::UdsTransportMessage>::SharedPtr m_transport_pub;
  /// \brief The mutex for synchronization of transport pub object.
  std::mutex m_response_pub_mtx;
  /// \brief The logical address of the server for identification in a UDS network.
  uint16_t m_logical_address;
};

}  // namespace com
}  // namespace uds_server
}  // namespace apex

#endif  // UDS_SERVER__COM__RESPONSE_PUBLISHER_HPP_
