// Copyright 2025 Apex.AI, Inc.
// All rights reserved.
/// \brief Topic routing funtionality for the UDS gateway for efficient message forwarding.
#ifndef UDS_GATEWAY__TOPIC_MAPPING_HPP_
#define UDS_GATEWAY__TOPIC_MAPPING_HPP_
#include <optional>
#include <string>
#include <unordered_map>

#include "grace/automotive_diagnose/uds_gateway/uds_gateway/utilities.hpp"

#include "uds_msgs/msg/uds_transport_message.hpp"

namespace apex::automotive_diagnose::uds_gateway
{
/// @brief checks for the validity of the arguments passed.
/// @param tester_address UDS tester address of the message to be checked.
/// @param ecu_address UDS ecu address of the message to be checked.
/// @param source_topic the topic the message has been received from.
/// @param address_dest_topic_mappings mapping from a address [key] to a source topic [value].
/// @param address_source_topic_mappings mapping from a address [key] to a detination topic [value].
/// @return flag if the arguments are considered valid.
bool argument_validity_check(const TesterAddress tester_address,
                             const EcuAddress ecu_address,
                             const std::string & source_topic,
                             const AddressDestinationTopicMap & address_dest_topic_mappings,
                             const AddressSourceTopicMap & address_source_topic_mappings);

/// @brief determine the destination address to forward the message to. Checks for ambiguous
/// addresses and topics.
/// @param tester_address UDS tester address of the message to be checked.
/// @param ecu_address UDS ecu address of the message to be checked.
/// @param source_topic the topic the message has been received from.
/// @param address_source_topic_mappings mapping from a address [key] to a detination topic [value].
/// @return the topic the message should be forwarded to, an empty std::optional if not found.
/// @attention returned value must be checked if empty.
std::optional<UdsAddressType> find_destination_address(
  const TesterAddress tester_address,
  const EcuAddress ecu_address,
  const std::string & source_topic,
  const AddressSourceTopicMap & address_source_topic_mappings);

/// @brief determine the destination address to forward the message to based on a destination
/// address and a mapping.
/// @param destination_address the message containing a tester and a ecu address.
/// @param address_dest_topic_mappings mapping from a address [key] to a source topic [value].
/// @return the topic the message should be forwarded to, an empty std::optional if not found.
/// @attention returned value must be checked if empty.
std::optional<std::string> find_destination_topic(
  const UdsAddressType & destination_address,
  const AddressDestinationTopicMap & address_dest_topic_mappings);

/// @brief determine the destination address to forward the message to. Checks for ambiguous
/// addresses and topics.
/// @param msg the message containing a tester and a ecu address.
/// @param source_topic the topic the message has been received from.
/// @param address_dest_topic_mappings mapping from a address [key] to a source topic [value].
/// @param address_source_topic_mappings mapping from a address [key] to a detination topic [value].
/// @return the topic the message should be forwarded to, an empty std::optional if not found.
/// @attention returned value must be checked if empty.
std::optional<std::string> to_destination_topic(
  const uds_msgs::msg::UdsTransportMessage & msg,
  const std::string & source_topic,
  const AddressDestinationTopicMap & address_dest_topic_mappings,
  const AddressSourceTopicMap & address_source_topic_mappings);
}  // namespace apex::automotive_diagnose::uds_gateway

#endif  // UDS_GATEWAY__TOPIC_MAPPING_HPP_
