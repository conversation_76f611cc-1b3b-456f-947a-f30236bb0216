cc_library(
    name = "config_parser",
    srcs = [
        "config_parser.cpp",
    ],
    hdrs = [
        "config_parser.hpp",
    ],
    visibility = ["//grace/automotive_diagnose/uds_gateway:__subpackages__"],
    deps = [
        "//grace/automotive_diagnose/uds_gateway/uds_gateway:uds_gateway_node_lib",
        "@apex//grace/automotive_diagnose/uds_msgs",
    ],
)

filegroup(
    name = "config",
    srcs = ["example_config.yaml"],
    visibility = ["//visibility:public"],
)
