// Copyright 2025 Apex.AI, Inc.
// All rights reserved.

#include "grace/automotive_diagnose/uds_gateway/uds_gateway/config/config_parser.hpp"

#include <string>

#include "settings/construct/getters.hpp"
#include "settings/inspect.hpp"
#include "settings/inspect/types.hpp"

namespace apex::automotive_diagnose::uds_gateway::config
{

using apex::settings::inspect::array_view;
using apex::settings::inspect::dictionary_view;
using apex::settings::inspect::get;
using apex::settings::inspect::integer;
using apex::settings::inspect::maybe;
using apex::settings::inspect::node_view;
using apex::settings::inspect::string_view;

namespace
{

std::string get_publisher_topic(const settings::inspect::dictionary_view & cfg)
{
  const auto publish_topic = get<maybe<string_view>>(cfg, "publish_topic").value_or("");
  if (publish_topic.empty()) {
    throw std::runtime_error("Error: The config is missing a \"publish_topic\" value!");
  }
  return std::string{publish_topic};
}

std::string get_subscriber_topic(const settings::inspect::dictionary_view & cfg)
{
  const auto subscribe_topic = get<maybe<string_view>>(cfg, "subscribe_topic").value_or("");
  if (subscribe_topic.empty()) {
    throw std::runtime_error("Error: The config is missing a \"subscribe_topic\" value!");
  }
  return std::string{subscribe_topic};
}

std::vector<UdsAddressType> get_tester_addresses(const settings::inspect::dictionary_view & cfg)
{
  std::vector<UdsAddressType> res;
  const auto associated_addresses_view = get<maybe<array_view>>(cfg, "uds_tester_addresses");
  if (!associated_addresses_view) {
    throw std::runtime_error("Error: The config is missing a \"uds_tester_addresses\" value!");
  }

  for (auto & it : associated_addresses_view.value()) {
    const auto address = get<maybe<integer>>(it, "address");
    if (!address) {
      throw std::runtime_error("Error: The config is missing a \"address\" value!");
    }
    const UdsAddressType tester_address = static_cast<UdsAddressType>(address.value());
    res.push_back(tester_address);
  }
  return res;
}

std::vector<UdsAddressType> get_ecu_addresses(const settings::inspect::dictionary_view & cfg)
{
  const auto associated_addresses_view = get<maybe<array_view>>(cfg, "uds_ecu_addresses");
  if (!associated_addresses_view) {
    throw std::runtime_error("Error: The config is missing a \"uds_ecu_addresses\" value!");
  }

  std::vector<UdsAddressType> res;
  for (const auto & it : associated_addresses_view.value()) {
    const auto address = get<maybe<integer>>(it, "address");
    if (!address) {
      throw std::runtime_error("Error: The config is missing a \"address\" value!");
    }
    res.push_back(static_cast<UdsAddressType>(address.value()));
  }
  return res;
}

NodeConfig get_ecu_node_config(const settings::inspect::dictionary_view & cfg)
{
  NodeConfig config{};
  config.publish_topic = get_publisher_topic(cfg);
  config.subscribe_topic = get_subscriber_topic(cfg);
  config.addresses = get_ecu_addresses(cfg);
  return config;
}

NodeConfig get_tester_node_config(const settings::inspect::dictionary_view & cfg)
{
  NodeConfig config{};
  config.publish_topic = get_publisher_topic(cfg);
  config.subscribe_topic = get_subscriber_topic(cfg);
  config.addresses = get_tester_addresses(cfg);
  return config;
}
}  // namespace


Configs parse(const settings::inspect::dictionary_view & cfg)
{
  Configs configs;
  configs.docan_config =
    get_ecu_node_config(get<dictionary_view>(cfg, "diagnostic/uds_gateway/docan_transport"));
  configs.doip_config =
    get_tester_node_config(get<dictionary_view>(cfg, "diagnostic/uds_gateway/doip_transport"));
  configs.uds_server_config =
    get_ecu_node_config(get<dictionary_view>(cfg, "diagnostic/uds_gateway/uds_server"));
  return configs;
}

Maps map(const Configs & configs)
{
  Maps maps;
  for (const auto & address : configs.docan_config.addresses) {
    maps.address_destination_topic_mappings.get()[address] = configs.docan_config.publish_topic;
    maps.address_source_topic_mappings.get()[address] = configs.docan_config.subscribe_topic;
  }
  for (const auto & address : configs.doip_config.addresses) {
    maps.address_destination_topic_mappings.get()[address] = configs.doip_config.publish_topic;
    maps.address_source_topic_mappings.get()[address] = configs.doip_config.subscribe_topic;
  }
  for (const auto & address : configs.uds_server_config.addresses) {
    maps.address_destination_topic_mappings.get()[address] =
      configs.uds_server_config.publish_topic;
    maps.address_source_topic_mappings.get()[address] = configs.uds_server_config.subscribe_topic;
  }
  return maps;
}
}  // namespace apex::automotive_diagnose::uds_gateway::config
