// Copyright 2025 Apex.AI, Inc.
// All rights reserved.
/// \brief Configuration parser related functions and types for the UDS gateway.
#ifndef UDS_GATEWAY__CONFIG_PARSER_HPP_
#define UDS_GATEWAY__CONFIG_PARSER_HPP_

#include "grace/automotive_diagnose/uds_gateway/uds_gateway/utilities.hpp"
#include "settings/inspect/types.hpp"

namespace apex::automotive_diagnose::uds_gateway::config
{
/// @brief groups  configuration parameters for a node the UDS gateway communicates with.
struct NodeConfig
{
  std::string publish_topic;
  std::string subscribe_topic;
  std::vector<UdsAddressType> addresses;
};

/// @brief groups  configuration parameters for all nodes the UDS gateway communicates with.
struct Configs
{
  NodeConfig docan_config;
  NodeConfig doip_config;
  NodeConfig uds_server_config;
};

/// @brief groups the mappings from addresses to destination(publish) topic and mappings to
/// source(subscribe) topics.
struct Maps
{
  AddressDestinationTopicMap address_destination_topic_mappings;
  AddressSourceTopicMap address_source_topic_mappings;
};

using apex::settings::inspect::dictionary_view;
/// @brief checks for the validity of the arguments passed.
/// @param cfg configuration view for UDS gateway parameters.
/// @return configurations related to all nodes the UDS gateway communicates with.
Configs parse(const settings::inspect::dictionary_view & cfg);

/// @brief checks for the validity of the arguments passed.
/// @param configs parsed configurations related to all nodes the UDS gateway communicates with.
/// @return mappings from addresses to topics .
Maps map(const Configs & configs);
}  // namespace apex::automotive_diagnose::uds_gateway::config
#endif  // UDS_GATEWAY__CONFIG_PARSER_HPP_
