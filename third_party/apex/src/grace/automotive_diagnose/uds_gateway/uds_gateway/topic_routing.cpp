// Copyright 2025 Apex.AI, Inc.
// All rights reserved.
#ifndef UDS_GATEWAY__TOPIC_MAPPING_HPP_
#define UDS_GATEWAY__TOPIC_MAPPING_HPP_


#include "grace/automotive_diagnose/uds_gateway/uds_gateway/topic_routing.hpp"

#include <optional>
#include <string>
#include <unordered_map>

#include "grace/automotive_diagnose/uds_gateway/uds_gateway/utilities.hpp"

namespace apex::automotive_diagnose::uds_gateway
{
bool argument_validity_check(const TesterAddress tester_address,
                             const EcuAddress ecu_address,
                             const std::string & source_topic,
                             const AddressDestinationTopicMap & address_dest_topic_mappings,
                             const AddressSourceTopicMap & address_source_topic_mappings)
{
  // Note: no check currently if the addresses are in the valid address ranges (0x700-0x7FF (11bit
  // CAN ID) or 0x18DAFFFF - 0x18DAFFFF (29bit CAN ID))
  constexpr UdsAddressType UNINITIALIZED_ADDRESS{0U};

  const bool is_uninitialized_address{tester_address.get() == UNINITIALIZED_ADDRESS ||
                                      ecu_address.get() == UNINITIALIZED_ADDRESS};
  const bool is_same_address{tester_address.get() == ecu_address.get()};
  const bool is_source_topic_mapped =
    (std::any_of(address_source_topic_mappings.get().begin(),
                 address_source_topic_mappings.get().end(),
                 [&](const auto & mapping) { return mapping.second == source_topic; }));

  const bool is_valid_args{!source_topic.empty() && !address_dest_topic_mappings.get().empty() &&
                           !address_source_topic_mappings.get().empty() &&
                           !is_uninitialized_address && !is_same_address && is_source_topic_mapped};

  if (!is_valid_args) {
    return false;
  }
  return true;
}

std::optional<UdsAddressType> find_destination_address(
  const TesterAddress tester_address,
  const EcuAddress ecu_address,
  const std::string & source_topic,
  const AddressSourceTopicMap & address_source_topic_mappings)
{
  const auto tester_source_mapping = address_source_topic_mappings.get().find(tester_address.get());
  const auto ecu_source_mapping = address_source_topic_mappings.get().find(ecu_address.get());
  const bool is_tester_source_mapping_found{tester_source_mapping !=
                                            address_source_topic_mappings.get().end()};
  const bool is_ecu_source_mapping_found{ecu_source_mapping !=
                                         address_source_topic_mappings.get().end()};
  bool is_tester_sender = false;
  if (is_tester_source_mapping_found) {
    is_tester_sender = (tester_source_mapping->second == source_topic);
  }
  bool is_ecu_sender = false;
  if (is_ecu_source_mapping_found) {
    is_ecu_sender = (ecu_source_mapping->second == source_topic);
  }

  const bool is_only_one_sender_found{is_tester_sender != is_ecu_sender};
  if (!is_only_one_sender_found) {
    return std::nullopt;
  }
  if (is_tester_sender) {
    return ecu_address.get();
  } else if (is_ecu_sender) {
    return tester_address.get();
  } else {
    return std::nullopt;
  }
}

std::optional<std::string> find_destination_topic(
  const UdsAddressType & destination_address,
  const AddressDestinationTopicMap & address_dest_topic_mappings)
{
  const auto destination_topic_mapping =
    address_dest_topic_mappings.get().find(destination_address);
  if (destination_topic_mapping == address_dest_topic_mappings.get().end()) {
    return std::nullopt;
  }
  return destination_topic_mapping->second;
}

std::optional<std::string> to_destination_topic(
  const uds_msgs::msg::UdsTransportMessage & msg,
  const std::string & source_topic,
  const AddressDestinationTopicMap & address_dest_topic_mappings,
  const AddressSourceTopicMap & address_source_topic_mappings)
{
  const bool is_input_args_valid{argument_validity_check(TesterAddress{msg.tester_address.address},
                                                         EcuAddress{msg.ecu_address.address},
                                                         source_topic,
                                                         address_dest_topic_mappings,
                                                         address_source_topic_mappings)};
  if (!is_input_args_valid) {
    return std::nullopt;
  }

  const std::optional<UdsAddressType> destination_address =
    find_destination_address(TesterAddress{msg.tester_address.address},
                             EcuAddress{msg.ecu_address.address},
                             source_topic,
                             address_source_topic_mappings);
  if (!destination_address) {
    return std::nullopt;
  }

  return find_destination_topic(destination_address.value(), address_dest_topic_mappings);
}
}  // namespace apex::automotive_diagnose::uds_gateway

#endif  // UDS_GATEWAY__TOPIC_MAPPING_HPP_
