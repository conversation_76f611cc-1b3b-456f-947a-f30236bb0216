/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief UdsGateway node.
#ifndef UDS_GATEWAY__UDS_GATEWAY_NODE_HPP_
#define UDS_GATEWAY__UDS_GATEWAY_NODE_HPP_
#include <assert.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/select.h>
#include <sys/types.h>

#include <unordered_map>

#include "executor2/apex_node_base.hpp"
#include "grace/automotive_diagnose/uds_gateway/uds_gateway/topic_routing.hpp"
#include "grace/automotive_diagnose/uds_gateway/uds_gateway/utilities.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "logging/logging_macros.hpp"
#include "timer_service/clock_timer_service.hpp"

#include "uds_msgs/msg/uds_transport_message.hpp"

namespace apex::automotive_diagnose::uds_gateway
{
class UdsGatewayNode : public apex::executor::apex_node_base
{
public:
  UdsGatewayNode(const apex::string_strict256_t & node_name,
                 const apex::string_strict256_t & docan_transport_subscribe_topic,
                 const apex::string_strict256_t & doip_transport_subscribe_topic,
                 const apex::string_strict256_t & uds_server_subscribe_topic,
                 const apex::string_strict256_t & docan_transport_publish_topic,
                 const apex::string_strict256_t & doip_transport_publish_topic,
                 const apex::string_strict256_t & uds_server_publish_topic,
                 AddressDestinationTopicMap address_destination_topic_mappings,
                 AddressSourceTopicMap address_src_topic_mappings);

private:
  /// @brief Main execution pipeline to forward messages.
  bool execute_impl() override;

  /// @brief Subscriptions that trigger the execution of the node.
  apex::executor::subscription_list get_triggering_subscriptions_impl() const override;

  /// @brief Log the node config like mappings and known publisher topics.
  void log_config() const;

  /// @brief Subscriber for messages from DoCAN transport.
  rclcpp::PollingSubscription<uds_msgs::msg::UdsTransportMessage>::SharedPtr
    m_docan_transport_subscription;
  /// @brief Subscriber for messages from DoIP transport.
  rclcpp::PollingSubscription<uds_msgs::msg::UdsTransportMessage>::SharedPtr
    m_doip_transport_subscription;
  /// @brief Subscriber for messages from UDS Server.
  rclcpp::PollingSubscription<uds_msgs::msg::UdsTransportMessage>::SharedPtr
    m_uds_server_subscription;
  /// @brief Publisher for messages to DoCAN transport.
  rclcpp::Publisher<uds_msgs::msg::UdsTransportMessage>::SharedPtr m_docan_transport_publisher;
  /// @brief Publisher for messages to DoIP transport.
  rclcpp::Publisher<uds_msgs::msg::UdsTransportMessage>::SharedPtr m_doip_transport_publisher;
  /// @brief Publisher for messages to UDS Server transport.
  rclcpp::Publisher<uds_msgs::msg::UdsTransportMessage>::SharedPtr m_uds_server_publisher;
  /// @brief Map with addresses [key] to destination/publish topics [value].
  AddressDestinationTopicMap m_address_destination_topic_mappings;
  /// @brief Map with addresses [key] to source/subscriber topics [value].
  AddressSourceTopicMap m_address_source_topic_mappings;
  /// @brief Map with destination/publish topics [key] to publishers [value].
  std::unordered_map<std::string, rclcpp::Publisher<uds_msgs::msg::UdsTransportMessage> *>
    m_topic_publisher_mappings;
  /// @brief Sequence of Subscription pointers holding the subscribers of the node.
  std::array<rclcpp::PollingSubscription<uds_msgs::msg::UdsTransportMessage> *, 3U> m_subscribers;
  /// @brief Apex logger.
  apex::logging::Logger<> m_logger;
};
}  // namespace apex::automotive_diagnose::uds_gateway

#endif  // UDS_GATEWAY__UDS_GATEWAY_NODE_HPP_
