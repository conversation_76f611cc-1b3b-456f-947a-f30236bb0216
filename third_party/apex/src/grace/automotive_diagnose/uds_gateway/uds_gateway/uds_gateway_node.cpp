/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief UdsGateway node.
#include "grace/automotive_diagnose/uds_gateway/uds_gateway/uds_gateway_node.hpp"

namespace apex::automotive_diagnose::uds_gateway
{

UdsGatewayNode::UdsGatewayNode(const apex::string_strict256_t & node_name,
                               const apex::string_strict256_t & docan_transport_subscribe_topic,
                               const apex::string_strict256_t & doip_transport_subscribe_topic,
                               const apex::string_strict256_t & uds_server_subscribe_topic,
                               const apex::string_strict256_t & docan_transport_publish_topic,
                               const apex::string_strict256_t & doip_transport_publish_topic,
                               const apex::string_strict256_t & uds_server_publish_topic,
                               AddressDestinationTopicMap address_destination_topic_mappings,
                               AddressSourceTopicMap address_src_topic_mappings)
: apex_node_base{node_name.c_str()},
  m_docan_transport_subscription{
    get_rclcpp_node().create_polling_subscription<uds_msgs::msg::UdsTransportMessage>(
      docan_transport_subscribe_topic.c_str(), rclcpp::DefaultQoS())},
  m_doip_transport_subscription{
    get_rclcpp_node().create_polling_subscription<uds_msgs::msg::UdsTransportMessage>(
      doip_transport_subscribe_topic.c_str(), rclcpp::DefaultQoS())},
  m_uds_server_subscription{
    get_rclcpp_node().create_polling_subscription<uds_msgs::msg::UdsTransportMessage>(
      uds_server_subscribe_topic.c_str(), rclcpp::DefaultQoS())},
  m_docan_transport_publisher{
    get_rclcpp_node().create_publisher<uds_msgs::msg::UdsTransportMessage>(
      docan_transport_publish_topic.c_str(), rclcpp::DefaultQoS())},
  m_doip_transport_publisher{get_rclcpp_node().create_publisher<uds_msgs::msg::UdsTransportMessage>(
    doip_transport_publish_topic.c_str(), rclcpp::DefaultQoS())},
  m_uds_server_publisher{get_rclcpp_node().create_publisher<uds_msgs::msg::UdsTransportMessage>(
    uds_server_publish_topic.c_str(), rclcpp::DefaultQoS())},
  m_address_destination_topic_mappings{address_destination_topic_mappings},
  m_address_source_topic_mappings{address_src_topic_mappings},
  m_logger{&get_rclcpp_node(), node_name}
{
  m_topic_publisher_mappings.insert(
    {m_docan_transport_publisher->get_topic_name(), m_docan_transport_publisher.get()});
  m_topic_publisher_mappings.insert(
    {m_doip_transport_publisher->get_topic_name(), m_doip_transport_publisher.get()});
  m_topic_publisher_mappings.insert(
    {m_uds_server_publisher->get_topic_name(), m_uds_server_publisher.get()});

  m_subscribers[0U] = m_docan_transport_subscription.get();
  m_subscribers[1U] = m_doip_transport_subscription.get();
  m_subscribers[2U] = m_uds_server_subscription.get();
  log_config();
  if (!(verify_map(m_address_source_topic_mappings.get()) &&
        verify_map(m_address_destination_topic_mappings.get()))) {
    APEX_ERROR(m_logger,
               "Invalid map detected. Topics must start with '/' and addresses must not be 0.");
    assert(false);
  }
}

bool UdsGatewayNode::execute_impl()
{
  for (const auto & subscriber : m_subscribers) {
    auto loaned_msgs{subscriber->take()};
    for (const auto & msg : loaned_msgs) {
      if (msg.info().valid()) {
        APEX_DEBUG(m_logger,
                   "Received message from: [",
                   apex::to_string(subscriber->get_topic_name()),
                   "], with ECU address: [",
                   apex::to_hex_string(msg->ecu_address.address),
                   "] and tester address: [",
                   apex::to_hex_string(msg->tester_address.address),
                   "]");
        const auto forward_topic =
          uds_gateway::to_destination_topic(msg.data(),
                                            subscriber->get_topic_name(),
                                            m_address_destination_topic_mappings,
                                            m_address_source_topic_mappings);
        if (forward_topic) {
          APEX_DEBUG(m_logger,
                     "Message from: [",
                     apex::to_string(subscriber->get_topic_name()),
                     "] will be forwarded to: [",
                     apex::to_string(forward_topic.value()),
                     "]");
          const auto forward_pub = m_topic_publisher_mappings.find(forward_topic.value());
          if (forward_pub != m_topic_publisher_mappings.end()) {
            forward_pub->second->publish(msg.data());
          } else {
            APEX_ERROR(m_logger, "Unknown topic, no mapping to publisher exists.");
          }
        } else {
          APEX_ERROR(m_logger, "Unknown address, no mapping to topic exists.");
        }
      }
    }
  }
  return true;
}

apex::executor::subscription_list UdsGatewayNode::get_triggering_subscriptions_impl() const
{
  return {m_docan_transport_subscription, m_doip_transport_subscription, m_uds_server_subscription};
}

void UdsGatewayNode::log_config() const
{
  std::string source_log_str{};
  for (const auto & mapping : m_address_source_topic_mappings.get()) {
    source_log_str += ("[" + apex::to_hex_string(mapping.first));
    source_log_str += ("|" + apex::to_string(mapping.second) + "]");
    source_log_str += ",";
  }
  source_log_str.pop_back();
  APEX_DEBUG(m_logger, "Address to source topic mappings:", source_log_str);
  std::string destination_log_str{};
  for (const auto & mapping : m_address_destination_topic_mappings.get()) {
    destination_log_str += ("[" + apex::to_hex_string(mapping.first));
    destination_log_str += ("|" + apex::to_string(mapping.second) + "]");
    destination_log_str += ",";
  }
  destination_log_str.pop_back();
  APEX_DEBUG(m_logger, "Address to destination topic mappings:", destination_log_str);
  std::string topic_str{};
  for (const auto & mapping : m_topic_publisher_mappings) {
    topic_str += ("[" + apex::to_string(mapping.first) + "],");
  }
  topic_str.pop_back();
  APEX_DEBUG(m_logger, "Known publisher Topics: [", topic_str + "]");
}

}  // namespace apex::automotive_diagnose::uds_gateway
