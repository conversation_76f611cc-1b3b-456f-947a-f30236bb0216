// Copyright 2025 Apex.AI, Inc.
// All rights reserved.
/// \brief Utility funcitonalityfor the UDS gateway.
#ifndef UDS_GATEWAY__UTILITIES_HPP_
#define UDS_GATEWAY__UTILITIES_HPP_

#include <string>
#include <unordered_map>

#include "uds_msgs/msg/uds_transport_message.hpp"

namespace apex::automotive_diagnose::uds_gateway
{
/// @brief Alias for a participants address type.
using UdsAddressType = uint16_t;

/// @brief Utility to support strong types. By using a tag the same underlying type can not be used
/// interchangable and we get strong interfaces. Provide the type you want to convert to a strong
/// type as first template parameter and a specific tag as the second template parameter. Usage:
/// using StrongType = NamedType<MyType, struct MyTag>;
template <typename T, typename TagType>
class NamedType final
{
public:
  NamedType() = default;
  explicit NamedType(T const & value) : m_value(value) {}
  explicit NamedType(T && value) : m_value(std::move(value)) {}

  T & get()
  {
    return m_value;
  }
  T const & get() const
  {
    return m_value;
  }

private:
  T m_value;
};

/// @brief Alias for a mapping of an address to a topic.
using AddressTopicMap = std::unordered_map<UdsAddressType, std::string>;
/// @brief Alias to provide a strong type for address source topic mapping.
using AddressSourceTopicMap = NamedType<AddressTopicMap, struct SourceTag>;
/// @brief Alias to provide a strong type for address destination topic mapping.
using AddressDestinationTopicMap = NamedType<AddressTopicMap, struct DestinationTag>;
/// @brief Alias to provide a strong type for ECU addresses.
using EcuAddress = NamedType<UdsAddressType, struct EcuAddressTag>;
/// @brief Alias to provide a strong type for Tester addresses.
using TesterAddress = NamedType<UdsAddressType, struct TesterAddressTag>;

/// @brief Verifies that a map follows the assumptions made when using it.
inline bool verify_map(const AddressTopicMap & map)
{
  return std::all_of(map.begin(), map.end(), [](const auto & mapping) {
    const bool is_address_valid{mapping.first != 0U};
    const bool is_topic_valid{!mapping.second.empty() && (mapping.second.front() == '/')};
    return (is_address_valid && is_topic_valid);
  });
}
}  // namespace apex::automotive_diagnose::uds_gateway
#endif  // UDS_GATEWAY__UTILITIES_HPP_
