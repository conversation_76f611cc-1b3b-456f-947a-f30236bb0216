/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief UDS Gateway main function.
///
#include <assert.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/select.h>
#include <sys/types.h>

#include <unordered_map>

#include "apex_init/apex_init.hpp"
#include "cpputils/common_exceptions.hpp"
#include "event/monitored_process.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "grace/automotive_diagnose/uds_gateway/uds_gateway/config/config_parser.hpp"
#include "grace/automotive_diagnose/uds_gateway/uds_gateway/uds_gateway_node.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "logging/logging_macros.hpp"
#include "timer_service/clock_timer_service.hpp"

#include "uds_msgs/msg/uds_transport_message.hpp"

int32_t main(const int32_t argc, char ** const argv)
{
  using namespace std::chrono_literals;

  int32_t result{};

  try {
    auto scoped_init = apex::scoped_init(argc, argv, false);

    const apex::interrupt_handler::installer interrupt_handler_installer{};
    apex::timer_service::steady_clock_timer_service srv;
    const auto global_config = apex::settings::repository::get();
    // Note: right now it is expected that topic names have a leading '/' in the string
    const auto configs = apex::automotive_diagnose::uds_gateway::config::parse(global_config);
    const auto address_mappings = apex::automotive_diagnose::uds_gateway::config::map(configs);

    const apex::string_strict256_t node_name{"uds_gateway"};
    const apex::string_strict256_t docan_sub_topic{configs.docan_config.subscribe_topic};
    const apex::string_strict256_t doip_sub_topic{configs.doip_config.subscribe_topic};
    const apex::string_strict256_t uds_server_sub_topic{configs.uds_server_config.subscribe_topic};
    const apex::string_strict256_t docan_pub_topic{configs.docan_config.publish_topic};
    const apex::string_strict256_t doip_pub_topic{configs.doip_config.publish_topic};
    const apex::string_strict256_t uds_server_pub_topic{configs.uds_server_config.publish_topic};
    auto uds_gateway_node =
      std::make_shared<apex::automotive_diagnose::uds_gateway::UdsGatewayNode>(
        node_name,
        docan_sub_topic,
        doip_sub_topic,
        uds_server_sub_topic,
        docan_pub_topic,
        doip_pub_topic,
        uds_server_pub_topic,
        address_mappings.address_destination_topic_mappings,
        address_mappings.address_source_topic_mappings);


    apex::event::monitored_process process{apex::event::sender_state::DoNotSyncWithDispatcher};
    const auto executor = apex::executor::executor_factory::create(process);
    (void)executor->add(uds_gateway_node);
    const apex::executor::executor_runner runner{apex::executor::executor_runner::deferred,
                                                 *executor};

    scoped_init.post_init(process);

    runner.issue();
    apex::interrupt_handler::wait();

    runner.stop();
  } catch (const std::exception & e) {
    std::cerr << e.what() << std::endl;
    result = 2;
  } catch (...) {
    std::cerr << "Unknown error occurred" << std::endl;
    result = -1;
  }
  return result;
}
