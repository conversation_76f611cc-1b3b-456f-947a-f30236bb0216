cc_library(
    name = "uds_gateway_routing_lib",
    srcs = [
        "topic_routing.cpp",
    ],
    hdrs = [
        "topic_routing.hpp",
        "utilities.hpp",
    ],
    visibility = ["//grace/automotive_diagnose/uds_gateway:__subpackages__"],
    deps = [
        "@apex//grace/automotive_diagnose/uds_msgs",
    ],
)

cc_library(
    name = "uds_gateway_node_lib",
    srcs = [
        "uds_gateway_node.cpp",
    ],
    hdrs = [
        "uds_gateway_node.hpp",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":uds_gateway_routing_lib",
        "//grace/execution/executor2",
        "@apex//grace/automotive_diagnose/uds_msgs",
    ],
)

cc_binary(
    name = "uds_gateway",
    srcs = ["main.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        ":uds_gateway_node_lib",
        "//common/interrupt",
        "//grace/automotive_diagnose/uds_gateway/uds_gateway/config:config_parser",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/ros/rclcpp/rclcpp",
        "@apex//grace/automotive_diagnose/uds_msgs",
    ],
)
