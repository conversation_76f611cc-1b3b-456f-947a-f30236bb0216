load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "uds_gateway_pkg",
    cc_libraries = [
        "//grace/automotive_diagnose/uds_gateway/uds_gateway:uds_gateway_node_lib",
        "//grace/automotive_diagnose/uds_gateway/uds_gateway/config:config_parser",
        "//grace/automotive_diagnose/uds_gateway/uds_gateway:uds_gateway_routing_lib",
    ],
    description = "Apex.Grace Gateway for Unified Diagnostic Services.",
    lib_executables = [
        "//grace/automotive_diagnose/uds_gateway/uds_gateway",
    ],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "uds_gateway",
    version = "0.1.0",
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils:cpputils_pkg",
        "//grace/execution/apex_init:apex_init_pkg",
        "//grace/execution/executor2:executor2_pkg",
    ],
)

ament_pkg_resources(
    name = "uds_gateway_resources",
    package = "uds_gateway",
    resources = {
        "//grace/automotive_diagnose/uds_gateway/uds_gateway": "executable",
        "//grace/automotive_diagnose/uds_gateway/uds_gateway/config": "share",
    },
    visibility = ["//visibility:public"],
)
