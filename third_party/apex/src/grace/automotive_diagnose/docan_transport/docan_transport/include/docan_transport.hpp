/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief DoCAN Transport providing functionality according to ISO-15765-2.
///
#ifndef DOCAN_TRANSPORT_INCLUDE__DOCAN_TRANSPORT_HPP_
#define DOCAN_TRANSPORT_INCLUDE__DOCAN_TRANSPORT_HPP_

#include <memory>

#include "apex_init/apex_init.hpp"
#include "executor2/apex_node_base.hpp"
#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/types.hpp"
#include "grace/connectors/can/raw_frame/raw_frame.hpp"
#include "grace/execution/executor2/include/executor2/apex_node_base.hpp"
#include "timer_service/clock_timer_service.hpp"

namespace apex::automotive_diagnose::docan
{
using CanRequestTopicType = apex_can_builtin::RawCanFrame;  // from tester
using CanResponseTopicType = apex_can_builtin::RawCanFrame;  // to ECU
using UdsTransportTopicType = uds_msgs::msg::UdsTransportMessage;  // to/from Gateway

/**
 * @brief Connector node for communication between gateway and CAN bus that uses the ISO-TP/DoCAN
 *protocol according to ISO-15765-2 for segmentation of data.
 **/
class DoCANTransport final : public apex::executor::executable_item
{
public:
  /**
   * @brief Create object.
   * @param node_name Name of the node in the network.
   * @param can_subscribe_topic topic to listen to for messages from the CAN bus.
   * @param can_publish_topic topic to publish to for messages to the CAN bus.
   * @param gateway_subscribe_topic topic to listen to for messages from the diagnostics gateway.
   * @param gateway_publish_topic topic to publish to for messages to the diagnostics gateway.
   * @param timer_sub timer subscriber.
   * @param config_params configuration parameters for docan.
   **/
  DoCANTransport(rclcpp::Node & node,
                 const apex::string_strict256_t & can_subscribe_topic,
                 const apex::string_strict256_t & can_publish_topic,
                 const apex::string_strict256_t & gateway_subscribe_topic,
                 const apex::string_strict256_t & gateway_publish_topic,
                 apex::timer_service::timer_subscription_ptr timer_sub,
                 const DoCANTransportParameters & config_params);
  /**
   * @brief Destroy object.
   **/
  ~DoCANTransport() final;

private:
  bool execute_impl() override;
  apex::executor::subscription_list get_triggering_subscriptions_impl() const override;
  class Impl;
  std::unique_ptr<Impl> m_impl;
};

}  // namespace apex::automotive_diagnose::docan
#endif  // DOCAN_TRANSPORT_INCLUDE__DOCAN_BRIDGE_HPP_
