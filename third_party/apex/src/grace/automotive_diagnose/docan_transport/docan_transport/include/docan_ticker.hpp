/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Ticker wrapper handles UDS-TO-GO time tickers.

#ifndef DOCAN_TRANSPORT_INCLUDE__TICKER_WRAPPER_HPP_
#define DOCAN_TRANSPORT_INCLUDE__TICKER_WRAPPER_HPP_

#include "executor2/executable_item.hpp"

namespace apex::automotive_diagnose::docan
{
/**
 * @brief Class that handles to internal time counting for the UDS-TO-GO stack as executable item.
 * @note: This class's execute_impl should be called with a frequency of 1Hz.
 **/
class DoCANTimerTicker final : public apex::executor::executable_item
{
public:
  /**
   * @brief Construct object.
   * @param node ros node.
   * @param timer_sub timer subscribtion.
   * @attention according to UDS-TO-GO the timers execute funtion must be called with a frequency of
   *1Hz. This must be respected in the timers setting.
   **/
  DoCANTimerTicker(rclcpp::Node & node, apex::timer_service::timer_subscription_ptr timer_sub);
  DoCANTimerTicker(const DoCANTimerTicker &) = delete;
  DoCANTimerTicker(DoCANTimerTicker &&) = delete;
  DoCANTimerTicker & operator=(const DoCANTimerTicker &) = delete;
  DoCANTimerTicker & operator=(DoCANTimerTicker &&) = delete;
  /**
   * @brief Destroy object.
   **/
  ~DoCANTimerTicker() final;

private:
  bool execute_impl() override;
  apex::executor::subscription_list get_triggering_subscriptions_impl() const override;
  class Impl;
  std::unique_ptr<Impl> m_impl;
  apex::timer_service::timer_subscription_ptr m_timer_sub;
};
}  // namespace apex::automotive_diagnose::docan
#endif  // DOCAN_TRANSPORT_INCLUDE__TICKER_WRAPPER_HPP_
