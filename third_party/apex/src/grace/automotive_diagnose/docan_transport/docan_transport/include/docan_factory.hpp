/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Creates neccessary executable items for DoCAN functionality.
///
#ifndef DOCAN_TRANSPORT_INCLUDE__DOCAN_FACTORY_HPP_
#define DOCAN_TRANSPORT_INCLUDE__DOCAN_FACTORY_HPP_

#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/types.hpp"
#include "grace/automotive_diagnose/docan_transport/docan_transport/include/docan_ticker.hpp"
#include "grace/automotive_diagnose/docan_transport/docan_transport/include/docan_transport.hpp"
#include "grace/execution/timer_service/include/timer_service/clock_timer_service.hpp"
#include "grace/execution/timer_service/include/timer_service/timer_service_interface.hpp"
#include "settings/construct/getters.hpp"
#include "settings/inspect.hpp"
#include "settings/inspect/types.hpp"
#include "timer_service/timer_service.hpp"


namespace apex::automotive_diagnose::docan
{
/**
 * @brief Convenience struct for parameters used by a docan executable_item like topics and docan
 *parameters.
 **/
struct NodeConfig
{
  apex::string_strict256_t can_subscribe_topic;
  apex::string_strict256_t can_publish_topic;
  apex::string_strict256_t gateway_subscribe_topic;
  apex::string_strict256_t gateway_publish_topic;
  DoCANTransportParameters docan_transport_parameters;
};

using settings::inspect::array_view;
using settings::inspect::boolean;
using settings::inspect::get;
using settings::inspect::integer;
using settings::inspect::maybe;
using settings::inspect::string_view;
using namespace std::chrono_literals;

/**
 * @brief Read a dictionary config node and write the contents to a NodeConfig struct.
 * @param config the dictionary node containing docan relevant parameters.
 **/
inline NodeConfig config_from_dictionary(const apex::settings::inspect::node_view config)
{
  // Read CAN topics
  auto can_read_topic = get<maybe<string_view>>(config, "can_read_topic").value_or("");
  if (can_read_topic.empty()) {
    throw std::runtime_error("Error: The config is missing a \"can_read_topic\" value!");
  }
  auto can_write_topic = get<maybe<string_view>>(config, "can_write_topic").value_or("");
  if (can_write_topic.empty()) {
    throw std::runtime_error("Error: The config is missing a \"can_write_topic\" value!");
  }

  // Read UDS topics
  auto uds_topic_in = get<maybe<string_view>>(config, "uds_topic_in").value_or("");
  if (uds_topic_in.empty()) {
    throw std::runtime_error("Error: The config is missing a \"uds_topic_in\" value!");
  }
  auto uds_topic_out = get<maybe<string_view>>(config, "uds_topic_out").value_or("");
  if (uds_topic_out.empty()) {
    throw std::runtime_error("Error: The config is missing a \"uds_topic_out\" value!");
  }

  // Read address mapping
  auto address_mappings = get<maybe<array_view>>(config, "uds_to_can_address_mapping");
  if (!address_mappings || address_mappings.value().empty()) {
    throw std::runtime_error("Error: The config is missing \"uds_to_can_address_mapping\" values!");
  }

  UdsCanAddressMapping uds_to_can_address_map{};
  // Get the first address mapping for default parameters
  for (auto & mapping : address_mappings.value()) {
    auto uds_ecu_address = get<maybe<integer>>(mapping, "uds_ecu_address");
    auto can_send_id = get<maybe<integer>>(mapping, "can_send_id");
    auto can_receive_id = get<maybe<integer>>(mapping, "can_receive_id");

    if (!uds_ecu_address || !can_send_id || !can_receive_id) {
      throw std::runtime_error("Error: Address mapping is missing required fields!");
    }

    uds_to_can_address_map[static_cast<uint32_t>(uds_ecu_address.value())] = {
      static_cast<uint32_t>(can_send_id.value()), static_cast<uint32_t>(can_receive_id.value())};
  }

  // Read ISOTP flow control parameters
  auto separation_time = get<maybe<integer>>(config, "isotp_flow_control/separation_time_ms");
  if (!separation_time) {
    throw std::runtime_error(
      "Error: The config is missing \"isotp_flow_control/separation_time_ms\" value!");
  }

  auto block_size = get<maybe<integer>>(config, "isotp_flow_control/block_size");
  if (!block_size) {
    throw std::runtime_error(
      "Error: The config is missing \"isotp_flow_control/block_size\" value!");
  }

  // Read CAN-FD and padding settings
  auto is_can_fd = get<maybe<boolean>>(config, "is_can_fd");
  if (!is_can_fd) {
    throw std::runtime_error("Error: The config is missing a \"is_can_fd\" value!");
  }
  auto docan_padding_byte = get<maybe<integer>>(config, "docan_padding_byte").value_or(0xCC);

  auto docan_request_timeout_ms =
    get<maybe<integer>>(config, "docan_request_timeout_ms").value_or(2000);

  // Create DoCANTransportParameters
  const apex::automotive_diagnose::docan::DoCANTransportParameters parameters{
    .uds_to_can_address_mapping = uds_to_can_address_map,
    .separation_time_ms = std::chrono::milliseconds(separation_time.value()),
    .block_size = static_cast<uint32_t>(block_size.value()),
    .is_can_fd = is_can_fd.value(),
    .docan_padding_byte = static_cast<uint32_t>(docan_padding_byte),
    .docan_request_timeout_ms = std::chrono::milliseconds(docan_request_timeout_ms)};

  // Return the node configuration
  return {
    .can_subscribe_topic = apex::to_string(can_read_topic),
    .can_publish_topic = apex::to_string(can_write_topic),
    .gateway_subscribe_topic = apex::to_string(uds_topic_in),
    .gateway_publish_topic = apex::to_string(uds_topic_out),
    .docan_transport_parameters = parameters,
  };
}

/**
 * @brief Read a array view of dictionary items from a global config and creates neccessary docan
 *items from it.
 * @param global_config the view over the global configuration.
 * @param node ros node needed for the construction of the items.
 * @param timer_service for the creation of timers. Passes as it needs to be alive during the
 *lifetime of the timers created from it.
 **/
inline std::vector<std::shared_ptr<executor::executable_item>> create_docan_executable_items(
  const apex::settings::inspect::dictionary_view & global_config,
  rclcpp::Node & node,
  const apex::timer_service::timer_service_ptr timer_service)
{
  using namespace std::chrono_literals;

  auto docan_configs = get<maybe<array_view>>(global_config, "diagnostic/docan");
  if (!docan_configs) {
    throw std::runtime_error("Error: Missing the \"docan_config/configs\" data!");
  }

  std::vector<std::shared_ptr<executor::executable_item>> executable_items;
  for (const auto config : docan_configs.value()) {
    auto const config_params = config_from_dictionary(config);
    auto docan_timer = timer_service->create_timer(0s, 50ms);
    auto const docan_item = std::make_shared<apex::automotive_diagnose::docan::DoCANTransport>(
      node,
      config_params.can_subscribe_topic,
      config_params.can_publish_topic,
      config_params.gateway_subscribe_topic,
      config_params.gateway_publish_topic,
      docan_timer,
      config_params.docan_transport_parameters);
    executable_items.push_back(docan_item);
  }
  auto ticker_timer = timer_service->create_timer(0s, 1000ms);
  auto const ticker =
    std::make_shared<apex::automotive_diagnose::docan::DoCANTimerTicker>(node, ticker_timer);
  executable_items.push_back(ticker);
  return executable_items;
}
}  // namespace apex::automotive_diagnose::docan

#endif  // DOCAN_TRANSPORT_INCLUDE__DOCAN_FACTORY_HPP_
