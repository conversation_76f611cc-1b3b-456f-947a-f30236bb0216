/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Types and aliasses that facilitate readability and functionality in the docan library.

#ifndef DOCAN_TRANSPORT_DETAIL__TYPES_HPP_
#define DOCAN_TRANSPORT_DETAIL__TYPES_HPP_

#include <functional>

#include "logging/logging_macros.hpp"
#include "string/string_strict.hpp"

#include "uds_msgs/msg/uds_transport_message.hpp"

namespace apex::automotive_diagnose::docan
{
/**
 * @brief Maximum size of bytes the internal CAN frame representation can hold.
 **/
constexpr size_t CAN_FRAME_DATA_SIZE{64U};
/**
 * @brief Internal CAN frame representation.
 **/
struct CanFrameSimple
{
  uint8_t data[CAN_FRAME_DATA_SIZE];
  size_t size;
  uint32_t msg_id;
};
/**
 * @brief Alias for the return type of subscription callbacks.
 **/
template <typename MessageT>
using SubscriberMessages = std::vector<MessageT>;
/**
 * @brief Alias for a logger.
 **/
using DoCANLogger = apex::logging::Logger<>;
/**
 * @brief Alias for callback responsible for subscription to messages from Gateway.
 **/
using GatewaySubscriptionCallback =
  std::function<SubscriberMessages<uds_msgs::msg::UdsTransportMessage>()>;
/**
 * @brief Alias for callback responsible for subscription to messages from CAN bus.
 **/
using CANSubscriptionCallback = std::function<std::vector<CanFrameSimple>()>;
/**
 * @brief Alias for callback responsible for publishing of messages to diagnostic gateway.
 **/
using GatewayPublisherCallback = std::function<void(const uint8_t *, size_t)>;
/**
 * @brief Alias for callback responsible for publishing of messages to CAN bus.
 **/
using CANPublisherCallback = std::function<void(const uint8_t *, size_t, uint32_t)>;
/**
 * @brief Configuration for underlying DoCAN stack
 **/
struct DoCANStackParameters
{
  uint32_t physical_id;
  uint32_t response_id;
  uint32_t functional_id;
  uint32_t separation_time_min;
  uint32_t blocksize;
  uint32_t candlength;
  uint32_t padding_byte;
  // additional configuration for Docan wrapper
  std::chrono::milliseconds docan_request_timeout_ms{};
};

/**
 * @brief CAN IDs for request and response.
 * @param msg_send_id CAN ID for sending UDS request from client to server
 * @param msg_send_id CAN ID for receiving UDS response from server to client
 **/
struct CanIdPair
{
  uint32_t msg_send_id;
  uint32_t msg_receive_id;
};

/**
 * @brief UDS Address for request and response.
 * @param msg_send_id UDS Address for sending UDS request from client to server
 * @param msg_send_id UDS Address for receiving UDS response from server to client
 **/
struct UdsAddressPair
{
  uint32_t tester_address;
  uint32_t ecu_address;
};

/**
 * @brief Alias for getting CAN identifiers for a UDS ECU address.
 */
using UdsCanAddressMapping = std::unordered_map<uint32_t, CanIdPair>;

/**
 * @brief Configuration for the DoCAN Transport
 **/
struct DoCANTransportParameters
{
  UdsCanAddressMapping uds_to_can_address_mapping;
  std::chrono::milliseconds separation_time_ms;
  uint32_t block_size;
  bool is_can_fd;
  uint32_t docan_padding_byte;
  std::chrono::milliseconds docan_request_timeout_ms{};
};

}  // namespace apex::automotive_diagnose::docan
#endif  // DOCAN_TRANSPORT_DETAIL__TYPES_HPP_
