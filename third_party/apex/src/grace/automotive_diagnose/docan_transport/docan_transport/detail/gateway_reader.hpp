/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Gateway reader forwards messages from diagnostic gateway bus to UDS-TO-GO docan.
///
#ifndef DOCAN_TRANSPORT_DETAIL__GATEWAY_READER_HPP_
#define DOCAN_TRANSPORT_DETAIL__GATEWAY_READER_HPP_


#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/types.hpp"

#include "src/uds/isotp/docan-tp.h"


namespace apex::automotive_diagnose::docan
{
/**
 * @brief Receiving connector to the gateway
 * @note: This class holds a reference to an object
 *that implements ICAN_Listener interface to follow the UDS-TO-GO libraries design. ICAN_Listener
 *here is the actual UDS-TO-GO docan.
 **/
class GatewayReader final
{
public:
  /**
   * @brief Initializes the reader to the physical can bus topic
   * @param docan_tp Instance of the UDS-TO-GO docan.
   * @param subscription_callback Subscriber callback to the topic associated with the CAN bus.
   * @param logger Logger instance.
   */
  GatewayReader(DoCAN_TP & docan_tp,
                GatewaySubscriptionCallback subscription_callback,
                DoCANLogger logger)
  : m_docan_tp{docan_tp},
    m_subscription_callback{std::move(subscription_callback)},
    m_logger{logger}
  {
  }
  GatewayReader(const GatewayReader &) = delete;
  GatewayReader(GatewayReader &&) = delete;
  GatewayReader & operator=(const GatewayReader &) = delete;
  GatewayReader & operator=(GatewayReader &&) = delete;

  /**
   * @brief Read data from the gateway and forward it to the DoCAN stack for processing
   **/
  void process();


private:
  /**
   * @brief UDS-TO-GO DoCAN_TP instance.
   **/
  DoCAN_TP & m_docan_tp;
  /**
   * @brief Callback to read from the topic associated with the gateway.
   **/
  GatewaySubscriptionCallback m_subscription_callback;
  /**
   * @brief Logger to log important info, events and data.
   **/
  DoCANLogger m_logger;
};


}  // namespace apex::automotive_diagnose::docan
#endif  // DOCAN_TRANSPORT_DETAIL__GATEWAY_READER_HPP_
