/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief DoCAN wrapper that wraps UDS-TO-GO.
///
#ifndef DOCAN_TRANSPORT_DETAIL__DOCAN_WRAPPER_HPP_
#define DOCAN_TRANSPORT_DETAIL__DOCAN_WRAPPER_HPP_


#include <memory>

#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/types.hpp"

namespace apex::automotive_diagnose::docan
{
/**
 * @brief Wraps and manages the DoCAN stack.
 **/
class DoCANWrapper final
{
public:
  /**
   * @brief Create object.
   * @param can_publisher_callback responsible for publishing messages to the CAN bus.
   * @param can_subscriber_callback responsible for receiving for messages from the CAN bus.
   * @param gateway_publisher_callback responsible for publishing messages to the diagnostics
   *gateway.
   * @param gateway_subscriber_callback responsible for receiving for messages from the diagnostics
   *gateway.
   * @param logger for logging.
   * @param config_params configuration parameters for docan.
   **/
  DoCANWrapper(CANPublisherCallback can_publisher_callback,
               CANSubscriptionCallback can_subscriber_callback,
               GatewayPublisherCallback gateway_publisher_callback,
               GatewaySubscriptionCallback gateway_subscriber_callback,
               DoCANLogger logger,
               const DoCANStackParameters & config_params);
  DoCANWrapper(const DoCANWrapper &) = delete;
  DoCANWrapper(DoCANWrapper &&) = delete;
  DoCANWrapper & operator=(const DoCANWrapper &) = delete;
  DoCANWrapper & operator=(DoCANWrapper &&) = delete;

  /**
   * @brief Destroy object.
   **/
  ~DoCANWrapper();

  /**
   * @brief Run the main ISO-TP/DoCAN pipeline
   **/
  void spin_once();

  /**
   * @brief Update the parameters of the DoCAN instance based on Incoming UDS message
   * @param params new parameters to set.
   **/
  void update_parameters(const DoCANStackParameters & params);

  /**
   * @brief Set the pending request flag.
   * @param pending true if a request is pending, false otherwise.
   **/
  void set_pending_request(bool pending);

private:
  class Impl;
  std::unique_ptr<Impl> m_impl;
};
}  // namespace apex::automotive_diagnose::docan
#endif  // DOCAN_TRANSPORT_DETAIL__DOCAN_WRAPPER_HPP_
