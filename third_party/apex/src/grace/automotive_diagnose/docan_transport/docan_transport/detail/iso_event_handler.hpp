/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Iso event handler receives events from UDS-TO-GO docan and handles them or publishes data
/// to the diagnostic gateway.

#ifndef DOCAN_TRANSPORT_DETAIL__ISO_EVENT_HANDLER_HPP_
#define DOCAN_TRANSPORT_DETAIL__ISO_EVENT_HANDLER_HPP_


#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/types.hpp"

#include "src/uds/isotp/docan-tp.h"


namespace apex::automotive_diagnose::docan
{
/**
 * @brief Class used from within ISO-TP/DoCAN stack to handle events.
 * @note: This class implements UDS-TO-GO interface IsoTpClient to follow the UDS-TO-GO libraries
 *design.
 **/
class IsoEventHandler final : public IsoTpClient
{
public:
  /**
   * @brief Create object
   * @param logger Logger object.
   * @param publisher_callback Publisher callback that will be used to publish messages to the
   *gateway.
   **/
  IsoEventHandler(DoCANLogger logger, GatewayPublisherCallback publisher_callback)
  : m_logger{logger}, m_publisher_callback{std::move(publisher_callback)}
  {
  }
  IsoEventHandler(const IsoEventHandler &) = delete;
  IsoEventHandler(IsoEventHandler &&) = delete;
  IsoEventHandler & operator=(const IsoEventHandler &) = delete;
  IsoEventHandler & operator=(IsoEventHandler &&) = delete;

protected:
  /**
   * @brief Handles Events that occured within the DoCAN stack, like messages that are received or
   *requested to be send on the CAN Bus
   * @param t Enum value to identify which event occured in the UDS-TO-GO stack.
   * @param res Enum value to identify what the result of the event is.
   * @param isotp_info Information about the event like data, length or id-type(double check).
   **/
  void OnIsoEvent(N_Event t, N_Result res, const IsoTpInfo & isotp_info) override;

private:
  /**
   * @brief Logger object.
   **/
  DoCANLogger m_logger;
  /**
   * @brief Callback to publish to the topic associated with the gateway.
   **/
  GatewayPublisherCallback m_publisher_callback;
};
}  // namespace apex::automotive_diagnose::docan

#endif  // DOCAN_TRANSPORT_DETAIL__ISO_EVENT_HANDLER_HPP_
