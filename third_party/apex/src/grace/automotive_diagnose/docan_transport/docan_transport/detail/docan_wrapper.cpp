/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief DoCAN wrapper that wraps UDS-TO-GO.
///
#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/docan_wrapper.hpp"

#include <chrono>

#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/can_reader.hpp"
#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/can_sender.hpp"
#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/gateway_reader.hpp"
#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/iso_event_handler.hpp"
#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/utilities.hpp"

#include "src/etc/helpers/static-allocator.h"
#include "src/uds/isotp/docan-tp.h"

namespace apex::automotive_diagnose::docan
{
namespace
{
constexpr size_t BYTES_TO_ALLOC{8192U};
}
/**
 * @brief Wraps and manages the DoCAN stack
 **/
class DoCANWrapper::Impl
{
public:
  /**
   * @brief Create object.
   * @param can_publisher_callback responsible for publishing messages to the CAN bus.
   * @param can_subscriber_callback responsible for receiving for messages from the CAN bus.
   * @param gateway_publisher_callback responsible for publishing messages to the diagnostics
   *gateway.
   * @param gateway_subscriber_callback responsible for receiving for messages from the diagnostics
   *gateway.
   * @param logger for logging.
   * @param config_params configuration parameters for docan.
   **/
  Impl(CANPublisherCallback can_publisher_callback,
       CANSubscriptionCallback can_subscriber_callback,
       GatewayPublisherCallback gateway_publisher_callback,
       GatewaySubscriptionCallback gateway_subscriber_callback,
       DoCANLogger logger,
       const DoCANStackParameters & config_params)
  : m_rxalloc{},
    m_txalloc{},

    m_iso_event_handler{logger, std::move(gateway_publisher_callback)},
    m_can_sender{std::move(can_publisher_callback), logger},
    m_docan_tp{m_rxalloc.ptr(),
               BYTES_TO_ALLOC,
               m_txalloc.ptr(),
               BYTES_TO_ALLOC,
               m_can_sender,
               m_iso_event_handler},
    m_can_reader{m_docan_tp, std::move(can_subscriber_callback), logger},
    m_gateway_reader{m_docan_tp, std::move(gateway_subscriber_callback), logger},
    m_logger{logger},
    m_docan_request_timeout_ms{config_params.docan_request_timeout_ms}
  {
    utilities::set_docan_parameters(m_docan_tp, config_params, m_logger);
  }
  Impl(const Impl &) = delete;
  Impl(Impl &&) = delete;
  Impl & operator=(const Impl &) = delete;
  Impl & operator=(Impl &&) = delete;
  ~Impl() = default;

  /**
   * @brief Run the main ISO-TP/DoCAN pipeline
   **/
  void spin_once()
  {
    m_docan_tp.Process();  // handles ongoing transmissions in different states
    m_can_reader.process();  // handles data received from can

    // If no request is pending or timed out, process the next one
    if (!is_request_pending() || is_request_timed_out()) {
      m_gateway_reader.process();  // handles requests from gateway to send data on the can bus
      set_pending_request(
        false);  // reset pending request timepoint, just in case reading fails or has no data
    }
  }

  /**
   * @brief Update the parameters of the DoCAN instance based on incoming UDS message
   * @param params new parameters to set.
   **/
  void update_parameters(const DoCANStackParameters & params)
  {
    utilities::set_docan_parameters(m_docan_tp, params, m_logger);
  }

  /**
   * @brief Set the pending request flag.
   * @param pending true if a request is pending, false otherwise.
   **/
  void set_pending_request(const bool pending)
  {
    if (pending) {
      m_uds_request_pending_time_point = std::chrono::steady_clock::now();
    } else {
      m_uds_request_pending_time_point = std::chrono::steady_clock::time_point{};
    }
  }

private:
  /**
   * @brief Check if a request is pending.
   * @return true if a request is pending, false otherwise.
   **/
  bool is_request_pending() const
  {
    return (m_uds_request_pending_time_point.time_since_epoch().count() > 0);
  }

  /**
   * @brief Check if the request has timed out.
   * @return true if the request has timed out, false otherwise.
   **/
  bool is_request_timed_out() const
  {
    return (std::chrono::steady_clock::now() - m_uds_request_pending_time_point >
            std::chrono::milliseconds(m_docan_request_timeout_ms));
  }

  /**
   * @brief UDS-TO-GO provided functionality to statically allocate memory for receiver.
   **/
  StaticMemAllocator<uint8_t, BYTES_TO_ALLOC> m_rxalloc;
  /**
   * @brief UDS-TO-GO provided functionality to statically allocate memory for transmitter.
   **/
  StaticMemAllocator<uint8_t, BYTES_TO_ALLOC> m_txalloc;
  /**
   * @brief handles events that occur within the UDS-TO-GO stack. Referenced by m_docan_tp.
   **/
  IsoEventHandler m_iso_event_handler;
  /**
   * @brief Used by UDS-TO-GO to send frames to the CAN bus. Referenced by m_docan_tp.
   **/
  CANSender m_can_sender;
  /**
   * @brief Handles the main UDS-TO-GO ISO-TP/DoCAN functionality. Referenced by m_can_reader.
   **/
  DoCAN_TP m_docan_tp;
  /**
   * @brief Used by UDS-TO-GO to receive frames from the CAN bus.
   **/
  CANReader m_can_reader;
  /**
   * @brief Used by the docan_bridge to receive frames from the gateway.
   **/
  GatewayReader m_gateway_reader;
  /**
   * @brief Logger to log important info, events and data.
   **/
  DoCANLogger m_logger;
  /**
   * @brief Flag to indicate if a UDS request is pending a response.
   **/
  std::chrono::time_point<std::chrono::steady_clock> m_uds_request_pending_time_point{};

  /**
   * @brief The amount of time before discarding a pending request.
   **/
  const std::chrono::milliseconds m_docan_request_timeout_ms;
};


DoCANWrapper::DoCANWrapper(CANPublisherCallback can_publisher_callback,
                           CANSubscriptionCallback can_subscriber_callback,
                           GatewayPublisherCallback gateway_publisher_callback,
                           GatewaySubscriptionCallback gateway_subscriber_callback,
                           DoCANLogger logger,
                           const DoCANStackParameters & config_params)
: m_impl{std::make_unique<Impl>(std::move(can_publisher_callback),
                                std::move(can_subscriber_callback),
                                std::move(gateway_publisher_callback),
                                std::move(gateway_subscriber_callback),
                                logger,
                                config_params)}
{
}

DoCANWrapper::~DoCANWrapper() = default;

void DoCANWrapper::spin_once()
{
  m_impl->spin_once();
}

void DoCANWrapper::update_parameters(const DoCANStackParameters & params)
{
  m_impl->update_parameters(params);
}

void DoCANWrapper::set_pending_request(const bool pending)
{
  m_impl->set_pending_request(pending);
}

}  // namespace apex::automotive_diagnose::docan
