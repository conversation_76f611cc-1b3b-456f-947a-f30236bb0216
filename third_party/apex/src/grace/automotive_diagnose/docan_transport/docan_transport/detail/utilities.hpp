// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Utilitiy functions that facilitate interaction with the UDS-TO-GO stack.

#ifndef DOCAN_TRANSPORT_DETAIL__UTILITIES_HPP_
#define DOCAN_TRANSPORT_DETAIL__UTILITIES_HPP_

#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/types.hpp"

#include "src/uds/isotp/docan-tp.h"

namespace apex::automotive_diagnose::docan::utilities
{
/**
 * @brief Configure the docan instance.
 * @param [in/out] docan instance to configure.
 * @param config_params configuration parameters for docan.
 * @param logger to log the parameters set for the docan instance.
 **/
inline void set_docan_parameters(DoCAN_TP & docan,
                                 const DoCANStackParameters & config_parameters,
                                 const DoCANLogger & logger)
{
  APEX_DEBUG(logger, "Initialize DoCAN parameters:");
  APEX_DEBUG(logger, "BLOCKSIZE = ", apex::to_string(config_parameters.blocksize));
  APEX_DEBUG(logger, "STMIN   = ", apex::to_string(config_parameters.separation_time_min));
  APEX_DEBUG(logger, "PHYS    = ", apex::to_hex_string(config_parameters.physical_id));
  APEX_DEBUG(logger, "RESP    = ", apex::to_hex_string(config_parameters.response_id));
  APEX_DEBUG(logger, "FUNC    = ", apex::to_hex_string(config_parameters.functional_id));
  APEX_DEBUG(logger, "CANDL   = ", apex::to_string(config_parameters.candlength));
  APEX_DEBUG(logger, "PADD_BYTE= ", apex::to_hex_string(config_parameters.padding_byte));
  docan.SetParameter(ParName::BLKSZ, config_parameters.blocksize);
  docan.SetParameter(ParName::ST_MIN, config_parameters.separation_time_min);
  docan.SetParameter(ParName::PHYS_ADDR, config_parameters.physical_id);
  docan.SetParameter(ParName::RESP_ADDR, config_parameters.response_id);
  docan.SetParameter(ParName::FUNC_ADDR, config_parameters.functional_id);
  docan.SetParameter(ParName::CANDL, config_parameters.candlength);
  docan.SetParameter(ParName::PADD_BYTE, config_parameters.padding_byte);
}

/**
 * @brief Convert UDS-TO-GO N_Event ENUM value to string.
 * @param event the event enum value to convert to string.
 * @return enum value as a string.
 **/
inline std::string toString(const N_Event & event)
{
  switch (event) {
      // Flow control Frame (FC) received.
    case N_Event::Conf:
      return "Conf";
      // Single frame of data (SF) OR consecutive frame (CF) of data received.
    case N_Event::Data:
      return "Data";
      // First Frame (FF) of data received.
    case N_Event::DataFF:
      return "DataFF";
    default:
      return "Unknown event.";
  }
}

/**
 * @brief Convert UDS-TO-GO N_Result ENUM value to string.
 * @param result the event enum value to convert to string.
 * @return enum value as a string.
 * @note: Documentation of values taken from UDS-TO-GO source code.
 **/
inline std::string toString(const N_Result & result)
{
  switch (result) {
    // This value means that the service execution has been completed successfully; it can be
    // issued to a
    // service user on both the sender and receiver sides.
    case N_Result::OK_s:
      return "OK_s";  // sender
    case N_Result::OK_r:
      return "OK_r";  // receiver
    // This value is issued to the protocol user when the timer N_Ar / N_As has passed its time -
    // out value N_Asmax / N_Armax; it can be issued to service users on both the sender and
    // receiver sides.
    case N_Result::TIMEOUT_As:
      return "TIMEOUT_As";  // sender
    case N_Result::TIMEOUT_Ar:
      return "TIMEOUT_Ar";  // receiver
    // This value is issued to the service user when the timer N_Bs has passed its time - out
    // value N_Bsmax;
    // it can be issued to the service user on the sender side only.
    case N_Result::TIMEOUT_Bs:
      return "TIMEOUT_Bs";  // sender
    // This value is issued to the service user when the timer N_Cr has passed its time - out value
    // N_Crmax; it can be issued to the service user on the receiver side only.
    case N_Result::TIMEOUT_Cr:
      return "TIMEOUT_Cr";
    // This value is issued to the service user upon receipt of an unexpected SequenceNumber
    // (PCI.SN) value; it can be issued to the service user on the receiver side only.
    case N_Result::WRONG_SN:
      return "WRONG_SN";
    // This value is issued to the service user when an invalid or unknown FlowStatus value has been
    // received in a FlowControl (FC) N_PDU; it can be issued to the service user on the sender side
    // only.
    case N_Result::INVALID_FS:
      return "INVALID_FS";
    // This value is issued to the service user upon receipt of an unexpected protocol data unit; it
    // can be issued to the service user on the receiver side only.
    case N_Result::UNEXP_PDU:
      return "UNEXP_PDU";
    // This value is issued to the service user when the receiver has transmitted N_WFTmax
    // FlowControl N_PDUs with FlowStatus = WAIT in a row and following this, it cannot meet the
    // performance requirement for the transmission of a FlowControl N_PDU with FlowStatus =
    // ClearToSend. It can be issued to the service user on the receiver side only.
    case N_Result::WFT_OVRN:
      return "WFT_OVRN";
    // This value is issued to the service user upon receipt of a FlowControl (FC) N_PDU with
    // FlowStatus = OVFLW. It indicates that the buffer on the receiver side of a segmented message
    // transmission cannot store the number of bytes specified by the FirstFrame DataLength (FF_DL)
    // parameter in the FirstFrame and therefore the transmission of the segmented message was
    // aborted. It can be issued to the service user on the sender side only.
    case N_Result::BUFFER_OVFLW:
      return "BUFFER_OVFLW";
    // This is the general error value. It shall be issued to the service user when an error has
    // been detected by the network layer and no other parameter value can be used to better
    // describe the error. It can be issued to the service user on both the sender and receiver
    // sides.
    case N_Result::ERROR_s:
      return "ERROR_s";  // sender
    case N_Result::ERROR_r:
      return "ERROR_r";  // receiver
    default:
      return "Unknown result.";
  }
}

/**
 * @brief Get the Request Response CAN ID pair for a UDS ECU address.
 * @param uds_ecu_address UDS ECU address.
 **/
inline std::optional<CanIdPair> get_can_ids_for_uds_ecu_address(
  const uint32_t uds_ecu_address, const UdsCanAddressMapping & uds_ecu_address_to_can_ids_map)
{
  auto it = uds_ecu_address_to_can_ids_map.find(uds_ecu_address);
  if (it != uds_ecu_address_to_can_ids_map.end()) {
    return it->second;
  }
  return std::nullopt;
}

}  // namespace apex::automotive_diagnose::docan::utilities
#endif  // DOCAN_TRANSPORT_DETAIL__UTILITIES_HPP_
