cc_library(
    name = "docan_transport_detail_lib",
    srcs = [
        "can_reader.cpp",
        "can_sender.cpp",
        "gateway_reader.cpp",
        "iso_event_handler.cpp",
    ],
    hdrs = [
        "can_reader.hpp",
        "can_sender.hpp",
        "gateway_reader.hpp",
        "iso_event_handler.hpp",
        "types.hpp",
        "utilities.hpp",
    ],
    visibility = ["//grace/automotive_diagnose/docan_transport/docan_transport:__subpackages__"],
    deps = [
        "//grace/execution/executor2",
        "//grace/ros/rclcpp/rclcpp",
        "@apex//grace/automotive_diagnose/uds_msgs",
        "@uds-to-go//:defines",
        "@uds-to-go//:helpers",
        "@uds-to-go//:isotp",
    ],
)

cc_library(
    name = "docan_wrapper",
    srcs = [
        "docan_wrapper.cpp",
    ],
    hdrs = [
        "docan_wrapper.hpp",
    ],
    visibility = ["//grace/automotive_diagnose/docan_transport/docan_transport:__subpackages__"],
    deps = [
        ":docan_transport_detail_lib",
        "//grace/execution/executor2",
        "//grace/ros/rclcpp/rclcpp",
        "@apex//grace/automotive_diagnose/uds_msgs",
    ],
)
