/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief CAN sender receives messages from UDS-TO-GO docan and publishes them.
///
#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/can_sender.hpp"

namespace apex::automotive_diagnose::docan
{
size_t CANSender::SendFrame(const uint8_t * data, size_t length, uint32_t msgid)
{
  APEX_DEBUG(m_logger,
             apex::to_string("SEND frame of length[") + apex::to_string(length) +
               apex::to_string("] with msgid[") + apex::to_string(msgid) + apex::to_string("]"));

  if (length >= 2) {
    APEX_DEBUG(m_logger,
               apex::to_string("SEND[") + apex::to_string(data[0]) + apex::to_string(",") +
                 apex::to_string(data[1]) + apex::to_string(",...]"));
  }

  m_publisher_callback(data, length, msgid);
  return length;
}

}  // namespace apex::automotive_diagnose::docan
