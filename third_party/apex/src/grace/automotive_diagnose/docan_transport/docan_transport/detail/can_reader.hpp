/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief CAN reader forwards messages to UDS-TO-GO docan.
///
#ifndef DOCAN_TRANSPORT_DETAIL__CAN_READER_HPP_
#define DOCAN_TRANSPORT_DETAIL__CAN_READER_HPP_

#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/types.hpp"

#include "src/uds/inc/iso-tp-if.h"
#include "src/uds/isotp/docan-tp.h"


namespace apex::automotive_diagnose::docan
{
/**
 * @brief Receiving connector to physical CAN Bus
 * @note: This class holds a reference to an object
 *that implements ICAN_Listener interface to follow the UDS-TO-GO libraries design. ICAN_Listener
 *here is the actual iso-tp.
 **/
class CANReader final
{
public:
  /**
   * @brief Initializes the reader to the physical can bus topic
   * @param docan_tp Instance of the UDS-TO-GO docan.
   * @param subscriber_callback Subscriber callback to the topic associated with the CAN bus.
   * @param logger Logger instance.
   */
  CANReader(ICAN_Listener & docan_tp,
            CANSubscriptionCallback subscriber_callback,
            DoCANLogger logger)
  : m_docan_tp{docan_tp}, m_subscriber_callback{std::move(subscriber_callback)}, m_logger{logger}
  {
  }
  CANReader(const CANReader &) = delete;
  CANReader(CANReader &&) = delete;
  CANReader & operator=(const CANReader &) = delete;
  CANReader & operator=(CANReader &&) = delete;

  /**
   * @brief Read data from the physical CAN BUS and forward it to the DoCAN stack for processing
   **/
  void process();


private:
  /**
   * @brief UDS-TO-GO ICAN_Listener instance.
   **/
  ICAN_Listener & m_docan_tp;
  /**
   * @brief Callback to read from the topic associated with the CAN bus.
   **/
  CANSubscriptionCallback m_subscriber_callback;
  /**
   * @brief Logger to log important info, events and data.
   **/
  DoCANLogger const m_logger;
};


}  // namespace apex::automotive_diagnose::docan
#endif  // DOCAN_TRANSPORT_DETAIL__CAN_READER_HPP_
