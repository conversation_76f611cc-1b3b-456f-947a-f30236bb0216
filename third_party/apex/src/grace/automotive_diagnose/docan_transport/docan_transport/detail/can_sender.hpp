/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief CAN sender receives messages from UDS-TO-GO docan and publishes them.
///
#ifndef DOCAN_TRANSPORT_DETAIL__CAN_SENDER_HPP_
#define DOCAN_TRANSPORT_DETAIL__CAN_SENDER_HPP_
#include <functional>

#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/types.hpp"

#include "src/uds/inc/iso-tp-if.h"


namespace apex::automotive_diagnose::docan
{
/**
 * @brief Sending connector to physical CAN Bus
 * @note: This class implements UDS-TO-GO interface ICAN_Sender to follow the UDS-TO-GO libraries
 *design.
 **/
class CANSender final : public ICAN_Sender
{
public:
  /**
   * @brief Create object
   * @param publisher_callback callback that will be used to publish messages to the
   * CAN bus.
   * @param logger Logger instance.
   **/
  CANSender(CANPublisherCallback publisher_callback, DoCANLogger logger)
  : m_publisher_callback{std::move(publisher_callback)}, m_logger{logger} {};
  CANSender(const CANSender &) = delete;
  CANSender(CANSender &&) = delete;
  CANSender & operator=(const CANSender &) = delete;
  CANSender & operator=(CANSender &&) = delete;

private:
  /**
   * @brief Send data to the physical CAN Bus, called from within DoCAN_TP
   * @param data pointer to the data that should be send.
   * @param length number of bytes to send.
   * @param msgid receiver ID of the message.
   **/
  size_t SendFrame(const uint8_t * data, size_t length, uint32_t msgid) override;

  /**
   * @brief Callback to publish to the topic associated with the CAN bus.
   **/
  CANPublisherCallback m_publisher_callback;
  /**
   * @brief Logger to log important info, events and data.
   **/
  DoCANLogger const m_logger;
};
}  // namespace apex::automotive_diagnose::docan
#endif  // DOCAN_TRANSPORT_DETAIL__CAN_SENDER_HPP_
