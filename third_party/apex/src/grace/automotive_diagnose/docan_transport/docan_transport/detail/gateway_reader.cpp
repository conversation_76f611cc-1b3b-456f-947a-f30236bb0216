/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Gateway reader forwards messages from diagnostic gateway bus to UDS-TO-GO docan.
///

#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/gateway_reader.hpp"

namespace apex::automotive_diagnose::docan
{
void GatewayReader::process()
{
  auto valid_msgs = m_subscription_callback();
  for (const auto & msg : valid_msgs) {
    APEX_DEBUG(m_logger,
               apex::to_string("Gateway request: tester[") +
                 apex::to_string(msg.tester_address.address) + apex::to_string("], ecu[") +
                 apex::to_string(msg.ecu_address.address) + apex::to_string("], [") +
                 apex::to_string(msg.payload.size()) + apex::to_string("] bytes."));
    // TODO: assert if IDs do not match the configured IDs.
    m_docan_tp.Request(msg.payload.data(), msg.payload.size());
  }
}
}  // namespace apex::automotive_diagnose::docan
