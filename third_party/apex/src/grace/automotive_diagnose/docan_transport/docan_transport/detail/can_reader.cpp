/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief CAN reader forwards messages to UDS-TO-GO docan.
///
#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/can_reader.hpp"

namespace apex::automotive_diagnose::docan
{
void CANReader::process()
{
  auto valid_msgs = m_subscriber_callback();
  for (auto & msg : valid_msgs) {
    APEX_DEBUG(m_logger,
               apex::to_string("READ frame of length[") + apex::to_string(msg.size) +
                 apex::to_string("] with msgid[") + apex::to_string(msg.msg_id) +
                 apex::to_string("]"));
    if (msg.size >= 2) {
      APEX_DEBUG(m_logger,
                 apex::to_string("READ[") + apex::to_string(msg.data[0]) + apex::to_string(",") +
                   apex::to_string(msg.data[1]) + apex::to_string(",...]"));
    }
    m_docan_tp.ReadFrame(static_cast<uint8_t *>(msg.data), msg.size, msg.msg_id);
  }
}
}  // namespace apex::automotive_diagnose::docan
