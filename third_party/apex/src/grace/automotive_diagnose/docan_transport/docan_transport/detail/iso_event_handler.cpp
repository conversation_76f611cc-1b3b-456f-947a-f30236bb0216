/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Iso event handler receives events from UDS-TO-GO docan and handles them or publishes data
/// to the diagnostic gateway.


#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/iso_event_handler.hpp"

#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/utilities.hpp"


namespace apex::automotive_diagnose::docan
{
void IsoEventHandler::OnIsoEvent(N_Event t, N_Result res, const IsoTpInfo & isotp_info)
{
  if (static_cast<uint8_t>(t) >= 3 || static_cast<uint8_t>(res) >= 13) {
    APEX_ERROR(m_logger,
               apex::to_string("Unknown Event [") + apex::to_string(static_cast<uint8_t>(t)) +
                 apex::to_string("] or Result [") + apex::to_string(static_cast<uint8_t>(t)) +
                 apex::to_string("]."));
    assert(false);  // Event is out of range of either ENUM
  }

  APEX_DEBUG(m_logger,
             apex::to_string("Event [") + utilities::toString(t) + "]" + ", Status [" +
               utilities::toString(res) + "]");

  if (t == N_Event::Data && res == N_Result::OK_r) {
    if (isotp_info.data == nullptr || isotp_info.length == 0U) {
      APEX_ERROR(m_logger,
                 apex::to_string("Data is nullptr or data length(") +
                   apex::to_string(isotp_info.length) + apex::to_string(") is 0."));
      assert(false);  // prevent nullptr access
    }
    m_publisher_callback(isotp_info.data, isotp_info.length);
    APEX_DEBUG(m_logger,
               apex::to_string(" <--- RECV OK: ") + apex::to_string(isotp_info.length) +
                 apex::to_string(" bytes."));
  } else if (t == N_Event::DataFF && res == N_Result::OK_r) {
    APEX_DEBUG(m_logger,
               apex::to_string("FirstFrame (FF): Expected size = ") +
                 apex::to_string(isotp_info.length) + apex::to_string(" bytes."));
  } else {
    APEX_DEBUG(m_logger, apex::to_string("No special handling for Event..."));
  }
}
}  // namespace apex::automotive_diagnose::docan
