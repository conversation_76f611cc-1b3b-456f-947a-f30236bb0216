/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief DoCAN Transport providing functionality according to ISO-15765-2.
///
#include "grace/automotive_diagnose/docan_transport/docan_transport/include/docan_transport.hpp"

#include <cstdint>

#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/docan_wrapper.hpp"
#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/utilities.hpp"

namespace apex::automotive_diagnose::docan
{
using CanRequestTopicType = apex_can_builtin::RawCanFrame;  // from tester
using CanResponseTopicType = apex_can_builtin::RawCanFrame;  // to ECU
using UdsTransportTopicType = uds_msgs::msg::UdsTransportMessage;  // to/from Gateway
/**
 * @brief Implementation of the DoCANTransport functionality
 **/
class DoCANTransport::Impl
{
public:
  /**
   * @brief Create object.
   * @param node Ros node in the network.
   * @param can_subscribe_topic topic to listen to for messages from the CAN bus.
   * @param can_publish_topic topic to publish to for messages to the CAN bus.
   * @param gateway_subscribe_topic topic to listen to for messages from the diagnostics gateway.
   * @param gateway_publish_topic topic to publish to for messages to the diagnostics gateway.
   * @param timer_sub timer subscriber.
   * @param config_params configuration parameters for docan.
   **/
  Impl(rclcpp::Node & node,
       const apex::string_strict256_t & can_subscribe_topic,
       const apex::string_strict256_t & can_publish_topic,
       const apex::string_strict256_t & gateway_subscribe_topic,
       const apex::string_strict256_t & gateway_publish_topic,
       apex::timer_service::timer_subscription_ptr timer_sub,
       const DoCANTransportParameters & config_params)
  : m_can_publisher{node.create_publisher<CanRequestTopicType>(can_publish_topic.c_str(),
                                                               rclcpp::DefaultQoS())},
    m_can_subscription{node.create_polling_subscription<CanResponseTopicType>(
      can_subscribe_topic.c_str(), rclcpp::DefaultQoS().keep_last(10U))},
    m_gateway_publisher{node.create_publisher<UdsTransportTopicType>(gateway_publish_topic.c_str(),
                                                                     rclcpp::DefaultQoS())},
    m_gateway_subscription{node.create_polling_subscription<UdsTransportTopicType>(
      gateway_subscribe_topic.c_str(), rclcpp::DefaultQoS().keep_last(10U))},
    m_timer_sub{std::move(timer_sub)},
    m_logger{&node, "docan"},
    m_docan_parameters{
      .separation_time_min = static_cast<uint32_t>(config_params.separation_time_ms.count()),
      .blocksize = config_params.block_size,
      .candlength = config_params.is_can_fd ? 64U : 8U,
      .padding_byte = config_params.docan_padding_byte},
    m_docan_wrapper{
      // Can callbacks
      [this](const uint8_t * data, size_t length, uint32_t msgid) {
        this->publish_can(data, length, msgid);
      },
      [this]() -> std::vector<CanFrameSimple> { return handle_can_sub(); },
      // Gateway callbacks
      [this](const uint8_t * data, size_t length) { this->publish_gateway(data, length); },
      [this]() -> SubscriberMessages<UdsTransportTopicType> { return this->handle_gateway_sub(); },
      m_logger,
      m_docan_parameters,
    },
    m_uds_to_can_address_mapping{config_params.uds_to_can_address_mapping},
    m_can_reception_id_to_uds_address{std::nullopt},
    m_is_can_fd(config_params.is_can_fd)
  {
  }
  ~Impl() = default;

  bool execute_impl()
  {
    m_timer_sub->test_and_reset();
    m_docan_wrapper.spin_once();
    return true;
  }

  apex::executor::subscription_list get_triggering_subscriptions_impl() const
  {
    return {m_timer_sub->to_sub_ptr(), m_can_subscription};
  }

private:
  /**
   * @brief Publish a frame to the CanConnector.
   * @attention Does not add padding bytes to fill the payload up to capacity.
   * @param data location of the data that has to be published.
   * @param length amount of bytes to publish.
   * @param msgid can response id of the docan member.
   **/
  void publish_can(const uint8_t * data, size_t length, uint32_t msgid)
  {
    auto loaned_msg = m_can_publisher->borrow_loaned_message();
    auto & msg_data = loaned_msg.get();
    apex::base::span<uint8_t const> const data_view(data, length);
    if (!(msg_data.payload.capacity() >= data_view.size())) {
      APEX_ERROR(m_logger, "Not enough capacity in CAN frame type");
      assert(false);
    }
    std::copy(data_view.begin(), data_view.end(), std::back_inserter(msg_data.payload));
    msg_data.can_id = msgid;

    m_can_publisher->publish(std::move(loaned_msg));
  }

  std::vector<CanFrameSimple> handle_can_sub()
  {
    auto loaned_msgs{m_can_subscription->take()};
    if (loaned_msgs.empty()) {
      return {};
    }
    std::vector<CanFrameSimple> vec{};
    for (const auto & msg : loaned_msgs) {
      if (msg.info().valid()) {
        CanFrameSimple frame{};
        if (!(CAN_FRAME_DATA_SIZE >= msg.data().payload.size())) {
          APEX_ERROR(m_logger, "Not enough capacity in CAN frame type");
          assert(false);
        }
        std::copy(
          msg.data().payload.begin(), msg.data().payload.end(), static_cast<uint8_t *>(frame.data));
        frame.size = msg.data().payload.size();
        frame.msg_id = msg.data().can_id;
        vec.push_back(frame);
      }
    }
    return vec;
  }

  void publish_gateway(const uint8_t * data, size_t length)
  {
    bool pending_request = false;
    // Check if the CAN reception identifier matches
    if (m_can_reception_id_to_uds_address.has_value() &&
        m_can_reception_id_to_uds_address.value().first == m_docan_parameters.physical_id) {
      auto loaned_msg = m_gateway_publisher->borrow_loaned_message();
      if (!(loaned_msg->payload.capacity() >= length)) {
        APEX_ERROR(m_logger,
                   "Not enough capacity in Gateway frame payload. Capacity: " +
                     std::to_string(loaned_msg->payload.capacity()) +
                     ", required length: " + std::to_string(length));
        assert(false);
      } else {
        loaned_msg->payload.reserve(length);
        const apex::base::span<const uint8_t> data_view(data, length);

        // Check if the payload contains a negative response service id
        // which indicates a pending response code
        constexpr uint8_t negative_response_code_service_id = 0x7F;
        constexpr uint8_t negative_response_code_response_pending = 0x78;

        // first byte of the payload is the negative response service id when there is a negative
        // response third byte of the payload is the negative response code which can indicate a
        // pending response
        if (negative_response_code_service_id == data_view[0] &&
            negative_response_code_response_pending == data_view[2]) {
          pending_request = true;
        }

        std::copy(data_view.begin(), data_view.end(), std::back_inserter(loaned_msg->payload));

        // Get the UDS address for the CAN reception ID
        const auto uds_address = m_can_reception_id_to_uds_address.value().second;
        loaned_msg->tester_address.address = uds_address.tester_address;
        loaned_msg->ecu_address.address = uds_address.ecu_address;

        this->m_gateway_publisher->publish(std::move(loaned_msg));
      }
    } else {
      APEX_ERROR(
        m_logger,
        "No UDS address found for Can ID: " + std::to_string(m_docan_parameters.physical_id));
    }

    if (!pending_request) {
      m_can_reception_id_to_uds_address.reset();
    }
    m_docan_wrapper.set_pending_request(pending_request);
  }

  SubscriberMessages<UdsTransportTopicType> handle_gateway_sub()
  {
    SubscriberMessages<UdsTransportTopicType> msgs{};

    // Take only one message from the subscription queue at a time.
    // This is to ensure that we do not process next UDS requests until we have the response
    // from the CAN bus for the previous one.
    auto loaned_msgs{this->m_gateway_subscription->take(1)};
    for (const auto & loaned_msg : loaned_msgs) {
      if (loaned_msg.info().valid()) {
        auto msg = loaned_msg.data();

        if (calculate_docan_stack_parameters(msg)) {
          // Set new parameters to docan instance before processing the message
          m_docan_wrapper.update_parameters(m_docan_parameters);

          // Set pending request to true
          m_docan_wrapper.set_pending_request(true);
        }
        msgs.push_back(msg);
      }
    }

    return msgs;
  }

  bool calculate_docan_stack_parameters(const UdsTransportTopicType & msg)
  {
    // Get CAN IDs corresponding to the UDS ECU address in the DOIP message
    auto can_ids = utilities::get_can_ids_for_uds_ecu_address(msg.ecu_address.address,
                                                              m_uds_to_can_address_mapping);
    if (!can_ids.has_value()) {
      APEX_ERROR(
        m_logger,
        "No CAN IDs found for UDS ECU address: " + std::to_string(msg.ecu_address.address));
      return false;
    }

    // Set CAN message length based on the size of the payload and CanFD settings for correct
    // ISOTP handling
    if (m_is_can_fd) {
      // PCI header bytes are 1 for 7 byte payload and 2 payloads greater than 8 bytes
      const auto pci_bytes = msg.payload.size() < 8 ? 1 : 2;

      // Calculate the total CAN message length
      const auto can_length = msg.payload.size() + pci_bytes;

      // Map to valid CAN FD lengths: 8, 12, 16, 20, 24, 32, 48, or 64 bytes
      constexpr size_t valid_canfd_lengths[] = {8, 12, 16, 20, 24, 32, 48, 64};
      constexpr size_t CANFD_MAX_LENGTH{64};
      size_t selected_length =
        CANFD_MAX_LENGTH;  // Default to max if can_length exceeds all valid lengths
      for (const auto & length : valid_canfd_lengths) {
        if (can_length <= length) {
          selected_length = length;
          break;
        }
      }
      m_docan_parameters.candlength = selected_length;
    } else {
      // Set the CAN message length in the docan parameters
      constexpr size_t CAN_MAX_LENGTH{8};
      m_docan_parameters.candlength = CAN_MAX_LENGTH;
    }

    // physical_id parameter to be used for receiving CAN message.
    m_docan_parameters.physical_id = can_ids.value().msg_receive_id;
    // response id parameter is used for sending CAN message.
    m_docan_parameters.response_id = can_ids.value().msg_send_id;

    // Set tester address to the receive id map
    m_can_reception_id_to_uds_address = {can_ids.value().msg_receive_id,
                                         {msg.tester_address.address, msg.ecu_address.address}};

    return true;
  }

  const rclcpp::Publisher<CanRequestTopicType>::SharedPtr m_can_publisher;
  const rclcpp::PollingSubscription<CanResponseTopicType>::SharedPtr m_can_subscription;
  const rclcpp::Publisher<UdsTransportTopicType>::SharedPtr m_gateway_publisher;
  const rclcpp::PollingSubscription<UdsTransportTopicType>::SharedPtr m_gateway_subscription;
  apex::timer_service::timer_subscription_ptr m_timer_sub;
  apex::logging::Logger<> m_logger;
  DoCANStackParameters m_docan_parameters;
  DoCANWrapper m_docan_wrapper;
  const UdsCanAddressMapping m_uds_to_can_address_mapping;
  std::optional<std::pair<uint32_t, UdsAddressPair>> m_can_reception_id_to_uds_address;
  bool m_is_can_fd;
};

DoCANTransport::DoCANTransport(rclcpp::Node & node,
                               const apex::string_strict256_t & can_subscribe_topic,
                               const apex::string_strict256_t & can_publish_topic,
                               const apex::string_strict256_t & gateway_subscribe_topic,
                               const apex::string_strict256_t & gateway_publish_topic,
                               apex::timer_service::timer_subscription_ptr timer_sub,
                               const DoCANTransportParameters & config_params)
: apex::executor::executable_item(node),
  m_impl{std::make_unique<Impl>(node,
                                can_subscribe_topic,
                                can_publish_topic,
                                gateway_subscribe_topic,
                                gateway_publish_topic,
                                timer_sub,
                                config_params)}
{
}

DoCANTransport::~DoCANTransport() = default;

bool DoCANTransport::execute_impl()
{
  return m_impl->execute_impl();
  ;
}

apex::executor::subscription_list DoCANTransport::get_triggering_subscriptions_impl() const
{
  return m_impl->get_triggering_subscriptions_impl();
}
}  // namespace apex::automotive_diagnose::docan
