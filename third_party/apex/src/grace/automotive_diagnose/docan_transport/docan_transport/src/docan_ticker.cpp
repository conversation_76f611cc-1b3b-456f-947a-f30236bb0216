#/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Ticker wrapper handles UDS-TO-GO time tickers.

#include "grace/automotive_diagnose/docan_transport/docan_transport/include/docan_ticker.hpp"

#include <chrono>

#include "src/etc/timers/tickerup.h"

#include "src/etc/helpers/static-allocator.h"

namespace apex::automotive_diagnose::docan
{
/**
 * @brief Class that handles to internal time counting for the UDS-TO-GO stack.
 * @note: This class follows the UDS-TO-GO suggested design.
 **/
class DoCANTimerTicker::Impl
{
public:
  /**
   * @brief Create object
   **/
  Impl() = default;
  Impl(const Impl &) = delete;
  Impl(Impl &&) = delete;
  Impl & operator=(const Impl &) = delete;
  Impl & operator=(Impl &&) = delete;
  ~Impl() = default;

  /**
   * @brief Process timers used internally by UDS-TO-GO.
   * @note: From UDS-TO-GO: Both layers (network and session) use timers. To process timer you have
   *to call static method of TickerCounter class periodically with frequency 1 Hz. In the example
   *project it has been made by wrapping this call to class Impl
   *(src/example/ticker-wrapper.h) which method Process is called from main thread. It Processes
   *core tick counter each 1 ms. For MCU implementation you can put this call
   * Timers::TickerCounter::ProcessTick() inside timer IRQ handler
   * @attention: timers will work properly only if the size of core timer counter type (32-bit by
   *default) is the same as the system base bit-width As long as your system bit width is the same
   *as the core timer counter type (32bit by default) everything is ok, timer is safe to be used in
   *multithread environment, because ticker increment is going to be atomic. In other cases it is
   *unsafe to use it with default implementation
   **/
  void execute()
  {
    static auto first_stamp = std::chrono::steady_clock::now();

    auto now_stamp = std::chrono::steady_clock::now();
    auto elapsed_us =
      std::chrono::duration_cast<std::chrono::microseconds>(now_stamp - first_stamp).count();

    while (elapsed_us > 1000) {
      elapsed_us -= 1000;
      first_stamp += std::chrono::microseconds(1000);

      Timers::TickerCounter::ProcessTick();
    }
  }
};

DoCANTimerTicker::DoCANTimerTicker(rclcpp::Node & node,
                                   apex::timer_service::timer_subscription_ptr timer_sub)
: apex::executor::executable_item(node), m_impl{std::make_unique<Impl>()}, m_timer_sub{timer_sub}
{
}

DoCANTimerTicker::~DoCANTimerTicker() = default;

bool DoCANTimerTicker::execute_impl()
{
  if (m_timer_sub->test_and_reset()) {
    m_impl->execute();
  }
  return true;
}

apex::executor::subscription_list DoCANTimerTicker::get_triggering_subscriptions_impl() const
{
  return {m_timer_sub->to_sub_ptr()};
}

}  // namespace apex::automotive_diagnose::docan
