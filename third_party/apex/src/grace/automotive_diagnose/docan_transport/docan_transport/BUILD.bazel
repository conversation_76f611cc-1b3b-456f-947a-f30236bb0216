cc_library(
    name = "docan_transport",
    srcs = [
        "src/docan_transport.cpp",
    ],
    hdrs = [
        "include/docan_transport.hpp",
    ],
    copts = select({
        "@apex//common/build_system_transfer:enabled": ["-Wno-error=missing-field-initializers"],
        "//conditions:default": [],
    }),
    visibility = ["//visibility:public"],
    deps = [
        "//common/interrupt",
        "//grace/automotive_diagnose/docan_transport/docan_transport/detail:docan_wrapper",
        "//grace/connectors/can/raw_frame",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/ros/rclcpp/rclcpp",
        "@apex//grace/automotive_diagnose/uds_msgs",
    ],
)

cc_library(
    name = "docan_ticker",
    srcs = [
        "src/docan_ticker.cpp",
    ],
    hdrs = [
        "include/docan_ticker.hpp",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/execution/executor2",
        "//grace/ros/rclcpp/rclcpp",
        "@apex//grace/automotive_diagnose/uds_msgs",
        "@uds-to-go//:helpers",
        "@uds-to-go//:timers",
    ],
)

cc_library(
    name = "docan_factory",
    hdrs = [
        "include/docan_factory.hpp",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":docan_ticker",
        ":docan_transport",
        "//common/configuration/settings",
        "//grace/execution/executor2",
        "//grace/ros/rclcpp/rclcpp",
        "@apex//grace/automotive_diagnose/uds_msgs",
        "@uds-to-go//:helpers",
        "@uds-to-go//:timers",
    ],
)

filegroup(
    name = "settings",
    srcs = ["settings/default_settings.yaml"],
    visibility = ["//visibility:public"],
)
