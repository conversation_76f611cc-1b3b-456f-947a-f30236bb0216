load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "docan_transport_pkg",
    cc_libraries = [
        "//grace/automotive_diagnose/docan_transport/docan_transport",
        "//grace/automotive_diagnose/docan_transport/docan_transport:docan_ticker",
        "//grace/automotive_diagnose/docan_transport/docan_transport:docan_factory",
    ],
    description = "Apex.OS to DoCAN transport",
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "docan_transport",
    version = "0.1.0",
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils:cpputils_pkg",
        "//grace/execution/apex_init:apex_init_pkg",
        "//grace/execution/executor2:executor2_pkg",
        "@uds-to-go//:uds-to-go_pkg",
    ],
)
