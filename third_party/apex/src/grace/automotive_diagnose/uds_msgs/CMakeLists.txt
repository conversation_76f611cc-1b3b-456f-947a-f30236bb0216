# Copyright 2022 Apex.AI, Inc.
# All rights reserved.
cmake_minimum_required(VERSION 3.5)

project(uds_msgs)

find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()

rosidl_generate_interfaces(${PROJECT_NAME}
  "msg/DoIpHeader.idl"
  "msg/UdsAddress.idl"
  "msg/UdsEnvironmentCondition.idl"
  "msg/UdsSecurityLevelUnlockingResult.idl"
  "msg/UdsSessionEvent.idl"
  "msg/UdsTransportMessage.idl"
  "srv/UdsClearDtc.idl"
  "srv/UdsControlDtcSetting.idl"
  "srv/UdsInputOutputControl.idl"
  "srv/UdsIsSecurityLevelAvailable.idl"
  "srv/UdsReadDataByIdentifier.idl"
  "srv/UdsReadDtc.idl"
  "srv/UdsRoutineControl.idl"
  "srv/UdsSecurityCompareKey.idl"
  "srv/UdsSecurityGetSeed.idl"
  "srv/UdsWriteDataByIdentifier.idl"
)

ament_export_dependencies(
    "rosidl_default_runtime")

ament_auto_package()
