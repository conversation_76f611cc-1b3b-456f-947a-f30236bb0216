load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "install_space")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "cc_test_in_install_space")

install_space(
    name = "install_space_for_tests",
    message_library_kinds = [
        "cpp",
        "introspection_cpp",
    ],
    ros_pkgs = [
        "@apex//grace/automotive_diagnose/diagnostic_fault_manager:diagnostic_fault_manager_pkg",
        "@apex//grace/tools/apex_integration_test_node:apex_integration_test_node_pkg",
        "@apex//grace/automotive_diagnose/diagnostic_fault_manager_msgs:diagnostic_fault_manager_msgs_pkg",
        "@apex//grace/automotive_diagnose/uds_msgs:uds_msgs_pkg",
        "@apex//grace/configuration/storage/storage:storage_pkg",
        "@apex//grace/automotive_diagnose/common:diagnostic_common_pkg",
        "@apex//common/configuration/settings:settings_pkg",
    ],
)

apex_cc_test(
    name = "test_diagnostic_fault_manager",
    srcs = [
        "mocks/dtc_subfubctions_persistency_mock.hpp",
        "mocks/persistency_base_mock.hpp",
        "test_config_parser.cpp",
        "test_dtc_change_publisher.cpp",
        "test_dtcc_clear_dtc.cpp",
        "test_dtcc_control_dtc_setting.cpp",
        "test_dtcc_read_dtc.cpp",
        "test_dtcm.cpp",
        "test_example_dtc_change_subscriber.cpp",
        "test_snapshot_creator.cpp",
    ],
    copts = ["-std=c++17"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "skip_asan",  # FIXME: fix asan findings and remove this tag
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        "@apex//common/configuration/settings",
        "@apex//grace/automotive_diagnose/common/test:common_test_lib",
        "@apex//grace/automotive_diagnose/diagnostic_fault_manager:diagnostic_fault_manager_node",
        "@apex//grace/automotive_diagnose/diagnostic_fault_manager_msgs",
        "@apex//grace/automotive_diagnose/uds_msgs",
        "@apex//grace/tools/apex_integration_test_node",
        "@googletest//:gtest_main",
    ],
)

cc_test_in_install_space(
    name = "test_diagnostic_fault_manager_persistency",
    srcs = [
        "storage_service_runner.hpp",
        "test_dfm_builder.cpp",
        "test_dfm_persistency.cpp",
    ],
    copts = ["-std=c++17"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    install_space = ":install_space_for_tests",
    tags = [
        "skip_asan",  # FIXME: fix asan findings and remove this tag
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        "@apex//grace/automotive_diagnose/common/test:common_test_lib",
        "@apex//grace/automotive_diagnose/diagnostic_fault_manager:diagnostic_fault_manager_node",
        "@apex//grace/automotive_diagnose/diagnostic_fault_manager_msgs",
        "@apex//grace/automotive_diagnose/uds_msgs",
        "@apex//grace/configuration/storage/storage",
        "@apex//grace/tools/apex_integration_test_node",
        "@googletest//:gtest_main",
    ],
)
