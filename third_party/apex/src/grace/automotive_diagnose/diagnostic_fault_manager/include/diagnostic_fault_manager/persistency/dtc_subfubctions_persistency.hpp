/// \copyright Copyright 2023 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief This abstract interface is designed for the 'Read DTC' subfunctions, which require data
/// from persistency/storage.

#ifndef DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__DTC_SUBFUNCTIONS_PERSISTENCY_HPP_
#define DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__DTC_SUBFUNCTIONS_PERSISTENCY_HPP_

#include <utility>

#include "containers/static_vector.hpp"
#include "cpputils/noncopyable.hpp"
#include "cpputils/optional.hpp"
#include "diagnostic_fault_manager/config/dfm_config.hpp"
#include "diagnostic_fault_manager/visibility_control.hpp"


namespace apex
{
namespace diagnostic_fault_manager
{
namespace persistency
{

/// \class DtcSubfunctionsPersistency
/// \brief This abstract interface is designed for the 'Read DTC' subfunctions, which require data
/// from persistency/storage.
class DtcSubfunctionsPersistency : private apex::NonCopyable
{
public:
  DtcSubfunctionsPersistency() = default;
  virtual ~DtcSubfunctionsPersistency() noexcept = default;

  /// \brief Gets DTCs for a given status mask.
  /// \param[in] dtc_status_mask status mask of the searched DTCs.
  /// \param[in] memory_area identifies memory area from which DTC statuses should be returned
  /// (other memory areas will be skipped).
  /// \return DTCs (DTCStatusAvailabilityMask + DTCAndStatusRecord).
  virtual const static_vector<uint8_t> & get_dtc_by_status_mask(
    const uint8_t dtc_status_mask, const optional<uint8_t> & memory_area = nullopt) = 0;
  /// \brief Gets DTCs for a given readiness and functional group.
  /// \param[in] readiness_group_id readiness group of the searched DTCs.
  /// \param[in] functional_group_id functional group of the searched DTCs.
  /// \return DTCs (DTCMask (3bytes) + DTCStatus)
  virtual const static_vector<uint8_t> & get_dtc_by_readiness_group_identifier(
    uint8_t readiness_group_id, uint8_t functional_group_id) = 0;
  /// \brief Gets all supported DTCs (defined in the configuration file).
  /// \return DTCs (DTCStatusAvailabilityMask + DTCAndStatusRecord).
  virtual const static_vector<uint8_t> & get_supported_dtcs() = 0;
  /// \brief Gets DTC detection counter for all currently stored DTCs.
  /// \return DTCs (DTCMask (3bytes) + DTCDetectionCounter).
  virtual const static_vector<uint8_t> & get_dtc_detection_counters() = 0;
  /// \brief Gets DTC which has the most recent time point value and confirmed status.
  /// \return The most recent and confirmed DTC (DTCStatusAvailabilityMask + DTCAndStatusRecord).
  virtual const static_vector<uint8_t> & get_most_recent_confirmed_dtc() = 0;
  /// \brief Gets DTCs which support given extended data record id.
  /// \param[in] record_id extended data record identifier.
  /// \return DTCs (DTCMask (3bytes) + DTCStatus).
  virtual const static_vector<uint8_t> & get_extended_data_record_dtcs(uint8_t record_id) = 0;
  /// \brief Gets extended data for a given data record id from all DTCs that support it.
  /// \param[in] record_id extended data record identifier.
  /// \return ExtendedData (DTCMask (3bytes) + DTCStatus + ExtendedData).
  virtual const static_vector<uint8_t> & get_extended_data_records_by_record_number(
    uint8_t record_id) = 0;
  /// \brief Gets extended data for a given data record ID from a DTC identified by the provided
  /// argument.
  /// \param[in] dtc_mask dtc's mask of the searched DTC.
  /// \param[in] record_id If the provided extended data record identifier is equal to 0xFF, then
  /// all extended data records will be returned.
  /// \param[in] memory_area_id identifies memory area from which DTC extended_data should be
  /// returned.
  /// \return ExtendedData (record id + ExtendedData).
  virtual const static_vector<uint8_t> & get_dtc_extended_data(
    const config::DtcMask & dtc_mask,
    uint8_t record_id,
    const optional<uint8_t> & mem_area_id = nullopt) = 0;
};

}  // namespace persistency
}  // namespace diagnostic_fault_manager
}  // namespace apex

#endif  // DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__DTC_SUBFUNCTIONS_PERSISTENCY_HPP_
