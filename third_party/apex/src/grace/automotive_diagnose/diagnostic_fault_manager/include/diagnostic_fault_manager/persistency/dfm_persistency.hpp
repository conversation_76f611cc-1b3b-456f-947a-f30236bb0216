/// \copyright Copyright 2023 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief apex::storage service based implementation of the abstract database interface.

#ifndef DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__DFM_PERSISTENCY_HPP_
#define DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__DFM_PERSISTENCY_HPP_
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>

#include "cpputils/optional.hpp"
#include "diagnostic_common/node_context.hpp"
#include "diagnostic_fault_manager/config/dfm_config.hpp"
#include "diagnostic_fault_manager/dtc_status_broadcast/dtc_change_publisher_base.hpp"
#include "diagnostic_fault_manager/persistency/dtc_subfubctions_persistency.hpp"
#include "diagnostic_fault_manager/persistency/persistency_base.hpp"
#include "diagnostic_fault_manager/persistency/snapshot_persistency_base.hpp"
#include "diagnostic_fault_manager/visibility_control.hpp"
#include "logging/logging.hpp"
#include "storage/api/storage.hpp"

#include "diagnostic_fault_manager_msgs/msg/dtc_db.hpp"
#include "diagnostic_fault_manager_msgs/msg/dtc_extended_data_db.hpp"
#include "diagnostic_fault_manager_msgs/msg/dtc_snapshot_db.hpp"

namespace apex
{
namespace diagnostic_fault_manager
{
namespace persistency
{

namespace storage = apex::storage;

using mem_area_to_ext_data_store_map =
  std::unordered_map<uint8_t, storage::store<ExtendedDataDb>::shared_ptr>;
using mem_area_to_dtc_store_map = std::unordered_map<uint8_t, storage::store<DtcDb>::shared_ptr>;
using mem_area_to_snapshot_data_store_map =
  std::unordered_map<uint8_t, storage::store<DtcSnapshotDb>::shared_ptr>;

/// \class DfmPersistency
/// \brief Implementation of PersistencyBase abstract persistency interface based on
/// `apex::persistency` service.
class DIAGNOSTIC_FAULT_MANAGER_PUBLIC DfmPersistency : public PersistencyBase,
                                                       public DtcSubfunctionsPersistency
{
public:
  /// \brief Construct new DfmPersistency object.
  /// \param[in] node_context context of the parent node.
  /// \param[in] config configuration read from the settings file.
  /// \param[in] dtc_change_publisher pointer to the DTC status change publisher.
  /// \param[in] db_operations_timeout The maximum time to wait for the storage service response.
  /// \param[in] db_instance_name Name of the storage instance to get and use as a physical
  /// database.
  explicit DfmPersistency(
    const diagnostic_common::NodeContextRef & node_context,
    std::shared_ptr<const config::DfmConfig> config,
    std::shared_ptr<dtc_status_broadcast::DtcChangePublisherBase> dtc_change_publisher,
    std::chrono::nanoseconds db_operations_timeout = std::chrono::seconds{1},
    const std::string & db_instance_name = "");

  // PersistencyBase methods:
  /// \copydoc PersistencyBase::store_dtc
  bool store_dtc(DtcDb & dtc_data) override;
  /// \copydoc PersistencyBase::get_dtc
  std::shared_ptr<DtcDb> get_dtc(const config::DtcMask & dtc_mask) override;
  /// \copydoc PersistencyBase::get_first_reported_dtc
  std::shared_ptr<DtcDb> get_first_reported_dtc() override;
  /// \copydoc PersistencyBase::get_last_reported_dtc
  std::shared_ptr<DtcDb> get_last_reported_dtc() override;
  /// \copydoc PersistencyBase::get_first_confirmed_dtc
  std::shared_ptr<DtcDb> get_first_confirmed_dtc() override;
  /// \copydoc PersistencyBase::get_number_of_dtc_by_status_mask
  uint16_t get_number_of_dtc_by_status_mask(const uint8_t dtc_status_mask) const override;
  /// \copydoc PersistencyBase::get_number_of_dtc_by_severity_mask_record
  uint16_t get_number_of_dtc_by_severity_mask_record(uint8_t severity_mask,
                                                     uint8_t dtc_status_mask) const override;
  /// \copydoc PersistencyBase::clear_dtc()
  void clear_dtc(const optional<uint8_t> & mem_area_id = nullopt) override;
  /// \copydoc PersistencyBase::clear_dtc(config::DtcMask mask)
  void clear_dtc(config::DtcMask mask, const optional<uint8_t> & mem_area_id = nullopt) override;
  /// \copydoc PersistencyBase::clear_dtc(uint8_t dtc_functional_group_id)
  void clear_dtc(uint8_t dtc_functional_group_id,
                 const optional<uint8_t> & mem_area_id = nullopt) override;
  /// \copydoc PersistencyBase::reset_dtc_status_cycle_related_bits()
  void reset_dtc_status_cycle_related_bits() override;
  /// \copydoc PersistencyBase::store_dtc_snapshot
  bool store_dtc_snapshot(const config::DtcMask & dtc_mask,
                          std::shared_ptr<DtcSnapshotDb> snapshot_data) override;
  /// \copydoc PersistencyBase::get_dtc_snapshot
  std::shared_ptr<DtcSnapshotDb> get_dtc_snapshot(
    const config::DtcMask & dtc_mask, const optional<uint8_t> & mem_area_id = nullopt) override;
  /// \copydoc PersistencyBase::store_dtc_extended_data
  bool store_dtc_extended_data(const config::DtcMask & dtc_mask,
                               const std::shared_ptr<extended_data_records> extended_data) override;

  // DtcSubfunctionsPersistency methods:
  /// \copydoc DtcSubfunctionsPersistency::get_most_recent_confirmed_dtc
  const static_vector<uint8_t> & get_most_recent_confirmed_dtc() override;
  /// \copydoc DtcSubfunctionsPersistency::get_dtc_by_status_mask
  const static_vector<uint8_t> & get_dtc_by_status_mask(
    const uint8_t dtc_status_mask, const optional<uint8_t> & memory_area = nullopt) override;
  /// \copydoc DtcSubfunctionsPersistency::get_dtc_by_readiness_group_identifier
  const static_vector<uint8_t> & get_dtc_by_readiness_group_identifier(
    uint8_t readiness_group_id, uint8_t functional_group_id) override;
  /// \copydoc DtcSubfunctionsPersistency::get_supported_dtcs
  const static_vector<uint8_t> & get_supported_dtcs() override;
  /// \copydoc DtcSubfunctionsPersistency::get_dtc_detection_counters
  const static_vector<uint8_t> & get_dtc_detection_counters() override;
  /// \copydoc DtcSubfunctionsPersistency::get_extended_data_record_dtcs
  const static_vector<uint8_t> & get_extended_data_record_dtcs(uint8_t record_id) override;
  /// \copydoc DtcSubfunctionsPersistency::get_extended_data_records_by_record_number
  const static_vector<uint8_t> & get_extended_data_records_by_record_number(
    uint8_t record_id) override;
  /// \copydoc DtcSubfunctionsPersistency::get_dtc_extended_data
  const static_vector<uint8_t> & get_dtc_extended_data(
    const config::DtcMask & dtc_mask,
    uint8_t record_id,
    const optional<uint8_t> & mem_area_id = nullopt) override;

private:
  /// \brief Loads a data store database based on a given data store type.
  ///
  /// This template method abstracts the common functionality of loading different types of data
  /// store databases. It iterates over the DTC configurations and uses the provided getter to
  /// access the correct type of data store from the DtcDatabases structure.
  /// \tparam DataStoreType The type of the data store (e.g., ExtendedDataDb or DtcSnapshotDb).
  /// \tparam StoreMapType The type of the map to be returned, mapping uint32_t to the data store
  /// map.
  /// \tparam DataStoreGetter The type of the member pointer used to access the correct data store.
  /// \param getter Pointer to the member of DtcDatabases that returns the required data store.
  /// \return std::unordered_map<uint32_t, StoreMapType> A map of the loaded data stores.
  template <typename DataStoreType, typename StoreMapType, typename DataStoreGetter>
  std::unordered_map<uint32_t, StoreMapType> load_data_store_db(DataStoreGetter getter)
  {
    std::unordered_map<uint32_t, StoreMapType> data_store_maps;

    for (const auto & [mask, dtc] : m_dfm_config->m_dtcs) {
      for (const auto dtc_mem_area : dtc.m_mem_area_ids) {
        auto it = m_mem_area_to_dtc_db.find(dtc_mem_area);
        if (it == m_mem_area_to_dtc_db.end()) {
          APEX_WARN(m_logger, "Wrong DTC memory area id was requested");
          continue;
        }

        auto & dtc_db = it->second;
        data_store_maps[mask][dtc_mem_area] =
          (dtc_db.*getter)
            ->template get_store<DataStoreType>(calculate_dtc_store_hash(mask, dtc_mem_area));
      }
    }
    return data_store_maps;
  }
  /// \brief Creates stores and read all dtcs extended data kept in storage (non-volatile
  /// memory).
  /// \return Container for extended data database stores (1 store per 1 DTC memory area).
  std::unordered_map<uint32_t, mem_area_to_ext_data_store_map> load_extended_data_db()
  {
    return load_data_store_db<ExtendedDataDb, mem_area_to_ext_data_store_map>(
      &DtcDatabases::m_extended_data);
  }
  /// \brief Creates stores and read all dtc snapshots data kept in persistency (non-volatile
  /// memory).
  /// \return Container for DTC snapshot database stores (1 store per 1 DTC memory area).
  std::unordered_map<uint32_t, mem_area_to_snapshot_data_store_map> load_snapshots_db()
  {
    return load_data_store_db<DtcSnapshotDb, mem_area_to_snapshot_data_store_map>(
      &DtcDatabases::m_snapshot_data);
  }
  /// \brief Creates stores and read all DTC data kept in storage (non-volatile memory).
  /// \return Container with DTCs database stores for every DTC memory area(1 store per 1 DTC memory
  /// area).
  std::unordered_map<uint32_t, mem_area_to_dtc_store_map> load_dtcs_db()
  {
    return load_data_store_db<DtcDb, mem_area_to_dtc_store_map>(&DtcDatabases::m_dtcs);
  }


  /// \struct DtcDatabases
  /// \brief Encapsulates all database instances (DTC, ext_data, snapshot_data) for a single DTC
  /// memory area.
  struct DtcDatabases
  {
    const storage::database::shared_ptr m_dtcs;
    const storage::database::shared_ptr m_extended_data;
    const storage::database::shared_ptr m_snapshot_data;
  };

  /// \brief Calculates summed hash value of the DTC mask and its memory area id.
  /// \param[in] dtc_mask dtc's mask of the DTC.
  /// \param[in,out] mem_area_id of given DTC memory area.
  /// \return Hash value.
  uint32_t calculate_dtc_store_hash(const config::DtcMask & mask, uint8_t mem_area_id);
  /// \brief Creates database pointers based on static DTC memory area configuration.
  std::unordered_map<uint8_t, const DtcDatabases> load_dtc_db_instances();
  /// \brief Try to gets DTC data from the database.
  /// \param[in] dtc_mask dtc's mask of the searched DTC.
  /// \param[in] memory_area_id identifies memory area from which DTC should be returned.
  /// \return True in case of success, false otherwise.
  bool try_get_dtc_data(const config::DtcMask & dtc_mask,
                        const optional<uint8_t> & mem_area_id = nullopt) const;
  /// \brief Verifies if DTC status mask has set confirmed or pending value.
  /// \param[in] dtc_status status to verify.
  /// \return True when status mask contains pending or confirmed status.
  bool is_status_pending_or_confirmed(const uint8_t dtc_status) const noexcept;
  /// \brief Reads from database and verifies if DTC has set confirmed or pending value.
  /// \param[in] dtc_mask dtc's mask of the verified DTC.
  /// \param[in,out] dynamic_dtc_data_buffer buffer to fill with obtained DTC data.
  /// \param[in] memory_area_id identifies memory area from which DTC should be returned.
  /// \return True when status mask from given DTC contains pending or confirmed status.
  bool is_dtc_pending_or_confirmed(const config::DtcMask & dtc_mask,
                                   const std::shared_ptr<DtcDb> & dynamic_dtc_data_buffer,
                                   const optional<uint8_t> & mem_area_id = nullopt) const;
  /// \brief Save given DTC data in the non-volatile memory for all DTC memory areas.
  /// \param[in] dtc_data DTC data to store.
  /// \param[in] dtc_mask dtc's mask of the DTC to store.
  void save_dtc_in_all_dtc_memory_areas(DtcDb & dtc_data, const config::DtcMask & mask);
  /// \brief Gets DTC from the database for a primary memory area (defined in the settings file).
  /// \param[in] dtc_mask dtc's mask of the DTC to get.
  /// \return True in case of success, false otherwise.
  bool get_dtc_from_primary_memory_area(const config::DtcMask & dtc_mask) const;
  /// \brief Clears DTC data stored in all memory areas.
  /// \param[in] dtc_mask dtc's mask of the DTC to clear.
  void clear_all_dtc_memory_areas(const config::DtcMask & mask);
  /// \brief Save given DTC extended data in the non-volatile memory for all DTC memory areas.
  /// \param[in] dtc_mask dtc's mask of the DTC to store.
  void save_extended_data_to_all_mem_area(const config::DtcMask & dtc_mask);
  /// \brief Gets DTC extended data from the database for a primary memory area (defined in the
  /// settings file).
  /// \param[in] dtc_mask dtc's mask of the DTC to get.
  /// \param[in,out] extended_data reference to read DTC extended data.
  /// \return True in case of success, false otherwise.
  bool get_ext_data_from_primary_area(const config::DtcMask & dtc_mask,
                                      ExtendedDataDb & extended_data);
  /// \brief Gets DTC extended data from the database for a given memory area.
  /// \param[in] dtc_mask dtc's mask of the DTC to get.
  /// \param[in,out] extended_data reference to read DTC extended data.
  /// \param[in] mem_area_id id of the given memory area.
  /// \return True in case of success, false otherwise.
  bool get_ext_data_from_mem_area(const config::DtcMask & dtc_mask,
                                  ExtendedDataDb & extended_data,
                                  const optional<uint8_t> & mem_area_id = nullopt);
  /// \brief Clears DTC data from a given memory area.
  /// \param[in] mask dtc's mask of the DTC to clear.
  /// \param[in] mem_area_id id of the given memory area.
  void clear_dtc_for_mem_area(const config::DtcMask & mask, uint8_t mem_area_id);

  // TODO(Michal.Faferek) Using the storage interface here requires its certification.
  const storage::communication::shared_ptr m_storage_com;
  /// \brief Keeps static DTC configuration.
  std::shared_ptr<const config::DfmConfig> m_dfm_config;
  /// \brief DtcDb internal wrapper to reuse in all persistency related operations (to avoid dynamic
  /// allocations in runtime).
  std::shared_ptr<DtcDb> m_dynamic_dtc_data_buffer;
  /// \brief Container to store all DTC database instances identified by the DTC memory area id.
  std::unordered_map<uint8_t, const DtcDatabases> m_mem_area_to_dtc_db;
  /// \brief This is a container used to track whether the stored DTC mask has already been reported
  /// or not.
  std::unordered_map<config::DtcMask, std::unordered_map<uint8_t, bool>, config::DtcHasher>
    m_occurred_dtc;
  /// \brief Container for DTCs database stores (1 store per 1 DTC memory area).
  const std::unordered_map<uint32_t, mem_area_to_dtc_store_map> m_dtc_stores;
  /// \brief Container for DTCs extended data stores (1 store per 1 DTC memory area).
  const std::unordered_map<uint32_t, mem_area_to_ext_data_store_map> m_dtc_extended_data_stores;
  /// \brief Pointer to the snapshot creator interface needed to create DTC snapshot data and store
  /// them in the persistency.
  std::unique_ptr<SnapshotPersistencyBase> m_snapshot_persistency;
  /// \brief Buffer for the DTC database extended data.
  ExtendedDataDb m_dtc_extended_data_db_buffer;
  /// \brief Buffer for the read DTC subfunction responses.
  static_vector<uint8_t> m_dtc_subfunctions_buffer;
  /// \brief The DfmPersistency logger object.
  logging::Logger<> m_logger;
  /// \brief Pointer to the DTC status change publisher.
  std::shared_ptr<dtc_status_broadcast::DtcChangePublisherBase> m_dtc_status_broadcast;
};

}  // namespace persistency
}  // namespace diagnostic_fault_manager
}  // namespace apex

#endif  // DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__DFM_PERSISTENCY_HPP_
