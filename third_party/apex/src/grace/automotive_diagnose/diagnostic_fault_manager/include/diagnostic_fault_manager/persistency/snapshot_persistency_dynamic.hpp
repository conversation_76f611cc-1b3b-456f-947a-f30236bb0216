/// \copyright Copyright 2023 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief Implements snapshot persistency-related interface with dynamic allocation during runtime
/// variant.

#ifndef DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__SNAPSHOT_PERSISTENCY_DYNAMIC_HPP_
#define DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__SNAPSHOT_PERSISTENCY_DYNAMIC_HPP_
#include <memory>
#include <unordered_map>

#include "containers/static_vector.hpp"
#include "cpputils/optional.hpp"
#include "diagnostic_fault_manager/config/dfm_config.hpp"
#include "diagnostic_fault_manager/persistency/snapshot_persistency_base.hpp"
#include "diagnostic_fault_manager/visibility_control.hpp"

#include "diagnostic_fault_manager_msgs/msg/dtc_snapshot_db.hpp"
#include "diagnostic_fault_manager_msgs/msg/snapshot_record_data.hpp"


namespace apex
{
namespace diagnostic_fault_manager
{

namespace persistency
{

/// \class SnapshotPersistencyDynamic
/// \brief Implements snapshot persistency-related interface with dynamic allocation during runtime
/// variant.
class SnapshotPersistencyDynamic : public SnapshotPersistencyBase
{
public:
  /// \brief Constructs new SnapshotPersistencyDynamic object.
  /// \param[in] config configuration read from the configuration file.
  /// \param[in] dtc_snapshot_stores database store instances (1 per DTC memory area) needed to save
  /// snapshot data in the non-volatile memory.
  SnapshotPersistencyDynamic(
    std::shared_ptr<const config::DfmConfig> config,
    std::unordered_map<uint32_t, mem_area_to_snapshot_data_store_map> && dtc_snapshot_stores);

  /// \copydoc SnapshotPersistencyBase::get_dtc_snapshot
  std::shared_ptr<DtcSnapshotDb> get_dtc_snapshot(
    const config::DtcMask & dtc_mask, const optional<uint8_t> & mem_area_id = nullopt) override;

private:
  /// \brief Contains mapping between DTCs and related allocated snapshots.
  std::unordered_map<uint32_t, std::shared_ptr<DtcSnapshotDb>> m_dtc_snapshot_cache;
};

}  // namespace persistency
}  // namespace diagnostic_fault_manager
}  // namespace apex

#endif  // DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__SNAPSHOT_PERSISTENCY_DYNAMIC_HPP_
