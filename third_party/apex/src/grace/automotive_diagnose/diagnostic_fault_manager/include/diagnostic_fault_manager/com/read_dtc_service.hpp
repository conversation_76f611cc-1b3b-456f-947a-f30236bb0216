/// \copyright Copyright 2023 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief DTCC (DiagnosticTroubleCodeCommunication) implementation for handling UDS read DTC
/// requests.

#ifndef DIAGNOSTIC_FAULT_MANAGER__COM__READ_DTC_SERVICE_HPP_
#define DIAGNOSTIC_FAULT_MANAGER__COM__READ_DTC_SERVICE_HPP_

#include <algorithm>
#include <memory>

#include "cpputils/optional.hpp"
#include "diagnostic_common/node_context.hpp"
#include "diagnostic_fault_manager/persistency/dtc_subfubctions_persistency.hpp"
#include "diagnostic_fault_manager/persistency/persistency_base.hpp"
#include "diagnostic_fault_manager/visibility_control.hpp"
#include "executor2/apex_node_base.hpp"
#include "logging/logging.hpp"

#include "uds_msgs/srv/uds_read_dtc.hpp"

namespace apex
{
namespace diagnostic_fault_manager
{
namespace com
{

using PersistencyBase = apex::diagnostic_fault_manager::persistency::PersistencyBase;
using DtcSubfunctionsPersistency =
  apex::diagnostic_fault_manager::persistency::DtcSubfunctionsPersistency;
using UdsReadDtc = uds_msgs::srv::UdsReadDtc;
using Subfunction = UdsReadDtc::Request::BorrowedType::Subfunction;
using ReadResponseCode = UdsReadDtc::Response::BorrowedType::ResponseCode;
using ReadResponse = UdsReadDtc::Response::BorrowedType;

/// \brief DTCC (DiagnosticTroubleCodeCommunication) executable_item which handles UDS readDTC
/// requests.
class DIAGNOSTIC_FAULT_MANAGER_PUBLIC ReadDtcService : public apex::executor::executable_item
{
public:
  /// \brief Construct new ReadDtcService object.
  /// \param[in] node_context context of the parent node.
  /// \param[in] persistency interface pointer to obtain required DTC data from the database.
  /// \param[in] dtc_subfunctions_persistency interface pointer to obtain required read DTC
  /// subfunctions related data from the database.
  /// \param[in] config configuration read from the configuration file.
  ReadDtcService(const diagnostic_common::NodeContextRef & node_context,
                 std::shared_ptr<PersistencyBase> persistency,
                 std::shared_ptr<DtcSubfunctionsPersistency> dtc_subfunctions_persistency,
                 std::shared_ptr<const config::DfmConfig> config);

private:
  /// \brief Updates read DTC response with static response data and number of DTCS returned by F
  /// function.
  /// Byte 1: DTCStatusAvailabilityMask Byte 2: DTCFormatIdentifier Bytes 3-4:
  /// DTCCount{DTCCountHighByte, DTCCountLowByte}
  /// \tparam F - callable type which returns number of DTCs consistent with processed subfunction.
  /// \param[in] dtc_counter - A function that counts the number of DTC codes based on user-defined
  /// criteria. The function takes a collection of DTC codes and returns an integer representing the
  /// count of DTC codes that meet the specified criteria. This argument allows users to customize
  /// the counting process according to their specific needs and requirements.
  template <typename F>
  /*
   AXIVION Next Construct MisraC++2023-28.6.2: Reason: Code Quality (Functional suitability),
   Justification: Only argument types need to be
   forwarded, 'dtc_counter' is a callable here
   */
  void report_number_of_dtcs(ReadResponse & response, F && dtc_counter)
  {
    // DTCStatusAvailabilityMask
    response.payload.push_back(m_dfm_config->m_dtc_availability_mask);
    // DTCFormatIdentifier
    response.payload.push_back(m_dfm_config->m_dtc_format_id);
    // DTCCount
    const uint16_t num_of_dtcs = dtc_counter();
    /*
     AXIVION DISABLE STYLE MisraC++2023-4.1.3, MisraC++2023-7.0.5: Reason: Code Quality
     (Functional suitability), Justification: Intentional type conversion to uint8_t
     */
    response.payload.push_back(static_cast<uint8_t>((num_of_dtcs >> 8) & uint16_t{0xFF}));
    response.payload.push_back(static_cast<uint8_t>(num_of_dtcs & uint16_t{0xFF}));
    // AXIVION ENABLE STYLE MisraC++2023-7.0.5
    // ReadResponseCode
    response.response_code = ReadResponseCode::POSITIVE_ACK;
  }

  /// \brief Updates read DTC response with static response data and DTCs returned by F function.
  /// Byte 1: DTCStatusAvailabilityMask
  /// Byte 2 to n: DTCAndStatusRecord[] = {
  /// DTCHighByte#1
  /// DTCMiddleByte#1
  /// DTCLowByte#1
  /// statusOfDTC#1
  /// DTCHighByte#2
  /// ...
  /// }
  /// \tparam F - callable type which returns demanded DTC data.
  /// \param[in] dtcExtractor - extracts DTC codes based on user-defined criteria. The function
  /// takes a collection of DTC codes and returns a collection of DTC codes that meet the specified
  /// criteria. This argument allows users to customize the extraction process according to their
  /// specific needs and requirements.
  template <typename F>
  /*
   AXIVION Next Construct MisraC++2023-28.6.2: Reason: Code Quality (Functional suitability),
   Justification: Only argument types need to be
   forwarded, 'dtc_extractor' is a callable here
   */
  void report_dtcs(ReadResponse & response, F && dtc_extractor)
  {
    // DTCStatusAvailabilityMask
    response.payload.push_back(m_dfm_config->m_dtc_availability_mask);
    // DTCs with status
    const auto & dtc_data = dtc_extractor();
    (void)std::copy(dtc_data.begin(), dtc_data.end(), std::back_inserter(response.payload));
    // ReadResponseCode
    response.response_code = ReadResponseCode::POSITIVE_ACK;
  }

  /// \return Return all the triggering services types.
  apex::executor::service_list get_triggering_services_impl() const override;
  /// \brief Implements execution logic.
  bool execute_impl() override;
  /// \brief Handles the processing of the read DTC subfunction.
  /// This function takes a read DTC request containing the subfunction type and its parameters.
  /// Depending on the subfunction value, it uses the persistency interface to obtain the proper DTC
  /// data and returns the corresponding read DTC response.
  /// \param[in] request UDS request to handle.
  /// \param[in, out] The read DTC response with the appropriate DTC data based on the subfunction.
  void handle_read_dtc_subfunction(const UdsReadDtc::Request::BorrowedType & request,
                                   const rclcpp::LoanedResponse<ReadResponse> & loaned_response);
  /// \brief Populates the response with all, or a specified, snapshot data record for a given DTC,
  /// based on the DTC mask.
  /// \param[in] mask This is a 3-byte DTC mask for the DTC instance, which determines the snapshot
  /// data to be returned.
  /// \param[in] snapshot_record id of the snapshot record, if equal 0xFF then all records are
  /// returned.
  /// \param[in, out] response The read DTC REPORT_DTC_SNAPSHOT_RECORD_BY_DTC_NUMBER subfunction
  /// response.
  /// \param[in] memory_area_id identifies memory area from which DTC snapshot should be returned.
  /// If the argument has a nullopt value, then data from the DTC's primary memory area will be
  /// obtained.
  void prepare_report_dtc_snapshot_record_by_dtc_number(
    const config::DtcMask & mask,
    const uint8_t snapshot_record,
    ReadResponse & response,
    const optional<uint8_t> & mem_area_id = nullopt);
  /// \brief Populates the response with all available snapshot identifications.
  /// \param[in] mask This is a 3-byte DTC mask for the DTC instance, which determines the snapshot
  /// data to be returned.
  /// \param[in, out] response The read DTC REPORT_DTC_SNAPSHOT_RECORD_BY_DTC_NUMBER subfunction
  /// response.
  void prepare_report_dtc_snapshot_identification_response(const config::DtcMask & mask,
                                                           ReadResponse & response);
  /// \brief Populates the response with all DTCs consistent with given functional and readiness
  /// group id.
  /// \param[in] functional_group_id functional group id of the returned DTCs.
  /// \param[in] readiness_group_id readiness group id of the returned DTCs.
  /// \param[in, out] response The read DTC REPORT_DTC_INFORMATION_BY_DTC_READINESS_GROUP_IDENTIFIER
  /// subfunction response.
  void prepare_report_dtc_information_by_dtc_readiness_group_id_response(
    const uint8_t functional_group_id, uint8_t readiness_group_id, ReadResponse & response);
  /// \brief Populates the DTCs with their statuses that contain the specified extended data record.
  /// \param[in] ext_data_record_number extended data record id of the returned DTCs.
  /// \param[in, out] response The read DTC REPORT_DTC_EXTENDED_DATA_RECORD_IDENTIFICATION
  /// subfunction response.
  void prepare_report_dtc_extended_data_record_identification(const uint8_t ext_data_record_number,
                                                              ReadResponse & response);
  /// \brief Populates the DTCs with their statuses and related extended data based on given
  /// argument.
  /// \param[in] ext_data_record_number extended data record id of the returned DTCs.
  /// \param[in, out] response The read DTC REPORT_DTC_EXT_DATA_RECORD_BY_RECORD_NUMBER subfunction
  /// response.
  void prepare_report_dtc_ext_data_record_by_record_number(const uint8_t ext_data_record_number,
                                                           ReadResponse & response);
  /// \brief Populates the DTC extended data for a given dtc mask.
  /// \param[in] mask This is a 3-byte DTC mask for the DTC instance, which determines the snapshot
  /// data to be returned.
  /// \param[in] ext_data_record_number extended data record id of the returned DTCs, if equal 0xFF
  /// then all records are returned.
  /// \param[in, out] response The read DTC REPORT_DTC_EXT_DATA_RECORD_BY_DTC_NUMBER subfunction
  /// response.
  /// \param[in] memory_area_id identifies memory area from which DTC extended_data should be
  /// returned. If the argument has a nullopt value, then data from the DTC's primary memory area
  /// will be obtained.
  void prepare_report_dtc_ext_data_record_by_dtc_number(
    const config::DtcMask & mask,
    const uint8_t ext_data_record_number,
    ReadResponse & response,
    const optional<uint8_t> & mem_area_id = nullopt);

  /// \brief Service instance to handle uds_msgs::srv::UdsReadDtc requests.
  const rclcpp::PollingService<UdsReadDtc>::SharedPtr m_service;
  /// \brief Pointer to the DTC persistency (database) interface.
  std::shared_ptr<PersistencyBase> m_persistency;
  /// \brief Pointer to the read DTC subfunctions related persistency interface.
  std::shared_ptr<DtcSubfunctionsPersistency> m_dtc_subfunctions_persistency;
  /// \brief Pointer to the DFM configuration/setting with DTC static data.
  std::shared_ptr<const config::DfmConfig> m_dfm_config;
};

}  // namespace com
}  // namespace diagnostic_fault_manager
}  // namespace apex

#endif  // DIAGNOSTIC_FAULT_MANAGER__COM__READ_DTC_SERVICE_HPP_
