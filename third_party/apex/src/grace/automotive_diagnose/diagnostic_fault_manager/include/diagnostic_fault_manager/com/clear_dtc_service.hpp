/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief DTCC (DiagnosticTroubleCodeCommunication) implementation for handling UDS clear DTC
/// requests.


#ifndef DIAGNOSTIC_FAULT_MANAGER__COM__CLEAR_DTC_SERVICE_HPP_
#define DIAGNOSTIC_FAULT_MANAGER__COM__CLEAR_DTC_SERVICE_HPP_

#include <algorithm>
#include <memory>

#include "cpputils/optional.hpp"
#include "diagnostic_common/node_context.hpp"
#include "diagnostic_fault_manager/persistency/persistency_base.hpp"
#include "diagnostic_fault_manager/visibility_control.hpp"
#include "executor2/apex_node_base.hpp"
#include "logging/logging.hpp"

#include "diagnostic_fault_manager_msgs/msg/dfm_commands.hpp"
#include "uds_msgs/srv/uds_clear_dtc.hpp"

namespace apex
{
namespace diagnostic_fault_manager
{
namespace com
{

using PersistencyBase = apex::diagnostic_fault_manager::persistency::PersistencyBase;
using UdsClearDtc = uds_msgs::srv::UdsClearDtc;
using ClearResponseCode = UdsClearDtc::Response::BorrowedType::ResponseCode;
using DfmCommandsMsg = diagnostic_fault_manager_msgs::msg::DFMCommands;

/// \brief DTCC (DiagnosticTroubleCodeCommunication) executable_item, which handles UDS clear DTC
/// requests and triggers proper actions on the persistency side (possible request variants: clear
/// all dtcs, clear dtcs with the requested functional group or clear dtc with the requested DTC
/// mask).
class DIAGNOSTIC_FAULT_MANAGER_PUBLIC ClearDtcService : public executor::executable_item
{
public:
  /// \brief Construct new ClearDtcService object.
  /// \param[in] node_context context of the parent node.
  /// \param[in] persistency interface pointer to obtain required DTC data from the database.
  /// \param[in] config configuration read from the configuration file.
  ClearDtcService(const diagnostic_common::NodeContextRef & node_context,
                  std::shared_ptr<PersistencyBase> persistency,
                  std::shared_ptr<const config::DfmConfig> config);

private:
  /// \return Return all the triggering services types.
  executor::service_list get_triggering_services_impl() const override;
  /// \brief Implements execution logic.
  bool execute_impl() override;
  /// \brief Process the clear DTC request.
  /// Depending on the parameters variant, the persistency interface is used to call a proper
  /// clear action.
  /// \param[in] request UDS clear request to process.
  /// \param[in, out] The clear DTC response which contains the result code.
  void process_clear_dtc_request(
    const UdsClearDtc::Request::BorrowedType & request,
    const rclcpp::LoanedResponse<UdsClearDtc::Response::BorrowedType> & loaned_response);
  /// \brief Sends a clear diagnostic command to fault monitors based on calculated event IDs.
  /// \param[in] target_type Determines whether the command should be sent to all FMs or only to the
  /// explicitly calculated ones.
  /// \param[in] dtc_mask Optional DTC mask which is used when the command should be sent to
  /// Fault Monitors related only to a given DTC.
  /// \param[in] dtc_functional_group Optional DTC functional group which is used when the command
  /// should be sent to Fault Monitors related only to a given functional group.
  void send_clear_command_to_fault_monitors(
    const DfmCommandsMsg::BorrowedType::CommandTargetEnum target_type,
    optional<config::DtcMask> dtc_mask,
    optional<uint8_t> dtc_functional_group);

  /// \brief Service instance to handle uds_msgs::srv::UdsClearDtc requests.
  const rclcpp::PollingService<UdsClearDtc>::SharedPtr m_service;
  /// \brief Pointer to the DTC persistency (database) interface.
  const std::shared_ptr<PersistencyBase> m_persistency;
  /// \brief Publisher of the DFMCommands (Fault Monitors commands).
  const rclcpp::Publisher<DfmCommandsMsg>::SharedPtr m_command_pub;
  /// \brief Pointer to the DFM configuration/setting with DTC static data.
  const std::shared_ptr<const config::DfmConfig> m_dfm_config;
};

}  // namespace com
}  // namespace diagnostic_fault_manager
}  // namespace apex

#endif  // DIAGNOSTIC_FAULT_MANAGER__COM__CLEAR_DTC_SERVICE_HPP_
