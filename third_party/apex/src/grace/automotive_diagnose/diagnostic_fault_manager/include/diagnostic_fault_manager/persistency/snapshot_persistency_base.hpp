/// \copyright Copyright 2023 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief Base interface to create DTC database snapshot instances.

#ifndef DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__SNAPSHOT_PERSISTENCY_BASE_HPP_
#define DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__SNAPSHOT_PERSISTENCY_BASE_HPP_
#include <memory>
#include <unordered_map>
#include <utility>

#include "cpputils/noncopyable.hpp"
#include "cpputils/optional.hpp"
#include "diagnostic_fault_manager/config/dfm_config.hpp"
#include "diagnostic_fault_manager/visibility_control.hpp"
#include "storage/api/storage.hpp"

#include "diagnostic_fault_manager_msgs/msg/dtc_snapshot_db.hpp"


namespace apex
{
namespace diagnostic_fault_manager
{

namespace persistency
{
namespace storage = apex::storage;

using DtcSnapshotDb = diagnostic_fault_manager_msgs::msg::DtcSnapshotDb;
using mem_area_to_snapshot_data_store_map =
  std::unordered_map<uint8_t, storage::store<DtcSnapshotDb>::shared_ptr>;

/// \class SnapshotPersistencyBase
/// \brief Base interface to create DTC database snapshot instances.
class SnapshotPersistencyBase : private apex::NonCopyable
{
public:
  /// \brief Constructs new SnapshotPersistencyBase object.
  /// \param[in] config configuration read from the configuration file.
  /// \param[in] dtc_snapshot_stores database store instances (1 per DTC memory area) needed to save
  /// snapshot data in the non-volatile memory.
  SnapshotPersistencyBase(
    std::shared_ptr<const config::DfmConfig> config,
    std::unordered_map<uint32_t, mem_area_to_snapshot_data_store_map> && dtc_snapshot_stores)
  : m_dfm_config(std::move(config)), m_dtc_snapshot_stores(std::move(dtc_snapshot_stores))
  {
    assert(m_dfm_config);
  }

  virtual ~SnapshotPersistencyBase() noexcept = default;

  /// \brief Stores a DTC snapshot based on the provided DTC mask.
  /// \param[in] dtc_mask The DTC mask of related snapshots data.
  /// \param[in] snapshot_data dtc already collected snapshot data to store.
  /// \return true if snapshot data was stored without errors, false otherwise.
  bool store_dtc_snapshot(const config::DtcMask & dtc_mask,
                          std::shared_ptr<DtcSnapshotDb> snapshot_data)
  {
    const auto snapshot_store = m_dtc_snapshot_stores.find(dtc_mask);
    if (snapshot_store == m_dtc_snapshot_stores.end() || !snapshot_data) {
      return false;
    }
    snapshot_data->mask_record = dtc_mask;

    // save in all DTC memory areas
    for (const auto mem_area : m_dfm_config->m_dtcs.at(dtc_mask).m_mem_area_ids) {
      snapshot_store->second.at(mem_area)->save(*snapshot_data);
    }

    return true;
  }
  /// \brief Retrieves a DTC snapshot data from the database based on the provided DTC mask.
  /// \param[in] dtc_mask The DTC mask of related snapshots data.
  /// \param[in] memory_area_id identifies memory area from which DTC snapshot should be returned.
  /// \return shared pointer to the dtc snapshot database instance.
  virtual std::shared_ptr<DtcSnapshotDb> get_dtc_snapshot(
    const config::DtcMask & dtc_mask, const optional<uint8_t> & mem_area_id = nullopt) = 0;

protected:
  /// \brief Keeps static DTC configuration.
  std::shared_ptr<const config::DfmConfig> m_dfm_config;
  /// \brief Container for DTC snapshot database stores (1 store per 1 DTC memory area).
  const std::unordered_map<uint32_t, mem_area_to_snapshot_data_store_map> m_dtc_snapshot_stores;
};

}  // namespace persistency
}  // namespace diagnostic_fault_manager
}  // namespace apex

#endif  // DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__DFM_PERSISTENCY_BASE_HPP_
