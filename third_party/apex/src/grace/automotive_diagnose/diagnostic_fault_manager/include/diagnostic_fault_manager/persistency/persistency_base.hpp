/// \copyright Copyright 2023 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief Abstract interface for the DTC persistency functionality.

#ifndef DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__PERSISTENCY_BASE_HPP_
#define DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__PERSISTENCY_BASE_HPP_
#include <memory>
#include <unordered_map>
#include <utility>

#include "containers/static_vector.hpp"
#include "cpputils/noncopyable.hpp"
#include "cpputils/optional.hpp"
#include "diagnostic_fault_manager/config/dfm_config.hpp"
#include "diagnostic_fault_manager/snapshot/snapshot_creator_base.hpp"
#include "diagnostic_fault_manager/visibility_control.hpp"

#include "diagnostic_fault_manager_msgs/msg/dtc_db.hpp"
#include "diagnostic_fault_manager_msgs/msg/dtc_extended_data_db.hpp"
#include "diagnostic_fault_manager_msgs/msg/dtc_snapshot_db.hpp"
#include "diagnostic_fault_manager_msgs/msg/snapshot_record_data.hpp"


namespace apex
{
namespace diagnostic_fault_manager
{
namespace persistency
{
using DtcDb = diagnostic_fault_manager_msgs::msg::DtcDb;
using DtcSnapshotDb = diagnostic_fault_manager_msgs::msg::DtcSnapshotDb;
using SnapshotRecordData = diagnostic_fault_manager_msgs::msg::SnapshotRecordData;
using ExtendedDataDb = diagnostic_fault_manager_msgs::msg::DtcExtendedDataDb;

/// \class PersistencyBase
/// \brief Abstract class for all DTC/diagnostic events database queries.
class PersistencyBase : private apex::NonCopyable
{
public:
  PersistencyBase() = default;
  virtual ~PersistencyBase() noexcept = default;

  /// \brief Stores given DTC data in the database.
  /// \param[in] dtc_data dtc's data to store.
  /// \return true in case of success, otherwise false.
  virtual bool store_dtc(DtcDb & dtc_data) = 0;
  /// \brief Gets DTC data from the database for a given DTC mask.
  /// \param[in] dtc_mask dtc's mask for the searched DTC.
  /// \return DTC data pointer in case of success, otherwise nullptr.
  virtual std::shared_ptr<DtcDb> get_dtc(const config::DtcMask & dtc_mask) = 0;
  /// \brief Gets first (the oldest) DTC data from the database.
  /// \return DTC data pointer of firstly reported DTC.
  virtual std::shared_ptr<DtcDb> get_first_reported_dtc() = 0;
  /// \brief Gets last (the newest) DTC data from the database.
  /// \return DTC data pointer of lastly reported DTC in case of success, otherwise nullptr.
  virtual std::shared_ptr<DtcDb> get_last_reported_dtc() = 0;
  /// \brief Gets first (the oldest) confirmed DTC data from the database.
  /// \return DTC data pointer of firstly confirmed DTC in case of success, otherwise nullptr.
  virtual std::shared_ptr<DtcDb> get_first_confirmed_dtc() = 0;
  /// \brief Gets the number of DTCs for a given status mask.
  /// \param[in] dtc_mask status mask of the searched DTCs.
  /// \return number of DTCs.
  virtual uint16_t get_number_of_dtc_by_status_mask(const uint8_t dtc_status_mask) const = 0;
  /// \brief Gets the number of all DTCs that pass this logical requirement:
  /// (((statusOfDTC & dtc_status_mask) != 0) && ((severity & severity_mask) != 0)) == TRUE
  /// \param[in] dtc_mask status mask of the searched DTCs.
  /// \param[in] severity_mask severity mask of the searched DTCs: 8 bits (3 for severity, 5 for
  /// class definition).
  /// \return number of DTCs.
  virtual uint16_t get_number_of_dtc_by_severity_mask_record(
    const uint8_t severity_mask, const uint8_t dtc_status_mask) const = 0;
  /// \brief Clears all currently stored DTCs in the persistency.
  /// \param[in] memory_area_id identifies memory area in which DTCs should be removed. If the
  /// argument has a nullopt value, then data from all DTC's memory area will be removed.
  virtual void clear_dtc(const optional<uint8_t> & mem_area_id = nullopt) = 0;
  /// \brief Clears a specific DTC using the given DTC mask.
  /// \param[in] mask The DTC mask that represents the DTC code to be cleared.
  /// \param[in] memory_area_id identifies memory area in which DTCs should be removed. If the
  /// argument has a nullopt value, then data from all DTC's memory area will be removed.
  virtual void clear_dtc(config::DtcMask mask, const optional<uint8_t> & mem_area_id = nullopt) = 0;
  /// \brief Clears DTC codes that belong to a specific functional group.
  /// \param[in] dtc_functional_group_id The ID of the functional group whose DTC codes should be
  /// cleared.
  /// \param[in] memory_area_id identifies memory area in which DTCs should be removed. If the
  /// argument has a nullopt value, then data from all DTC's memory area will be removed.
  virtual void clear_dtc(uint8_t dtc_functional_group_id,
                         const optional<uint8_t> & mem_area_id = nullopt) = 0;
  /// \brief Resets the DTC status cycle-related bits for all DTCs in the persistency database.
  /// This method sets the TEST_NOT_COMPLETED_THIS_OPERATION_CYCLE bit to true and resets the
  /// TEST_FAILED_THIS_OPERATION_CYCLE bit for every DTC in the persistency database. This is
  /// typically called at the beginning of a new operation cycle to prepare the DTCs for the
  /// upcoming diagnostics tests.
  virtual void reset_dtc_status_cycle_related_bits() = 0;
  /// \brief Stores a DTC snapshot based on the provided DTC mask.
  /// \param[in] dtc_mask The DTC mask of related snapshots data.
  /// \param[in] snapshot_data dtc already collected snapshot data to store.
  virtual bool store_dtc_snapshot(const config::DtcMask & dtc_mask,
                                  std::shared_ptr<DtcSnapshotDb> snapshot_data) = 0;
  /// \brief Retrieves a DTC snapshot data from the database based on the provided DTC mask.
  /// \param[in] dtc_mask The DTC mask of related snapshots data.
  /// \param[in] memory_area_id identifies memory area from which DTC snapshot should be returned.
  /// If the argument has a nullopt value, then data from the DTC's primary memory area will be
  /// obtained.
  /// \return shared pointer to the dtc snapshot database instance.
  virtual std::shared_ptr<DtcSnapshotDb> get_dtc_snapshot(
    const config::DtcMask & dtc_mask, const optional<uint8_t> & mem_area_id = nullopt) = 0;

  using extended_data_record = static_vector<char>;
  using extended_data_records = static_vector<extended_data_record>;
  /// \brief Stores a DTC extended data for the provided DTC mask.
  /// \param[in] dtc_mask The DTC mask of related extended data.
  /// \param[in] extended dtc already collected extended data to store.
  virtual bool store_dtc_extended_data(
    const config::DtcMask & dtc_mask,
    const std::shared_ptr<extended_data_records> extended_data) = 0;
};

}  // namespace persistency
}  // namespace diagnostic_fault_manager
}  // namespace apex

#endif  // DIAGNOSTIC_FAULT_MANAGER__PERSISTENCY__PERSISTENCY_BASE_HPP_
