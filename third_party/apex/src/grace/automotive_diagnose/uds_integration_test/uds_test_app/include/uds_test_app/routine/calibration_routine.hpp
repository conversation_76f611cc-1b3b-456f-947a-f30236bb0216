// Copyright 2023 Apex.AI, Inc
// All rights reserved.
/// \file
/// \brief The implementation of the calibration Routine

#ifndef UDS_TEST_APP__ROUTINE__CALIBRATION_ROUTINE_HPP_
#define UDS_TEST_APP__ROUTINE__CALIBRATION_ROUTINE_HPP_

#include <memory>  // for std::shared_ptr<>

#include "containers/static_vector.hpp"
#include "cpputils/optional.hpp"
#include "diagnostic_common/timer/timer_base.hpp"
#include "uds_test_app/did/did_handler.hpp"
#include "uds_test_app/routine/routine.hpp"
#include "uds_test_app/visibility_control.hpp"  // for UDS_TEST_APP_PUBLIC

#include "uds_msgs/srv/uds_routine_control.hpp"

namespace apex
{
namespace uds_test_app
{
namespace routine
{

using TimerPtr = std::shared_ptr<diagnostic_common::timer::TimerBase>;
using DidHandlerPtr = std::shared_ptr<did::DidHand<PERSON>>;

/// \class CalibrationRoutine
/// \brief The calibration routine that use the value of one DID to calibrate the second one.
class UDS_TEST_APP_PUBLIC CalibrationRoutine : public Routine
{
public:
  /// \brief Constructs the Calibration Routine
  /// \param calibrated_did The pointer to the DidHandler that holds calibrated value.
  /// \param threshold_did The pointer to the DidHandler that holds the threshold value.
  /// \param timer The pointer to the Timer.
  CalibrationRoutine(DidHandlerPtr calibrated_did, DidHandlerPtr threshold_did, TimerPtr timer);

  /// \copydoc Routine::start
  void start(const RoutineOptions & options) override;

  /// \copydoc Routine::stop
  void stop(const RoutineOptions & options) override;

  /// \copydoc Routine::result
  const RoutineStatus & result(const RoutineOptions & options) override;

private:
  /// \brief The DidHandler that holds the calibrated data.
  const DidHandlerPtr m_calibrated_did;
  /// \brief The DidHandler with a value used for calibration.
  const DidHandlerPtr m_threshold_did;
  /// \brief Timer used for starting the timed routine.
  const TimerPtr m_timer;
  /// \brief The optional timer id value. It holds value if the timer is armed.
  optional<uint32_t> m_timer_id{nullopt};
};

}  // namespace routine
}  // namespace uds_test_app
}  // namespace apex

#endif  // UDS_TEST_APP__ROUTINE__CALIBRATION_ROUTINE_HPP_
