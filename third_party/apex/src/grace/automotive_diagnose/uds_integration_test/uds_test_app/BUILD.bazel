load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

cc_library(
    name = "uds_test_app_lib",
    srcs = glob(
        [
            "src/**/*.cpp",
        ],
        exclude = [
            "src/app_main.cpp",
        ],
    ),
    hdrs = glob(["include/**"]),
    strip_include_prefix = "include",
    tags = ["exclude_sca"],
    deps = [
        "@apex//grace/automotive_diagnose/common",
        "@apex//grace/automotive_diagnose/uds_msgs",
        "@apex//grace/execution/executor2",
        "@apex//grace/monitoring/logging",
    ],
)

cc_binary(
    name = "uds_test_app_node",
    srcs = ["src/app_main.cpp"],
    tags = ["exclude_sca"],
    deps = [
        ":uds_test_app_lib",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
    ],
)

ros_pkg(
    name = "uds_test_app",
    description = "Test application to interact with the UDS",
    lib_executables = ["uds_test_app_node"],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "uds_test_app",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)
