// Copyright 2022 Apex.AI, Inc
// All rights reserved.

#include <string>

#include "apex_integration_test_node/apex_integration_test_node.hpp"
#include "doip_transport/config/config_parser.hpp"
#include "gtest/gtest.h"
#include "settings/construct.hpp"
#include "settings/from_yaml.hpp"
#include "settings/repository.hpp"

using DoIpNodeFixture = apex::tools::apex_integration_test_node::ApexIntegrationTestNode;

namespace yaml = apex::settings::yaml;
namespace repository = apex::settings::repository;
namespace construct = apex::settings::construct;

void yaml_from_string(const std::string & yaml, construct::dictionary & dict)
{
  yaml::from_string(yaml, dict, false);
  repository::set(dict);
}

TEST_F(DoIpNodeFixture, config_parser_default_correct)
{
  auto config =
    R"yaml(
diagnostic:
  doip:
    # 0x01: ISO/DIS 13400-2:2010
    # 0x02: ISO 13400-2:2012
    # 0x03: this document
    protocol_version: 0x01
    # Equals the <Protocol_Version>
    # XOR 0xFF (e.g. 0xFE for protocol
    # version 0x01).
    inverse_protocol_version: 0xFF

    gid: "GID123"
    vin: "VIN12345678901234"
    eid: "EID123"

    network_interface: "eth0"  # Not used for now.
    mac_as_eid: true  # Not used for now.
    max_payload_size: 1024

    tcp_server_address: "127.0.0.1"
    tcp_server_port: 13400
    max_connections: 2

    udp_connection_address: "127.0.0.1"
    udp_discovery_port: 13400

    uds_node: # In the future, it can be replaced with the array of UDS nodes.
      approved_client_addresses: [0x3344, 0x4455]
      can_ecu_addresses: [0x203,0x204]
      uds_server_address: 0x2233
      req_topic: "/uds/req"
      res_topic: "/uds/ind"

    tcp_initial_inactivity_timeout_ms: 2000 # 2s
    tcp_general_inactivity_timeout_ms: 300000 # 5min

    vehicle_announcement_interval_ms: 500
    vehicle_announcement_count: 3
)yaml";

  construct::dictionary dict;
  yaml_from_string(config, dict);

  try {
    const auto settings = apex::doip_transport::config::parse(apex::settings::repository::get());

    // DoIp settings
    EXPECT_EQ(settings.m_protocol_version, 0x01);
    EXPECT_EQ(settings.m_inverse_protocol_version, uint8_t(0xFF) ^ settings.m_protocol_version);
    EXPECT_EQ(settings.m_gid, "GID123");
    EXPECT_EQ(settings.m_vin, "VIN12345678901234");
    EXPECT_EQ(settings.m_eid, "EID123");
    EXPECT_TRUE(settings.m_mac_as_eid);
    EXPECT_EQ(settings.m_max_payload_size, 1024);
    EXPECT_EQ(settings.m_tcp_server_address, "127.0.0.1");
    EXPECT_EQ(settings.m_tcp_server_port, 13400);
    EXPECT_EQ(settings.m_max_connections, 2);
    EXPECT_EQ(settings.m_udp_connection_address, "127.0.0.1");
    EXPECT_EQ(settings.m_udp_discovery_port, 13400);
    EXPECT_EQ(settings.m_vehicle_announcement_count, 3);

    // UDS node settings
    EXPECT_EQ(settings.m_uds_node.m_approved_client_addresses,
              apex::static_vector<uint32_t>(2, {0x3344, 0x4455}));
    EXPECT_EQ(settings.m_uds_node.m_can_ecu_addresses,
              apex::static_vector<uint16_t>(2, {0x203, 0x204}));
    EXPECT_EQ(settings.m_uds_node.m_address, 0x2233);
    EXPECT_EQ(settings.m_uds_node.m_req_topic, "/uds/req");
    EXPECT_EQ(settings.m_uds_node.m_res_topic, "/uds/ind");
  } catch (const apex::runtime_error & err) {
    FAIL() << err.what();
  }
}

TEST_F(DoIpNodeFixture, config_parser_settings_validation)
{
  const size_t number_of_udp_ports = 1;
  const size_t number_of_approved_client_ports = 1;
  const size_t number_of_approved_server_ports = 1;
  apex::doip_transport::DoIpSettings settings{
    number_of_udp_ports, number_of_approved_server_ports, number_of_approved_client_ports};

  settings.m_gid = "123456";
  settings.m_vin = "VIN12345678901234";
  settings.m_max_payload_size = 1024;

  // EID should have 6 characters
  settings.m_eid = "123";
  EXPECT_THROW(settings.validate_constraints(), apex::runtime_error);
  settings.m_eid = "123456";
  EXPECT_NO_THROW(settings.validate_constraints());

  // GID should have 6 characters
  settings.m_gid = "123";
  EXPECT_THROW(settings.validate_constraints(), apex::runtime_error);
  settings.m_gid = "123456";
  EXPECT_NO_THROW(settings.validate_constraints());

  // VIN should have 17 characters
  settings.m_vin = "123";
  EXPECT_THROW(settings.validate_constraints(), apex::runtime_error);
  settings.m_vin = "VIN12345678901234";
  EXPECT_NO_THROW(settings.validate_constraints());

  // It requires at least 10 bytes to support processing diagnostic request
  settings.m_max_payload_size = apex::doip_transport::MIN_PAYLOAD_SIZE - 1;
  EXPECT_NO_THROW(settings.validate_constraints());
  EXPECT_EQ(settings.m_max_payload_size, apex::doip_transport::DEFAULT_MAX_PAYLOAD_SIZE);
  settings.m_max_payload_size = apex::doip_transport::MIN_PAYLOAD_SIZE;
  EXPECT_NO_THROW(settings.validate_constraints());
  EXPECT_EQ(settings.m_max_payload_size, apex::doip_transport::MIN_PAYLOAD_SIZE);

  // We need at least 1 UDP and TCP client sockets
  settings.m_max_connections = apex::doip_transport::MIN_NUMBER_OF_TCP_AND_UDP_SOCKETS - 1;
  EXPECT_NO_THROW(settings.validate_constraints());
  EXPECT_EQ(settings.m_max_connections, apex::doip_transport::DEFAULT_MAX_CONNECTIONS);
  settings.m_max_connections = apex::doip_transport::MIN_NUMBER_OF_TCP_AND_UDP_SOCKETS;
  EXPECT_NO_THROW(settings.validate_constraints());
  EXPECT_EQ(settings.m_max_connections, apex::doip_transport::MIN_NUMBER_OF_TCP_AND_UDP_SOCKETS);
}

TEST_F(DoIpNodeFixture, config_parser_fails_approved_client_addresses)
{
  auto config =
    R"yaml(
diagnostic:
  doip:
    # 0x01: ISO/DIS 13400-2:2010
    # 0x02: ISO 13400-2:2012
    # 0x03: this document
    protocol_version: 0x01
    # Equals the <Protocol_Version>
    # XOR 0xFF (e.g. 0xFE for protocol
    # version 0x01).
    inverse_protocol_version: 0xFF

    gid: "GID123"
    vin: "VIN12345678901234"
    eid: "EID123"

    network_interface: "eth0"  # Not used for now.
    mac_as_eid: true  # Not used for now.
    max_payload_size: 1024

    tcp_server_address: "127.0.0.1"
    tcp_server_port: 13400
    max_connections: 2

    udp_connection_address: "127.0.0.1"
    udp_discovery_port: 13400

    uds_node: # In the future, it can be replaced with the array of UDS nodes.
      # approved_client_addresses: [0x3344Z, 0x4455] ERROR HERE! Missing parameter
      can_ecu_addresses: [0x203,0x204]
      uds_server_address: 0x2233
      req_topic: "/uds/req"
      res_topic: "/uds/ind"

    tcp_initial_inactivity_timeout_ms: 2000 # 2s
    tcp_general_inactivity_timeout_ms: 300000 # 5min

    vehicle_announcement_interval_ms: 500
    vehicle_announcement_count: 3
)yaml";

  construct::dictionary dict;
  yaml_from_string(config, dict);

  EXPECT_THROW(apex::doip_transport::config::parse(apex::settings::repository::get()),
               apex::diagnostic_common::config_error);
}

TEST_F(DoIpNodeFixture, config_parser_does_not_fails_with_optional_approved_can_addresses)
{
  auto config =
    R"yaml(
diagnostic:
  doip:
    # 0x01: ISO/DIS 13400-2:2010
    # 0x02: ISO 13400-2:2012
    # 0x03: this document
    protocol_version: 0x01
    # Equals the <Protocol_Version>
    # XOR 0xFF (e.g. 0xFE for protocol
    # version 0x01).
    inverse_protocol_version: 0xFF

    gid: "GID123"
    vin: "VIN12345678901234"
    eid: "EID123"

    network_interface: "eth0"  # Not used for now.
    mac_as_eid: true  # Not used for now.
    max_payload_size: 1024

    tcp_server_address: "127.0.0.1"
    tcp_server_port: 13400
    max_connections: 2

    udp_connection_address: "127.0.0.1"
    udp_discovery_port: 13400

    uds_node: # In the future, it can be replaced with the array of UDS nodes.
      approved_client_addresses: [0x3344, 0x4455]
      # can_ecu_addresses: [0x203,0x204]
      uds_server_address: 0x2233
      req_topic: "/uds/req"
      res_topic: "/uds/ind"

    tcp_initial_inactivity_timeout_ms: 2000 # 2s
    tcp_general_inactivity_timeout_ms: 300000 # 5min

    vehicle_announcement_interval_ms: 500
    vehicle_announcement_count: 3
)yaml";

  construct::dictionary dict;
  yaml_from_string(config, dict);

  apex::doip_transport::config::parse(apex::settings::repository::get());
}

TEST_F(DoIpNodeFixture, config_parser_fails_max_connections)
{
  auto config =
    R"yaml(
diagnostic:
  doip:
    # 0x01: ISO/DIS 13400-2:2010
    # 0x02: ISO 13400-2:2012
    # 0x03: this document
    protocol_version: 0x01
    # Equals the <Protocol_Version>
    # XOR 0xFF (e.g. 0xFE for protocol
    # version 0x01).
    inverse_protocol_version: 0xFF

    gid: "GID123"
    vin: "VIN12345678901234"
    eid: "EID123"

    network_interface: "eth0"  # Not used for now.
    mac_as_eid: true  # Not used for now.
    max_payload_size: 1024

    tcp_server_address: "127.0.0.1"
    tcp_server_port: 13400
    max_connections: -1 # ERROR HERE! Incorrect value.

    udp_connection_address: "127.0.0.1"
    udp_discovery_port: 13400

    uds_node: # In the future, it can be replaced with the array of UDS nodes.
      approved_client_addresses: [0x3344Z, 0x4455]
      can_ecu_addresses: [0x203,0x204]
      uds_server_address: 0x2233
      req_topic: "/uds/req"
      res_topic: "/uds/ind"

    tcp_initial_inactivity_timeout_ms: 2000 # 2s
    tcp_general_inactivity_timeout_ms: 300000 # 5min

    vehicle_announcement_interval_ms: 500
    vehicle_announcement_count: 3
)yaml";

  construct::dictionary dict;
  yaml_from_string(config, dict);

  EXPECT_THROW(apex::doip_transport::config::parse(apex::settings::repository::get()),
               apex::diagnostic_common::config_error);
}

TEST_F(DoIpNodeFixture, config_parser_protocol_version)
{
  auto config =
    R"yaml(
diagnostic:
  doip:
    # 0x01: ISO/DIS 13400-2:2010
    # 0x02: ISO 13400-2:2012
    # 0x03: this document
    protocol_version: -1 # ERROR HERE! Incorrect value.
    # Equals the <Protocol_Version>
    # XOR 0xFF (e.g. 0xFE for protocol
    # version 0x01).
    inverse_protocol_version: 0xFF

    gid: "GID123"
    vin: "VIN12345678901234"
    eid: "EID123"

    network_interface: "eth0"  # Not used for now.
    mac_as_eid: true  # Not used for now.
    max_payload_size: 1024

    tcp_server_address: "127.0.0.1"
    tcp_server_port: 13400
    max_connections: 2

    udp_connection_address: "127.0.0.1"
    udp_discovery_port: 13400

    uds_node: # In the future, it can be replaced with the array of UDS nodes.
      approved_client_addresses: [0x3344Z, 0x4455]
      can_ecu_addresses: [0x203,0x204]
      uds_server_address: 0x2233
      req_topic: "/uds/req"
      res_topic: "/uds/ind"

    tcp_initial_inactivity_timeout_ms: 2000 # 2s
    tcp_general_inactivity_timeout_ms: 300000 # 5min

    vehicle_announcement_interval_ms: 500
    vehicle_announcement_count: 3
)yaml";

  construct::dictionary dict;
  yaml_from_string(config, dict);

  EXPECT_THROW(apex::doip_transport::config::parse(apex::settings::repository::get()),
               apex::diagnostic_common::config_error);
}
