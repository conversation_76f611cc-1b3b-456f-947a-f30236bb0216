// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include <memory>
#include <utility>

#include "apex_integration_test_node/apex_integration_test_node.hpp"
#include "doip_transport/doip_msg_processor.hpp"
#include "doip_transport/doip_transport.hpp"
#include "gtest/gtest.h"

#include "uds_msgs/msg/do_ip_header.hpp"

using DoIpMsgParserFixture = apex::tools::apex_integration_test_node::ApexIntegrationTestNode;

namespace
{
namespace doip = apex::doip_transport;
using UdsTransportMessage = uds_msgs::msg::UdsTransportMessage;
using uds_msgs::msg::DoIpHeader;
}  // namespace

const char * IP4_PORT_ADDRESS = "127.0.0.1";
const char * EID = "EID123";
const char * INCORRECT_EID = "EID456";
const char * VIN = "VIN1234567890123";
const char * INCORRECT_VIN = "VIN4564567890123";
constexpr uint16_t TESTER_ADDRESS = 0x11;
constexpr uint16_t TESTER_ADDRESS2 = 0x12;
constexpr uint16_t TESTER_ADDRESS3 = 0x13;
constexpr uint16_t UDS_NODE_ADDRESS = 0x22;
constexpr uint16_t CAN_ECU_ADDRESS = 0x55;


TEST_F(DoIpMsgParserFixture, process_udp_msg_test)
{
  rclcpp::Node test_node{"test_node"};
  const size_t max_num_of_connections = 1;
  const size_t max_num_of_client_ports = 2U;
  const size_t max_num_of_ecu_can_addresses = 1U;
  doip::DoIpSettings settings(
    max_num_of_connections, max_num_of_client_ports, max_num_of_ecu_can_addresses);
  settings.m_eid = EID;
  settings.m_vin = VIN;

  doip::MsgProcessor msg_processor(
    std::make_shared<apex::logging::Logger<>>(&test_node, "TestNode"), settings);

  doip::UdpRequest request;
  request.m_header.payload_type =
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::VEHICLE_IDENTIFICATION_REQUEST_EMPTY));
  request.m_header.payload_length = 0;  // empty request
  memset(static_cast<void *>(&request.m_payload), 0, sizeof(request.m_payload));
  auto maybe_response_to_send = msg_processor.process_udp_msg(request);
  ASSERT_TRUE(maybe_response_to_send);
  EXPECT_EQ(
    maybe_response_to_send.value().m_header.payload_type,
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::VEHICLE_IDENTIFICATION_RESPONSE)));

  // correct EID
  request.m_header.payload_type = apex_tcp_htons(
    static_cast<uint16_t>(doip::PayloadType::VEHICLE_IDENTIFICATION_REQUEST_WITH_EID));
  request.m_header.payload_length = apex_tcp_htonl(doip::EID_SIZE);
  memset(static_cast<void *>(&request.m_payload), 0, sizeof(request.m_payload));
  memcpy(&request.m_payload[0], EID, doip::EID_SIZE);
  maybe_response_to_send = msg_processor.process_udp_msg(request);
  ASSERT_TRUE(maybe_response_to_send);
  EXPECT_EQ(
    maybe_response_to_send.value().m_header.payload_type,
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::VEHICLE_IDENTIFICATION_RESPONSE)));
  // incorrect EID
  memcpy(&request.m_payload[0], INCORRECT_EID, doip::EID_SIZE);
  maybe_response_to_send = msg_processor.process_udp_msg(request);
  ASSERT_TRUE(maybe_response_to_send);
  EXPECT_EQ(maybe_response_to_send.value().m_header.payload_type,
            apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::GENERIC_DOIP_NEGATIVE_ACK)));

  // correct VIN
  request.m_header.payload_type = apex_tcp_htons(
    static_cast<uint16_t>(doip::PayloadType::VEHICLE_IDENTIFICATION_REQUEST_WITH_VIN));
  request.m_header.payload_length = apex_tcp_htonl(doip::VIN_SIZE);
  memset(static_cast<void *>(&request.m_payload), 0, sizeof(request.m_payload));
  memcpy(&request.m_payload[0], VIN, doip::VIN_SIZE);
  maybe_response_to_send = msg_processor.process_udp_msg(request);
  ASSERT_TRUE(maybe_response_to_send);
  EXPECT_EQ(
    maybe_response_to_send.value().m_header.payload_type,
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::VEHICLE_IDENTIFICATION_RESPONSE)));
  // incorrect VIN
  memcpy(&request.m_payload[0], INCORRECT_VIN, doip::VIN_SIZE);
  maybe_response_to_send = msg_processor.process_udp_msg(request);
  ASSERT_TRUE(maybe_response_to_send);
  EXPECT_EQ(maybe_response_to_send.value().m_header.payload_type,
            apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::GENERIC_DOIP_NEGATIVE_ACK)));

  // not supported DOIP_ENTITY_STATUS_REQUEST, GENERIC_DOIP_NEGATIVE_ACK should be sent
  request.m_header.payload_type =
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::DOIP_ENTITY_STATUS_REQUEST));
  request.m_header.payload_length = 0;
  memset(static_cast<void *>(&request.m_payload), 0, sizeof(request.m_payload));
  maybe_response_to_send = msg_processor.process_udp_msg(request);
  ASSERT_TRUE(maybe_response_to_send);
  EXPECT_EQ(maybe_response_to_send.value().m_header.payload_type,
            apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::GENERIC_DOIP_NEGATIVE_ACK)));

  // not supported DIAGNOSTIC_POWER_MODE_INFORMATION_REQUEST
  request.m_header.payload_type = apex_tcp_htons(
    static_cast<uint16_t>(doip::PayloadType::DIAGNOSTIC_POWER_MODE_INFORMATION_REQUEST));
  request.m_header.payload_length = 0;
  memset(static_cast<void *>(&request.m_payload), 0, sizeof(request.m_payload));
  maybe_response_to_send = msg_processor.process_udp_msg(request);
  ASSERT_TRUE(maybe_response_to_send);
  EXPECT_EQ(maybe_response_to_send.value().m_header.payload_type,
            apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::GENERIC_DOIP_NEGATIVE_ACK)));

  // not known payload type, response shouldn't be sent
  request.m_header.payload_type =
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::DOIP_ENTITY_STATUS_REQUEST) + 1);
  request.m_header.payload_length = 0;
  memset(static_cast<void *>(&request.m_payload), 0, sizeof(request.m_payload));
  maybe_response_to_send = msg_processor.process_udp_msg(request);
  ASSERT_FALSE(maybe_response_to_send);
}

struct MockTcpConnection
{
  MockTcpConnection() {}
  MockTcpConnection(const char * const, const uint16_t) {}
  explicit MockTcpConnection(const apex_tcp_t &) {}
  apex_ret_t send(const void * const, const size_t)
  {
    return {};
  }
  apex_ret_t read_ms(char *, const size_t, const int64_t)
  {
    return {};
  }
  apex_socket_t socket() const
  {
    return {};
  }
};

TEST_F(DoIpMsgParserFixture, process_routing_activation_request_test)
{
  rclcpp::Node test_node{"test_node"};
  const size_t max_num_of_connections = 1;
  const size_t max_num_of_client_ports = 2U;
  const size_t max_num_of_ecu_can_addresses = 1U;
  const size_t response_code_idx = 4;
  doip::DoIpSettings settings(
    max_num_of_connections, max_num_of_client_ports, max_num_of_ecu_can_addresses);
  settings.m_uds_node.m_approved_client_addresses.push_back(TESTER_ADDRESS3);

  doip::MsgProcessor msg_processor(
    std::make_shared<apex::logging::Logger<>>(&test_node, "TestNode"), settings);

  doip::RoutingActivationRequest activation_request;
  activation_request.m_header.payload_type =
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::ROUTING_ACTIVATION_REQUEST));
  activation_request.m_header.payload_length = apex_tcp_htonl(activation_request.m_payload.size());
  uint16_t network_tester_address = apex_tcp_htons(TESTER_ADDRESS);
  memcpy(&activation_request.m_payload[0], &network_tester_address, sizeof(network_tester_address));
  const size_t index_of_routing_activation_type = 2;
  activation_request.m_payload[index_of_routing_activation_type] =
    static_cast<uint8_t>(doip::RoutingActivationRequestActivationTypes::DEFAULT);

  apex::static_vector<doip::TesterConnection<MockTcpConnection>> clients_connections(1);
  clients_connections.push_back({false, TESTER_ADDRESS2, {}});

  // lack of free sockets
  auto response_to_send = msg_processor.process_routing_activation_request(
    activation_request, clients_connections, max_num_of_connections);
  EXPECT_EQ(response_to_send.m_client_tester_address, TESTER_ADDRESS);
  EXPECT_EQ(response_to_send.m_header.payload_type,
            apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::ROUTING_ACTIVATION_RESPONSE)));
  EXPECT_EQ(response_to_send.m_payload[response_code_idx],
            static_cast<uint8_t>(doip::RoutingActivationResponses::DENIED_LACK_OF_FREE_SOCKETS));

  // SA already registered
  network_tester_address = apex_tcp_htons(TESTER_ADDRESS2);
  memcpy(&activation_request.m_payload[0], &network_tester_address, sizeof(network_tester_address));
  response_to_send =
    msg_processor.process_routing_activation_request(activation_request, clients_connections, 0);
  EXPECT_EQ(response_to_send.m_client_tester_address, TESTER_ADDRESS2);
  EXPECT_EQ(response_to_send.m_header.payload_type,
            apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::ROUTING_ACTIVATION_RESPONSE)));
  EXPECT_EQ(response_to_send.m_payload[response_code_idx],
            static_cast<uint8_t>(
              doip::RoutingActivationResponses::DENIED_SA_ALREADY_REGISTERED_ON_OTHER_SOCKET));

  // unsupported routing activation type
  network_tester_address = apex_tcp_htons(TESTER_ADDRESS);
  memcpy(&activation_request.m_payload[0], &network_tester_address, sizeof(network_tester_address));
  activation_request.m_payload[index_of_routing_activation_type] =
    static_cast<uint8_t>(doip::RoutingActivationRequestActivationTypes::REQUIRED_BY_ISO) + 1;
  response_to_send =
    msg_processor.process_routing_activation_request(activation_request, clients_connections, 0);
  EXPECT_EQ(response_to_send.m_client_tester_address, TESTER_ADDRESS);
  EXPECT_EQ(response_to_send.m_header.payload_type,
            apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::ROUTING_ACTIVATION_RESPONSE)));
  EXPECT_EQ(
    response_to_send.m_payload[response_code_idx],
    static_cast<uint8_t>(doip::RoutingActivationResponses::DENIED_UNSUPPORTED_ACTIVATION_TYPE));

  // unknown source address
  activation_request.m_payload[index_of_routing_activation_type] =
    static_cast<uint8_t>(doip::RoutingActivationRequestActivationTypes::REQUIRED_BY_ISO);
  response_to_send =
    msg_processor.process_routing_activation_request(activation_request, clients_connections, 0);
  EXPECT_EQ(response_to_send.m_client_tester_address, TESTER_ADDRESS);
  EXPECT_EQ(response_to_send.m_header.payload_type,
            apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::ROUTING_ACTIVATION_RESPONSE)));
  EXPECT_EQ(response_to_send.m_payload[response_code_idx],
            static_cast<uint8_t>(doip::RoutingActivationResponses::DENIED_UNKNOWN_SOURCE_ADDRESS));

  // positive_response
  network_tester_address = apex_tcp_htons(TESTER_ADDRESS3);
  memcpy(&activation_request.m_payload[0], &network_tester_address, sizeof(network_tester_address));
  response_to_send =
    msg_processor.process_routing_activation_request(activation_request, clients_connections, 0);
  EXPECT_EQ(response_to_send.m_client_tester_address, TESTER_ADDRESS3);
  EXPECT_EQ(response_to_send.m_header.payload_type,
            apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::ROUTING_ACTIVATION_RESPONSE)));
  EXPECT_EQ(response_to_send.m_payload[response_code_idx],
            static_cast<uint8_t>(doip::RoutingActivationResponses::SUCCESSFULLY_ACTIVATED));
}

TEST_F(DoIpMsgParserFixture, process_diagnostic_request_test)
{
  rclcpp::Node test_node{"test_node"};

  const size_t ACK_IDX_IN_PAYLOAD = 4;
  const size_t max_num_of_connections = 1;
  const size_t max_num_of_client_ports = 2U;
  const size_t max_num_of_ecu_can_addresses = 1U;
  doip::DoIpSettings settings(
    max_num_of_connections, max_num_of_client_ports, max_num_of_ecu_can_addresses);
  settings.m_uds_node.m_can_ecu_addresses.push_back(CAN_ECU_ADDRESS);
  settings.m_uds_node.m_address = UDS_NODE_ADDRESS;
  const uint32_t payload_length = uint32_t{4 + 5};
  settings.m_max_payload_size = payload_length;

  doip::MsgProcessor msg_processor(
    std::make_shared<apex::logging::Logger<>>(&test_node, "TestNode"), settings);

  std::array<char, payload_length> msg_buffer;
  // invalid source address
  const uint16_t wrong_doip_address = 0x1122;
  uint16_t network_doip_address = apex_tcp_htons(wrong_doip_address);
  const uint16_t network_tester_address = apex_tcp_htons(TESTER_ADDRESS);
  memcpy(
    static_cast<void *>(&msg_buffer[0]), &network_tester_address, sizeof(network_tester_address));
  memcpy(static_cast<void *>(&msg_buffer[2]), &network_doip_address, sizeof(network_doip_address));
  const uint8_t payload_item_value = '1';
  memset(static_cast<void *>(&msg_buffer[4]), payload_item_value, 5);

  apex::static_vector<char> buffer(msg_buffer.size(), msg_buffer.begin(), msg_buffer.end());
  auto response_to_send = msg_processor.process_diagnostic_request(false, buffer, payload_length);
  EXPECT_FALSE(response_to_send.first);
  EXPECT_EQ(
    response_to_send.second.m_header.payload_type,
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::DIAGNOSTIC_MESSAGE_NEGATIVE_ACK)));
  EXPECT_EQ(response_to_send.second.m_payload[ACK_IDX_IN_PAYLOAD],
            static_cast<uint16_t>(doip::DiagnosticMsgAckCodes::INVALID_SOURCE_ADDRESS));

  // unknown target address
  response_to_send = msg_processor.process_diagnostic_request(true, buffer, payload_length);
  EXPECT_FALSE(response_to_send.first);
  EXPECT_EQ(
    response_to_send.second.m_header.payload_type,
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::DIAGNOSTIC_MESSAGE_NEGATIVE_ACK)));
  EXPECT_EQ(response_to_send.second.m_payload[ACK_IDX_IN_PAYLOAD],
            static_cast<uint16_t>(doip::DiagnosticMsgAckCodes::UNKNOWN_TARGET_ADDRESS));

  // too big request payload
  network_doip_address = apex_tcp_htons(CAN_ECU_ADDRESS);
  memcpy(static_cast<void *>(&msg_buffer[2]), &network_doip_address, sizeof(network_doip_address));
  apex::static_vector<char> buffer2(msg_buffer.size(), msg_buffer.begin(), msg_buffer.end());
  response_to_send = msg_processor.process_diagnostic_request(true, buffer2, payload_length + 1);
  EXPECT_FALSE(response_to_send.first);
  EXPECT_EQ(
    response_to_send.second.m_header.payload_type,
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::DIAGNOSTIC_MESSAGE_NEGATIVE_ACK)));
  EXPECT_EQ(response_to_send.second.m_payload[ACK_IDX_IN_PAYLOAD],
            static_cast<uint16_t>(doip::DiagnosticMsgAckCodes::OUT_OF_MEMORY));

  // positive response
  response_to_send = msg_processor.process_diagnostic_request(true, buffer2, payload_length);
  ASSERT_TRUE(response_to_send.first);
  EXPECT_EQ(response_to_send.first->tester_address.address, TESTER_ADDRESS);
  EXPECT_EQ(response_to_send.first->ecu_address.address, CAN_ECU_ADDRESS);
  EXPECT_EQ(
    response_to_send.second.m_header.payload_type,
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::DIAGNOSTIC_MESSAGE_POSITIVE_ACK)));
  EXPECT_EQ(response_to_send.second.m_payload[ACK_IDX_IN_PAYLOAD],
            static_cast<uint16_t>(doip::DiagnosticMsgAckCodes::ROUTING_CONFIRMATION_ACK));
}

TEST_F(DoIpMsgParserFixture, process_diagnostic_request_unknown_address)
{
  rclcpp::Node test_node{"test_node"};

  const size_t ACK_IDX_IN_PAYLOAD = 4;
  const size_t max_num_of_connections = 1;
  const size_t max_num_of_client_ports = 2U;
  const size_t max_num_of_ecu_can_addresses = 1U;
  doip::DoIpSettings settings(
    max_num_of_connections, max_num_of_client_ports, max_num_of_ecu_can_addresses);
  constexpr uint16_t WRONG_ADDRESS = UDS_NODE_ADDRESS + 1;
  settings.m_uds_node.m_can_ecu_addresses.push_back(WRONG_ADDRESS);
  settings.m_uds_node.m_address = UDS_NODE_ADDRESS;
  const uint32_t payload_length = uint32_t{4 + 5};
  settings.m_max_payload_size = payload_length;

  doip::MsgProcessor msg_processor(
    std::make_shared<apex::logging::Logger<>>(&test_node, "TestNode"), settings);

  std::array<char, payload_length> msg_buffer;
  // invalid source address
  const uint16_t wrong_doip_address = 0x1122;
  uint16_t network_doip_address = apex_tcp_htons(wrong_doip_address);
  const uint16_t network_tester_address = apex_tcp_htons(TESTER_ADDRESS);
  memcpy(
    static_cast<void *>(&msg_buffer[0]), &network_tester_address, sizeof(network_tester_address));
  memcpy(static_cast<void *>(&msg_buffer[2]), &network_doip_address, sizeof(network_doip_address));
  const uint8_t payload_item_value = '1';
  memset(static_cast<void *>(&msg_buffer[4]), payload_item_value, 5);

  apex::static_vector<char> buffer(msg_buffer.size(), msg_buffer.begin(), msg_buffer.end());
  // unknown target address
  auto response_to_send = msg_processor.process_diagnostic_request(true, buffer, payload_length);
  EXPECT_FALSE(response_to_send.first);
  EXPECT_EQ(
    response_to_send.second.m_header.payload_type,
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::DIAGNOSTIC_MESSAGE_NEGATIVE_ACK)));
  EXPECT_EQ(response_to_send.second.m_payload[ACK_IDX_IN_PAYLOAD],
            static_cast<uint16_t>(doip::DiagnosticMsgAckCodes::UNKNOWN_TARGET_ADDRESS));

  // too big request payload but fail for address
  memcpy(static_cast<void *>(&msg_buffer[2]), &network_doip_address, sizeof(network_doip_address));
  apex::static_vector<char> buffer2(msg_buffer.size(), msg_buffer.begin(), msg_buffer.end());
  response_to_send = msg_processor.process_diagnostic_request(true, buffer2, payload_length + 1);
  EXPECT_FALSE(response_to_send.first);
  EXPECT_EQ(
    response_to_send.second.m_header.payload_type,
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::DIAGNOSTIC_MESSAGE_NEGATIVE_ACK)));
  EXPECT_EQ(response_to_send.second.m_payload[ACK_IDX_IN_PAYLOAD],
            static_cast<uint16_t>(doip::DiagnosticMsgAckCodes::UNKNOWN_TARGET_ADDRESS));
}

TEST_F(DoIpMsgParserFixture, process_uds_connector_response_test)
{
  rclcpp::Node test_node{"test_node"};

  const size_t max_num_of_connections = 1;
  const size_t max_num_of_client_ports = 2U;
  const size_t max_num_of_ecu_can_addresses = 1U;
  doip::DoIpSettings settings(
    max_num_of_connections, max_num_of_client_ports, max_num_of_ecu_can_addresses);
  const uint32_t payload_length = uint32_t{4 + 5};
  settings.m_max_payload_size = payload_length;
  settings.m_protocol_version = 0x03;
  settings.m_inverse_protocol_version = uint8_t(0xFF) ^ settings.m_protocol_version;
  settings.m_uds_node.m_can_ecu_addresses.push_back(CAN_ECU_ADDRESS);
  settings.m_uds_node.m_address = UDS_NODE_ADDRESS;

  doip::MsgProcessor msg_processor(
    std::make_shared<apex::logging::Logger<>>(&test_node, "TestNode"), settings);

  UdsTransportMessage uds_msg;
  uds_msg.tester_address.address = TESTER_ADDRESS;
  uds_msg.ecu_address.address = CAN_ECU_ADDRESS;
  for (size_t i = 0; i < 5; ++i) {
    uds_msg.payload.push_back(i);
  }

  const size_t expected_response_size = sizeof(DoIpHeader) + sizeof(TESTER_ADDRESS2) +
                                        sizeof(UDS_NODE_ADDRESS) + uds_msg.payload.size();
  apex::static_vector<char> response_buffer(expected_response_size);

  size_t response_size = msg_processor.process_uds_connector_response(uds_msg, response_buffer);
  EXPECT_EQ(response_size, expected_response_size);

  uds_msgs::msg::DoIpHeader received_diag_msg_header;
  memcpy(&received_diag_msg_header, &response_buffer.data()[0], sizeof(received_diag_msg_header));
  EXPECT_EQ(received_diag_msg_header.payload_type,
            apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::DIAGNOSTIC_MESSAGE)));
  EXPECT_EQ(received_diag_msg_header.protocol_version, settings.m_protocol_version);
  EXPECT_EQ(received_diag_msg_header.inverse_protocol_version, settings.m_inverse_protocol_version);
  uint16_t doip_address = 0;
  memcpy(&doip_address, &response_buffer.data()[8], sizeof(doip_address));
  EXPECT_EQ(uds_msg.ecu_address.address, apex_tcp_htons(doip_address));
  EXPECT_EQ(CAN_ECU_ADDRESS, apex_tcp_htons(doip_address));
  uint16_t tester_address = 0;
  memcpy(&tester_address, &response_buffer.data()[10], sizeof(tester_address));
  EXPECT_EQ(TESTER_ADDRESS, apex_tcp_htons(tester_address));
}
