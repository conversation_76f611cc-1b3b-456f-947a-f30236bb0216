// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include <algorithm>
#include <memory>
#include <utility>

#include "apex_integration_test_node/apex_integration_test_node.hpp"
#include "doip_transport/doip_transport.hpp"
#include "gtest/gtest.h"
#include "mocks/pooling_subscription_mock.hpp"
#include "mocks/tcp_connection_mock.hpp"
#include "mocks/tcp_server_mock.hpp"
#include "mocks/timer_mock.hpp"
#include "mocks/udp_void_receiver_mock.hpp"
#include "mocks/udp_void_sender_mock.hpp"

#include "uds_msgs/msg/do_ip_header.hpp"


using DoIpDriverFixture = apex::tools::apex_integration_test_node::ApexIntegrationTestNode;

namespace
{
namespace doip = apex::doip_transport;
using UdsTransportMessage = uds_msgs::msg::UdsTransportMessage;
using uds_msgs::msg::DoIpHeader;

const char * IP4_PORT_ADDRESS = "127.0.0.1";
constexpr uint16_t DISCOVERY_PORT = 13400;
constexpr uint16_t TCP_SERVER_PORT1 = 13400;
}  // namespace

TEST_F(DoIpDriverFixture, udp_handle_received_msg_test)
{
  auto doip_timer = std::make_shared<MockTimerBase>();
  const size_t max_num_of_connections = 2U;
  const size_t max_num_of_client_ports = 2U;
  const size_t max_num_of_server_ports = 1U;
  doip::DoIpSettings settings(
    max_num_of_connections, max_num_of_client_ports, max_num_of_server_ports);

  settings.m_tcp_server_address = IP4_PORT_ADDRESS;
  settings.m_tcp_server_port = TCP_SERVER_PORT1;
  rclcpp::Node test_node{"test_node"};
  doip::DoIpDriver<UdpVoidSenderMock, UdpVoidReceiverMock> doip(
    std::make_shared<apex::logging::Logger<>>(&test_node, "TestNode"),
    std::move(settings),
    doip_timer,
    test_node.create_polling_subscription<doip::UdsTransportMessage>("reqTopic",
                                                                     rclcpp::DefaultQoS()),
    test_node.create_publisher<doip::UdsTransportMessage>("resTopic", rclcpp::DefaultQoS()));

  doip::DiagnosticPort<UdpVoidReceiverMock> diag_port(IP4_PORT_ADDRESS, DISCOVERY_PORT);
  doip::UdpRequest request;
  request.m_header.payload_type =
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::VEHICLE_IDENTIFICATION_REQUEST_EMPTY));
  request.m_header.payload_length = 0;  // empty request
  memset(static_cast<void *>(&request.m_payload), 0, sizeof(request.m_payload));
  doip.handle_received_msg(diag_port, request);
  EXPECT_EQ(1, UdpVoidSenderMock::m_send_response_counter);

  // not known payload type, response shouldn't be sent
  request.m_header.payload_type =
    apex_tcp_htons(static_cast<uint16_t>(doip::PayloadType::DOIP_ENTITY_STATUS_REQUEST) + 1);
  request.m_header.payload_length = 0;
  memset(static_cast<void *>(&request.m_payload), 0, sizeof(request.m_payload));
  doip.handle_received_msg(diag_port, request);
  EXPECT_EQ(1, UdpVoidSenderMock::m_send_response_counter);
}

TEST_F(DoIpDriverFixture, try_accept_tcp_connection_test)
{
  auto doip_timer = std::make_shared<MockTimerBase>();
  const size_t max_num_of_connections = 1U;
  const size_t max_num_of_client_ports = 1U;
  const size_t max_num_of_server_ports = 1U;
  doip::DoIpSettings settings(
    max_num_of_connections, max_num_of_client_ports, max_num_of_server_ports);

  settings.m_tcp_server_address = IP4_PORT_ADDRESS;
  settings.m_tcp_server_port = TCP_SERVER_PORT1;
  settings.m_tcp_initial_inactivity_timeout_ms = 10;
  rclcpp::Node test_node{"test_node"};
  doip::DoIpDriver<UdpVoidSenderMock,
                   UdpVoidReceiverMock,
                   TcpConnectionMock,
                   TcpServerMock<TcpConnectionMock>>
    doip(std::make_shared<apex::logging::Logger<>>(&test_node, "TestNode"),
         std::move(settings),
         doip_timer,
         test_node.create_polling_subscription<doip::UdsTransportMessage>("reqTopic",
                                                                          rclcpp::DefaultQoS()),
         test_node.create_publisher<doip::UdsTransportMessage>("resTopic", rclcpp::DefaultQoS()));

  doip.try_accept_tcp_connection();
  EXPECT_EQ(TcpServerMock<TcpConnectionMock>::number_of_accept_calls, 1);
  EXPECT_EQ(TcpConnectionMock::number_of_socket_calls, 1);

  doip.try_accept_tcp_connection();
  EXPECT_EQ(TcpServerMock<TcpConnectionMock>::number_of_accept_calls, 2);
  EXPECT_EQ(TcpConnectionMock::number_of_socket_calls, 2);

  // Maximum number of client sockets was reached, new client rejected
  doip.try_accept_tcp_connection();
  EXPECT_EQ(TcpServerMock<TcpConnectionMock>::number_of_accept_calls, 3);
  EXPECT_EQ(TcpConnectionMock::number_of_socket_calls, 2);
}

TEST_F(DoIpDriverFixture, handle_clients_tcp_requests_test)
{
  TcpServerMock<TcpConnectionMock>::number_of_accept_calls = 0;
  TcpConnectionMock::number_of_socket_calls = 0;
  const size_t max_num_of_connections = 1U;
  const size_t max_num_of_client_ports = 1U;
  const size_t max_num_of_server_ports = 1U;
  doip::DoIpSettings settings(
    max_num_of_connections, max_num_of_client_ports, max_num_of_server_ports);


  settings.m_max_payload_size = 1024;
  settings.m_tcp_server_address = IP4_PORT_ADDRESS;
  settings.m_tcp_server_port = TCP_SERVER_PORT1;
  settings.m_tcp_initial_inactivity_timeout_ms = 10;
  settings.m_tcp_general_inactivity_timeout_ms = 10;
  rclcpp::Node test_node{"test_node"};
  auto doip_timer = std::make_shared<MockTimerBase>();
  doip::DoIpDriver<UdpVoidSenderMock,
                   UdpVoidReceiverMock,
                   TcpConnectionMock,
                   TcpServerMock<TcpConnectionMock>>
    doip(std::make_shared<apex::logging::Logger<>>(&test_node, "TestNode"),
         std::move(settings),
         doip_timer,
         test_node.create_polling_subscription<doip::UdsTransportMessage>("reqTopic",
                                                                          rclcpp::DefaultQoS()),
         test_node.create_publisher<doip::UdsTransportMessage>("resTopic", rclcpp::DefaultQoS()));

  doip.try_accept_tcp_connection();
  EXPECT_EQ(TcpServerMock<TcpConnectionMock>::number_of_accept_calls, 1);
  EXPECT_EQ(TcpConnectionMock::number_of_socket_calls, 1);

  auto triggering_fds = doip.get_triggering_file_descriptors();
  for (auto & fd : triggering_fds) {
    fd->revents = POLLIN;
  }

  // routing activation
  EXPECT_EQ(TcpConnectionMock::number_of_tcp_reads, 0);
  EXPECT_EQ(TcpConnectionMock::number_of_tcp_sends, 0);
  doip.handle_clients_tcp_requests();
  // header read, payload read
  EXPECT_EQ(TcpConnectionMock::number_of_tcp_reads, 2);
  // response sent
  EXPECT_EQ(TcpConnectionMock::number_of_tcp_sends, 1);

  TcpConnectionMock::number_of_tcp_reads = 0;
  TcpConnectionMock::number_of_tcp_sends = 0;

  // diagnostic message
  TcpConnectionMock::request_type = doip::PayloadType::DIAGNOSTIC_MESSAGE;
  doip.handle_clients_tcp_requests();
  // header read, payload read
  EXPECT_EQ(TcpConnectionMock::number_of_tcp_reads, 2);
  // response sent
  EXPECT_EQ(TcpConnectionMock::number_of_tcp_sends, 1);

  auto is_invalid_fd = [](const auto & fd) { return fd->fd == doip::INVALID_FD; };
  const auto number_of_inactive_tcp_fd_before_remove =
    std::count_if(triggering_fds.begin(), triggering_fds.end(), is_invalid_fd);

  // timeout cb should trigger removing tcp connection instance
  doip_timer->m_cb(1);

  const auto number_of_inactive_tcp_fd_after_remove =
    std::count_if(triggering_fds.begin(), triggering_fds.end(), is_invalid_fd);
  EXPECT_EQ(number_of_inactive_tcp_fd_before_remove, number_of_inactive_tcp_fd_after_remove - 1);
}

TEST_F(DoIpDriverFixture, handle_udp_requests_test)
{
  auto doip_timer = std::make_shared<MockTimerBase>();
  const size_t max_num_of_connections = 1U;
  const size_t max_num_of_client_ports = 1U;
  const size_t max_num_of_server_ports = 1U;
  doip::DoIpSettings settings(
    max_num_of_connections, max_num_of_client_ports, max_num_of_server_ports);

  settings.m_udp_discovery_port = 13400;
  settings.m_udp_connection_address = "127.0.0.1";
  settings.m_tcp_initial_inactivity_timeout_ms = 10;
  settings.m_tcp_general_inactivity_timeout_ms = 10;
  settings.m_vehicle_announcement_count = 3;
  rclcpp::Node test_node{"test_node"};
  doip::DoIpDriver<UdpVoidSenderMock,
                   UdpVoidReceiverMock,
                   TcpConnectionMock,
                   TcpServerMock<TcpConnectionMock>>
    doip(std::make_shared<apex::logging::Logger<>>(&test_node, "TestNode"),
         std::move(settings),
         doip_timer,
         test_node.create_polling_subscription<doip::UdsTransportMessage>("reqTopic",
                                                                          rclcpp::DefaultQoS()),
         test_node.create_publisher<doip::UdsTransportMessage>("resTopic", rclcpp::DefaultQoS()));
  doip.send_vehicle_announcement();

  auto triggering_fds = doip.get_triggering_file_descriptors();
  for (auto & fd : triggering_fds) {
    fd->revents = POLLIN;
  }

  // simulate 3 vehicle announcement messages
  for (uint8_t i = 0; i < settings.m_vehicle_announcement_count; ++i) {
    doip_timer->m_cb(1);
  }

  // Proper msg is returned by mock so response should be sent.
  UdpVoidSenderMock::m_send_response_counter = 0;
  doip.handle_udp_requests();
  EXPECT_EQ(1, UdpVoidReceiverMock::m_receive_response_counter);
  EXPECT_EQ(1, UdpVoidSenderMock::m_send_response_counter);
}

TEST_F(DoIpDriverFixture, handle_uds_connector_response_test)
{
  auto doip_timer = std::make_shared<MockTimerBase>();
  const size_t max_num_of_connections = 1U;
  const size_t max_num_of_client_ports = 1U;
  const size_t max_num_of_server_ports = 1U;
  doip::DoIpSettings settings(
    max_num_of_connections, max_num_of_client_ports, max_num_of_server_ports);

  settings.m_max_payload_size = 1024;
  settings.m_tcp_server_address = IP4_PORT_ADDRESS;
  settings.m_tcp_server_port = TCP_SERVER_PORT1;
  settings.m_tcp_initial_inactivity_timeout_ms = 10;
  settings.m_tcp_general_inactivity_timeout_ms = 10;
  rclcpp::Node test_node{"test_node"};
  doip::DoIpDriver<UdpVoidSenderMock,
                   UdpVoidReceiverMock,
                   TcpConnectionMock,
                   TcpServerMock<TcpConnectionMock>,
                   std::shared_ptr<PoolingSubMock>>
    doip(std::make_shared<apex::logging::Logger<>>(&test_node, "TestNode"),
         std::move(settings),
         doip_timer,
         std::make_shared<PoolingSubMock>(),
         test_node.create_publisher<doip::UdsTransportMessage>("resTopic", rclcpp::DefaultQoS()));

  TcpConnectionMock::number_of_tcp_sends = 0;
  // unknown tester address, TCP response shouldn't be sent
  doip.handle_uds_connector_response();
  EXPECT_EQ(TcpConnectionMock::number_of_tcp_sends, 0);

  // try to add new TCP client
  doip.try_accept_tcp_connection();
  // known tester address, TCP response should be sent
  doip.handle_uds_connector_response();
  EXPECT_EQ(TcpConnectionMock::number_of_tcp_sends, 1);
}

TEST_F(DoIpDriverFixture, remove_connection_at_test)
{
  auto doip_timer = std::make_shared<MockTimerBase>();
  const size_t max_num_of_connections = 2U;
  const size_t max_num_of_client_ports = 2U;
  const size_t max_num_of_server_ports = 1U;
  doip::DoIpSettings settings(
    max_num_of_connections, max_num_of_client_ports, max_num_of_server_ports);

  settings.m_tcp_server_address = IP4_PORT_ADDRESS;
  settings.m_tcp_server_port = TCP_SERVER_PORT1;
  settings.m_tcp_initial_inactivity_timeout_ms = 10;
  settings.m_tcp_general_inactivity_timeout_ms = 10;
  rclcpp::Node test_node{"test_node"};
  doip::DoIpDriver<UdpVoidSenderMock,
                   UdpVoidReceiverMock,
                   TcpConnectionMock,
                   TcpServerMock<TcpConnectionMock>>
    doip(std::make_shared<apex::logging::Logger<>>(&test_node, "TestNode"),
         std::move(settings),
         doip_timer,
         test_node.create_polling_subscription<doip::UdsTransportMessage>("reqTopic",
                                                                          rclcpp::DefaultQoS()),
         test_node.create_publisher<doip::UdsTransportMessage>("resTopic", rclcpp::DefaultQoS()));

  // add 2 connections
  doip.try_accept_tcp_connection();
  doip.try_accept_tcp_connection();

  auto triggering_fds = doip.get_triggering_file_descriptors();
  auto is_invalid_fd = [](const auto & fd) { return fd->fd == doip::INVALID_FD; };
  const auto number_of_inactive_tcp_fd_before_remove =
    std::count_if(triggering_fds.begin(), triggering_fds.end(), is_invalid_fd);

  doip.remove_connection_at(0);
  const auto number_of_inactive_tcp_fd_after_first_remove =
    std::count_if(triggering_fds.begin(), triggering_fds.end(), is_invalid_fd);
  EXPECT_EQ(number_of_inactive_tcp_fd_before_remove,
            number_of_inactive_tcp_fd_after_first_remove - 1);

  doip.remove_connection_at(0);
  const auto number_of_inactive_tcp_fd_after_second_remove =
    std::count_if(triggering_fds.begin(), triggering_fds.end(), is_invalid_fd);
  EXPECT_EQ(number_of_inactive_tcp_fd_before_remove,
            number_of_inactive_tcp_fd_after_second_remove - 2);
}

TEST_F(DoIpDriverFixture, check_tcp_sockets_test)
{
  auto doip_timer = std::make_shared<MockTimerBase>();
  const size_t max_num_of_connections = 2U;
  const size_t max_num_of_client_ports = 2U;
  const size_t max_num_of_server_ports = 1U;
  doip::DoIpSettings settings(
    max_num_of_connections, max_num_of_client_ports, max_num_of_server_ports);

  settings.m_tcp_server_address = IP4_PORT_ADDRESS;
  settings.m_tcp_server_port = TCP_SERVER_PORT1;
  settings.m_tcp_initial_inactivity_timeout_ms = 10;
  settings.m_tcp_general_inactivity_timeout_ms = 10;
  rclcpp::Node test_node{"test_node"};
  doip::DoIpDriver<UdpVoidSenderMock,
                   UdpVoidReceiverMock,
                   TcpConnectionMock,
                   TcpServerMock<TcpConnectionMock>>
    doip(std::make_shared<apex::logging::Logger<>>(&test_node, "TestNode"),
         std::move(settings),
         doip_timer,
         test_node.create_polling_subscription<doip::UdsTransportMessage>("reqTopic",
                                                                          rclcpp::DefaultQoS()),
         test_node.create_publisher<doip::UdsTransportMessage>("resTopic", rclcpp::DefaultQoS()));

  auto triggering_fds = doip.get_triggering_file_descriptors();
  for (auto & fd : triggering_fds) {
    fd->revents = POLLIN;
  }

  TcpConnectionMock::number_of_tcp_reads = 0;
  // No accepted clients, so reading shouldn't be called.
  doip.check_tcp_sockets();
  EXPECT_EQ(0, TcpConnectionMock::number_of_tcp_reads);

  // Adding one new client.
  doip.try_accept_tcp_connection();

  // Read should be called as we have one active client.
  TcpConnectionMock::request_type = doip::PayloadType::ROUTING_ACTIVATION_REQUEST;
  doip.check_tcp_sockets();
  const size_t exp_num_of_tcp_reads = 2;  // 1 for msg header and 1 for msg body
  EXPECT_EQ(exp_num_of_tcp_reads, TcpConnectionMock::number_of_tcp_reads);
}
