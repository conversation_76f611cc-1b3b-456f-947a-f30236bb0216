#! [All parameters]
diagnostic:
  doip:
    # 0x01: ISO/DIS 13400-2:2010
    # 0x02: ISO 13400-2:2012
    # 0x03: ISO 13400-2:2019(E)
    protocol_version: 0x03 # (1)

    gid: "GID123" # (2)
    vin: "VIN12345678901234" # (3)
    eid: "EID123" # (4)

    network_interface: "eth0" # optional, not used currently
    mac_as_eid: true  # optional, not used currently
    max_payload_size: 1024 # optional (5)

    tcp_server_address: "127.0.0.1" # (6)
    tcp_server_port: 13400 # (7)
    max_connections: 2 # optional (8)

    udp_connection_address: "127.0.0.1" # (9)
    udp_discovery_port: 13400 # (10)

    uds_node:
      approved_client_addresses: [0x3344, 0x4455] # (11)
      can_ecu_addresses: [0x203,0x204] # optional (12)
      uds_server_address: 0x2233 # (13)
      req_topic: "/uds/req" # (14)
      res_topic: "/uds/ind" # (15)

    tcp_initial_inactivity_timeout_ms: 2000 # optional default 2s (16)
    tcp_general_inactivity_timeout_ms: 300000 # optional default 5min (17)

    vehicle_announcement_interval_ms: 500 # optional (18)
    vehicle_announcement_count: 3 # (19)

#! [All parameters]
