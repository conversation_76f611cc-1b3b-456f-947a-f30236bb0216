// Copyright 2022 Apex.AI, Inc.
// All rights reserved.
/// \file
/// \brief Abstraction to store doip configuration read and parsed from yaml file.

#ifndef DOIP_TRANSPORT__DOIP_SETTINGS_HPP_
#define DOIP_TRANSPORT__DOIP_SETTINGS_HPP_

#include "containers/static_vector.hpp"
#include "doip_transport/visibility_control.hpp"
#include "string/string_strict.hpp"

namespace apex
{
namespace doip_transport
{
constexpr uint8_t VIN_REQUIRED_LENGTH = 17;
constexpr uint8_t GID_REQUIRED_LENGTH = 6;
constexpr uint8_t EID_REQUIRED_LENGTH = 6;
constexpr uint8_t MIN_PAYLOAD_SIZE = 10;
constexpr uint8_t MIN_NUMBER_OF_TCP_AND_UDP_SOCKETS = 1;
constexpr uint32_t DEFAULT_MAX_PAYLOAD_SIZE = 1024;
constexpr size_t DEFAULT_MAX_CONNECTIONS = 1;
constexpr uint32_t DEFAULT_TCP_INITIAL_INACTIVITY_TIMEOUT_MS = 2000;
constexpr uint32_t DEFAULT_TCP_GENERAL_INACTIVITY_TIMEOUT_MS = 300000;  // 5 min
constexpr uint32_t DEFAULT_ANNOUNCEMENT_INTERVAL_MS = 500;
constexpr uint8_t DEFAULT_VEHICLE_ANNOUNCEMENT_COUNT = 3;

/// \struct UdsNodeConfig
/// \brief Keeps obtained configuration of UDS node for DoIp functionality.
/*
 AXIVION Next CodeLine MisraC++2023-15.0.1: Reason: Code Quality (Functional suitability),
 Justification: This class should be copyable since DoIpSettings should be copyable and
 UdsNodeConfig instance is one of member variable in the DoIpSettings class.
 */
struct UdsNodeConfig
{
  /// \brief Constructs a new UdsNodeConfig object, required for static_vector members
  /// initialization.
  /// \param[in] number_of_approved_client_ports amount of accepted client ports.
  /// \param[in] number_of_can_ecu_addresses amount of accepted can ecu addresses.
  explicit UdsNodeConfig(const size_t number_of_approved_client_ports,
                         const size_t number_of_can_ecu_addresses)
  : m_approved_client_addresses(number_of_approved_client_ports),
    m_can_ecu_addresses(number_of_can_ecu_addresses)

  {
  }

  /// \brief List of approved clients.
  apex::static_vector<uint32_t> m_approved_client_addresses;
  /// \brief List of ecu addresses on CAN.
  apex::static_vector<uint16_t> m_can_ecu_addresses;
  /// \brief UDS node address.
  uint16_t m_address{};
  /// \brief Topic to publish requests to the UDS node.
  apex::string_strict256_t m_req_topic{};
  /// \brief Topic to subscribe on responses from the UDS node.
  apex::string_strict256_t m_res_topic{};
};

/// \struct DoIpSettings
/// \brief Keeps obtained configuration of the DoIp node.
/*
 AXIVION Next CodeLine MisraC++2023-15.0.1: Reason: Code Quality (Functional suitability),
 Justification: This class should be copyable since `parse` function returns the local object
 of this class. copy constructor is called in this case.
 */
struct DoIpSettings
{
  /// \brief Constructs a new DoIpSettings object, required for static_vector members
  /// initialization.
  /// \param[in] number_of_max_connections maximum number of accepted TCP connections.
  /// \param[in] number_of_approved_client_ports amount of accepted tester/client ports.
  /// \param[in] number_of_can_ecu_addresses  amount of accepted can ecu addresses..
  explicit DoIpSettings(size_t number_of_max_connections,
                        size_t number_of_approved_client_ports,
                        size_t number_of_can_ecu_addresses)
  : m_max_connections(number_of_max_connections),
    m_uds_node(number_of_approved_client_ports, number_of_can_ecu_addresses)
  {
  }

  /// \brief Used protocol version.
  uint8_t m_protocol_version{};
  /// \brief Inverted value of used protocol version.
  uint8_t m_inverse_protocol_version{};
  /// \brief Group identification.
  apex::string_strict256_t m_gid{};
  /// \brief Vehicle identification number.
  apex::string_strict256_t m_vin{};
  /// \brief Entity identification.
  apex::string_strict256_t m_eid{};
  /// \brief Name of the used network interface.
  apex::string_strict256_t m_network_interface{};
  /// \brief Indicates if the mac number should be used as eid.
  bool m_mac_as_eid{true};
  /// \brief The maximum amount of bytes for message payload.
  uint32_t m_max_payload_size{DEFAULT_MAX_PAYLOAD_SIZE};
  /// \brief IP4 TCP server address.
  apex::string_strict256_t m_tcp_server_address{};
  /// \brief IP4 TCP server port.
  uint16_t m_tcp_server_port{};
  /// \brief The maximum number of simultaneous clients.
  size_t m_max_connections{DEFAULT_MAX_CONNECTIONS};
  /// \brief IP4 address of UDP ports.
  apex::string_strict256_t m_udp_connection_address{};
  /// \brief IP4 UDP client discovery port.
  uint16_t m_udp_discovery_port{};
  /// \brief Configuration of used by DoIp UDS node.
  UdsNodeConfig m_uds_node;
  /// \brief This timeout specifies the maximum time of inactivity directly after a TCP_DATA socket
  /// is established. After the specified time without routing activation, the TCP_DATA socket is
  /// closed by the DoIP entity.
  uint32_t m_tcp_initial_inactivity_timeout_ms{DEFAULT_TCP_INITIAL_INACTIVITY_TIMEOUT_MS};
  /// \brief This timeout specifies the maximum time of inactivity on a TCP_DATA socket
  /// (no data received or sent) before it is closed by the DoIP entity.
  uint32_t m_tcp_general_inactivity_timeout_ms{DEFAULT_TCP_GENERAL_INACTIVITY_TIMEOUT_MS};
  /// \brief Time interval between initial vehicle announcement messages.
  uint32_t m_vehicle_announcement_interval_ms{DEFAULT_ANNOUNCEMENT_INTERVAL_MS};
  /// \brief Number of vehicle announcement messages sent on start.
  uint8_t m_vehicle_announcement_count{};

  /// \brief Validates correctness of parsed settings values.
  void validate_constraints()
  {
    // VIN should have 17 characters
    if (m_vin.size() != VIN_REQUIRED_LENGTH) {
      throw apex::runtime_error("Wrong VIN format.");
    }
    // EID should have 6 characters
    if (m_eid.size() != EID_REQUIRED_LENGTH) {
      throw apex::runtime_error("Wrong EID format.");
    }
    // GID should have 6 characters
    if (m_gid.size() != GID_REQUIRED_LENGTH) {
      throw apex::runtime_error("Wrong GID format.");
    }

    // It requires at least 10 bytes to support processing diagnostic request
    if (m_max_payload_size < MIN_PAYLOAD_SIZE) {
      m_max_payload_size = DEFAULT_MAX_PAYLOAD_SIZE;
    }

    // We need at least 1 UDP and TCP client sockets
    if (m_max_connections < MIN_NUMBER_OF_TCP_AND_UDP_SOCKETS) {
      m_max_connections = DEFAULT_MAX_CONNECTIONS;
    }
  }
};

}  // namespace doip_transport
}  // namespace apex

#endif  // DOIP_TRANSPORT__DOIP_SETTINGS_HPP_
