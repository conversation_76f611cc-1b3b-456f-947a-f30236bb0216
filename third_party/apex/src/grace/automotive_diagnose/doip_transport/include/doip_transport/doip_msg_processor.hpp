// Copyright 2022 Apex.AI, Inc.
// All rights reserved.
/// \file
/// \brief Implements logic to parse, validate and prepare responses for UDP, TCP and UDS requests.

#ifndef DOIP_TRANSPORT__DOIP_MSG_PROCESSOR_HPP_
#define DOIP_TRANSPORT__DOIP_MSG_PROCESSOR_HPP_

#include <memory>
#include <utility>

#include "cpputils/optional.hpp"
#include "doip_transport/doip_settings.hpp"
#include "doip_transport/types/iso_enums.hpp"
#include "doip_transport/types/msg_types.hpp"
#include "doip_transport/visibility_control.hpp"
#include "logging/logging_macros.hpp"

#include "uds_msgs/msg/uds_transport_message.hpp"

namespace apex
{
namespace doip_transport
{
namespace detail
{
constexpr size_t TESTER_ADDRESS_IDX = 0;
constexpr size_t DOIP_ADDRESS_IDX_IN_PAYLOAD = 2;
constexpr size_t ROUTING_ACTIVATION_TYPE_IDX = 2;
constexpr size_t RESPONSE_CODE_IDX_IN_PAYLOAD = 4;
}  // namespace detail
using UdsTransportMessage = uds_msgs::msg::UdsTransportMessage;
using uds_msgs::msg::DoIpHeader;

/// \brief Number of bytes needed to store VIN number.
constexpr uint32_t VIN_SIZE = 17U;
/// \brief Number of bytes needed to store GID.
constexpr uint32_t GID_SIZE = 6U;
/// \brief Number of bytes needed to store EID.
constexpr uint32_t EID_SIZE = 6U;

/// \brief Implements logic to parse, validate and prepare responses for UDP, TCP and UDS requests.
class DOIP_TRANSPORT_PUBLIC MsgProcessor
{
public:
  /// \brief construct new MsgProcessor object.
  /// \param[in] logger pointer to logger, created by owning node.
  /// \param[in] doip_settings settings read from the configuration file.
  MsgProcessor(std::shared_ptr<apex::logging::Logger<>> logger, const DoIpSettings & doip_settings);

  /// \brief Processes UDP requests (vehicle identification requests) and returns response ready
  /// to send if request format was correct.
  /// \param[in] request UDP request to process.
  /// \return Vehicle identification response if the request was correct, otherwise nullopt.
  apex::optional<VehicleIdentificationResponse> process_udp_msg(const UdpRequest & request) const;

  /// \brief Creates UDP response (vehicle identification response).
  /// \param[in] is_positive_response indicates whether the response should be positive or
  /// negative.
  /// \return Vehicle identification response ready to send.
  VehicleIdentificationResponse create_vehicle_identification_response(
    const bool is_positive_response) const;

  /// \brief Processes TCP routing activation request and returns response ready to send.
  /// \tparam Connection The type which delivers TCP connection interface.
  /// \param[in] request TCP routing activation request to process.
  /// \param[in] clients_connections Container with all currently active TCP connections.
  /// \param[in] connection_index Index of processed connection in the internal container.
  /// \return Routing activation response.
  template <typename Connection>
  RoutingActivationResponse process_routing_activation_request(
    const RoutingActivationRequest & request,
    const apex::static_vector<Connection> & clients_connections,
    size_t connection_index) const
  {
    // read tester/client address from request
    uint16_t network_tester_address = 0;
    /*
     AXIVION DISABLE STYLE MisraC++2023-24.5.2: Reason: Code Quality (Functional suitability),
     Justification: Usage of forbidden entity from <cstring> is allowed for functionality.
     */
    /*
     AXIVION DISABLE STYLE MisraC++2023-7.0.3, MisraC++2023-8.2.8: Reason: Code Quality
     (Functional suitability), Justification: type conversion is necessary for functionality.
     */
    memcpy(&network_tester_address,
           &request.m_payload[detail::TESTER_ADDRESS_IDX],
           sizeof(network_tester_address));
    uint16_t host_tester_address = apex_tcp_ntohs(network_tester_address);
    // read ecu/server address from request
    uint16_t network_ecu_address = 0;
    /*
     AXIVION DISABLE STYLE MisraC++2023-24.5.2: Reason: Code Quality (Functional suitability),
     Justification: Usage of forbidden entity from <cstring> is allowed for functionality.
     */
    /*
     AXIVION DISABLE STYLE MisraC++2023-7.0.3, MisraC++2023-8.2.8: Reason: Code Quality
     (Functional suitability), Justification: type conversion is necessary for functionality.
     */
    memcpy(&network_ecu_address,
           &request.m_payload[detail::DOIP_ADDRESS_IDX_IN_PAYLOAD],
           sizeof(network_ecu_address));
    const uint16_t response_doip_address = apex_tcp_htons(network_ecu_address);
    APEX_INFO(*m_logger,
              "Routing activation request from client: ",
              host_tester_address,
              " to server: ",
              network_ecu_address,
              " was received.");

    // Prepare response message
    RoutingActivationResponse response;
    response.m_header.protocol_version = m_doip_settings.m_protocol_version;
    response.m_header.inverse_protocol_version = m_doip_settings.m_inverse_protocol_version;
    response.m_header.payload_type =
      apex_tcp_htons(static_cast<uint16_t>(PayloadType::ROUTING_ACTIVATION_RESPONSE));
    response.m_header.payload_length =
      apex_tcp_htonl(static_cast<uint32_t>(response.m_payload.size()));
    // client address
    memcpy(&response.m_payload[detail::TESTER_ADDRESS_IDX],
           &network_tester_address,
           sizeof(network_tester_address));
    // set logical address of DoIp node
    memcpy(&response.m_payload[detail::DOIP_ADDRESS_IDX_IN_PAYLOAD],
           &response_doip_address,
           sizeof(response_doip_address));
    // AXIVION ENABLE STYLE MisraC++2023-24.5.2
    // AXIVION ENABLE STYLE MisraC++2023-7.0.3, MisraC++2023-8.2.8
    const uint8_t routing_activation_type =
      static_cast<uint8_t>(request.m_payload[detail::ROUTING_ACTIVATION_TYPE_IDX]);

    // Check all possible errors and fill RoutingActivationResponse code
    if (connection_index == m_doip_settings.m_max_connections) {
      response.m_payload[detail::RESPONSE_CODE_IDX_IN_PAYLOAD] =
        static_cast<char>(RoutingActivationResponses::DENIED_LACK_OF_FREE_SOCKETS);
      APEX_WARN(*m_logger,
                "Routing activation denied because all concurrently supported TCP_DATA sockets are",
                "registered and active.");
    } else if (std::find_if(clients_connections.begin(),
                            clients_connections.end(),
                            [&host_tester_address](const auto & connection) {
                              return host_tester_address == connection.m_tester_address;
                            }) != clients_connections.end()) {
      response.m_payload[detail::RESPONSE_CODE_IDX_IN_PAYLOAD] =
        static_cast<char>(RoutingActivationResponses::DENIED_SA_ALREADY_REGISTERED_ON_OTHER_SOCKET);
      APEX_WARN(*m_logger,
                "Routing activation denied because the SA is already registered and active on a",
                "different TCP_DATA socket.");
    } else if ((routing_activation_type !=
                static_cast<uint8_t>(RoutingActivationRequestActivationTypes::DEFAULT)) &&
               (routing_activation_type !=
                static_cast<uint8_t>(RoutingActivationRequestActivationTypes::REQUIRED_BY_ISO))) {
      response.m_payload[detail::RESPONSE_CODE_IDX_IN_PAYLOAD] =
        static_cast<char>(RoutingActivationResponses::DENIED_UNSUPPORTED_ACTIVATION_TYPE);
      APEX_WARN(*m_logger, "Routing activation denied due to unsupported routing activation type.");
    } else if (std::find(m_doip_settings.m_uds_node.m_approved_client_addresses.begin(),
                         m_doip_settings.m_uds_node.m_approved_client_addresses.end(),
                         host_tester_address) ==
               m_doip_settings.m_uds_node.m_approved_client_addresses.end()) {
      response.m_payload[detail::RESPONSE_CODE_IDX_IN_PAYLOAD] =
        static_cast<char>(RoutingActivationResponses::DENIED_UNKNOWN_SOURCE_ADDRESS);
      APEX_WARN(*m_logger, "Routing activation denied due to unknown source address.");
    } else {  // positive_response - add new client data into the internal container
      response.m_payload[detail::RESPONSE_CODE_IDX_IN_PAYLOAD] =
        static_cast<char>(RoutingActivationResponses::SUCCESSFULLY_ACTIVATED);
    }
    const auto response_code_byte =
      static_cast<uint8_t>(response.m_payload[detail::RESPONSE_CODE_IDX_IN_PAYLOAD]);
    assert(response_code_byte <=
           static_cast<uint8_t>(
             RoutingActivationResponses::SUCCESSFULLY_ACTIVATED_CONFIRMATION_REQUIRED));
    response.m_response_code = static_cast<RoutingActivationResponses>(response_code_byte);
    response.m_client_tester_address = host_tester_address;

    return response;
  }

  /// \brief Processes TCP diagnostic request and returns UDS request ready to send if request
  /// format was correct and ack for tester client.
  /// \param[in] is_connection_established Indicates if routing activation routine was done on
  /// currently processing socket.
  /// \param[in] msg_buffer Buffer with the diagnostic request to process.
  /// \param[in] received_payload_length Payload length of the received request.
  /// \return Pair with acknowledge for tester client and UDS request if the received diagnostic
  /// message was correct.
  std::pair<apex::optional<UdsTransportMessage>, DiagnosticAck> process_diagnostic_request(
    bool is_connection_established,
    const apex::static_vector<char> & msg_buffer,
    uint32_t received_payload_length) const;

  /// \brief Processes received UDS response to forward it to tester client.
  /// \param[in] data Received UDS response.
  /// \param[in] msg_buffer TCP response buffer which is sent to tester client.
  /// \return size of response which is sent to tester client.
  size_t process_uds_connector_response(const UdsTransportMessage & data,
                                        apex::static_vector<char> & msg_buffer) const;

private:
  /// \brief The DoIpTransport logger object.
  std::shared_ptr<apex::logging::Logger<>> m_logger;
  /// \brief The DoIpTransport settings got from the yaml configuration file.
  const DoIpSettings & m_doip_settings;
};

}  // namespace doip_transport
}  // namespace apex

#endif  // DOIP_TRANSPORT__DOIP_MSG_PROCESSOR_HPP_
