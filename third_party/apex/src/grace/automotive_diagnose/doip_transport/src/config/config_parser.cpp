// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include "doip_transport/config/config_parser.hpp"

#include <string>

#include "diagnostic_common/config/config_parser.hpp"

namespace apex
{
namespace doip_transport
{
namespace config
{

using apex::settings::construct::array;
using apex::settings::inspect::array_view;
using apex::settings::inspect::boolean;
using apex::settings::inspect::dictionary_view;
using apex::settings::inspect::get;
using apex::settings::inspect::integer;
using apex::settings::inspect::maybe;
using apex::settings::inspect::string_view;
using diagnostic_common::config::get_value;
using inspect::get_required_opt;
using inspect::get_value_or;

DoIpSettings parse(const settings::inspect::dictionary_view & cfg)
{
  const auto doip_params = get<dictionary_view>(cfg, "diagnostic/doip");
  const auto uds_params = get<dictionary_view>(cfg, "diagnostic/doip/uds_node");

  auto approved_clients_view =
    get_required_opt<array_view>(uds_params, "approved_client_addresses");
  auto can_ecu_addresses_view = get<maybe<array_view>>(uds_params, "can_ecu_addresses");
  const size_t number_of_approved_client_ports = approved_clients_view.size();
  size_t number_of_approved_server_ports = 0;
  if (can_ecu_addresses_view.has_value()) {
    number_of_approved_server_ports = can_ecu_addresses_view.value().size();
  }
  const size_t max_connections =
    get_value_or<integer, size_t>(doip_params, "max_connections", DEFAULT_MAX_CONNECTIONS);
  DoIpSettings doip_settings(
    max_connections, number_of_approved_client_ports, number_of_approved_server_ports);

  doip_settings.m_protocol_version = get_value<integer, uint8_t>(doip_params, "protocol_version");
  // Contains the bit-wise inverse value of the protocol version, ISO 13400-2:2019 Table 16.
  /*
   AXIVION Next Construct MisraC++2023-4.1.3, MisraC++2023-7.0.5: Reason: Code Quality
   (Functional suitability), Justification: Intended type conversion to uint8_t
   */
  doip_settings.m_inverse_protocol_version =
    static_cast<uint8_t>(static_cast<uint8_t>(0xFFU) ^ doip_settings.m_protocol_version);
  doip_settings.m_gid = std::string{get<string_view>(doip_params, "gid")};
  doip_settings.m_vin = std::string{get<string_view>(doip_params, "vin")};
  doip_settings.m_eid = std::string{get<string_view>(doip_params, "eid")};
  doip_settings.m_network_interface =
    std::string{get<string_view>(doip_params, "network_interface")};
  doip_settings.m_mac_as_eid = get_value_or<boolean, bool>(doip_params, "mac_as_eid", true);
  doip_settings.m_max_payload_size =
    get_value_or<integer, uint32_t>(doip_params, "max_payload_size", DEFAULT_MAX_PAYLOAD_SIZE);
  doip_settings.m_tcp_server_address =
    std::string{get<string_view>(doip_params, "tcp_server_address")};
  doip_settings.m_tcp_server_port = get_value<integer, uint16_t>(doip_params, "tcp_server_port");
  doip_settings.m_udp_connection_address =
    std::string{get<string_view>(doip_params, "udp_connection_address")};
  doip_settings.m_udp_discovery_port =
    get_value<integer, uint16_t>(doip_params, "udp_discovery_port");
  doip_settings.m_tcp_initial_inactivity_timeout_ms = get_value_or<integer, uint32_t>(
    doip_params, "tcp_initial_inactivity_timeout_ms", DEFAULT_TCP_INITIAL_INACTIVITY_TIMEOUT_MS);
  doip_settings.m_tcp_initial_inactivity_timeout_ms = get_value_or<integer, uint32_t>(
    doip_params, "tcp_general_inactivity_timeout_ms", DEFAULT_TCP_GENERAL_INACTIVITY_TIMEOUT_MS);
  doip_settings.m_tcp_initial_inactivity_timeout_ms = get_value_or<integer, uint32_t>(
    doip_params, "vehicle_announcement_interval_ms", DEFAULT_ANNOUNCEMENT_INTERVAL_MS);
  doip_settings.m_vehicle_announcement_count = get_value_or<integer, uint8_t>(
    doip_params, "vehicle_announcement_count", DEFAULT_VEHICLE_ANNOUNCEMENT_COUNT);

  doip_settings.m_uds_node.m_address =
    get_value<integer, uint16_t>(uds_params, "uds_server_address");
  for (auto & it : approved_clients_view) {
    const uint16_t address_port = cast::safe_cast<uint16_t>(get<integer>(it));
    doip_settings.m_uds_node.m_approved_client_addresses.push_back(address_port);
  }

  if (can_ecu_addresses_view.has_value()) {
    for (auto & it : can_ecu_addresses_view.value()) {
      const uint16_t address_port = cast::safe_cast<uint16_t>(get<integer>(it));
      doip_settings.m_uds_node.m_can_ecu_addresses.push_back(address_port);
    }
  }

  doip_settings.m_uds_node.m_req_topic = std::string{get<string_view>(uds_params, "req_topic")};
  doip_settings.m_uds_node.m_res_topic = std::string{get<string_view>(uds_params, "res_topic")};

  doip_settings.validate_constraints();
  return doip_settings;
}

}  // namespace config
}  // namespace doip_transport
}  // namespace apex
