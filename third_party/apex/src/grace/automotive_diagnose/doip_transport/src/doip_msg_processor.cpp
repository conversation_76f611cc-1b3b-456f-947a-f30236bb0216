// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include "doip_transport/doip_msg_processor.hpp"

#include <algorithm>
#include <memory>
#include <utility>

#include "cpputils/optional.hpp"
#include "doip_transport/doip_settings.hpp"
#include "doip_transport/types/iso_enums.hpp"
#include "doip_transport/types/msg_types.hpp"
#include "logging/logging_macros.hpp"

namespace apex
{
namespace doip_transport
{

MsgProcessor::MsgProcessor(std::shared_ptr<apex::logging::Logger<>> logger,
                           const DoIpSettings & doip_settings)
: m_logger(logger), m_doip_settings(doip_settings)
{
}

apex::optional<VehicleIdentificationResponse> MsgProcessor::process_udp_msg(
  const UdpRequest & request) const
{
  bool is_request_correct = true;
  bool is_known_payload_type = true;

  /*
   AXIVION Next Construct MisraC++2023-7.0.5: Reason: Code Quality (Functional suitability),
   Justification: Intentional type casting from uint16_t to PayloadType
   */
  switch (static_cast<PayloadType>(apex_tcp_ntohs(request.m_header.payload_type))) {
    case PayloadType::VEHICLE_IDENTIFICATION_REQUEST_EMPTY:
      APEX_INFO(*m_logger, "Request with payload type: EMPTY");
      break;
    case PayloadType::VEHICLE_IDENTIFICATION_REQUEST_WITH_EID:
      APEX_INFO(*m_logger, "Request with payload type: WITH_EID");
      break;
    case PayloadType::VEHICLE_IDENTIFICATION_REQUEST_WITH_VIN:
      APEX_INFO(*m_logger, "Request with payload type: WITH_VIN");
      break;
    case PayloadType::DOIP_ENTITY_STATUS_REQUEST:
      APEX_WARN(*m_logger, "DOIP_ENTITY_STATUS request not implemented yet. Sending negative ack.");
      is_request_correct = false;
      break;
    case PayloadType::DIAGNOSTIC_POWER_MODE_INFORMATION_REQUEST:
      APEX_WARN(
        *m_logger,
        "DIAGNOSTIC_POWER_MODE_INFORMATION request not implemented yet. Sending negative ack.");
      is_request_correct = false;
      break;
    case PayloadType::VEHICLE_IDENTIFICATION_RESPONSE:
      APEX_DEBUG(*m_logger, "Triggered by vehicle announcement message - message will be skipped.");
      is_known_payload_type = false;
      break;
    default:
      APEX_WARN(*m_logger, "Request with unknown payload type.");
      is_known_payload_type = false;
      break;
  }

  if (is_known_payload_type) {
    const auto payload_length = apex_tcp_ntohl(request.m_header.payload_length);
    if (payload_length == VIN_SIZE) {
      /*
       AXIVION DISABLE STYLE MisraC++2023-21.2.2: Reason: Code Quality (Functional suitability),
       Justification: use `strncmp` here is safe since two str1 and str2 pointers are valid
       */
      if (strncmp(m_doip_settings.m_vin.data(),
                  (&request.m_payload[0]),
                  static_cast<size_t>(VIN_SIZE)) != 0) {
        is_request_correct = false;
      }
    } else if (payload_length == EID_SIZE) {
      if (strncmp(m_doip_settings.m_eid.data(),
                  (&request.m_payload[0]),
                  static_cast<size_t>(EID_SIZE)) != 0) {
        // AXIVION ENABLE STYLE MisraC++2023-21.2.2
        is_request_correct = false;
      }
    } else {
      APEX_INFO(*m_logger, "Empty request was received.");
    }

    return create_vehicle_identification_response(is_request_correct);
  }

  return apex::nullopt;
}

VehicleIdentificationResponse MsgProcessor::create_vehicle_identification_response(
  const bool is_positive_response) const
{
  VehicleIdentificationResponse response;
  // response header
  response.m_header.protocol_version = m_doip_settings.m_protocol_version;
  response.m_header.inverse_protocol_version = m_doip_settings.m_inverse_protocol_version;

  if (is_positive_response) {
    constexpr size_t SERVER_ADDRESS_SIZE =
      sizeof(decltype(m_doip_settings.m_uds_node.m_can_ecu_addresses)::value_type);

    // response header
    /*
     AXIVION DISABLE STYLE MisraC++2023-4.1.3: Reason: Code Quality (Functional suitability),
     Justification: Intentional type casting from unsigned long to uint32_t. No UB
     */
    response.m_header.payload_type =
      apex_tcp_htons(static_cast<uint16_t>(PayloadType::VEHICLE_IDENTIFICATION_RESPONSE));
    const uint8_t further_action = static_cast<uint8_t>(
      IdentificationResponseFurtherActions::ROUTING_ACTIVATION_REQUIRED_TO_INITIATE);
    response.m_header.payload_length =
      apex_tcp_htonl(VIN_SIZE + static_cast<uint32_t>(SERVER_ADDRESS_SIZE) + EID_SIZE + GID_SIZE +
                     static_cast<uint32_t>(sizeof(further_action)));
    // AXIVION ENABLE STYLE MisraC++2023-4.1.3

    // response payload
    /*
     AXIVION DISABLE STYLE MisraC++2023-24.5.2: Reason: Code Quality (Functional suitability),
     Justification: Usage of forbidden entity from <cstring> is allowed for functionality.
     */
    /*
     AXIVION DISABLE STYLE MisraC++2023-7.0.3, MisraC++2023-8.2.8: Reason: Code Quality
     (Functional suitability), Justification: type conversion is necessary for functionality.
     */
    memcpy(&response.m_payload, m_doip_settings.m_vin.data(), sizeof(VIN_SIZE));
    const uint16_t network_doip_address = apex_tcp_htons(m_doip_settings.m_uds_node.m_address);
    memcpy(&response.m_payload[VIN_SIZE], &network_doip_address, SERVER_ADDRESS_SIZE);
    memcpy(&response.m_payload[VIN_SIZE + SERVER_ADDRESS_SIZE],
           m_doip_settings.m_eid.data(),
           sizeof(EID_SIZE));
    memcpy(&response.m_payload[VIN_SIZE + SERVER_ADDRESS_SIZE + EID_SIZE],
           m_doip_settings.m_gid.data(),
           sizeof(GID_SIZE));
    memcpy(&response.m_payload[VIN_SIZE + SERVER_ADDRESS_SIZE + EID_SIZE + GID_SIZE],
           &further_action,
           sizeof(further_action));
  } else {
    // response header
    response.m_header.payload_type =
      apex_tcp_htons(static_cast<uint16_t>(PayloadType::GENERIC_DOIP_NEGATIVE_ACK));
    response.m_header.payload_length = 0;
    APEX_INFO(*m_logger, "Received vehicle identification request has incorrect format.");
  }

  return response;
}

std::pair<apex::optional<UdsTransportMessage>, DiagnosticAck>
MsgProcessor::process_diagnostic_request(bool is_connection_established,
                                         const apex::static_vector<char> & msg_buffer,
                                         uint32_t received_payload_length) const
{
  constexpr size_t SA_IDX_IN_PAYLOAD = 0;
  constexpr size_t TA_IDX_IN_PAYLOAD = 2;
  constexpr size_t ACK_IDX_IN_PAYLOAD = 4;
  constexpr size_t DATA_IDX_IN_INTERNAL_BUFFER = 4;
  constexpr size_t DATA_IDX_IN_ACK = 5;
  constexpr size_t NUM_OF_BYTES_TO_CPY_TO_ACK_FROM_REQ_PAYLOAD = 5;

  // Start preparing a request for the UDS node
  UdsTransportMessage request;
  uint16_t network_tester_address;
  memcpy(static_cast<void *>(&network_tester_address),
         msg_buffer.data() + SA_IDX_IN_PAYLOAD,
         sizeof(network_tester_address));
  request.tester_address.address = apex_tcp_ntohs(network_tester_address);

  uint16_t network_received_uds_node_address;
  memcpy(static_cast<void *>(&network_received_uds_node_address),
         msg_buffer.data() + TA_IDX_IN_PAYLOAD,
         sizeof(network_received_uds_node_address));
  request.ecu_address.address = apex_tcp_ntohs(network_received_uds_node_address);

  // Create diagnostic ack for client/tester.
  DiagnosticAck ack;
  ack.m_header.protocol_version = m_doip_settings.m_protocol_version;
  ack.m_header.inverse_protocol_version = m_doip_settings.m_inverse_protocol_version;
  ack.m_header.payload_length = apex_tcp_htonl(static_cast<uint32_t>(ack.m_payload.size()));
  // Swap TA with SA
  memcpy(static_cast<void *>(&ack.m_payload[SA_IDX_IN_PAYLOAD]),
         msg_buffer.data() + 2,
         sizeof(uint16_t));  // SA
  memcpy(static_cast<void *>(&ack.m_payload[TA_IDX_IN_PAYLOAD]),
         msg_buffer.data() + 0,
         sizeof(uint16_t));  // TA

  DiagnosticMsgAckCodes ack_code;
  if (!is_connection_established) {
    ack.m_header.payload_type =
      apex_tcp_htons(static_cast<uint16_t>(PayloadType::DIAGNOSTIC_MESSAGE_NEGATIVE_ACK));
    ack_code = DiagnosticMsgAckCodes::INVALID_SOURCE_ADDRESS;
    APEX_WARN(*m_logger, "Diagnostic request failed, because of invalid source address.");
  } else if (std::find(m_doip_settings.m_uds_node.m_can_ecu_addresses.begin(),
                       m_doip_settings.m_uds_node.m_can_ecu_addresses.end(),
                       request.ecu_address.address) ==
               m_doip_settings.m_uds_node.m_can_ecu_addresses.end() &&
             (request.ecu_address.address != m_doip_settings.m_uds_node.m_address)) {
    ack.m_header.payload_type =
      apex_tcp_htons(static_cast<uint16_t>(PayloadType::DIAGNOSTIC_MESSAGE_NEGATIVE_ACK));
    ack_code = DiagnosticMsgAckCodes::UNKNOWN_TARGET_ADDRESS;
    APEX_WARN(*m_logger, "Diagnostic request failed, because of unknown target address.");
  } else if (received_payload_length > m_doip_settings.m_max_payload_size) {
    ack.m_header.payload_type =
      apex_tcp_htons(static_cast<uint16_t>(PayloadType::DIAGNOSTIC_MESSAGE_NEGATIVE_ACK));
    ack_code = DiagnosticMsgAckCodes::OUT_OF_MEMORY;
    APEX_WARN(*m_logger, "Diagnostic request failed, because of too big request payload.");
  } else {
    // positive ack
    ack.m_header.payload_type =
      apex_tcp_htons(static_cast<uint16_t>(PayloadType::DIAGNOSTIC_MESSAGE_POSITIVE_ACK));
    ack_code = DiagnosticMsgAckCodes::ROUTING_CONFIRMATION_ACK;
  }
  memcpy(static_cast<void *>(&ack.m_payload[ACK_IDX_IN_PAYLOAD]), &ack_code, sizeof(ack_code));

  // copying payload to ack response only if OUT_OF_MEMORY error didn't occur
  if (ack_code != DiagnosticMsgAckCodes::OUT_OF_MEMORY) {
    // Update UDS request payload size
    assert(received_payload_length >=
           (sizeof(request.tester_address.address) + sizeof(request.ecu_address.address)));
    const size_t payload_data_size = received_payload_length -
                                     sizeof(request.tester_address.address) -
                                     sizeof(request.ecu_address.address);
    request.payload.resize(payload_data_size);
    // Copy payload from internal buffer to the UDS request
    memcpy(static_cast<void *>(request.payload.data()),
           msg_buffer.data() + DATA_IDX_IN_INTERNAL_BUFFER,
           payload_data_size);

    const auto req_payload_size = msg_buffer.size() - DATA_IDX_IN_INTERNAL_BUFFER;
    memcpy(static_cast<void *>(&ack.m_payload[DATA_IDX_IN_ACK]),
           msg_buffer.data() + DATA_IDX_IN_INTERNAL_BUFFER,
           std::min(req_payload_size, NUM_OF_BYTES_TO_CPY_TO_ACK_FROM_REQ_PAYLOAD));
  }

  if (ack_code == DiagnosticMsgAckCodes::ROUTING_CONFIRMATION_ACK) {
    return {request, ack};
  }

  return {apex::nullopt, ack};
}

size_t MsgProcessor::process_uds_connector_response(const UdsTransportMessage & data,
                                                    apex::static_vector<char> & msg_buffer) const
{
  constexpr size_t SIZE_OF_ADDRESSES =
    sizeof(data.ecu_address.address) + sizeof(data.tester_address.address);

  // response header
  DoIpHeader header;
  header.payload_type = apex_tcp_htons(static_cast<uint16_t>(PayloadType::DIAGNOSTIC_MESSAGE));
  header.payload_length =
    apex_tcp_htonl(static_cast<uint32_t>(data.payload.size() + SIZE_OF_ADDRESSES));
  header.protocol_version = m_doip_settings.m_protocol_version;
  header.inverse_protocol_version = m_doip_settings.m_inverse_protocol_version;
  memcpy(static_cast<void *>(msg_buffer.data()), &header, sizeof(header));
  // response payload, 0-1 SA, 2-3 TA, 4-... Payload
  constexpr size_t SA_IDX_IN_PAYLOAD = 0;
  const uint16_t network_uds_node_address = apex_tcp_htons(data.ecu_address.address);
  memcpy(static_cast<void *>(msg_buffer.data() + sizeof(header) + SA_IDX_IN_PAYLOAD),
         &network_uds_node_address,
         sizeof(network_uds_node_address));
  constexpr size_t TA_IDX_IN_PAYLOAD = 2;
  const uint16_t network_tester_address = apex_tcp_htons(data.tester_address.address);
  memcpy(static_cast<void *>(msg_buffer.data() + sizeof(header) + TA_IDX_IN_PAYLOAD),
         &network_tester_address,
         sizeof(network_tester_address));
  constexpr size_t DATA_IDX_IN_PAYLOAD = 4;
  memcpy(static_cast<void *>(msg_buffer.data() + sizeof(header) + DATA_IDX_IN_PAYLOAD),
         data.payload.data(),
         data.payload.size());
  // AXIVION ENABLE STYLE MisraC++2023-24.5.2
  // AXIVION ENABLE STYLE MisraC++2023-7.0.3, MisraC++2023-8.2.8
  // response size
  return sizeof(header) + data.payload.size() + SIZE_OF_ADDRESSES;
}

}  // namespace doip_transport
}  // namespace apex
