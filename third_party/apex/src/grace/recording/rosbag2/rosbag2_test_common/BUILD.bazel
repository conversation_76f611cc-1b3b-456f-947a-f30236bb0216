load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_python//python:defs.bzl", "py_library")

ros_pkg(
    name = "rosbag2_test_common_pkg",
    cc_libraries = [":rosbag2_test_common_cpp"],
    description = "Commonly used test helper classes and fixtures for rosbag2",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    py_libraries = [":rosbag2_test_common"],
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = [
        "//common/bazel/rules_deployment/configured_env:configured_env_pkg",
        "//grace/interfaces/test_msgs:test_msgs_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/ros/rclcpp/rclcpp_action:rclcpp_action_pkg",
        "//grace/ros/rcutils:rcutils_pkg",
        "@googletest//:googletest_pkg",
    ],
)

apex_cc_library(
    name = "rosbag2_test_common_cpp",
    hdrs = [
        "include/rosbag2_test_common/action_client_manager.hpp",
        "include/rosbag2_test_common/action_server_manager.hpp",
        "include/rosbag2_test_common/client_manager.hpp",
        "include/rosbag2_test_common/memory_management.hpp",
        "include/rosbag2_test_common/process_execution_helpers.hpp",
        "include/rosbag2_test_common/process_execution_helpers_unix.hpp",
        "include/rosbag2_test_common/process_execution_helpers_windows.hpp",
        "include/rosbag2_test_common/publication_manager.hpp",
        "include/rosbag2_test_common/resource_helper.hpp",
        "include/rosbag2_test_common/service_manager.hpp",
        "include/rosbag2_test_common/subscription_manager.hpp",
        "include/rosbag2_test_common/temporary_directory_fixture.hpp",
        "include/rosbag2_test_common/tested_storage_ids.hpp",
        "include/rosbag2_test_common/wait_for.hpp",
    ],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "//common/bazel/rules_deployment/configured_env:runfiles",
        "//grace/interfaces/test_msgs",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rclcpp/rclcpp_action",
        "//grace/ros/rcutils",
    ],
)

py_library(
    name = "rosbag2_test_common",
    srcs = [
        "rosbag2_test_common/__init__.py",
        "rosbag2_test_common/resource_helper.py",
    ],
    data = [":rosbag2_test_common_pkg.wheel_data"],
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/interfaces/test_msgs:test_msgs_py",
        "@rules_python//python/runfiles",
    ],
)
