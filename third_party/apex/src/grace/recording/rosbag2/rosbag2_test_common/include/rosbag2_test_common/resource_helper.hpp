// Copyright 2025, Apex.AI
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef ROSBAG2_TEST_COMMON__RESOURCE_HELPER_HPP_
#define ROSBAG2_TEST_COMMON__RESOURCE_HELPER_HPP_

#include <filesystem>
#include <string_view>
#include <string>

#ifndef _NO_BAZEL_RUNFILES
#include "configured_env/runfiles/apex.hpp"
#endif

namespace fs = std::filesystem;

namespace rosbag2_test_common
{

class ResourceHelper
{
public:
  ResourceHelper() = delete;
  explicit ResourceHelper(std::string_view resources_dir_path)
  : resources_dir_path_(resources_dir_path.data()) {}

  [[nodiscard]] std::string get_relative_resource_path_str(std::string_view resource_path) const
  {
    return get_relative_resource_path(resource_path).generic_string();
  }

  [[nodiscard]] std::filesystem::path
  get_relative_resource_path(std::string_view resource_path) const
  {
    return fs::relative(get_full_resource_path(resource_path), fs::current_path());
  }


  [[nodiscard]] std::string get_full_resource_path_str(std::string_view resource_path) const
  {
    return get_full_resource_path(resource_path).generic_string();
  }

  [[nodiscard]] std::filesystem::path
  get_full_resource_path(std::string_view resource_path) const
  {
    #ifdef _NO_BAZEL_RUNFILES
    auto full_resource_path = fs::path(resources_dir_path_) / resource_path;
    #else
    // If using Bazel, use bazel runfiles_ to get the full path
    auto full_resource_path = fs::path(runfiles_.Rlocation(
          resources_dir_path_ + "/" + resource_path.data()));
    #endif
    return full_resource_path;
  }

  const std::string resources_dir_path_;
  fs::path full_resources_path_;
#ifndef _NO_BAZEL_RUNFILES
  const apex::configured_env::runfiles::ApexRunfiles runfiles_{};
#endif
};

}  // namespace rosbag2_test_common
#endif  // ROSBAG2_TEST_COMMON__RESOURCE_HELPER_HPP_
