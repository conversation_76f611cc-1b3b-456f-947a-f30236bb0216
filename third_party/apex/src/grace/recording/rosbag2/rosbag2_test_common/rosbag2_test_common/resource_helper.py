# Copyright 2025, Apex.AI, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
from pathlib import Path


class ResourceHelper:
    def __init__(self):
        try:
            from rules_python.python.runfiles import runfiles
            self.bazel_runfiles_ = runfiles.Create()
        except ImportError:
            self.bazel_runfiles_ = None

        assert "ROSBAG2_PY_TEST_RESOURCES_DIR" in os.environ, \
            "Set ROSBAG2_PY_TEST_RESOURCES_DIR to the path to the `resources` directory"

        if self.bazel_runfiles_ is None:
            self.resources_path_ = \
                Path(os.environ["ROSBAG2_PY_TEST_RESOURCES_DIR"]).parent / 'resources'
        else:
            self.resources_path_ = Path(os.environ["ROSBAG2_PY_TEST_RESOURCES_DIR"])

    def get_full_resource_path_str(self, resource: str) -> str:
        """
         Public method: returns the absolute path to the requested resource.

        :param resource: The filename or relative path of the resource.
        :return: A string representing the absolute path to the resource.
        """
        full_resource_path = str(self.resources_path_ / resource)
        if self.bazel_runfiles_ is not None:
            # If running under Bazel, we use Bazel's runfiles mechanism
            full_resource_path = self.bazel_runfiles_.Rlocation(full_resource_path)
        return full_resource_path
