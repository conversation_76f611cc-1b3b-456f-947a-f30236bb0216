load("@bazel_skylib//rules:common_settings.bzl", "bool_flag")
load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_pkg//:mappings.bzl", "pkg_files")
load("//common/bazel/rules_deployment:defs.bzl", "executables_collection")
load("//common/bazel/rules_deployment:defs.bzl", "configured_tool")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//tools/launch/launch_testing/rules:defs.bzl", "launch_test")

bool_flag(
    name = "enable_rosbag2_benchmark_tests",
    build_setting_default = False,
    visibility = ["//visibility:private"],
)

config_setting(
    name = "enable_benchmark_tests",
    flag_values = {
        ":enable_rosbag2_benchmark_tests": "true",
    },
)

ros_pkg(
    name = "rosbag2_performance_benchmarking_pkg",
    cc_libraries = [
        ":headers",
    ],
    description = "Rosbag2 writer benchmarking tool",
    lib_executables = [
        ":benchmark_publishers",
        ":results_writer",
        ":writer_benchmark",
    ],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    share_data = [":share_data"],
    tags = ["skip_bst"],
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/cli/ros2cli:ros2cli_pkg",
        "//grace/interfaces/sensor_msgs:sensor_msgs_pkg",
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/recording/rosbag2/rosbag2_compression:rosbag2_compression_pkg",
        "//grace/recording/rosbag2/rosbag2_cpp:rosbag2_cpp_pkg",
        "//grace/recording/rosbag2/rosbag2_performance/rosbag2_performance_benchmarking_msgs:rosbag2_performance_benchmarking_msgs_pkg",
        "//grace/recording/rosbag2/rosbag2_storage:rosbag2_storage_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/ros/rcutils:rcutils_pkg",
        "//grace/ros/rmw:rmw_pkg",
        "//tools/ament/ament_index/ament_index_python:ament_index_python_pkg",
        "@yaml-cpp//:yaml-cpp_pkg",
    ],
)

filegroup(
    name = "config_files",
    srcs = glob(
        [
            "config/benchmarks/*.yaml",
            "config/benchmarks/test/*.yaml",
            "config/storage/*.yaml",
            "config/producers/*.yaml",
        ],
    ),
    visibility = ["//visibility:public"],
)

filegroup(
    name = "scripts_files",
    srcs = glob(["scripts/*.py"]),
    visibility = ["//visibility:public"],
)

filegroup(
    name = "launch_files",
    srcs = glob(["launch/*.py"]),
    visibility = ["//visibility:public"],
)

pkg_files(
    name = "share_data",
    srcs = [
        ":config_files",
        ":launch_files",
        ":scripts_files",
    ],
)

ament_pkg_resources(
    name = "rosbag2_performance_benchmarking_resources",
    testonly = False,
    package = "rosbag2_performance_benchmarking",
    resources = {
        ":benchmark_publishers": "executable",
        ":results_writer": "executable",
        ":writer_benchmark": "executable",
        ":config_files": "share",
        ":launch_files": "share",
        ":scripts_files": "share",
        requirement("psutil"): "share",
    },
    visibility = ["//visibility:public"],
)

cc_library(
    name = "headers",
    hdrs = glob(
        [
            "include/msg_utils/*.hpp",
            "include/rosbag2_performance_benchmarking/*.hpp",
        ],
    ),
    strip_include_prefix = "include",
    tags = ["exclude_sca"],
)

cc_binary(
    name = "writer_benchmark",
    srcs = [
        "src/config_utils.cpp",
        "src/msg_utils/helpers.cpp",
        "src/result_utils.cpp",
        "src/writer_benchmark.cpp",
    ],
    data = [
        "//grace/recording/rosbag2/rosbag2_performance/rosbag2_performance_benchmarking_msgs:ament_resources",
    ],
    tags = ["exclude_sca"],
    deps = [
        ":headers",
        "//grace/interfaces/sensor_msgs",
        "//grace/recording/rosbag2/rosbag2_compression",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_performance/rosbag2_performance_benchmarking_msgs",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/ros/rclcpp/rclcpp",
        "@yaml-cpp//:yaml-cpp",
    ],
)

cc_binary(
    name = "benchmark_publishers",
    srcs = [
        "src/benchmark_publishers.cpp",
        "src/config_utils.cpp",
        "src/msg_utils/helpers.cpp",
    ],
    data = [
        "//grace/recording/rosbag2/rosbag2_performance/rosbag2_performance_benchmarking_msgs:ament_resources",
    ],
    tags = ["exclude_sca"],
    deps = [
        ":headers",
        "//grace/interfaces/sensor_msgs",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_performance/rosbag2_performance_benchmarking_msgs",
        "//grace/recording/rosbag2/rosbag2_storage",
        "@yaml-cpp//:yaml-cpp",
    ],
)

cc_binary(
    name = "results_writer",
    srcs = [
        "src/config_utils.cpp",
        "src/result_utils.cpp",
        "src/results_writer.cpp",
    ],
    tags = ["exclude_sca"],
    deps = [
        ":headers",
        "//grace/interfaces/std_msgs",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_storage",
    ],
)

configured_tool(
    name = "rosbag2_ros2_cli",
    data = [
        ":rosbag2_performance_benchmarking_resources",
    ],
    framework = "Apex.Grace",
    # Needs to be public to be bale to run Rosbag2 benchmarks from CLI
    visibility = ["//visibility:public"],
    deps = [
        "@rules_python//python/runfiles",
        requirement("psutil"),
        "//grace/cli",
    ],
)

executables_collection(
    name = "executables",
    testonly = True,
    executables = [":rosbag2_ros2_cli"],
)

launch_test(
    name = "rosbag2_benchmark_test",
    timeout = "long",
    data = [":executables"],
    launch_test_file = "test/benchmark_test.py",
    local = True,  # Avoid BRE (Bazel Remote Execution)
    ros_domain_id_isolation = True,
    tags = [
        "constrained_test",
        "exclusive",
    ],
    target_compatible_with = select({
        # To enable tests use:
        # `bazel test //grace/recording/rosbag2/rosbag2_performance/rosbag2_performance_benchmarking:all \
        # --//grace/recording/rosbag2/rosbag2_performance/rosbag2_performance_benchmarking:enable_rosbag2_benchmark_tests=true`
        ":enable_benchmark_tests": ["@platforms//os:linux"],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
    deps = [
        requirement("pytest"),
        "//grace/tools/launch_ros/launch_ros",
        "//grace/tools/launch_ros/launch_testing_ros",
        "//tools/ament/ament_index/ament_index_python",
        "//tools/apex_pytest_utils",
        "//tools/launch/launch",
    ],
)
