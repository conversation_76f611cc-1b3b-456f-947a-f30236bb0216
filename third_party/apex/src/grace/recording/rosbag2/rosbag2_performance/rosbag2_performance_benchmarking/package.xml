<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rosbag2_performance_benchmarking</name>
  <version>0.33.0</version>
  <description>Code to benchmark rosbag2</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="adam.<PERSON><PERSON><PERSON>@robotec.ai"><PERSON></maintainer>
  <license>Apache License 2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>apex_cmake</buildtool_depend>

  <exec_depend>ament_index_python</exec_depend>
  <exec_depend>python3-psutil</exec_depend>
  <exec_depend>launch</exec_depend>
  <exec_depend>launch_ros</exec_depend>

  <depend>rclcpp</depend>
  <depend>rosbag2_compression</depend>
  <depend>rosbag2_py</depend>
  <depend>rosbag2_cpp</depend>
  <depend>rosbag2_storage</depend>
  <depend>rmw</depend>
  <depend>rosbag2_performance_benchmarking_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>yaml_cpp_vendor</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>launch_ros</test_depend>
  <test_depend>ros_testing</test_depend>
  <test_depend>ros2launch</test_depend>
  <test_depend>ros2bag</test_depend>
  <test_depend>rosbag2_storage_default_plugins</test_depend>
  <test_depend>rosbag2_converter_default_plugins</test_depend>
  <test_depend>rosbag2_test_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
