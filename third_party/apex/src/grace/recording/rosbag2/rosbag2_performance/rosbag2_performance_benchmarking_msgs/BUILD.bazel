load("//grace/rosidl/rules_rosidl:defs.bzl", "ROSIDL_COMMON_PKGS")
load("//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "rosbag2_performance_benchmarking_msgs_pkg",
    description = "Message definition for performance benchmarking package",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    msg_libraries = [":rosbag2_performance_benchmarking_msgs"],
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = ROSIDL_COMMON_PKGS,
)

msgs_library(
    name = "rosbag2_performance_benchmarking_msgs",
    srcs = glob([
        "msg/*.idl",
        "msg/*.msg",
        "srv/*.srv",
    ]),
    visibility = ["//visibility:public"],
    wheel_data = ":rosbag2_performance_benchmarking_msgs_pkg.wheel_data",
)

ament_pkg_resources(
    name = "ament_resources",
    package = "rosbag2_performance_benchmarking_msgs",
    resources = {
        ":rosbag2_performance_benchmarking_msgs": "rosidl_interface",
    },
    visibility = ["//visibility:public"],
)
