load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")

ros_pkg(
    name = "rosbag2_compression_pkg",
    cc_libraries = [
        ":rosbag2_compression",
    ],
    description = "Compression implementations for rosbag2 bags and messages",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "rosbag2_compression",
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp:rosbag2_cpp_pkg",
        "//grace/recording/rosbag2/rosbag2_storage:rosbag2_storage_pkg",
    ],
)

ament_pkg_resources(
    name = "ament_resources",
    package = "rosbag2_compression",
    resources = {
        ":package.xml": "share",
        "rosbag2_compression_shared": "bazel_solib",
    },
    visibility = ["//visibility:public"],
)

apex_cc_shared_library(
    name = "rosbag2_compression_shared",
    apex_cc_library = "rosbag2_compression",
    # shared_lib_name = "librosbag2_compression.so",
)

apex_cc_library(
    name = "rosbag2_compression",
    srcs = [
        "src/rosbag2_compression/compression_factory.cpp",
        "src/rosbag2_compression/compression_factory_impl.hpp",
        "src/rosbag2_compression/compression_options.cpp",
        "src/rosbag2_compression/logging.hpp",
        "src/rosbag2_compression/sequential_compression_reader.cpp",
        "src/rosbag2_compression/sequential_compression_writer.cpp",
    ],
    hdrs = [
        "include/rosbag2_compression/base_compressor_interface.hpp",
        "include/rosbag2_compression/base_decompressor_interface.hpp",
        "include/rosbag2_compression/compression_factory.hpp",
        "include/rosbag2_compression/compression_options.hpp",
        "include/rosbag2_compression/sequential_compression_reader.hpp",
        "include/rosbag2_compression/sequential_compression_writer.hpp",
        "include/rosbag2_compression/visibility_control.hpp",
    ],
    strip_include_prefix = "include",
    tags = [
        "Use short library name in install space",
        "exclude_sca",
    ],  # exclude Axivion SCA
    target_compatible_with = ["@platforms//os:linux"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_storage",
    ],
)
