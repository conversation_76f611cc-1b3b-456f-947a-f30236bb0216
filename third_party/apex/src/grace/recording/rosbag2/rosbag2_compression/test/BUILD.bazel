load("@rules_pkg//pkg:mappings.bzl", "pkg_files")
load("//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("//tools/ament/rules_ament:defs.bzl", "ament_pluginlib_resources")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "fake_compression_plugin_pkg",
    cc_libraries = [":fake_plugin"],
    description = "Fake plugin for testing rosbag2_compression package",
    license = "Apex.AI license",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "fake_plugin",
    pluginlib_description_files = {
        "rosbag2_compression/fake_plugin.xml": "rosbag2_compression",
    },
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_compression:rosbag2_compression_pkg",
        "//grace/tools/pluginlib/pluginlib:pluginlib_pkg",
    ],
)

apex_cc_library(
    name = "fake_plugin",
    srcs = [
        "rosbag2_compression/fake_compressor.cpp",
        "rosbag2_compression/fake_compressor.hpp",
        "rosbag2_compression/fake_decompressor.cpp",
        "rosbag2_compression/fake_decompressor.hpp",
    ],
    tags = ["Use short library name in install space"],
    visibility = ["//visibility:private"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_compression",
        "//grace/tools/pluginlib/pluginlib",
    ],
)

apex_cc_shared_library(
    name = "fake_plugin_shared",
    apex_cc_library = ":fake_plugin",
)

ament_pkg_resources(
    name = "ament_resources",
    data = ["//grace/recording/rosbag2/rosbag2_compression:ament_resources"],
    package = "fake_plugin",
    resources = {
        ":fake_plugin_shared": "bazel_solib",
        ":package_xml_file": "share",
    },
    visibility = ["//visibility:private"],
)

pkg_files(
    name = "package_xml_file",
    srcs = [":rosbag2_compression/package.xml"],
    strip_prefix = "rosbag2_compression",
)

pkg_files(
    name = "plugin_xml_file",
    srcs = [":rosbag2_compression/fake_plugin.xml"],
    strip_prefix = "rosbag2_compression",
)

ament_pluginlib_resources(
    name = "pluginlib_resources",
    data = [":ament_resources"],
    pluginlib_type = "rosbag2_compression",
    resources = {
        "fake_plugin": ":plugin_xml_file",
    },
    visibility = ["//grace/recording/rosbag2/rosbag2_py/test:__pkg__"],
)

apex_cc_shared_library(
    name = "rosbag2_compression_shared",
    apex_cc_library = "//grace/recording/rosbag2/rosbag2_compression",
)

apex_cc_test(
    name = "test_compression_factory",
    srcs = ["rosbag2_compression/test_compression_factory.cpp"],
    data = [
        ":pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_compression_zstd:pluginlib_resources",
    ],
    dynamic_deps = [":rosbag2_compression_shared"],
    tags = ["skip_asan"],  # FIXME: fix asan findings and remove this tag
    deps = [
        "//grace/recording/rosbag2/rosbag2_compression",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_compression_options",
    srcs = ["rosbag2_compression/test_compression_options.cpp"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_compression",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_sequential_compression_reader",
    srcs = [
        "rosbag2_compression/mock_compression.hpp",
        "rosbag2_compression/mock_compression_factory.hpp",
        "rosbag2_compression/mock_converter_factory.hpp",
        "rosbag2_compression/mock_metadata_io.hpp",
        "rosbag2_compression/mock_storage.hpp",
        "rosbag2_compression/mock_storage_factory.hpp",
        "rosbag2_compression/test_sequential_compression_reader.cpp",
    ],
    data = [
        ":pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_compression_zstd:pluginlib_resources",
    ],
    dynamic_deps = [":rosbag2_compression_shared"],
    tags = ["skip_asan"],  # FIXME: fix asan findings and remove this tag
    deps = [
        "//grace/recording/rosbag2/rosbag2_compression",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/ros/rclcpp/rclcpp",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_sequential_compression_writer",
    srcs = [
        "rosbag2_compression/fake_compression_factory.hpp",
        "rosbag2_compression/fake_compressor.cpp",
        "rosbag2_compression/fake_compressor.hpp",
        "rosbag2_compression/mock_compression_factory.hpp",
        "rosbag2_compression/mock_converter_factory.hpp",
        "rosbag2_compression/mock_metadata_io.hpp",
        "rosbag2_compression/mock_storage.hpp",
        "rosbag2_compression/mock_storage_factory.hpp",
        "rosbag2_compression/test_sequential_compression_writer.cpp",
    ],
    data = [
        ":pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_compression_zstd:pluginlib_resources",
    ],
    dynamic_deps = [":rosbag2_compression_shared"],
    tags = ["skip_asan"],  # FIXME: fix asan findings and remove this tag
    deps = [
        "//grace/recording/rosbag2/rosbag2_compression",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/ros/rclcpp/rclcpp",
        "@googletest//:gtest_main",
    ],
)
