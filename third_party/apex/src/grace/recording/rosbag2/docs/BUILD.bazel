load("@apex//tools/bazel/rules_docs:defs.bzl", "docs_chapter")

docs_chapter(
    name = "docs",
    # all subpackages that contain C++ headers with doxygen comments except
    # vendor packages
    api_srcs = [
        "//grace/recording/rosbag2/rosbag2_compression:_apex_srcs",
        "//grace/recording/rosbag2/rosbag2_compression_zstd:_apex_srcs",
        "//grace/recording/rosbag2/rosbag2_cpp:_apex_srcs",
        "//grace/recording/rosbag2/rosbag2_examples/rosbag2_examples_cpp:_apex_srcs",
        "//grace/recording/rosbag2/rosbag2_py:_apex_srcs",
        "//grace/recording/rosbag2/rosbag2_storage:_apex_srcs",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:_apex_srcs",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:_apex_srcs",
        "//grace/recording/rosbag2/rosbag2_test_common:_apex_srcs",
        "//grace/recording/rosbag2/rosbag2_tests:_apex_srcs",
        "//grace/recording/rosbag2/rosbag2_transport:_apex_srcs",
    ],
    homepage = "recording-and-replaying-with-rosbag.md",
    references = [
        "//grace/recording/rosbag2/rosbag2_performance/rosbag2_player_benchmarking",
        "@apex//common/bazel/rules_deployment/doc",
        "@apex//grace/doc/communication",
        "@apex//grace/examples/sim_time_example/doc",
    ],
    subchapters = [
        ("Converter plugin development", "converter_plugin_development.md"),
        ("Message definition encoding", "message_definition_encoding.md"),
        ("Rosbag2 playback time", "rosbag2_playback_time.md"),
        ("Storage plugin development", "storage_plugin_development.md"),
    ],
)
