<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rosbag2_compression_zstd</name>
  <version>0.33.0</version>
  <description>Zstandard compression library implementation of rosbag2_compression</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>">ROS 2 Tooling WG</maintainer>
  <license>Apache 2.0</license>

  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>apex_cmake</buildtool_depend>

  <depend>pluginlib</depend>
  <depend>rcutils</depend>
  <depend>rosbag2_compression</depend>
  <depend>zstd_vendor</depend>

  <test_depend>ament_cmake_gmock</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>rclcpp</test_depend>
  <test_depend>rosbag2_test_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
