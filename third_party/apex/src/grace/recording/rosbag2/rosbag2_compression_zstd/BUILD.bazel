load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("//tools/ament/rules_ament:defs.bzl", "ament_pluginlib_resources")
load("//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "rosbag2_compression_zstd_pkg",
    cc_libraries = [":rosbag2_compression_zstd"],
    description = "Zstandard compression library implementation of rosbag2_compression",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "rosbag2_compression_zstd",
    pluginlib_description_files = {"plugin_description.xml": "rosbag2_compression"},
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_compression:rosbag2_compression_pkg",
        "//grace/tools/pluginlib/pluginlib:pluginlib_pkg",
        "@zstd//:zstd_pkg",
    ],
)

ament_pkg_resources(
    name = "ament_resources",
    data = ["//grace/recording/rosbag2/rosbag2_compression:ament_resources"],
    package = "rosbag2_compression_zstd",
    resources = {
        ":package.xml": "share",
        ":rosbag2_compression_zstd_shared": "bazel_solib",
    },
    visibility = ["//visibility:public"],
)

ament_pluginlib_resources(
    name = "pluginlib_resources",
    data = [":ament_resources"],
    pluginlib_type = "rosbag2_compression",
    resources = {
        "rosbag2_compression_zstd": ":plugin_description.xml",
    },
    visibility = ["//visibility:public"],
)

apex_cc_shared_library(
    name = "rosbag2_compression_zstd_shared",
    apex_cc_library = "rosbag2_compression_zstd",
    # TODO(32040): uncomment after fix
    # shared_lib_name = "librosbag2_compression_zstd.so",
)

apex_cc_library(
    name = "rosbag2_compression_zstd",
    srcs = [
        "src/rosbag2_compression_zstd/compression_utils.cpp",
        "src/rosbag2_compression_zstd/compression_utils.hpp",
        "src/rosbag2_compression_zstd/logging.hpp",
        "src/rosbag2_compression_zstd/zstd_compressor.cpp",
        "src/rosbag2_compression_zstd/zstd_decompressor.cpp",
    ],
    hdrs = [
        "include/rosbag2_compression_zstd/visibility_control.hpp",
        "include/rosbag2_compression_zstd/zstd_compressor.hpp",
        "include/rosbag2_compression_zstd/zstd_decompressor.hpp",
    ],
    local_defines = ["ROSBAG2_COMPRESSION_ZSTD_BUILDING_DLL"],
    strip_include_prefix = "include",
    tags = [
        "Use short library name in install space",
        "exclude_sca",  # exclude Axivion SCA
    ],
    target_compatible_with = ["@platforms//os:linux"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_compression",
        "//grace/tools/pluginlib/pluginlib",
        "@zstd",
    ],
)

apex_cc_test(
    name = "test_sqlite_wrapper",
    srcs = [
        "test/rosbag2_compression_zstd/test_zstd_compressor.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
    ],
    deps = [
        ":rosbag2_compression_zstd",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/recording/rosbag2/rosbag2_test_common:rosbag2_test_common_cpp",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/tools/pluginlib/pluginlib",
        "@googletest//:gtest_main",
    ],
)
