load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "ROSIDL_COMMON_PKGS")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "rosbag2_interfaces_pkg",
    description = "Interface definitions for controlling rosbag2",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    msg_libraries = [":rosbag2_interfaces"],
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/interfaces/builtin_interfaces:builtin_interfaces_pkg",
    ] + ROSIDL_COMMON_PKGS,
)

msgs_library(
    name = "rosbag2_interfaces",
    srcs = glob([
        "msg/*.idl",
    ]),
    rosmsg_srcs = glob([
        "msg/*.msg",
        "srv/*.srv",
    ]),
    visibility = ["//visibility:public"],
    wheel_data = ":rosbag2_interfaces_pkg.wheel_data",
    deps = [
        "//grace/interfaces/builtin_interfaces",
    ],
)
