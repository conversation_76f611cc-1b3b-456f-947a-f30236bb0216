^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Changelog for package rosbag2_interfaces
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

0.33.0 (2025-04-25)
-------------------

0.32.0 (2025-04-18)
-------------------

0.31.0 (2025-02-02)
-------------------

0.30.0 (2024-11-26)
-------------------

0.29.0 (2024-09-03)
-------------------

0.28.0 (2024-06-17)
-------------------

0.27.0 (2024-04-30)
-------------------

0.26.1 (2024-04-17)
-------------------

0.26.0 (2024-04-16)
-------------------
* Add node name to the Read(Write)SplitEvent message (`#1609 <https://github.com/ros2/rosbag2/issues/1609>`_)
* Contributors: Michael Orlov

0.25.0 (2024-03-27)
-------------------

0.24.0 (2023-07-11)
-------------------

0.23.0 (2023-04-28)
-------------------

0.22.0 (2023-04-18)
-------------------

0.21.0 (2023-04-12)
-------------------
* Update rosbag2 to C++17. (`#1238 <https://github.com/ros2/rosbag2/issues/1238>`_)
* Contributors: Chris Lalancette

0.20.0 (2023-02-14)
-------------------

0.19.0 (2023-01-13)
-------------------
* Add Michael Orlov as maintainer in rosbag2 packages (`#1215 <https://github.com/ros2/rosbag2/issues/1215>`_)
* Contributors: Michael Orlov

0.18.0 (2022-11-15)
-------------------
* Add SplitBagfile recording service. (`#1115 <https://github.com/ros2/rosbag2/issues/1115>`_)
* Contributors: rshanor

0.17.0 (2022-07-30)
-------------------
* Adds stop operation for rosbag2::Player (`#1007 <https://github.com/ros2/rosbag2/issues/1007>`_)
* Notification of significant events during bag recording and playback (`#908 <https://github.com/ros2/rosbag2/issues/908>`_)
* Adds play until timestamp functionality (`#1005 <https://github.com/ros2/rosbag2/issues/1005>`_)
* Add CLI verb for burst mode of playback (`#980 <https://github.com/ros2/rosbag2/issues/980>`_)
* Add play-for specified number of seconds functionality (`#960 <https://github.com/ros2/rosbag2/issues/960>`_)
* Contributors: Agustin Alba Chicar, Geoffrey Biggs, Michael Orlov, Misha Shalem

0.16.0 (2022-05-11)
-------------------

0.15.1 (2022-04-06)
-------------------

0.15.0 (2022-04-05)
-------------------

0.14.1 (2022-03-29)
-------------------
* Bump version number to avoid conflict
* Contributors: Chris Lalancette

0.14.0 (2022-03-29)
-------------------
* Add burst-mode to Player (`#977 <https://github.com/ros2/rosbag2/issues/977>`_)
* Contributors: Geoffrey Biggs

0.13.0 (2022-01-13)
-------------------

0.12.0 (2021-12-17)
-------------------

0.11.0 (2021-11-08)
-------------------
* Update package maintainers (`#899 <https://github.com/ros2/rosbag2/issues/899>`_)
* Contributors: Michel Hidalgo

0.10.1 (2021-10-22)
-------------------

0.10.0 (2021-10-19)
-------------------
* Implement snapshot mechanism and corresponding ROS Service (`#850 <https://github.com/ros2/rosbag2/issues/850>`_)
  * Add snapshot service to recorder node
  * Simplify and clarify double buffering patterns
* Contributors: Cameron Miller

0.9.0 (2021-05-17)
------------------

0.8.0 (2021-04-19)
------------------
* Add rosbag2_interfaces package with playback service definitions (`#728 <https://github.com/ros2/rosbag2/issues/728>`_)
* Contributors: Emerson Knapp
