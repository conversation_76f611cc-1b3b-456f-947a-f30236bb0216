load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("//tools/ament/rules_ament:defs.bzl", "ament_pluginlib_resources")

ENTRY_POINTS = {
    "ros2bag.storage_plugin_cli_extension": [
        "sqlite3 = ros2bag_sqlite3_cli",
    ],
}

ros_pkg(
    name = "rosbag2_storage_sqlite3_pkg",
    cc_libraries = [
        ":rosbag2_storage_sqlite3",
    ],
    description = "Rosbag2 SQLite3 storage plugin",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pluginlib_description_files = {
        "plugin_description.xml": "rosbag2_storage",
    },
    py_libraries = [":ros2bag_sqlite3_cli"],
    version = "0.33.0",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Intended Audience :: Developers",
        "Programming Language :: Python",
    ],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "//grace/recording/rosbag2/rosbag2_storage:rosbag2_storage_pkg",
        "//grace/ros/rcpputils:rcpputils_pkg",
        "//grace/ros/rcutils:rcutils_pkg",
        "//grace/tools/pluginlib/pluginlib:pluginlib_pkg",
        "@sqlite3//:sqlite3_pkg",
        "@yaml-cpp//:yaml-cpp_pkg",
    ],
)

ament_pkg_resources(
    name = "ament_resources",
    data = ["//grace/recording/rosbag2/rosbag2_storage:ament_resources"],
    package = "rosbag2_storage_sqlite3",
    resources = {
        ":package.xml": "share",
        ":rosbag2_storage_sqlite3_shared": "bazel_solib",
    },
    visibility = ["//visibility:public"],
)

ament_pluginlib_resources(
    name = "pluginlib_resources",
    data = [":ament_resources"],
    pluginlib_type = "rosbag2_storage",
    resources = {
        "rosbag2_storage_sqlite3": ":plugin_description.xml",
    },
    visibility = ["//visibility:public"],
)

apex_cc_shared_library(
    name = "rosbag2_storage_sqlite3_shared",
    apex_cc_library = ":rosbag2_storage_sqlite3",
    # TODO(32040)
    # shared_lib_name = "librosbag2_storage_sqlite.so",
)

apex_cc_library(
    name = "rosbag2_storage_sqlite3",
    srcs = [
        "src/rosbag2_storage_sqlite3/logging.hpp",
        "src/rosbag2_storage_sqlite3/sqlite_statement_wrapper.cpp",
        "src/rosbag2_storage_sqlite3/sqlite_storage.cpp",
        "src/rosbag2_storage_sqlite3/sqlite_wrapper.cpp",
    ],
    hdrs = [
        "include/rosbag2_storage_sqlite3/sqlite_exception.hpp",
        "include/rosbag2_storage_sqlite3/sqlite_pragmas.hpp",
        "include/rosbag2_storage_sqlite3/sqlite_statement_wrapper.hpp",
        "include/rosbag2_storage_sqlite3/sqlite_storage.hpp",
        "include/rosbag2_storage_sqlite3/sqlite_wrapper.hpp",
        "include/rosbag2_storage_sqlite3/visibility_control.hpp",
    ],
    local_defines = [
        "ROSBAG2_STORAGE_DEFAULT_PLUGINS_BUILDING_DLL",
    ],
    strip_include_prefix = "include",
    tags = [
        "Use short library name in install space",
        "exclude_sca",  # exclude Axivion SCA
    ],
    target_compatible_with = ["@platforms//os:linux"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/ros/rcpputils",
        "//grace/ros/rcutils",
        "//grace/tools/pluginlib/pluginlib",
        "@sqlite3",
    ],
)

py_entry_points_library(
    name = "ros2bag_sqlite3_cli",
    srcs = ["ros2bag_sqlite3_cli/__init__.py"],
    data = [
        ":rosbag2_storage_sqlite3_pkg.wheel_data",
    ],
    entry_points = ENTRY_POINTS,
    imports = ["."],
    visibility = ["//visibility:public"],
)

apex_cc_test(
    name = "test_sqlite_wrapper",
    srcs = [
        "test/rosbag2_storage_sqlite3/storage_test_fixture.hpp",
        "test/rosbag2_storage_sqlite3/test_sqlite_wrapper.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":rosbag2_storage_sqlite3",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/recording/rosbag2/rosbag2_test_common:rosbag2_test_common_cpp",
        "//grace/ros/rcutils",
        "//grace/tools/pluginlib/pluginlib",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_sqlite_storage",
    srcs = [
        "test/rosbag2_storage_sqlite3/storage_test_fixture.hpp",
        "test/rosbag2_storage_sqlite3/test_sqlite_storage.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "skip_asan",  # FIXME: fix asan findings and remove this tag
    ],
    deps = [
        ":rosbag2_storage_sqlite3",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/recording/rosbag2/rosbag2_test_common:rosbag2_test_common_cpp",
        "//grace/ros/rcutils",
        "//grace/tools/pluginlib/pluginlib",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_sqlite_topic_filter",
    srcs = ["test/rosbag2_storage_sqlite3/test_sqlite_topic_filter.cpp"],
    data = [":pluginlib_resources"],
    dynamic_deps = [":rosbag2_storage_sqlite3_shared"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":rosbag2_storage_sqlite3",
        "//grace/interfaces/std_msgs",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/recording/rosbag2/rosbag2_test_common:rosbag2_test_common_cpp",
        "//grace/tools/pluginlib/pluginlib",
        "@googletest//:gtest_main",
    ],
)
