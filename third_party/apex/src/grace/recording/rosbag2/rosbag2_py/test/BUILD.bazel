load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@rules_python//python:defs.bzl", "py_library")

py_library(
    name = "test_common",
    testonly = True,
    srcs = ["common.py"],
    imports = ["."],
    deps = [
        "@apex//grace/recording/rosbag2/rosbag2_py",
        "@apex//grace/ros/rclpy/rclpy",
    ],
)

filegroup(
    name = "test_resources",
    testonly = True,
    srcs = glob(["resources/**"]),
)

apex_py_test(
    name = "test_compression",
    srcs = ["test_compression.py"],
    data = [
        "resources",
        ":test_resources",
        "//grace/recording/rosbag2/rosbag2_compression_zstd:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    tags = ["constrained_test"],
    deps = [
        ":std_msgs_py",
        ":test_common",
        "@apex//grace/recording/rosbag2/rosbag2_py",
        "@apex//grace/recording/rosbag2/rosbag2_test_common",
        "@apex//grace/ros/rclpy/rclpy",
        "@rules_python//python/runfiles",
    ],
)

apex_py_test(
    name = "test_convert",
    srcs = ["test_convert.py"],
    data = [
        "resources",
        ":test_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    env = {"ROSBAG2_PY_TEST_RESOURCES_DIR": "$(rlocationpath resources)"},
    local = True,  # TODO(): failing in RBE (remote bazel env)
    tags = ["constrained_test"],
    deps = [
        "@apex//grace/recording/rosbag2/rosbag2_py",
        "@apex//grace/recording/rosbag2/rosbag2_test_common",
    ],
)

apex_py_test(
    name = "test_reindexer",
    srcs = ["test_reindexer.py"],
    data = [
        "resources",
        ":test_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    env = {"ROSBAG2_PY_TEST_RESOURCES_DIR": "$(rlocationpath resources)"},
    local = True,  # TODO(): failing in RBE (remote bazel env)
    tags = ["constrained_test"],
    deps = [
        ":test_common",
        "@apex//grace/recording/rosbag2/rosbag2_py",
        "@apex//grace/recording/rosbag2/rosbag2_test_common",
    ],
)

apex_py_test(
    name = "test_sequential_reader",
    srcs = ["test_sequential_reader.py"],
    data = [
        "resources",
        ":test_resources",
        "//grace/recording/rosbag2/rosbag2_storage/test:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    env = {"ROSBAG2_PY_TEST_RESOURCES_DIR": "$(rlocationpath resources)"},
    local = True,  # TODO(): failing in RBE (remote bazel env)
    tags = ["constrained_test"],
    deps = [
        ":rcl_interfaces_py",
        ":std_msgs_py",
        ":test_common",
        "@apex//grace/recording/rosbag2/rosbag2_py",
        "@apex//grace/recording/rosbag2/rosbag2_test_common",
        "@apex//grace/ros/rclpy/rclpy",
        "@apex//grace/rosidl/rosidl_runtime_py",
    ],
)

apex_py_test(
    name = "test_sequential_reader_multiple_files",
    srcs = [
        "test_sequential_reader_multiple_files.py",
    ],
    data = [
        "resources",
        ":test_resources",
        "//grace/recording/rosbag2/rosbag2_storage/test:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    env = {"ROSBAG2_PY_TEST_RESOURCES_DIR": "$(rlocationpath resources)"},
    local = True,  # TODO(): failing in RBE (remote bazel env)
    tags = ["constrained_test"],
    deps = [
        ":test_common",
        "@apex//grace/recording/rosbag2/rosbag2_py",
        "@apex//grace/recording/rosbag2/rosbag2_test_common",
    ],
)

apex_py_test(
    name = "test_sequential_writer",
    srcs = ["test_sequential_writer.py"],
    data = [
        "//grace/recording/rosbag2/rosbag2_compression/test:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_compression_zstd:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_cpp/test:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage/test:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    tags = ["constrained_test"],
    deps = [
        ":std_msgs_py",
        ":test_common",
        "@apex//grace/recording/rosbag2/rosbag2_py",
        "@apex//grace/recording/rosbag2/rosbag2_test_common",
        "@apex//grace/ros/rclpy/rclpy",
        "@apex//grace/rosidl/rosidl_runtime_py",
    ],
)

apex_py_test(
    name = "test_storage",
    srcs = ["test_storage.py"],
    deps = [
        ":test_common",
        "@apex//grace/recording/rosbag2/rosbag2_py",
        "@apex//grace/ros/rclpy/rclpy",
    ],
)

py_msgs_library(
    name = "rcl_interfaces_py",
    msgs = "@apex//grace/interfaces/rcl_interfaces",
)

py_msgs_library(
    name = "std_msgs_py",
    msgs = "@apex//grace/interfaces/std_msgs",
)

apex_py_test(
    name = "test_transport",
    srcs = ["test_transport.py"],
    data = [
        "resources",
        ":test_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    env = {
        "ROSBAG2_PY_TEST_RESOURCES_DIR": "$(rlocationpath resources)",
        "IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc",
    },
    local = True,  # TODO(): failing in RBE (remote bazel env)
    tags = ["constrained_test"],
    deps = [
        ":std_msgs_py",
        ":test_common",
        "//grace/recording/rosbag2/rosbag2_py",
        "//grace/recording/rosbag2/rosbag2_test_common",
        "//grace/ros/rclpy/rclpy",
    ],
)

apex_py_test(
    name = "test_message_definitions",
    srcs = ["test_message_definitions.py"],
    data = [
        "//grace/recording/rosbag2/rosbag2_test_msgdefs:ament_resources",
        "//grace/recording/rosbag2/rosbag2_test_msgdefs:rosbag2_test_msgdefs_ament_resources",
    ],
    deps = [
        "//grace/recording/rosbag2/rosbag2_py",
        "//grace/recording/rosbag2/rosbag2_test_msgdefs",
    ],
)
