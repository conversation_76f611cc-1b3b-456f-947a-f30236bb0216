// Copyright 2020 Open Source Robotics Foundation, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <algorithm>
#include <memory>
#include <string>
#include <unordered_set>
#include <utility>

#include "rosbag2_compression/sequential_compression_writer.hpp"
#include "rosbag2_cpp/converter_options.hpp"
#include "rosbag2_cpp/plugins/plugin_utils.hpp"
#include "rosbag2_cpp/writer.hpp"
#include "rosbag2_cpp/writers/sequential_writer.hpp"
#include "rosbag2_cpp/serialization_format_converter_factory.hpp"
#include "rosbag2_storage/ros_helper.hpp"
#include "rosbag2_storage/storage_filter.hpp"
#include "rosbag2_storage/storage_interfaces/read_write_interface.hpp"
#include "rosbag2_storage/storage_options.hpp"
#include "rosbag2_storage/topic_metadata.hpp"

#include "./pybind11.hpp"

namespace rosbag2_py
{

template<typename T>
class Writer : public rosbag2_cpp::Writer
{
public:
  template<typename ... Args>
  explicit Writer(Args && ... args)
  : rosbag2_cpp::Writer(std::make_unique<T>(std::forward<Args>(args)...))
  {}

  /// Write a serialized message to a bag file
  void write(
    const std::string & topic_name, const std::string & message,
    const rcutils_time_point_value_t & time_stamp, uint32_t sequence_number)
  {
    write(topic_name, message, time_stamp, time_stamp, sequence_number);
  }

  void write(
    const std::string & topic_name, const std::string & message,
    const rcutils_time_point_value_t & recv_timestamp,
    const rcutils_time_point_value_t & send_timestamp,
    uint32_t sequence_number)
  {
    auto bag_message =
      std::make_shared<rosbag2_storage::SerializedBagMessage>();

    bag_message->topic_name = topic_name;
    bag_message->serialized_data =
      rosbag2_storage::make_serialized_message(message.c_str(), message.length());
    bag_message->recv_timestamp = recv_timestamp;
    bag_message->send_timestamp = send_timestamp;
    bag_message->sequence_number = sequence_number;

    rosbag2_cpp::Writer::write(bag_message);
  }
};

std::unordered_set<std::string> get_registered_writers()
{
  return rosbag2_cpp::plugins::get_class_plugins
         <rosbag2_storage::storage_interfaces::ReadWriteInterface>();
}

std::unordered_set<std::string> get_registered_compressors()
{
  return rosbag2_cpp::plugins::get_class_plugins
         <rosbag2_compression::BaseCompressorInterface>();
}

std::unordered_set<std::string> get_registered_serializers()
{
  auto serializers = rosbag2_cpp::plugins::get_class_plugins<
    rosbag2_cpp::converter_interfaces::SerializationFormatSerializer>();
  auto converters = rosbag2_cpp::plugins::get_class_plugins<
    rosbag2_cpp::converter_interfaces::SerializationFormatConverter>();
  serializers.insert(converters.begin(), converters.end());
  return serializers;
}

}  // namespace rosbag2_py

using PyWriter = rosbag2_py::Writer<rosbag2_cpp::writers::SequentialWriter>;
using PyCompressionWriter = rosbag2_py::Writer<rosbag2_compression::SequentialCompressionWriter>;

PYBIND11_MODULE(_writer, m) {
  m.doc() = "Python wrapper of the rosbag2_cpp writer API";

  pybind11::class_<PyWriter>(m, "SequentialWriter")
  .def(pybind11::init())
  .def(
    "open",
    pybind11::overload_cast<
      const rosbag2_storage::StorageOptions &, const rosbag2_cpp::ConverterOptions &
    >(&PyWriter::open))
  .def(
    "write",
    (void (PyWriter::*)(
      const std::string &,
      const std::string &,
      const rcutils_time_point_value_t &,
      uint32_t
    )) & PyWriter::write,
    pybind11::arg("topic_name"),
    pybind11::arg("message"),
    pybind11::arg("time_stamp"),
    pybind11::arg("sequence_number") = 0
  )
  .def(
    "write",
    (void (PyWriter::*)(
      const std::string &,
      const std::string &,
      const rcutils_time_point_value_t &,
      const rcutils_time_point_value_t &,
      uint32_t
    )) & PyWriter::write,
    pybind11::arg("topic_name"),
    pybind11::arg("message"),
    pybind11::arg("recv_timestamp"),
    pybind11::arg("send_timestamp"),
    pybind11::arg("sequence_number") = 0
  )
  .def("close", &PyWriter::close)
  .def("remove_topic", &PyWriter::remove_topic)
  .def(
    "create_topic",
    pybind11::overload_cast<const rosbag2_storage::TopicMetadata &>(&PyWriter::create_topic))
  .def("take_snapshot", &PyWriter::take_snapshot)
  .def("split_bagfile", &PyWriter::split_bagfile)
  ;

  pybind11::class_<PyCompressionWriter>(m, "SequentialCompressionWriter")
  .def(pybind11::init<rosbag2_compression::CompressionOptions>())
  .def(
    "open",
    pybind11::overload_cast<
      const rosbag2_storage::StorageOptions &, const rosbag2_cpp::ConverterOptions &
    >(&PyCompressionWriter::open))
  .def(
    "write",
    (void (PyCompressionWriter::*)(
      const std::string &,
      const std::string &,
      const rcutils_time_point_value_t &,
      uint32_t
    )) & PyCompressionWriter::write,
    pybind11::arg("topic_name"),
    pybind11::arg("message"),
    pybind11::arg("time_stamp"),
    pybind11::arg("sequence_number") = 0
  )
  .def(
    "write",
    (void (PyCompressionWriter::*)(
      const std::string &,
      const std::string &,
      const rcutils_time_point_value_t &,
      const rcutils_time_point_value_t &,
      uint32_t
    )) & PyCompressionWriter::write,
    pybind11::arg("topic_name"),
    pybind11::arg("message"),
    pybind11::arg("recv_timestamp"),
    pybind11::arg("send_timestamp"),
    pybind11::arg("sequence_number") = 0
  )
  .def("remove_topic", &PyCompressionWriter::remove_topic)
  .def(
    "create_topic",
    pybind11::overload_cast<
      const rosbag2_storage::TopicMetadata &
    >(&PyCompressionWriter::create_topic))
  .def("take_snapshot", &PyCompressionWriter::take_snapshot)
  .def("split_bagfile", &PyCompressionWriter::split_bagfile)
  ;

  m.def(
    "get_registered_writers",
    &rosbag2_py::get_registered_writers,
    "Returns list of discovered plugins that support rosbag2 recording");

  m.def(
    "get_registered_compressors",
    &rosbag2_py::get_registered_compressors,
    "Returns list of compression plugins available for rosbag2 recording");

  m.def(
    "get_registered_serializers",
    &rosbag2_py::get_registered_serializers,
    "Returns list of serialization format plugins available for rosbag2 recording");
}
