// Copyright 2024 Open Source Robotics Foundation, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.


#ifndef ROSBAG2_PY__SERVICE_EVENT_INFO_HPP_
#define ROSBAG2_PY__SERVICE_EVENT_INFO_HPP_

#include <string>

namespace rosbag2_py
{

struct ServiceMetadata
{
  std::string name;
  std::string type;
  std::string serialization_format;
};

struct ServiceEventInformation
{
  ServiceMetadata service_metadata;
  size_t event_message_count = 0;
};

}  // namespace rosbag2_py

#endif  // ROSBAG2_PY__SERVICE_EVENT_INFO_HPP_
