# rosbag2_py

## Regenerating Python stub files (.pyi)

Python stub files allow to supply type-hinting information for binary Python modules (e.g. pybind-based).

In rosbag2_py stub files are generated with utility called `stubgen`.

To regenerate stub files with colcon build, follow these steps:
```
cd <workspace>
colcon build --packages-up-to rosbag2_py
source install/setup.sh
# Make sure rosbag2_py can be imported
python3 -c 'import rosbag2_py'

# Ubuntu 24.04
sudo apt update && sudo apt install mypy

# Older Ubuntu
# pip3 install -U mypy==1.9

cd <rosbag2 git repo>
stubgen -p rosbag2_py -o rosbag2_py
```

To regenerate stub files with <PERSON>zel build, follow these steps:

1. Install mypy:
   - For Ubuntu 24.04 and later, use the following command:
     ```shell dollar
     $ sudo apt update && sudo apt install mypy
     ```
   - For older Ubuntu versions, use the following command::
     ```shell dollar
     $ pip3 install -U mypy==1.9
     ```

2. Run the stubgen tool on sources
```shell dollar
$ stubgen ./grace/recording/rosbag2/rosbag2_py/rosbag2_py -o ./grace/recording/rosbag2/rosbag2_py
```

3. Verify the generated stubs: Check the rosbag2_py directory for the generated .pyi files.