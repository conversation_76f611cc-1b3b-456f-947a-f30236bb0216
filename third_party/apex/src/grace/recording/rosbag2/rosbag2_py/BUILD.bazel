load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_python//python:defs.bzl", "py_library")

ros_pkg(
    name = "rosbag2_py_pkg",
    description = "Python API for rosbag2",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "rosbag2_py",
    py_libraries = [
        ":rosbag2_py",
    ],
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_compression:rosbag2_compression_pkg",
        "//grace/recording/rosbag2/rosbag2_cpp:rosbag2_cpp_pkg",
        "//grace/recording/rosbag2/rosbag2_storage:rosbag2_storage_pkg",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:rosbag2_storage_mcap_pkg",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:rosbag2_storage_sqlite3_pkg",
        "//grace/recording/rosbag2/rosbag2_transport:rosbag2_transport_pkg",
        "//grace/ros/rpyutils:rpyutils_pkg",
        "@pybind11//:pybind11_pkg",
    ],
)

apex_cc_library(
    name = "compression_options",
    srcs = [
        "src/rosbag2_py/_compression_options.cpp",
        "src/rosbag2_py/pybind11.hpp",
    ],
    shared_lib_name = "rosbag2_py/_compression_options.so",
    tags = [
        "Python extension module",
        "exclude_sca",
    ],
    deps = [
        "//grace/recording/rosbag2/rosbag2_compression",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_storage",
        "@pybind11",
    ],
)

apex_cc_library(
    name = "message_definitions",
    srcs = [
        "src/rosbag2_py/_message_definitions.cpp",
        "src/rosbag2_py/pybind11.hpp",
    ],
    shared_lib_name = "rosbag2_py/_message_definitions.so",
    tags = [
        "Python extension module",
        "exclude_sca",
    ],
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "@pybind11",
    ],
)

apex_cc_library(
    name = "reader",
    srcs = [
        "src/rosbag2_py/_reader.cpp",
        "src/rosbag2_py/pybind11.hpp",
    ],
    # TODO(32040)
    shared_lib_name = "rosbag2_py/_reader.so",
    tags = [
        "Python extension module",
        "exclude_sca",
    ],
    deps = [
        "//grace/recording/rosbag2/rosbag2_compression",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_storage",
        "@pybind11",
    ],
)

apex_cc_library(
    name = "storage",
    srcs = [
        "src/rosbag2_py/_storage.cpp",
        "src/rosbag2_py/action_info.hpp",
        "src/rosbag2_py/format_bag_metadata.cpp",
        "src/rosbag2_py/format_bag_metadata.hpp",
        "src/rosbag2_py/info_sorting_method.cpp",
        "src/rosbag2_py/info_sorting_method.hpp",
        "src/rosbag2_py/pybind11.hpp",
        "src/rosbag2_py/service_event_info.hpp",
    ],
    # TODO(32040)
    shared_lib_name = "rosbag2_py/_storage.so",
    tags = [
        "Python extension module",
        "exclude_sca",
    ],
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_storage",
        "@pybind11",
    ],
)

apex_cc_library(
    name = "writer",
    srcs = [
        "src/rosbag2_py/_writer.cpp",
        "src/rosbag2_py/pybind11.hpp",
    ],
    shared_lib_name = "rosbag2_py/_writer.so",
    tags = [
        "Python extension module",
        "exclude_sca",
    ],
    deps = [
        "//grace/recording/rosbag2/rosbag2_compression",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_storage",
        "@pybind11",
    ],
)

apex_cc_library(
    name = "info",
    srcs = [
        "src/rosbag2_py/_info.cpp",
        "src/rosbag2_py/action_info.hpp",
        "src/rosbag2_py/format_action_info.cpp",
        "src/rosbag2_py/format_action_info.hpp",
        "src/rosbag2_py/format_bag_metadata.cpp",
        "src/rosbag2_py/format_bag_metadata.hpp",
        "src/rosbag2_py/format_service_info.cpp",
        "src/rosbag2_py/format_service_info.hpp",
        "src/rosbag2_py/format_utils.cpp",
        "src/rosbag2_py/format_utils.hpp",
        "src/rosbag2_py/info_sorting_method.cpp",
        "src/rosbag2_py/info_sorting_method.hpp",
        "src/rosbag2_py/pybind11.hpp",
        "src/rosbag2_py/service_event_info.hpp",
    ],
    shared_lib_name = "rosbag2_py/_info.so",
    tags = [
        "Python extension module",
        "exclude_sca",
    ],
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_storage",
        "@pybind11",
    ],
)

apex_cc_library(
    name = "transport",
    srcs = [
        "src/rosbag2_py/_transport.cpp",
        "src/rosbag2_py/pybind11.hpp",
    ],
    # Has to be here instead of the extractor to rename the real bazel_solib
    shared_lib_name = "rosbag2_py/_transport.so",
    tags = [
        "Python extension module",
        "exclude_sca",
    ],
    deps = [
        "//grace/recording/rosbag2/rosbag2_compression",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/recording/rosbag2/rosbag2_transport:player",
        "//grace/recording/rosbag2/rosbag2_transport:recorder",
        "@pybind11",
    ],
)

apex_cc_library(
    name = "reindexer",
    srcs = [
        "src/rosbag2_py/_reindexer.cpp",
        "src/rosbag2_py/pybind11.hpp",
    ],
    # TODO(32040)
    shared_lib_name = "rosbag2_py/_reindexer.so",
    tags = [
        "Python extension module",
        "exclude_sca",
    ],
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_storage",
        "@pybind11",
    ],
)

apex_cc_shared_library(
    name = "compression_options_shared",
    apex_cc_library = ":compression_options",
    # shared_lib_name = "rosbag2_py/_compression_options.so",
)

apex_cc_shared_library(
    name = "info_shared",
    apex_cc_library = ":info",
    # TODO(32040)
    # shared_lib_name = "rosbag2_py/_info.so",
)

apex_cc_shared_library(
    name = "message_definitions_shared",
    apex_cc_library = ":message_definitions",
    # shared_lib_name = "rosbag2_py/_message_definitions.so",
)

apex_cc_shared_library(
    name = "reader_shared",
    apex_cc_library = ":reader",
    # shared_lib_name = "rosbag2_py/_reader.so",
)

apex_cc_shared_library(
    name = "reindexer_shared",
    apex_cc_library = ":reindexer",
    # shared_lib_name = "rosbag2_py/_reindexer.so",
)

apex_cc_shared_library(
    name = "storage_shared",
    apex_cc_library = ":storage",
    # shared_lib_name = "rosbag2_py/_storage.so",
)

apex_cc_shared_library(
    name = "transport_shared",
    apex_cc_library = ":transport",
    # shared_lib_name = "rosbag2_py/_transport.so",
)

apex_cc_shared_library(
    name = "writer_shared",
    apex_cc_library = ":writer",
    # shared_lib_name = "rosbag2_py/_writer.so",
)

py_library(
    name = "rosbag2_py",
    srcs = ["rosbag2_py/__init__.py"],
    data = select({
        "@apex//common/build_system_transfer:enabled": [
        ],
        "//conditions:default": [
            ":compression_options_shared",
            ":info_shared",
            ":message_definitions_shared",
            ":reader_shared",
            ":reindexer_shared",
            ":rosbag2_py_pkg.wheel_data",
            ":storage_shared",
            ":transport_shared",
            ":writer_shared",
        ],
    }),
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/ros/rclpy/rclpy",
        "//grace/ros/rpyutils",  # Imported from within pybind11
    ],
)
