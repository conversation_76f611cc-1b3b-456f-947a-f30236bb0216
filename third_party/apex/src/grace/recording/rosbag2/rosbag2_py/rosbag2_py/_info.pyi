import rosbag2_py._storage

class Info:
    def __init__(self) -> None: ...
    def get_sorting_methods(self) -> Set[str]: ...
    def print_output(self, arg0: rosbag2_py._storage.BagMetadata, arg1: str) -> None: ...
    def print_output_topic_name_only(self, arg0: rosbag2_py._storage.BagMetadata, arg1: str) -> None: ...
    def print_output_verbose(self, arg0: str, arg1: rosbag2_py._storage.BagMetadata, arg2: str) -> None: ...
    def read_metadata(self, arg0: str, arg1: str) -> rosbag2_py._storage.BagMetadata: ...
