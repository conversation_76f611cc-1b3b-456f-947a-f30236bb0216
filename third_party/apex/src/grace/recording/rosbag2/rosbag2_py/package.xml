<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rosbag2_py</name>
  <version>0.33.0</version>
  <description>Python API for rosbag2</description>
  <maintainer email="micha<PERSON>.<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>">ROS Tooling Working Group</maintainer>
  <license>Apache License 2.0</license>

  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>ament_cmake_ros</buildtool_depend>
  <buildtool_depend>apex_cmake</buildtool_depend>
  <buildtool_depend>ament_cmake_python</buildtool_depend>

  <build_depend>python3-dev</build_depend>

  <depend>pybind11_vendor</depend>
  <depend>rosbag2_compression</depend>
  <depend>rosbag2_cpp</depend>
  <depend>rosbag2_storage</depend>
  <depend>rosbag2_transport</depend>

  <exec_depend>rclpy</exec_depend>
  <exec_depend>rpyutils</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>python3-pytest</test_depend>
  <test_depend>rcl_interfaces</test_depend>
  <test_depend>rosbag2_compression_zstd</test_depend>
  <test_depend>rosbag2_storage_default_plugins</test_depend>
  <test_depend>rosbag2_test_common</test_depend>
  <test_depend>rosidl_runtime_py</test_depend>
  <test_depend>std_msgs</test_depend>
  <test_depend>rosbag2_test_msgdefs</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
