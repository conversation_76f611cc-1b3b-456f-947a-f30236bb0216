recorder_params_node:
  ros__parameters:

    use_sim_time: false

    record:
      all_topics: true
      all_services: true
      all_actions: true
      is_discovery_disabled: true
      topics: ["topic", "other_topic"]
      topic_types: ["std_msgs/msg/Header", "geometry_msgs/msg/Pose"]
      exclude_topic_types: ["sensor_msgs/msg/Image"]
      services: ["service", "other_service"]
      actions: ["action", "other_action"]
      rmw_serialization_format: "cdr"
      topic_polling_interval:
        sec: 0
        nsec: 10000000
      regex: "[xyz]/topic"
      exclude_regex: "(.*)"
      exclude_topics: ["exclude_topic", "other_exclude_topic"]
      exclude_services: ["exclude_service", "other_exclude_service"]
      exclude_actions: ["exclude_action", "other_exclude_action"]
      node_prefix: "prefix"
      compression_mode: "file"
      compression_format: "zstd"
      compression_queue_size: 10
      compression_threads: 2
      compression_threads_priority: -1
      qos_profile_overrides_path: ""
      include_hidden_topics: true
      include_unpublished_topics: true
      ignore_leaf_topics: false
      start_paused: false
      disable_keyboard_controls: true

    storage:
      uri: "path/to/some_bag"
      storage_id: "sqlite3"
      storage_config_uri: ""
      max_bagfile_size: 2147483646
      max_bagfile_duration: 2147483646
      max_cache_size: 989888
      storage_preset_profile: "none"
      snapshot_mode: false
      custom_data: ["key1=value1", "key2=value2"]
      start_time_ns: 0
      end_time_ns: 100000
