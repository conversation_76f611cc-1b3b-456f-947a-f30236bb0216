load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("//grace/recording/rosbag2/rosbag2_storage_mcap:detail/defs.bzl", "MCAP_COMPILE_DEFS")

apex_cc_shared_library(
    name = "std_msgs_shared",
    apex_cc_library = "//grace/interfaces/std_msgs",
)

apex_cc_shared_library(
    name = "rosbag2_storage_mcap_shared",
    apex_cc_library = "//grace/recording/rosbag2/rosbag2_storage_mcap",
)

apex_cc_test(
    name = "test_mcap_storage",
    srcs = ["rosbag2_storage_mcap/test_mcap_storage.cpp"],
    copts = ["-std=c++17"],
    data = [
        "rosbag2_storage_mcap",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
    ],
    dynamic_deps = [
        ":std_msgs_shared",
        ":rosbag2_storage_mcap_shared",
    ],
    local = True,  # TODO(): failing in RBE (remote bazel env)
    local_defines = [
        "_TEST_RESOURCES_DIR_PATH=\\\"$(rootpath rosbag2_storage_mcap)\\\"",
    ] + MCAP_COMPILE_DEFS,
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/recording/rosbag2/rosbag2_storage_mcap",
        "//grace/recording/rosbag2/rosbag2_test_common:rosbag2_test_common_cpp",
        "//grace/ros/rclcpp/rclcpp",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_mcap_topic_filter",
    srcs = ["rosbag2_storage_mcap/test_mcap_topic_filter.cpp"],
    copts = ["-std=c++17"],
    data = [
        "rosbag2_storage_mcap",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
    ],
    dynamic_deps = [
        ":std_msgs_shared",
        ":rosbag2_storage_mcap_shared",
    ],
    local_defines = [
        "_TEST_RESOURCES_DIR_PATH=\\\"$(rootpath rosbag2_storage_mcap)\\\"",
    ] + MCAP_COMPILE_DEFS,
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/recording/rosbag2/rosbag2_test_common:rosbag2_test_common_cpp",
        "//grace/ros/rclcpp/rclcpp",
        "@googletest//:gtest_main",
    ],
)
