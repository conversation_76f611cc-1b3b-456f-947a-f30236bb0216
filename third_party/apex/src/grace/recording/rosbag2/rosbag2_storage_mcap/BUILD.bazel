load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("//tools/ament/rules_ament:defs.bzl", "ament_pluginlib_resources")
load(":detail/defs.bzl", "MCAP_COMPILE_DEFS")

exports_files([".clang-format"])

ENTRY_POINTS = {
    "ros2bag.storage_plugin_cli_extension": [
        "mcap = ros2bag_mcap_cli",
    ],
}

ros_pkg(
    name = "rosbag2_storage_mcap_pkg",
    cc_libraries = [
        ":rosbag2_storage_mcap",
    ],
    description = "rosbag2 storage plugin using the MCAP file format",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pluginlib_description_files = {
        "plugin_description.xml": "rosbag2_storage",
    },
    py_libraries = [":ros2bag_mcap_cli"],
    version = "0.33.0",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Intended Audience :: Developers",
        "Programming Language :: Python",
    ],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "//grace/recording/rosbag2/mcap_vendor:mcap_pkg",
        "//grace/recording/rosbag2/rosbag2_storage:rosbag2_storage_pkg",
        "//grace/ros/rcutils:rcutils_pkg",
        "//grace/tools/pluginlib/pluginlib:pluginlib_pkg",
    ],
)

ament_pkg_resources(
    name = "ament_resources",
    data = ["//grace/recording/rosbag2/rosbag2_storage:ament_resources"],
    package = "rosbag2_storage_mcap",
    resources = {
        ":package.xml": "share",
        ":rosbag2_storage_mcap_shared": "bazel_solib",
    },
    visibility = ["//visibility:public"],
)

ament_pluginlib_resources(
    name = "pluginlib_resources",
    data = [":ament_resources"],
    pluginlib_type = "rosbag2_storage",
    resources = {
        "rosbag2_storage_mcap": ":plugin_description.xml",
    },
    visibility = ["//visibility:public"],
)

apex_cc_shared_library(
    name = "rosbag2_storage_mcap_shared",
    apex_cc_library = "rosbag2_storage_mcap",
    # TODO(32040): uncomment after fix
    # shared_lib_name = "librosbag2_storage_mcap.so",
)

apex_cc_library(
    name = "rosbag2_storage_mcap",
    srcs = ["src/mcap_storage.cpp"],
    hdrs = ["include/rosbag2_storage_mcap/visibility_control.hpp"],
    copts = ["-std=c++17"],
    local_defines = MCAP_COMPILE_DEFS,
    strip_include_prefix = "include",
    tags = ["Use short library name in install space"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/recording/rosbag2/mcap_vendor:mcap",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/ros/rcutils",
        "//grace/tools/pluginlib/pluginlib",
    ],
)

py_entry_points_library(
    name = "ros2bag_mcap_cli",
    srcs = ["ros2bag_mcap_cli/__init__.py"],
    data = [
        ":rosbag2_storage_mcap_pkg.wheel_data",
    ],
    entry_points = ENTRY_POINTS,
    imports = ["."],
    visibility = ["//visibility:public"],
)
