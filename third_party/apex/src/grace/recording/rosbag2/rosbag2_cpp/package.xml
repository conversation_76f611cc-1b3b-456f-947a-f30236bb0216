<?xml version="1.0"?>
<package format="2">
  <name>rosbag2_cpp</name>
  <version>0.33.0</version>
  <description>C++ ROSBag2 client library</description>
  <maintainer email="micha<PERSON>.<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>">ROS Tooling Working Group</maintainer>
  <license>Apache License 2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>apex_cmake</buildtool_depend>

  <depend>ament_index_cpp</depend>
  <depend>pluginlib</depend>
  <depend>rclcpp</depend>
  <depend>rcutils</depend>
  <depend>rcpputils</depend>
  <depend>rmw</depend>
  <depend>rmw_implementation</depend>
  <depend>rosbag2_storage</depend>
  <depend>rosidl_generator_c</depend>
  <depend>rosidl_generator_cpp</depend>
  <depend>rosidl_typesupport_cpp</depend>
  <depend>rosidl_typesupport_introspection_cpp</depend>

  <test_depend>rosbag2_storage_default_plugins</test_depend>
  <test_depend>ament_cmake_gmock</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>rmw_implementation_cmake</test_depend>
  <test_depend>test_msgs</test_depend>
  <test_depend>std_msgs</test_depend>
  <test_depend>rosbag2_test_common</test_depend>
  <test_depend>rosbag2_test_msgdefs</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
