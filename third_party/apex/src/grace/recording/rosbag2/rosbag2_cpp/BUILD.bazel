load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")

ros_pkg(
    name = "rosbag2_cpp_pkg",
    cc_libraries = [
        ":rosbag2_cpp",
    ],
    description = "C++ rosbag2 client library",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_storage:rosbag2_storage_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/ros/rcpputils:rcpputils_pkg",
        "//grace/ros/rcutils:rcutils_pkg",
        "//grace/ros/rmw:rmw_pkg",
        "//grace/rosidl/rosidl_runtime_c:rosidl_runtime_c_pkg",
        "//grace/rosidl/rosidl_runtime_cpp:rosidl_runtime_cpp_pkg",
        "//grace/rosidl/rosidl_typesupport_introspection_cpp:rosidl_typesupport_introspection_cpp_pkg",
        "//grace/tools/pluginlib/pluginlib:pluginlib_pkg",
    ],
)

USE_DLL_EXPORT_DEF = ["ROSBAG2_CPP_BUILDING_DLL"]

ament_pkg_resources(
    name = "ament_resources",
    package = "rosbag2_cpp",
    resources = {
        ":package.xml": "share",
    },
    visibility = ["//visibility:public"],
)

apex_cc_library(
    name = "rosbag2_cpp",
    srcs = glob(["src/**/*.*pp"]),
    hdrs = glob(["include/**/*.hpp"]),
    data = [":ament_resources"],
    local_defines = USE_DLL_EXPORT_DEF,
    strip_include_prefix = "include",
    tags = ["exclude_sca"],  # exclude Axivion SCA
    target_compatible_with = ["@platforms//os:linux"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/ros/rcpputils",
        "//grace/ros/rcutils",
        "//grace/ros/rmw",
        "//grace/rosidl/rosidl_runtime_c",
        "//grace/rosidl/rosidl_runtime_cpp",
        "//grace/rosidl/rosidl_typesupport_introspection_cpp",
        "//grace/tools/pluginlib/pluginlib",
    ],
)
