load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_pkg//pkg:mappings.bzl", "pkg_files")
load("//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library")
load("//tools/ament/rules_ament:defs.bzl", "ament_pluginlib_resources")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")

ros_pkg(
    name = "converter_test_plugins_pkg",
    cc_libraries = [":converter_test_plugins"],
    description = "Test package for rosbag2_cpp",
    license = "Apex.AI license",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "converter_test_plugins",
    pluginlib_description_files = {"rosbag2_cpp/converter_test_plugin.xml": "rosbag2_cpp"},
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp:rosbag2_cpp_pkg",
        "//grace/recording/rosbag2/rosbag2_storage:rosbag2_storage_pkg",
        "//grace/tools/pluginlib/pluginlib:pluginlib_pkg",
    ],
)

apex_cc_library(
    name = "converter_test_plugins",
    srcs = [
        "rosbag2_cpp/converter_test_plugin.cpp",
        "rosbag2_cpp/converter_test_plugin.hpp",
        "rosbag2_cpp/serializer_test_plugin.cpp",
        "rosbag2_cpp/serializer_test_plugin.hpp",
    ],
    tags = [
        "Use short library name in install space",
        "exclude_sca",  # exclude Axivion SCA
    ],
    target_compatible_with = ["@platforms//os:linux"],
    visibility = ["//visibility:private"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/tools/pluginlib/pluginlib",
    ],
)

apex_cc_shared_library(
    name = "converter_test_plugins_shared",
    apex_cc_library = ":converter_test_plugins",
)

ament_pkg_resources(
    name = "ament_resources",
    data = ["//grace/recording/rosbag2/rosbag2_cpp:ament_resources"],
    package = "converter_test_plugins",
    resources = {
        ":converter_test_plugins_shared": "bazel_solib",
        ":package_xml_file": "share",
    },
    visibility = ["//visibility:private"],
)

pkg_files(
    name = "package_xml_file",
    srcs = [":rosbag2_cpp/package.xml"],
    strip_prefix = "rosbag2_cpp",
)

pkg_files(
    name = "plugin_xml_file",
    srcs = [":rosbag2_cpp/converter_test_plugin.xml"],
    strip_prefix = "rosbag2_cpp",
)

ament_pluginlib_resources(
    name = "pluginlib_resources",
    data = [":ament_resources"],
    pluginlib_type = "rosbag2_cpp",
    resources = {
        "converter_test_plugins": ":plugin_xml_file",
    },
    visibility = ["//grace/recording/rosbag2/rosbag2_py/test:__pkg__"],
)

apex_cc_shared_library(
    name = "rosbag2_cpp_shared",
    apex_cc_library = "//grace/recording/rosbag2/rosbag2_cpp",
)

apex_cc_test(
    name = "test_converter_factory",
    srcs = ["rosbag2_cpp/test_converter_factory.cpp"],
    data = [
        ":pluginlib_resources",
    ],
    dynamic_deps = [":rosbag2_cpp_shared"],
    tags = ["skip_asan"],  # FIXME: fix asan findings and remove this tag
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "@console_bridge",
        "@googletest//:gtest_main",
        "@tinyxml2",
    ],
)

cpp_msgs_introspection_library(
    name = "test_msgs_shared",
    add_shared = True,
    ament_runfiles = True,
    msgs = "//grace/interfaces/test_msgs",
)

apex_cc_test(
    name = "test_typesupport_helpers",
    srcs = ["rosbag2_cpp/test_typesupport_helpers.cpp"],
    dynamic_deps = [":test_msgs_shared"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/ros/rcpputils",
        "//tools/ament/ament_index/ament_index_cpp",
        "@console_bridge",
        "@googletest//:gtest_main",
        "@tinyxml2",
    ],
)

apex_cc_test(
    name = "test_info",
    srcs = ["rosbag2_cpp/test_info.cpp"],
    data = [
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    dynamic_deps = [":test_msgs_shared"],
    tags = [
        "skip_asan",  # FIXME: fix asan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        "//grace/interfaces/test_msgs",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_test_common:rosbag2_test_common_cpp",
        "//grace/ros/rcpputils",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_sequential_reader",
    srcs = [
        "rosbag2_cpp/fake_data.cpp",
        "rosbag2_cpp/fake_data.hpp",
        "rosbag2_cpp/mock_converter.hpp",
        "rosbag2_cpp/mock_converter_factory.hpp",
        "rosbag2_cpp/mock_metadata_io.hpp",
        "rosbag2_cpp/mock_storage.hpp",
        "rosbag2_cpp/mock_storage_factory.hpp",
        "rosbag2_cpp/test_sequential_reader.cpp",
    ],
    data = [
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    dynamic_deps = [":test_msgs_shared"],
    tags = [
        "skip_asan",  # FIXME: fix asan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/interfaces/test_msgs",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/recording/rosbag2/rosbag2_test_common:rosbag2_test_common_cpp",
        "//grace/ros/rcpputils",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_storage_without_metadata_file",
    srcs = [
        "rosbag2_cpp/mock_converter.hpp",
        "rosbag2_cpp/mock_converter_factory.hpp",
        "rosbag2_cpp/mock_metadata_io.hpp",
        "rosbag2_cpp/mock_storage.hpp",
        "rosbag2_cpp/mock_storage_factory.hpp",
        "rosbag2_cpp/test_storage_without_metadata_file.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "@console_bridge",
        "@googletest//:gtest_main",
        "@tinyxml2",
    ],
)

apex_cc_test(
    name = "test_local_message_definition_source",
    srcs = ["rosbag2_cpp/test_local_message_definition_source.cpp"],
    copts = ["-std=c++17"],
    data = [
        "//grace/recording/rosbag2/rosbag2_test_msgdefs",
        "//grace/recording/rosbag2/rosbag2_test_msgdefs:ament_resources",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_test_msgdefs",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_message_cache",
    srcs = [
        "rosbag2_cpp/mock_cache_consumer.hpp",
        "rosbag2_cpp/mock_message_cache.hpp",
        "rosbag2_cpp/test_message_cache.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "@console_bridge",
        "@googletest//:gtest_main",
        "@tinyxml2",
    ],
)

apex_cc_test(
    name = "test_circular_message_cache",
    srcs = ["rosbag2_cpp/test_circular_message_cache.cpp"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "@console_bridge",
        "@googletest//:gtest_main",
        "@tinyxml2",
    ],
)

apex_cc_shared_library(
    name = "rosidl_typesupport_introspection_cpp_shared",
    apex_cc_library = "//grace/rosidl/rosidl_typesupport_introspection_cpp",
)

apex_cc_test(
    name = "test_ros2_message",
    srcs = [
        "rosbag2_cpp/types/test_ros2_message.cpp",
    ],
    dynamic_deps = [
        ":test_msgs_shared",
        ":rosidl_typesupport_introspection_cpp_shared",
    ],
    deps = [
        "//grace/interfaces/test_msgs",
        "//grace/interfaces/test_msgs:fixtures",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/ros/rcpputils",
        "//grace/rosidl/rosidl_runtime_cpp",
        "//grace/rosidl/rosidl_typesupport_cpp",
        "//grace/rosidl/rosidl_typesupport_introspection_cpp",
        "//tools/ament/ament_index/ament_index_cpp",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_sequential_writer",
    srcs = [
        "rosbag2_cpp/fake_data.cpp",
        "rosbag2_cpp/fake_data.hpp",
        "rosbag2_cpp/mock_converter.hpp",
        "rosbag2_cpp/mock_converter_factory.hpp",
        "rosbag2_cpp/mock_metadata_io.hpp",
        "rosbag2_cpp/mock_storage.hpp",
        "rosbag2_cpp/mock_storage_factory.hpp",
        "rosbag2_cpp/test_sequential_writer.cpp",
    ],
    data = [
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    dynamic_deps = [":test_msgs_shared"],
    tags = [
        "skip_asan",  # FIXME: fix asan findings and remove this tag
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
    ],
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_test_common:rosbag2_test_common_cpp",
        "@console_bridge",
        "@googletest//:gtest_main",
        "@tinyxml2",
    ],
)

cpp_msgs_introspection_library(
    name = "std_msgs_shared",
    add_shared = True,
    ament_runfiles = True,
    msgs = "//grace/interfaces/std_msgs",
)

apex_cc_test(
    name = "test_serialization_converter",
    srcs = [
        "rosbag2_cpp/fake_data.cpp",
        "rosbag2_cpp/fake_data.hpp",
        "rosbag2_cpp/mock_converter.hpp",
        "rosbag2_cpp/mock_converter_factory.hpp",
        "rosbag2_cpp/mock_metadata_io.hpp",
        "rosbag2_cpp/mock_storage.hpp",
        "rosbag2_cpp/mock_storage_factory.hpp",
        "rosbag2_cpp/test_serialization_converter.cpp",
    ],
    dynamic_deps = [
        ":std_msgs_shared",
        ":rosidl_typesupport_introspection_cpp_shared",
    ],
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_multifile_reader",
    srcs = [
        "rosbag2_cpp/mock_converter_factory.hpp",
        "rosbag2_cpp/mock_metadata_io.hpp",
        "rosbag2_cpp/mock_storage.hpp",
        "rosbag2_cpp/mock_storage_factory.hpp",
        "rosbag2_cpp/test_multifile_reader.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "@console_bridge",
        "@googletest//:gtest_main",
        "@tinyxml2",
    ],
)

apex_cc_test(
    name = "test_time_controller_clock",
    srcs = ["rosbag2_cpp/test_time_controller_clock.cpp"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = ["constrained_test"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "@console_bridge",
        "@googletest//:gtest_main",
        "@tinyxml2",
    ],
)

apex_cc_test(
    name = "test_service_utils",
    srcs = ["rosbag2_cpp/test_service_utils.cpp"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "//grace/interfaces/test_msgs",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_test_common:rosbag2_test_common_cpp",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_action_utils",
    srcs = ["rosbag2_cpp/test_action_utils.cpp"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_test_common:rosbag2_test_common_cpp",
        "@googletest//:gtest_main",
    ],
)
