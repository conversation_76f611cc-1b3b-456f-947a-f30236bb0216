load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "mcap_pkg",
    cc_libraries = [
        ":mcap",
        "@lz4//:lz4",
    ],
    description = "The MCAP container file format",
    license = "MIT License (MCAP), Apache License 2.0 (src/main.cpp from mcap_vendor)",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "mcap_vendor",
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = [
        "@lz4//:lz4_pkg",
        "@zstd//:zstd_pkg",
    ],
)

apex_cc_library(
    name = "mcap",
    srcs = ["src/main.cpp"],
    hdrs = glob([
        "mcap-src/cpp/mcap/include/mcap/*.hpp",
        "mcap-src/cpp/mcap/include/mcap/*.inl",
    ]),
    copts = ["-std=c++17"],
    strip_include_prefix = "mcap-src/cpp/mcap/include",
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        "@lz4",
        "@zstd",
    ],
)

# Only the main library is bazelized intentionally.
# bazelization_report ignore: mcap-src/cpp/bench
# bazelization_report ignore: mcap-src/cpp/docs
# bazelization_report ignore: mcap-src/cpp/examples
# bazelization_report ignore: mcap-src/cpp/mcap/conanfile.py
# bazelization_report ignore: mcap-src/cpp/scripts
# bazelization_report ignore: mcap-src/cpp/test
# bazelization_report ignore: mcap-src/go
# bazelization_report ignore: mcap-src/python
# bazelization_report ignore: mcap-src/rust
# bazelization_report ignore: mcap-src/swift
# bazelization_report ignore: mcap-src/tests
# bazelization_report ignore: mcap-src/typescript
# bazelization_report ignore: mcap-src/website
