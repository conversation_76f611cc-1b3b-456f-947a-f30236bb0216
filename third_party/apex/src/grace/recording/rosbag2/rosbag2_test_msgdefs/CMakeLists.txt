cmake_minimum_required(VERSION 3.8)
project(rosbag2_test_msgdefs)

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(apex_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)

if(QNX)
  message(STATUS "QNX currently not supported, due to the lack of support for C++17")
  skip_on_compiler("QCC")
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

rosidl_generate_interfaces(${PROJECT_NAME}
  "msg/ComplexIdl.idl"
  "msg/BasicIdl.idl"
  "msg/BasicMsg.msg"
  "msg/ComplexMsg.msg"
  "msg/ComplexMsgDependsOnIdl.msg"
  "srv/BasicSrv.srv"
  "srv/ComplexSrvMsg.srv"
  "srv/ComplexSrvIdl.srv"
  "action/BasicAction.action"
  "action/ComplexActionMsg.action"
  "action/ComplexActionIdl.action"
#  ADD_LINTER_TESTS
)

ament_package()
