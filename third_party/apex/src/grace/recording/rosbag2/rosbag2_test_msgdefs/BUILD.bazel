load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "ROSIDL_COMMON_PKGS")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")

ros_pkg(
    name = "rosbag2_test_msgdefs_pkg",
    description = "message definition test fixtures for MCAP schema recording",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    msg_libraries = [":rosbag2_test_msgdefs"],
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = ROSIDL_COMMON_PKGS,
)

msgs_library(
    name = "rosbag2_test_msgdefs",
    srcs = glob([
        "msg/*.idl",
        "msg/*.msg",
        "srv/*.srv",
        "action/*.action",
    ]),
    ament_runfiles = True,
    # TODO(#31491): Add support for msg with IDL dependencies when using rosmsg_srcs
    visibility = ["//visibility:public"],
    wheel_data = ":rosbag2_test_msgdefs_pkg.wheel_data",
)

# Todo: #32755 These files, if needed should be provided by the msgs_library
# Discuss, why these files are needed in the test //grace/recording/rosbag2/rosbag2_py/test/...
ament_pkg_resources(
    name = "ament_resources",
    package = "rosbag2_test_msgdefs",
    resources = {
        "srv/BasicSrv.srv": "share",
        "srv/ComplexSrvMsg.srv": "share",
        "srv/ComplexSrvIdl.srv": "share",
        "msg/BasicMsg.msg": "share",
        "msg/ComplexMsg.msg": "share",
        "msg/ComplexMsgDependsOnIdl.msg": "share",
        "action/BasicAction.action": "share",
        "action/ComplexActionIdl.action": "share",
        "action/ComplexActionMsg.action": "share",
    },
    skip_package_resource = True,
    visibility = ["//visibility:public"],
)
