load("@python_vendor//:requirements.bzl", "requirement")
load("//common/bazel/rules_deployment:defs.bzl", "configured_tool", "executables_collection")
load("//common/bazel/rules_pytest:defs.bzl", "apex_py_test")
load("//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library")
load("//tools/launch/launch_testing/rules:defs.bzl", "launch_test")

# We don't use Python ament_* tests with <PERSON><PERSON>, at least not currently
# bazelization_report ignore: test_copyright.py
# bazelization_report ignore: test_flake8.py
# bazelization_report ignore: test_pep257.py

filegroup(
    name = "test_resources",
    testonly = True,
    srcs = glob([
        "resources/**/*.yaml",
        "resources/**/*.db3",
    ]),
)

cpp_msgs_introspection_library(
    name = "rosbag2_interfaces_msgs",
    ament_runfiles = True,
    msgs = "//grace/recording/rosbag2/rosbag2_interfaces",
)

# ros2 cli target under test - only include the relevant rosbag2 plugins here
configured_tool(
    name = "ros2",
    testonly = True,
    data = [
        ":rosbag2_interfaces_msgs",
        "//grace/recording/rosbag2/rosbag2_compression_zstd:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    framework = "Apex.Grace",
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    visibility = ["//visibility:private"],
    deps = [
        "//grace/cli:base_cli",
        "//grace/recording/rosbag2/ros2bag",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:ros2bag_mcap_cli",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:ros2bag_sqlite3_cli",
    ],
)

executables_collection(
    name = "executables",
    testonly = True,
    executables = [
        ":ros2",
        "@python_interpreter//:python3",
    ],
    target_compatible_with = [
        "@platforms//os:linux",
    ],
)

launch_test(
    name = "test_play_qos_profiles",
    data = [
        ":executables",
        ":test_resources",
    ],
    launch_test_file = "test_play_qos_profiles.py",
    ros_domain_id_isolation = True,
    tags = ["constrained_test"],
    deps = [
        requirement("pytest"),
        "//grace/recording/rosbag2/ros2bag",
        "//grace/recording/rosbag2/rosbag2_test_common",
        "//grace/tools/launch_ros/launch_testing_ros",
        "//tools/apex_pytest_utils",
    ],
)

launch_test(
    name = "test_api",
    data = [
        ":executables",
        ":test_resources",
    ],
    launch_test_file = "test_api.py",
    ros_domain_id_isolation = True,
    tags = ["constrained_test"],
    deps = [
        requirement("pytest"),
        "//grace/recording/rosbag2/ros2bag",
        "//grace/recording/rosbag2/rosbag2_test_common",
        "//grace/tools/launch_ros/launch_testing_ros",
        "//tools/apex_pytest_utils",
    ],
)

launch_test(
    name = "test_burst",
    data = [
        ":executables",
        ":test_resources",
    ],
    launch_test_file = "test_burst.py",
    ros_domain_id_isolation = True,
    tags = [
        "constrained_test",
    ],
    deps = [
        requirement("pytest"),
        "//grace/recording/rosbag2/ros2bag",
        "//grace/tools/launch_ros/launch_ros",
        "//tools/apex_pytest_utils",
    ],
)

launch_test(
    name = "test_cli_extension",
    data = [
        ":executables",
        ":test_resources",
    ],
    launch_test_file = "test_cli_extension.py",
    local = True,  # Avoid BRE (Bazel Remote Execution)
    ros_domain_id_isolation = True,
    tags = [
        "constrained_test",
        "exclusive",  # The test fails when run in parallel with other tests
    ],
    deps = [
        requirement("pytest"),
        "//grace/recording/rosbag2/ros2bag",
        "//grace/recording/rosbag2/rosbag2_test_common",
        "//grace/tools/launch_ros/launch_ros",
        "//grace/tools/launch_ros/launch_testing_ros",
        "//tools/apex_pytest_utils",
    ],
)

launch_test(
    name = "test_info",
    data = [
        ":executables",
        ":test_resources",
    ],
    launch_test_file = "test_info.py",
    ros_domain_id_isolation = True,
    tags = [
        "constrained_test",
        "exclusive",  # The test fails when run in parallel with other tests
    ],
    deps = [
        requirement("pytest"),
        "//grace/recording/rosbag2/ros2bag",
        "//grace/tools/launch_ros/launch_ros",
        "//tools/apex_pytest_utils",
    ],
)

launch_test(
    name = "test_record",
    data = [
        ":executables",
        ":test_resources",
    ],
    launch_test_file = "test_record.py",
    local = True,  # TODO(): failing in RBE (remote bazel env)
    ros_domain_id_isolation = True,
    tags = ["constrained_test"],
    deps = [
        requirement("pytest"),
        "//grace/recording/rosbag2/ros2bag",
        "//grace/recording/rosbag2/rosbag2_test_common",
        "//tools/apex_pytest_utils",
    ],
)

launch_test(
    name = "test_record_qos_profiles",
    data = [
        ":executables",
        ":test_resources",
    ],
    launch_test_file = "test_record_qos_profiles.py",
    local = True,  # TODO(): failing in RBE (remote bazel env)
    ros_domain_id_isolation = True,
    tags = ["constrained_test"],
    deps = [
        requirement("pytest"),
        "//grace/recording/rosbag2/ros2bag",
        "//grace/tools/launch_ros/launch_ros",
        "//tools/apex_pytest_utils",
    ],
)

launch_test(
    name = "test_record_with_compression_thread_priority",
    data = [
        ":executables",
        ":test_resources",
    ],
    launch_test_file = "test_record_with_compression_thread_priority.py",
    ros_domain_id_isolation = True,
    tags = [
        "constrained_test",
        "skip_rbe",  # The test fails during remote execution because it can't find libpython3.8.so
    ],
    deps = [
        requirement("pytest"),
        "//grace/recording/rosbag2/ros2bag",
        "//grace/tools/launch_ros/launch_ros",
        "//tools/apex_pytest_utils",
    ],
)

apex_py_test(
    name = "test_recorder_args_parser",
    srcs = ["test_recorder_args_parser.py"],
    data = [":executables"],
    deps = [
        "//grace/recording/rosbag2/ros2bag",
    ],
)
