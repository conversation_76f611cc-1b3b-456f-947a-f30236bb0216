<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format2.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="2">
  <name>ros2bag</name>
  <version>0.33.0</version>
  <description>
    Entry point for rosbag in ROS 2
  </description>
  <maintainer email="micha<PERSON>.<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>">ROS Tooling Working Group</maintainer>
  <license>Apache License 2.0</license>

  <buildtool_depend>ament_cmake_python</buildtool_depend>
  <buildtool_depend>ament_cmake</buildtool_depend>

  <exec_depend>ament_index_python</exec_depend>
  <exec_depend>python3-yaml</exec_depend>
  <exec_depend>rclpy</exec_depend>
  <exec_depend>ros2cli</exec_depend>
  <exec_depend>rosbag2_py</exec_depend>

  <test_depend>ament_cmake_pytest</test_depend>
  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>ament_xmllint</test_depend>
  <test_depend>launch_testing</test_depend>
  <test_depend>launch_testing_ament_cmake</test_depend>
  <test_depend>launch_testing_ros</test_depend>
  <test_depend>python3-pytest</test_depend>
  <test_depend>rosbag2_storage_default_plugins</test_depend>
  <test_depend>rosbag2_test_common</test_depend>

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
