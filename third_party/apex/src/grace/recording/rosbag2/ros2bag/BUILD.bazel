load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("@python_vendor//:requirements.bzl", "requirement")
load("//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ENTRY_POINTS = {
    "ros2cli.command": [
        "bag = ros2bag.command.bag:BagCommand",
    ],
    "ros2cli.extension_point": [
        "ros2bag.verb = ros2bag.verb:VerbExtension",
    ],
    "ros2bag.verb": [
        "burst = ros2bag.verb.burst:BurstVerb",
        "convert = ros2bag.verb.convert:ConvertVerb",
        "info = ros2bag.verb.info:InfoVerb",
        "list = ros2bag.verb.list:ListVerb",
        "play = ros2bag.verb.play:PlayVerb",
        "record = ros2bag.verb.record:RecordVerb",
        "reindex = ros2bag.verb.reindex:ReindexVerb",
    ],
}

ros_pkg(
    name = "ros2bag_pkg",
    description = "Entry point for rosbag in ROS 2",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "ros2bag",
    py_libraries = [":ros2bag"],
    version = "0.33.0",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Environment :: Console",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: Apache Software License",
        "Programming Language :: Python",
    ],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "//grace/cli/ros2cli:ros2cli_pkg",
        "//grace/recording/rosbag2/rosbag2_py:rosbag2_py_pkg",
        "//grace/recording/rosbag2/rosbag2_transport:rosbag2_transport_pkg",
        "//grace/ros/rclpy/rclpy:rclpy_pkg",
        "//tools/ament/ament_index/ament_index_python:ament_index_python_pkg",
    ],
)

py_entry_points_library(
    name = "ros2bag",
    srcs = [
        "ros2bag/__init__.py",
        "ros2bag/api/__init__.py",
        "ros2bag/command/__init__.py",
        "ros2bag/command/bag.py",
        "ros2bag/verb/__init__.py",
        "ros2bag/verb/burst.py",
        "ros2bag/verb/convert.py",
        "ros2bag/verb/info.py",
        "ros2bag/verb/list.py",
        "ros2bag/verb/play.py",
        "ros2bag/verb/record.py",
        "ros2bag/verb/reindex.py",
    ],
    data = [
        ":ros2bag_pkg.wheel_data",
    ],
    entry_points = ENTRY_POINTS,
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        requirement("pyyaml"),
        "//grace/cli/ros2cli",
        "//grace/recording/rosbag2/rosbag2_py",
        "//grace/ros/rclpy/rclpy",
        "//tools/ament/ament_index/ament_index_python",
    ],
)
