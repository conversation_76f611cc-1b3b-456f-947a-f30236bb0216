load("@rules_pkg//pkg:mappings.bzl", "pkg_files")
load("//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("//tools/ament/rules_ament:defs.bzl", "ament_pluginlib_resources")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "test_plugin_pkg",
    cc_libraries = [":test_plugin"],
    description = "Test package with plugins for rosbag2_storage",
    license = "Apex.AI license",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "test_plugin",
    pluginlib_description_files = {"rosbag2_storage/test_plugin.xml": "rosbag2_storage"},
    tags = ["skip_bst"],
    version = "0.33.0",
    visibility = ["//grace/recording/rosbag2:__subpackages__"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_storage:rosbag2_storage_pkg",
        "//grace/tools/pluginlib/pluginlib:pluginlib_pkg",
    ],
)

apex_cc_library(
    name = "test_plugin",
    srcs = [
        "rosbag2_storage/test_constants.hpp",
        "rosbag2_storage/test_plugin.cpp",
        "rosbag2_storage/test_plugin.hpp",
        "rosbag2_storage/test_read_only_plugin.cpp",
        "rosbag2_storage/test_read_only_plugin.hpp",
    ],
    tags = [
        "Use short library name in install space",
        "exclude_sca",  # exclude Axivion SCA
    ],
    visibility = ["//visibility:private"],
    deps = [
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/tools/pluginlib/pluginlib",
    ],
)

apex_cc_shared_library(
    name = "test_plugin_shared",
    apex_cc_library = ":test_plugin",
)

ament_pkg_resources(
    name = "ament_resources",
    data = ["//grace/recording/rosbag2/rosbag2_storage:ament_resources"],
    package = "test_plugin",
    resources = {
        ":test_plugin_shared": "bazel_solib",
        "//grace/recording/rosbag2/rosbag2_storage:package_xml_file": "share",
    },
    visibility = ["//visibility:private"],
)

pkg_files(
    name = "plugin_xml_file",
    srcs = [":rosbag2_storage/test_plugin.xml"],
    strip_prefix = "rosbag2_storage",
)

ament_pluginlib_resources(
    name = "pluginlib_resources",
    data = [":ament_resources"],
    pluginlib_type = "rosbag2_storage",
    resources = {
        "test_plugin": ":plugin_xml_file",
    },
    visibility = ["//grace/recording/rosbag2/rosbag2_py/test:__pkg__"],
)

apex_cc_shared_library(
    name = "rosbag2_storage_shared",
    apex_cc_library = "//grace/recording/rosbag2/rosbag2_storage",
)

apex_cc_test(
    name = "test_storage_factory",
    srcs = [
        "rosbag2_storage/test_constants.hpp",
        "rosbag2_storage/test_plugin.hpp",
        "rosbag2_storage/test_storage_factory.cpp",
    ],
    data = [":pluginlib_resources"],
    dynamic_deps = [":rosbag2_storage_shared"],
    tags = ["skip_asan"],  # FIXME: fix asan findings and remove this tag
    deps = [
        "//grace/recording/rosbag2/rosbag2_storage",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_ros_helper",
    srcs = ["rosbag2_storage/test_ros_helper.cpp"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "//grace/recording/rosbag2/rosbag2_storage",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_metadata_serialization",
    srcs = ["rosbag2_storage/test_metadata_serialization.cpp"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/recording/rosbag2/rosbag2_test_common:rosbag2_test_common_cpp",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_storage_options",
    srcs = ["rosbag2_storage/test_storage_options.cpp"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "//grace/recording/rosbag2/rosbag2_storage",
        "//grace/recording/rosbag2/rosbag2_test_common:rosbag2_test_common_cpp",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_qos",
    srcs = ["rosbag2_storage/test_qos.cpp"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "//grace/recording/rosbag2/rosbag2_storage",
        "@googletest//:gtest_main",
    ],
)
