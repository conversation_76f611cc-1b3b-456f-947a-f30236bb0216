load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_pkg//pkg:mappings.bzl", "pkg_files")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")

ros_pkg(
    name = "rosbag2_storage_pkg",
    cc_libraries = [
        ":rosbag2_storage",
    ],
    description = "ROS2 independent storage format to store serialized ROS2 messages",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/ros/rmw:rmw_pkg",
        "//grace/tools/pluginlib/pluginlib:pluginlib_pkg",
    ],
)

pkg_files(
    name = "package_xml_file",
    srcs = ["package.xml"],
    visibility = [":__subpackages__"],
)

ament_pkg_resources(
    name = "ament_resources",
    package = "rosbag2_storage",
    resources = {
        ":package_xml_file": "share",
        ":rosbag2_storage_shared": "bazel_solib",
    },
    visibility = ["//visibility:public"],
)

apex_cc_library(
    name = "rosbag2_storage",
    srcs = [
        "src/rosbag2_storage/base_io_interface.cpp",
        "src/rosbag2_storage/default_storage_id.cpp",
        "src/rosbag2_storage/impl/storage_factory_impl.hpp",
        "src/rosbag2_storage/metadata_io.cpp",
        "src/rosbag2_storage/qos.cpp",
        "src/rosbag2_storage/ros_helper.cpp",
        "src/rosbag2_storage/storage_factory.cpp",
        "src/rosbag2_storage/storage_options.cpp",
    ],
    hdrs = glob(["include/rosbag2_storage/**/*.hpp"]),
    copts = ["-std=c++17"],
    shared_lib_name = "librosbag2_storage.so",
    strip_include_prefix = "include",
    tags = ["exclude_sca"],  # exclude Axivion SCA
    target_compatible_with = ["@platforms//os:linux"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rmw",
        "//grace/tools/pluginlib/pluginlib",
    ],
)

apex_cc_shared_library(
    name = "rosbag2_storage_shared",
    apex_cc_library = ":rosbag2_storage",
)
