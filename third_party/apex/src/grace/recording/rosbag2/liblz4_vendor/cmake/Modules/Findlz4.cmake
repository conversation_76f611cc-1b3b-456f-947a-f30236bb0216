# Get package location hint from environment variable (if any)
if(NOT lz4_ROOT_DIR AND DEFINED ENV{lz4_ROOT_DIR})
  set(lz4_ROOT_DIR "$ENV{lz4_ROOT_DIR}" CACHE PATH
      "lz4 base directory location (optional, used for nonstandard installation paths)")
endif()

# When using lz4 from conda on Windows, and cmake before 3.29.0,
# we need to add the library prefix "lib" so that the library can
# be successfully found.
if(MSVC AND CMAKE_VERSION VERSION_LESS "3.29.0")
  set(CMAKE_FIND_LIBRARY_PREFIXES "" "lib")
endif()

# Search path for nonstandard package locations
if(lz4_ROOT_DIR)
  set(lz4_INCLUDE_PATH PATHS "${lz4_ROOT_DIR}/include" NO_DEFAULT_PATH)
  set(lz4_LIBRARY_PATH PATHS "${lz4_ROOT_DIR}/lib"     NO_DEFAULT_PATH)
else()
  set(lz4_INCLUDE_PATH "")
  set(lz4_LIBRARY_PATH "")
endif()

# Find headers and libraries
find_path(lz4_INCLUDE_DIR NAMES lz4.h PATH_SUFFIXES "lz4" ${lz4_INCLUDE_PATH})
find_library(lz4_LIBRARY  NAMES lz4   PATH_SUFFIXES "lz4" ${lz4_LIBRARY_PATH})

if (lz4_INCLUDE_DIR)
  file(STRINGS "${lz4_INCLUDE_DIR}/lz4.h" version-file
    REGEX "#define[ \t]+LZ4_VERSION_(MAJOR|MINOR|RELEASE).*")
  list(GET version-file 0 major-line)
  list(GET version-file 1 minor-line)
  list(GET version-file 2 release-line)
  string(REGEX REPLACE "^#define[ \t]+LZ4_VERSION_MAJOR[ \t]+([0-9]+).*$" "\\1" LZ4_VERSION_MAJOR ${major-line})
  string(REGEX REPLACE "^#define[ \t]+LZ4_VERSION_MINOR[ \t]+([0-9]+).*$" "\\1" LZ4_VERSION_MINOR ${minor-line})
  string(REGEX REPLACE "^#define[ \t]+LZ4_VERSION_RELEASE[ \t]+([0-9]+).*$" "\\1" LZ4_VERSION_RELEASE ${release-line})
  set(lz4_VERSION ${LZ4_VERSION_MAJOR}.${LZ4_VERSION_MINOR}.${LZ4_VERSION_RELEASE})
endif()

mark_as_advanced(lz4_INCLUDE_DIR lz4_LIBRARY)

# Output variables generation
include(FindPackageHandleStandardArgs)
find_package_handle_standard_args(lz4 REQUIRED_VARS lz4_LIBRARY lz4_INCLUDE_DIR
  VERSION_VAR lz4_VERSION)

set(lz4_FOUND ${LZ4_FOUND}) # Enforce case-correctness: Set appropriately cased variable...
unset(LZ4_FOUND) # ...and unset uppercase variable generated by find_package_handle_standard_args

if(lz4_FOUND)
  set(lz4_INCLUDE_DIRS ${lz4_INCLUDE_DIR})
  set(lz4_LIBRARIES ${lz4_LIBRARY})

  if(NOT TARGET LZ4::lz4)
    add_library(LZ4::lz4 UNKNOWN IMPORTED)
    set_property(TARGET LZ4::lz4 PROPERTY IMPORTED_LOCATION ${lz4_LIBRARY})
    set_property(TARGET LZ4::lz4 PROPERTY INTERFACE_INCLUDE_DIRECTORIES ${lz4_INCLUDE_DIR})
  endif()
  list(APPEND lz4_TARGETS LZ4::lz4)
elseif(lz4_FIND_REQUIRED)
  message(FATAL_ERROR "Unable to find lz4")
endif()
