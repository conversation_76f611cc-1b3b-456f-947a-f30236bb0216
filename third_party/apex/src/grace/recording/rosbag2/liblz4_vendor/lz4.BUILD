load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//grace/recording/rosbag2/liblz4_vendor:defs.bzl", "VERSION")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "lz4_pkg",
    cc_libraries = [":lz4"],
    description = "The lz4 compression library",
    license = "BSD 2-Clause license",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "lz4_vendor",
    version = VERSION.version,
    visibility = ["//visibility:public"],
    deps = [],
)

apex_cc_library(
    name = "lz4",
    srcs = glob(["lib/*.c"]),
    hdrs = glob([
        "lib/*.h",
        "lib/lz4.c",
    ]),
    strip_include_prefix = "lib",
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
)
