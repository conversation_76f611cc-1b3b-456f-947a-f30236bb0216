^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Changelog for package rosbag2_examples_py
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

0.33.0 (2025-04-25)
-------------------
* Upstream quality changes from Apex.AI part-2 (`#1924 <https://github.com/ros2/rosbag2/issues/1924>`_)
* Add a simple example showing how to convert bags to the csv file (`#1974 <https://github.com/ros2/rosbag2/issues/1974>`_)
* Contributors: <PERSON>, <PERSON>

0.32.0 (2025-04-18)
-------------------

0.31.0 (2025-02-02)
-------------------
* avoid using internal modules for examples. (`#1905 <https://github.com/ros2/rosbag2/issues/1905>`_)
* Add test_xmllint.py to python packages. (`#1879 <https://github.com/ros2/rosbag2/issues/1879>`_)
* Contributors: <PERSON> <PERSON>ance<PERSON>, <PERSON>oya <PERSON>ta

0.30.0 (2024-11-26)
-------------------

0.29.0 (2024-09-03)
-------------------
* simple_bag_reader.py should publish the data for each timer callback. (`#1767 <https://github.com/ros2/rosbag2/issues/1767>`_)
* Change the python examples to use the rclpy context manager. (`#1758 <https://github.com/ros2/rosbag2/issues/1758>`_)
* Add rosbag2_examples_cpp/simple_bag_reader.cpp. (`#1683 <https://github.com/ros2/rosbag2/issues/1683>`_)
* Contributors: Chris Lalancette, Tomoya Fujita

0.28.0 (2024-06-17)
-------------------

0.27.0 (2024-04-30)
-------------------

0.26.1 (2024-04-17)
-------------------

0.26.0 (2024-04-16)
-------------------

0.25.0 (2024-03-27)
-------------------
* Add topic_id returned by storage to the TopicMetadata (`#1538 <https://github.com/ros2/rosbag2/issues/1538>`_)
* Contributors: Michael Orlov

0.24.0 (2023-07-11)
-------------------
* Fix a warning from python setuptools. (`#1312 <https://github.com/ros2/rosbag2/issues/1312>`_)
* Contributors: Chris Lalancette

0.23.0 (2023-04-28)
-------------------

0.22.0 (2023-04-18)
-------------------

0.21.0 (2023-04-12)
-------------------
* Add API samples for Python [rebased] (`#1253 <https://github.com/ros2/rosbag2/issues/1253>`_)
  * Add API samples for Python
  * Package Renaming and Move
  * linting + copyright
  * more linting
  ---------
  Co-authored-by: Geoffrey Biggs <<EMAIL>>
* Contributors: David V. Lu!!
