load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_python//python:defs.bzl", "py_binary")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "rosbag2_examples_py_pkg",
    description = "Python bag writing tutorial.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "rosbag2_examples_py",
    version = "0.33.0",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/examples/example_interfaces:example_interfaces_pkg",
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/recording/rosbag2/rosbag2_py:rosbag2_py_pkg",
        "//grace/ros/rclpy/rclpy:rclpy_pkg",
    ],
)

py_msgs_library(
    name = "example_interfaces_py",
    msgs = "//grace/examples/example_interfaces",
)

msgs_library(
    name = "_std_msgs",
    ament_runfiles = True,
    deps = ["//grace/interfaces/std_msgs"],
)

cpp_msgs_introspection_library(
    name = "_std_msgs_introspection",
    ament_runfiles = True,
    msgs = ":_std_msgs",
)

py_msgs_library(
    name = "std_msgs_py",
    msgs = ":_std_msgs",
)

py_binary(
    name = "data_generator_executable",
    srcs = ["rosbag2_examples_py/data_generator_executable.py"],
    data = [
        "//grace/examples/example_interfaces",
        "//grace/recording/rosbag2/rosbag2_storage:ament_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    main = "rosbag2_examples_py/data_generator_executable.py",
    visibility = ["//visibility:public"],
    deps = [
        ":example_interfaces_py",
        "//grace/recording/rosbag2/rosbag2_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

py_binary(
    name = "data_generator_node",
    srcs = ["rosbag2_examples_py/data_generator_node.py"],
    data = [
        "//grace/examples/example_interfaces",
        "//grace/recording/rosbag2/rosbag2_storage:ament_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    main = "rosbag2_examples_py/data_generator_node.py",
    visibility = ["//visibility:public"],
    deps = [
        ":example_interfaces_py",
        "//grace/recording/rosbag2/rosbag2_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

py_binary(
    name = "simple_bag_recorder_node",
    srcs = ["rosbag2_examples_py/simple_bag_recorder.py"],
    data = [
        ":_std_msgs_introspection",
        "//grace/recording/rosbag2/rosbag2_storage:ament_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    main = "rosbag2_examples_py/simple_bag_recorder.py",
    visibility = ["//visibility:public"],
    deps = [
        ":std_msgs_py",
        "//grace/recording/rosbag2/rosbag2_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

py_binary(
    name = "simple_bag_reader_node",
    srcs = ["rosbag2_examples_py/simple_bag_reader.py"],
    data = [
        ":_std_msgs_introspection",
        "//grace/recording/rosbag2/rosbag2_storage:ament_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    main = "rosbag2_examples_py/simple_bag_reader.py",
    visibility = ["//visibility:public"],
    deps = [
        ":std_msgs_py",
        "//grace/recording/rosbag2/rosbag2_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

py_binary(
    name = "rosbag2csv",
    srcs = ["rosbag2_examples_py/rosbag2csv.py"],
    data = [
        # Add your autogenerated messages ament resources here to be visible to the converter
        "//grace/examples/example_interfaces",
        ":_std_msgs",
        "//grace/recording/rosbag2/rosbag2_storage:ament_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    main = "rosbag2_examples_py/rosbag2csv.py",
    visibility = ["//visibility:public"],
    deps = [
        ":example_interfaces_py",
        ":std_msgs_py",
        # Add your messages py library here as dependencies
        "//grace/recording/rosbag2/rosbag2_py",
        "//grace/ros/rclpy/rclpy",
        "@apex//grace/rosidl/rosidl_runtime_py",
    ],
)
