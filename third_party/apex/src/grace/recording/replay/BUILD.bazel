load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_binary")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_pkg//:mappings.bzl", "pkg_files")

ros_pkg(
    name = "replay_pkg",
    cc_libraries = [":replay"],
    description = "Package containing replay facilities",
    lib_executables = select({
        "@platforms//os:linux": [
            "replay_coordinator",
            "replay_control",
        ],
        "//conditions:default": [],
    }),
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    # TODO(carlos): move pkg_files to testing ros_pkg
    share_data = [":share_data"],
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/containers:containers_pkg",
        "//common/cpputils:cpputils_pkg",
        "//common/threading:threading_pkg",
        "//grace/execution/timer_service:timer_service_pkg",
        "//grace/interfaces/replay_msgs:replay_msgs_pkg",
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/utils/apexcpp:apexcpp_pkg",
    ] + select({
        "@platforms//os:linux": [
            "//grace/recording/rosbag2/rosbag2_cpp:rosbag2_cpp_pkg",
            "//grace/recording/rosbag2/rosbag2_transport:rosbag2_transport_pkg",
        ],
        "//conditions:default": [],
    }),
)

ament_pkg_resources(
    name = "ament_resources",
    package = "replay",
    resources = {
        ":package.xml": "share",
        ":share_data": "share",
    },
)

apex_cc_library(
    name = "replay",
    srcs = glob(
        ["src/**"],
        exclude = [
            "src/coordinator_main.cpp",
            "src/coordinator_data_source_rosbag.cpp",
            "src/replay_control.cpp",
        ],
    ),
    hdrs = glob(
        ["include/**"],
        exclude = [
            "include/replay/coordinator_data_source_rosbag.hpp",
        ],
    ),
    strip_include_prefix = "include",
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        "//common/containers",
        "//common/cpputils",
        "//common/threading",
        "//grace/execution/timer_service",
        "//grace/interfaces/replay_msgs",
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

apex_cc_library(
    name = "replay_data_source_rosbag_lib",
    srcs = ["src/coordinator_data_source_rosbag.cpp"],
    hdrs = ["include/replay/coordinator_data_source_rosbag.hpp"],
    strip_include_prefix = "include",
    tags = ["exclude_sca"],
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    deps = [
        ":replay",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/recording/rosbag2/rosbag2_transport:player",
    ],
)

cc_binary(
    name = "replay_coordinator_exe",
    srcs = ["src/coordinator_main.cpp"],
    tags = ["exclude_sca"],
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":replay",
        ":replay_data_source_rosbag_lib",
        "@boost//:program_options",
    ],
)

cc_binary(
    name = "replay_control",
    srcs = ["src/replay_control.cpp"],
    tags = ["exclude_sca"],
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":replay",
    ],
)

msgs_library(
    name = "interfaces_with_ament_resources",
    ament_runfiles = True,
    deps = ["//grace/interfaces"],
)

py_msgs_library(
    name = "py_interfaces",
    msgs = ":interfaces_with_ament_resources",
)

cpp_msgs_introspection_library(
    name = "cpp_interfaces",
    ament_runfiles = True,
    msgs = ":interfaces_with_ament_resources",
)

configured_binary(
    name = "replay_coordinator",
    data = [
        ":cpp_interfaces",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    executable = ":replay_coordinator_exe",
    visibility = ["//visibility:public"],
    deps = [
        ":py_interfaces",
        "@apex//grace/recording/rosbag2/rosbag2_storage_mcap:ros2bag_mcap_cli",
        "@apex//grace/recording/rosbag2/rosbag2_storage_sqlite3:ros2bag_sqlite3_cli",
    ],
)

apex_cc_test(
    name = "test_replay_misc",
    srcs = [
        "test/test_messenger.cpp",
        "test/test_messenger_doc.cpp",
        "test/test_replay_timer_service.cpp",
        "test/test_topology_node_graph.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "constrained_test",
        "exclusive",
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        ":replay",
        "//common/cpputils",
        "//common/threading",
        "//grace/interfaces/std_msgs",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_replay_coordinator",
    timeout = "moderate",
    srcs = [
        "test/coordinator_data_source_mock.hpp",
        "test/test_coordinator.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "constrained_test",
        "exclusive",
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        ":replay",
        "//common/cpputils",
        "//common/threading",
        "//grace/interfaces/std_msgs",
        "@googletest//:gtest_main",
    ],
)

# TODO(carlos): move pkg_files to testing ros_pkg
pkg_files(
    name = "share_data",
    srcs = glob([
        "test/**/*.yaml",
        "test/**/*.db3",
    ]),
    prefix = "rosbag2_deterministic_replay_test",
)

ros_pkg(
    name = "replay_data_source_rosbag_lib_pkg_for_testing",
    cc_libraries = [":replay_data_source_rosbag_lib"],
    description = "Test package",
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "replay_data_source_rosbag_lib_pkg_for_testing",
    tags = ["skip_bst"],
    version = "0.0.1",
    deps = [
        ":replay_pkg",
        "//grace/recording/rosbag2/rosbag2_cpp:rosbag2_cpp_pkg",
        "//grace/recording/rosbag2/rosbag2_transport:rosbag2_transport_pkg",
    ],
)

apex_cc_shared_library(
    name = "rosbag2_storage_mcap_shared",
    apex_cc_library = "//grace/recording/rosbag2/rosbag2_storage_mcap",
)

apex_cc_shared_library(
    name = "rosbag2_storage_sqlite3_shared",
    apex_cc_library = "//grace/recording/rosbag2/rosbag2_storage_sqlite3",
)

apex_cc_test(
    name = "test_replay_rosbag",
    srcs = ["test/test_data_source_rosbag.cpp"],
    data = [
        ":ament_resources",
        ":share_data",
        "//grace/interfaces/std_msgs:std_msgs_ament_resources",
        "//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ],
    dynamic_deps = [
        ":rosbag2_storage_mcap_shared",
        ":rosbag2_storage_sqlite3_shared",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
    ],
    deps = [
        ":replay_data_source_rosbag_lib",
        "//grace/recording/rosbag2/rosbag2_cpp",
        "//grace/tools/ros_domain_coordinator:gtest_main",
        "//tools/ament/ament_index/ament_index_cpp",
    ],
)

filegroup(
    name = "api_files",
    srcs = glob(["include/**/*.hpp"]),
    visibility = [":__subpackages__"],
)

filegroup(
    name = "doc_files",
    srcs = [
        "test/test_messenger_doc.cpp",
    ],
    visibility = [":__subpackages__"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
