// Copyright 2023 Apex.AI, Inc.
// All rights reserved.

#include <timer_service/sim_timer_service.hpp>
#include <msgutils/time_cast.hpp>

#include <cassert>
#include <algorithm>
#include <limits>
#include <string>
#include <utility>

namespace apex
{
namespace timer_service
{

sim_timer_service::sim_timer_service(
  const std::string & node_name,
  const std::string & node_namespace /*= {}*/,
  apex::threading::thread_attributes thread_attrs /*= apex::threading::thread_attributes::build()*/,
  const rclcpp::QoS & clock_qos /*= rclcpp::ClockQoS()*/)
: timer_service{node_name, node_namespace},
  m_worker{[this] {run();}, std::move(thread_attrs)}
{
  m_clock_sub = get_rclcpp_node()
    .create_polling_subscription<rosgraph_msgs::msg::Clock>("/clock", clock_qos,
      rclcpp::SubscriptionOptions());
  (void)m_ws.add(m_stop);
  (void)m_ws.add(m_clock_sub);
  (void)m_ws.add(m_change);
  m_worker.issue();
}

sim_timer_service::sim_timer_service(
  apex::threading::thread_attributes thread_attrs /*= apex::threading::thread_attributes::build()*/,
  const rclcpp::QoS & clock_qos /*= rclcpp::ClockQoS()*/
)
: sim_timer_service{next_default_service_name(), "", std::move(thread_attrs), clock_qos}
{}

sim_timer_service::~sim_timer_service()
{
  stop();  // terminate() is intended in case of exception
}

void sim_timer_service::stop()
{
  if (m_worker.joinable()) {
    m_stop->set_value(true);
    m_worker.join();
  }
}

types::clock::time sim_timer_service::now() const
{
  std::shared_lock<std::shared_timed_mutex> l{m_clock_mutex};
  return types::clock::time{m_now};
}

void sim_timer_service::now(types::clock::time t)
{
  std::unique_lock<std::shared_timed_mutex> l{m_clock_mutex};
  m_now = t.to_time_point<clock_type>();
  m_clock_set = (m_now.time_since_epoch() != interval_type::zero());
  if (m_clock_set) {
    l.unlock();
    m_clock_cv.notify_all();
    m_change->set_value(true);
  }
}

void sim_timer_service::run()
{
  while (true) {
    m_ws.wait();
    if (m_ws[m_stop]) {
      break;
    }
    if (m_ws[m_clock_sub]) {
      std::unique_lock<std::shared_timed_mutex> l{m_clock_mutex};
      m_clock_set = update_clock();
      if (m_clock_set) {
        l.unlock();
        m_clock_cv.notify_all();
      } else {
        continue;
      }
    }

    std::unique_lock<std::mutex> l{m_mutex};
    std::shared_lock<std::shared_timed_mutex> cl{m_clock_mutex};
    if (m_clock_set) {
      while (!m_timers_heap.empty()) {
        const auto timer = heap_pop();
        if (timer->is_expired(m_now)) {
          timer->trigger(*this);
          if (timer->interval() > interval_type::zero()) {
            // A periodic timer -- update the deadline and put it back on the priority queue
            timer->expires_at(timer->expires_at() + timer->interval());
            heap_push(timer);
          } else {
            // The timer was not a periodic one we just let him hang in the air
            // until reschedule() is called
            timer->active(false);
          }
        } else {  //  if (timer->is_expired(m_now))
          heap_push(timer);
          break;
        }  // if (m_now.time_since_epoch() != interval_type::zero())
      }  // while(!m_timers_heap.empty())
    }
  }  // while(true)
}

bool sim_timer_service::update_clock() noexcept
{
  const auto msgs = m_clock_sub->take();
  builtin_interfaces::msg::Time time;
  for (const auto & msg : msgs) {
    if (msg.info().valid()) {
      time = msg.data().clock;
    }
  }
  m_now = apex::chrono::time_point_cast<time_point>(time);
  return m_now.time_since_epoch() != interval_type::zero();
}

types::timer::id sim_timer_service::get_next_timer_id()
{
  if (std::numeric_limits<types::timer::id>::max() == m_id) {
    throw apex::runtime_error{"too many timers"};
  }
  ++m_id;
  return m_id;
}

void sim_timer_service::create_timer_impl(const timer & t)
{
  std::unique_lock<std::mutex> lock{m_mutex};
  (void) add_new_timer(t);
}

void sim_timer_service::create_timer_impl(
  const timer & t,
  const types::clock::time & time,
  types::timer::units interval)
{
  std::unique_lock<std::mutex> lock{m_mutex};
  auto timer = add_new_timer(t, interval);
  timer->expires_at(time.to_time_point<clock_type>());
  heap_push(timer);
  lock.unlock();
  m_change->set_value(true);
}

void sim_timer_service::create_timer_from_now_impl(
  const timer & t,
  types::timer::units time_from_now,
  types::timer::units interval)
{
  create_timer_impl(t, now() + time_from_now, interval);
}

void sim_timer_service::reschedule(
  const timer & t,
  const types::clock::time & time,
  types::timer::units interval)
{
  std::unique_lock<std::mutex> lock{m_mutex};

  const auto iter = m_timers.find(&t);
  assert(iter != m_timers.end());

  iter->second.interval(interval);
  iter->second.expires_at(time.to_time_point<clock_type>());

  // Nothing to do if it's the current timer which is altered: run() will return it to the heap
  if (iter->second.active()) {
    // The timer is currently on the heap waiting to be executed, so the heap has to be rebuild
    std::make_heap(m_timers_heap.begin(), m_timers_heap.end(), heap_comparator{});
  } else {
    // The timer is an expired one so it should be re-pushed to the heap
    heap_push(&iter->second);
  }
  lock.unlock();
  m_change->set_value(true);
}

void sim_timer_service::reschedule_from_now(
  const timer & t,
  types::timer::units time_from_now,
  types::timer::units interval)
{
  return reschedule(t, now() + time_from_now, interval);
}

void sim_timer_service::cancel(const timer & t)
{
  std::unique_lock<std::mutex> lock{m_mutex};

  const auto iter = m_timers.find(&t);
  assert(iter != m_timers.end());
  if (!iter->second.active()) {
    return;
  }

  // A current timer is not on the heap for sure
  const auto new_end = std::remove(m_timers_heap.begin(), m_timers_heap.end(), &iter->second);
  // If the canceled timer was on the heap, re-establish the heap property
  // This can only happen if a timer is canceled before it expires
  if (new_end != m_timers_heap.end()) {
    (void)m_timers_heap.erase(new_end, m_timers_heap.end());
    std::make_heap(m_timers_heap.begin(), m_timers_heap.end(), heap_comparator{});
  }

  iter->second.interval(interval_type::zero());
  iter->second.active(false);
  lock.unlock();
  m_change->set_value(true);
}

bool sim_timer_service::is_scheduled(const timer & t) const
{
  std::unique_lock<std::mutex> lock{m_mutex};
  const auto iter = m_timers.find(&t);
  assert(iter != m_timers.end());
  return iter->second.active();
}

types::timer::units sim_timer_service::interval(const timer & t) const
{
  std::unique_lock<std::mutex> lock{m_mutex};
  const auto iter = m_timers.find(&t);
  assert(iter != m_timers.end());
  return iter->second.interval();
}

sim_timer_service::timer_internal * sim_timer_service::add_new_timer(
  const timer & t,
  interval_type interval /*= interval_type::zero()*/)
{
  const auto timer = &m_timers.emplace(&t, timer_internal{t, interval}).first->second;
  m_timers_heap.reserve(m_timers_heap.size() + 1U);
  return timer;
}

void sim_timer_service::heap_push(sim_timer_service::timer_internal * timer)
{
  assert(m_timers_heap.size() != m_timers.size());
  timer->active(true);
  m_timers_heap.push_back(timer);
  std::push_heap(m_timers_heap.begin(), m_timers_heap.end(), heap_comparator{});
}

sim_timer_service::timer_internal * sim_timer_service::heap_pop()
{
  assert(!m_timers_heap.empty());
  std::pop_heap(m_timers_heap.begin(), m_timers_heap.end(), heap_comparator{});
  const auto timer = m_timers_heap.back();
  m_timers_heap.pop_back();
  return timer;
}

}  // namespace timer_service
}  // namespace apex
