// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include <unistd.h>

#include <timer_service/common.hpp>
#include <timer_service/types.hpp>
#include <cpputils/common_exceptions.hpp>
#include <rclcpp/rclcpp.hpp>

#include <string>
#include <limits>
#include <cstdint>
#include <atomic>
#include "ida/config/default_deployment.hpp"

constexpr size_t KEEP_LAST = apex::config::MAX_IPC_HISTORY_DEPTH;
namespace apex
{
namespace timer_service
{

std::string next_default_service_name()
{
  static const std::string name_prefix = "_apex_tmr_srv_" + std::to_string(::getpid()) + "_";
  static std::atomic<std::int32_t> current{0};
  const auto val = current.fetch_add(1, std::memory_order_relaxed);
  if (val == std::numeric_limits<std::int32_t>::max()) {
    throw apex::runtime_error{"too many service names"};
  }
  return name_prefix + std::to_string(val);
}

std::string create_timer_topic_name(const std::string & node_name, types::timer::id id)
{
  return node_name + std::string{"_idx_"} + std::to_string(id);
}

std::string create_timer_topic_name(const std::string & node_name, const std::string & timer_name)
{
  return node_name + std::string{"_name_"} + timer_name;
}

rclcpp::QoS get_common_timer_qos()
{
  return rclcpp::QoS(rclcpp::KeepLast(KEEP_LAST)).resource_limits_max_allocated_samples(300);
}

rclcpp::PollingSubscription<types::timer::msg>::SharedPtr
create_timer_topic_subscription(
  rclcpp::Node & factory_node,
  const std::string & service_name,
  const std::string & timer_name,
  rclcpp::QoS qos /*= get_common_timer_qos()*/)
{
  return factory_node.create_polling_subscription<types::timer::msg>(
    create_timer_topic_name(service_name, timer_name),
    qos
  );
}

}  // namespace timer_service
}  // namespace apex
