// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include <timer_service/clock_timer_service.hpp>
#include <timer_service/remote_timer_service.hpp>
#include <timer_service/timer_service_server.hpp>

#include <rclcpp/rclcpp.hpp>
#include <rclcpp/dynamic_waitset/waitset.hpp>
#include <cpputils/common_exceptions.hpp>

#include <gtest/gtest.h>

#include <chrono>
#include <memory>
#include <vector>

using apex::timer_service::steady_clock_timer_service;
using apex::timer_service::timer_service_server;
using apex::timer_service::remote_timer_service;
using apex::timer_service::timer_service_ptr;
using apex::timer_service::timer_ptr;
using apex::timer_service::timer_subscription_ptr;
using namespace std::chrono_literals;
namespace types = apex::timer_service::types;
namespace dw = rclcpp::dynamic_waitset;

namespace
{

class test_remote_timer_service : public ::testing::Test
{
protected:
  void SetUp() override
  {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
  }

  void TearDown() override
  {
    rclcpp::shutdown();
  }

  void compare_to(const timer_ptr & tmr, const timer_service_ptr & service)
  {
    ASSERT_NE(tmr, nullptr);
    ASSERT_NE(service, nullptr);
    timer_ptr other_tmr;
    ASSERT_NE(other_tmr = service->get_timer(tmr->id()), nullptr);
    ASSERT_EQ(other_tmr->name(), tmr->name());
    ASSERT_EQ(other_tmr->is_scheduled(), tmr->is_scheduled());
    ASSERT_EQ(other_tmr->interval(), tmr->interval());
    ASSERT_STREQ(other_tmr->to_sub_ptr()->get_topic_name(), tmr->to_sub_ptr()->get_topic_name());
  }
};
}  // namespace

TEST_F(test_remote_timer_service, bad_service) {
  ASSERT_THROW(timer_service_server{nullptr}, apex::invalid_argument);
}

TEST_F(test_remote_timer_service, client_name) {
  auto service = std::make_shared<steady_clock_timer_service>("my_service");
  timer_service_server server{service};
  remote_timer_service proxy{"my_service"};
  ASSERT_STREQ(proxy.get_rclcpp_node().get_name(), "my_service_client");
}

TEST_F(test_remote_timer_service, service_ptr) {
  auto service = std::make_shared<steady_clock_timer_service>("my_service");
  timer_service_server server{service};
  ASSERT_EQ(service, server.get_timer_service());
}

#ifndef APEX_CERT
TEST_F(test_remote_timer_service, create_timer) {
  auto service = std::make_shared<steady_clock_timer_service>("my_service");
  timer_service_server server{service};
  remote_timer_service proxy{"my_service"};
  ASSERT_EQ(service->size(), 0U);
  timer_ptr tmr;
  ASSERT_NO_THROW(tmr = proxy.create_timer());
  ASSERT_EQ(service->size(), 1U);
  ASSERT_FALSE(tmr->is_scheduled());
  ASSERT_EQ(tmr->interval(), types::timer::units::zero());
  ASSERT_EQ(tmr->id(), 1U);
  compare_to(tmr, service);
  ASSERT_NO_THROW(tmr = proxy.create_timer(10s));
  ASSERT_EQ(service->size(), 2U);
  ASSERT_TRUE(tmr->is_scheduled());
  ASSERT_EQ(tmr->interval(), types::timer::units::zero());
  ASSERT_EQ(tmr->id(), 2U);
  compare_to(tmr, service);
  ASSERT_NO_THROW(tmr = proxy.create_timer(10s, 10s));
  ASSERT_EQ(service->size(), 3U);
  ASSERT_TRUE(tmr->is_scheduled());
  ASSERT_EQ(tmr->interval(), 10s);
  ASSERT_EQ(tmr->id(), 3U);
  compare_to(tmr, service);
  ASSERT_NO_THROW(tmr = proxy.create_timer("name"));
  ASSERT_EQ(service->size(), 4U);
  ASSERT_FALSE(tmr->is_scheduled());
  ASSERT_EQ(tmr->interval(), types::timer::units::zero());
  ASSERT_EQ(tmr->id(), 4U);
  compare_to(tmr, service);
  ASSERT_THROW(tmr = proxy.create_timer("name"), apex::runtime_error);
  ASSERT_NO_THROW(tmr = proxy.create_timer("name2", 10s));
  ASSERT_EQ(service->size(), 5U);
  ASSERT_TRUE(tmr->is_scheduled());
  ASSERT_EQ(tmr->interval(), types::timer::units::zero());
  ASSERT_EQ(tmr->id(), 5U);
  compare_to(tmr, service);
  ASSERT_NO_THROW(tmr = proxy.create_timer("name3", 10s, 10s));
  ASSERT_EQ(service->size(), 6U);
  ASSERT_TRUE(tmr->is_scheduled());
  ASSERT_EQ(tmr->interval(), 10s);
  ASSERT_EQ(tmr->id(), 6U);
  compare_to(tmr, service);
}

TEST_F(test_remote_timer_service, get_timer) {
  auto service = std::make_shared<steady_clock_timer_service>("my_service");
  timer_service_server server{service};
  remote_timer_service proxy{"my_service"};
  timer_ptr tmr;
  ASSERT_NO_THROW(tmr = proxy.create_timer());
  ASSERT_EQ(proxy.get_timer(tmr->id()), tmr);
  compare_to(tmr, service);
  ASSERT_NO_THROW(tmr = proxy.create_timer("name"));
  ASSERT_EQ(proxy.get_timer("name"), tmr);
  compare_to(tmr, service);
  ASSERT_NO_THROW(tmr = service->create_timer("name2"));
  ASSERT_EQ(proxy.get_timer(tmr->id()), nullptr);
  ASSERT_EQ(proxy.get_timer("name2"), nullptr);
  compare_to(proxy.get_remote_timer(tmr->id()), service);
  compare_to(proxy.get_remote_timer("name2"), service);
  ASSERT_EQ(proxy.get_timer(tmr->id() + 1), nullptr);
  ASSERT_EQ(proxy.get_timer("non-existent"), nullptr);
  ASSERT_EQ(proxy.get_remote_timer(tmr->id() + 1), nullptr);
  ASSERT_EQ(proxy.get_remote_timer("non-existent"), nullptr);
  ASSERT_EQ(proxy.create_timer()->id(), tmr->id() + 1);
}

TEST_F(test_remote_timer_service, reschedule) {
  auto service = std::make_shared<steady_clock_timer_service>("my_service");
  timer_service_server server{service};
  remote_timer_service proxy{"my_service"};
  timer_ptr tmr;
  ASSERT_NO_THROW(tmr = proxy.create_timer(1s, 1s));
  ASSERT_TRUE(tmr->is_scheduled());
  compare_to(tmr, service);
  ASSERT_NO_THROW(tmr->reschedule_from_now(1s));
  compare_to(tmr, service);
  {
    dw::Waitset ws1{*tmr};
    ASSERT_TRUE(ws1.wait(2s));
    ASSERT_FALSE(tmr->is_scheduled());
    compare_to(tmr, service);
    ASSERT_EQ(tmr->count_and_reset(), 1U);
    ASSERT_NO_THROW(tmr->reschedule_from_now(0ns));
  }
  dw::Waitset ws2{*tmr};
  ASSERT_TRUE(ws2.wait(1s));
  ASSERT_FALSE(tmr->is_scheduled());
  compare_to(tmr, service);
  ASSERT_EQ(tmr->count_and_reset(), 1U);
}

TEST_F(test_remote_timer_service, subscribe) {
  auto service = std::make_shared<steady_clock_timer_service>("my_service");
  auto remote_tmr = service->create_timer("my_timer");
  timer_service_server server{service};
  remote_timer_service proxy{"my_service"};
  timer_subscription_ptr sub1;
  ASSERT_NO_THROW(sub1 = proxy.create_timer_subscription("my_timer"));
  timer_subscription_ptr sub2;
  ASSERT_NO_THROW(sub2 = proxy.create_timer_subscription(remote_tmr->id()));
  timer_subscription_ptr sub3;
  ASSERT_NO_THROW(sub3 = proxy.create_timer_subscription(remote_tmr));
  ASSERT_EQ(sub1->name(), "my_timer");
  ASSERT_EQ(sub2->name(), "my_timer");
  ASSERT_EQ(sub3->name(), "my_timer");
  ASSERT_NE(proxy.get_timer("my_timer"), nullptr);
  ASSERT_NE(proxy.get_timer(remote_tmr->id()), nullptr);
  ASSERT_EQ(proxy.get_timer("my_timer"), proxy.get_timer(remote_tmr->id()));
  compare_to(proxy.get_timer("my_timer"), service);
  ASSERT_NO_THROW(remote_tmr->reschedule_from_now(0ns));
  dw::Waitset ws1{*sub1};
  ASSERT_TRUE(ws1.wait(1s));
  dw::Waitset ws2{*sub2};
  ASSERT_TRUE(ws2.wait(1s));
  dw::Waitset ws3{*sub3};
  ASSERT_TRUE(ws3.wait(1s));
  ASSERT_EQ(sub1->count_and_reset(), 1U);
  ASSERT_EQ(sub2->count_and_reset(), 1U);
  ASSERT_EQ(sub3->count_and_reset(), 1U);
}

TEST_F(test_remote_timer_service, cancel) {
  auto service = std::make_shared<steady_clock_timer_service>("my_service");
  timer_service_server server{service};
  remote_timer_service proxy{"my_service"};
  timer_ptr tmr;
  ASSERT_NO_THROW(tmr = proxy.create_timer(10s));
  ASSERT_TRUE(tmr->is_scheduled());
  compare_to(tmr, service);
  ASSERT_NO_THROW(tmr->cancel());
  ASSERT_FALSE(tmr->is_scheduled());
  compare_to(tmr, service);
}

TEST_F(test_remote_timer_service, now) {
  auto service = std::make_shared<steady_clock_timer_service>("my_service");
  timer_service_server server{service};
  remote_timer_service proxy{"my_service"};
  auto now = proxy.now().to_duration();
  std::this_thread::sleep_for(1ms);
  ASSERT_GT(proxy.now().to_duration(), now);
  ASSERT_LE(std::chrono::duration_cast<std::chrono::seconds>(
      steady_clock_timer_service::clock_type::now() -
      proxy.now().to_time_point<steady_clock_timer_service::clock_type>()), 3s);
}

TEST_F(test_remote_timer_service, absolute_time) {
  auto service = std::make_shared<steady_clock_timer_service>("my_service");
  timer_service_server server{service};
  remote_timer_service proxy{"my_service"};

  auto tmr = proxy.create_timer(proxy.now() + 500ms);
  ASSERT_TRUE(tmr->is_scheduled());
  {
    dw::Waitset ws{*tmr};
    ASSERT_TRUE(ws.wait(2s));
  }
  ASSERT_TRUE(tmr->test_and_reset());
  ASSERT_FALSE(tmr->is_scheduled());
  tmr->reschedule(proxy.now() + 500ms);
  ASSERT_TRUE(tmr->is_scheduled());
  {
    dw::Waitset ws{*tmr};
    ASSERT_TRUE(ws.wait(2s));
  }
  ASSERT_FALSE(tmr->is_scheduled());
  ASSERT_TRUE(tmr->test_and_reset());
  tmr = proxy.create_timer(proxy.now() + 500ms, 100ms);
  ASSERT_TRUE(tmr->is_scheduled());
  {
    dw::Waitset ws{*tmr};
    ASSERT_TRUE(ws.wait(2s));
  }
  ASSERT_TRUE(tmr->test_and_reset());
  ASSERT_TRUE(tmr->is_scheduled());
  tmr = proxy.create_timer("named", proxy.now() + 500ms);
  ASSERT_TRUE(tmr->is_scheduled());
  ASSERT_EQ(tmr->name(), "named");
  {
    dw::Waitset ws{*tmr};
    ASSERT_TRUE(ws.wait(2s));
  }
  ASSERT_TRUE(tmr->test_and_reset());
  ASSERT_FALSE(tmr->is_scheduled());
  tmr = proxy.create_timer("named2", proxy.now() + 500ms, 100ms);
  ASSERT_TRUE(tmr->is_scheduled());
  ASSERT_EQ(tmr->name(), "named2");
  {
    dw::Waitset ws{*tmr};
    ASSERT_TRUE(ws.wait(2s));
  }
  ASSERT_TRUE(tmr->test_and_reset());
  ASSERT_TRUE(tmr->is_scheduled());
}
#endif  // !APEX_CERT

TEST_F(test_remote_timer_service, unchecked_subscriptions) {
  remote_timer_service proxy{"my_service", 1000ms, true};
  const auto sub = proxy.create_timer_subscription_unchecked("my_timer");
  auto service = std::make_shared<steady_clock_timer_service>("my_service");
  auto remote_tmr = service->create_timer("my_timer");
  timer_service_server server{service};
  ASSERT_NO_THROW(remote_tmr->reschedule_from_now(0ns, 200ms));
  dw::Waitset ws{sub};
  ASSERT_TRUE(ws.wait(1s));
  (void)sub->take();
  ASSERT_TRUE(ws.wait(1s));
  (void)sub->take();
  ASSERT_TRUE(ws.wait(1s));
}
