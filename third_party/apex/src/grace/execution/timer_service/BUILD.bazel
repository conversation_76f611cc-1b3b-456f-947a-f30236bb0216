load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

config_setting(
    name = "under_ci",
    values = {"define": "UNDER_CI=true"},
)

ros_pkg(
    name = "timer_service_pkg",
    cc_libraries = [
        ":timer_service",
    ],
    description = "Package containing API for working with timer service",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/cpputils:cpputils_pkg",
        "@apex//grace/interfaces/rosgraph_msgs:rosgraph_msgs_pkg",
        "@apex//grace/interfaces/std_msgs:std_msgs_pkg",
        "@apex//grace/interfaces/timer_service_msgs:timer_service_msgs_pkg",
        "@apex//grace/monitoring/logging:logging_pkg",
        "@apex//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "@apex//grace/utils/msgutils:msgutils_pkg",
    ],
)

apex_cc_library(
    name = "timer_service",
    srcs = glob(["src/**"]),
    hdrs = glob(["include/**"]),
    applicable_licenses = ["//common/integrity:embedded_gold"],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/cpputils",
        "@apex//grace/interfaces/rosgraph_msgs",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/interfaces/timer_service_msgs",
        "@apex//grace/monitoring/logging",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@apex//grace/utils/msgutils",
    ],
)

apex_cc_test(
    name = "timer_service_tests",
    srcs = [
        "test/test.cpp",
        "test/test_clock_timer_service.cpp",
        "test/test_remote_timer_service.cpp",
        "test/test_sim_timer_service.cpp",
        "test/test_time_struct.cpp",
        "test/test_timer_service_doc.cpp",
        "test/timer_service_common.cpp",
        "test/timer_service_common.hpp",
    ],
    defines = select({
        ":under_ci": ["UNDER_CI"],
        "//conditions:default": [],
    }),
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    # timers tests are time-sensitive
    flaky = True,
    tags = [
        "constrained_test",
        "exclusive",
    ],
    deps = [
        ":timer_service",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "api_files",
    srcs = glob(["include/**"]),
    visibility = ["//visibility:public"],
)

filegroup(
    name = "doc_files",
    srcs = [
        "test/test_timer_service_doc.cpp",
    ],
    visibility = [":__subpackages__"],
)
