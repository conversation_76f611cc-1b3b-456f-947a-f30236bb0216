// Copyright 2017-2024 Apex.AI, Inc.
// All rights reserved.

#include "test_executor_common.hpp"

#include <gtest/gtest.h>

#include <rclcpp/rclcpp.hpp>
#include <rclcpp/dynamic_waitset/waitset.hpp>
#include <rclcpp/dynamic_waitset/file_monitor.hpp>
#include <executor2/executor_factory.hpp>
#include <executor2/executor_runner.hpp>
#include <executor2/executable_item.hpp>
#include <executor2/vertex.hpp>
#include <executor2/graph.hpp>
#include <executor2/graph_runner.hpp>
#include <execution_monitor/communication_stub.hpp>
#include <execution_monitor/communication.hpp>
#include <timer_service/sim_timer_service.hpp>
#include <cpputils/optional.hpp>
#include <std_msgs/msg/int32.hpp>

#include <string>
#include <array>
#include <memory>
#include <atomic>
#include <utility>
#include <vector>
#include <algorithm>
#include <thread>
#include <unordered_set>

using apex::executor::executable_item;
using apex::executor::executable_item_ptr;
using apex::executor::wrap_with_condition;
using apex::executor::subscription_type;
using apex::executor::subscription_list;
using apex::executor::executor_runner;
using apex::executor::vertex;
using apex::executor::graph;
using apex::executor::graph_runner;
using apex::executor::InfiniteWait;
namespace factory = apex::executor::executor_factory;
namespace custom = apex::executor::custom;

using namespace std::chrono_literals;
using namespace test_common;  // NOLINT

namespace
{
class test_executor : public ::testing::Test
{
protected:
  void SetUp() override
  {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
    node = std::make_shared<rclcpp::Node>("test_executor");
    pub = node->create_publisher<MsgType>(TriggeringTopic, qos());
    another_pub = node->create_publisher<MsgType>(AnotherTriggeringTopic, qos());
  }

  void TearDown() override
  {
    rclcpp::shutdown();
  }

  void publish(
    std::size_t num_of_msgs = 10U,
    std::chrono::milliseconds pause = 10ms,
    bool another_topic = false) const
  {
    for (auto i = 0U; i < num_of_msgs; ++i) {
      MsgType m;
      if (another_topic) {
        another_pub->publish(m);
      } else {
        pub->publish(m);
      }
      std::this_thread::sleep_for(pause);
    }
  }

  rclcpp::Node::SharedPtr node;
  rclcpp::Publisher<MsgType>::SharedPtr pub;
  rclcpp::Publisher<MsgType>::SharedPtr another_pub;
};

inline auto create_test_graph_nodes(bool first_has_subscriptions = false)
{
  if (!first_has_subscriptions) {
    return generate_nodes<node_that_has_no_subs, test_node_base>(6);
  } else {
    std::vector<test_node_base_ptr> nodes{std::make_shared<node_that_has_a_sub>(true)};
    attach_nodes<node_that_has_no_subs>(nodes, 5);
    return nodes;
  }
}

}  // namespace

TEST_F(test_executor, one_node) {
  auto exec = factory::create();
  auto node = std::make_shared<node_that_has_a_sub>(true);
  ASSERT_NO_THROW(exec->add(node));
  publish();
  executor_runner runner{*exec};
  const auto start = std::chrono::steady_clock::now();
  while (node->get_execution_count() < 10U && std::chrono::steady_clock::now() - start < 3s) {
    std::this_thread::sleep_for(1ms);
  }
  ASSERT_EQ(node->get_execution_count(), 10U);
  ASSERT_NO_THROW(runner.stop());
}

TEST_F(test_executor, one_node_with_condition) {
  auto exec = factory::create();
  std::atomic<int32_t> i{0};
  auto node = std::make_shared<node_that_has_a_sub>(true, TriggeringTopic,
      [&i](auto & node) {
        ++i;
        auto res = ((i - 1) % 2) == 0;
        if (!res) {node.take();}
        return res;
      }
  );
  ASSERT_NO_THROW(exec->add(node));
  publish();
  executor_runner runner{*exec};
  const auto start = std::chrono::steady_clock::now();
  while (i < 10 && std::chrono::steady_clock::now() - start < 3s) {
    std::this_thread::sleep_for(1ms);
  }
  ASSERT_EQ(node->get_execution_count(), 5U);
  ASSERT_NO_THROW(runner.stop());
}

TEST_F(test_executor, node_chain) {
  auto exec = factory::create();
  auto node1 = std::make_shared<node_that_has_a_sub>(true);
  auto node2 = std::make_shared<node_that_has_no_subs>();
  auto node3 = std::make_shared<node_that_has_no_subs>();
  ASSERT_NO_THROW(exec->add({node1, node2, node3}));
  publish();
  executor_runner runner{*exec};
  const auto start = std::chrono::steady_clock::now();
  while (node3->get_execution_count() < 10U && std::chrono::steady_clock::now() - start < 3s) {
    std::this_thread::sleep_for(1ms);
  }
  assert_execution_count(std::vector<test_node_base_ptr>{node1, node2, node3}, 10U);
  ASSERT_NO_THROW(runner.stop());
}

TEST_F(test_executor, node_chain_with_condition) {
  auto exec = factory::create();
  std::atomic<int32_t> i{0};
  auto node1 = std::make_shared<node_that_has_a_sub>(true, TriggeringTopic,
      [&i](auto & node) {
        ++i;
        auto res = ((i - 1) % 2) == 0;
        if (!res) {node.take();}
        return res;
      }
  );
  auto node2 = std::make_shared<node_that_has_no_subs>();
  auto node3 = std::make_shared<node_that_has_no_subs>();
  ASSERT_NO_THROW(exec->add({node1, node2, node3}));
  publish();
  executor_runner runner{*exec};
  const auto start = std::chrono::steady_clock::now();
  while (i < 10 && std::chrono::steady_clock::now() - start < 3s) {
    std::this_thread::sleep_for(1ms);
  }
  assert_execution_count(std::vector<test_node_base_ptr>{node1, node2, node3}, 5U);
  ASSERT_NO_THROW(runner.stop());
}

TEST_F(test_executor, no_items_in_task) {
  auto exec = factory::create();
  ASSERT_THROW(exec->add(std::vector<executable_item_ptr>{}), apex::runtime_error);
}

TEST_F(test_executor, null_items_in_task) {
  auto exec = factory::create();
  ASSERT_THROW(exec->add(std::vector<executable_item_ptr>{nullptr}), apex::runtime_error);
}

TEST_F(test_executor, no_subscriptions_on_master_node) {
  auto exec = factory::create();
  auto node = std::make_shared<node_that_has_no_subs>();
  ASSERT_THROW(exec->add(node), apex::runtime_error);
}

TEST_F(test_executor, execute_throws) {
  auto exec = factory::create();
  auto node = std::make_shared<node_that_throws_on_execute>();
  ASSERT_NO_THROW(exec->add(node));
  publish(1U);
  ASSERT_THROW(exec->run(), apex::runtime_error);
}

TEST_F(test_executor, wait_timeout) {
  auto exec = factory::create();
  auto node = std::make_shared<node_that_has_a_sub>(true);
  ASSERT_NO_THROW(exec->add(node));
  ASSERT_THROW(exec->run(100ms), apex::runtime_error);
}

TEST_F(test_executor, multiple_tasks) {
  auto exec = factory::create();
  std::vector<test_node_base_ptr> nodes{std::make_shared<node_that_has_a_sub>(true)};
  attach_nodes<node_that_has_no_subs>(nodes, 2);
  nodes.push_back(std::make_shared<node_that_has_a_sub>(true));
  attach_nodes<node_that_has_no_subs>(nodes, 2);
  ASSERT_NO_THROW(exec->add({nodes[0], nodes[1], nodes[2]}));
  ASSERT_NO_THROW(exec->add({nodes[3], nodes[4], nodes[5]}));
  publish();
  executor_runner runner{*exec};
  const auto start = std::chrono::steady_clock::now();
  while ((nodes[2]->get_execution_count() < 10U || nodes[5]->get_execution_count() < 10U) &&
    std::chrono::steady_clock::now() - start < 3s)
  {
    std::this_thread::sleep_for(1ms);
  }
  assert_execution_count(nodes, 10U);
  ASSERT_NO_THROW(runner.stop());
}

TEST_F(test_executor, graph) {
  auto exec = factory::create();
  auto nodes = create_test_graph_nodes(true);
  auto v = nodes_to_vertices(nodes);
  connect_test_graph(v);
  ASSERT_NO_THROW(exec->add(v[0]));
  publish();
  executor_runner runner{*exec};
  const auto start = std::chrono::steady_clock::now();
  while (nodes[5]->get_execution_count() < 10U && std::chrono::steady_clock::now() - start < 3s) {
    std::this_thread::sleep_for(1ms);
  }
  assert_execution_count(nodes, 10U);
  ASSERT_NO_THROW(runner.stop());
}

TEST_F(test_executor, graph_with_condition) {
  auto exec = factory::create();
  auto nodes = create_test_graph_nodes(true);
  std::atomic<int32_t> i{0};
  nodes[0]->set_condition([&i](auto & node) {
      ++i;
      const auto res = (((i - 1) % 2) == 0);
      if (!res) {node.take();}
      return res;
    });
  auto v = nodes_to_vertices(nodes);
  connect_test_graph(v);
  ASSERT_NO_THROW(exec->add(v[0]));
  publish();
  executor_runner runner{*exec};
  const auto start = std::chrono::steady_clock::now();
  while (i < 10 && std::chrono::steady_clock::now() - start < 3s) {
    std::this_thread::sleep_for(1ms);
  }
  assert_execution_count(nodes, 5U);
  ASSERT_NO_THROW(runner.stop());
}

TEST_F(test_executor, graph_no_subscriptions_on_root) {
  auto exec = factory::create();
  vertex v{std::make_shared<node_that_has_no_subs>()};
  ASSERT_THROW(exec->add(v), apex::runtime_error);
}

TEST_F(test_executor, graph_execute_throws) {
  auto exec = factory::create();
  vertex v{std::make_shared<node_that_has_a_sub>()};
  v.connect(vertex{std::make_shared<node_that_throws_on_execute>()});
  ASSERT_NO_THROW(exec->add(v));
  publish();
  ASSERT_THROW(exec->run(), apex::runtime_error);
}

TEST_F(test_executor, singular_events) {
  auto exec = factory::create();
  auto node = std::make_shared<node_that_has_a_sub>(true);
  ASSERT_NO_THROW(exec->add(node));
  publish(3U, 0ms);
  ASSERT_NO_THROW(exec->run(0ms, 1U));
  ASSERT_EQ(exec->get_total_iterations(), 1U);
  ASSERT_EQ(node->get_execution_count(), 1U);
  ASSERT_NO_THROW(exec->run(0ms, 2U));
  ASSERT_EQ(exec->get_total_iterations(), 3U);
  ASSERT_EQ(node->get_execution_count(), 3U);
  ASSERT_THROW(exec->run(0ms, 1U), apex::runtime_error);
}

TEST_F(test_executor, custom_graph_runner_settings) {
  auto exec = factory::create();
  std::unordered_set<std::thread::id> threads;
  std::mutex m;
  std::vector<test_node_base_ptr> nodes;
  const auto insertion = [&threads, &m](auto &) {
      std::unique_lock l{m};
      threads.insert(std::this_thread::get_id());
      return true;
    };
  nodes.emplace_back(std::make_shared<node_that_has_a_sub>(true, TriggeringTopic, insertion));
  for (auto i = 0U; i < 5; ++i) {
    nodes.emplace_back(std::make_shared<node_that_has_no_subs>(insertion));
  }
  auto v = nodes_to_vertices(nodes);
  v[0].connect(v[1]);
  v[0].connect(v[2]);
  v[0].connect(v[3]);
  v[0].connect(v[4]);
  v[0].connect(v[5]);
  ASSERT_NO_THROW(exec->add(v[0], apex::executor::graph_runner_settings{1U}));
  publish();
  executor_runner runner{*exec};
  const auto start = std::chrono::steady_clock::now();
  while (nodes[5]->get_execution_count() < 10U && std::chrono::steady_clock::now() - start < 3s) {
    std::this_thread::sleep_for(1ms);
  }
  assert_execution_count(nodes, 10U);
  ASSERT_EQ(threads.size(), 1U);
  ASSERT_NO_THROW(runner.stop());
}

TEST_F(test_executor, thread_pool) {
  auto exec = factory::create(10U);
  std::unordered_set<std::thread::id> threads;
  std::mutex m;
  const auto reg_func = [&threads, &m](std::thread::id id) {
      std::unique_lock l{m};
      threads.insert(id);
    };
  std::vector<std::shared_ptr<thread_pool_test_node>> nodes(13);
  std::generate(nodes.begin(), nodes.end(),
    [&] {return std::make_shared<thread_pool_test_node>(reg_func);}
  );
  for (auto node : nodes) {
    exec->add(std::move(node));
  }
  for (auto j = 1U; j <= 10U; ++j) {
    publish();
    exec->run(InfiniteWait, 10U);
    assert_execution_count(nodes, 10U * j);
  }
  ASSERT_EQ(threads.size(), 10U);
}

TEST_F(test_executor, multiple_items) {
  rclcpp::Node node1{"mi_test1", "ns1"};
  rclcpp::Node node2{"mi_test2"};

  struct item : public executable_item
  {
    using executable_item::executable_item;
    bool execute_impl() override {return true;}
  };

  auto item11 = std::make_shared<item>(node1);
  ASSERT_EQ(item11->get_related_node_id(), "/ns1::mi_test1");
  ASSERT_EQ(item11->get_id(), "/ns1::mi_test1#1");
  auto item12 = std::make_shared<item>(node1);
  ASSERT_EQ(item12->get_related_node_id(), "/ns1::mi_test1");
  ASSERT_EQ(item12->get_id(), "/ns1::mi_test1#2");

  auto item21 = std::make_shared<item>(node2);
  ASSERT_EQ(item21->get_related_node_id(), "/::mi_test2");
  ASSERT_EQ(item21->get_id(), "/::mi_test2#1");
  auto item22 = std::make_shared<item>(node2);
  ASSERT_EQ(item22->get_related_node_id(), "/::mi_test2");
  ASSERT_EQ(item22->get_id(), "/::mi_test2#2");
}

using rclcpp::dynamic_waitset::file_monitor;

TEST_F(test_executor, no_file_monitor_add_files) {
  auto exec = factory::create();
  auto p = create_pipe();
  auto guard = create_pipe_guard(p);
  auto node = std::make_shared<node_with_files>(std::vector<std::int32_t>{p[PipeIn]});
  ASSERT_THROW(exec->add(node), apex::runtime_error);
}

TEST_F(test_executor, one_file) {
  auto exec = factory::create();
  file_monitor fm;
  exec->set_file_monitor(fm);
  auto p = create_pipe();
  auto guard = create_pipe_guard(p);
  auto node = std::make_shared<node_with_files>(std::vector<std::int32_t>{p[PipeIn]});
  ASSERT_NO_THROW(exec->add(node));
  executor_runner runner{*exec};
  for (std::int32_t i = 1; i < 6; ++i) {
    write_pipe(p, i);
    const auto count = static_cast<std::size_t>(i);
    const auto start = std::chrono::steady_clock::now();
    while (node->get_execution_count() < count &&
      std::chrono::steady_clock::now() - start < 3s)
    {
      std::this_thread::sleep_for(1ms);
    }
    ASSERT_EQ(node->get_execution_count(), count);
  }
  ::close(p[PipeOut]);
  const auto start = std::chrono::steady_clock::now();
  while (node->get_execution_count() < 6U && std::chrono::steady_clock::now() - start < 3s) {
    std::this_thread::sleep_for(1ms);
  }
  ASSERT_EQ(node->get_execution_count(), 6U);
  ASSERT_NO_THROW(runner.stop());
  ASSERT_EQ(node->file_reads[p[PipeIn]], (std::vector<std::int32_t>{1, 2, 3, 4, 5, -1}));
}

TEST_F(test_executor, multiple_files) {
  auto exec = factory::create();
  file_monitor fm;
  exec->set_file_monitor(fm);
  pipe_t p[] = {create_pipe(), create_pipe()};
  auto_close_pipe guards[] = {create_pipe_guard(p[0]), create_pipe_guard(p[1])};
  auto node = std::make_shared<node_with_files>(std::vector<std::int32_t>{p[0][PipeIn],
        p[1][PipeIn]});
  ASSERT_NO_THROW(exec->add(node));
  executor_runner runner{*exec};
  for (std::int32_t i = 1; i < 11; ++i) {
    if ((i % 2) != 0) {
      write_pipe(p[0], i);
    } else {
      write_pipe(p[1], i);
    }
    const auto count = static_cast<std::size_t>(i);
    const auto start = std::chrono::steady_clock::now();
    while (node->get_execution_count() < count &&
      std::chrono::steady_clock::now() - start < 3s)
    {
      std::this_thread::sleep_for(1ms);
    }
    ASSERT_EQ(node->get_execution_count(), count);
  }
  ::close(p[0][PipeOut]);
  const auto start = std::chrono::steady_clock::now();
  while (node->get_execution_count() < 11U && std::chrono::steady_clock::now() - start < 3s) {
    std::this_thread::sleep_for(1ms);
  }
  ASSERT_EQ(node->get_execution_count(), 11U);
  ASSERT_NO_THROW(runner.stop());
  ASSERT_EQ(node->file_reads[p[0][PipeIn]], (std::vector<std::int32_t>{1, 3, 5, 7, 9, -1}));
  ASSERT_EQ(node->file_reads[p[1][PipeIn]], (std::vector<std::int32_t>{2, 4, 6, 8, 10}));
}

#ifndef APEX_CERT  // TODO(misha) : remove when #16761 is resolved
TEST_F(test_executor, client_service) {
  auto exec = factory::create();
  auto snode = std::make_shared<service_node>();
  auto cnode = std::make_shared<client_node>();
  cnode->wait_for_service();
  ASSERT_NO_THROW(exec->add(snode));
  ASSERT_NO_THROW(exec->add(cnode));
  executor_runner runner{*exec};
  for (auto i = 0; i < 10; ++i) {
    cnode->trigger();
  }
  const auto start = std::chrono::steady_clock::now();
  while (((cnode->get_execution_count() < 10U) || (snode->get_execution_count() < 10U)) &&
    std::chrono::steady_clock::now() - start < 1s)
  {
    std::this_thread::sleep_for(1ms);
  }
  ASSERT_EQ(cnode->get_execution_count(), 10U);
  ASSERT_EQ(snode->get_execution_count(), 10U);
}
#endif

TEST_F(test_executor, custom_ids) {
  auto exec = factory::create();
  ASSERT_EQ(exec->add("id1", std::make_shared<node_that_has_a_sub>()), "id1");
  ASSERT_EQ(exec->add("id6",
    std::vector<executable_item_ptr>{std::make_shared<node_that_has_a_sub>()}), "id6");
  ASSERT_EQ(exec->add("id11", vertex{std::make_shared<node_that_has_a_sub>()}), "id11");
  ASSERT_FALSE(exec->add(std::make_shared<node_that_has_a_sub>()).empty());
  ASSERT_FALSE(exec->add(vertex{std::make_shared<node_that_has_a_sub>()}).empty());
}

TEST_F(test_executor, wrap_with_condition_with_condition) {
  auto exec = factory::create();
  auto node = std::make_shared<node_that_has_a_sub>(true);
  auto flag = false;
  std::atomic<std::int32_t> counter{0};
  auto wrapped_node = wrap_with_condition(node, [&flag, &counter] {
        if (counter == 10) {return false;}
        ++counter;
        flag = !flag;
        return flag;
      });
  ASSERT_NO_THROW(exec->add(wrapped_node));
  publish();
  executor_runner runner{*exec};
  const auto start = std::chrono::steady_clock::now();
  while ((counter < 10) &&
    (std::chrono::steady_clock::now() - start < 3s))
  {
    std::this_thread::sleep_for(1ms);
  }
  ASSERT_EQ(node->get_execution_count(), 5U);
}

TEST_F(test_executor, wrap_with_condition_nullptr) {
  auto exec = factory::create();
  ASSERT_THROW(wrap_with_condition(nullptr, [] {return true;}), apex::runtime_error);
}

TEST_F(test_executor, interrupt_chain) {
  auto exec = factory::create();
  auto node1 = std::make_shared<node_that_has_a_sub>(true);
  auto node2 = std::make_shared<node_that_has_no_subs>();
  auto node3 = std::make_shared<node_that_has_no_subs>();
  auto wrapped_node3 = wrap_with_condition(node3, [] {return false;});
  ASSERT_NO_THROW(exec->add({node1, node2, wrapped_node3}));
  publish();
  executor_runner runner{*exec};
  const auto start = std::chrono::steady_clock::now();
  while ((node2->get_execution_count() < 10U) &&
    (std::chrono::steady_clock::now() - start < 3s))
  {
    std::this_thread::sleep_for(1ms);
  }
  ASSERT_EQ(node1->get_execution_count(), 10U);
  ASSERT_EQ(node2->get_execution_count(), 10U);
  ASSERT_EQ(node3->get_execution_count(), 0U);
  ASSERT_NO_THROW(runner.stop());
}

TEST_F(test_executor, interrupt_graph) {
  auto exec = factory::create();
  auto nodes = create_test_graph_nodes(true);
  std::atomic<std::int32_t> counter = 0;
  nodes.back() = std::make_shared<node_that_has_no_subs>([&counter](auto &) {
        ++counter;
        return false;
      });
  auto v = nodes_to_vertices(nodes);
  connect_test_graph(v);
  ASSERT_NO_THROW(exec->add(v[0]));
  publish();
  executor_runner runner{*exec};
  const auto start = std::chrono::steady_clock::now();
  while ((counter < 10) &&
    (std::chrono::steady_clock::now() - start < 3s))
  {
    std::this_thread::sleep_for(1ms);
  }
  ASSERT_EQ(nodes[0]->get_execution_count(), 10U);
  ASSERT_EQ(nodes[1]->get_execution_count(), 10U);
  ASSERT_EQ(nodes[2]->get_execution_count(), 10U);
  ASSERT_EQ(nodes[3]->get_execution_count(), 10U);
  ASSERT_EQ(nodes[4]->get_execution_count(), 10U);
  ASSERT_EQ(nodes[5]->get_execution_count(), 0U);
  ASSERT_NO_THROW(runner.stop());
}

TEST_F(test_executor, add_periodic_task) {
  apex::timer_service::sim_timer_service ts;
  auto exec = factory::create();
  constexpr auto node_count = 12U;
  std::vector<std::shared_ptr<node_that_has_a_sub>> nodes;
  nodes.reserve(node_count);
  for (auto i = 0U; i < node_count; ++i) {
    nodes.push_back(std::make_shared<node_that_has_a_sub>());
  }
  ASSERT_EQ(exec->add_periodic("id1", nodes[0], ts, 0ns, 10ns), "id1");
  ASSERT_EQ(exec->add_periodic("id2", vertex{nodes[1]}, ts, 0ns, 10ns), "id2");
  ASSERT_EQ(exec->add_periodic("id3", std::vector<executable_item_ptr>{nodes[2]}, ts, 0ns, 10ns),
    "id3");
  ASSERT_EQ(exec->add_periodic("id4", nodes[3], ts, 10ns), "id4");
  ASSERT_EQ(exec->add_periodic("id5", vertex{nodes[4]}, ts, 10ns), "id5");
  ASSERT_EQ(exec->add_periodic("id6", std::vector<executable_item_ptr>{nodes[5]}, ts, 10ns), "id6");

  std::unordered_set<apex::executor::task_id_t> auto_task_ids;
  auto_task_ids.insert(exec->add_periodic(nodes[6], ts, 0ns, 10ns));
  auto_task_ids.insert(exec->add_periodic(vertex{nodes[7]}, ts, 0ns, 10ns));
  auto_task_ids.insert(exec->add_periodic(std::vector<executable_item_ptr>{nodes[8]}, ts, 0ns,
    10ns));
  auto_task_ids.insert(exec->add_periodic(nodes[9], ts, 10ns));
  auto_task_ids.insert(exec->add_periodic(vertex{nodes[10]}, ts, 10ns));
  auto_task_ids.insert(exec->add_periodic(std::vector<executable_item_ptr>{nodes[11]}, ts, 10ns));
  ASSERT_EQ(auto_task_ids.size(), 6U);

  executor_runner runner{*exec};
  ts.now(ts.now() + 1ns);

  for (auto i = 0U; i < 2U; ++i) {
    std::this_thread::sleep_for(100ms);
    for (const auto & node : nodes) {
      ASSERT_EQ(node->get_execution_count(), i + 1);
    }
    ts.now(ts.now() + 11ns);
  }
  std::this_thread::sleep_for(100ms);
  std::vector<size_t> counts;
  for (auto & node : nodes) {
    counts.push_back(node->get_execution_count());
  }
  publish();
  for (auto i = 0U; i < node_count; ++i) {
    ASSERT_EQ(nodes[i]->get_execution_count() - counts[i], 10U);
  }
}

TEST_F(test_executor, add_timeout_task) {
  apex::timer_service::sim_timer_service ts;
  auto exec = factory::create();
  constexpr auto node_count = 12U;
  std::vector<std::shared_ptr<node_that_has_a_sub>> nodes;
  nodes.reserve(node_count);
  for (auto i = 0U; i < node_count; ++i) {
    nodes.push_back(std::make_shared<node_that_has_a_sub>());
  }
  ASSERT_EQ(exec->add_with_timeout("id1", nodes[0], ts, 10ns), "id1");
  ASSERT_EQ(exec->add_with_timeout("id2", vertex{nodes[1]}, ts, 10ns), "id2");
  ASSERT_EQ(exec->add_with_timeout("id3", std::vector<executable_item_ptr>{nodes[2]}, ts, 10ns),
    "id3");
  ASSERT_EQ(exec->add_with_timeout("id4", nodes[3], ts, 10ns), "id4");
  ASSERT_EQ(exec->add_with_timeout("id5", vertex{nodes[4]}, ts, 10ns), "id5");
  ASSERT_EQ(exec->add_with_timeout("id6", std::vector<executable_item_ptr>{nodes[5]}, ts,
    10ns), "id6");

  std::unordered_set<apex::executor::task_id_t> auto_task_ids;
  auto_task_ids.insert(exec->add_with_timeout(nodes[6], ts, 10ns));
  auto_task_ids.insert(exec->add_with_timeout(vertex{nodes[7]}, ts, 10ns));
  auto_task_ids.insert(exec->add_with_timeout(std::vector<executable_item_ptr>{nodes[8]}, ts,
    10ns));
  auto_task_ids.insert(exec->add_with_timeout(nodes[9], ts, 10ns));
  auto_task_ids.insert(exec->add_with_timeout(vertex{nodes[10]}, ts, 10ns));
  auto_task_ids.insert(exec->add_with_timeout(std::vector<executable_item_ptr>{nodes[11]}, ts,
    10ns));
  ASSERT_EQ(auto_task_ids.size(), 6U);

  executor_runner runner{*exec};
  ts.now(ts.now() + 1ns);

  for (auto i = 0U; i < 2U; ++i) {
    std::this_thread::sleep_for(100ms);
    for (const auto & node : nodes) {
      ASSERT_EQ(node->get_execution_count(), i);
    }
    ts.now(ts.now() + 11ns);
  }
  std::this_thread::sleep_for(100ms);
  std::vector<size_t> counts;
  for (auto & node : nodes) {
    counts.push_back(node->get_execution_count());
  }
  publish();
  for (auto i = 0U; i < node_count; ++i) {
    ASSERT_EQ(nodes[i]->get_execution_count() - counts[i], 10U);
    counts[i] = nodes[i]->get_execution_count();
  }
  for (auto i = 0U; i < 10U; ++i) {
    ts.now(ts.now() + 9ns);
    publish(1U);
  }
  std::this_thread::sleep_for(100ms);
  for (auto i = 0U; i < node_count; ++i) {
    ASSERT_EQ(nodes[i]->get_execution_count() - counts[i], 10U);
    counts[i] = nodes[i]->get_execution_count();
  }
  ts.now(ts.now() + 11ns);
  std::this_thread::sleep_for(100ms);
  for (auto i = 0U; i < node_count; ++i) {
    ASSERT_EQ(nodes[i]->get_execution_count() - counts[i], 1U);
  }
}

TEST_F(test_executor, timeout_task_no_reschedule_on_false_condition) {
  apex::timer_service::sim_timer_service ts;
  ts.now(ts.now() + 1ns);
  auto exec = factory::create();
  auto node = std::make_shared<node_that_has_a_sub>();
  std::atomic_bool retval{true};
  node->set_condition([&retval](const auto &) {
      return retval.load();
    });
  ASSERT_NO_THROW(exec->add_with_timeout(node, ts, 10ns));
  executor_runner runner{*exec};
  ts.now(ts.now() + 10ns);
  std::this_thread::sleep_for(100ms);
  ASSERT_EQ(node->get_execution_count(), 1U);
  retval = false;
  ts.now(ts.now() + 10ns);
  std::this_thread::sleep_for(100ms);
  ASSERT_EQ(node->get_execution_count(), 1U);
  ts.now(ts.now() + 10ns);
  std::this_thread::sleep_for(100ms);
  ASSERT_EQ(node->get_execution_count(), 1U);
  retval = true;
  publish(1U);
  std::this_thread::sleep_for(100ms);
  ASSERT_EQ(node->get_execution_count(), 2U);
  ts.now(ts.now() + 10ns);
  std::this_thread::sleep_for(100ms);
  ASSERT_EQ(node->get_execution_count(), 3U);
}

TEST_F(test_executor, timers_with_graph_args_compile) {
  apex::timer_service::sim_timer_service ts;
  auto exec = factory::create();
  auto node = std::make_shared<node_that_has_a_sub>();
  ASSERT_NO_THROW(exec->add_with_timeout(vertex{node}, ts, 10ns,
    apex::executor::graph_runner_settings{1U}));
  ASSERT_NO_THROW(exec->add_periodic(vertex{node}, ts, 10ns,
    apex::executor::graph_runner_settings{1U}));
}

TEST_F(test_executor, timers_with_expectations_compile) {
  using apex::execution_monitor::expectation_list;
  apex::timer_service::sim_timer_service ts;
  auto exec = factory::create();
  auto node = std::make_shared<node_that_has_a_sub>();
  apex::execution_monitor::communication_stub comm;
  apex::executor::execution_monitor em{"test", comm};
  exec->set_execution_monitor(em);
  ASSERT_NO_THROW(exec->add_with_timeout(expectation_list{}, vertex{node}, ts, 10ns));
  ASSERT_NO_THROW(exec->add_periodic(expectation_list{}, vertex{node}, ts, 10ns));
  ASSERT_NO_THROW(exec->add_with_timeout(expectation_list{}, vertex{node}, ts, 10ns,
    apex::executor::graph_runner_settings{1U}));
  ASSERT_NO_THROW(exec->add_periodic(expectation_list{}, vertex{node}, ts, 10ns,
    apex::executor::graph_runner_settings{1U}));
}

TEST_F(test_executor, null_item) {
  apex::timer_service::sim_timer_service ts;
  auto exec = factory::create();
  ASSERT_THROW(exec->add_periodic(nullptr, ts, 1s), apex::runtime_error);
  ASSERT_THROW(exec->add_with_timeout(nullptr, ts, 1s), apex::runtime_error);
}

TEST_F(test_executor, timed_tasks_properly_wraps_graph) {
  apex::timer_service::sim_timer_service ts;
  ts.now(ts.now() + 1ns);
  auto exec = factory::create();
  auto node1 = std::make_shared<node_that_has_no_subs>();
  auto node2 = std::make_shared<node_that_has_no_subs>();
  vertex v1{node1};
  vertex v2{node2};
  v1.connect(v2);
  ASSERT_NO_THROW(exec->add_periodic(v1, ts, 0ns, 10ns));
  executor_runner runner{*exec};
  std::this_thread::sleep_for(100ms);
  ASSERT_EQ(node1->get_execution_count(), 1U);
  ASSERT_EQ(node2->get_execution_count(), 1U);
}

TEST_F(test_executor, timed_tasks_properly_wraps_chain) {
  apex::timer_service::sim_timer_service ts;
  ts.now(ts.now() + 1ns);
  auto exec = factory::create();
  auto node1 = std::make_shared<node_that_has_no_subs>();
  auto node2 = std::make_shared<node_that_has_no_subs>();
  std::vector<apex::executor::executable_item_ptr> chain{node1, node2};
  ASSERT_NO_THROW(exec->add_periodic(chain, ts, 0ns, 10ns));
  executor_runner runner{*exec};
  std::this_thread::sleep_for(100ms);
  ASSERT_EQ(node1->get_execution_count(), 1U);
  ASSERT_EQ(node2->get_execution_count(), 1U);
}

TEST_F(test_executor, one_shot_timer) {
  apex::timer_service::sim_timer_service ts;
  ts.now(ts.now() + 1ns);
  auto exec = factory::create();
  auto node = std::make_shared<node_that_has_no_subs>();
  ASSERT_NO_THROW(exec->add_periodic(node, ts, 0ns, 0ns));
  executor_runner runner{*exec};
  std::this_thread::sleep_for(100ms);
  ASSERT_EQ(node->get_execution_count(), 1U);
  ts.now(ts.now() + 10ns);
  std::this_thread::sleep_for(100ms);
  ASSERT_EQ(node->get_execution_count(), 1U);
}
