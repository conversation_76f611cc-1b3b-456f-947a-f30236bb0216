// Copyright 2017-2021 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>
#include <apex_test_tools/apex_skip.hpp>

#include <executor2/graph_runner.hpp>

#include <list>
#include <vector>
#include <unordered_set>
#include <unordered_map>
#include <algorithm>
#include <iterator>
#include <cassert>
#include <numeric>
#include <utility>
#include <chrono>

using apex::executor::node;
using apex::executor::node_sptr;
using apex::executor::graph;
using apex::executor::graph_runner;
using apex::executor::connect;

using namespace std::chrono_literals;

namespace
{
void find_all_topological_orders(
  std::unordered_map<node *, std::size_t> & indegree,
  std::list<node *> & path,
  std::unordered_set<node *> & discovered,
  std::vector<std::vector<node *>> & result)
{
  for (const auto & p : indegree) {
    const auto v = p.first;

    if ((indegree[v] == 0) && (discovered.find(v) == discovered.end())) {
      for (const auto & c : v->adj) {
        indegree[c.get()]--;
      }

      path.push_back(v);
      discovered.insert(v);

      find_all_topological_orders(indegree, path, discovered, result);

      for (const auto & c : v->adj) {
        indegree[c.get()]++;
      }

      path.pop_back();
      discovered.erase(v);
    }
  }

  if (path.size() == indegree.size()) {
    result.emplace_back(path.begin(), path.end());
  }
}

std::vector<std::vector<node *>> get_all_topological_orders(const graph & g)
{
  std::vector<std::vector<node *>> result;
  std::unordered_set<node *> discovered;
  std::list<node *> path;
  auto indegree = g.get_expected_indegrees();
  const auto roots = g.get_roots();

  // remove roots indegree since they are virtual (inbound execution flow)
  for (const auto & r : roots) {
    --indegree[r.get()];
  }

  find_all_topological_orders(indegree, path, discovered, result);
  return result;
}

template<std::size_t size>
inline std::vector<node *> map_order_to_nodes(
  const std::vector<std::int32_t> & order,
  const node_sptr (& nodes)[size])
{
  std::vector<node *> ptrs;
  ptrs.reserve(order.size());
  std::transform(order.begin(), order.end(), std::back_inserter(ptrs),
    [&](auto id) {
      assert(id < static_cast<decltype(id)>(size));
      return nodes[id].get();
    });
  return ptrs;
}

}  // namespace

TEST(graph, empty) {
  ASSERT_THROW(graph{node_sptr{}}, apex::runtime_error);
}

TEST(graph, null_connect) {
  auto n1 = node::make([] {});
  auto n2 = node::make([] {});
  auto n3 = node_sptr{};
  n2->adj.push_back(n3);  // can't connect nullptr with `connect`
  ASSERT_TRUE(connect(n1, n2));
  ASSERT_FALSE(connect(n1, n2));
  ASSERT_THROW(graph{std::move(n1)}, apex::runtime_error);
}

TEST(graph, self_cycle) {
#ifdef ADDRESS_SANITIZER
  APEX_SKIP_TEST(35483, "The cycle results in a node being leaked, but this is an error condition");
#endif
  auto n1 = node::make([] {});
  n1->adj.push_back(n1);
  ASSERT_THROW(graph{std::move(n1)}, apex::runtime_error);
}

TEST(graph, cycle) {
  {
#ifdef ADDRESS_SANITIZER
    APEX_SKIP_TEST(35483,
      "The cycle results in a node being leaked, but this is an error condition");
#endif
    node_sptr nodes[3];
    for (auto & n : nodes) {
      n = node::make([] {});
    }

    ASSERT_TRUE(connect(nodes[0], nodes[1]));
    ASSERT_TRUE(connect(nodes[1], nodes[2]));
    ASSERT_TRUE(connect(nodes[2], nodes[0]));

    ASSERT_THROW(graph{std::move(nodes[0])}, apex::runtime_error);
  }
  {
    node_sptr nodes[5];
    for (auto & n : nodes) {
      n = node::make([] {});
    }

    ASSERT_TRUE(connect(nodes[0], nodes[1]));
    ASSERT_TRUE(connect(nodes[1], nodes[2]));
    ASSERT_TRUE(connect(nodes[2], nodes[4]));
    ASSERT_TRUE(connect(nodes[2], nodes[3]));
    ASSERT_TRUE(connect(nodes[3], nodes[0]));
    ASSERT_TRUE(connect(nodes[4], nodes[2]));
    ASSERT_THROW(graph{std::move(nodes[0])}, apex::runtime_error);
  }
}

TEST(graph, tree_calculations) {
  auto root = node::make([] {});
  auto level1_1 = node::make([] {});
  auto level1_2 = node::make([] {});
  auto level2_1 = node::make([] {});
  auto level2_2 = node::make([] {});
  auto level2_3 = node::make([] {});
  auto level2_4 = node::make([] {});

  root->adj.push_back(std::move(level1_1));
  root->adj.push_back(std::move(level1_2));
  root->adj[0]->adj.push_back(std::move(level2_1));
  root->adj[0]->adj.push_back(std::move(level2_2));
  root->adj[1]->adj.push_back(std::move(level2_3));
  root->adj[1]->adj.push_back(std::move(level2_4));

  graph g{std::move(root)};
  ASSERT_EQ(g.get_node_count(), 7U);
  ASSERT_EQ(g.get_exec_queue_capacity(), 4u);
  ASSERT_EQ(g.get_max_branching_factor(), 4U);

  const auto expected_indegree = g.get_expected_indegrees();
  ASSERT_EQ(expected_indegree.size(), 7u);
  for (const auto & item : expected_indegree) {
    ASSERT_EQ(item.second, 1u);
  }

  const auto current_indegree = g.get_current_indegrees();
  ASSERT_EQ(current_indegree.size(), 7u);
  for (const auto & item : current_indegree) {
    ASSERT_EQ(item.second, 0u);
  }
}

TEST(graph, graph_calculations) {
  auto root = node::make([] {});
  auto level1_1 = node::make([] {});
  auto level1_2 = node::make([] {});
  auto level2_1 = node::make([] {});
  auto level2_2 = node::make([] {});
  auto level2_3 = node::make([] {});
  auto level2_4 = node::make([] {});

  root->adj.push_back(std::move(level1_1));
  root->adj.push_back(std::move(level1_2));
  root->adj[0]->adj.push_back(std::move(level2_1));
  root->adj[0]->adj.push_back(level2_2);
  root->adj[1]->adj.push_back(level2_2);
  root->adj[1]->adj.push_back(std::move(level2_3));
  root->adj[1]->adj.push_back(std::move(level2_4));

  graph g{std::move(root)};
  ASSERT_EQ(g.get_node_count(), 7U);
  ASSERT_EQ(g.get_exec_queue_capacity(), 5U);
  ASSERT_EQ(g.get_max_branching_factor(), 4U);

  const auto expected_indegree = g.get_expected_indegrees();
  ASSERT_EQ(expected_indegree.size(), 7u);
  auto plevel2_2 = level2_2.get();
  for (const auto & item : expected_indegree) {
    if (item.first == plevel2_2) {
      ASSERT_EQ(item.second, 2u);
    } else {
      ASSERT_EQ(item.second, 1u);
    }
  }

  const auto current_indegree = g.get_current_indegrees();
  ASSERT_EQ(current_indegree.size(), 7u);
  for (const auto & item : current_indegree) {
    ASSERT_EQ(item.second, 0u);
  }
}

TEST(graph, graph_caculations2) {
  node_sptr nodes[6];
  for (auto & n : nodes) {
    n = node::make([] {});
  }

  nodes[4]->adj.push_back(nodes[0]);
  nodes[4]->adj.push_back(nodes[1]);
  nodes[5]->adj.push_back(nodes[0]);
  nodes[5]->adj.push_back(nodes[2]);
  nodes[2]->adj.push_back(nodes[3]);
  nodes[3]->adj.push_back(nodes[1]);

  graph g{{nodes[4], nodes[5]}};
  ASSERT_EQ(g.get_node_count(), 6u);
  ASSERT_EQ(g.get_exec_queue_capacity(), 4u);
  ASSERT_EQ(g.get_max_branching_factor(), 3u);

  auto expected_indegree = g.get_expected_indegrees();
  ASSERT_EQ(expected_indegree.size(), 6u);
  ASSERT_EQ(expected_indegree[nodes[0].get()], 2u);
  ASSERT_EQ(expected_indegree[nodes[1].get()], 2u);
  ASSERT_EQ(expected_indegree[nodes[2].get()], 1u);
  ASSERT_EQ(expected_indegree[nodes[3].get()], 1u);
  ASSERT_EQ(expected_indegree[nodes[4].get()], 1u);
  ASSERT_EQ(expected_indegree[nodes[5].get()], 1u);

  const auto current_indegree = g.get_current_indegrees();
  ASSERT_EQ(current_indegree.size(), 6u);
  for (const auto & item : current_indegree) {
    ASSERT_EQ(item.second, 0u);
  }
}

TEST(graph, one_node_run) {
  std::int32_t step_counter{0};
  graph g{node::make([&] {++step_counter;})};

  graph_runner r{g};
  ASSERT_EQ(r.get_concurrency(), std::thread::hardware_concurrency() * 2);

  for (auto i = 0; i < 100; ++i) {
    ASSERT_NO_THROW(g.step());
  }

  ASSERT_EQ(step_counter, 100);
}

TEST(graph, one_node_throws) {
  std::int32_t step_counter{0};
  graph g{node::make([&] {
        ++step_counter;
        // cppcheck-suppress internalAstError
        if (step_counter == 5) {
          throw std::exception();
        }
      })};

  graph_runner r{g};
  ASSERT_EQ(r.get_concurrency(), std::thread::hardware_concurrency() * 2);

  for (auto i = 0; i < 100; ++i) {
    if (i == 4) {
      ASSERT_THROW(g.step(), std::exception);
    } else {
      ASSERT_NO_THROW(g.step());
    }
  }

  ASSERT_EQ(step_counter, 100);
}

TEST(graph, graph_run) {
  std::unordered_map<std::int32_t, std::thread::id> log;
  std::vector<std::int32_t> order;
  std::mutex m;

  const auto add_to_log = [&](std::int32_t id) {
      std::unique_lock<std::mutex> lock{m};
      ASSERT_TRUE(log.emplace(id, std::this_thread::get_id()).second);
      order.push_back(id);
      lock.unlock();
      std::this_thread::sleep_for(1ms);
    };

  node_sptr nodes[10];
  std::int32_t id = 0;
  for (auto & n : nodes) {
    n = node::make([&add_to_log, id] {add_to_log(id);});
    ++id;
  }

  ASSERT_TRUE(connect(nodes[0], nodes[1]));
  ASSERT_TRUE(connect(nodes[0], nodes[2]));
  ASSERT_TRUE(connect(nodes[0], nodes[3]));
  ASSERT_TRUE(connect(nodes[1], nodes[4]));
  ASSERT_TRUE(connect(nodes[2], nodes[5]));
  ASSERT_TRUE(connect(nodes[3], nodes[6]));
  ASSERT_TRUE(connect(nodes[4], nodes[7]));
  ASSERT_TRUE(connect(nodes[4], nodes[8]));
  ASSERT_TRUE(connect(nodes[5], nodes[8]));
  ASSERT_TRUE(connect(nodes[5], nodes[9]));
  ASSERT_TRUE(connect(nodes[6], nodes[9]));
  ASSERT_TRUE(connect(nodes[7], nodes[9]));
  ASSERT_TRUE(connect(nodes[8], nodes[9]));

  graph g{nodes[0]};

  ASSERT_EQ(g.get_node_count(), 10U);
  ASSERT_EQ(g.get_exec_queue_capacity(), 5U);
  ASSERT_EQ(g.get_max_branching_factor(), 3U);

  const auto all_orders = get_all_topological_orders(g);

  {
    graph_runner r{g, g.get_max_branching_factor()};
    std::vector<std::size_t> thread_count;

    for (auto i = 0; i < 1000; ++i) {
      ASSERT_NO_THROW(g.step());

      ASSERT_EQ(log.size(), g.get_node_count());

      std::unordered_set<std::thread::id> threads;
      for (const auto & rec : log) {
        threads.insert(rec.second);
      }
      thread_count.push_back(threads.size());
      ASSERT_EQ(order.size(), g.get_node_count());
      const auto order_nodes = map_order_to_nodes(order, nodes);
      ASSERT_NE(std::find(all_orders.begin(), all_orders.end(), order_nodes), all_orders.end());
      log.clear();
      order.clear();
    }

    auto sum = std::accumulate(thread_count.begin(), thread_count.end(), 0U);
    ASSERT_GT(static_cast<double>(sum) / static_cast<double>(thread_count.size()), 2.0);
  }

  {
    graph_runner r{g, 1u};
    ASSERT_NO_THROW(g.step());

    ASSERT_EQ(log.size(), g.get_node_count());

    std::unordered_set<std::thread::id> threads;
    for (const auto & rec : log) {
      threads.insert(rec.second);
    }

    ASSERT_EQ(threads.size(), 1u);
    ASSERT_EQ(order.size(), g.get_node_count());
    const auto order_nodes = map_order_to_nodes(order, nodes);
    ASSERT_NE(std::find(all_orders.begin(), all_orders.end(), order_nodes), all_orders.end());
  }
}

TEST(graph, graph_run_throws) {
  std::vector<std::int32_t> order;
  std::mutex m;

  const auto add = [&](std::int32_t id) {
      std::unique_lock<std::mutex> lock{m};
      order.push_back(id);
    };

  node_sptr nodes[10];
  std::int32_t id = 0;
  for (auto & n : nodes) {
    n = node::make([&add, id] {
          add(id);
          if (id == 4) {throw std::runtime_error{"oops"};}
        });

    ++id;
  }

  ASSERT_TRUE(connect(nodes[0], nodes[1]));
  ASSERT_TRUE(connect(nodes[0], nodes[2]));
  ASSERT_TRUE(connect(nodes[0], nodes[3]));
  ASSERT_TRUE(connect(nodes[1], nodes[4]));
  ASSERT_TRUE(connect(nodes[2], nodes[5]));
  ASSERT_TRUE(connect(nodes[3], nodes[6]));
  ASSERT_TRUE(connect(nodes[4], nodes[7]));
  ASSERT_TRUE(connect(nodes[4], nodes[8]));
  ASSERT_TRUE(connect(nodes[5], nodes[8]));
  ASSERT_TRUE(connect(nodes[5], nodes[9]));
  ASSERT_TRUE(connect(nodes[6], nodes[9]));
  ASSERT_TRUE(connect(nodes[7], nodes[9]));
  ASSERT_TRUE(connect(nodes[8], nodes[9]));

  graph g{nodes[0]};

  ASSERT_EQ(g.get_node_count(), 10U);
  ASSERT_EQ(g.get_exec_queue_capacity(), 5U);
  ASSERT_EQ(g.get_max_branching_factor(), 3U);

  graph_runner r{g, g.get_max_branching_factor()};
  for (auto i = 0; i < 1000; ++i) {
    ASSERT_THROW(g.step(), std::runtime_error);

    ASSERT_TRUE(std::find(order.begin(), order.end(), 0) != order.end());
    ASSERT_TRUE(std::find(order.begin(), order.end(), 1) != order.end());
    ASSERT_TRUE(std::find(order.begin(), order.end(), 4) != order.end());

    // Note: 2, 3, 5 and 6 may or may not execute
    // 7, 8, 9 _must_ not execute
    for (std::int32_t nid = 7; nid < 10; ++nid) {
      ASSERT_TRUE(std::find(order.begin(), order.end(), nid) == order.end());
    }

    order.clear();
  }
}

TEST(graph, empty_roots) {
  graph g{std::vector<node_sptr>{}};
  graph_runner r{g};
  for (auto i = 0U; i < 10; ++i) {
    ASSERT_NO_THROW(g.step());
  }
}

TEST(graph, multiple_roots) {
  std::vector<std::int32_t> order;
  std::mutex m;

  const auto add = [&](std::int32_t id) {
      std::unique_lock<std::mutex> lock{m};
      order.push_back(id);
    };

  node_sptr nodes[12];
  std::int32_t id = 0;
  for (auto & n : nodes) {
    n = node::make([&add, id] {add(id);});
    ++id;
  }

  ASSERT_TRUE(connect(nodes[0], nodes[4]));
  ASSERT_TRUE(connect(nodes[0], nodes[3]));
  ASSERT_TRUE(connect(nodes[1], nodes[4]));
  ASSERT_TRUE(connect(nodes[2], nodes[4]));
  ASSERT_TRUE(connect(nodes[3], nodes[5]));
  ASSERT_TRUE(connect(nodes[4], nodes[6]));
  ASSERT_TRUE(connect(nodes[5], nodes[7]));
  ASSERT_TRUE(connect(nodes[6], nodes[7]));
  ASSERT_TRUE(connect(nodes[8], nodes[9]));
  ASSERT_TRUE(connect(nodes[8], nodes[11]));
  ASSERT_TRUE(connect(nodes[9], nodes[10]));
  ASSERT_TRUE(connect(nodes[10], nodes[11]));

  graph g{{nodes[0], nodes[1], nodes[2], nodes[8]}};
  const auto all_orders = get_all_topological_orders(g);
  graph_runner r{g, g.get_max_branching_factor()};

  for (auto i = 0; i < 1000; ++i) {
    g.step();
    ASSERT_EQ(order.size(), 12u);
    const auto order_nodes = map_order_to_nodes(order, nodes);
    ASSERT_NE(std::find(all_orders.begin(), all_orders.end(), order_nodes), all_orders.end());
    order.clear();
  }
}
