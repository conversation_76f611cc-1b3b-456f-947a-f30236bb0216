load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "executor2_pkg",
    cc_libraries = [
        ":executor2",
        ":executor2_noreplay",
    ],
    description = "Package containing Apex.Grace executor",
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/asil:asil_pkg",
        "//common/containers:containers_pkg",
        "//common/interrupt:interrupt_pkg",
        "//grace/execution/timer_service:timer_service_pkg",
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/monitoring/event:event_pkg",
        "//grace/monitoring/event_registry:event_registry_pkg",
        "//grace/monitoring/execution_monitor:execution_monitor_pkg",
        "//grace/monitoring/logging:logging_pkg",
        "//grace/recording/replay:replay_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/tools/ros2_tracing/tracetools:tracetools_pkg",
        "//grace/utils/apexcpp:apexcpp_pkg",
    ],
)

filegroup(
    name = "asil_d_headers",
    srcs = [
        "include/executor2/apex_node_base.hpp",
        "include/executor2/apex_node_base_ptr.hpp",
        "include/executor2/connector.hpp",
        "include/executor2/connector/common.hpp",
        "include/executor2/connector/signal.hpp",
        "include/executor2/connector/slot.hpp",
        "include/executor2/details/customization/execution_monitor.hpp",
        "include/executor2/details/customization/file_monitor.hpp",
        "include/executor2/details/customization/graph_runner_facility.hpp",
        "include/executor2/details/executor_task_base.hpp",
        "include/executor2/details/generic_factory.hpp",
        "include/executor2/details/graph_task_base.hpp",
        "include/executor2/details/item_task_base.hpp",
        "include/executor2/details/live/executor.hpp",
        "include/executor2/details/live/executor_task.hpp",
        "include/executor2/details/live/task_base.hpp",
        "include/executor2/details/node_details.hpp",
        "include/executor2/details/signal_executable_item.hpp",
        "include/executor2/details/slot_executable_item.hpp",
        "include/executor2/executable_item.hpp",
        "include/executor2/executable_item_ptr.hpp",
        "include/executor2/execution_monitor.hpp",
        "include/executor2/executor_base.hpp",
        "include/executor2/executor_base_ptr.hpp",
        "include/executor2/executor_constants.hpp",
        "include/executor2/executor_factory.hpp",
        "include/executor2/executor_runner.hpp",
        "include/executor2/graph.hpp",
        "include/executor2/graph_runner.hpp",
        "include/executor2/node.hpp",
        "include/executor2/thread_pool.hpp",
        "include/executor2/types.hpp",
        "include/executor2/vertex.hpp",
        "include/executor2/visibility.hpp",
    ],
    visibility = ["//visibility:public"],
)

filegroup(
    name = "asil_d_sources",
    srcs = [
        "src/axivion.cpp",
        "src/executable_item.cpp",
        "src/graph.cpp",
    ],
)

filegroup(
    name = "replay_headers",
    srcs = [
        "include/executor2/details/replay/executor.hpp",
        "include/executor2/details/replay/executor_task.hpp",
        "include/executor2/details/replay/task_base.hpp",
    ],
)

apex_cc_library(
    name = "executor2",
    srcs = [":asil_d_sources"],
    hdrs = [":asil_d_headers"] +
           select({
               "//common/asil:d": [],
               "//conditions:default": [":replay_headers"],
           }),
    applicable_licenses = ["//common/integrity:embedded_gold"],
    defines = select({
        "//common/asil:d": [],
        "//conditions:default": [
            "REPLAY_SUPPORT=TRUE",
        ],
    }),
    strip_include_prefix = "include",
    tags = [
        "integrity ASIL-D",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//common/asil",
        "//common/containers",
        "//common/interrupt",
        "//grace/execution/timer_service",
        "//grace/interfaces/std_msgs",
        "//grace/monitoring/event",
        "//grace/monitoring/event_registry",
        "//grace/monitoring/execution_monitor",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/tools/ros2_tracing/tracetools",
        "//grace/utils/apexcpp",
        "@coverage_tool//:coverage_io_lib",
    ] + select({
        "//common/asil:d": [],
        "//conditions:default": [
            "//grace/recording/replay",
        ],
    }),
)

# Usually, the executor2 package includes support for deterministic replay in the non-Cert build.
# This does not involve any runtime overhead, but might slightly increase build times due to
# the introduction of additional dependencies. Applications that do not need replay support can link
# with the `executor2_noreplay` target instead, which always comes without replay support.
# When in doubt, use the `executor2` target instead.
cc_library(
    name = "executor2_noreplay",
    srcs = [":asil_d_sources"],
    hdrs = [":asil_d_headers"],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "//common/asil",
        "//common/containers",
        "//common/interrupt",
        "//grace/execution/timer_service",
        "//grace/interfaces/std_msgs",
        "//grace/monitoring/event",
        "//grace/monitoring/event_registry",
        "//grace/monitoring/execution_monitor",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/tools/ros2_tracing/tracetools",
        "//grace/utils/apexcpp",
    ],
)

apex_cc_test(
    name = "test_executor_base_utils",
    srcs = [
        "test/test.cpp",
        "test/test_apex_node_base.cpp",
        "test/test_common.hpp",
        "test/test_connector.cpp",
        "test/test_executable_item.cpp",
        "test/test_executor_common.cpp",
        "test/test_executor_common.hpp",
        "test/test_executor_runner.cpp",
        "test/test_generic_factory.cpp",
        "test/test_graph.cpp",
        "test/test_graph_doc.cpp",
        "test/test_thread_pool.cpp",
        "test/test_vertex.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    flaky = True,
    includes = [
        "include",
        "test",
    ],
    # TODO(lander.usategui): Enable the test when #22814 is fixed
    tags = [
        "exclusive",
        "skip_coverage",
    ],
    deps = [
        ":executor2",
        "//grace/interfaces/test_msgs",
        "//tools/testing/apex_test_tools:apex_skip",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_executor",
    srcs = [
        "test/example_app.hpp",
        "test/test.cpp",
        "test/test_common.hpp",
        "test/test_executor_common.hpp",
        "test/test_executor_common.cpp",
        "test/test_executor.cpp",
        "test/test_executor_events.cpp",
        # TODO(hunter.allen): remove this source from QNX
        "test/test_executor_with_monitor.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    flaky = True,
    includes = [
        "include",
        "test",
    ],
    # TODO(team::os): Fix coverage issues #23706
    tags = [
        "exclusive",
        "skip_coverage",
    ],
    deps = [
        ":executor2",
        "//grace/interfaces/test_msgs",
        "//grace/monitoring/dispatcher_interfaces:dispatcher_common",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_replay_executor",
    srcs = [
        "test/example_app.hpp",
        "test/test.cpp",
        "test/test_common.hpp",
        "test/test_executor_common.cpp",
        "test/test_executor_common.hpp",
        "test/test_executor_doc.cpp",
        "test/test_replay_executor.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "exclusive",
    ],
    deps = [
        ":executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/replay_msgs",
        "//grace/interfaces/test_msgs",
        "//grace/recording/replay",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "test/test_executor_doc.cpp",
    ],
    visibility = [":__subpackages__"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [
        ":__subpackages__",
        "//grace/automotive_diagnose/diagnostic_fault_manager/doc:__subpackages__",
        "//grace/automotive_diagnose/fault_monitor/doc:__subpackages__",
    ],
)
