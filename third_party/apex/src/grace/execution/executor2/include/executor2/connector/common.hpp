/// \copyright Copyright 2024 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTOR2__CONNECTOR__CONNECTOR_COMMON_HPP
#define EXECUTOR2__CONNECTOR__CONNECTOR_COMMON_HPP

#include <unistd.h>

#include <std_msgs/msg/empty.hpp>
#include <executor2/executable_item.hpp>

#include <string>

namespace apex::executor::connector
{
/// \brief The default message type for connectors
/// \cert
/// \deterministic
using default_message_type = std_msgs::msg::Empty;

namespace details
{
/// \brief Gets topic base name
/// \cert
inline std::string get_topic_base_name(const std::string & topic)
{
  const auto last = topic.find_last_of('/');
  if (last != std::string::npos) {
    return topic.substr(last + 1);
  }
  return topic;
}
/// \brief Gets node namespace
/// If it is not specified explicitly -- it is taken from the topic
/// \cert
inline std::string get_node_namespace(const std::string & topic, const std::string & node_namespace)
{
  if (!node_namespace.empty()) {
    return node_namespace;
  }
  const auto last = topic.find_last_of('/');
  if (last != std::string::npos) {
    return topic.substr(0, last);
  }
  return "/";
}

/// Creates a string suffix unique to an object of executable_item and current process
/// \cert
inline std::string get_unique_suffix(const executable_item & item)
{
  /*
   AXIVION Next Line MisraC++2023-8.2.5, MisraC++2023-8.2.7: Reason: Code Quality (Functional
   suitability), Justification: cast used here has
   no side-effects.
   */
  return std::to_string(reinterpret_cast<std::uintptr_t>(&item)) + std::to_string(::getpid());
}

}  // namespace details
}  // namespace apex::executor::connector

#endif  // EXECUTOR2__CONNECTOR__CONNECTOR_COMMON_HPP
