/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTOR2__DETAILS__EXECUTOR_BASE_HPP_
#define EXECUTOR2__DETAILS__EXECUTOR_BASE_HPP_

#include <executor2/executor_base_ptr.hpp>
#include <executor2/types.hpp>
#include <executor2/executable_item.hpp>
#include <executor2/vertex.hpp>
#include <executor2/executor_constants.hpp>
#include <executor2/details/executor_task_base.hpp>
#include <executor2/details/generic_factory.hpp>
#include <executor2/graph_runner.hpp>
#include <executor2/details/customization/graph_runner_facility.hpp>
#include <executor2/details/customization/file_monitor.hpp>
#include <executor2/details/customization/execution_monitor.hpp>

#include <timer_service/timer_service_interface.hpp>
#include <threading/thread_attributes.hpp>
#include <cpputils/common_exceptions.hpp>
#include <cpputils/optional.hpp>
#include <cpputils/safe_cast.hpp>
#include <rclcpp/dynamic_waitset/waitset.hpp>
#include <rclcpp/dynamic_waitset/guard_condition.hpp>
#include <event/sender_factory_base.hpp>
#include <event/sender_base.hpp>
#include <event_registry/event_registry.hpp>
#include <logging/logging.hpp>
#include <logging/logging_macros.hpp>
#include <tracetools/tracetools.h>

#include <type_traits>
#include <atomic>
#include <cassert>
#include <cstdint>
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <unordered_map>

namespace apex
{
namespace executor
{

class executor_base;

namespace details
{
/// \brief Type trait for any kind of std::vector
/// \cert
template<class>
struct is_std_vector : std::false_type {};

template<class T, class ... A>
struct is_std_vector<std::vector<T, A...>>: std::true_type {};

template<class T>
constexpr bool is_std_vector_v = is_std_vector<T>::value;

template<class, class = void>
struct is_item : std::false_type {};

template<class T>
struct is_item<T,
  std::enable_if_t<std::is_convertible_v<T, vertex>|| std::is_convertible_v<T,
  executable_item_ptr>>>
  : std::true_type {};

/// \brief Is true only if the type of the object is a legal public API argument to
/// an add() function
/// \cert
template<class T>
constexpr bool is_item_v = is_item<T>::value;

/// \brief Returns true only if the type of the vector is a legal public API argument to
/// an add() function
/// \cert
template<class T>
constexpr bool is_vector_of_items_v()
{
  if constexpr (is_std_vector_v<T>) {
    return std::is_same_v<typename T::value_type, executable_item_ptr>;
  } else {
    return false;
  }
}

/// \brief A helper constant for using with if constexpr + static_assert.
/// Introduces a deductible context
/// \cert
template<class ...>
constexpr std::false_type always_false{};

/// \class executable_item_with_timer
/// Wraps an executable item with a timer
/// Exclusively used by the add_periodic() and add_with_timeout().
/// The constructor is private and only accessible for executor base to prevent
/// surprise behaviors that could arise if manual wrapping was allowed.
/// This class implements both periodic and timeout scenarios.
/// In the timeout scenario the timer will not be rescheduled if execute_impl()
/// return false but will be on any successful execution
/// \cert
/// \deterministic
class executable_item_with_timer : public executable_item
{
  friend class ::apex::executor::executor_base;

  /// \brief Enables sending execution events
  /// \param sender The sender for using in this executable item
  /// \cert
  /// \deterministic
  void set_event_sender(apex::event::sender_ptr sender) override
  {
    m_event_sender = sender;
    m_item->set_event_sender(std::move(sender));
  }

  /// \brief Sets the executor id
  /// \param executor_id The executor id
  /// \cert
  /// \deterministic
  void set_executor_id(executor_id_t executor_id) noexcept override
  {
    m_executor_id = executor_id;
    m_item->set_executor_id(executor_id);
  }

  /// \brief Gets the executor id
  /// \return The executor id
  /// \cert
  /// \deterministic
  executor_id_t get_executor_id() const noexcept override
  {
    return m_item->get_executor_id();
  }

private:
  /// \brief Wraps an executable item with a timer
  /// \param item The executable item to wrap into timed execution
  /// \param timer The timer to work with
  /// \param timeout_offset The offset from now for rescheduling the timer.
  /// A non-zero value indicates a timeout timer, otherwise -- periodic
  /// \cert
  /// \deterministic
  executable_item_with_timer(
    executable_item_ptr item,
    apex::timer_service::timer_ptr timer,
    std::chrono::nanoseconds timeout_offset)
  : executable_item{item->get_rclcpp_node()},
    m_item{std::move(item)},
    m_timer{std::move(timer)},
    m_timeout_offset{timeout_offset},
    m_logger{&get_rclcpp_node()},
    m_timer_topic_name{m_timer->to_sub_ptr()->get_topic_name()}
  {
    if (m_timeout_offset != std::chrono::nanoseconds::zero()) {
      m_timer->reschedule_from_now(m_timeout_offset);
    }
  }

  /// \brief The implementation of `execute()`
  /// First collects timer events, if were more than one they are considered
  /// missed events and logged as such. In any case, the underlying `execute_impl()`
  /// is called only once. If it returns `false`, the timeout timer is not rescheduled
  /// and the function returns `false` too.
  /// \return Whether the wrapped `execute_impl()` call returned `true`
  /// \cert
  /// \deterministic if the wrapped item is deterministic
  bool execute_impl() override
  {
    const auto times = m_timer->count_and_reset();
    if (times > 1U) {
      APEX_WARN(m_logger, apex::no_separator{},
        "Missed ", times - 1U, " timer events in the executable item: '",
        get_id(), "', in executor: ", get_executor_id(),
        ", on timer topic: '", m_timer_topic_name, "'");
    }

    if (!m_item->execute_impl()) {
      return false;
    }

    if (m_timeout_offset != std::chrono::nanoseconds::zero()) {
      m_timer->reschedule_from_now(m_timeout_offset);
    }
    return true;
  }

  /// \brief The implementation of get_triggering_subscriptions_impl()
  /// \return Returns the timer subscription and whatever the wrapped item returns
  /// \cert
  subscription_list get_triggering_subscriptions_impl() const override
  {
    auto list = m_item->get_triggering_subscriptions_impl();
    subscription_list ret;
    ret.reserve(list.size() + 1U);
    ret.push_back(m_timer->to_sub_ptr());
    std::move(list.begin(), list.end(), std::back_inserter(ret));
    return ret;
  }

  /// \brief The implementation of get_non_triggering_subscriptions_impl()
  /// \return Returns whatever the wrapped item returns
  /// \cert
  subscription_list get_non_triggering_subscriptions_impl() const override
  {
    return m_item->get_non_triggering_subscriptions_impl();
  }

  /// \brief The implementation of get_publishers_impl()
  /// \return Returns whatever the wrapped item returns
  /// \cert
  publisher_list get_publishers_impl() const override
  {
    return m_item->get_publishers_impl();
  }

  /// \brief The implementation of get_file_descriptors_impl()
  /// \return Returns whatever the wrapped item returns
  /// \cert
  file_descriptor_list get_file_descriptors_impl() const override
  {
    return m_item->get_file_descriptors_impl();
  }

  /// \brief The implementation of get_triggering_services_impl()
  /// \return Returns whatever the wrapped item returns
  /// \cert
  service_list get_triggering_services_impl() const override
  {
    return m_item->get_triggering_services_impl();
  }

  /// \brief The implementation of get_triggering_clients_impl()
  /// \return Returns whatever the wrapped item returns
  /// \cert
  client_list get_triggering_clients_impl() const override
  {
    return m_item->get_triggering_clients_impl();
  }

  /// \brief Returns the related application id
  /// \return Returns whatever the wrapped item returns
  /// \cert
  /// \deterministic if implementation is deterministic
  std::string_view get_app_id_impl() const noexcept override
  {
    return m_item->get_app_id_impl();
  }

  /// \brief Returns the related application instance id
  /// \return Returns whatever the wrapped item returns
  /// \cert
  /// \deterministic if implementation is deterministic
  apex::event::types::instance_id_t get_app_instance_id_impl() const noexcept override
  {
    return m_item->get_app_instance_id_impl();
  }

  executable_item_ptr m_item;
  timer_service::timer_ptr m_timer;
  std::chrono::nanoseconds m_timeout_offset;
  apex::logging::Logger<> m_logger;
  const std::string m_timer_topic_name;
};
}  // namespace details

/// \brief The setting for graph_runners
/// \cert
struct graph_runner_settings
{
  std::size_t thread_count{0U};
  threading::thread_attributes thread_attributes = threading::thread_attributes::build();
};

/// class executor_base
/// The base class for executors
/// \param event_sender_factory A factory for creating event senders
/// \cert
class executor_base
{
public:
  /// Creates a new executor and registers the interrupt event as the first one
  /// \param id An id of the executor
  /// \cert
  executor_base(executor_id_t id, apex::event::sender_factory_ptr event_sender_factory)
  : m_id{id},
    m_event_sender_factory{std::move(event_sender_factory)}
  {
    if (m_event_sender_factory != nullptr) {
      m_event_sender = m_event_sender_factory->create_sender(m_id);
    }

    //lint -e{9175} : empty functor is intended NOLINT
    // empty functor is intended
    (void) m_ws.add(m_stop, [] {});

    m_generic_factory.register_type<custom::graph_runner_facility>();
    set_graph_runner_facility();

    TRACETOOLS_TRACEPOINT(
      executor_create,
      static_cast<const void *>(this),
      static_cast<const uint64_t>(m_id),
      static_cast<const void *>(&m_ws));
  }

  /// Creates a new executor and registers the interrupt event as the first one
  /// \param id An id of the executor
  /// \param num_of_threads The number of threads to dispatch events on
  /// \param attr The attributes of threads to dispatch events on
  /// \param event_sender_factory A factory for creating event senders
  /// \cert
  executor_base(
    executor_id_t id,
    std::size_t num_of_threads,
    apex::threading::thread_attributes attr,
    apex::event::sender_factory_ptr event_sender_factory)
  : executor_base{id, std::move(event_sender_factory)}
  {
    m_thread_pool.emplace(num_of_threads, std::move(attr));
  }

  executor_base(const executor_base &) = delete;
  executor_base & operator=(const executor_base &) = delete;
  virtual ~executor_base() = default;

  /// \brief Adds a single-item task to the executor
  /// \param item The executable item executed by the task
  /// \returns A task identifier for the newly-created task
  /// \cert
  task_id_cref_t add(executable_item_ptr item)
  {
    return add_impl(task_id_t{}, std::vector<executable_item_ptr>{std::move(item)});
  }

  /// \brief Adds a single-item task to the executor
  /// \param task_id A user-defined id of the task
  /// \param item The executable item executed by the task
  /// \returns A task identifier for the newly-created task
  /// \cert
  task_id_cref_t add(task_id_cref_t task_id, executable_item_ptr item)
  {
    return add_impl(task_id, std::vector<executable_item_ptr>{std::move(item)});
  }

  /// \brief Adds a chain task to the executor
  /// \param items The executable items executed by the task
  /// \returns A task identifier for the newly-created task
  /// \cert
  task_id_cref_t add(std::vector<executable_item_ptr> items)
  {
    return add_impl(task_id_t{}, std::move(items));
  }

  /// \brief Adds a chain task to the executor
  /// \param task_id A user-defined id of the task
  /// \param items The executable items executed by the task
  /// \returns A task identifier for the newly-created task
  /// \cert
  task_id_cref_t add(task_id_cref_t task_id, std::vector<executable_item_ptr> items)
  {
    return add_impl(task_id, std::move(items));
  }

  /// \brief Adds a graph task to the executor
  /// \param root The root vertex of the graph to add to the executor
  /// \returns A task identifier for the newly-created task
  /// \cert
  task_id_cref_t add(vertex root)
  {
    return add_impl(task_id_t{}, std::vector<vertex>{std::move(root)});
  }

  /// \brief Adds a graph task to the executor
  /// \param root The root vertex of the graph to add to the executor
  /// \returns A task identifier for the newly-created task
  /// \cert
  task_id_cref_t add(vertex root, graph_runner_settings runner_settings)
  {
    m_graph_runner_settings[size()] = std::move(runner_settings);
    return add_impl(task_id_t{}, std::vector<vertex>{std::move(root)});
  }

  /// \brief Adds a graph task to the executor
  /// \param task_id A user-defined id of the task
  /// \param root The root vertex of the graph to add to the executor
  /// \returns A task identifier for the newly-created task
  /// \cert
  task_id_cref_t add(task_id_cref_t task_id, vertex root)
  {
    return add_impl(task_id, std::vector<vertex>{std::move(root)});
  }

  /// \brief Adds a graph task to the executor
  /// \param task_id A user-defined id of the task
  /// \param root The root vertex of the graph to add to the executor
  /// \returns A task identifier for the newly-created task
  /// \cert
  task_id_cref_t add(
    task_id_cref_t task_id,
    vertex root,
    graph_runner_settings runner_settings)
  {
    m_graph_runner_settings[size()] = std::move(runner_settings);
    return add_impl(task_id, std::vector<vertex>{std::move(root)});
  }

  /// \brief Wraps the first item of the task into a periodic timer-based item
  /// and adds the task to the executor
  /// \param task_id A user-defined id of the task
  /// \param item The executable item, vertex or a vector of items to wrap and add as a task
  /// \param timer_srv The times service to create the timer
  /// \param start The time offset for starting the timer
  /// \param period The period of the timer (0 for one-shot timers)
  /// \param args Additional arguments to forward to the add() call (like graph_runner_settings)
  /// \returns A task identifier for the newly-created task
  /// \cert
  template<class Item, class R1, class P1, class R2, class P2, class ... Args>
  task_id_cref_t add_periodic(
    task_id_cref_t task_id,
    Item item,
    apex::timer_service::timer_service_interface & timer_srv,
    std::chrono::duration<R1, P1> start,
    std::chrono::duration<R2, P2> period,
    Args && ... args)
  {
    auto timer = timer_srv.create_timer(start, period);
    return add_timer_based_task(
      task_id,
      std::move(item),
      std::move(timer),
      std::chrono::nanoseconds::zero(),
      std::forward<Args>(args)...);
  }

  /// \brief Wraps the first item of the task into a periodic timer-based item
  /// and adds the task to the executor, auto-generating the id for the task
  /// \param item The executable item, vertex or a vector of items to wrap and add as a task
  /// \param timer_srv The times service to create the timer
  /// \param start The time offset for starting the timer
  /// \param period The period of the timer (0 for one-shot timers)
  /// \param args Additional arguments to forward to the add() call (like graph_runner_settings)
  /// \returns A task identifier for the newly-created task
  /// \cert
  template<class Item, class R1, class P1, class R2, class P2, class ... Args>
  task_id_cref_t add_periodic(
    Item item,
    apex::timer_service::timer_service_interface & timer_srv,
    std::chrono::duration<R1, P1> start,
    std::chrono::duration<R2, P2> period,
    Args && ... args)
  {
    return add_periodic(task_id_t{}, std::move(item), timer_srv, start, period,
             std::forward<Args>(args)...);
  }

  /// \brief Wraps the first item of the task into a periodic timer-based item
  /// and adds the task to the executor. The timer is triggered for the first time immediately.
  /// \param task_id A user-defined id of the task
  /// \param item The executable item, vertex or a vector of items to wrap and add as a task
  /// \param timer_srv The times service to create the timer
  /// \param period The period of the timer (0 for one-shot timers)
  /// \param args Additional arguments to forward to the add() call (like graph_runner_settings)
  /// \returns A task identifier for the newly-created task
  /// \cert
  template<class Item, class R, class P, class ... Args>
  task_id_cref_t add_periodic(
    task_id_cref_t task_id,
    Item item,
    apex::timer_service::timer_service_interface & timer_srv,
    std::chrono::duration<R, P> period,
    Args && ... args)
  {
    return add_periodic(task_id, std::move(item), timer_srv,
             std::chrono::nanoseconds::zero(), period, std::forward<Args>(args)...);
  }

  /// \brief Wraps the first item of the task into a periodic timer-based item
  /// and adds the task to the executor, auto-generating the id for the task.
  /// The timer is triggered for the first time immediately.
  /// \param item The executable item, vertex or a vector of items to wrap and add as a task
  /// \param timer_srv The times service to create the timer
  /// \param period The period of the timer (0 for one-shot timers)
  /// \param args Additional arguments to forward to the add() call (like graph_runner_settings)
  /// \returns A task identifier for the newly-created task
  /// \cert
  template<class Item, class R, class P, class ... Args>
  task_id_cref_t add_periodic(
    Item item,
    apex::timer_service::timer_service_interface & timer_srv,
    std::chrono::duration<R, P> period,
    Args && ... args)
  {
    return add_periodic(task_id_t{}, std::move(item), timer_srv,
             std::chrono::nanoseconds::zero(), period, std::forward<Args>(args)...);
  }

  /// \brief Wraps the first item of the task into a timeout timer-based item
  /// and adds the task to the executor
  /// \param task_id A user-defined id of the task
  /// \param item The executable item, vertex or a vector of items to wrap and add as a task
  /// \param timer_srv The times service to create the timer
  /// \param timeout The timeout to trigger the timer if no other triggers happened
  /// \param args Additional arguments to forward to the add() call (like graph_runner_settings)
  /// \returns A task identifier for the newly-created task
  /// \cert
  template<class Item, class R, class P, class ... Args>
  task_id_cref_t add_with_timeout(
    task_id_cref_t task_id,
    Item item,
    apex::timer_service::timer_service_interface & timer_srv,
    std::chrono::duration<R, P> timeout,
    Args && ... args)
  {
    auto timer = timer_srv.create_timer();
    return add_timer_based_task(
      task_id,
      std::move(item),
      std::move(timer),
      timeout,
      std::forward<Args>(args)...);
  }

  /// \brief Wraps the first item of the task into a timeout timer-based item
  /// and adds the task to the executor, auto-generating the id for the task
  /// \param item The executable item, vertex or a vector of items to wrap and add as a task
  /// \param timer_srv The times service to create the timer
  /// \param timeout The timeout to trigger the timer if no other triggers happened
  /// \param args Additional arguments to forward to the add() call (like graph_runner_settings)
  /// \returns A task identifier for the newly-created task
  /// \cert
  template<class Item, class R, class P, class ... Args>
  task_id_cref_t add_with_timeout(
    Item item,
    apex::timer_service::timer_service_interface & timer_srv,
    std::chrono::duration<R, P> timeout,
    Args && ... args)
  {
    return add_with_timeout(task_id_t{}, std::move(item), timer_srv,
             timeout, std::forward<Args>(args)...);
  }

  /// \brief Runs the executor until it is stopped or until an error happens
  /// \param timeout Maximum of how long to wait for a next event
  /// \param max_iterations How many iterations to run
  /// \note This method is not thread-safe. Only one thread may run the executor at any time
  /// \cert
  /// \deterministic Note, this method is not deterministic on its init and cleanup.
  /// However, internally, its main loop called from separate method `run_internal()`
  /// which is deterministic. In this regard this method behaves like the `main()` for executor.
  /// This behavior is documented
  void run(
    std::chrono::nanoseconds timeout = InfiniteWait,
    std::size_t max_iterations = InfiniteIterations)
  {
    TRACETOOLS_TRACEPOINT(executor_run_start, static_cast<const void *>(this));
    assert(m_running != nullptr);
    m_running->set_value(true);
    const auto deleter = [](auto running) {
        running->set_value(false);
        TRACETOOLS_TRACEPOINT(executor_run_end);
      };
    // Use of underlying type of lambda is intentional
    using guard_type =
      std::unique_ptr<rclcpp::dynamic_waitset::GuardCondition, decltype(deleter)>;
    const guard_type guard{m_running.get(), deleter};

    send_execution_event(apex::event::ExecutorUp);
    on_tasks_start();
    try {
      run_internal(timeout, max_iterations);
      // Intended catch and re-throw
    } catch (const std::exception & e) {
      on_tasks_finished(false);
      send_execution_event(apex::event::ExecutorError, e.what());
      throw;
      // Intended catch and re-throw
    } catch (...) {
      on_tasks_finished(false);
      send_execution_event(apex::event::ExecutorError);
      throw;
    }
    on_tasks_finished(true);
    send_execution_event(apex::event::ExecutorDown);
  }

  /// \param Waits until the run function is actually running
  /// \param timeout How long to wait
  /// \return Whether the waiting was successful
  /// \cert
  bool wait_for_running(std::chrono::nanoseconds timeout = InfiniteWait)
  {
    rclcpp::dynamic_waitset::Waitset ws{m_running,
      rclcpp::dynamic_waitset::ThreadSafety::NOT_THREAD_SAFE};
    return ws.wait(timeout);
  }

  /// \brief Stops the run() loop, ensuring that the executor does not take on any new work
  ///
  /// stop() does not wait for the executor to come to a stop; it signals a stop command to the
  /// executor and returns immediately.
  /// Note that stop() does not interrupt triggered executor tasks. The executor will not stop
  /// until the tasks triggered by the previous call to dynamic_waitset::wait() complete.
  /// \cert
  /// \deterministic
  void stop() const
  {
    assert(m_stop != nullptr);
    m_stop->set_value(true);
  }

  /// \brief Gets the total number of iterations of the executor until now
  /// \return The total number of iterations of the executor until now
  /// \cert
  /// \deterministic
  /// \note Intended to skip the determinism verification.
  /// Because the determinism was reviewed through code inspection.
  std::size_t get_total_iterations() const noexcept{return m_total_iterations;}

  /// \brief Returns number of tasks in the executor
  /// \return The number of tasks in the executor
  /// \cert
  /// \deterministic
  std::size_t size() const noexcept
  {
    return m_tasks.size();
  }

  /// \brief Gets the ID of this executor
  /// \cert
  /// \deterministic
  executor_id_t get_id() const noexcept
  {
    return m_id;
  }

protected:
  /// \brief Runs the executor until it is stopped or until an error happens
  /// \param timeout Maximum of how long to wait for a next event
  /// \param max_iterations How many iterations to run
  /// \cert
  /// \deterministic
  void run_internal(std::chrono::nanoseconds timeout, std::size_t max_iterations)
  {
    std::size_t i{0U};
    while ((max_iterations == InfiniteIterations) || (i < max_iterations)) {
      wait_and_dispatch(timeout);
      if (m_total_iterations == std::numeric_limits<std::size_t>::max()) {
        m_total_iterations = 0U;
      }

      (void) ++m_total_iterations;
      if (m_ws[m_stop]) {
        break;
      }
      ++i;
    }
  }

  /// \brief Sends an execution event
  /// \param event The event to send
  /// \cert
  /// \deterministic
  void send_execution_event(apex::event::event_id_t event)
  {
    if (m_event_sender) {
      m_event_sender->send(event);
    }
  }

  /// \brief Sends an execution event with a text
  /// \param event The event to send
  /// \param str The text to attach to the event
  /// \cert
  /// \deterministic
  void send_execution_event(apex::event::event_id_t event, const char * str)
  {
    assert(str != nullptr);
    if (m_event_sender) {
      m_event_sender->send(event, str);
    }
  }

  /// \brief Dispatches the events either on the current thread or on a thread pool if one exists
  /// \param timeout Maximum of how long to wait for an event
  /// \cert
  /// \deterministic if callables are deterministic
  void wait_and_dispatch(std::chrono::nanoseconds timeout)
  {
    if (m_thread_pool != apex::nullopt) {
      if (m_ws.wait_and_dispatch_all(timeout, *m_thread_pool)) {
        return;
      }
    } else {
      if (m_ws.wait_and_dispatch_all(timeout)) {
        return;
      }
    }

    throw apex::runtime_error{apex::no_separator{},
            "timeout in executor: ", get_id(), " after ",
            std::chrono::duration_cast<std::chrono::milliseconds>(timeout).count(), " ms",
            ": no event triggered the execution of any executable item."
            " Check the execution conditions of the executable items added to this executor"
    };
  }

  /// \brief Called when entering run()
  /// \cert
  void on_tasks_start()
  {
    if (m_total_iterations == 0U) {
      if (m_generic_factory.is_invocable<custom::file_monitor>()) {
        auto & fm = *m_generic_factory.invoke<custom::file_monitor>();
        TRACETOOLS_TRACEPOINT(
          executor_file_monitor_init,
          static_cast<const void *>(this),
          static_cast<const void *>(&fm));
        m_ws.add_background_worker(fm);
      }
      if (m_generic_factory.is_invocable<custom::execution_monitor>()) {
        const auto monitor = m_generic_factory.invoke<custom::execution_monitor>();
        for (const auto & task : m_tasks) {
          if (!monitor->is_registered(task->get_id())) {
            throw apex::logic_error(apex::no_separator{},
                    "the execution monitor does not define "
                    "any expectation for task: '", apex::to_string(task->get_id()),
                    "', in executor: ", get_id());
          }
        }
        monitor->issue();
      }
      for (const auto & task : m_tasks) {
        // tasks cannot be null at this point
        task->pre_run_hook();
      }
    }
  }

  /// \brief Called when leaving run()
  /// \param success Whether the run was successful
  /// \cert
  void on_tasks_finished(bool success) noexcept
  {
    for (const auto & task : m_tasks) {
      // tasks cannot be null at this point
      task->post_run_hook(success);
    }
  }

  /// \brief Adds a task to the executor and registers its events in
  /// the waitset
  /// \param task_id A user-defined id of the task
  /// \param task A task to add
  /// \returns A task identifier for the newly-created task
  /// It is either the task_id or a string representation of the address
  /// of the task if the task_id's value is an empty string
  /// \cert
  task_id_cref_t add(task_id_cref_t task_id, details::executor_task_base_ptr task)
  {
    m_tasks.push_back(std::move(task));
    // tasks cannot be null at this point, they are created when calling add()
    auto & new_task = *m_tasks.back();
    new_task.setup(m_id, task_id, m_ws, m_event_sender_factory);
    TRACETOOLS_TRACEPOINT(
      executor_add_task,
      static_cast<const void *>(this),
      static_cast<const void *>(&new_task),
      m_tasks.size());
    return new_task.get_id();
  }

private:
  /// \brief Wraps the first item of the task into a timer-based item
  /// and adds the task to the executor
  /// \param task_id A user-defined id of the task
  /// \param item The executable item, vertex or a vector of items to wrap and add as a task
  /// \param timer The timer to use
  /// \param timeout The timeout to trigger the timer if no other triggers happened
  /// (0 for periodic timers)
  /// \param args Additional arguments to forward to the add() call (like graph_runner_settings)
  /// \returns A task identifier for the newly-created task
  /// \cert
  template<class Item, class R, class P, class ... Args>
  task_id_cref_t add_timer_based_task(
    task_id_cref_t task_id,
    Item item,
    apex::timer_service::timer_ptr timer,
    std::chrono::duration<R, P> timeout,
    Args && ... args)
  {
    using details::executable_item_with_timer;
    constexpr auto make_item = [](executable_item_ptr item, auto... args) {
        if (item == nullptr) {
          throw apex::runtime_error{"an executable item pointer cannot be nullptr"};
        }
        return std::shared_ptr<executable_item_with_timer>(
          new executable_item_with_timer(std::move(item),
          std::forward<decltype(args)>(args)...));
      };

    if constexpr (details::is_item_v<Item>) {
      if constexpr (std::is_same_v<Item, vertex>) {
        vertex v{make_item(std::move(item.m_state->m_item), std::move(timer), timeout)};
        v.m_state->m_computational_node->adj = item.m_state->m_computational_node->adj;
        v.m_state->m_adj = item.m_state->m_adj;
        return add(task_id, std::move(v), std::forward<Args>(args)...);
      } else {
        auto timer_item = make_item(std::move(item), std::move(timer), timeout);
        return add(task_id, std::move(timer_item), std::forward<Args>(args)...);
      }
    } else if constexpr (details::is_vector_of_items_v<Item>()) {  // NOLINT
      item[0] = make_item(std::move(item[0]), std::move(timer), timeout);
      return add(task_id, std::move(item), std::forward<Args>(args)...);
    } else {
      static_assert(details::always_false<Item>, "illegal item type");
    }
  }

  /// \brief Adds a chain task to the executor
  /// \param task_id A user-defined id of the task
  /// \param items The executable items executed by the task
  /// \returns A task identifier for the newly-created task
  /// \cert
  virtual task_id_cref_t add_impl(
    task_id_cref_t task_id,
    std::vector<executable_item_ptr> items) = 0;

  /// \brief Adds a graph task to the executor
  /// \param task_id A user-defined id of the task
  /// \param roots The root vertices of the graph
  /// \returns A task identifier for the newly-created task
  /// \cert
  virtual task_id_cref_t add_impl(
    task_id_cref_t task_id, std::vector<vertex> roots) = 0;

  /// \brief Install the graph runner facility which takes into account
  /// settings provided by users for the particular graph
  /// \cert
  void set_graph_runner_facility()
  {
    m_generic_factory.set<custom::graph_runner_facility>(  // NOLINT
      [this](std::size_t index, graph * graph_to_run, const void * task) {
        const auto & settings = m_graph_runner_settings[index];
        const auto thread_count = (settings.thread_count == 0U) ?
        graph_to_run->get_max_branching_factor() : settings.thread_count;
        m_graph_runners.emplace_back(*graph_to_run, settings.thread_attributes, thread_count);

#ifndef TRACETOOLS_DISABLED
        if (TRACETOOLS_TRACEPOINT_ENABLED(executor_task_graph_thread_pool_init)) {
          const auto thread_pool = &m_graph_runners.back();
          TRACETOOLS_DO_TRACEPOINT(
            executor_task_graph_thread_pool_init,
            task,
            static_cast<const void *>(thread_pool));
        }
#else
        (void) task;
#endif  // TRACETOOLS_DISABLED
      });
  }

protected:
  details::generic_factory m_generic_factory;

private:
  executor_id_t m_id;
  apex::event::sender_factory_ptr m_event_sender_factory;
  apex::event::sender_ptr m_event_sender;
  std::vector<details::executor_task_base_ptr> m_tasks;
  rclcpp::dynamic_waitset::Waitset m_ws{rclcpp::dynamic_waitset::ThreadSafety::NOT_THREAD_SAFE};
  apex::optional<rclcpp::dynamic_waitset::ThreadPool> m_thread_pool;

  std::shared_ptr<rclcpp::dynamic_waitset::GuardCondition> m_stop{
    std::make_shared<rclcpp::dynamic_waitset::GuardCondition>()
  };

  std::shared_ptr<rclcpp::dynamic_waitset::GuardCondition> m_running{
    std::make_shared<rclcpp::dynamic_waitset::GuardCondition>()
  };

  std::atomic<std::size_t> m_total_iterations{0U};
  std::unordered_map<std::size_t, graph_runner_settings> m_graph_runner_settings;
  std::vector<graph_runner> m_graph_runners;
};

}  // namespace executor
}  // namespace apex

#endif  // EXECUTOR2__DETAILS__EXECUTOR_BASE_HPP_
