/// \copyright Copyright 2017-2021 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTOR2__THREAD_POOL_HPP_
#define EXECUTOR2__THREAD_POOL_HPP_

#include <threading/thread.hpp>
#include <tracetools/tracetools.h>

#include <thread>
#include <vector>
#include <utility>

namespace apex
{
namespace executor
{
/// \class thread_pool
/// Represents a threading model of a Runnable execution
///
/// The concept of Runnable requires three methods to be defined:
/// 1. `void run()` which runs the execution until the `stop()` method is called
/// 2. `void stop()` which can be called concurrently to `run()` and should interrupt its execution
/// 3. `void reset_run_state()` which being called after `stop()` prepares the object
/// for a new `run()` call
/// It is guaranteed that `reset_run_state()` will not be called concurrently.
/// The other two methods must be thread-safe.
///
/// The Runnable object is reusable. The thread_pool itself is not.
/// \cert
template<class Runnable>
class thread_pool final
{
public:
  /// \brief Creates and starts all the worker threads
  /// \param r A runnable to run
  /// \param attr Attributes which are applied to all threads in the pool
  /// \param num_of_threads The number of threads in the pool (by default
  /// std::thread::hardware_concurrency()) * 2U)
  /// \cert
  thread_pool(
    Runnable & r,
    threading::thread_attributes attr,
    std::size_t num_of_threads =
    static_cast<std::size_t>(std::thread::hardware_concurrency()) * 2U)
  : m_runnable{&r}
  {
    m_runnable->reset_run_state();
    const auto runnable = m_runnable;
    const auto this_ = this;
    for (auto i = 0U; i < num_of_threads; ++i) {
      m_pool.emplace_back([runnable, this_] {
          TRACETOOLS_TRACEPOINT(thread_pool_run, static_cast<const void *>(this_));
          runnable->run();
        }, attr);
      m_pool.back().issue();
    }
  }

  /// \brief Creates and starts all the worker threads
  /// \param r A runnable to run
  /// \param num_of_threads The number of threads in the pool
  /// \cert
  explicit thread_pool(
    Runnable & r,
    std::size_t num_of_threads =
    static_cast<std::size_t>(std::thread::hardware_concurrency()) * 2U)
  : thread_pool{r, threading::thread_attributes::build(), num_of_threads}
  {}

  /// \brief Creates and starts all the worker threads
  /// \param r A runnable to run
  /// \param attrs Attributes which are applied to the threads with the same index
  /// \cert
  thread_pool(Runnable & r, std::vector<threading::thread_attributes> attrs)
  : m_runnable{&r}
  {
    m_runnable->reset_run_state();

    for (auto & attr : attrs) {
      const auto runnable = m_runnable;
      const auto this_ = this;
      m_pool.emplace_back([runnable, this_] {
          TRACETOOLS_TRACEPOINT(thread_pool_run, static_cast<const void *>(this_));
          runnable->run();
        }, std::move(attr));
      m_pool.back().issue();
    }
  }

  thread_pool(const thread_pool &) = delete;
  thread_pool & operator=(const thread_pool &) = delete;
  thread_pool(thread_pool &&) noexcept = default;
  thread_pool & operator=(thread_pool &&) noexcept = default;

  /// \brief Destroys the runner by calling the stop() method of the Runnable
  /// and subsequently joining the worker threads
  /// The destructor may call std::terminate() if there are unprocessed exceptions on any thread
  /// \cert
  // terminating if there are unprocessed exceptions is the intended behavior
  ~thread_pool()
  {
    stop();
  }

  /// \brief Stops the runner by calling the stop() method of the Runnable
  /// and subsequently joining the worker threads.
  /// Only the first exception is saved and rethrown if any of the worker threw.
  /// After this method is called the object cannot be reused
  /// \cert
  void stop()
  {
    if (!m_pool.empty()) {
      m_runnable->stop();
      std::exception_ptr err;
      for (auto & t : m_pool) {
        if (t.joinable()) {
          try {
            t.join();
            //lint -e{1766} : catch(...) is intended NOLINT
          } catch (...) {
            if (!err) {
              err = std::current_exception();
            }
          }
        }
      }
      if (err) {
        std::rethrow_exception(err);
      }
    }
  }

  /// \brief Returns the number of worker threads
  /// \return The number of worker threads
  /// A moved from object will return 0
  /// \cert
  /// \deterministic
  std::size_t get_concurrency() const noexcept
  {
    return m_pool.size();
  }

private:
  std::vector<apex::threading::thread> m_pool;
  Runnable * m_runnable;
};

}  // namespace executor
}  // namespace apex

#endif  // EXECUTOR2__THREAD_POOL_HPP_
