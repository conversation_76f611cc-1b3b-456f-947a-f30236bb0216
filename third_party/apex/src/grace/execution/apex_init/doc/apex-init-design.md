# apex_init

## Purpose / Use cases

The `apex_init` package provides features to:

1. Set up the Apex.Grace and ROS frameworks
1. Clearly separate the initialization and steady state phases
1. Perform optimizations for improved timing predictability
1. Configure real-time settings

## Usage

In an Apex.Grace application, an [](apex::scoped_init) object should be created when the process is
started, before any node initialization. In most cases, this will be part of the `main()` function.

After all initialization is complete, the [](apex::scoped_init::post_init) method should be called.
This indicates that the initialization phase is complete and the application is ready to enter the
runtime phase. After [](apex::scoped_init::post_init) is called, no further initialization is
allowed.

All resource allocation (e.g., initialization of `std::shared_ptr`) must happen before the call to
[](apex::scoped_init::post_init).

Below is an example of an initialization phase:

{{ code_snippet( "grace/examples/apex_os_minimal_pub_sub/src/minimal_publisher.cpp",
    {'tag': '//! [Initialization phase]', 'strip_substring': ['// (2)!', '// (3)!', '// (4)!',
    '// (5)!', '// (6)!', '// (7)!', '// (8)!', '// (9)!', '// (10)!', '//! [Signal handler]'] }) }}

The [](apex::scoped_init) constructor calls [](apex::pre_init), and [](apex::scoped_init::post_init)
calls [](apex::post_init). Once an [](apex::scoped_init) object goes out of scope, it automatically
calls [](rclcpp::shutdown) to cleanly shut down. It also throws an exception if [](apex::pre_init)
or [](apex::post_init) fail. However, if special handling is needed on failure, [](apex::pre_init)
and [](apex::post_init) can be called directly, in which case [](rclcpp::shutdown) should be called
manually.

### `apex_main` library

The `apex_main` library defines a real `main` function that calls the [](::apex_main)
function within a default `try/catch` block.  The [](::apex_main) function definition
is then supplied by the end user, without requiring any `try/catch` boilerplate.

This default `try/catch` block within the [](::apex_main) logs any uncaught
exceptions and exits gracefully, calling `rclcpp::shutdown()` if
possible.

### Interrupt handling

The default ROS 2 `rclcpp` interrupt handler is intrusive. It forcefully shuts
down the [](rclcpp::Context) which can cause race conditions and other errors.
Apex.Grace provides an alternative for cooperative handling of interrupts in the
[interrupt package](interrupt-design.md). Applications using the interrupt package
should pass `false` as the `install_ros_signal_handler` argument of [](apex::scoped_init).

### Root logger initialization

By default, [](apex::pre_init) and [](apex::scoped_init) initializes the
[root logger](logging-design.md#log-messages-with-a-contextual-logger). It is possible to skip
the root logger initialization by passing `false` as the `initialize_root_logger` argument
of [](apex::pre_init) or [](apex::scoped_init).

### Setting files type deduction

By default, [](apex::pre_init) and [](apex::scoped_init)
[deduce the value types](settings-design.md#value-type-deduction)
for the settings
[loaded from YAML files](settings-design.md#loading-the-repository-from-command-line-arguments).
It is possible to disable the deduction by passing `false`
as the `deduct_types_of_settings` argument of [](apex::pre_init) or [](apex::scoped_init).

!!! note
    Since internal mechanisms of Apex.Grace initialization
    (like [real-time settings](#configure-real-time-settings)) expect settings to be typed,
    disabling type deduction will cause settings to be parsed twice: first with type deduction,
    and then without. It may have a minor impact on the start-up time when type deduction
    is disabled.

### Catching exceptions

The `scoped_init` class provides a default implementation for a termination handler that is
automatically set by default. To opt-out, set the `scoped_init::pre_init()` argument
`set_terminate_handler` to `false`.

The handler should log any uncaught exceptions and call `std::abort()` so that a core dump
is generated.

### Configure real-time settings

Real-time settings can be configured using [the Apex.Grace settings](settings-design.md). Apex.Grace
supports the following settings:

| Name                   | Parameter        | Default value | Description                                                                                                                                                                                                                                         |
| ---------------------- | ---------------- | ------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| CPU affinity           | `proc_cpu_mask`  | 0             | Restricts which CPU cores the processes threads may run on. Selecting which CPU cores the threads should run on depends on the number of cores available, the number of safety-critical processes on each machine, and the CPU load of each process |
| CPU affinity           | `proc_cpu_list`  | 0             | Same as `proc_cpu_mask` but using a CPU list instead of a CPU mask  |
| Process priority       | `proc_prio`      | 0             | Used by the OS scheduler. The same priority is given to each thread of the process                                                                                                                                                                  |
| Process maximum memory | `proc_max_mem`   | 0             | Empirically defined as the upper memory bound for the process                                                                                                                                                                                       |
| Process scheduler      | `proc_scheduler` | `other`       | Selects the process scheduler policy via [](apex::threading::scheduler)                                                                                                                                                    |
| Memory locking         | `lock_memory`    | false         | Forces all virtual memory allocated in the process to be allocated as physical memory                                                                                                                                                               |

!!! warning
    The settings described above might not be supported by all operating systems, so the
    use of an Apex.Grace supported RTOS is encouraged. If the operating system does not
    support these settings, the behavior is undefined.

!!! note
    The Apex.Grace node will perform additional steps to ensure more
    determinism (see the documentation of `apex_proc_cpu_rt_init()` for more
    information) only if a real-time scheduler is set.

To pass the real-time settings to an executable, define a YAML file with the desired settings,
e.g.:

```yaml
rt_settings:
    proc_prio: 5
    proc_cpu_mask: 63
    proc_max_mem_mb : 2000
    proc_scheduler: "fifo"
    lock_memory: true
```

#### Configure process priority and scheduling policy

The `proc_prio` setting sets the process priority. This setting's default value is `0` if it is not
defined in a setting file.

The `proc_scheduler` setting sets the process scheduler policy. The available options are:

* `other` (default)
* `fifo`
* `round_robin`

A priority is defined for a specific scheduling policy. For this reason, it is required to
always set `proc_prio` and `proc_scheduler` explicitly. If any of these values is not set, an
error will be propagated when parsing the settings. If none of `proc_prio` and `proc_scheduler`
are set, no priority and scheduling policy will be applied.

When real-time is requested for an executable when these settings are defined with values other than
their defaults, a validation check occurs in `apex_init` to ensure `proc_prio` is within the
minimum and maximum allowed priority for the requested policy for the OS (and the OS's configuration).
On Linux, the default range is `1-99` (see <https://linux.die.net/man/2/sched_get_priority_max>).
The range can also be configured for non-privileged users by editing `rtprio` in
`/etc/security/limits.conf` (see <https://www.man7.org/linux/man-pages/man5/limits.conf.5.html>).
For information on the range of `proc_prio` for QNX SDP 7.1, see <https://www.qnx.com/developers/docs/7.1/index.html#com.qnx.doc.neutrino.prog/topic/overview_Priority_range.html>.

<!-- markdownlint-disable MD046 -->
!!! warning
    If the requested `proc_prio` is outside the valid range for the requested `proc_scheduler`,
    the executable will terminate on startup by raising the following runtime error:

    ```log
    Exception in apex::pre_init(): Could not perform scheduling related real-time initialization
    apex::pre_init() failed, Error code: -1
    Can't pre-init Apex.OS
    ```

<!-- markdownlint-restore -->

#### Configure CPU affinity

There are two types of parameters to set the CPU affinity, `proc_cpu_mask` and `proc_cpu_list`.
These options are similar to the options used by the Linux tool [taskset](https://man7.org/linux/man-pages/man1/taskset.1.html)

The parameter `proc_cpu_mask` uses a bitmask to specify the CPUs to bond the process with. The
lowest bit in the bitmask represents the first CPU or `CPU0`.

CPU affinity is limited to 31 CPUs if an integer value is used with `proc_cpu_mask`. For
larger values a string type should be used by adding a `!!str` YAML tag before the value
or use `proc_cpu_list` instead.

Some examples using `proc_cpu_mask`:

* `1`.  This sets CPU #0.
* `3`.  This sets CPUs #0 and #1.
* `10`.  This sets CPU #0.
* `63`.  This sets CPUs #0, #1, #2, #3, #4, and #5.
* `!!str 4294967295`. This sets CPUs from #0 to #32
* `0`. Error, no CPU is set.

In the case of `proc_cpu_list` parameter, it is possible to specify the CPU number in the
following ways:

* Using individual CPU numbers. i.e. `0,1,3`. This sets CPUs #0, #1 and #3.
* Using ranges. i.e. `0-2`. This sets CPUs #0, #1 and #2.
* Using ranges with a specific stride. i.e. `0-10:3`. This sets CPUs #0, #3, #6 and #9.

It is possible to combine all the options shown above, i.e. `0,1,4:6,10:20:2`.

Depending on the parameter and format used there are the following limitations:

* `proc_cpu_mask: N`. Using integer CPU masks is limited to 31 CPUs
* `proc_cpu_mask: !!str N`. Using strings as CPU masks is limited to 64 CPUs
* `proc_cpu_list`. Using a CPU list is limited to 1024 CPUs

### Integration with monitored process

There are overloads for [](apex::post_init) and [](apex::scoped_init::post_init) that
take [](apex::event::monitored_process) as an argument. These functions call
[](apex::event::monitored_process::send_post_init) as the last thing they do.
This is a convenience that allows to save a line of the boilerplate code. See
[monitored_process](event-design.md#sending-the-post-init-message) for further explanation.

## Assumptions / Known limits

* Only [](apex::pre_init) is allowed to call `apex_initialize`
* Only [](apex::pre_init) is allowed to call ROS 2 (via [](rclcpp::init))
* The APIs provided by the `apex_init` package are not designed for
  multi-threaded usage

## Future extensions / Unimplemented parts

N/A
