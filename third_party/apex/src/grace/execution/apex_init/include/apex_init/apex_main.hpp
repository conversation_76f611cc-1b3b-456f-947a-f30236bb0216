/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file declares apex_main function that automatically provides
/// the recommended try/catch block around Apex.Grace mains
///
/// It is expected that users provide the definition to apex_main.
#ifndef APEX_INIT__APEX_MAIN_HPP_
#define APEX_INIT__APEX_MAIN_HPP_

/// \brief Declare the apex_main function in which the end user will
/// provide its definition.
///
/// This is then called within an Apex.Grace-supplied `main` function that
/// has the default try/catch boilerplate around `apex_main`.
int apex_main(int argc, char* argv[]);

#endif  // APEX_INIT__APEX_MAIN_HPP_
