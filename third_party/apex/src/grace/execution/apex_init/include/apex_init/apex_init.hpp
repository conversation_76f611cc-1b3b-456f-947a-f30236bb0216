/// \copyright Copyright 2017-2019 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file declares apex initialization functions

#ifndef APEX_INIT__APEX_INIT_HPP_
#define APEX_INIT__APEX_INIT_HPP_

#include <apexutils/apexutils.h>
#include <apex_init/macros.hpp>
#include <apex_init/visibility_control.hpp>
#include <event/monitored_process.hpp>

#include <threading/thread_attributes.hpp>
#include <settings/inspect.hpp>
#include <bitset>
#include <optional>  // NOLINT cpplint mistakes <optional> for a C system header
#include <exception>

/// \namespace apex
/// \brief Classes that wrap `apexutils` objects
namespace apex
{

using Scheduler = threading::scheduler;

/// \class Settings
/// \brief Settings class aggregates process specific parameters into an easy to use class
/// to pass to pre_init API call
class Settings
{
public:
  /// \brief a Settings constructor that uses native priority
  /// \param[in] priority is native OS priority
  /// \param[in] scheduler is scheduler to use. See Scheduler for details
  /// \param[in] cpu_bit_mask is cpu affinity of all the threads in the process
  /// \param[in] approx_max_mem_reqd_sz is Approximate maximum memory size required for the process
  /// \param[in] apex_init_flags is OR-ed set of bits to control Apex.core behavior
  /// \cert
  /// \deprecated
  explicit Settings(
    const std::optional<int32_t> priority = std::nullopt,
    const std::optional<Scheduler> scheduler = std::nullopt,
    const uint64_t cpu_bit_mask = 0ULL,
    const size_t approx_max_mem_reqd_sz = 0ULL,
    const bool memory_lock_flag = false)
  : m_cpu_set{cpu_bit_mask},
    m_priority(priority),
    m_scheduler(scheduler),
    m_approx_max_mem_reqd_sz(approx_max_mem_reqd_sz),
    m_memory_lock_flag(memory_lock_flag)
  {
  }

  /// \brief a Settings constructor that uses native priority
  /// \param[in] priority is native OS priority
  /// \param[in] scheduler is scheduler to use. See Scheduler for details
  /// \param[in] cpu_bit_mask is cpu affinity of all the threads in the process
  /// \param[in] approx_max_mem_reqd_sz is Approximate maximum memory size required for the process
  /// \param[in] apex_init_flags is OR-ed set of bits to control Apex.core behavior
  /// \cert
  explicit Settings(
    const std::bitset<APEX_CPUSET_SIZE> cpu_set,
    const std::optional<int32_t> priority = std::nullopt,
    const std::optional<Scheduler> scheduler = std::nullopt,
    const size_t approx_max_mem_reqd_sz = 0ULL,
    const bool memory_lock_flag = false)
  : m_cpu_set{cpu_set},
    m_priority(priority),
    m_scheduler(scheduler),
    m_approx_max_mem_reqd_sz(approx_max_mem_reqd_sz),
    m_memory_lock_flag(memory_lock_flag)
  {
  }

  /// \cert
  std::optional<int32_t> get_priority() const
  {
    return m_priority;
  }

  /// \cert
  std::optional<Scheduler> get_scheduler() const
  {
    return m_scheduler;
  }

  /// \cert
  uint64_t get_cpu_bit_mask() const
  {
    return m_cpu_set.to_ulong();
  }

  /// \cert
  std::bitset<APEX_CPUSET_SIZE> get_cpu_set() const
  {
    return m_cpu_set;
  }

  /// \cert
  size_t get_approx_max_mem_reqd_sz() const
  {
    return m_approx_max_mem_reqd_sz;
  }

  /// \cert
  bool get_memory_lock_flag() const
  {
    return m_memory_lock_flag;
  }

private:
  std::bitset<APEX_CPUSET_SIZE> m_cpu_set;  ///< Default threads cpu affinity in the process
  std::optional<int32_t> m_priority;  ///< default priority for newly created tasks
  std::optional<Scheduler> m_scheduler;  ///< Task scheduler to use. See Scheduler.
  size_t m_approx_max_mem_reqd_sz;  ///< Approximate maximum memory size required for the process
  bool m_memory_lock_flag;  ///< Flag indicating whether or not to lock memory after initialization
};

/// \brief  Parse cpu_affinity options and convert it to a CPU bitset
/// \param[in] cfg dictionary view of rt settings. proc_prio, proc_cpu_mask, proc_max_mem_mb
///                keys expected at the top level
/// \return bitset with the CPU affinity information
APEX_INIT_PUBLIC
std::bitset<APEX_CPUSET_SIZE> parse_proc_cpu_affinity(
  const apex::settings::inspect::dictionary_view & cfg);

/// \brief Parse RT settings from apex settings dictionary
/// \param[in] cfg dictionary view of rt settings. proc_prio, proc_cpu_mask, proc_max_mem_mb
///                keys expected at the top level
/// \return Apex.OS initialization settings with memory in bytes and scheduler assigned to a
///         proper enum value
APEX_INIT_PUBLIC
apex::Settings parse_rt_from_apex_settings(const apex::settings::inspect::dictionary_view & cfg);

/// \brief Check if the process is set with RT parameters or not
/// \return true if RT params are set, if not false.
/// \attention return value of this function is undefined if apex::pre_init has not been called.
/// \cert
/// \deterministic
APEX_INIT_PUBLIC bool is_proc_rt();

/// \brief Check if the process is configured for memory locking
/// \return true if the process has been configured to lock memory after initialization
/// \cert
/// \deterministic
APEX_INIT_PUBLIC bool should_lock_memory();

/// \brief Set up the Apex.OS framework and enter the initialization phase. Must be called
/// before any other Apex.OS API
///
/// The function performs the following initialization steps:
///  1. Filling an `apex::Settings` object from the Apex.OS settings repository
///     and applying the chosen settings
///  2. Initializing Apex.Middleware with the settings from the Apex.OS settings repository
///  3. Initializing ROS 2 using `rclcpp::init`
///
/// \param[in] argc is original argument counter from the main function
/// \param[in] argv is original argument vector from the main function
/// \param[in] install_ros_signal_handler whether to install the ROS signal handler, which
/// shuts down the context and is required for `rclcpp::ok`. Apex.OS applications should set
/// this to `false` and use `apex::interrupt_handler` instead.
/// \param[in] initialize_root_logger whether to initialize the root logger
/// \param[in] deduct_types_of_settings whether to deduce the types of the settings values
/// from the context while parsing the yaml files specified by the user
/// \return return APEX_RET_OK on success and APEX_RET_ERROR otherwise.
/// \note This function catches `std::exception`'s and does not re-throw them.
/// \cert
/*
 AXIVION Next Line MisraC++2023-10.1.1: argv not being ptr to const is C++ convention
 */
APEX_INIT_PUBLIC apex_ret_t pre_init(
  const int32_t argc,
  char ** const argv,
  bool install_ros_signal_handler = true,
  bool initialize_root_logger = true,
  bool deduct_types_of_settings = true,
  bool set_terminate_handler = true
);

/// \brief Complete the initialization phase and enter the runtime phase.
/// Applications that do not distinguish between initialization and runtime phase
/// do not need to call this function
///
/// If no real-time scheduler is configured, the function is a no-op and only serves
/// as a marker for the phase transition.
///
/// If a real-time scheduler is configured, the function performs optimizations for
/// improved timing predictability. This includes locking memory with `mlock`,
/// disabling heap movements, and disabling `mmap`. These memory optimizations can
/// alternatively be disabled for real-time schedulers via yaml configuration.
///
/// See `apex_proc_mem_rt_init` for details.
/// \return return APEX_RET_OK if success, APEX_RET_ERROR otherwise
/// \cert
APEX_INIT_PUBLIC apex_ret_t post_init();

/// \brief Complete the initialization phase and enter the runtime phase,
/// calling the `send_post_init` method of the monitored process in the end
/// Applications that do not distinguish between initialization and runtime phase
/// do not need to call this function
///
/// If no real-time scheduler is configured, the function is a no-op and only serves
/// as a marker for the phase transition.
///
/// If a real-time scheduler is configured, the function performs optimizations for
/// improved timing predictability. This includes locking memory with `mlock`,
/// disabling heap movements, and disabling `mmap`. These memory optimizations can
/// alternatively be disabled for real-time schedulers via yaml configuration.
///
/// See `apex_proc_mem_rt_init` for details.
/// \param[in] monitored_process monitored class to send process initialization confirmation
/// \return return APEX_RET_OK if success, APEX_RET_ERROR otherwise
/// \cert
APEX_INIT_PUBLIC apex_ret_t post_init(apex::event::monitored_process & monitored_process);

/// \class scoped_init
/// \brief RAII helper for calling apex::pre_init() right away and rclcpp::shutdown() on scope exit.
class scoped_init
{
public:
  /// \brief a scoped_init constructor. Set up the Apex.OS framework and enter the initialization
  /// phase. Must be called before any other Apex.OS API.
  ///
  /// This calls apex::pre_init() and throws if it fails.
  ///
  /// \param[in] argc is original argument counter from the main function
  /// \param[in] argv is original argument vector from the main function
  /// \param[in] install_ros_signal_handler whether to install the ROS signal handler, which shuts
  /// down the context and is required for rclcpp::ok. Apex.OS applications should set this to
  /// `false` and use apex::interrupt_handler instead.
  /// \param[in] initialize_root_logger whether to initialize the root logger
  /// \param[in] deduct_types_of_settings whether to deduce the types of the settings values
  /// from the context while parsing the yaml files specified by the user
  /// \throws apex::runtime_error if apex::pre_init() fails
  /// \warning do not discard the result/object, otherwise rclcpp::shutdown() will be called
  /// prematurely
  /// \see apex::pre_init
  /// \see rclcpp::shutdown
#if defined(__GNUC__) || defined(QNX)
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wattributes"
#endif  // __GNUC__ || QNX
  APEX_INIT_NODISCARD_CTOR
  APEX_INIT_PUBLIC
  scoped_init(
    const int32_t argc,
    char ** const argv,
    bool install_ros_signal_handler = true,
    bool initialize_root_logger = true,
    bool deduct_types_of_settings = true,
    bool set_terminate_handler = true);
#if defined(__GNUC__) || defined(QNX)
#pragma GCC diagnostic pop
#endif  // __GNUC__ || QNX

  /// \brief a scoped_init destructor.
  ///
  /// This calls rclcpp::shutdown().
  /// \see rclcpp::shutdown
  APEX_INIT_PUBLIC
  ~scoped_init();

  scoped_init(const scoped_init &) = delete;
  scoped_init(scoped_init &&) = delete;
  scoped_init & operator=(const scoped_init &) = delete;
  scoped_init & operator=(scoped_init &&) = delete;

  /// \brief Complete the initialization phase and enter the runtime phase.
  /// Applications that do not distinguish between initialization and runtime phase
  /// do not need to call this function
  /// \throws apex::runtime_error if apex::post_init() fails
  /// \see apex::post_init
  /// \cert
  APEX_INIT_PUBLIC
  void post_init() const;

  /// \brief Complete the initialization phase and enter the runtime phase,
  /// calling the `send_post_init` method of the monitored process in the end.
  /// Applications that do not distinguish between initialization and runtime phase
  /// do not need to call this function
  /// \param[in] monitored_process monitored class to send process initialization confirmation
  /// \throws apex::runtime_error if apex::post_init() fails
  /// \see apex::post_init
  /// \cert
  APEX_INIT_PUBLIC
  void post_init(apex::event::monitored_process & monitored_process) const;

};

}  // namespace apex

#endif  // APEX_INIT__APEX_INIT_HPP_
