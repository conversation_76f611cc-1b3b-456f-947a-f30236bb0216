#include <exception>
#include <iostream>

#include "cpputils/common_exceptions.hpp"
#include "rclcpp/rclcpp.hpp"
#include "logging/logging_macros.hpp"
#include "apex_init/apex_main.hpp"


namespace apex
{
namespace {

void log_exception(const char * log_str)
{
  if (rclcpp::ok())
  {
    APEX_FATAL_R(log_str);
    rclcpp::shutdown();
  }
  else
  {
    std::cerr << log_str << std::endl;
  }
}

std::string combine_const_char(const char * str1, const char * str2)
{
  return std::string(str1) + str2;
}

}  // namespace
}  // namepace apex


int main(int argc, char* argv[])
{
  int result = EXIT_SUCCESS;
  try { return apex_main(argc, argv); }
  catch (const apex::exception & apex_err) {
    // TODO: also extract stack trace from the apex::exception object if we can
    const std::string combined_str = apex::combine_const_char(
      "[ apex ] ",
      apex_err.what()
    );
    apex::log_exception(combined_str.c_str());
    result = EXIT_FAILURE;
  }
  catch (const std::exception & err) {
    apex::log_exception(err.what());
    result = EXIT_FAILURE;
  }
  catch (...) {
    apex::log_exception("Unknown error occurred");
    result = EXIT_FAILURE;
  }
  return result;
}