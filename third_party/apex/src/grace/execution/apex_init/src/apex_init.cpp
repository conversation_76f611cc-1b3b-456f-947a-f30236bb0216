// Copyright 2017-2019 Apex.AI, Inc.
// All rights reserved.

#include <apex_init/apex_init.hpp>
#include <logging/logging.hpp>
#include <logging/logging_macros.hpp>
#include <tracetools/tracetools.h>
#include <rcutils/cmdline_parser.h>
#include <settings/repository.hpp>
#include <string/string_silent.hpp>
#include <rclcpp/rclcpp.hpp>
#include <rcpputils/split.hpp>

#include <vector>
#include <regex>
#include <iostream>
#include <exception>
#include <cstdlib>


#ifndef  APEX_CERT
#include <settings_extensions/from_yaml.hpp>
#include <cstdint>
#endif  // APEX_CERT

namespace apex
{
namespace
{
/*
 AXIVION Next CodeLine MisraC++2023-6.7.2, MisraC++2023-18.4.1: Reason: Code Quality
 (Functional suitability), Justification: Use the global variable; standard implementation.
 init form singleton.
 */
//lint -e{1756} NOLINT - Settings is essentially a POD class
static Settings apex_proc_settings;

apex_cpuset_t bitset_to_apex_cpu_set(const std::bitset<APEX_CPUSET_SIZE> & bitset)
{
  apex_cpuset_t set;
  apex_cpu_zero(&set);
  for (uint32_t i = 0U; i < bitset.size(); i++) {
    if (bitset.test(i)) {
      apex_cpu_set(i, &set);
    }
  }
  return set;
}

void set_cpu_bit_mask_range(
  std::bitset<APEX_CPUSET_SIZE> & cpu_bit_mask, uint64_t pos)
{
  if (pos >= APEX_CPUSET_SIZE) {
    throw apex::runtime_error("out of range CPU bit set ", pos,
            " max (", APEX_CPUSET_SIZE, ")");
  }
  (void)cpu_bit_mask.set(pos, true);
}

void set_cpu_bit_mask_range(
  std::bitset<APEX_CPUSET_SIZE> & cpu_bit_mask,
  uint64_t range_start,
  uint64_t range_end,
  uint64_t range_stride = 1UL)
{
  if (range_start > range_end) {
    throw apex::runtime_error("failed to parse CPU list, incorrect range ",
            range_start, " > ", range_end);
  }

  for (auto i = range_start; i <= range_end; i += range_stride) {
    set_cpu_bit_mask_range(cpu_bit_mask, i);
  }
}

/// parse list of CPUs, intended to work as taskset tool cpulist command option
std::bitset<APEX_CPUSET_SIZE> parse_cpulist(const apex::string_strict256_t & cpu_list)
{
  std::bitset<APEX_CPUSET_SIZE> cpu_bit_mask;
  // i.e, 4-10:2
  const std::regex cpu_range_with_stride_regex(R"((\d+)-(\d+):(\d+))");
  // i.e, 4-10
  const std::regex cpu_range_regex(R"((\d+)-(\d+))");
  // i.e, 4
  const std::regex cpu_regex(R"((\d+))");

  auto tokens = rcpputils::split(cpu_list, ',', true);
  for (const auto & token : tokens) {
    std::smatch match;
    if (std::regex_match(token, match, cpu_range_with_stride_regex)) {
      if (match.size() == 4UL) {
        const auto range_start = std::stoul(match.str(1UL));
        const auto range_end = std::stoul(match.str(2UL));
        const auto range_stride = std::stoul(match.str(3UL));
        set_cpu_bit_mask_range(cpu_bit_mask, range_start, range_end, range_stride);
      } else {
        throw apex::runtime_error("failed to parse CPU list: ", cpu_list);
      }
    } else if (std::regex_match(token, match, cpu_range_regex)) {
      if (match.size() == 3UL) {
        const auto range_start = std::stoul(match.str(1UL));
        const auto range_end = std::stoul(match.str(2UL));
        set_cpu_bit_mask_range(cpu_bit_mask, range_start, range_end);
      } else {
        throw apex::runtime_error("failed to parse CPU list: ", cpu_list);
      }
    } else if (std::regex_match(token, match, cpu_regex)) {
      if (match.size() == 2UL) {
        const auto pos = std::stoul(match.str(1UL));
        set_cpu_bit_mask_range(cpu_bit_mask, pos);
      } else {
        throw apex::runtime_error("failed to parse CPU list: ", cpu_list);
      }
    } else {
      throw apex::runtime_error("failed to parse CPU list: ", cpu_list);
    }
  }

  return cpu_bit_mask;
}

/// \brief The default termination handler provided with Apex.Grace.
///
/// Intended to log uncaught exceptions and exit the process.
void terminate_handler() noexcept
{
  std::exception_ptr exception = std::current_exception();
  if (exception) {
    try { std::rethrow_exception(exception); }
    catch (const apex::exception & apex_err) {
      std::cerr << "[ apex ] " << apex_err.what() << std::endl;
    }
    catch (const std::exception & err) {
      std::cerr << err.what() << std::endl;
    }
    catch (...) {
      std::cerr << "Unknown error occurred" << std::endl;
    }
  }
  std::abort();
}
}  // namespace


std::bitset<APEX_CPUSET_SIZE> parse_proc_cpu_affinity(
  const apex::settings::inspect::dictionary_view & cfg)
{
  namespace cast = apex::cast;
  namespace settings = apex::settings::inspect;

  std::bitset<APEX_CPUSET_SIZE> cpu_set;
  apex::string_strict256_t proc_cpu_list;

  // parse proc_cpu_list (optional)
  auto maybe_proc_cpu_list_as_string =
    settings::get<settings::maybe<settings::string_view>>(cfg, "rt_settings/proc_cpu_list");
  if (maybe_proc_cpu_list_as_string) {
    proc_cpu_list = maybe_proc_cpu_list_as_string.value().data();
  } else {
    // if the list contains just one element it is parsed as a integer
    auto maybe_proc_cpu_list_as_int =
      settings::get<settings::maybe<settings::integer>>(cfg, "rt_settings/proc_cpu_list");
    if (maybe_proc_cpu_list_as_int) {
      proc_cpu_list = apex::to_string(maybe_proc_cpu_list_as_int.value());
    }
  }

  if (!proc_cpu_list.empty()) {
    cpu_set = parse_cpulist(proc_cpu_list);
  } else {
    uint64_t proc_cpu_mask = 0UL;
    // proc_cpu_mask accept string values for values larger than uint32_t size
    // integer values are still valid to be compatible with previous versions
    auto maybe_proc_cpu_mask =
      settings::get<settings::maybe<settings::string_view>>(cfg, "rt_settings/proc_cpu_mask");
    if (maybe_proc_cpu_mask) {
      proc_cpu_mask = cast::safe_cast<uint64_t>(std::stoul(maybe_proc_cpu_mask.value().data()));
    } else {
      proc_cpu_mask = cast::safe_cast<uint64_t>(settings::get_or_default<settings::integer>(
            cfg, "rt_settings/proc_cpu_mask", 0));
    }
    cpu_set = std::bitset<APEX_CPUSET_SIZE>(proc_cpu_mask);
  }

  return cpu_set;
}

APEX_INIT_PUBLIC bool is_proc_rt()
{
  return (apex_proc_settings.get_scheduler() != apex::nullopt) &&
         (apex_proc_settings.get_scheduler() != Scheduler::other);
}

APEX_INIT_PUBLIC bool should_lock_memory()
{
  return apex_proc_settings.get_memory_lock_flag();
}

/*
 AXIVION Next Line MisraC++2023-10.1.1: Reason: Code Quality (Functional suitability),
 Justification: argv not being ptr to const is C++ convention
 */
APEX_INIT_PUBLIC apex_ret_t pre_init(
  const int32_t argc,
  char ** const argv,
  bool install_ros_signal_handler,
  bool initialize_root_logger,
  bool deduct_types_of_settings,
  bool set_terminate_handler)
{
  TRACETOOLS_TRACEPOINT(apex_pre_init_start);
  apex_ret_t retval = APEX_RET_OK;

  base::log::Logger::init(base::log::logLevelFromEnvOr(base::log::MINIMAL_LOG_LEVEL));
  if (set_terminate_handler) { std::set_terminate(&terminate_handler); }

  // In Non cert version, rt settings loaded from the command line(if exists) overrides
  // rt settings loaded from builder functions
#ifndef APEX_CERT
  // load the user settings from command line(if exists)
  apex::settings_extensions::yaml::load_repository_from_command_line(argc, argv);
#endif  // APEX_CERT

  // Load RT settings from YAML
  const auto settings = apex::parse_rt_from_apex_settings(apex::settings::repository::get());

  const auto settings_priority = settings.get_priority();
  const auto settings_scheduler = settings.get_scheduler();
  const auto settings_cpu_set = settings.get_cpu_set();

  try {
    if (settings_cpu_set.any()) {
      const auto mask = bitset_to_apex_cpu_set(settings_cpu_set);
      retval = apex_set_cpu_affinity(mask);
      if (retval != APEX_RET_OK) {
        throw apex::runtime_error("Could not perform cpu mask related real-time initialization");
      }
    }

    // priority is only set if both scheduling policy and priority are explicitly set in settings
    if (settings_priority.has_value() && settings_scheduler.has_value()) {
      int32_t scheduling_policy;
      if (settings_scheduler.value() == Scheduler::fifo) {
        scheduling_policy = SCHED_FIFO;
      } else if (settings_scheduler.value() == Scheduler::round_robin) {
        scheduling_policy = SCHED_RR;
      } else {
        scheduling_policy = SCHED_OTHER;
      }
      retval = apex_set_priority(settings_priority.value(), scheduling_policy);
      if (retval != APEX_RET_OK) {
        throw apex::runtime_error("Could not perform scheduling related real-time initialization");
      }
    }

    const auto argc_int32 = static_cast<std::int32_t>(argc);

    // set rclcpp global time source init options
    const bool global_sim_time =
      settings::inspect::get_or_default<settings::inspect::boolean>(
      apex::settings::repository::get(), "time_settings/use_sim_time", false);
    const bool global_use_clock_thread =
      settings::inspect::get_or_default<settings::inspect::boolean>(
      apex::settings::repository::get(), "time_settings/use_clock_thread", false);
    auto init_options = rclcpp::InitOptions();
    init_options.global_sim_time = global_sim_time;
    init_options.global_use_clock_thread = global_use_clock_thread;

    ::rclcpp::init(argc_int32, argv, init_options, install_ros_signal_handler);

    if (initialize_root_logger) {
      APEX_DEBUG_R("Initializing root logger.");
    }
#ifndef APEX_CERT
    // reload settings without deduction if users do not want to deduct types
    if (!deduct_types_of_settings) {
      apex::settings_extensions::yaml::load_repository_from_command_line(argc, argv, true);
    }
#endif
  } catch (const std::exception & e) {
    std::cerr << "Exception in apex::pre_init(): " << e.what() << "\n";
    retval = APEX_RET_ERROR;
  }

  apex_proc_settings = settings;

  if (retval != APEX_RET_OK) {
    std::cerr << "apex::pre_init() failed, Error code: " << retval << "\n";
  }
  TRACETOOLS_TRACEPOINT(apex_pre_init_end);
  return retval;
}

APEX_INIT_PUBLIC apex_ret_t post_init()
{
  TRACETOOLS_TRACEPOINT(apex_post_init_start);
  apex_ret_t retval = APEX_RET_OK;
  if (should_lock_memory()) {
    retval = apex_proc_mem_rt_init(apex_proc_settings.get_approx_max_mem_reqd_sz());
  } else {
    if (is_proc_rt()) {
      APEX_WARN_R("Memory not locked whilst real-time scheduling policy is in use.");
    }
  }
  TRACETOOLS_TRACEPOINT(apex_post_init_end);
  return retval;
}

APEX_INIT_PUBLIC apex_ret_t post_init(apex::event::monitored_process & monitored_process)
{
  const auto retval = post_init();
  if (retval == APEX_RET_OK) {
    monitored_process.send_post_init();
  }
  return retval;
}

////////////////////////////////////////////////////////////////////////////////
apex::Settings parse_rt_from_apex_settings(const apex::settings::inspect::dictionary_view & cfg)
{
  namespace cast = apex::cast;
  namespace settings = apex::settings::inspect;
  std::optional<int32_t> proc_prio;
  const auto maybe_proc_prio = settings::get<settings::maybe<settings::integer>>(cfg,
      "rt_settings/proc_prio");
  if (maybe_proc_prio.has_value()) {
    proc_prio = cast::safe_cast<int32_t>(maybe_proc_prio.value());
  }

  const auto cpu_set = parse_proc_cpu_affinity(cfg);

  const auto proc_max_mem_mb =
    cast::safe_cast<std::size_t>(settings::get_or_default<settings::integer>(cfg,
      "rt_settings/proc_max_mem_mb", 0));
  const auto proc_scheduler_str =
    settings::get<settings::maybe<settings::string_view>>(cfg, "rt_settings/proc_scheduler");
  const auto memory_lock_flag = settings::get_or_default<settings::boolean>(cfg,
      "rt_settings/lock_memory", false);

  std::optional<apex::Scheduler> proc_scheduler;
  if (proc_scheduler_str.has_value()) {
    if (*proc_scheduler_str == "fifo") {
      proc_scheduler = Scheduler::fifo;
    } else if (*proc_scheduler_str == "round_robin") {
      proc_scheduler = Scheduler::round_robin;
    } else if ((*proc_scheduler_str == "other") || (*proc_scheduler_str == "normal")) {
      proc_scheduler = Scheduler::other;
    } else {
      throw apex::runtime_error("failed to parse process scheduler, unknown schedule: ",
              *proc_scheduler_str);
    }
  }

  if (proc_prio.has_value() && !proc_scheduler.has_value()) {
    throw apex::invalid_argument("Error in real-time settings: "
            "priority is set but no scheduler is set");
  } else if (!proc_prio.has_value() && proc_scheduler.has_value()) {
    throw apex::invalid_argument("Error in real-time settings: "
            "scheduler is set but no priority is set");
  }

  constexpr size_t B_IN_KB = 1024U;
  constexpr size_t KB_IN_MB = 1024U;
  const std::size_t proc_max_mem = proc_max_mem_mb * KB_IN_MB * B_IN_KB;

  return apex::Settings{cpu_set, proc_prio, proc_scheduler, proc_max_mem, memory_lock_flag};
}

scoped_init::scoped_init(
  const int32_t argc,
  char ** const argv,
  bool install_ros_signal_handler,
  bool initialize_root_logger,
  bool deduct_types_of_settings,
  bool set_terminate_handler)
{
  const apex_ret_t ret = apex::pre_init(argc, argv, install_ros_signal_handler,
      initialize_root_logger, deduct_types_of_settings, set_terminate_handler);
  if (APEX_RET_OK != ret) {
    throw apex::runtime_error("apex::pre_init() failed: error code:", ret);
  }
}

scoped_init::~scoped_init()
{
  // Shut down if not already shut down, even though it's not a big deal if we shut down while the
  // context is already shut down
  if (rclcpp::ok()) {
    (void)rclcpp::shutdown();
  }
}

void scoped_init::post_init() const
{
  const apex_ret_t ret = apex::post_init();
  if (APEX_RET_OK != ret) {
    throw apex::runtime_error("apex::post_init() failed: error code:", ret);
  }
}

void scoped_init::post_init(apex::event::monitored_process & monitored_process) const
{
  const apex_ret_t ret = apex::post_init(monitored_process);
  if (APEX_RET_OK != ret) {
    throw apex::runtime_error("apex::post_init() failed: error code:", ret);
  }
}

}  // namespace apex
