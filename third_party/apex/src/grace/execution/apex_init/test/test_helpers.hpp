// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#include <sys/resource.h>
#include <unistd.h>

#include "apexutils/apex_rt.h"


namespace test_helpers
{

std::bitset<APEX_CPUSET_SIZE> apex_cpu_set_to_bitset(const apex_cpuset_t & cpuset)
{
  std::bitset<APEX_CPUSET_SIZE> bitset;
  for (uint32_t i = 0U; i < bitset.size(); i++) {
    if (apex_cpu_is_set(i, &cpuset)) {
      bitset.set(i, true);
    }
  }
  return bitset;
}

bool system_allows_to_set_realtime_priority()
{
#ifdef APEX_LINUX
  struct rlimit priority_limits;
  const auto ret = getrlimit(RLIMIT_RTPRIO, &priority_limits);
  return (ret == 0) && (priority_limits.rlim_cur > 0);
#elif defined(QNX)
  // On QNX unprivileged threads can have a priority ranging from 1 to 63
  // there is no need for special permissions without exceeding that range
  return true;
#else
  return false;
#endif
}

bool system_allows_to_lock_the_memory()
{
#ifdef ADDRESS_SANITIZER
  // ASan does not get along with memory locking
  return false;
#endif
#ifdef APEX_LINUX
  struct rlimit limits;
  const auto ret = getrlimit(RLIMIT_MEMLOCK, &limits);
  return (ret == 0) && (limits.rlim_cur > 0);
#elif defined(QNX)
  // On QNX we check if the user is root
  return getuid() == 0;
#else
  return false;
#endif
}

}  // namespace test_helpers
