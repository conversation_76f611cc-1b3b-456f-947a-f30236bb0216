// Copyright 2020 Apex.AI, Inc.
// All rights reserved.

#include <apexutils/apexutils.h>

#include <apex_init/apex_init.hpp>
#include <settings/repository.hpp>
#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>

#include "param/rt_mock_settings.hpp"

using ::testing::Return;

// Mock the public APIs in apexutils packages
// apex_set_cpu_affinity function become the stub function
class apexutilsMock
{
public:
  apexutilsMock() {}
  MOCK_METHOD1(apex_set_cpu_affinity, apex_ret_t(const apex_cpuset_t mask));
};
std::unique_ptr<apexutilsMock> apexutils;

class apex_init_gmock : public testing::Test
{
public:
  void SetUp()
  {
    apexutils = std::make_unique<apexutilsMock>();
  }
  void TearDown()
  {
    apexutils.reset();
  }
};

apex_ret_t apex_set_cpu_affinity(const apex_cpuset_t mask)
{
  apex_ret_t ret = APEX_RET_ERROR;
  if (nullptr != apexutils) {
    ret = apexutils->apex_set_cpu_affinity(mask);
  }
  return ret;
}

apex_cpuset_t to_apex_cpu_set(std::bitset<APEX_CPUSET_SIZE> bitset)
{
  apex_cpuset_t mask;
  apex_cpu_zero(&mask);
  for (uint32_t i = 0; i < bitset.size(); i++) {
    if (bitset.test(i)) {
      apex_cpu_set(i, &mask);
    }
  }
  return mask;
}

bool operator==(apex_cpuset_t a, apex_cpuset_t b)
{
  for (uint32_t i = 0U; i < APEX_CPUSET_SIZE; i++) {
    if (apex_cpu_is_set(i, &a) != apex_cpu_is_set(i, &b)) {
      return false;
    }
  }
  return true;
}

/*
 * The purpose of the test case is to test `pre_init` function
 * when `apex_set_cpu_affinity` function returns APEX_RET_ERROR
 * So, `apex_set_cpu_affinity` function become the stub function with GMock. and The stub
 * function returns APEX_RET_ERROR
 */
TEST_F(apex_init_gmock, pre_init)
{
  apex_ret_t retval = APEX_RET_OK;

  retval = apex::pre_init(
    0U,
    nullptr,
    false);
  EXPECT_EQ(retval, static_cast<apex_ret_t>(APEX_RET_OK));

  apex::settings::repository::set(apex::settings::generated::rt_mock_settings::create());

  using integer = apex::settings::inspect::integer;
  const uint64_t test_cpu_mask = apex::settings::inspect::get<integer>(
    apex::settings::repository::get(), "rt_settings/proc_cpu_mask");

  std::bitset<APEX_CPUSET_SIZE> bitset(test_cpu_mask);
  apex_cpuset_t cpu_set = to_apex_cpu_set(bitset);

  // When `apex_proc_cpu_rt_init` that become the stub function is called,
  // the argument should be `test_cpu_mask`
  // and the return value of `apex_proc_cpu_rt_init` is set with APEX_RET_ERROR
  EXPECT_CALL(*apexutils,
    apex_set_cpu_affinity(cpu_set)).WillOnce(Return(APEX_RET_ERROR));
  retval = apex::pre_init(
    0U,
    nullptr,
    false);
  EXPECT_EQ(retval, static_cast<apex_ret_t>(APEX_RET_ERROR));

  rclcpp::shutdown();
}
