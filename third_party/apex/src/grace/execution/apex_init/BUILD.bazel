load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "apex_init_pkg",
    cc_libraries = [
        ":apex_init",
        ":apex_main",
    ],
    description = "Package containing initialization functions of Apex.OS",
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/apexutils:apexutils_pkg",
        "//common/configuration/settings:settings_pkg",
        "//common/threading:threading_pkg",
        "//grace/configuration/settings_extensions:settings_extensions_pkg",
        "//grace/monitoring/event:event_pkg",
        "//grace/monitoring/logging:logging_pkg",
        "//grace/tools/ros2_tracing/tracetools:tracetools_pkg",
    ],
)

filegroup(
    name = "doc_files",
    srcs = glob(["include/**/*.hpp"]),
    visibility = [":__subpackages__"],
)

cc_library(
    name = "apex_init",
    srcs = ["src/apex_init.cpp"],
    hdrs = glob(
        ["include/**/*.hpp"],
        exclude = ["include/apex_init/apex_main.hpp"],
    ),
    applicable_licenses = ["//common/integrity:embedded_gold"],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "//common/apexutils",
        "//common/configuration/settings",
        "//common/threading",
        "//grace/configuration/settings_extensions",
        "//grace/monitoring/event",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/tools/ros2_tracing/tracetools",
        "@coverage_tool//:coverage_io_lib",
    ],
)

cc_library(
    name = "apex_main",
    srcs = ["src/apex_main.cpp"],
    hdrs = ["include/apex_init/apex_main.hpp"],
    applicable_licenses = ["//common/integrity:embedded_gold"],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_library(
    name = "test_lib",
    testonly = True,
    srcs = glob([
        "test/param/*.cpp",
        "test/param/*.hpp",
        "test/test_helpers.hpp",
    ]),
    data = glob([
        "test/**/*.yaml",
    ]),
    tags = ["exclude_sca"],
    deps = [
        "//common/configuration/settings",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_apex_init",
    srcs = [
        "test/test_apex_init.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":apex_init",
        ":test_lib",
    ],
)

filegroup(
    name = "test_post_init_srcs_with_req_ids",
    srcs = ["test/test_post_init.cpp"],
    visibility = ["//grace/execution/apex_init/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_post_init",
    srcs = [":test_post_init_srcs_with_req_ids"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    # TODO(carlos): Set this define based on the testing platform #27338
    #local_defines = ["SYSTEM_HAS_REALTIME_PRIVILEGES"],
    deps = [
        ":apex_init",
        ":test_lib",
    ],
)

filegroup(
    name = "test_post_init_rt_srcs_with_req_ids",
    srcs = ["test/test_post_init_rt.cpp"],
    visibility = ["//grace/execution/apex_init/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_post_init_rt",
    srcs = [":test_post_init_rt_srcs_with_req_ids"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    # TODO(carlos): Set this define based on the testing platform #27338
    #local_defines = ["SYSTEM_HAS_REALTIME_PRIVILEGES"],
    deps = [
        ":apex_init",
        ":test_lib",
    ],
)

filegroup(
    name = "test_apex_init_fifo_srcs_with_req_ids",
    srcs = ["test/test_apex_init_fifo.cpp"],
    visibility = ["//grace/execution/apex_init/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_apex_init_fifo",
    srcs = [":test_apex_init_fifo_srcs_with_req_ids"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    # TODO(carlos): Set this define based on the testing platform #27338
    #local_defines = ["SYSTEM_HAS_REALTIME_PRIVILEGES"],
    deps = [
        ":apex_init",
        ":test_lib",
    ],
)

filegroup(
    name = "test_apex_init_rr_srcs_with_req_ids",
    srcs = ["test/test_apex_init_rr.cpp"],
    visibility = ["//grace/execution/apex_init/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_apex_init_rr",
    srcs = [":test_apex_init_rr_srcs_with_req_ids"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    # TODO(carlos): Set this define based on the testing platform #27338
    #local_defines = ["SYSTEM_HAS_REALTIME_PRIVILEGES"],
    deps = [
        ":apex_init",
        ":test_lib",
    ],
)

filegroup(
    name = "test_apex_init_other_srcs_with_req_ids",
    srcs = ["test/test_apex_init_other.cpp"],
    visibility = ["//grace/execution/apex_init/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_apex_init_other",
    srcs = [":test_apex_init_other_srcs_with_req_ids"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":apex_init",
        ":test_lib",
    ],
)

filegroup(
    name = "test_apex_init_proc_cpu_list_srcs_with_req_ids",
    srcs = ["test/test_apex_init_proc_cpu_list.cpp"],
    visibility = ["//grace/execution/apex_init/doc/internal:__subpackages__"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)

apex_cc_test(
    name = "test_apex_init_proc_cpu_list",
    srcs = [":test_apex_init_proc_cpu_list_srcs_with_req_ids"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":apex_init",
        ":test_lib",
    ],
)

apex_cc_test(
    name = "test_apex_init_gmock",
    srcs = [
        "test/test_apex_init_gmock.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":apex_init",
        ":test_lib",
    ],
)

apex_cc_test(
    name = "test_parse_rt_settings",
    srcs = [
        "test/test_parse_rt_settings.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    local_defines = [
        "PATH_TO_YAML=\\\"./grace/execution/apex_init/test/param/rt_settings.yaml\\\"",
    ],
    deps = [
        ":apex_init",
        ":test_lib",
    ],
)

apex_cc_test(
    name = "test_settings_parsing",
    srcs = [
        "test/test_settings_parsing.cpp",
    ],
    data = [
        "test/param/parsing.yaml",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    local_defines = [
        "PATH_TO_YAML=\\\"./grace/execution/apex_init/test/param/parsing.yaml\\\"",
    ],
    deps = [
        ":apex_init",
        ":test_lib",
    ],
)
