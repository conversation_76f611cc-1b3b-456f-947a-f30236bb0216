/// \copyright 2021-2024 Apex.AI, Inc.
/// All rights reserved.

#include <aws/common/log_writer.h>
#include <aws/crt/UUID.h>
#include <gtest/gtest.h>

#include <filesystem>
#include <fstream>
#include <future>
#include <memory>
#include <string>

#include "cpputils/common_exceptions.hpp"
#include "logging/logging.hpp"
#include "mqtt_connector/utils.hpp"
#include "mqtt_connector_test_utils.hpp"
#include "rclcpp/dynamic_waitset/waitset.hpp"
#include "rclcpp/polling_subscription.hpp"
#include "rclcpp/utilities.hpp"

namespace fs = std::filesystem;

static Aws::Crt::ApiHandle aws_api_handle;

class test_integration_aws_mqtt_logging : public ::testing::Test
{
  using LogMessageType = apex_msgs::msg::LogMessage;

public:
  static void SetUpTestSuite()
  {
    rclcpp::init(0, nullptr);
  }

  static void TearDownTestSuite()
  {
    rclcpp::shutdown();
  }

  void setup_root_logger_waitset()
  {
    m_node = std::make_shared<rclcpp::Node>("mqtt_connector_logging_test");
    auto & root_logger = apex::logging::LoggerBase::get_root_logger()->get();
    m_sub = m_node->create_polling_subscription<LogMessageType>(
      root_logger.get_publisher()->get_topic_name(), rclcpp::UnitTestDefaultsQoS());
    m_waitset = std::make_shared<rclcpp::dynamic_waitset::Waitset>(m_sub);
    root_logger.get_publisher()->wait_for_matched(1U, std::chrono::seconds(3));
  }

  void receive_and_compare_msg(const apex::logging::LogLevel level, std::string message)
  {
    if (m_waitset->wait(std::chrono::seconds(1))) {
      auto loaned_msg = m_sub->take();
      if (!loaned_msg.empty() && loaned_msg[0].info().valid()) {
        auto msg = loaned_msg[0].data();
        EXPECT_TRUE(msg.text.find(message) != msg.text.npos);
        EXPECT_EQ(static_cast<apex::logging::LogLevel>(msg.level), level);
      } else {
        ADD_FAILURE();
      }
    } else {
      ADD_FAILURE();
    }
  }

private:
  rclcpp::Node::SharedPtr m_node{};
  std::shared_ptr<rclcpp::PollingSubscription<LogMessageType>> m_sub{};
  std::shared_ptr<rclcpp::dynamic_waitset::Waitset> m_waitset{};
};

TEST_F(test_integration_aws_mqtt_logging, enable_trace_logging_to_callback)
{
  using AwsLogLevel = apex::connectors::mqtt::AwsLogLevel;

  std::promise<void> callback_invoked;
  AwsLogLevel expected_level{AwsLogLevel::NONE};
  std::string expected_message{};

  auto setup_expectations = [&expected_level, &expected_message](const auto level,
                                                                 const auto & message) {
    expected_level = level;
    expected_message = message;
  };

  auto verify_call = [&callback_invoked]() {
    EXPECT_TRUE(std::future_status::ready == callback_invoked.get_future().wait_for(MAX_WAIT_TIME));
    callback_invoked = decltype(callback_invoked){};
  };

  auto callback_fn = [&callback_invoked, &expected_level, &expected_message](
                       const AwsLogLevel level, std::string_view message) {
    EXPECT_TRUE(message.find(expected_message) != message.npos);
    EXPECT_EQ(level, expected_level);
    callback_invoked.set_value();
  };

  const auto AWS_C_COMMON_SUBJECT_ID = 0U;
  const AwsLogLevel aws_log_level = AwsLogLevel::TRACE;
  enable_aws_custom_logging(aws_api_handle, aws_log_level, callback_fn);

  setup_expectations(AwsLogLevel::TRACE, "[aws-c-common] - trace_message\n");
  AWS_LOGF_TRACE(AWS_C_COMMON_SUBJECT_ID, "trace_message");
  verify_call();

  setup_expectations(AwsLogLevel::DEBUG, "[aws-c-common] - debug_message\n");
  AWS_LOGF_DEBUG(AWS_C_COMMON_SUBJECT_ID, "debug_message");
  verify_call();

  setup_expectations(AwsLogLevel::INFO, "[aws-c-common] - info_message\n");
  AWS_LOGF_INFO(AWS_C_COMMON_SUBJECT_ID, "info_message");
  verify_call();

  setup_expectations(AwsLogLevel::WARN, "[aws-c-common] - warn_message\n");
  AWS_LOGF_WARN(AWS_C_COMMON_SUBJECT_ID, "warn_message");
  verify_call();

  setup_expectations(AwsLogLevel::ERROR, "[aws-c-common] - error_message\n");
  AWS_LOGF_ERROR(AWS_C_COMMON_SUBJECT_ID, "error_message");
  verify_call();

  setup_expectations(AwsLogLevel::FATAL, "[aws-c-common] - fatal_message\n");
  AWS_LOGF_FATAL(AWS_C_COMMON_SUBJECT_ID, "fatal_message");
  verify_call();

  setup_expectations(AwsLogLevel::NONE, "none_message\n");
  aws_logger_get()->vtable->log(
    aws_logger_get(), aws_log_level::AWS_LL_NONE, AWS_C_COMMON_SUBJECT_ID, "none_message");
  verify_call();
}

TEST_F(test_integration_aws_mqtt_logging, enable_trace_logging_to_root_logger)
{
  using AwsLogLevel = apex::connectors::mqtt::AwsLogLevel;
  const AwsLogLevel aws_log_level{AwsLogLevel::TRACE};
  setup_root_logger_waitset();

  enable_aws_logging(aws_api_handle, aws_log_level);

  auto const AWS_C_COMMON_SUBJECT_ID = 0U;

  AWS_LOGF_TRACE(AWS_C_COMMON_SUBJECT_ID, "trace_message");
  receive_and_compare_msg(apex::logging::LogLevel::TRACE, "[aws-c-common] - trace_message\n");

  AWS_LOGF_DEBUG(AWS_C_COMMON_SUBJECT_ID, "debug_message");
  receive_and_compare_msg(apex::logging::LogLevel::DEBUG, "[aws-c-common] - debug_message\n");

  AWS_LOGF_INFO(AWS_C_COMMON_SUBJECT_ID, "info_message");
  receive_and_compare_msg(apex::logging::LogLevel::INFO, "[aws-c-common] - info_message\n");

  AWS_LOGF_WARN(AWS_C_COMMON_SUBJECT_ID, "warn_message");
  receive_and_compare_msg(apex::logging::LogLevel::WARN, "[aws-c-common] - warn_message\n");

  AWS_LOGF_ERROR(AWS_C_COMMON_SUBJECT_ID, "error_message");
  receive_and_compare_msg(apex::logging::LogLevel::ERROR, "[aws-c-common] - error_message\n");

  AWS_LOGF_FATAL(AWS_C_COMMON_SUBJECT_ID, "fatal_message");
  receive_and_compare_msg(apex::logging::LogLevel::FATAL, "[aws-c-common] - fatal_message\n");

  aws_logger_get()->vtable->log(
    aws_logger_get(), aws_log_level::AWS_LL_NONE, AWS_C_COMMON_SUBJECT_ID, "none_message");
  receive_and_compare_msg(apex::logging::LogLevel::INFO, "none_message\n");
}

TEST_F(test_integration_aws_mqtt_logging,
       enable_trace_logging_to_callback_resets_installed_logging_to_root_logger)
{
  using AwsLogLevel = apex::connectors::mqtt::AwsLogLevel;
  const auto AWS_C_COMMON_SUBJECT_ID = 0U;
  const AwsLogLevel aws_log_level = AwsLogLevel::INFO;
  setup_root_logger_waitset();
  enable_aws_logging(aws_api_handle, aws_log_level);
  AWS_LOGF_INFO(AWS_C_COMMON_SUBJECT_ID, "reset_logger");
  receive_and_compare_msg(apex::logging::LogLevel::INFO, "[aws-c-common] - reset_logger\n");

  std::promise<void> callback_invoked;
  auto callback_fn = [&callback_invoked](auto, auto) { callback_invoked.set_value(); };
  enable_aws_custom_logging(aws_api_handle, aws_log_level, callback_fn);
  AWS_LOGF_INFO(AWS_C_COMMON_SUBJECT_ID, "user_callback");
  EXPECT_TRUE(std::future_status::ready == callback_invoked.get_future().wait_for(MAX_WAIT_TIME));
}

TEST_F(test_integration_aws_mqtt_logging,
       enable_trace_logging_to_root_logger_resets_installed_callback)
{
  using AwsLogLevel = apex::connectors::mqtt::AwsLogLevel;
  std::promise<void> callback_invoked;
  auto callback_fn = [&callback_invoked](auto, auto) { callback_invoked.set_value(); };
  const auto AWS_C_COMMON_SUBJECT_ID = 0U;
  const AwsLogLevel aws_log_level = AwsLogLevel::INFO;
  enable_aws_custom_logging(aws_api_handle, aws_log_level, callback_fn);
  AWS_LOGF_INFO(AWS_C_COMMON_SUBJECT_ID, "user_callback");
  EXPECT_TRUE(std::future_status::ready == callback_invoked.get_future().wait_for(MAX_WAIT_TIME));

  setup_root_logger_waitset();
  enable_aws_logging(aws_api_handle, aws_log_level);
  AWS_LOGF_INFO(AWS_C_COMMON_SUBJECT_ID, "reset_logger");
  receive_and_compare_msg(apex::logging::LogLevel::INFO, "[aws-c-common] - reset_logger\n");
}

TEST_F(test_integration_aws_mqtt_logging, enable_trace_logging_to_file)
{
  auto random_client_name = Aws::Crt::UUID().ToString();
  using AwsLogLevel = apex::connectors::mqtt::AwsLogLevel;

  // Log file is created once AWS client logging is enabled.
  fs::remove(tmp_log_filepath.c_str());
  const AwsLogLevel aws_log_level = AwsLogLevel::TRACE;
  enable_aws_logging(aws_api_handle, aws_log_level, tmp_log_filepath.c_str());
  ASSERT_TRUE(fs::exists(tmp_log_filepath.c_str()));
  fs::remove(tmp_log_filepath.c_str());
}

TEST_F(test_integration_aws_mqtt_logging, enable_trace_logging_invalid_filepath_throws)
{
  using AwsLogLevel = apex::connectors::mqtt::AwsLogLevel;

  const char * empty_filepath = "";
  const AwsLogLevel aws_log_level = AwsLogLevel::TRACE;
  ASSERT_THROW(enable_aws_logging(aws_api_handle, aws_log_level, empty_filepath),
               apex::invalid_argument);

  const char * null_filepath = nullptr;
  ASSERT_THROW(enable_aws_logging(aws_api_handle, aws_log_level, null_filepath),
               apex::invalid_argument);
}
