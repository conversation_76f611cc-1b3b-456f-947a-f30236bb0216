load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_protobuf_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "c_msgs_protobuf_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

cc_library(
    name = "mqtt_connector_utils",
    srcs = ["src/utils.cpp"],
    hdrs = [
        "include/mqtt_connector/utils.hpp",
        "include/mqtt_connector/visibility_control.hpp",
    ],
    includes = ["include/"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils",
        "//grace/monitoring/logging",
        "@aws_crt_cpp",
    ],
)

cc_library(
    name = "mqtt_connector",
    srcs = [
        "src/aws_mqtt_client.cpp",
        "src/mqtt_connector.cpp",
    ],
    hdrs = [
        "include/mqtt_connector/aws_mqtt_client.hpp",
        "include/mqtt_connector/details/mqtt_to_ros.hpp",
        "include/mqtt_connector/details/ros_to_mqtt.hpp",
        "include/mqtt_connector/mqtt_client_interface.hpp",
        "include/mqtt_connector/mqtt_connector.hpp",
        "include/mqtt_connector/ros_mqtt_options.hpp",
    ],
    includes = ["include/"],
    tags = ["exclude_sca"],
    target_compatible_with = ["@platforms//os:linux"],
    visibility = ["//visibility:public"],
    deps = [
        ":mqtt_connector_utils",
        "//common/cpputils",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/rosidl/protobuf_typesupport",
        "@aws_crt_cpp",
    ],
)

cc_library(
    name = "mqtt5_connector",
    srcs = [
        "src/aws_mqtt5_client.cpp",
        "src/mqtt5_connector.cpp",
    ],
    hdrs = [
        "include/mqtt_connector/aws_mqtt5_client.hpp",
        "include/mqtt_connector/details/mqtt_to_ros.hpp",
        "include/mqtt_connector/details/ros_mqtt_service.hpp",
        "include/mqtt_connector/details/ros_to_mqtt.hpp",
        "include/mqtt_connector/mqtt5_connector.hpp",
        "include/mqtt_connector/mqtt_client_interface.hpp",
        "include/mqtt_connector/ros_mqtt_options.hpp",
    ],
    includes = ["include/"],
    tags = ["exclude_sca"],
    target_compatible_with = ["@platforms//os:linux"],
    visibility = ["//visibility:public"],
    deps = [
        ":mqtt_connector_utils",
        "//common/cpputils",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/rosidl/protobuf_typesupport",
        "@aws_crt_cpp",
    ],
)

ros_pkg(
    name = "mqtt_connector_pkg",
    cc_libraries = [
        ":mqtt_connector",
        ":mqtt5_connector",
        ":mqtt_connector_utils",
    ],
    description = "Apex.OS to MQTT connector",
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "mqtt_connector",
    version = "0.1.0",
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils:cpputils_pkg",
        "//grace/execution/apex_init:apex_init_pkg",
        "//grace/execution/executor2:executor2_pkg",
        "//grace/rosidl/protobuf_typesupport:protobuf_typesupport_pkg",
        "@aws_crt_cpp//:aws_crt_cpp_pkg",
    ],
)

apex_cc_test(
    name = "test_aws_mqtt_client",
    srcs = ["test/test_aws_mqtt_client.cpp"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    local = True,
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag. See #30130
    ],
    deps =
        [
            ":mqtt_connector",
            "//tools/testing/apex_test_tools",
            "@googletest//:gtest",
        ],
)

apex_cc_test(
    name = "test_aws_mqtt5_client",
    srcs = ["test/test_aws_mqtt5_client.cpp"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    local = True,
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag. See #30130
    ],
    deps =
        [
            ":mqtt5_connector",
            "//tools/testing/apex_test_tools",
            "@googletest//:gtest",
        ],
)

apex_cc_test(
    name = "test_integration_aws_mqtt_client",
    srcs = [
        "test/gtest_main.cpp",
        "test/mqtt_connector_test_utils.hpp",
        "test/test_integration_aws_mqtt_client.cpp",
    ],
    env = {
        "APEX_GRACE_LOG_LEVEL": "TRACE",
        "IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc",
    },
    local = True,
    tags = ["skip_tsan"],  # FIXME: fix tsan findings and remove this tag. See #30130
    deps = [
        ":mqtt_connector",
        "//tools/testing/apex_test_tools",
        "@googletest//:gtest",
    ],
)

apex_cc_test(
    name = "test_integration_aws_mqtt5_client",
    srcs = [
        "test/gtest_main.cpp",
        "test/mqtt_connector_test_utils.hpp",
        "test/test_integration_aws_mqtt5_client.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    local = True,
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag. See #30130
    ],
    deps =
        [
            ":mqtt5_connector",
            "//tools/testing/apex_test_tools",
            "@googletest//:gtest",
        ],
)

cpp_msgs_protobuf_library(
    name = "std_msgs_proto",
    msgs = "//grace/interfaces/std_msgs",
)

c_msgs_protobuf_library(
    name = "std_msgs_proto_c",
    msgs = "//grace/interfaces/std_msgs",
)

apex_cc_test(
    name = "test_mqtt_connector",
    srcs = [
        "test/fake_mqtt_client.hpp",
        "test/mqtt_connector_test_utils.hpp",
        "test/test_mqtt_connector.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    local = True,
    tags = [
        "manual",
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag. See #30130
    ],
    deps =
        [
            "std_msgs_proto",
            "std_msgs_proto_c",
            ":mqtt_connector",
            "//grace/interfaces/std_msgs",
            "//tools/testing/apex_test_tools",
            "@googletest//:gtest",
        ],
)

apex_cc_test(
    name = "test_integration_mqtt_connector",
    srcs = [
        "test/gtest_main.cpp",
        "test/mqtt_connector_test_utils.hpp",
        "test/test_integration_mqtt_connector.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    local = True,
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag. See #30130
    ],
    deps =
        [
            "std_msgs_proto",
            "std_msgs_proto_c",
            ":mqtt_connector",
            "//grace/interfaces/std_msgs",
            "//tools/testing/apex_test_tools",
            "@googletest//:gtest",
        ],
)

cpp_msgs_protobuf_library(
    name = "example_interfaces_proto",
    msgs = "//grace/examples/example_interfaces",
)

c_msgs_protobuf_library(
    name = "example_interfaces_proto_c",
    msgs = "//grace/examples/example_interfaces",
)

apex_cc_test(
    name = "test_mqtt5_connector",
    srcs = [
        "test/fake_mqtt_client.hpp",
        "test/mqtt_connector_test_utils.hpp",
        "test/test_mqtt5_connector.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    local = True,
    tags = [
        "manual",
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag. See #30130
    ],
    deps =
        [
            "example_interfaces_proto",
            "example_interfaces_proto_c",
            "std_msgs_proto",
            "std_msgs_proto_c",
            ":mqtt5_connector",
            "//grace/examples/example_interfaces",
            "//grace/interfaces/std_msgs",
            "//tools/testing/apex_test_tools",
            "@googletest//:gtest",
        ],
)

apex_cc_test(
    name = "test_integration_mqtt5_connector",
    srcs = [
        "test/gtest_main.cpp",
        "test/mqtt_connector_test_utils.hpp",
        "test/test_integration_mqtt5_connector.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    local = True,
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag. See #30130
    ],
    deps =
        [
            "example_interfaces_proto",
            "example_interfaces_proto_c",
            "std_msgs_proto",
            "std_msgs_proto_c",
            ":mqtt5_connector",
            "//grace/examples/example_interfaces",
            "//grace/interfaces/std_msgs",
            "//tools/testing/apex_test_tools",
            "@googletest//:gtest",
        ],
)

apex_cc_test(
    name = "test_integration_aws_mqtt_logging",
    srcs = [
        "test/gtest_main.cpp",
        "test/mqtt_connector_test_utils.hpp",
        "test/test_integration_aws_mqtt_logging.cpp",
    ],
    env = {
        "APEX_GRACE_LOG_LEVEL": "TRACE",
        "IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc",
    },
    tags = ["skip_tsan"],  # FIXME: fix tsan findings and remove this tag
    deps = [
        ":mqtt_connector_utils",
        "//grace/execution/apex_init",
        "@googletest//:gtest",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "test/create_aws_iot_resources.sh",
    ],
    visibility = [
        "//grace/examples/mqtt_connector_example/doc:__subpackages__",
    ],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
