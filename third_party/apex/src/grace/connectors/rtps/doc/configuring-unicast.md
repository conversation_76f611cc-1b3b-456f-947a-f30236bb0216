# Configuring unicast communication

TODO(#35524): Rewrite this article for cyclone_gateway

## Introduction

By default, the discovery process of DDS entities (including DomainParticipant, Subscriber,
Publisher, etc.) uses a multicast address in the local network. The multicast address is
`***********`, with the port derived from the domain ID for isolation purpose. However, for
special network structure where multicast is disabled, it is also possible to configure
the application to unicast for communication.

This article explains how to enable unicast communication with Apex.Ida.

{{ source_admonition("ApexGraceBilbo") }}

## Port number

The port number that the middleware will be listening to is determined by the DDS configuration.
Refer to the [Controlling port numbers](https://github.com/eclipse-cyclonedds/cyclonedds/blob/51aa411347bb56d7e5c90f5bb62b9cb926e0feea/docs/manual/config/cyclonedds_specifics.rst#controlling-port-numbers)
article for information on how the actual port number to be used is calculated.

## Runtime configuration

Enabling unicast communication requires disabling multicast and setting the unicast parameters at
runtime in the Apex.Grace settings file on both hosts.
See the documentation for the [Apex.Grace Settings package](settings-design.md)
for more information about runtime settings.

Two example files are shown below:

```yaml
Host 1:                                      |   Host 2:
domain:                                      |   domain:
  discovery:                                 |     discovery:
    peers: ["HOST 2 IP"]                     |       peers: ["HOST 1 IP"]
    participant_index: auto                  |       participant_index: auto
    max_auto_participant_index: 8            |       max_auto_participant_index: 8
  general:                                   |     general:
    allow_multicast: false                   |       allow_multicast: false
  tracing:                                   |     tracing:
    output_file: "apex-cyclonedds-host1.log" |       output_file: "apex-cyclonedds-host2.log"
    verbosity: "config"                      |       verbosity: "config"
```

In the settings file:

1. Multicast is disabled by setting `allow_multicast: false`
2. The addresses for discovery are statically configured by setting the host IP in
   `domain/discovery/peers`
3. Set the DDSI participant index to be used for discovery by setting
   `domain/discovery/participant_index` to either `auto`, `none`, or a non-negative integer
4. Specify the maximum DDSI participant index that the middleware can use when
   `domain/discovery/participant_index` is set to `auto`

!!! note
    If one of the host is hidden behind
    [NAT](https://en.wikipedia.org/wiki/Network_address_translation),
    its private and public IP address will be different.
    For instance, if **Host 2** has private IP address `*************` and public
    IP address `*************`, then set the option
    [`domain/general/external_network_address`](
    https://github.com/eclipse-cyclonedds/cyclonedds/blob/51aa411347bb56d7e5c90f5bb62b9cb926e0feea/docs/manual/options.md#cycloneddsdomaingeneralexternalnetworkaddress)
    to `*************` in the configuration file of **Host 1**.
    Using this configuration **Host 2** will advertise its public IP address correctly.

### Performance consideration

Each time the applications starts with the Apex.Grace settings file above, the application sends
multiple discovery messages to different ports of each IP in the `peer` list to determine the
actual port that the remote peer is using. The number of ports to try is controlled by
`domain/discovery/max_auto_participant_index`. Since configuring peers this way will cause a large
burst of packets each time a discovery message is sent, it is recommended to use static port
with unicast communication.

One way to avoid this large burst of packets to each host is to also specify the port number to
the IP address of the remote application as `IP:PORT` in the `peer` list. However, this requires
manually calculating the port number.

Refer to the [Controlling port numbers](https://github.com/eclipse-cyclonedds/cyclonedds/blob/51aa411347bb56d7e5c90f5bb62b9cb926e0feea/docs/manual/config/cyclonedds_specifics.rst#controlling-port-numbers)
article for more detail.

## Systematic example

Two ADE instances are launched in the same machine to simulate the cross-host unicast
communication.

1. In terminal 1, start the first ADE instance and check the IP address

    ```shell dollar
    export ADE_NAME=ade1
    ade start --enter
    ```

    And within the first `ade`:

    ```shell prefix="(ade1)$"
    (ade1)$ ip a
    3741: eth0@if3742: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue state UP group default
    link/ether 02:42:ac:11:00:04 brd ff:ff:ff:ff:ff:ff link-netnsid 0
    inet **********/16 brd ************** scope global eth0  <<<=== this is the IP of host 1
    valid_lft forever preferred_lft forever
    ```

2. Create the Apex.Grace settings file `$HOME/cyclone_dds_config_1.yaml` and copy the following
   content to it:

    ```yaml
    domain:
      discovery:
        peers: ["**********"] # IP Address from the second ade instance
        participant_index: 0
      general:
        allow_multicast: false
      tracing:
        output_file: "apex-cyclonedds-host1.log"
        category: "config, info, discovery"
        verbosity: "fine"
    ```

3. In terminal 2, start the second ADE instance and check the IP address

    ```shell dollar
    export ADE_NAME=ade2
    ade start --enter
    ```

    And within the second `ade`:

    ```shell prefix="(ade2)$"
    (ade2)$ ip a
    4183: eth0@if4184: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue state UP group default
    link/ether 02:42:ac:11:00:05 brd ff:ff:ff:ff:ff:ff link-netnsid 0
    inet **********/16 brd ************** scope global eth0
      valid_lft forever preferred_lft forever
    ```

4. Create the Apex.Grace settings file `$HOME/cyclone_dds_config_2.yaml` and copy the following
content to it

    ```yaml
    domain:
      discovery:
        peers: ["**********"] # IP Address from the first ade instance
        participant_index: 0
      general:
        allow_multicast: false
      tracing:
        output_file: "apex-cyclonedds-host2.log"
        category: "config, info, discovery"
        verbosity: "fine"
    ```

5. The actual IP address will be different from the test environment; make sure the IP address
   in `peer` list is substituted with the correct one, and note that the IP in each Apex.Grace settings
   file is the IP of the other host

6. In terminal 1, start the talker application

    ```shell prefix="(ade1)$"
    (ade1)$ export APEX_MIDDLEWARE_SETTINGS=$HOME/cyclone_dds_config_1.yaml
    (ade1)$ /opt/ApexGraceBilbo/lib/demo_nodes_cpp/talker
    RMW Implementation: rmw_apex_middleware
    [INFO] [talker]: Publishing: 'Hello World: 1'
    [INFO] [talker]: Publishing: 'Hello World: 2'
    ...
    ```

7. In terminal 2, start the listener application; it begins to receive messages sent from
   ADE terminal 1

    ```shell prefix="(ade2)$"
    (ade2)$ export APEX_MIDDLEWARE_SETTINGS=$HOME/cyclone_dds_config_2.yaml
    (ade2)$ /opt/ApexGraceBilbo/lib/demo_nodes_cpp/listener
    RMW Implementation: rmw_apex_middleware
    [INFO] [listener]: I heard: [Hello World: 1]
    [INFO] [listener]: I heard: [Hello World: 2]
    [INFO] [listener]: I heard: [Hello World: 3]
    ...
    ```
