---
tags:
    - SOME/IP
    - connector
---

<!-- markdownlint-disable MD046 -->

# How to use the Apex.Ida SOME/IP connector

On a high level, the following steps are necessary to use the Apex.Ida SOME/IP connector:

1. Use the `cc_msgs_library` to define the interface types for methods/events/fields and configure
   SOME/IP interfaces.
1. Specify the topic mapping in a configuration file.
1. Create a vsomeip_gateway target referring to the configuration
   as well as the interface type targets.
1. Build your application using those types and regular topic/service-based APIs offered
   by Apex.Grace and Apex.Ida
1. Launch the gateway target alongside the application target.

## Using the Apex.Ida SOME/IP connector

To learn how to use the Apex.Ida SOME/IP connector, in  this article we go over one of our examples
where two Apex.Grace applications communicate over the network via SOME/IP. The example can be run
using `bazel` with the following command:

```shell dollar
bazel run @apex//grace/examples/someip_connector/arxml:example
```

In the `host1` there are two applications, the first one called `publisher` and the second one
called `server`. The `publisher` application publishes messages periodically to an
`example_event_topic` topic. The `server` application uses the topic `example_method` to receive and
reply to requests.

The SOME/IP gateway in the `host1` is configured to provide a SOME/IP service consisting of an
`ExampleEvent` and an `ExampleMethod`. It forwards messages published to `example_event_topic` as
SOME/IP `ExampleEvent` events. And to reply to requests to the SOME/IP `ExampleMethod` by forwarding
requests and responses to the `example_method` topic.

```plantuml

skinparam componentStyle rectangle

component host1 {

    component "SOME/IP Gateway" as gw1
    component "publisher" as pub
    component "server" as ser

    pub --> gw1 : example_event_topic
    ser <--> gw1 : example_methodRequest

}

component host2 {

    component "SOME/IP Gateway" as gw2
    component "subscriber" as sub
    component "client" as cli

    sub --> gw2 : example_event_topic
    cli <--> gw2 : example_method

}

gw1 <--> gw2 : " SOME/IP communication"

```

Analogously, in `host2` there are two applications as well called `subscriber` and `client`. The
`subscriber` application logs messages received from `example_event_topic`. The `client` application
sends requests periodically to the `example_method` topic. The SOME/IP gateway in `host2` is
configured to required the service provided by the SOME/IP gateway in `host1`.

In the following sections, we will highlight the steps necessary to setup this example.

### Define the message library target with the support for the interface types

The first step is to define the message types for the interfaces with the SOME/IP events and the
types used. In this example we use a group of ARXML files to describe the interfaces, types and
deployment information needed for the example.

{{
    code_snippet(
        "grace/examples/someip_connector/arxml/BUILD.bazel",
        {"tag": "#! [cc_msgs_library]"},
        "bazel"
    )
}}

1. ARXML defining the service interface with an example event.
1. ARXML defining the service instances.

??? example "grace/examples/someip_connector/arxml/msg/ExampleInterface.arxml"

{{
    code_snippet(
        "grace/examples/someip_connector/arxml/msg/ExampleInterface.arxml",
        {"indent": 4},
        "text"
    )
}}

The `ExampleInterface.arxml` file describes an `ExampleInterface` that has a `ExampleEvent` event
and a `ExampleMethod` method.

??? example "grace/examples/someip_connector/arxml/msg/SomeipInstances.arxml"

{{
    code_snippet(
        "grace/examples/someip_connector/arxml/msg/SomeipInstances.arxml",
        {"indent": 4},
        "text"
    )
}}

The `SomeipInstances.arxml` specifies a service called `example_service` that provides the
`ExampleInterface`.

### Configure Apex.Ida topics to map to SOME/IP

The next step is to write the topic mapping configuration that specifies what Apex.Ida topics are
going to be used to send or receive messages over SOME/IP. In the case of the gateway in `host1`, it
is configured to _provide_ the `example_service`.

{{ code_snippet("grace/examples/someip_connector/config/host1.vsomeip_gateway.yaml", {}, "yaml") }}

1. The key attribute tells the gateway what service to provide, in this case `example_service`.
1. The value of the `event` attribute is the name of the event in the interface, in this case
   `ExampleEvent`.
1. Regular pub-sub topics are derived by prefixing the publisher or subscriber name with `rt/`.
1. The value of the `method` attribute is the name of the method in the interface, in this case
   `ExampleMethod`.
1. The request topic is deriving from the service name by adding the prefix `rq/` and the suffix
   `Request`.
1. The response topic is deriving from the service name by adding the prefix `rr/` and the suffix
   `Reply`.
1. The unicast address of `host1`

The configuration is provided as the input to the gateway target in `bazel` as in the next snippet.

{{
    code_snippet(
        "grace/examples/someip_connector/arxml/BUILD.bazel",
        {"tag": "#! [host1 vsomeip_gateway]"},
        "bazel"
    )
}}

1. `YAML` file with the topic mapping.
1. Dependency to the interfaces and deployment.

At this point, the gateway target can be run using `bazel` as follows:

```shell dollar
bazel run @apex//grace/examples/someip_connector/arxml:host1_someip_gateway
```

!!! info

    gateway targets depend on the resource creator to access to the shared memory resources.
    Therefore, ensure that the resource creator is running before starting a gateway target.

    ```shell dollar
    bazel run @apex//ida/resource_creator
    ```

The gateway in the `host2` is configured in the same manner with the main difference being that
instead of offering the `example_service`, it _requires_ it.

{{ code_snippet("grace/examples/someip_connector/config/host2.vsomeip_gateway.yaml", {}, "yaml") }}

1. The `example_service` is required instead of provided.
1. The unicast address of `host2`

{{
    code_snippet(
        "grace/examples/someip_connector/arxml/BUILD.bazel",
        {"tag": "#! [host2 vsomeip_gateway]"},
        "bazel"
    )
}}

### Publishing and Subscribing to SOME/IP topics in an Apex.Grace application

Finally, once the gateway is configured and running, Apex.Grace applications can interact with
SOME/IP by publishing or subscribing to the mapped topics. The only requirement is that the
Apex.Grace application has to depend on the target with the interfaces and type definitions:

{{
    code_snippet(
        "grace/examples/someip_connector/arxml/BUILD.bazel",
        {"tag": "#! [publisher]"},
        "bazel"
    )
}}

1. Add the interface types as a dependency.

In the application, the generated headers must be included to use the interface types in the topics:

{{
    code_snippet(
        "grace/examples/someip_connector/srcs/publisher.cpp",
        {"tag": "//! [interface headers]"},
        "cpp"
    )
}}

1. The header files are under the package name specified in the `cc_msgs_library`, in this case `example_msgs`

!!! info

    Publishing and subscribing to mapped topics is done in the same way as any other Apex.Grace.
    However, it is important to note that since serialization and de-serialization is unavoidable
    for SOME/IP message types, the corresponding QoS settings in the resource limits must be
      configured with a sensible serialization size for the size of non-self-contained type.

{{
    code_snippet(
        "grace/examples/someip_connector/srcs/publisher.cpp",
        {"tag": "//! [Publisher class]"},
        "cpp"
    )
}}

1. Optional adjustment of the serialized size for SOME/IP samples.
1. The publisher type `example_msgs::msg::ExampleInterface::ExampleEvent` is generated by the
   `cc_msgs_library` target.
1. The topic name is `example_event_topic`, the prefix `rt/` is added by the `rmw` layer before
   creating the underlying Apex.Ida topic.
1. The message published here is forwarded by the gateway as an `ExampleEvent` notification.

{{
    code_snippet(
        "grace/examples/someip_connector/srcs/server.cpp",
        {"tag": "//! [Server class]"},
        "cpp"
    )
}}

1. The generated method type, `example_msgs::msg::ExampleInterface::ExampleMethod`.
1. The service name `example_method` is the used to derive the request topic
   `rq/example_methodRequest` and the response topic `rr/example_methodReply`.

!!! info

    SOME/IP topics have a limited guarantees in terms of QoS settings due to the restrictions
    inherent to the underlying SOME/IP transport. See [Apex.Ida SOME/IP topic QoS
    Settings](#apexida-someip-topic-qos-settings) for more information.

The `client` and `subscriber` applications are analogously similar to `server` and `publisher`.

### Using Apex.Grace process manager to ease handling gateway processes

Apex.Grace applications communicating over SOME/IP involve multiple processes since a SOME/IP
gateway needs to run alongside them. The Apex.Grace process manager can be a helpful tool to ensure
that the SOME/IP gateway is executed together with the Apex.Grace application.

{{ code_snippet("grace/examples/someip_connector/arxml/config/host1.launch.yaml", {}, "yaml") }}

In the launch file, it is possible to specifies the `process-dependencies` to the gateway from the
Apex.Grace application like the `publisher` and `server`, to ensure the gateway is present and
running before the Apex.Grace application starts.

{{
    code_snippet(
        "grace/examples/someip_connector/arxml/BUILD.bazel",
        {"tag": "#! [host1 process_manager]"},
        "bazel"
    )
}}

The `bazel` target can be executed using `bazel`:

```shell dollar
bazel run @apex//grace/examples/someip_connector/arxml:host1_process_manager
```

## Connecting to the SOME/IP Gateway from Apex.Grace

Since SOME/IP events, methods and fields are logically grouped as services that can become available
or unavailable during runtime, the availability of Apex.Ida topic participants will determine the
availability of those _provided_ or _required_ SOME/IP services.

### Provided Endpoints

Before making a provided SOME/IP service available, the gateway will make sure that at least 1
Apex.Ida participant is matched for every topic configured for the service to provide in the
gateway configuration.

The topics in the provided section are immediately made available to Apex.Ida applications by the
gateway during its initialization. After a participant is matched with every topic of a provided
service, the gateway offers the SOME/IP service. After this point, Apex.Ida is considered to be
connected to the Gateway and all messages arriving to the gateway from either Apex.Ida or SOME/IP
are mapped to the corresponding event, method, field or Apex.Ida topic and sent to the other side.

!!! note

    In order to offer a SOME/IP service, all the involved Apex.Ida endpoints must match with their
    counterpart in the Gateway. Otherwise, services will not be offered via SOME/IP!

```plantuml
@startuml

participant "Apex.Ida Application" as app
participant "Apex.Ida Discovery" as discovery
participant "SOME/IP Gateway" as gw
participant "SOME/IP World" as someip

app -> discovery : available provided endpoint
activate discovery #LightBlue

activate gw #LightBlue
gw -> gw : init
gw -> discovery : available endpoint counterpart
deactivate gw

discovery -> gw : match with provided endpoint

activate gw #LightBlue
gw -> someip : offer provided SOME/IP service or event
deactivate gw #LightBlue

discovery -> app : match with counterpart in gateway
deactivate discovery #LightBlue

@enduml
```

### Required Endpoints

The topics for required SOME/IP services are made
available to Apex.Ida after the SOME/IP service is discovered by the gateway. Topics for required
SOME/IP events are made available once the gateway subscription is accepted.

```plantuml
@startuml

participant "Apex.Ida Application" as app
participant "Apex.Ida Discovery" as discovery
participant "SOME/IP Gateway" as gw
participant "SOME/IP World" as someip

app -> discovery : interested endpoint available
activate discovery #LightBlue

someip -> gw : required SOME/IP service found
activate gw #LightBlue
gw -> someip : subscribe to events
someip -> gw : subscriptions accepted
gw -> discovery : available endpoint
deactivate gw

discovery -> app : match with endpoint in gateway
discovery -> gw : match with endpoint
deactivate discovery #LightBlue

@enduml
```

### Apex.Ida SOME/IP Topic QoS Settings

The QoS Settings used by topics mapped to SOME/IP event, methods or fields are limited and
implicitly configured since SOME/IP does not support every Apex.Ida QoS setting.

### History and Durability

Every gateway endpoint is created with QoS Durability `volatile` except
those corresponding to SOME/IP field notifications. Neither the SOME/IP Gateway nor the SOME/IP
implementation support message history.

Endpoints mapped to SOME/IP field notifiers are configured with a QoS
History of `keep_last(1)` and a QoS durability of
`transient_local`. The reason is that SOME/IP fields notify their last value
upon subscription.

### Reliability

The QoS Reliability is determined by the protocol
configured in the SOME/IP deployment model. Topics for broadcast or methods
using TCP will be created with QoS reliability of `reliable` and `best_effort`
for the case of UDP.

### Resource Limits

Configuring the resource limits might be necessary depending on the size of the message types. It is
recommended to read the Configuring Resource Limits QoS article.
