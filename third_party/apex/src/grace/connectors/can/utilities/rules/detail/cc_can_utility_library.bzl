load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "RuleMetaInfo")
load("@apex//common/bazel/helpers:snake_case.bzl", "to_snake_case_with_exceptions")
load("@apex//common/bazel/rules_cc:defs.bzl", "cc_library_with_hdrs_extracted_from_srcs")
load("@apex//ida/bazel:detail/providers.bzl", "TypeModel", "create_ida_idl_info", "get_real_short_path", "make_import_lookup_map_for_json")
load("@apex//tools/bazel/rules_repo:defs.bzl", "sub_target_name")
load("@bazel_skylib//lib:paths.bzl", "paths")

def _can_utility_library_impl(ctx):
    if len(ctx.attr.srcs) > 1:
        fail("cc_can_utility_library: Only one TypeModel input supported.")

    config_json_path = ctx.actions.declare_file(ctx.label.name + "/can_utilities_config.json")
    input_deps = [config_json_path]

    mapping = {}
    generated_can_helpers_files = []

    type_model = ctx.attr.srcs[0][TypeModel]
    input_deps.append(type_model.serialized_ast)

    dep_typemodels = [t.serialized_ast for t in type_model.typemodel_deps.to_list()]
    input_deps.extend(dep_typemodels)

    for dsl_file in type_model.dsl_files:
        dsl_path = get_real_short_path(dsl_file)
        dsl_short_path = dsl_path
        snake_case_name = to_snake_case_with_exceptions(paths.basename(dsl_short_path))

        base_name = paths.dirname(paths.replace_extension(dsl_short_path, ""))
        stem = paths.join(base_name, snake_case_name)

        generated_header_file = ctx.actions.declare_file(stem + "_can_helpers.hpp")

        generated_can_helpers_files.append(generated_header_file)
        mapping[dsl_path] = (generated_header_file,)

    import_lookup = make_import_lookup_map_for_json(all_deps = ctx.attr.deps)

    serialization_config_json = {
        "serialization_config": {
            "type_model_path": type_model.serialized_ast.path,
            "dep_typemodels": [t.path for t in dep_typemodels],
            "output_map": {k: paths.replace_extension(v[0].path, "") for k, v in mapping.items()},
            "import_lookup_map": import_lookup,
            "genfile_prefix": ctx.genfiles_dir.path + "/",
        },
    }

    ctx.actions.write(config_json_path, json.encode(serialization_config_json))

    generator_args = ctx.actions.args()
    generator_args.add("--config", config_json_path)

    ctx.actions.run(
        executable = ctx.executable._generator,
        arguments = [generator_args],
        inputs = input_deps,
        outputs = generated_can_helpers_files,
    )

    deps = generated_can_helpers_files

    idl_info = create_ida_idl_info(ctx.label.workspace_root, all_deps = ctx.attr.deps, generator_name = "can_helpers", own_mapping = mapping)

    return [
        DefaultInfo(
            files = depset(deps),
        ),
        idl_info,
        RuleMetaInfo(),
    ]

_can_utility_library = rule(
    implementation = _can_utility_library_impl,
    attrs = {
        "srcs": attr.label_list(
            doc = "TypeModel dependencies",
            providers = [[TypeModel]],
            default = [],
        ),
        "deps": attr.label_list(
            default = [],
        ),
        "_generator": attr.label(
            default = Label("@apex//grace/connectors/can/utilities/generator:generator"),
            executable = True,
            cfg = "exec",
        ),
    },
)

def cc_can_utility_library(*, name, srcs, deps = [], target_compatible_with = [], **kwargs):
    tags = kwargs.get("tags", default = []) or []
    _can_utility_library(
        name = sub_target_name("files", name),
        srcs = srcs,
        deps = deps,
        target_compatible_with = target_compatible_with,
        tags = tags,
    )

    cc_library_with_hdrs_extracted_from_srcs(
        name = name,
        srcs = [sub_target_name("files", name)],
        deps = deps,
        target_compatible_with = target_compatible_with,
        **kwargs
    )
