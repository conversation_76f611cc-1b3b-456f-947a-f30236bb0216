load("//tools/bazel/rules_docs:defs.bzl", "bzl_docs")

bzl_docs(
    name = "bzl_docs",
    srcs = ["defs.bzl"],
    bzl_docs_deps = [
        "//common/bazel/aspects/dependencies:bzl_docs",
        "//common/bazel/aspects/rule_meta_aspect:bzl_docs",
        "//common/bazel/helpers:bzl_docs",
        "//ida/bazel:bzl_docs",
        "//tools/bazel/rules_repo:bzl_docs",
    ],
    detail_srcs = glob(["detail/*.bzl"]),
    tags = ["manual"],  # Marking this target as manual since it cannot be built independently due to cyclic dependencies
    deps = [
        "@bazel_skylib//rules:common_settings",
    ],
)
