# Copyright 2024 Apex.AI, Inc.
# All rights reserved.

import argparse
from typing import Sequence
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, StrictUndefined
from idl_common import common_model, type_model, include_computation, misc

from grace.connectors.can.utilities.generator import (
    view_models,
    serialization_configuration as config,
)

_template_env = Environment(
    loader=FileSystemLoader(str(Path(__file__).parent / "templates")),
    undefined=StrictUndefined,
    trim_blocks=True,
    lstrip_blocks=True,
    keep_trailing_newline=True,
    autoescape=True,
)


def _render_templates(
    serialization_config: config.SerializationConfig,
) -> None:
    """
    Writes header (filled out) templates to files.

    :param view_models: Header view_models to write to files.
    :param serialization_config: CAN serialization configuration.
    """
    idl_model = common_model.IdlModel.from_json(
        serialization_config.type_model_path.read_text()
    )
    dep_type_models = [
        common_model.IdlModel.from_json(p.read_text())
        for p in serialization_config.dep_typemodels
    ]
    type_model.merge_typemodels(idl_model, dep_type_models)
    idl_model.ast.resolve()

    include_helper = include_computation.IncludeGenerator(
        idl_model, genfile_prefix=serialization_config.genfile_prefix
    )
    import_lookup_map = serialization_config.import_lookup_map

    header_template = _template_env.get_template("can_helpers.hpp.j2")

    for dsl_file, gen_file in serialization_config.output_map.items():
        nodes_in_file = idl_model.dsl_file_to_nodes[dsl_file]

        idl_model.own_nodes = nodes_in_file

        header_file_path = Path(gen_file).with_suffix(".hpp")

        header_file_without_genfile_prefix = include_computation.sanitize_path(
            header_file_path, genfile_prefix=serialization_config.genfile_prefix
        )

        view_model = view_models.create_view_model(
            idl_model=idl_model, only_nodes=idl_model.own_nodes
        )

        include_guard = misc.make_include_guard(Path(dsl_file), header_file_path.stem)
        header_guard = f"GRACE_CONNECTORS_CAN_UTILITIES__{include_guard}_HPP"

        generated_rosidl_include = include_helper.generated_file_for_dsl_file(
            dsl_file=dsl_file, mapping=import_lookup_map["rosidl_generator_cpp"]
        )

        generated_helper_include = include_helper.generated_file_for_dsl_file(
            dsl_file=dsl_file, mapping=import_lookup_map["can_helpers"]
        )

        header_includes = [
            header_file_without_genfile_prefix,
            f"{generated_rosidl_include}.hpp",
            f"{generated_helper_include}.hpp",
        ]

        header_file_path.write_text(
            header_template.render(
                {
                    "m": view_model,
                    "header_includes": header_includes,
                    "header_guard": header_guard,
                }
            )
        )


def _parse_args(arguments: Sequence[str]) -> config.CliConfig:
    """Parse command line arguments into a config object.

    :param arguments: CLI arguments WITHOUT the initial binary name.
    :returns: parsed config.
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config_path",
        type=Path,
        required=True,
        help="path to config json",
    )

    parsed_arguments = parser.parse_args(arguments)
    with Path.open(parsed_arguments.config_path, "r") as json_file:
        json_string = json_file.read()

    return config.CliConfig.model_validate_json(json_string)


def main(arguments: Sequence[str]) -> None:
    configuration = _parse_args(arguments)
    _render_templates(configuration.serialization_config)
