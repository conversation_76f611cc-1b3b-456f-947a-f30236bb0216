# Copyright 2024 Apex.AI, Inc.
# All rights reserved.

import json
import os
from pathlib import Path
import tempfile
from grace.connectors.can.utilities.generator import generator
import pytest


def test_failing_argument_parsing():
    with pytest.raises(SystemExit):
        _ = generator._parse_args(["wrong_argument"])


def test_successful_argument_parsing():
    with tempfile.NamedTemporaryFile(suffix=".json") as file:
        Path(file.name).write_text(
            json.dumps(
                {
                    "serialization_config": {
                        "output_map": {"foo.dbc": "bar"},
                        "genfile_prefix": "foo/bar",
                        "type_model_path": "bar",
                        "dep_typemodels": [],
                        "import_lookup_map": {"idlc": {"foo.dbc": "foo.idl"}},
                    }
                }
            )
        )

        config = generator._parse_args(["--config", file.name])
        assert config.serialization_config
        assert os.fspath(config.serialization_config.output_map["foo.dbc"]) == "bar"
        assert os.fspath(config.serialization_config.genfile_prefix) == "foo/bar"
        assert os.fspath(config.serialization_config.type_model_path) == "bar"
        assert config.serialization_config.dep_typemodels == []
        assert (
            os.fspath(config.serialization_config.import_lookup_map["idlc"]["foo.dbc"])
            == "foo.idl"
        )
