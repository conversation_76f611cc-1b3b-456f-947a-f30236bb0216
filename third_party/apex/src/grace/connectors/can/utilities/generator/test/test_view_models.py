# Copyright 2024 Apex.AI, Inc.
# All rights reserved.

# disable magic-value-comparison error
# ruff: noqa: PLR2004, E501

from grace.connectors.can.utilities.generator import view_models

from ida.idls.backends.can_dds_utility_backend.test import model_helper


def test_view_models():
    models = model_helper.make_standard_multiplexed_message("mock_db")
    vms = view_models.create_view_model(
        models.tm,
        only_nodes=models.tm.dsl_file_to_nodes[model_helper.mock_dsl_filename()]
        + [["Root"]],
    )

    assert len(vms.idl.structs) == 1
    assert vms.idl.structs[0].name == "standard_multiplexed_signals_message"
    assert (
        vms.idl.structs[0].type_fqn == "mock_db::standard_multiplexed_signals_message"
    )
