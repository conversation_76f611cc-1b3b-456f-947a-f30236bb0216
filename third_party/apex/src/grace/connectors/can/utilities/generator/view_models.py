# Copyright 2024 Apex.AI, Inc.
# All rights reserved.

from typing import List
from dataclasses import dataclass
from rosidl_parser_ng.generator.transformer import skip_included_nodes, Transformer
from rosidl_parser_ng.model import RosidlModel, AstNodePath
from rosidl_parser_ng.generator.utils import (
    get_node_namespaces,
)


@dataclass
class StructViewModel:
    name: str
    type_fqn: str
    ros_type_fqn: str


@dataclass
class IdlViewModel:
    """View of ROSIDL model."""

    structs: List[StructViewModel]


@dataclass
class ViewModel:
    """Contain all view models for templates rendering."""

    idl: IdlViewModel


class ViewModelTransformer(Transformer):
    def __init__(self, own_nodes):
        self.idl_model = IdlViewModel(structs=[])
        self.own_nodes = own_nodes

    @skip_included_nodes
    def transform_type_def(self, _, __):
        pass

    @skip_included_nodes
    def transform_enum(self, _, __):
        pass

    @skip_included_nodes
    def transform_constant(self, _, __):
        pass

    @skip_included_nodes
    def transform_event_member(self, node, children):
        pass

    @skip_included_nodes
    def transform_event(self, node, children) -> StructViewModel:
        return self.transform_struct(node, children)

    @skip_included_nodes
    def transform_struct_member(self, node, children):
        pass

    @skip_included_nodes
    def transform_struct(self, node, _) -> StructViewModel:
        type_fqn = "::".join(get_node_namespaces(node) + [node.name])
        return [
            StructViewModel(
                name=node.name,
                type_fqn=type_fqn,
                ros_type_fqn=f"::{type_fqn}_",
            )
        ]

    @skip_included_nodes
    def transform_union_member(self, node, _):
        pass

    @skip_included_nodes
    def transform_union_type(self, node, children):
        pass

    @skip_included_nodes
    def transform_interface(self, node, children):
        return self.transform_namespace(node, children)

    @skip_included_nodes
    def transform_namespace(self, _, children):
        for sublist in children:
            for c in sublist:
                if isinstance(c, StructViewModel):
                    self.idl_model.structs.append(c)
                else:
                    pass

    def transform_root_node(self, _, __):
        return ViewModel(
            idl=self.idl_model,
        )


def create_view_model(
    idl_model: RosidlModel, only_nodes: List[AstNodePath]
) -> ViewModel:
    transformer = ViewModelTransformer(only_nodes)
    view_models = transformer.transform(idl_model)
    return view_models
