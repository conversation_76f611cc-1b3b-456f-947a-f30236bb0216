{# Copyright 2025 Apex.AI, Inc. #}
{# All rights reserved. #}
// generated from grace/connectors/can/utilities/generator/templates/can_helpers.hpp.j2
// generated code does not contain a copyright notice

#ifndef {{header_guard}}
#define {{header_guard}}

{% for include in header_includes |sort %}
#include "{{include}}"
{% endfor %}
{% for struct in m.idl.structs %}

namespace apex::connectors::can::detail
{

template<typename ContainerAllocator, bool UseFlat>
struct MultiplexingTypeTraits<{{struct.ros_type_fqn}}<ContainerAllocator, UseFlat>>
{
  using GraceType = {{struct.ros_type_fqn}}<ContainerAllocator, UseFlat>;
  using IdaGraceTypeTraits = apex::apex_middleware::DDS_ROS_TypeTraits<GraceType>;

  using IdaType = typename IdaGraceTypeTraits::DDSType;
  using MuxType = apex::{{struct.type_fqn}}::message;

  static MuxType expand(const GraceType & message_from)
  {
    {# Perform double copy ROSType -> DDSType -> MuxType #}
    IdaType ida_message;
    (void)IdaGraceTypeTraits::convert_ros_message_to_dds(message_from, ida_message);
    return MuxType::expand_from(ida_message);
  }

  static void flatten(const MuxType & message_from, GraceType & message_to)
  {
    {# Perform double copy DDSType -> MuxType -> ROSType #}
    IdaType ida_message;
    message_from.flatten_to(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }

  static void init(GraceType & message_to)
  {
    {# Initialize object with copy DDSType -> ROSType #}
    IdaType ida_message;
    MuxType::init(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }
};

}  // namespace apex::connectors::can::detail
{% endfor %}
#endif  // {{header_guard}}
