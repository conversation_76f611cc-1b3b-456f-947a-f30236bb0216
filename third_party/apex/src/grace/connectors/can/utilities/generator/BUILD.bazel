load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_binary", "py_library")

py_library(
    name = "can_helpers_lib",
    srcs = [
        "generator.py",
        "serialization_configuration.py",
        "view_models.py",
    ],
    data = [
        "templates/can_helpers.hpp.j2",
    ],
    visibility = [
        ":__subpackages__",
        "//grace/connectors/can/utilities/test:__subpackages__",
    ],
    deps = [
        "//grace/rosidl/rosidl_cmake:rosidl_cmake_python",
        "//grace/rosidl/rosidl_parser_ng:rosidl_parser_ng_python",
        requirement("jinja2"),
        requirement("pydantic"),
    ],
)

py_binary(
    name = "generator",
    srcs = [
        "main.py",
    ],
    legacy_create_init = 0,  # required for py_binaries used on execution platform
    main = "main.py",
    visibility = ["//visibility:public"],
    deps = [
        ":can_helpers_lib",
    ],
)
