// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#include <iostream>
#include <vector>

#include "gtest/gtest.h"
#include "ida/idls/frontends/dbc/test/config/test_messages_can_helpers.hpp"

// This file provides unit tests for multiplexed messages in ida/idls/frontends/dbc/test/config/test_messages.dbc
// Below provided are layouts for messages under test:
//
// simple_multiplex:
// [
//   {
//     'command_mux': {
//       0: ['read_cmd'],
//       1: ['write_cmd']
//     }
//   },
//   "timestamp"
// ]


TEST(CanRosHelpersTest, test_apex_type_works_with_ros_type)
{
  using RosType = test_messages_dbc::simple_multiplex;
  using MuxType = apex::connectors::can::MuxMessage<RosType>;

  MuxType message;
  RosType ros_msg;

  apex::test_messages_dbc::simple_multiplex::command_mux_0::page page_0;

  page_0.timestamp(0xF0);
  page_0.read_cmd(0x44);
  message.set(page_0);

  message.flatten_to(ros_msg);
  EXPECT_EQ(ros_msg.command_mux, 0);
  EXPECT_EQ(ros_msg.timestamp, 0xF0);
  EXPECT_EQ(ros_msg.read_cmd, 0x44);

  apex::test_messages_dbc::simple_multiplex::command_mux_1::page page_1;

  page_1.timestamp(0xFF);
  page_1.write_cmd(0x88);
  message.set(page_1);

  message.flatten_to(ros_msg);
  EXPECT_EQ(ros_msg.command_mux, 1);
  EXPECT_EQ(ros_msg.timestamp, 0xFF);
  EXPECT_EQ(ros_msg.write_cmd, 0x88);
}

TEST(CanRosHelpersTest, test_init_ros_message)
{
  using RosType = test_messages_dbc::test_initial_signal_values;
  using MuxType = apex::connectors::can::MuxMessage<RosType>;

  // check the default values provided by can_serialization
  RosType msg;
  MuxType::init(msg);
  ASSERT_EQ(msg.int_signal, 8);
  ASSERT_EQ(msg.int_signal_no_initial_value, 0);
  ASSERT_EQ(msg.int_signal_with_scale_offset, 6.0f);

  // in the DBC: BA_ "GenSigStartValue" SG_ 8 int_signal_with_scale_offset_2 3;
  // After factor and offset are applied, the value should be: 0
  ASSERT_EQ(msg.int_signal_with_scale_offset_2, 0);

  // NOTE(alivenets): the initial value of IEEE floating-point signal is truncated to int
  ASSERT_EQ(msg.float_signal, 42.0f);
  ASSERT_EQ(msg.mux, 2);
  ASSERT_EQ(msg.s0, 42);
  ASSERT_EQ(msg.s1, 7);
  ASSERT_EQ(msg.s2, 3);
}
