# Copyright 2024 Apex.AI, Inc.
# All rights reserved.
# flake8: noqa

from pathlib import Path
from typing import NamedTuple

from grace.connectors.can.utilities.generator import generator
from idl_common.common_model import (
    RosidlModel,
)

from ida.connectors.can.model.test.can_model_testing_helpers import (
    mock_type_model,
)

from grace.connectors.can.utilities.generator import (
    serialization_configuration as config,
)


class TestModels(NamedTuple):
    """can_model with the corresponding type_model."""

    __test__ = False

    type_model: RosidlModel


class TestConfiguration(NamedTuple):
    """can_model with the corresponding type_model."""

    __test__ = False

    config_file: Path
    generated_hpp_file: Path


def create_configuration(tmp_path_factory, type_model: RosidlModel) -> TestConfiguration:
    """
    Creates test setup for generator.

    Created directories:
     - generated - for generator generated files
     - input, contains:
       - generator_config.json
       - rosidl_model.json
    """

    generated_dir = tmp_path_factory.mktemp("generated")
    generated_hpp_file = generated_dir / "can_helpers.hpp"

    input_dir_path = tmp_path_factory.mktemp("input")
    config_file_path = input_dir_path / "generator_config.json"
    type_model_path = input_dir_path / "type_model.json"

    serialization_config = config.SerializationConfig(
        output_map={"foo.dbc": str(generated_hpp_file)},
        genfile_prefix=str(generated_dir) + "/",
        type_model_path=type_model_path,
        dep_typemodels=[],
        import_lookup_map={"idlc": {"foo.dbc": "foo.idl"}, "rosidl_generator_cpp": {"foo.dbc": "foo.idl"}, "can_helpers": {"foo.dbc": "foo_dds_helpers"},},
    )
    generator_config = config.CliConfig(
        serialization_config=serialization_config,
    )

    config_file_path.write_text(generator_config.model_dump_json())
    type_model_path.write_text(type_model.model_dump_json())

    return TestConfiguration(
        config_file=config_file_path,
        generated_hpp_file=generated_hpp_file,
    )


def test_correct_file_content_serialization(
    tmp_path_factory, mock_type_model
):
    expected_hpp_file = Path(__file__).parent.joinpath(
        "expected_files/can_helpers.hpp"
    )

    type_model = mock_type_model

    test_config = create_configuration(tmp_path_factory, type_model)

    generator.main(["--config", str(test_config.config_file)])

    with open(expected_hpp_file, "r") as expected, open(
        test_config.generated_hpp_file, "r"
    ) as gen:
        assert expected.readlines()[3:-2] == gen.readlines()[3:-2]

