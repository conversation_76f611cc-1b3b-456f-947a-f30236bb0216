load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")

msgs_library(
    name = "test_msgs",
    dbc_srcs = ["//ida/idls/frontends/dbc/test:dbc_files"],
    pkg_name = "test_msgs",
)

apex_cc_test(
    name = "test_generated_can_helpers",
    srcs = [
        "test_can_utilities.cpp",
    ],
    deps = [
        ":test_msgs",
        "@googletest//:gtest_main",
    ],
)

apex_py_test(
    name = "generator_test_py",
    srcs = [
        "test_generator.py",
    ],
    data = [
        "expected_files/can_helpers.hpp",
    ],
    tags = ["exclude_sca"],
    deps = [
        "//ida/connectors/can/model:can_model_testing_helpers",
        "//ida/idls/idl_common:idl_common_testing_helpers",
        "@apex//grace/connectors/can/utilities/generator:can_helpers_lib",
    ],
)
