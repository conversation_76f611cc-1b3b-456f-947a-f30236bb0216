// generated from grace/connectors/can/utilities/generator/templates/can_helpers.hpp.j2
// generated code does not contain a copyright notice

#ifndef GRACE_CONNECTORS_CAN_UTILITIES__CAN_HELPERS_E6D10C32_HPP
#define GRACE_CONNECTORS_CAN_UTILITIES__CAN_HELPERS_E6D10C32_HPP

#include "can_helpers.hpp"
#include "foo.idl.hpp"
#include "foo_dds_helpers.hpp"

namespace apex::connectors::can::detail
{

template<typename ContainerAllocator, bool UseFlat>
struct MultiplexingTypeTraits<::mock_can_db::PlainCanMessage_<ContainerAllocator, UseFlat>>
{
  using GraceType = ::mock_can_db::PlainCanMessage_<ContainerAllocator, UseFlat>;
  using IdaGraceTypeTraits = apex::apex_middleware::DDS_ROS_TypeTraits<GraceType>;

  using IdaType = typename IdaGraceTypeTraits::DDSType;
  using MuxType = apex::mock_can_db::PlainCanMessage::message;

  static MuxType expand(const GraceType & message_from)
  {
    IdaType ida_message;
    (void)IdaGraceTypeTraits::convert_ros_message_to_dds(message_from, ida_message);
    return MuxType::expand_from(ida_message);
  }

  static void flatten(const MuxType & message_from, GraceType & message_to)
  {
    IdaType ida_message;
    message_from.flatten_to(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }

  static void init(GraceType & message_to)
  {
    IdaType ida_message;
    MuxType::init(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }
};

}  // namespace apex::connectors::can::detail

namespace apex::connectors::can::detail
{

template<typename ContainerAllocator, bool UseFlat>
struct MultiplexingTypeTraits<::mock_can_db::FloatCanMessage_<ContainerAllocator, UseFlat>>
{
  using GraceType = ::mock_can_db::FloatCanMessage_<ContainerAllocator, UseFlat>;
  using IdaGraceTypeTraits = apex::apex_middleware::DDS_ROS_TypeTraits<GraceType>;

  using IdaType = typename IdaGraceTypeTraits::DDSType;
  using MuxType = apex::mock_can_db::FloatCanMessage::message;

  static MuxType expand(const GraceType & message_from)
  {
    IdaType ida_message;
    (void)IdaGraceTypeTraits::convert_ros_message_to_dds(message_from, ida_message);
    return MuxType::expand_from(ida_message);
  }

  static void flatten(const MuxType & message_from, GraceType & message_to)
  {
    IdaType ida_message;
    message_from.flatten_to(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }

  static void init(GraceType & message_to)
  {
    IdaType ida_message;
    MuxType::init(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }
};

}  // namespace apex::connectors::can::detail

namespace apex::connectors::can::detail
{

template<typename ContainerAllocator, bool UseFlat>
struct MultiplexingTypeTraits<::mock_can_db::MultiplexedCanMessage_<ContainerAllocator, UseFlat>>
{
  using GraceType = ::mock_can_db::MultiplexedCanMessage_<ContainerAllocator, UseFlat>;
  using IdaGraceTypeTraits = apex::apex_middleware::DDS_ROS_TypeTraits<GraceType>;

  using IdaType = typename IdaGraceTypeTraits::DDSType;
  using MuxType = apex::mock_can_db::MultiplexedCanMessage::message;

  static MuxType expand(const GraceType & message_from)
  {
    IdaType ida_message;
    (void)IdaGraceTypeTraits::convert_ros_message_to_dds(message_from, ida_message);
    return MuxType::expand_from(ida_message);
  }

  static void flatten(const MuxType & message_from, GraceType & message_to)
  {
    IdaType ida_message;
    message_from.flatten_to(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }

  static void init(GraceType & message_to)
  {
    IdaType ida_message;
    MuxType::init(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }
};

}  // namespace apex::connectors::can::detail

namespace apex::connectors::can::detail
{

template<typename ContainerAllocator, bool UseFlat>
struct MultiplexingTypeTraits<::mock_can_db::DoubleCanMessage_<ContainerAllocator, UseFlat>>
{
  using GraceType = ::mock_can_db::DoubleCanMessage_<ContainerAllocator, UseFlat>;
  using IdaGraceTypeTraits = apex::apex_middleware::DDS_ROS_TypeTraits<GraceType>;

  using IdaType = typename IdaGraceTypeTraits::DDSType;
  using MuxType = apex::mock_can_db::DoubleCanMessage::message;

  static MuxType expand(const GraceType & message_from)
  {
    IdaType ida_message;
    (void)IdaGraceTypeTraits::convert_ros_message_to_dds(message_from, ida_message);
    return MuxType::expand_from(ida_message);
  }

  static void flatten(const MuxType & message_from, GraceType & message_to)
  {
    IdaType ida_message;
    message_from.flatten_to(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }

  static void init(GraceType & message_to)
  {
    IdaType ida_message;
    MuxType::init(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }
};

}  // namespace apex::connectors::can::detail

namespace apex::connectors::can::detail
{

template<typename ContainerAllocator, bool UseFlat>
struct MultiplexingTypeTraits<::mock_can_db::ExtendedCanIdMessage_<ContainerAllocator, UseFlat>>
{
  using GraceType = ::mock_can_db::ExtendedCanIdMessage_<ContainerAllocator, UseFlat>;
  using IdaGraceTypeTraits = apex::apex_middleware::DDS_ROS_TypeTraits<GraceType>;

  using IdaType = typename IdaGraceTypeTraits::DDSType;
  using MuxType = apex::mock_can_db::ExtendedCanIdMessage::message;

  static MuxType expand(const GraceType & message_from)
  {
    IdaType ida_message;
    (void)IdaGraceTypeTraits::convert_ros_message_to_dds(message_from, ida_message);
    return MuxType::expand_from(ida_message);
  }

  static void flatten(const MuxType & message_from, GraceType & message_to)
  {
    IdaType ida_message;
    message_from.flatten_to(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }

  static void init(GraceType & message_to)
  {
    IdaType ida_message;
    MuxType::init(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }
};

}  // namespace apex::connectors::can::detail

namespace apex::connectors::can::detail
{

template<typename ContainerAllocator, bool UseFlat>
struct MultiplexingTypeTraits<::mock_can_db::Uint64CanMessage_<ContainerAllocator, UseFlat>>
{
  using GraceType = ::mock_can_db::Uint64CanMessage_<ContainerAllocator, UseFlat>;
  using IdaGraceTypeTraits = apex::apex_middleware::DDS_ROS_TypeTraits<GraceType>;

  using IdaType = typename IdaGraceTypeTraits::DDSType;
  using MuxType = apex::mock_can_db::Uint64CanMessage::message;

  static MuxType expand(const GraceType & message_from)
  {
    IdaType ida_message;
    (void)IdaGraceTypeTraits::convert_ros_message_to_dds(message_from, ida_message);
    return MuxType::expand_from(ida_message);
  }

  static void flatten(const MuxType & message_from, GraceType & message_to)
  {
    IdaType ida_message;
    message_from.flatten_to(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }

  static void init(GraceType & message_to)
  {
    IdaType ida_message;
    MuxType::init(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }
};

}  // namespace apex::connectors::can::detail

namespace apex::connectors::can::detail
{

template<typename ContainerAllocator, bool UseFlat>
struct MultiplexingTypeTraits<::mock_can_db::SignalInitialValuesMessage_<ContainerAllocator, UseFlat>>
{
  using GraceType = ::mock_can_db::SignalInitialValuesMessage_<ContainerAllocator, UseFlat>;
  using IdaGraceTypeTraits = apex::apex_middleware::DDS_ROS_TypeTraits<GraceType>;

  using IdaType = typename IdaGraceTypeTraits::DDSType;
  using MuxType = apex::mock_can_db::SignalInitialValuesMessage::message;

  static MuxType expand(const GraceType & message_from)
  {
    IdaType ida_message;
    (void)IdaGraceTypeTraits::convert_ros_message_to_dds(message_from, ida_message);
    return MuxType::expand_from(ida_message);
  }

  static void flatten(const MuxType & message_from, GraceType & message_to)
  {
    IdaType ida_message;
    message_from.flatten_to(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }

  static void init(GraceType & message_to)
  {
    IdaType ida_message;
    MuxType::init(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }
};

}  // namespace apex::connectors::can::detail

namespace apex::connectors::can::detail
{

template<typename ContainerAllocator, bool UseFlat>
struct MultiplexingTypeTraits<::mock_can_db::SignalInitialValuesMessage_2_<ContainerAllocator, UseFlat>>
{
  using GraceType = ::mock_can_db::SignalInitialValuesMessage_2_<ContainerAllocator, UseFlat>;
  using IdaGraceTypeTraits = apex::apex_middleware::DDS_ROS_TypeTraits<GraceType>;

  using IdaType = typename IdaGraceTypeTraits::DDSType;
  using MuxType = apex::mock_can_db::SignalInitialValuesMessage_2::message;

  static MuxType expand(const GraceType & message_from)
  {
    IdaType ida_message;
    (void)IdaGraceTypeTraits::convert_ros_message_to_dds(message_from, ida_message);
    return MuxType::expand_from(ida_message);
  }

  static void flatten(const MuxType & message_from, GraceType & message_to)
  {
    IdaType ida_message;
    message_from.flatten_to(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }

  static void init(GraceType & message_to)
  {
    IdaType ida_message;
    MuxType::init(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }
};

}  // namespace apex::connectors::can::detail

namespace apex::connectors::can::detail
{

template<typename ContainerAllocator, bool UseFlat>
struct MultiplexingTypeTraits<::mock_can_db::E2EProtectedMessage_<ContainerAllocator, UseFlat>>
{
  using GraceType = ::mock_can_db::E2EProtectedMessage_<ContainerAllocator, UseFlat>;
  using IdaGraceTypeTraits = apex::apex_middleware::DDS_ROS_TypeTraits<GraceType>;

  using IdaType = typename IdaGraceTypeTraits::DDSType;
  using MuxType = apex::mock_can_db::E2EProtectedMessage::message;

  static MuxType expand(const GraceType & message_from)
  {
    IdaType ida_message;
    (void)IdaGraceTypeTraits::convert_ros_message_to_dds(message_from, ida_message);
    return MuxType::expand_from(ida_message);
  }

  static void flatten(const MuxType & message_from, GraceType & message_to)
  {
    IdaType ida_message;
    message_from.flatten_to(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }

  static void init(GraceType & message_to)
  {
    IdaType ida_message;
    MuxType::init(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }
};

}  // namespace apex::connectors::can::detail

namespace apex::connectors::can::detail
{

template<typename ContainerAllocator, bool UseFlat>
struct MultiplexingTypeTraits<::mock_can_db::VeryLongSignalMessage_<ContainerAllocator, UseFlat>>
{
  using GraceType = ::mock_can_db::VeryLongSignalMessage_<ContainerAllocator, UseFlat>;
  using IdaGraceTypeTraits = apex::apex_middleware::DDS_ROS_TypeTraits<GraceType>;

  using IdaType = typename IdaGraceTypeTraits::DDSType;
  using MuxType = apex::mock_can_db::VeryLongSignalMessage::message;

  static MuxType expand(const GraceType & message_from)
  {
    IdaType ida_message;
    (void)IdaGraceTypeTraits::convert_ros_message_to_dds(message_from, ida_message);
    return MuxType::expand_from(ida_message);
  }

  static void flatten(const MuxType & message_from, GraceType & message_to)
  {
    IdaType ida_message;
    message_from.flatten_to(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }

  static void init(GraceType & message_to)
  {
    IdaType ida_message;
    MuxType::init(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }
};

}  // namespace apex::connectors::can::detail

namespace apex::connectors::can::detail
{

template<typename ContainerAllocator, bool UseFlat>
struct MultiplexingTypeTraits<::mock_can_db::PDMessage_<ContainerAllocator, UseFlat>>
{
  using GraceType = ::mock_can_db::PDMessage_<ContainerAllocator, UseFlat>;
  using IdaGraceTypeTraits = apex::apex_middleware::DDS_ROS_TypeTraits<GraceType>;

  using IdaType = typename IdaGraceTypeTraits::DDSType;
  using MuxType = apex::mock_can_db::PDMessage::message;

  static MuxType expand(const GraceType & message_from)
  {
    IdaType ida_message;
    (void)IdaGraceTypeTraits::convert_ros_message_to_dds(message_from, ida_message);
    return MuxType::expand_from(ida_message);
  }

  static void flatten(const MuxType & message_from, GraceType & message_to)
  {
    IdaType ida_message;
    message_from.flatten_to(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }

  static void init(GraceType & message_to)
  {
    IdaType ida_message;
    MuxType::init(ida_message);
    IdaGraceTypeTraits::convert_dds_message_to_ros(ida_message, message_to);
  }
};

}  // namespace apex::connectors::can::detail
#endif  // GRACE_CONNECTORS_CAN_UTILITIES__CAN_HELPERS_E6D10C32_HPP
