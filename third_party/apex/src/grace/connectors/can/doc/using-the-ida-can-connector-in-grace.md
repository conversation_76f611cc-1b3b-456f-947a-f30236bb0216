---
tags:
  - <PERSON><PERSON>
---
# Using the Apex.Ida CAN Connector in an Apex.Grace Application

The following steps are necessary to use the Apex.Ida CAN connector in Apex.Grace.

## Initialize the project

First, create a `BUILD.bazel` file where all needed targets will be declared.

## Add a target with your CAN database definitions

Create the `msgs_library` target as an entry point to handle the IDL files.

Provide one or more DBC files or ARXML ECU Extracts for the CAN message definitions.

{{ code_snippet("grace/demos/demo_grace_can_connector/BUILD.bazel",
  {"tag": "#! [grace_msgs_library]"}, "python") }}

1. Name the target. This name is used in the next step.
1. DBC or ARXML file(s) with the message types intended to be used by the application.

This Bazel target loads the CAN messages from DBC and ARXML files and generates
the libraries needed to handle the message types they define.

## Configure your CAN message to topic mappings

Specify the topic mapping in a
[configuration file](<APEX_MIDDLEWARE_DOCS>/ida/connectors/can/doc/reference/can-gateway-configuration.md).

{{ code_snippet("grace/demos/demo_grace_can_connector/config/ping_mapping.yaml") }}

!!! note
    Prefix the topics with `rt/` when using the Apex.Ida CAN Connector in Apex.Grace

The topic names configured in the CAN message mapping will be used later in the application code.

## Write code to send and receive topics

Add a C++ source file for the application code.

Include the generated type headers.

{{ code_snippet("grace/demos/demo_grace_can_connector/ping.cpp",
  {"tag": "//! [generated_header_includes]"}, "cpp") }}

1. Header file containing generated helper functions including `init()`
   from the `msgs_library` target
2. Header file containing generated types from the `msgs_library` target

!!! note
    The generated include files are named the same as the `.dbc`/`.arxml`
    files passed to the `msgs_library` Bazel target.

Declare C++ types for topics:

{{ code_snippet("grace/demos/demo_grace_can_connector/ping.cpp",
  {"tag": "//! [topic_type_declaration]"}, "cpp") }}

!!! note
    The fully qualified type name consists is in format `<namespace_name>::<can_message_name>`.
    The namespace name is taken from the database name from the `.dbc`/`.arxml` file.

Now, create publishers and subscribers for sending and
receiving data with the Apex.Ida CAN Connector

Instantiate the publishers and subscribers:

{{ code_snippet("grace/demos/demo_grace_can_connector/ping.cpp",
  {"tag": "//! [instantiate_publishers_and_subscribers]"}, "cpp") }}

1. The publisher is instantiated with the C++ type `PingMessage` that we defined earlier
1. The subscriber is instantiated with the C++ type `PongMessage` that we defined earlier

Initialize the publishers and subscribers:

{{ code_snippet("grace/demos/demo_grace_can_connector/ping.cpp",
  {"tag": "//! [initialize_publishers_and_subscribers]"}, "cpp") }}

1. The publisher is initialized with the topic name `ping`,
   not `rt/ping` as in the mapping .yaml file
2. The subscriber is initialized with the topic name `pong`,
   not `rt/pong` as in the mapping .yaml file

Publishing to an Apex.Ida CAN Connector topic:

{{ code_snippet("grace/demos/demo_grace_can_connector/ping.cpp",
  {"tag": "//! [publishing]"}, "cpp") }}

1. Set the signals to the CAN default values defined in the DBC or ARXML file
1. Set individual signals as required
1. Publish the message. It is then received and processed by the Apex.Ida CAN Connector.

!!! note
    Make sure to initialize all signals you send according to expected CAN values.
    The best way to do this is to call the `init()` function as shown in the above example.

Receiving a message from an Apex.Ida CAN Connector topic is just like
receiving a message from any other topic:

{{ code_snippet("grace/demos/demo_grace_can_connector/ping.cpp",
  {"tag": "//! [receiving]"}, "cpp") }}

## Writing an Apex.Grace node to handle the CAN topics

{{ code_snippet("grace/demos/demo_grace_can_connector/ping.cpp", {"tag": "//! [running]"}, "cpp") }}

In this example, we create a service class that inherits from
`apex::executor::apex_node_base`. Then, we instantiate an executor, a timer service,
and finally run the execution.

Every second, the executor calls the `execute_impl` function of the service class.
In this function, the data reception occurs.

This code is fundamentally the same as any other Apex.Grace node with
publishers and subscribers of messages.

## Further Steps

For more detailed information and how to run the demo, refer to the
[Running an application with the Apex.Ida CAN Connector in Apex.Grace](ida-can-connector-in-grace.md)
document.
