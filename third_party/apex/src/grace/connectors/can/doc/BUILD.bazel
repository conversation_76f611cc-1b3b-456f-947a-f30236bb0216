load("@apex//tools/bazel/rules_docs:defs.bzl", "docs_chapter")

docs_chapter(
    name = "doc",
    referenced_srcs = [
        ".meta.yml",
        "//grace/demos/demo_grace_can_connector:doc_files",
    ],
    references = [
        "//grace/demos/demo_grace_can_connector/doc",
        "//grace/demos/demo_grace_can_to_someip/doc",
        "//ida/connectors/can/doc/reference",
    ],
    subchapters = [
        ("Using the Ida CAN Connector", "using-the-ida-can-connector-in-grace.md"),
        ("CAN to SOME/IP with Apex.Ida connectors in an Apex.Grace Application", "//grace/demos/demo_grace_can_to_someip/doc"),
        ("Running an application with the Apex.Ida CAN Connector in Apex.Grace", "//grace/demos/demo_grace_can_connector/doc"),
    ],
)
