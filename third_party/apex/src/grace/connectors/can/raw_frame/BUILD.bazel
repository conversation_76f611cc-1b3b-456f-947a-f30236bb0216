load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "ROSIDL_COMMON_PKGS", "msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

msgs_library(
    name = "apex_can_builtin",
    ida_type_srcs = "//ida/serializers/can/raw_frame:types",
    visibility = ["//visibility:public"],
)

apex_cc_library(
    name = "raw_frame",
    hdrs = ["raw_frame.hpp"],
    visibility = ["//visibility:public"],
    deps = [
        ":apex_can_builtin",
    ],
)

# need ros_pkg for typesupport and ament_index
ros_pkg(
    name = "raw_frame_pkg",
    cc_libraries = [":raw_frame"],
    description = "CAN Connector raw frame",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI, Inc.",
    msg_libraries = [
        ":apex_can_builtin",
    ],
    version = "0.1.0",
    visibility = ["//visibility:public"],
    deps = ROSIDL_COMMON_PKGS,
)
