<!-- markdownlint-disable -->
# V&V Mapping Configuration

The V&V mapping configuration specifies the relationship between various kinds of messages
and their intended destination topics. It is configured in a simple YAML file, which is provided
as a source file into a `vnv_gateway` Bazel target.

CAN, FlexRay, PDU, and SOME/IP mappings each go into their own section, which are all optional.
The overall structure is as follows:
```yaml
can:
  [list of CAN database & message mappings]

flexray:
  [list of FlexRay database & message mappings]

pdu:
  [list of PDU database & message mappings]

someip:
  [list of SOME/IP service & event mappings]

settings:
  domain_id: 0  # Configures the Apex.OS domain id used for the mappings. Optional, defaults to 0.
  tecmp: true  # Configures whether TECMP or raw ethernet frames are received. Optional, defaults to true.
  interface: eth0  # Configures the network interface to listen on.
```

## TECMP sources

For TECMP sources, a numeric channel id needs to be assigned to each CAN/FlexRay/PDU database
or SOME/IP service mapping.

## Mapping CAN messages

CAN messages are grouped into databases (e.g. DBC database file, ARXML cluster). Each message to
be mapped needs to be associated with a topic. It is valid to only map a subset of available messages.

Note that the topic type is automatically inferred from the existing interface definitions.

Example:
```yaml
can:
  - database: "test_db"
    tecmp_channel_id: 1  # only needed for TECMP.
    messages_to_receive:
      - name: "msg_1"
        topic: "topic_1"
      - name: "msg_2"
        topic: "topic_2"
  - database: "test_db"  # Databases can be referenced multiple times (e.g. for different channel ids)
    tecmp_channel_id: 2
    messages_to_receive:
      - name: "msg_3"
        topic: "topic_3"

settings:
  interface: dummy0
```

## Mapping FlexRay messages

FlexRay messages are also grouped into databases. Each message to be mapped needs to be
associated with a topic. It is valid to only map a subset of available messages.

Note that the topic type is automatically inferred from the existing interface definitions.

Example:
```yaml
flexray:
  - name: "system_4_2_fr"
    tecmp_channel_id: 1
    messages_to_receive:
      - message: "Message1"
        topic: "test_message"

settings:
  interface: dummy0
```

## Mapping PDU messages

PDU messages are also grouped into databases. Each message to be mapped needs to be
associated with a topic. It is valid to only map a subset of available messages.

Note that the topic type is automatically inferred from the existing interface definitions.

Example:
```yaml
pdu:
  - name: "system_4_2_pdu"
    tecmp_channel_id: 43
    messages_to_receive:
      - message: "MyPDUMessage1"
        topic: "my_pdu_message"

settings:
  interface: dummy0
```

## Mapping SOME/IP events

SOME/IP events are grouped into services. Since there can be multiple instances of a service,
the mapping needs to specify a service instance (as defined in the interface definition, e.g. ARXML).
Each event needs to be associated with a topic. However, it is valid to only map a subset of available events.

Note that the topic type is automatically inferred from the existing interface definitions.

Example:
```yaml
someip:
  - service: "ping_service"
    tecmp_channel_id: 1
    events_to_receive:
      - name: "pong"
        topic: "pong"

settings:
  interface: dummy0
```

## Miscellaneous

!!!note
    When used with Apex.Grace, topics in Apex.Ida uses specific prefixes which must be manually prepended
    in the mapping configuration. Use the `rt/` prefix for all topics that are used in Apex.Grace.
    `rt` stands for *ROS topic*. Topics that do not have this prefixed are not shown in Apex.Grace
    tooling, and cannot be subscribed to by Apex.Grace nodes.