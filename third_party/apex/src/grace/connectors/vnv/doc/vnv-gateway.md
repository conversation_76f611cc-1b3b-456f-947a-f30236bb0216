# V&V Gateway

## Overview

The verification and validation (V&V) gateway is a special connector gateway used
to ingest data captured directly from the network or other vehicle buses into Apex.OS.
This can be useful to verify and validate system communication, either live
or for creating topic recordings (using e.g. [Rosbag2](recording-and-replaying-with-rosbag.md))
which can later be used for other testing purposes.

## Supported features

The gateway receives raw ethernet frames from the network and can decode the following protocols:

- [TECMP](https://github.com/Technica-Engineering/libtecmp/blob/master/docs/TECMP_User_Manual_V1.7.pdf)
    - Nested ethernet frames
    - CAN and CAN/FD
    - FlexRay
- AUTOSAR SocketAdapter PDUs
- SOME/IP

!!! note
    The V&V gateway can only receive data, it cannot send data to the network or vehicle buses.
    It also does not participate actively in stateful protocols like SOME/IP (although it does
    understand SOME/IP service discovery), making it possible to tap into and capture communication
    of live systems without changing them.

## Requirements

In order to be able to correctly decode incoming data, the V&V gateway requires model knowledge about
types and entities in the network. This information is taken from interface descriptions like
AUTOSAR XML (classic and adaptive) or DBC files. Users only need to supplement mapping information,
define destination topics and channel IDs if TECMP is used.

!!!note
      Since ethernet frames are received from a raw socket, the V&V gateway requires elevated
      privileges in order to run (either root or `cap_net_raw+eip` capabilities on Linux)

!!!note
      If data needs to be received from multiple network interfaces, more than one V&V gateway
      must be used in parallel. Each gateway instance can only be configured to listen on a single
      network interface.

!!!note
      If multiple versions of types (or conflicting deployments thereof) need to be received, more
      than one V&V gateway must be used in parallel. Each gateway instance can only map
      non-conflicting types.

## Usage

On a high level, the following steps are necessary to configure the gateway:

1. Import ARXML or DBC interface definitions using [msgs_library](grace_Srosidl_Srules_Urosidl_Cdefs.md#msgs_library)
1. Specify the topic mapping in a [configuration file](vnv-gateway-configuration.md)
1. Create a corresponding `vnv_gateway` Bazel target, pulling in all referenced interface
   definitions through dependencies to `msgs_library` Bazel targets
1. Add the `vnv_gateway` target to the list of applications to launch on the target

At runtime, Apex.OS applications can then simply subscribe to the mapped topics to receive data.
