load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex_config//:defs.bzl", "CONFIG")

apex_cc_test(
    name = "test_streaming_connector",
    srcs = glob(["test/*.cpp"]),
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "no-remote",  # TODO(#28272): fails to load zlib.so on remote, fix in follow up
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
        "weekly",
    ],
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    deps = [
        "//grace/connectors/streaming_connector:streamer",
        "//grace/tools/apex_integration_test_node",
        "@googletest//:gtest",
        "@opencv",
    ],
) if CONFIG.APEX_ENABLE_OPENCV else None
