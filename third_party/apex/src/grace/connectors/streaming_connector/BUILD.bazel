load("@apex_config//:defs.bzl", "CONFIG")

cc_library(
    name = "streamer",
    srcs = [
        "src/streaming_connector_subscriber_node.cpp",
    ],
    hdrs = glob([
        "include/streaming_connector/*.hpp",
    ]),
    # TODO(kyle.marcey): #24903 Remove line below
    copts = ["-Wno-error"],
    strip_include_prefix = "include",
    tags = [
        "exclude_sca",
        "weekly",
    ],
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//common/configuration/settings",
        "//common/interrupt",
        "//common/threading",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/interfaces/sensor_msgs",
        "//grace/ros/rclcpp/rclcpp",
        "@gstreamer",
        "@opencv",
    ],
) if CONFIG.APEX_ENABLE_OPENCV else None

cc_binary(
    name = "streaming_connector_exe",
    srcs = [
        "src/streaming_connector_main.cpp",
    ],
    tags = [
        "exclude_sca",
        "weekly",
    ],
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    deps = [
        "streamer",
    ],
) if CONFIG.APEX_ENABLE_OPENCV else None

filegroup(
    name = "doc_files",
    srcs = ["include/streaming_connector/streaming_connector_subscriber_node_settings.hpp"],
    visibility = ["//grace/connectors/streaming_connector/doc:__subpackages__"],
)
