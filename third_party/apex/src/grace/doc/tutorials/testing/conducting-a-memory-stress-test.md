# Conducting memory stress tests

## Introduction

This article introduces several ways to test nodes of Apex.Grace when the system
resources are under stress. Apex.Grace strives to be memory-leak free, but other
applications in the system might not have such a guarantee. Therefore, it is of
great importance to test the behavior of nodes when memory is stressed to
extremes.

Desired behavior:

- The physical memory that the node is using should always remain in memory and
  not be swapped to disk
- The performance of a node should not be affected

In Apex.Grace, the memory used by a node should be allocated in the initialization stage
and locked, preventing the memory from being swapped out.

## Memory stress test tools

Apex.<PERSON> comes with a memory stress tool named `mem_hogger`.

```shell ade
ros2 run mem_tests mem_hogger -h
Usage
mem_hogger [OPTIONS]
        [-p <1>] => num_pages per alloc
        [-f <15000>] => min num of avail pages
        [-h] => This help
```

## Memory stress test systematic example

This section shows a test of `apex_ecu_monitor` under memory
stressed conditions.

The `apex_ecu_monitor` package
creates processes which pre-allocate all the required memory during the
initialization phase. All the allocated memory is then locked
into the physical RAM while heap movement and new `mmap` calls are disabled.

Apex.Grace applications feature the `proc_max_mem` using Apex.Grace real-time
settings. See the tutorial on [running an application with real-time settings](from-prototype-to-production-grade-applications.md#tune-settings-for-performance-improvement)
for more details. This option can be used to define the upper memory
bound for the process, allowing to lock additional memory than it was
requested in the initialization phase. This prevents memory from being swapped
out when the process still dynamically allocates memory in the runtime phase.
As a best practice, the `proc_max_mem` option should be set to the maximum
expected memory consumption of the process.

### Test steps

The following example applies to the target aarch64 machine, such as the Renesas
R-Car H3 provisioned with the [the real-time preempt
patch](provisioning-renesas-h3-with-linux-rt.md).
This example assumes that a [distributed
system](configuring-a-distributed-system.md) is set up, such that data can be
monitored on an x86_64 machine while the tested application runs
on the target aarch64 machine.
If `apex_ecu_monitor` example is not working,
[`apex_ecu_monitor`design doc](apex-ecu-monitor-design.md) can be referred
to in order to resolve the issue.

1. In ADE terminal 1, start the `apex_ecu_monitor` on the target:

    ```shell ade
    sudo -s  # switch to root in order to lock memory
    export ROS_DOMAIN_ID=<your ros domain id>
    ros2 run launcher apex_launch \
             --apex-settings-file \
             /opt/ApexGraceBilbo/share/apex_ecu_monitor/launch/apex_ecu_monitor_aarch64.launch.yaml
    ```

    1. After the initialization phase of the `apex_ecu_monitor` process,
    all the required memory is allocated and locked into the physical memory

    !!! note
        The correct network interface should be
        set while using `apex_ecu_monitor` in
        `/opt/ApexGraceBilbo/share/apex_ecu_monitor/param/apex_ecu_monitor_default_settings_aarch64.yaml`.
        By default, the interfaces `lo` and `eth0` are set.

2. In ADE terminal 2, read the published topic `/raw_ecu_info` on the x86_64 machine:

    ```shell ade
    export ROS_DOMAIN_ID=<your ros domain id>
    ros2 topic echo /raw_ecu_info
    ```

3. In ADE terminal 3, start the memory test tool to stress the system on the target:

    ```shell ade
    (ade)$ ros2 run mem_tests mem_hogger -p 4 -f 25000
    Num of pages per alloc: 4
    Min num of free avail pages: 25000
    Start allocation
    sbrk: 0xaaaac5e7a000
    sbrk: 0xaaaac5e7a000
    ...
    ```

    !!! note
        It might take around 30s for the `mem_hogger` test to start.

## Expected behavior

It is expected that the performance of `apex_ecu_monitor` is not affected
by the memory stress condition. We can verify this by monitoring the period
of `raw_ecu_info` and checking the are no meaningful variations.

```shell ade
(ade)$ ros2 topic hz /raw_ecu_info
average rate: 9.915
 min: 0.099s max: 6.276s std dev: 0.07084s window: 7600
average rate: 9.915
 min: 0.099s max: 6.276s std dev: 0.07079s window: 7610
average rate: 9.915
 min: 0.099s max: 6.276s std dev: 0.07074s window: 7620
average rate: 9.915
```
