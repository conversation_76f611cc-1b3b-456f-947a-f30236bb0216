---
tags:
  - Colcon
---
# Running tests, static code analysis, and linters

This article is a generic article across all of Apex.AI's product suite.  Example
code blocks are provided for each product.

## Run the tests, the static code analysis and the linters for a specific package

To run all the tests for a specific package, first ensure the correct `colcon build`
command has been run at least once in order to build the desired package.

The build is usually performed in the `apex_ws` directory.

In this example, a fictional `my_cool_pkg` in the `apex_ws` workspace will be built
and tested.

<!-- markdownlint-disable MD046 -->

=== "Apex.Grace"

    ```shell ade
    {{
        include_external("grace/integration_tests/os_verify_docs/os_verify_docs/console_snippets/running_tests_static_code_analysis_and_linters/build_sourcing_os/snippet.jinja")
            |indent(width=4)
    }}
    ```

=== "Apex.Ida"

    ```shell ade
    {{
        include_external("grace/integration_tests/os_verify_docs/os_verify_docs/console_snippets/running_tests_static_code_analysis_and_linters/build_sourcing_middleware/snippet.jinja")
            |indent(width=4)
    }}
    ```

=== "Apex.Tools"

    ```shell ade
    {{
        include_external("grace/integration_tests/os_verify_docs/os_verify_docs/console_snippets/running_tests_static_code_analysis_and_linters/build_sourcing_tools/snippet.jinja")
            |indent(width=4)
    }}
    ```

<!-- markdownlint-restore -->

!!! warning
    If the `ROS_DOMAIN_ID` environment variable is set, there might be crosstalk between some
    tests. Unset this environment variable when testing, or run tests sequentially.

Executed tests can be separated into two categories:

- Behavioral tests (unit tests, integration tests)
- Linters and static code analysis (uncrustify, cppcheck, …)

All of the `ament_*` tools listed below are made available in the `/opt/ApexToolsBilbo` volume
or in the `ApexTools-src` repository. They are wrappers around the respective linters that,
among other things, generate XML reports that `colcon test` then consumes.

To see which tests are run and how to run them individually, see the following table:

<!-- markdownlint-disable MD033 -->

| <div style="width:150px">Tool</div> | CLion usage | <div style="width:450px">CLI usage</div> | Note |
|:----:|:-----------:|:---------:|:----:|
| Run all the tests defined in a specific package | Select the `package.xml` of your package or its corresponding `CMakeLists.txt` and run `External Tools->Test Current`. | `colcon test --merge-install --packages-select <my_pkg>`| Remove `--merge-install` if the package is built without `merge-install`.|
| [cppcheck](https://github.com/danmar/cppcheck) | Not integrated | `$ ament_cppcheck <filename.cpp>` | Used for internally written Apex.Grace and Apex.Grace reference implmentation applications. Check a few of [MISRA C 2012 rules](https://cppcheck.sourceforge.io/misra.php)|
| [CPP Lint](https://github.com/google/styleguide/tree/gh-pages/cpplint) | Select a `c/h` or `cpp/hpp` file to check and run `External Tools->CPP Lint Current`. | `$ ament_cpplint <filename.cpp>`  | Check the style according to the (slightly modified) Google CPP guideline in `apex_ws/src/tools/ament/ament_lint/ament_cpplint/ament_cpplint/cpplint.py`|
| [Uncrustify](https://github.com/uncrustify/uncrustify)| See the note on building Uncrustify below. Select a `c/h` or `cpp/hpp` file to reformat and run `External Tools->Uncrustify Current`. CLion will automatically reformat the file according to the style guide. | `$ ament_uncrustify <filename.cpp> --reformat` | Allow Apex.Grace to follow (slightly modified) OSRF C and C++ style guides (`apex_ws/src/tools/ament/ament_lint/ament_uncrustify/ament_uncrustify/configuration/ament_code_style.cfg`) which results in uniform, easy to read code.|
| Copyright lint | Select a `c/h` or `cpp/hpp` file to check and run `External Tools->Copyright` | `$ ament_copyright <filename>` | - |
| CMake lint | Select a `CMakeLists.txt` file to check and run `External Tools->Lint CMake` | `$ ament_lint_cmake <CMakeLists.txt>` | - |
| [gtest](https://github.com/google/googletest) | Build the package, go to the unit test source file and click on the run button next to `TEST_*` | Run the test executable generated in the build folder | - |
| [ros_testing](https://github.com/ros2/ros_testing) | Not integrated | `ros2 test test/<test-filename.py>` | - |

!!! warning
    Uncrustify is not included in the Apex.Grace release because of the GPLv2 license it uses.
    To enable Uncrustify, either install it via `sudo apt update && sudo apt install uncrustify`
    or build the Apex.Tools `uncrustify_vendor` package.

<!-- markdownlint-restore -->

## Print the test results

The test command output contains a brief report of all the test results.

To get job-wise information of all executed tests, call:

```shell ade
{{
    include_external(
        "grace/integration_tests/os_verify_docs/os_verify_docs/console_snippets/running_tests_static_code_analysis_and_linters/test_result/snippet.jinja")
}}
{{
    include_external("grace/integration_tests/os_verify_docs/os_verify_docs/console_snippets/running_tests_static_code_analysis_and_linters/test_result/stdout.jinja")
}}
```

To see the full logs and failure messages, run `colcon test-result --all --verbose | less`, or
look in the `apex_ws/log/test_<date>/<package_name>` directory for all the
raw test commands, `std_out`, and `std_err`. There's also the
`apex_ws/log/latest_*/` directory containing symbolic links to the most
recent package-level build and test output.

To print the tests' details while the tests are being run, use the
`--event-handlers console_cohesion+` option to print the details directly to the
console:

```shell ade
{{
    include_external("grace/integration_tests/os_verify_docs/os_verify_docs/console_snippets/running_tests_static_code_analysis_and_linters/test_console_output/snippet.jinja")
}}
{{
    include_external("grace/integration_tests/os_verify_docs/os_verify_docs/console_snippets/running_tests_static_code_analysis_and_linters/test_console_output/stdout.jinja")
}}
```

## Running tests in a high-frequency loop

For running the same test in rapid succession, for instance for reproducing a
flaky test, it is recommended to execute it in a `bash` loop:

```shell ade
while true; do; launch_test src/package/test/integration_test.py || break; done
```

This is faster than using the `colcon test --retest-until-fail` command.

When executing a gtest, consider using the [`--gtest_repeat` flag](
https://github.com/google/googletest/blob/main/docs/advanced.md#repeating-the-tests).

## Resource limits

By default, `rmw_apex_middleware` is configured with large resource limits so many
different applications and tests with varied requirements can run
out-of-the-box. However, having large resource limits causes additional memory
and resource consumption; to have a minimal footprint the memory and CPU
resource limits must be tuned. Depending on the frequency in which tests are
run, the resource limits may need to be tuned to avoid tests from crashing due
to resource constraints.
