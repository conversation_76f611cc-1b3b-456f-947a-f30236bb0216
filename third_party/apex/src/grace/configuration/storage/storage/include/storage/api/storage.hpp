/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#ifndef STORAGE__API__STORAGE_HPP_
#define STORAGE__API__STORAGE_HPP_

#include <storage/visibility.hpp>
#include <storage/api/watch_callable.hpp>
#include <storage/common/names.hpp>
#include <storage/common/commands.hpp>
#include <storage/common/traits.hpp>
#include <storage/common/request_id.hpp>
#include <storage/common/time.hpp>
#include <storage/common/qos.hpp>

#include <rclcpp/rclcpp.hpp>
#include <rclcpp/polling_client.hpp>
#include <rclcpp/dynamic_waitset/waitset.hpp>
#include <rclcpp/dynamic_waitset/guard_condition.hpp>
#include <storage_msgs/srv/control.hpp>
#include <cpputils/safe_cast.hpp>
#include <cpputils/optional.hpp>
#include <apexcpp/apexcpp.hpp>
#include <threading/thread.hpp>
#include <logging/logging.hpp>

#include "grace/rmw_ida/typesupport/traits.hpp"

#include <cstdint>
#include <unordered_map>
#include <type_traits>
#include <memory>
#include <iterator>
#include <cassert>
#include <chrono>
#include <utility>
#include <string>
#include <vector>
#include <map>
#include <limits>

namespace apex
{
namespace storage
{

class database;
class communication;

/// \class store
/// The front-end to a data store in the storage service
/// \tparam T The type of the message to store
/// \cert
template<class T>
class store final
{
public:
  friend class database;

  using shared_ptr = std::shared_ptr<store>;

  store(const store &) = delete;
  store & operator=(const store &) = delete;

  /// \brief Sends a request to save data in the storage service
  /// The message cannot be considered really stored until `commit` is called
  /// \param msg The message to save
  /// \cert
  /// \deterministic
  inline void save(T & msg) const;

  /// \brief Sends a request to save data in the storage service
  /// The message cannot be considered really stored until `commit` is called
  /// \param msg The message to save
  /// \param history_length How many old records to store;
  /// Overrides the history_length property of this store
  /// \cert
  /// \deterministic
  inline void save(T & msg, std::uint32_t history_length) const;

  /// \brief Sends a request to store transient data in the storage service until next restart
  /// The message will never be committed to disk
  /// \param msg The message to save as transient
  /// \cert
  /// \deterministic
  inline void save_transient(T & msg) const;

  /// \brief Sends a request to store transient data in the storage service until next restart
  /// The message will never be committed to disk
  /// \param msg The message to save as transient
  /// \param history_length How many old records to store;
  /// Overrides the history_length property of this store
  /// \cert
  /// \deterministic
  inline void save_transient(T & msg, std::uint32_t history_length) const;

  /// \brief Sends a request to fetch a message from the storage service
  /// \param result The fetched message
  /// \return Whether the operation was successful
  /// \cert
  /// \deterministic
  inline bool fetch(T & result) const;

  /// \brief Sends a request to fetch history from the storage service
  /// \param begin An iterator to the place for the first message to fetch into
  /// \param end An iterator to one-past-last place for the message to fetch
  /// \param index At which index to start fetching (0 == latest)
  /// \return The end iterator which can be less then the requested
  /// \cert
  /// \deterministic
  template<class I>
  I fetch_history(I begin, I end, std::uint32_t index = 0) const;

  /// \brief Sends a request to drop this data store
  /// \cert
  /// \deterministic
  inline void drop() const;

  /// \brief Adds a watch for a store
  /// The callable will be called every time there is a notification about a change in the store
  /// value. The const reference to the updating message is the argument of the callable
  /// \param f The callable to call with the message as the argument if store value changes
  /// \return A unique id of the watch to use in remove_watch (cannot be 0)
  /// \cert
  template<class F,
    std::enable_if_t<callable_traits<F>::Arity == 1> * = nullptr,
    class U = std::decay_t<callable_arg_t<F, 0>>,
    std::enable_if_t<std::is_convertible<const T, U>::value> * = nullptr
  >
  std::size_t add_watch(F && f) const;

  /// \brief Removes watch (if exists)
  /// \param watch_id A unique id of the watch
  void remove_watch(std::size_t watch_id) const noexcept;

private:
  /// \brief Creates a data store front-end
  /// \param db The associate database
  /// \param instance_id The instance id of the store
  /// \param history_length How many old records to store
  /// \cert
  /// \deterministic
  store(
    std::shared_ptr<database> db,
    std::uint32_t instance_id,
    std::uint32_t history_length) noexcept
  : m_database{std::move(db)},
    m_instance_id{instance_id},
    m_history_length{history_length}
  {}

  const std::shared_ptr<database> m_database;
  const std::uint32_t m_instance_id;
  const std::uint32_t m_history_length;
};

/// \class database
/// The front-end to a database in the storage service
class database final : public std::enable_shared_from_this<database>
{
public:
  friend class communication;

  template<typename T>
  friend class store;

  using shared_ptr = std::shared_ptr<database>;

  database(const database &) = delete;
  database & operator=(const database &) = delete;

  /// \brief Creates a store front-end associated with this database
  /// \tparam T The type of the messages to store
  /// \param instance_id The id of the data store instance
  /// \param history_length How many old records to store
  /// \return A new store front-end associated with this database
  /// \cert
  template<class T, common::enable_if_compatible_message_type_t<T> = nullptr>
  typename store<T>::shared_ptr
  get_store(std::uint32_t instance_id = 0U, std::uint32_t history_length = 0U);

  /// \brief Explicitly asks the storage service to commit all changes to the disk
  /// \cert
  /// \deterministic
  inline void commit() const;

  /// \brief Sends a request to drop this database entirely
  /// \cert
  /// \deterministic
  inline void drop() const;

private:
  /// \brief Creates a database front-end
  /// \param com Service communication object
  /// \param db_name The name of the database
  /// \param timeout Operation timeout
  /// \cert
  /// \deterministic
  database(
    std::shared_ptr<communication> com,
    std::string db_name,
    std::chrono::nanoseconds timeout) noexcept
  : m_com{std::move(com)},
    m_db_name{std::move(db_name)},
    m_timeout{timeout}
  {}

  const std::shared_ptr<communication> m_com;
  const std::string m_db_name;
  const std::chrono::nanoseconds m_timeout;
};

/// \class communication
/// Message storage service communication facility
class STORAGE_PUBLIC communication final : public std::enable_shared_from_this<communication>
{
public:
  friend class database;

  template<typename T>
  friend class store;

  using shared_ptr = std::shared_ptr<communication>;

  /// \brief Creates an object of communication
  /// \param node A node to use for creation of communication objects
  /// \param instance_name The name of the instance of the service
  /// \param qos The QoS settings for control service and data subscriptions
  /// \param wait_for_service_timeout How long to wait for service
  /// after client's creation
  /// \param thread_attr Attributes for the watching thread
  /// \return A communication object
  /// \cert
  static shared_ptr create(
    rclcpp::Node::SharedPtr node,
    std::string instance_name = "",
    common::qos qos = common::qos{},
    std::chrono::nanoseconds wait_for_service_timeout = std::chrono::seconds{1},
    apex::threading::thread_attributes thread_attr = apex::threading::thread_attributes::build())
  {
    /*
     AXIVION Next Codeline MisraC++2023-21.6.2, MisraC++2023-23.11.1: Reason: Code Quality
     (Functional suitability), Justification: make_shared() cannot
     work with a private constructor
     */
    return shared_ptr{new communication{std::move(node),
        std::move(instance_name),
        std::move(qos),
        wait_for_service_timeout,
        std::move(thread_attr)}};
  }

private:
  /// \class global_com_context
  /// A helper class for communication singleton which makes sure
  /// rclcpp objects exist only between rclcpp::init() and rclcpp::shutdown()
  /// \cert
  class global_com_context
  {
public:
    /// \brief Creates a node on demand
    /// \cert
    void init()
    {
      std::unique_lock<std::mutex> lock{m_mutex};
      if (m_node == nullptr) {
        m_node = std::make_shared<rclcpp::Node>("apex_storage_com_node");
      }
    }

    /// \brief Creates or gets an object of communication from a static storage
    /// \param instance_name The name of the instance of the service
    /// \param qos The QoS settings for control service and data subscriptions
    /// \param wait_for_service_timeout How long to wait for
    /// service after client's creation
    /// \param thread_attr Attributes for the watching thread
    /// \return A communication object
    /// \cert
    shared_ptr get(
      std::string instance_name,
      common::qos qos,
      std::chrono::nanoseconds wait_for_service_timeout,
      apex::threading::thread_attributes thread_attr)
    {
      std::unique_lock<std::mutex> lock{m_mutex};
      const auto iter = m_instances.find(instance_name);
      if (iter != m_instances.end()) {
        return iter->second;
      }

      auto com = create(m_node,
          instance_name,
          std::move(qos),
          wait_for_service_timeout,
          std::move(thread_attr));

      (void) m_instances.emplace(std::move(instance_name), com);
      return com;
    }

private:
    rclcpp::Node::SharedPtr m_node;
    std::unordered_map<std::string, shared_ptr> m_instances;
    mutable std::mutex m_mutex;
  };

public:
  /// \brief Creates or gets an object of communication from a static storage
  /// \param instance_name The name of the instance of the service
  /// \param qos The QoS settings for control service and data subscriptions
  /// \param wait_for_service_timeout How long to wait for
  /// service after client's creation
  /// \param thread_attr Attributes for the watching thread
  /// \return A communication object
  /// \cert
  static shared_ptr get(
    std::string instance_name = "",
    common::qos qos = common::qos{},
    std::chrono::nanoseconds wait_for_service_timeout = std::chrono::seconds{1},
    apex::threading::thread_attributes thread_attr = apex::threading::thread_attributes::build());

  /// \brief Produces a database front-end object
  /// \param db_name The name of the database
  /// \param timeout Operation timeout
  /// \return A database object
  /// \cert
  database::shared_ptr get_database(
    std::string db_name,
    std::chrono::nanoseconds timeout = std::chrono::seconds{1})
  {
    /*
     AXIVION Next Codeline MisraC++2023-21.6.2, MisraC++2023-23.11.1: Reason: Code Quality
     (Functional suitability), Justification: make_shared() cannot
     work with a private constructor
     */
    return database::shared_ptr{new database{shared_from_this(), std::move(db_name), timeout}};
  }

  /// \brief Starts the watcher thread which monitors any notifications for store value change
  /// and calls the appropriate callables
  /// Does nothing if there are no watches
  /// Can be called only once if the lifetime of the object if there are watches
  /// \cert
  /// \deterministic
  void start_watching() const
  {
    std::unique_lock<std::mutex> l{m_mutex};
    if (m_worker_thread != apex::nullopt) {
      m_worker_thread->issue();
    }
  }

  /// \brief Stops the watcher thread
  /// Does nothing if there are no watches
  /// \cert
  /// \deterministic
  void stop_watching() const
  {
    std::unique_lock<std::mutex> l{m_mutex};
    // m_worker_thread may not always not be apex::nullopt, and short-circuiting is OK here
    if (m_worker_thread != apex::nullopt && m_worker_thread->joinable()) {
      m_stop->set_value(true);
      m_worker_thread->join();
    }
  }

  /// \brief Stops the watcher thread ignoring any errors
  /// \cert
  /// \deterministic
  ~communication()
  {
    try {
      stop_watching();
    } catch (...) {}
  }

private:
  /// \brief Creates an object of communication
  /// \param node A node to use for creation of communication objects
  /// \param instance_name The name of the instance of the service
  /// \param qos The QoS settings for control service and data subscriptions
  /// \param wait_for_service_timeout How long wait for service after client's creation
  /// \param thread_attr Attributes for the watching thread
  /// \cert
  communication(
    rclcpp::Node::SharedPtr node,
    std::string instance_name,
    common::qos qos,
    std::chrono::nanoseconds wait_for_service_timeout,
    apex::threading::thread_attributes thread_attr)
  : m_instance_name{std::move(instance_name)},
    m_node{std::move(node)},
    m_logger{m_node.get(), "apex_storage_api_logger_" + m_instance_name},
    m_qos{std::move(qos)},
    m_thread_attr{std::move(thread_attr)}
  {
    m_control_srv = m_node->create_polling_client<storage_msgs::srv::Control>(
      common::get_service_name(m_instance_name), m_qos.service);
    try {
      m_control_srv->wait_for_service(wait_for_service_timeout);
    } catch (const std::exception &) {
      throw apex::runtime_error{"storage service is not available"};
    }
    (void) m_service_ws.add(m_control_srv);
  }

  communication(const communication &) = delete;
  communication & operator=(const communication &) = delete;

  /// \brief Sends request to register a type in the storage service (on demand)
  /// \tparam T The type of the message to be stored
  /// \param db_name The name of the database
  /// \param instance_id The instance_id of the data store
  /// \param history_length How many old records to store
  /// \param timeout The timeout for this operation
  /// \cert
  template<class T>
  void registration(
    const std::string & db_name,
    std::uint32_t instance_id,
    std::uint32_t history_length,
    std::chrono::nanoseconds timeout)
  {
    const auto clock_start = std::chrono::steady_clock::now();
    std::unique_lock<std::mutex> l{m_mutex};

    (void) send_control_request<T>(common::commands::Registration,
      0U,
      0U,
      history_length,
      db_name,
      instance_id);
    (void) get_control_response(clock_start, timeout);

    const auto key = type_id<T>();
    const auto iter = m_data_publishers.find(key);
    if (iter == m_data_publishers.end()) {
      assert(m_data_subscribers.find(key) == m_data_subscribers.end());
      const auto msg_type_name = type_name<T>();
      const auto pub_topic = common::get_data_save_topic_name(msg_type_name, m_instance_name);
      const auto sub_topic = common::get_data_fetch_topic_name(msg_type_name, m_instance_name);

      auto qos = m_qos.publisher;
      const auto & pub = m_data_publishers.emplace(key,
          m_node->create_publisher<T>(pub_topic, qos.transient_local())).first->second;
      pub->wait_for_matched(1U, common::adjust_and_test_timeout(clock_start, timeout));

      qos = m_qos.subscriber;
      const auto & sub = m_data_subscribers.emplace(key,
          std::make_unique<sub_data>(
            m_node->create_polling_subscription<T>(sub_topic, qos.transient_local())
          )
        ).first->second->sub;
      sub->wait_for_matched(1U, common::adjust_and_test_timeout(clock_start, timeout));
    }
  }

  /// \brief Sends request to save data in the storage service
  /// The message cannot be considered really stored until `commit` is called
  /// \param db_name The name of the database
  /// \param instance_id The id of the data store instance
  /// \param history_length How many old records to store
  /// \param begin An iterator to the first message to save
  /// \param end An iterator to one-past-last message to save
  /// \param timeout The timeout for this operation
  /// \cert
  /// \deterministic
  template<class T, class I>
  void save(
    const std::string & db_name,
    std::uint32_t instance_id,
    std::uint32_t history_length,
    I begin,
    I end,
    std::chrono::nanoseconds timeout)
  {
    static_assert(std::is_base_of<std::input_iterator_tag,
      typename std::iterator_traits<I>::iterator_category>::value,
      "bad iterator type");

    if (begin > end) {
      throw apex::invalid_argument{"bad message iterator"};
    }

    const auto clock_start = std::chrono::steady_clock::now();
    std::unique_lock<std::mutex> l{m_mutex};

    const auto key = type_id<T>();
    const auto iter = m_data_publishers.find(key);
    assert(iter != m_data_publishers.end());
    auto & pub = static_cast<rclcpp::Publisher<T> &>(*iter->second);

    const auto item_count = apex::cast::safe_cast<std::uint32_t>(std::distance(begin, end));
    const auto request_id = send_control_request<T>(
      common::commands::Save,
      0U,
      item_count,
      history_length,
      db_name,
      instance_id);

    while (begin != end) {
      common::set_request_id(request_id, *begin);
      begin->header.creation_time_stamp = apex::to_msg_time(apex::system_clock::now());
      begin->header.db_name = db_name;
      begin->header.instance_id = instance_id;
      auto msg = pub.borrow_loaned_message();
      msg.get() = *begin;
      pub.publish(std::move(msg));
      common::set_zero_request_id(*begin);
      ++begin;
    }

    const auto acknowledged_count = get_control_response(clock_start, timeout);
    assert(acknowledged_count == item_count);
    (void) acknowledged_count;
  }

  /// \brief Sends request to save data in the storage service
  /// The message cannot be considered really stored until `commit` is called
  /// \param db_name The name of the database
  /// \param instance_id The id of the data store instance
  /// \param history_length How many old records to store
  /// \param msg The message to save
  /// \param timeout The timeout for this operation
  /// \cert
  /// \deterministic
  template<class T>
  void save(
    const std::string & db_name,
    std::uint32_t instance_id,
    std::uint32_t history_length,
    T & msg,
    std::chrono::nanoseconds timeout)
  {
    save<T>(db_name, instance_id, history_length, &msg, (&msg + 1), timeout);
  }

  /// \brief Sends request to fetch messages from the storage service
  /// \param db_name The name of the database
  /// \param instance_id The id of the data store instance
  /// \param history_length How many old records to store
  /// \param begin An iterator to the place for the first message to fetch into
  /// \param end An iterator to one-past-last place for the message to fetch
  /// \param index At which index to start fetching (0 == latest)
  /// \param timeout The timeout for this operation
  /// \return The end iterator which can be less then the requested
  /// \cert
  /// \deterministic
  template<class T, class I>
  I fetch(
    const std::string & db_name,
    std::uint32_t instance_id,
    std::uint32_t history_length,
    I begin,
    I end,
    std::uint32_t index,
    std::chrono::nanoseconds timeout)
  {
    static_assert(std::is_base_of<std::forward_iterator_tag,
      typename std::iterator_traits<I>::iterator_category>::value,
      "bad iterator type");

    if (begin > end) {
      throw apex::invalid_argument{"bad message iterator"};
    }

    const auto clock_start = std::chrono::steady_clock::now();
    std::unique_lock<std::mutex> l{m_mutex};

    const auto requested_count = apex::cast::safe_cast<std::uint32_t>(std::distance(begin, end));

    const auto key = type_id<T>();
    const auto iter = m_data_subscribers.find(key);
    assert(iter != m_data_subscribers.end());

    const auto request_id = send_control_request<T>(
      common::commands::Fetch,
      index,
      requested_count,
      history_length,
      db_name,
      instance_id
    );

    const auto expected_count = get_control_response(clock_start, timeout);
    assert(expected_count <= requested_count);

    auto & sub = static_cast<rclcpp::PollingSubscription<T> &>(*iter->second->sub);
    auto & ws = iter->second->ws;

    auto received_count = 0U;
    while (received_count < expected_count) {
      if (!ws.wait(common::adjust_and_test_timeout(clock_start, timeout))) {
        throw apex::runtime_error{"timeout while waiting for data of type",
                type_name<T>(), "to arrive"};
      }

      const auto data_msgs = sub.take();
      for (const auto & msg : data_msgs) {
        if (msg.info().valid()) {
          const auto received_request_id = common::get_request_id(msg.data());
          if (received_request_id == request_id) {
            if (begin == end) {
              throw apex::runtime_error{"unexpected data messages of type",
                      type_name<T>(), "received"};
            }
            *begin = msg.data();
            common::set_zero_request_id(*begin);
            ++begin;
            ++received_count;
          }
        }
      }
    }
    return begin;
  }

  /// \brief Sends request to fetch a message from the storage service
  /// \param db_name The name of the database
  /// \param instance_id The id of the data store instance
  /// \param history_length How many old records to store
  /// \param result The fetched message
  /// \param index Which one message to fetch (0 == latest)
  /// \param timeout The timeout for this operation
  /// \return Whether the operation was successful
  /// \cert
  /// \deterministic
  template<class T>
  bool fetch(
    const std::string & db_name,
    std::uint32_t instance_id,
    std::uint32_t history_length,
    T & result,
    std::uint32_t index,
    std::chrono::nanoseconds timeout)
  {
    const auto begin = &result;
    const auto end = begin + 1;
    return fetch<T>(db_name, instance_id, history_length, begin, end, index, timeout) == end;
  }

  /// \brief Explicitly asks the storage service to commit all changes
  /// \param db_name The name of the database
  /// \param timeout The timeout for this operation
  /// \cert
  /// \deterministic
  void commit(const std::string & db_name, std::chrono::nanoseconds timeout)
  {
    using storage_msgs::srv::Control;

    const auto clock_start = std::chrono::steady_clock::now();
    std::unique_lock<std::mutex> l{m_mutex};
    auto req = m_control_srv->borrow_loaned_request();
    req.get().command_code = common::commands::Commit;
    req.get().hdr.db_name = db_name;
    (void) m_control_srv->async_send_request(std::move(req));
    (void) get_control_response(clock_start, timeout);
  }

  /// \brief Sends a request to drop a database
  /// \param db_name The name of the database to drop
  /// \param timeout The timeout for this operation
  /// \cert
  /// \deterministic
  void drop(const std::string & db_name, std::chrono::nanoseconds timeout)
  {
    drop(db_name, nullptr, 0U, timeout);
  }

  /// \brief Sends a request to drop a single data store
  /// \param db_name The name of the database
  /// \param instance_id The id of the data store instance
  /// \param timeout The timeout for this operation
  /// \cert
  /// \deterministic
  template<class T>
  void drop(
    const std::string & db_name,
    std::uint32_t instance_id,
    std::chrono::nanoseconds timeout)
  {
    drop(db_name, type_name<T>(), instance_id, timeout);
  }

  /// \brief Sends a request to drop a database or a single data store
  /// \param db_name The name of the database
  /// \param msg_type_name The name of the message type or empty for the whole db
  /// \param instance_id The id of the data store instance
  /// \param timeout The timeout for this operation
  /// \cert
  /// \deterministic
  void drop(
    const std::string & db_name,
    const char * msg_type_name,
    std::uint32_t instance_id,
    std::chrono::nanoseconds timeout)
  {
    assert((instance_id == 0U) || (msg_type_name != nullptr));
    using storage_msgs::srv::Control;
    const auto clock_start = std::chrono::steady_clock::now();
    std::unique_lock<std::mutex> l{m_mutex};
    auto req = m_control_srv->borrow_loaned_request();
    req.get().command_code = common::commands::Drop;
    auto & hdr = req.get().hdr;
    hdr.db_name = db_name;
    if (msg_type_name != nullptr) {
      hdr.msg_type_name = msg_type_name;
      hdr.instance_id = instance_id;
    }
    (void) m_control_srv->async_send_request(std::move(req));
    (void) get_control_response(clock_start, timeout);
  }

  /// \brief Registers a watch callable for a data store
  /// \tparam T the type of the data store
  /// \tparam F The type of the callable to call when and update is received
  /// \param f The callable which takes (const T&) or (T) as the only argument
  /// \param db_name The name of the database of the data store
  /// \param instance_id The instance id of the data store
  /// \param timeout The timeout for this operation
  /// \return A unique id of the watch (cannot be 0)
  /// \cert
  template<class T, class F>
  std::size_t register_watch_callable(
    F && f,
    const std::string & db_name,
    std::uint32_t instance_id,
    std::chrono::nanoseconds timeout
  )
  {
    const auto clock_start = std::chrono::steady_clock::now();
    std::unique_lock<std::mutex> l{m_mutex};
    if (m_worker_thread != apex::nullopt && m_worker_thread->issued()) {
      throw apex::runtime_error{"cannot add a watch while running"};
    }
    const auto msg_type_name = type_name<T>();
    const auto key = type_id<T>();
    typename rclcpp::PollingSubscription<T>::SharedPtr sub;
    auto watch_iter = m_watch_clients.find(key);
    if (watch_iter == m_watch_clients.end()) {
      sub = m_node->create_polling_subscription<T>(
        common::get_watch_topic_name(msg_type_name, m_instance_name),
        m_qos.subscriber);
      sub->wait_for_matched(1U, common::adjust_and_test_timeout(clock_start, timeout));
      watch_iter = m_watch_clients.emplace(key, watch_data{sub}).first;
    }

    auto & vec = watch_iter->second.callables[db_name][instance_id];
    vec.push_back(details::make_watch_callable(m_logger, std::forward<F>(f)));
    if (m_current_callable_id == std::numeric_limits<std::size_t>::max()) {
      throw apex::runtime_error{"too many watches"};
    }
    ++m_current_callable_id;
    m_callable_handles[m_current_callable_id] = callable_handle{&vec, vec.back().get()};

    if (sub != nullptr) {
      if (m_watch_ws == apex::nullopt) {
        m_watch_ws.emplace();
        m_watch_ws->add(m_stop, [] {});
        m_worker_thread.emplace([this] {run();}, m_thread_attr);
      }
      m_watch_ws->add(sub,
        [sub, callables = &watch_iter->second.callables] {
          const auto msgs = sub->take();
          for (const auto & msg : msgs) {
            if (msg.info().valid()) {
              decltype(auto) data = msg.data();
              const auto db_name_iter = callables->find(data.header.db_name);
              if (db_name_iter != callables->end()) {
                const auto instance_iter = db_name_iter->second.find(
                  data.header.instance_id);
                if (instance_iter != db_name_iter->second.end()) {
                  for (const auto & c : instance_iter->second) {
                    c->call(&data);
                  }
                }
              }
            }
          }
        }
      );
    }
    return m_current_callable_id;
  }

  void remove_watch(std::size_t watch_id) noexcept
  {
    std::unique_lock<std::mutex> l{m_mutex};
    const auto iter = m_callable_handles.find(watch_id);
    if (iter != m_callable_handles.end()) {
      auto & vec = *iter->second.vec;
      const auto ptr = iter->second.elem;
      (void) vec.erase(std::remove_if(vec.begin(), vec.end(),
        [ptr](const auto & e) {return e.get() == ptr;}));
      (void) m_callable_handles.erase(iter);
    }
  }

  /// \brief Sends a request to storage control service
  /// \param command The command to send
  /// \param index The index of the data message for the command (if relevant)
  /// \param count The amount of the data message to work on (if relevant)
  /// \param db_name The name of the database
  /// \param instance_id The id of the data store instance
  /// \cert
  /// \deterministic
  template<class T>
  rmw_request_id_t send_control_request(
    std::uint8_t command,
    std::uint32_t index,
    std::uint32_t count,
    std::uint32_t history_length,
    const std::string & db_name,
    std::uint32_t instance_id)
  {
    auto req = m_control_srv->borrow_loaned_request();
    req.get().command_code = command;
    auto & hdr = req.get().hdr;
    hdr.db_name = db_name;
    hdr.instance_id = instance_id;
    hdr.index = index;
    hdr.count = count;
    hdr.history_length = history_length;
    hdr.msg_type_name = type_name<T>();
    const auto sequence_number = m_control_srv->async_send_request(std::move(req));
    const auto request_id = m_control_srv->request_last_header();
    assert(sequence_number == request_id.sequence_number);
    (void) sequence_number;
    return request_id;
  }

  /// \brief Waits for a response from the storage control service
  /// \param clock_start The time point to calculate the timeout from
  /// \param timeout The timeout of this operation
  /// \return The number of messages the storage is able to provide
  /// \cert
  /// \deterministic
  std::uint32_t get_control_response(
    std::chrono::steady_clock::time_point clock_start,
    std::chrono::nanoseconds timeout)
  {
    while (true) {
      if (!m_service_ws.wait(common::adjust_and_test_timeout(clock_start, timeout))) {
        throw apex::runtime_error{"timeout while waiting for answer from the control service"};
      }

      const auto responses = m_control_srv->take_response();
      for (const auto & response : responses) {
        if (response.data().status != 0U) {
          throw apex::runtime_error{"remote:", response.data().error_string};
        }

        return response.data().message_count;
      }
    }
  }

  /// \brief The process-wise type id
  /// \tparam T The type to identify
  /// \return A type id
  /// \cert
  /// \deterministic
  template<typename T>
  static std::uint64_t type_id() noexcept
  {
    static std::uint8_t dummy{};
    /*
     AXIVION Next Line MisraC++2023-8.2.5, MisraC++2023-8.2.7: Reason: Code Quality (Functional
     suitability), Justification: In-process type_id
     */
    return reinterpret_cast<std::uint64_t>(&dummy);
  }

  /// \brief Get a message type name as defined by rosidl_generator_traits
  /// \return The message type name as defined by rosidl_generator_traits
  /// \cert
  /// \deterministic
  template<typename T>
  static const char * type_name() noexcept
  {
    return rosidl_generator_traits::name<T>();
  }

  /// \brief Runs the watcher thread
  /// \cert
  /// \deterministic
  void run()
  {
    try {
      while (true) {
        m_watch_ws->wait_and_dispatch_all();
        if ((*m_watch_ws)[m_stop]) {
          break;
        }
      }
    } catch (const std::exception & e) {
      APEX_ERROR(m_logger, "Unexpected error in the storage-com watcher thread:", e.what());
      throw;
    } catch (...) {
      APEX_ERROR(m_logger, "Unexpected error in the storage-com watcher thread");
      throw;
    }
  }

  const std::string m_instance_name;
  rclcpp::Node::SharedPtr m_node;
  apex::logging::LoggerBase m_logger;
  common::qos m_qos;
  rclcpp::PollingClient<storage_msgs::srv::Control>::SharedPtr m_control_srv;
  rclcpp::dynamic_waitset::Waitset m_service_ws;
  std::unordered_map<std::uint64_t, rclcpp::PublisherBase::SharedPtr> m_data_publishers;

  /// \brief The infrastructural objects for data subscription
  /// \cert
  struct sub_data
  {
    /// \brief Creates an infrastructural objects for data subscription
    /// \cert
    explicit sub_data(rclcpp::PollingSubscriptionBase::SharedPtr s)
    : sub{std::move(s)},
      ws{sub} {}

    rclcpp::PollingSubscriptionBase::SharedPtr sub;
    rclcpp::dynamic_waitset::Waitset ws;
  };

  std::unordered_map<std::uint64_t, std::unique_ptr<sub_data>> m_data_subscribers;

  /// \brief The infrastructural objects for data watch
  /// \cert
  struct watch_data
  {
    /// \brief Creates an infrastructural objects for data watch
    /// \cert
    explicit watch_data(rclcpp::PollingSubscriptionBase::SharedPtr sub)
    : sub{std::move(sub)} {}

    rclcpp::PollingSubscriptionBase::SharedPtr sub;
    std::map<std::string,
      std::map<std::uint32_t,
      std::vector<std::unique_ptr<details::watch_callable_base>>>> callables;
  };

  struct callable_handle
  {
    // pointer to the vector which holds the elem
    std::vector<std::unique_ptr<details::watch_callable_base>> * vec;
    // the pointer to the callable in vector vec which is used to find it by value
    details::watch_callable_base * elem;
  };

  std::size_t m_current_callable_id{0U};
  std::map<std::size_t, callable_handle> m_callable_handles;
  std::map<std::uint64_t, watch_data> m_watch_clients;

  mutable std::mutex m_mutex;
  apex::optional<rclcpp::dynamic_waitset::Waitset> m_watch_ws;
  std::shared_ptr<rclcpp::dynamic_waitset::GuardCondition> m_stop{
    std::make_shared<rclcpp::dynamic_waitset::GuardCondition>()
  };
  apex::optional<apex::threading::thread> m_worker_thread;
  apex::threading::thread_attributes m_thread_attr;
};

template<class T, common::enable_if_compatible_message_type_t<T>>
typename store<T>::shared_ptr
database::get_store(std::uint32_t instance_id, std::uint32_t history_length)
{
  m_com->registration<T>(m_db_name, instance_id, history_length, m_timeout);
  return typename store<T>::shared_ptr {
    new store<T>{shared_from_this(), instance_id, history_length}
  };
}

void database::commit() const
{
  m_com->commit(m_db_name, m_timeout);
}

void database::drop() const
{
  m_com->drop(m_db_name, m_timeout);
}

template<class T>
void store<T>::save(T & msg, std::uint32_t history_length) const
{
  msg.header.transient = false;
  m_database->m_com->save(m_database->m_db_name, m_instance_id, history_length,
    msg, m_database->m_timeout);
}

template<class T>
void store<T>::save(T & msg) const
{
  save(msg, m_history_length);
}

template<class T>
void store<T>::save_transient(T & msg, std::uint32_t history_length) const
{
  msg.header.transient = true;
  m_database->m_com->save(m_database->m_db_name, m_instance_id, history_length,
    msg, m_database->m_timeout);
}

template<class T>
void store<T>::save_transient(T & msg) const
{
  save_transient(msg, m_history_length);
}

template<class T>
bool store<T>::fetch(T & result) const
{
  return m_database->m_com->fetch(m_database->m_db_name, m_instance_id, m_history_length,
           result, 0, m_database->m_timeout);
}

template<class T>
template<class I>
I store<T>::fetch_history(
  I begin,
  I end,
  std::uint32_t index) const
{
  return m_database->m_com->fetch<T>(m_database->m_db_name, m_instance_id, m_history_length,
           begin, end, index, m_database->m_timeout);
}

template<class T>
void store<T>::drop() const
{
  m_database->m_com->drop<T>(m_database->m_db_name, m_instance_id, m_database->m_timeout);
}

template<class T>
template<class F,
  std::enable_if_t<callable_traits<F>::Arity == 1> *,
  class U,
  std::enable_if_t<std::is_convertible<const T, U>::value> *
>
std::size_t store<T>::add_watch(F && f) const  //lint !e4981  NOLINT  Definition matches declaration
{
  return m_database->m_com->template register_watch_callable<T>(
    std::forward<F>(f), m_database->m_db_name, m_instance_id, m_database->m_timeout);
}

template<class T>
void store<T>::remove_watch(std::size_t watch_id) const noexcept
{
  return m_database->m_com->remove_watch(watch_id);
}

/// \brief Creates on demand a communication object and an associated database (helper)
/// \param db_name The name of the database
/// \param db_timeout The timout of operations on the database
/// \param service_instance_name The name of the instance of the service
/// \param qos The QoS settings for control service and data subscriptions
/// \param wait_for_service_timeout How long to wait for service
/// after client's creation
/// \param thread_attr Attributes for the watching thread
/// \return A database object
/// \cert
inline database::shared_ptr get_database(
  std::string db_name,
  std::chrono::nanoseconds db_timeout,
  std::string service_instance_name = "",
  common::qos qos = common::qos{},
  std::chrono::nanoseconds wait_for_service_timeout = std::chrono::seconds{1},
  apex::threading::thread_attributes thread_attr = apex::threading::thread_attributes::build())
{
  return communication::get(
    std::move(service_instance_name),
    std::move(qos),
    wait_for_service_timeout,
    std::move(thread_attr))->get_database(std::move(db_name), db_timeout);
}

/// \brief Creates on demand a communication object and an associated database (helper)
/// \param db_name The name of the database
/// \param service_instance_name The name of the instance of the service
/// \param qos The QoS settings for control service and data subscriptions
/// \param wait_for_service_timeout How long to wait for service
/// after client's creation
/// \param thread_attr Attributes for the watching thread
/// \return A database object
/// \cert
inline database::shared_ptr get_database(
  std::string db_name,
  std::string service_instance_name = "",
  common::qos qos = common::qos{},
  std::chrono::nanoseconds wait_for_service_timeout = std::chrono::seconds{1},
  apex::threading::thread_attributes thread_attr = apex::threading::thread_attributes::build())
{
  return get_database(std::move(db_name),
           std::chrono::seconds{1},
           std::move(service_instance_name),
           std::move(qos),
           wait_for_service_timeout,
           std::move(thread_attr));
}

}  // namespace storage
}  // namespace apex

#endif  // STORAGE__API__STORAGE_HPP_
