/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#ifndef STORAGE__JSON_PARSER__JSON_COMMON_HPP_
#define STORAGE__JSON_PARSER__JSON_COMMON_HPP_

#include <nlohmann/json.hpp>
#include <cpputils/common_exceptions.hpp>
#include <rosidl_typesupport_introspection_cpp/field_types.hpp>
#include <rosidl_typesupport_introspection_cpp/message_introspection.hpp>

#include <map>
#include <vector>
#include <string>
#include <cstdint>
#include <cstddef>
#include <codecvt>
#include <locale>
#include <cassert>

namespace apex
{
namespace storage
{
namespace json_parser
{
/// \brief Json base type
using json = nlohmann::basic_json<std::map, std::vector, std::string, bool,
    std::int64_t, std::uint64_t, double>;

/// \brief ROS message meta-information pointer
using msg_type_info_t = rosidl_typesupport_introspection_cpp::MessageMembers;

/// \brief ROS message member meta-information pointer
using msg_member_info_t = rosidl_typesupport_introspection_cpp::MessageMember;

namespace details
{
/// \brief Defines the sized int type depending on the architecture int type
template<class T, class = void>
struct typed_int;

template<class T>
struct typed_int<T, std::enable_if_t<std::is_same<T, int>::value && sizeof(T) == 2>>
{
  using type = std::int16_t;
  static constexpr const char * string = "std::int16_t";
};

template<class T>
struct typed_int<T, std::enable_if_t<std::is_same<T, int>::value && sizeof(T) == 4>>
{
  using type = std::int32_t;
  static constexpr const char * string = "std::int32_t";
};

template<class T>
struct typed_int<T, std::enable_if_t<std::is_same<T, int>::value && sizeof(T) == 8>>
{
  using type = std::int64_t;
  static constexpr const char * string = "std::int64_t";
};
}  // namespace details

/// \brief The sized int type which depends on the architecture int type
using typed_int_t = typename details::typed_int<int>::type;

/// \brief The name of the real int type
static constexpr const char * typed_int_string = details::typed_int<int>::string;

/// \brief Returns the type info for a nested type
/// \param The member info the type is nested under
/// \return The type info for a nested type
inline const msg_type_info_t * get_nested_type_info(const msg_member_info_t & member_info) noexcept
{
  /*
   AXIVION Next Construct MisraC++2023-8.2.5, MisraC++2023-8.2.6: Reason: Code Quality (Functional
   suitability), Justification: unrelated reinterpret cast
   */
  return reinterpret_cast<const msg_type_info_t *>(member_info.members_->data);
}

inline std::u16string string_to_u16string(const std::string & input)
{
  std::wstring_convert<std::codecvt_utf8_utf16<char16_t>, char16_t> converter;
  return converter.from_bytes(input);
}

inline std::string u16string_to_string(const std::u16string & input)
{
  std::wstring_convert<std::codecvt_utf8_utf16<char16_t>, char16_t> converter;
  return converter.to_bytes(input);
}

}  // namespace json_parser
}  // namespace storage
}  // namespace apex

#endif  // STORAGE__JSON_PARSER__JSON_COMMON_HPP_
