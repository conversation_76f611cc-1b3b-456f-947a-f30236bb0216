/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#ifndef STORAGE__COMMON__QOS_HPP_
#define STORAGE__COMMON__QOS_HPP_

#include <rclcpp/qos.hpp>

#include <memory>

namespace apex
{
namespace storage
{
namespace common
{
/// \class qos
/// Holds the various QoS settings used by storage service
/// This is asymmetric as reader must use DDS settings in order to set limits on keyed instances
/// \cert
struct qos
{
  // TODO(Sumanth.Nirmal) #31386 these should be configurable by user
  rclcpp::QoS subscriber{rclcpp::DefaultQoS().keep_last(100).
    resource_limits_max_non_self_contained_type_serialized_size(131072)};
  rclcpp::QoS publisher{rclcpp::DefaultQoS().keep_last(100).
    resource_limits_max_non_self_contained_type_serialized_size(131072)};
  rclcpp::QoS service{rclcpp::DefaultQoS().keep_last(1)};
};
}  // namespace common
}  // namespace storage
}  // namespace apex

#endif  // STORAGE__COMMON__QOS_HPP_
