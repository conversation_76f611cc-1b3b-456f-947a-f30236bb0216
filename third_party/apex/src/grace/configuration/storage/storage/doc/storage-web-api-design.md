---
tags:
  - storage
---

# Storage web API

## Purpose / Use cases

The storage web API is a self-contained web server that provides a REST-style API
wrapper for the [generic storage API](storage-design.md#api) and aims to serve web-based
applications working with [storage](storage-design.md).

!!! note
    This functionality only exists in non-certifiable builds and is aimed to serve
    the tooling. It is not recommended to run it on a target device

## Design

The web API is implemented as a self-contained web server that is run by the
`message_storage_web_server` executable. This executable serves the incoming HTTP requests.
There is near 1:1 functional parity between the web API and the
[message_storage_cli](storage-design.md#cli-tool), as they both delegate to the same
[generic storage API](storage-design.md#api) under the hood.

### Usage

{{ source_admonition("ApexGraceBilbo") }}

```shell dollar
message_storage_web_server --apex-settings-file optional-settings-file.yaml
```

#### Web application

Optionally, the `message_storage_web_server` may also serve an arbitrary web application. The web application
is run by serving the `index.html` file from the working directory of the server
if this file exists.
The application is always mounted to the root of the server and it is the responsibility of
the application to avoid trespassing on any registered API paths.

#### Settings file

Configuration of the `message_storage_web_server` can be done via an
[Apex.Settings](settings-design.md) yaml file:

{{ code_snippet(
    "grace/configuration/storage/storage/test/web-settings.yaml",
    {'tag': '', 'title': 'web-settings.yaml'}, 'yaml')}}

!!! note
    All the values of the settings in the example above are the defaults which are
    used if no values provided

#### Command line arguments

The parameters which belong to the server and set via [settings file](#settings-file)
can also be overridden by the command line
arguments which may be convenient during development:

```shell dollar
--port <arg>                   The port to listen on
--work-dir <arg>               The working directory for the web application
```

Any API-specific parameters are set via the [settings file](#settings-file) only.

### API

#### Deviations from common REST conventions

The API does not strictly adheres to common REST-API conventions in two ways:

1. Resources are located via URL parameters rather than via their paths. The reason for this is
   that the most of the resource names may include characters which must be URL encoded. For the
   most clients the URL encoding is more naturally applied to the arguments and not to paths
2. There are no `PUT` methods used. The reason for this is that the storage API does not have
   any distinct "create resource" semantics -- it is always "get or create",
   so all requests of this type are handled via the `GET` HTTP method

#### URL arguments

This is the full list of URL parameters used by the API.
Not all of the functions make use of all of the parameters but when they do, the name,
whether the argument is optional, the default value, and the interpretation of the value
are identical for all usages.

1. `service_instance` -- The name of the service instance. Type: `string`.
**Optional**, default is `""`
1. `db_name` -- The name of the database Type: `string`
1. `msg_type_name` -- The name of the ROS message type  (slash delimited). Type: `string`
1. `instance_id` -- The numeric instance id of a data store. Type: `uint32`.
**Optional**, default is `0`
1. `history_length` -- The length of the history for `save` operations. Type: `uint32`.
**Optional**, default is `0`
1. `history_index` -- The index of a message in history for `fetch_history` (0 is the latest).
Type: `uint32`. **Optional**, default is `0`

#### Common URL path

All API functions are located under `/storage/api/`

#### Error format

All errors are returned as an appropriate HTTP error status with
`application/json` content. The JSON object consists of one entry named `error` with
the textual message provided by the originating exception's `what()` call.

#### Function list

##### `generate`

* **HTTP method**: `GET`
* **URL parameters**: `msg_type_name`
* **Return content type**: `application/json`
* **Return value**: A JSON representation of a default initialized message of type `msg_type_name`

##### `enumerate_databases`

* **HTTP method**: `GET`
* **URL parameters**: `service_instance`
* **Return content type**: `application/json`
* **Return value**: A JSON list of the databases.
[See format](storage-design.md#database-enumeration)

##### `enumerate_data_stores`

* **HTTP method**: `GET`
* **URL parameters**: `service_instance`, `db_name`
* **Return content type**: `application/json`
* **Return value**: A JSON list of the data stores.
[See format](storage-design.md#data-store-enumeration)

##### `fetch`

* **HTTP method**: `GET`
* **URL parameters**: `service_instance`, `db_name`, `msg_type_name`, `instance_id`
* **Return content type**: `application/json`
* **Return value**: A JSON representation of a message of type `msg_type_name`

##### `fetch_history`

* **HTTP method**: `GET`
* **URL parameters**: `service_instance`, `db_name`, `msg_type_name`, `instance_id`,
`history_index`
* **Return content type**: `application/json`
* **Return value**: A JSON representation of a message of type `msg_type_name`

##### `save`

* **HTTP method**: `POST`
* **URL parameters**: `service_instance`, `db_name`, `msg_type_name`, `instance_id`,
`history_length`
* **Content type**: `application/json`
* **Content**: A JSON representation of a message of type `msg_type_name`

##### `save_transient`

* **HTTP method**: `POST`
* **URL parameters**: `service_instance`, `db_name`, `msg_type_name`, `instance_id`, `history_length`
* **Content type**: `application/json`
* **Content**: A JSON representation of a message of type `msg_type_name`

##### `drop_database`

* **HTTP method**: `DELETE`
* **URL parameters**: `service_instance`, `db_name`

##### `drop_data_store`

* **HTTP method**: `DELETE`
* **URL parameters**: `service_instance`, `db_name`, `msg_type_name`, `instance_id`

##### `commit`

* **HTTP method**: `POST`
* **URL parameters**: `service_instance`, `db_name`

##### `watch`

* **HTTP method**: `Websocket`
* **URL parameters**: `service_instance`, `db_name`, `msg_type_name`, `instance_id`
* **Return value**: Push notifications of textual JSON representations of messages of type
`msg_type_name` on the websocket
