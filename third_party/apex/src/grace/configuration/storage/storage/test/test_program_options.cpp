/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#include <gtest/gtest.h>

#include <storage/ui/program_options.hpp>

#include <utility>
#include <vector>
#include <string>
#include <cstdint>

namespace po = apex::storage::ui::program_options;
using namespace std::string_literals;  // NOLINT

namespace
{
class test_program_options : public ::testing::Test
{
protected:
  std::pair<std::int32_t, char **> create_argc_argv(const std::vector<std::string> & arguments)
  {
    args.clear();
    argv.clear();
    args = arguments;
    for (const auto & arg : args) {
      argv.push_back(const_cast<char *>(arg.data()));
    }
    argv.push_back(nullptr);
    return std::make_pair(argv.size() - 1, argv.data());
  }
  std::vector<std::string> args;
  std::vector<char *> argv;
};
}  // namespace

TEST_F(test_program_options, basic_operations) {
  po::options_map map;
  ASSERT_NO_THROW(map.parse(0, nullptr));
  ASSERT_FALSE(map.exists("my_opt"));
  ASSERT_FALSE(map.exists("my_opt2"));
  ASSERT_EQ(map.size(), 0U);
  ASSERT_TRUE(map.empty());
  map.add(po::value<std::string>{"my_opt", "my option"})
  .add(po::value<std::int32_t>{"my_opt2", "my option2"})
  .add(po::bool_switch{"my_opt3", "my option3"});
  ASSERT_TRUE(map.exists("my_opt"));
  ASSERT_TRUE(map.exists("my_opt2"));
  ASSERT_TRUE(map.exists("my_opt3"));
  ASSERT_EQ(map.size(), 3U);
  ASSERT_FALSE(map.empty());
  ASSERT_FALSE(map["my_opt"].has_argument());
  ASSERT_FALSE(map["my_opt"].has_value());
  ASSERT_FALSE(map["my_opt"]);
  ASSERT_FALSE(map["my_opt"].is_required());
  ASSERT_FALSE(map["my_opt2"].has_argument());
  ASSERT_FALSE(map["my_opt2"].has_value());
  ASSERT_FALSE(map["my_opt2"]);
  ASSERT_FALSE(map["my_opt2"].is_required());
  ASSERT_FALSE(map["my_opt3"].has_argument());
  ASSERT_FALSE(map["my_opt3"].has_value());
  ASSERT_FALSE(map["my_opt3"]);
  ASSERT_FALSE(map["my_opt3"].is_required());
  map.clear();
  ASSERT_EQ(map.size(), 0U);
  ASSERT_TRUE(map.empty());
}

TEST_F(test_program_options, single_value_with_single_arg) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"})
  .add(po::value<std::int32_t>{"my_opt2", "my option2"});
  const auto argc_argv = create_argc_argv({"--my_opt", "hi", "--my_opt2", "42"});
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_TRUE(map["my_opt"].has_argument());
  ASSERT_TRUE(map["my_opt"].has_value());
  ASSERT_TRUE(map["my_opt"]);
  ASSERT_TRUE(map["my_opt2"].has_argument());
  ASSERT_TRUE(map["my_opt2"].has_value());
  ASSERT_TRUE(map["my_opt2"]);
  ASSERT_EQ(map["my_opt"].as<std::string>(), "hi");
  ASSERT_EQ(map["my_opt2"].as<std::int32_t>(), 42);
}

TEST_F(test_program_options, single_value_with_multiple_args) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"})
  .add(po::value<std::int32_t>{"my_opt2", "my option2"});
  const auto argc_argv = create_argc_argv(
    {"--my_opt", "hi",
      "--my_opt2", "42",
      "--my_opt", "bye",
      "--my_opt2", "43"
    }
  );
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_TRUE(map["my_opt"].has_argument());
  ASSERT_TRUE(map["my_opt"].has_value());
  ASSERT_TRUE(map["my_opt"]);
  ASSERT_TRUE(map["my_opt2"].has_argument());
  ASSERT_TRUE(map["my_opt2"].has_value());
  ASSERT_TRUE(map["my_opt2"]);
  ASSERT_EQ(map["my_opt"].as<std::string>(), "bye");
  ASSERT_EQ(map["my_opt2"].as<std::int32_t>(), 43);
}

TEST_F(test_program_options, multiple_values_with_multiple_args) {
  po::options_map map;
  map.add(po::value<std::vector<std::string>>{"my_opt", "my option"})
  .add(po::value<std::vector<std::int32_t>>{"my_opt2", "my option2"});
  const auto argc_argv = create_argc_argv(
    {"--my_opt", "hi",
      "--my_opt2", "42",
      "--my_opt", "bye",
      "--my_opt2", "43"
    }
  );
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_TRUE(map["my_opt"].has_argument());
  ASSERT_TRUE(map["my_opt"].has_value());
  ASSERT_TRUE(map["my_opt"]);
  ASSERT_TRUE(map["my_opt2"].has_argument());
  ASSERT_TRUE(map["my_opt2"].has_value());
  ASSERT_TRUE(map["my_opt2"]);
  const auto expected_str = std::vector<std::string>{"hi", "bye"};
  ASSERT_EQ(map["my_opt"].as<std::vector<std::string>>(), expected_str);
  const auto expected_int = std::vector<std::int32_t>{42, 43};
  ASSERT_EQ(map["my_opt2"].as<std::vector<std::int32_t>>(), expected_int);
}

TEST_F(test_program_options, single_value_with_default) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"}.default_value("hi"))
  .add(po::value<std::int32_t>{"my_opt2", "my option2"}.default_value(42));
  ASSERT_FALSE(map["my_opt"].has_argument());
  ASSERT_TRUE(map["my_opt"].has_value());
  ASSERT_TRUE(map["my_opt"]);
  ASSERT_FALSE(map["my_opt2"].has_argument());
  ASSERT_TRUE(map["my_opt2"].has_value());
  ASSERT_TRUE(map["my_opt2"]);
  ASSERT_EQ(map["my_opt"].as<std::string>(), "hi");
  ASSERT_EQ(map["my_opt2"].as<std::int32_t>(), 42);
  const auto argc_argv = create_argc_argv({"--my_opt2", "43"});
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_FALSE(map["my_opt"].has_argument());
  ASSERT_TRUE(map["my_opt"].has_value());
  ASSERT_TRUE(map["my_opt"]);
  ASSERT_TRUE(map["my_opt2"].has_argument());
  ASSERT_TRUE(map["my_opt2"].has_value());
  ASSERT_TRUE(map["my_opt2"]);
  ASSERT_EQ(map["my_opt"].as<std::string>(), "hi");
  ASSERT_EQ(map["my_opt2"].as<std::int32_t>(), 43);
}

TEST_F(test_program_options, multiple_values_with_default) {
  po::options_map map;
  map.add(po::value<std::vector<std::string>>{"my_opt", "my option"}
    .default_value({"hi", "bye"}))
  .add(po::value<std::vector<std::int32_t>>{"my_opt2", "my option2"}
    .default_value({42, 43}));
  ASSERT_FALSE(map["my_opt"].has_argument());
  ASSERT_TRUE(map["my_opt"].has_value());
  ASSERT_TRUE(map["my_opt"]);
  ASSERT_FALSE(map["my_opt2"].has_argument());
  ASSERT_TRUE(map["my_opt2"].has_value());
  ASSERT_TRUE(map["my_opt2"]);
  auto expected_str = std::vector<std::string>{"hi", "bye"};
  ASSERT_EQ(map["my_opt"].as<std::vector<std::string>>(), expected_str);
  auto expected_int = std::vector<std::int32_t>{42, 43};
  ASSERT_EQ(map["my_opt2"].as<std::vector<std::int32_t>>(), expected_int);
  const auto argc_argv = create_argc_argv({"--my_opt2", "1",
        "--my_opt2", "2",
        "--my_opt", "oops"});
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_TRUE(map["my_opt"].has_argument());
  ASSERT_TRUE(map["my_opt"].has_value());
  ASSERT_TRUE(map["my_opt"]);
  ASSERT_TRUE(map["my_opt2"].has_argument());
  ASSERT_TRUE(map["my_opt2"].has_value());
  ASSERT_TRUE(map["my_opt2"]);
  expected_str = std::vector<std::string>{"oops"};
  ASSERT_EQ(map["my_opt"].as<std::vector<std::string>>(), expected_str);
  expected_int = std::vector<std::int32_t>{1, 2};
  ASSERT_EQ(map["my_opt2"].as<std::vector<std::int32_t>>(), expected_int);
}

TEST_F(test_program_options, bool_value) {
  po::options_map map;
  map.add(po::value<bool>{"my_opt", "my option"});
  auto argc_argv = create_argc_argv({"--my_opt", "1"});
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_TRUE(map["my_opt"].has_argument());
  ASSERT_TRUE(map["my_opt"].has_value());
  ASSERT_TRUE(map["my_opt"]);
  ASSERT_TRUE(map["my_opt"].as<bool>());
  map.clear();
  map.add(po::value<bool>{"my_opt", "my option"});
  argc_argv = create_argc_argv({"--my_opt", "0"});
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_TRUE(map["my_opt"].has_argument());
  ASSERT_TRUE(map["my_opt"].has_value());
  ASSERT_TRUE(map["my_opt"]);
  ASSERT_FALSE(map["my_opt"].as<bool>());
  map.clear();
  map.add(po::value<bool>{"my_opt", "my option"});
  argc_argv = create_argc_argv({"--my_opt", "true"});
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_TRUE(map["my_opt"].has_argument());
  ASSERT_TRUE(map["my_opt"].has_value());
  ASSERT_TRUE(map["my_opt"]);
  ASSERT_TRUE(map["my_opt"].as<bool>());
  map.clear();
  map.add(po::value<bool>{"my_opt", "my option"});
  argc_argv = create_argc_argv({"--my_opt", "false"});
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_TRUE(map["my_opt"].has_argument());
  ASSERT_TRUE(map["my_opt"].has_value());
  ASSERT_TRUE(map["my_opt"]);
  ASSERT_FALSE(map["my_opt"].as<bool>());
  map.clear();
  map.add(po::value<bool>{"my_opt", "my option"});
  argc_argv = create_argc_argv({"--my_opt", "False"});
  ASSERT_THROW(map.parse(argc_argv.first, argc_argv.second), apex::runtime_error);
}

TEST_F(test_program_options, bool_switch) {
  po::options_map map;
  map.add(po::bool_switch{"my_opt", "my option"});
  ASSERT_THROW(map["my_opt"].as<bool>(), apex::runtime_error);
  const auto argc_argv = create_argc_argv({"--my_opt"});
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_TRUE(map["my_opt"].has_argument());
  ASSERT_TRUE(map["my_opt"].has_value());
  ASSERT_TRUE(map["my_opt"]);
  ASSERT_TRUE(map["my_opt"].as<bool>());
}

TEST_F(test_program_options, unknown_option) {
  po::options_map map;
  const auto argc_argv = create_argc_argv({"--my_opt"});
  ASSERT_THROW(map.parse(argc_argv.first, argc_argv.second), apex::runtime_error);
}

TEST_F(test_program_options, duplicate_description) {
  po::options_map map;
  map.add(po::bool_switch{"my_opt", "my option"});
  ASSERT_THROW(map.add(po::bool_switch{"my_opt", "my option"}), apex::runtime_error);
  ASSERT_NO_THROW(map.add(po::value<std::int32_t>{"my_opt2", "my option2"}));
  ASSERT_THROW(map.add(po::value<std::int32_t>{"my_opt2", "my option2"}), apex::runtime_error);
}

TEST_F(test_program_options, bad_value_type_parse) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"})
  .add(po::value<std::int32_t>{"my_opt2", "my option2"});
  const auto argc_argv = create_argc_argv({"--my_opt", "hi", "--my_opt2", "hi again"});
  ASSERT_THROW(map.parse(argc_argv.first, argc_argv.second), std::exception);
}

TEST_F(test_program_options, bad_value_type_as) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"})
  .add(po::value<std::int32_t>{"my_opt2", "my option2"});
  const auto argc_argv = create_argc_argv({"--my_opt", "hi", "--my_opt2", "42"});
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_THROW(map["my_opt"].as<std::int32_t>(), apex::runtime_error);
  ASSERT_THROW(map["my_opt2"].as<std::string>(), apex::runtime_error);
}

TEST_F(test_program_options, missing_argument) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"})
  .add(po::value<std::int32_t>{"my_opt2", "my option2"});
  const auto argc_argv = create_argc_argv({"--my_opt", "hi", "--my_opt2"});
  ASSERT_THROW(map.parse(argc_argv.first, argc_argv.second), apex::runtime_error);
}

TEST_F(test_program_options, bad_argument_form) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"})
  .add(po::value<std::int32_t>{"my_opt2", "my option2"});
  const auto argc_argv = create_argc_argv({"--my_opt", "--my_opt2", "42"});
  ASSERT_THROW(map.parse(argc_argv.first, argc_argv.second), apex::runtime_error);
}

TEST_F(test_program_options, required) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"}.required())
  .add(po::value<std::int32_t>{"my_opt2", "my option2"});
  const auto argc_argv = create_argc_argv({"--my_opt2", "42"});
  ASSERT_THROW(map.parse(argc_argv.first, argc_argv.second), apex::runtime_error);
}

TEST_F(test_program_options, result) {
  po::options_map map;
  std::string my_opt;
  std::int32_t my_opt2{0};
  std::vector<std::string> my_opt3;
  bool my_opt4{false};
  std::int32_t no_val{13};
  std::int32_t default_val{0};

  map.add(po::value<std::string>{"my_opt", "my option"}.result(&my_opt))
  .add(po::value<std::int32_t>{"my_opt2", "my option"}.result(&my_opt2))
  .add(po::value<std::vector<std::string>>{"my_opt3", "my option"}.result(&my_opt3))
  .add(po::bool_switch{"my_opt4", "my option"}.result(&my_opt4))
  .add(po::value<std::int32_t>{"no_val", "my option"}.result(&no_val))
  .add(po::value<std::int32_t>{"default_val",
      "my option"}.result(&default_val).default_value(44));

  const auto argc_argv = create_argc_argv({
    "--my_opt", "hi",
    "--my_opt2", "42",
    "--my_opt3", "a",
    "--my_opt4",
    "--my_opt3", "b",
    "--my_opt2", "43"
  }
  );
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_EQ(my_opt, "hi");
  ASSERT_EQ(my_opt2, 43);
  ASSERT_EQ(my_opt3, (std::vector<std::string>{"a", "b"}));
  ASSERT_TRUE(my_opt4);
  ASSERT_EQ(no_val, 13);
  ASSERT_EQ(default_val, 44);
}

TEST_F(test_program_options, result_bind_style) {
  po::options_map map;
  std::string my_opt;
  std::int32_t my_opt2{0};
  std::vector<std::string> my_opt3;
  bool my_opt4{false};

  map.add(po::bind_value(my_opt, "my_opt", "my option2"))
  .add(po::bind_value(my_opt2, "my_opt2", "my option2"))
  .add(po::bind_value(my_opt3, "my_opt3", "my option"))
  .add(po::bind_bool_switch(my_opt4, "my_opt4", "my option"));
  const auto argc_argv = create_argc_argv({
    "--my_opt", "hi",
    "--my_opt2", "42",
    "--my_opt3", "a",
    "--my_opt4",
    "--my_opt3", "b",
    "--my_opt2", "43"
  }
  );
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_EQ(my_opt, "hi");
  ASSERT_EQ(my_opt2, 43);
  ASSERT_EQ(my_opt3, (std::vector<std::string>{"a", "b"}));
  ASSERT_TRUE(my_opt4);
}

TEST_F(test_program_options, unique) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"}.unique());
  auto argc_argv = create_argc_argv({"--my_opt", "hi", "--my_opt", "bye"});
  ASSERT_THROW(map.parse(argc_argv.first, argc_argv.second), apex::runtime_error);
  map.clear();

  map.add(po::value<std::vector<std::string>>{"my_opt2", "my option"}.unique());
  // unique trumps vector
  argc_argv = create_argc_argv({"--my_opt2", "hi", "--my_opt2", "bye"});
  ASSERT_THROW(map.parse(argc_argv.first, argc_argv.second), apex::runtime_error);
}

TEST_F(test_program_options, constraint_at_most_one) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"})
  .add(po::value<std::string>{"my_opt2", "my option"})
  .add(po::value<std::string>{"my_opt3", "my option"})
  .required_at_most_one_of("my_opt", "my_opt2");

  auto argc_argv = create_argc_argv({"--my_opt3", "hi", "--my_opt3", "bye"});
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));

  argc_argv = create_argc_argv({"--my_opt", "hi", "--my_opt2", "bye"});
  ASSERT_THROW(map.parse(argc_argv.first, argc_argv.second), apex::runtime_error);
}

TEST_F(test_program_options, constraint_exactly_one) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"})
  .add(po::value<std::string>{"my_opt2", "my option"})
  .add(po::value<std::string>{"my_opt3", "my option"})
  .required_exactly_one_of("my_opt", "my_opt2");

  auto argc_argv = create_argc_argv({"--my_opt3", "hi", "--my_opt3", "bye"});
  ASSERT_THROW(map.parse(argc_argv.first, argc_argv.second), apex::runtime_error);

  argc_argv = create_argc_argv({"--my_opt", "hi", "--my_opt2", "bye"});
  ASSERT_THROW(map.parse(argc_argv.first, argc_argv.second), apex::runtime_error);
}

TEST_F(test_program_options, constraint_all_or_none) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"})
  .add(po::value<std::string>{"my_opt2", "my option"})
  .add(po::value<std::string>{"my_opt3", "my option"})
  .required_all_or_none_of("my_opt", "my_opt2");

  auto argc_argv = create_argc_argv({"--my_opt3", "hi", "--my_opt3", "bye"});
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  argc_argv = create_argc_argv({"--my_opt2", "bye"});
  ASSERT_THROW(map.parse(argc_argv.first, argc_argv.second), apex::runtime_error);
  argc_argv = create_argc_argv({"--my_opt", "hi", "--my_opt2", "bye"});
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
}

TEST_F(test_program_options, bad_constriants) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"})
  .add(po::value<std::string>{"my_opt2", "my option"})
  .add(po::value<std::string>{"my_opt3", "my option"});
  ASSERT_THROW(map.required_exactly_one_of("unknown"), apex::runtime_error);
  ASSERT_THROW(map.required_at_most_one_of("unknown"), apex::runtime_error);
  ASSERT_THROW(map.required_all_or_none_of("unknown"), apex::runtime_error);
}

TEST_F(test_program_options, help) {
  po::options_map map;
  map.add(po::value<std::string>{"my_opt", "my option"}.help());
  const auto argc_argv = create_argc_argv({"--my_opt", "hi", "--unknown", "bye"});
  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_TRUE(map["my_opt"]);
}

TEST_F(test_program_options, multitoken) {
  {
    po::options_map map;
    std::vector<std::string> my_opt;
    std::string my_opt2;
    map.add(po::bind_value(my_opt, "my_opt", "my option").multitoken())
    .add(po::bind_value(my_opt2, "my_opt2", "my option").required());
    const auto argc_argv = create_argc_argv({"--my_opt", "hi", "unknown", "--", "--my_opt2",
          "there"});
    ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
    ASSERT_EQ(my_opt, (std::vector<std::string>{"hi", "unknown"}));
    ASSERT_EQ(my_opt2, "there");
  }
  {
    po::options_map map;
    std::vector<std::string> my_opt;
    std::string my_opt2;
    map.add(po::bind_value(my_opt, "my_opt", "my option").multitoken())
    .add(po::bind_value(my_opt2, "my_opt2", "my option"));
    const auto argc_argv = create_argc_argv({"--my_opt", "hi", "unknown", "--my_opt2", "there"});
    ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
    ASSERT_EQ(my_opt, (std::vector<std::string>{"hi", "unknown", "--my_opt2", "there"}));
    ASSERT_EQ(my_opt2, "");
  }
  {
    po::options_map map;
    std::vector<std::string> my_opt;
    std::string my_opt2;
    map.add(po::bind_value(my_opt, "my_opt", "my option").multitoken())
    .add(po::bind_value(my_opt2, "my_opt2", "my option"));
    const auto argc_argv = create_argc_argv({"--my_opt", "--", "--my_opt2", "there"});
    ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
    ASSERT_TRUE(my_opt.empty());
    ASSERT_EQ(my_opt2, "there");
  }
}

TEST_F(test_program_options, floating_point) {
  po::options_map map;
  float my_opt;
  double my_opt2;
  std::vector<float> my_opt3;
  std::vector<double> my_opt4;
  map.add(po::bind_value(my_opt, "my_opt", "my option"))
  .add(po::bind_value(my_opt2, "my_opt2", "my option"))
  .add(po::bind_value(my_opt3, "my_opt3", "my option"))
  .add(po::bind_value(my_opt4, "my_opt4", "my option"));

  const auto argc_argv = create_argc_argv(
    {"--my_opt", "1.2",
      "--my_opt2", "2.3",
      "--my_opt3", "3.4", "--my_opt3", "5.6", "--my_opt3", "7.8",
      "--my_opt4", "9.10", "--my_opt4", "11.12", "--my_opt4", "13.14",
    });

  ASSERT_NO_THROW(map.parse(argc_argv.first, argc_argv.second));
  ASSERT_FLOAT_EQ(my_opt, 1.2f);
  ASSERT_DOUBLE_EQ(my_opt2, 2.3);
  ASSERT_FLOAT_EQ(my_opt3[0], 3.4f);
  ASSERT_FLOAT_EQ(my_opt3[1], 5.6f);
  ASSERT_FLOAT_EQ(my_opt3[2], 7.8f);
  ASSERT_DOUBLE_EQ(my_opt4[0], 9.10);
  ASSERT_DOUBLE_EQ(my_opt4[1], 11.12);
  ASSERT_DOUBLE_EQ(my_opt4[2], 13.14);
}
