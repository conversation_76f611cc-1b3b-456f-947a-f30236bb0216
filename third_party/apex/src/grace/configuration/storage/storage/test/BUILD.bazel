# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//common/bazel/rules_deployment:defs.bzl", "executables_collection")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test")

apex_cc_library(
    name = "test_lib",
    testonly = True,
    hdrs = glob([
        "*.hpp",
    ]),
    deps = [
        "//grace/configuration/storage/storage_test_msgs",
    ],
)

msgs_library(
    name = "test_json_parser_msgs",
    deps = [
        "//grace/configuration/storage/storage_test_msgs",
        "//grace/interfaces/std_msgs",
        "//grace/interfaces/storage_msgs",
    ],
)

cpp_msgs_introspection_library(
    name = "test_json_parser_introspection_msgs",
    add_shared = True,
    ament_runfiles = True,
    msgs = "test_json_parser_msgs",
)

cpp_msgs_introspection_library(
    name = "test_msgs_shared",
    add_shared = True,
    ament_runfiles = True,
    msgs = "//grace/interfaces/test_msgs",
)

cpp_msgs_introspection_library(
    name = "storage_test_msgs_shared",
    add_shared = True,
    ament_runfiles = True,
    msgs = "//grace/configuration/storage/storage_test_msgs",
)

apex_cc_test(
    name = "test_json_parser",
    srcs = ["test_json_parser.cpp"],
    dynamic_deps = [":test_json_parser_introspection_msgs"],
    deps = [
        ":test_json_parser_introspection_msgs",
        "//grace/configuration/storage/storage",
        "@apex//grace/tools/ros_domain_coordinator:gtest_main",
    ],
)

apex_cc_test(
    name = "test_ros_binary_message",
    srcs = ["test_ros_binary_message.cpp"],
    dynamic_deps = [":test_msgs_shared"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":test_msgs_shared",
        "//grace/configuration/storage/storage",
        "@apex//grace/tools/ros_domain_coordinator:gtest_main",
    ],
)

apex_cc_test(
    name = "test_unqlitecpp",
    srcs = ["test_unqlitecpp.cpp"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        "//grace/configuration/storage/storage",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_backend",
    srcs = [
        "test_data_enumerator.cpp",
        "test_database.cpp",
        "test_storage_service.cpp",
    ],
    dynamic_deps = [":storage_test_msgs_shared"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "constrained_test",
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        ":storage_test_msgs_shared",
        ":test_lib",
        "//grace/configuration/storage/storage",
        "@apex//grace/tools/ros_domain_coordinator:gtest_main",
    ],
)

apex_cc_test(
    name = "test_storage_doc",
    srcs = ["test_storage_doc.cpp"],
    dynamic_deps = [":storage_test_msgs_shared"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        ":storage_test_msgs_shared",
        ":test_lib",
        "//grace/configuration/storage/storage",
        "@apex//grace/tools/ros_domain_coordinator:gtest_main",
    ],
)

apex_cc_test(
    name = "test_storage_api",
    srcs = [
        "common.hpp",
        "test_storage_api.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "constrained_test",
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
    ],
    deps = [
        "//grace/configuration/storage/storage",
        "//grace/configuration/storage/storage_test_msgs",
        "//grace/interfaces/storage_msgs",
        "//grace/tools/ros_domain_coordinator:gtest_main",
    ],
)

apex_cc_test(
    name = "test_program_options",
    srcs = ["test_program_options.cpp"],
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
    ],
    deps = [
        "//grace/configuration/storage/storage:program_options",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_view_api",
    srcs = ["test_view_api.cpp"],
    dynamic_deps = [":storage_test_msgs_shared"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        ":storage_test_msgs_shared",
        ":test_lib",
        "//grace/configuration/storage/storage",
        "//grace/tools/ros_domain_coordinator:gtest_main",
    ],
)

apex_cc_test(
    name = "test_parameters_api",
    srcs = [
        "common.hpp",
        "service_runner.hpp",
        "test_parameters.cpp",
    ],
    dynamic_deps = [":storage_test_msgs_shared"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "constrained_test",
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        ":storage_test_msgs_shared",
        "//grace/configuration/storage/storage",
        "//grace/interfaces/storage_msgs",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_generic_storage_api",
    srcs = ["test_generic_storage_api.cpp"],
    dynamic_deps = [":storage_test_msgs_shared"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "constrained_test",
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        ":storage_test_msgs_shared",
        ":test_lib",
        "//grace/configuration/storage/storage",
        "//grace/tools/ros_domain_coordinator:gtest_main",
    ],
)

apex_cc_test(
    name = "test_common",
    srcs = ["test_common.cpp"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "//grace/configuration/storage/storage",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_message_storage_service_cli",
    srcs = ["test_message_storage_service_cli.cpp"],
    data = glob(["**/*.yaml"]),
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    local_defines = [
        'PACKAGE_PATH=\\\"grace/configuration/storage/storage\\\"',
    ],
    deps = [
        "//grace/configuration/storage/storage:message_storage_service_cli",
        "@googletest//:gtest_main",
    ],
)

executables_collection(
    name = "storage_executables",
    executables = [
        "//grace/configuration/storage/storage:message_storage_web_server",
    ],
    runfiles_folder = "ros_runtime/bin",
)

ament_pkg_resources(
    name = "message_storage_web_server_resources",
    package = "storage",
    resources = {},
)

launch_test(
    name = "test_web_server_runs_with_default_settings",
    data = [
        "web-settings.yaml",
        ":message_storage_web_server_resources",
        ":storage_executables",
    ],
    launch_test_file = "test_web_server_runs_with_default_settings.py",
    ros_domain_id_isolation = True,
    tags = [
        "constrained_test",
        "skip_coverage",
    ],
    deps = [
        "@apex//grace/configuration/storage/storage",
        "@apex//grace/tools/launch_ros/launch_testing_ros",
        "@apex//tools/ament/ament_index/ament_index_python",
        "@apex//tools/launch/launch",
    ],
)

sh_test(
    name = "test_rest_api",
    srcs = ["test_rest_api.sh"],
    args = [
        ".",
        "../$(rlocationpath //grace/configuration/storage/storage:message_storage_cli)",
        "../$(rlocationpath //grace/configuration/storage/storage:message_storage_service)",
        "../$(rlocationpath //grace/configuration/storage/storage:message_storage_web_server)",
        "$(rootpath //ida/resource_creator)",
    ],
    data = [
        "test_rest_api_store_srv1.yaml",
        "test_rest_api_store_srv2.yaml",
        ":storage_test_msgs_shared",
        "//grace/configuration/storage/storage:message_storage_cli",
        "//grace/configuration/storage/storage:message_storage_service",
        "//grace/configuration/storage/storage:message_storage_web_server",
        "//ida/resource_creator",
    ],
    tags = [
        "constrained_test",
        "exclusive",
        "skip_asan",  # FIXME: fix asan findings and remove this tag
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "test_parameters.cpp",
        "test_storage_doc.cpp",
        "web-settings.yaml",
    ],
    visibility = ["//grace/configuration/storage/storage/doc:__subpackages__"],
)
