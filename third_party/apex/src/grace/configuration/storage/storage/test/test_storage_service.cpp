/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#include <gtest/gtest.h>

#include <storage/service/storage_service.hpp>
#include <storage/common/names.hpp>
#include <storage/unqlitecpp/database.hpp>
#include <storage/unqlitecpp/cursor.hpp>
#include <storage/api/storage.hpp>
#include <storage/common/file.hpp>

#include <storage_msgs/srv/control.hpp>
#include <storage_test_msgs/msg/test_data_message.hpp>
#include <storage_test_msgs/msg/test_data_message2.hpp>
#include <test_msgs/msg/basic_types.hpp>

#include <rclcpp/rclcpp.hpp>
#include <rclcpp/dynamic_waitset/waitset.hpp>

#include <array>
#include <algorithm>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "common.hpp"
#include "service_runner.hpp"

using namespace apex::storage::backend;  // NOLINT
using namespace apex::storage;  // NOLINT
using namespace std::chrono_literals;
namespace unq = apex::storage::unqlite;
namespace common = apex::storage::common;
using rclcpp::dynamic_waitset::Waitset;
using test::service_runner;
using test::wait_for_state;
using test::remove_db_file;
using test::db_file_exists;
using storage_msgs::srv::Control;
using storage_test_msgs::msg::TestDataMessage;
using storage_test_msgs::msg::TestDataMessage2;
using test_msgs::msg::BasicTypes;

namespace
{

const char * db_name1 = "test_srv1";
const char * db_name2 = "test_srv2";

class test_storage_service : public ::testing::Test
{
protected:
  void SetUp() override
  {
    remove_db_file(db_name1);
    remove_db_file(db_name2);
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
    node = std::make_shared<rclcpp::Node>("test_storage_service");
  }

  void TearDown() override
  {
    rclcpp::shutdown();
  }

  void assert_protocol_response_error(
    const rclcpp::PollingClient<Control>::SharedPtr & control_srv,
    const Control::Request & req)
  {
    control_srv->async_send_request(req);
    Waitset ws{control_srv};
    ws.wait();
    const auto rsp = control_srv->take_response();
    ASSERT_EQ(rsp.size(), 1U);
    ASSERT_TRUE(rsp[0].info().valid());
    ASSERT_EQ(rsp[0].data().status, 1U);
    ASSERT_FALSE(rsp[0].data().error_string.empty());
  }

  rclcpp::Node::SharedPtr node;
};

inline bool test_if_value_in_db(const std::string & db_path, const std::string & key)
{
  unq::database db{common::get_db_file_name(db_path).c_str()};
  unq::cursor c{db};
  return c.seek(key.c_str());
}

inline void make_directory(const std::string & dirname)
{
  if (::mkdir(dirname.c_str(), 0777) < 0) {
    throw std::runtime_error{"can't create directory"};
  }
}

inline void remove_directory(const std::string & dirname)
{
  if (::system(("rm -rf ./" + dirname).c_str()) != 0) {
    throw std::runtime_error{"can't remove directory"};
  }
}

struct auto_remove_dir
{
  explicit auto_remove_dir(std::string dirname)
  : dir{std::move(dirname)}
  {
    make_directory(dir);
  }
  ~auto_remove_dir()
  {
    remove_directory(dir);
  }

  operator std::string() const {
    return dir;
  }

  const std::string dir;
};

}  // namespace

TEST_F(test_storage_service, create_store) {
  storage_service srv;
  ASSERT_TRUE(srv.get_instance_name().empty());
  ASSERT_EQ(srv.get_file_path(), "./");
  service_runner r{srv};
  {
    const auto com = communication::create(node);
    const auto db = com->get_database(db_name1);
    const auto store = db->get_store<TestDataMessage>();
    ASSERT_EQ(srv.get_database_count(), 1);
    ASSERT_EQ(srv.get_infra_count(), 1);
    ASSERT_EQ(srv.get_data_store_count(), 1);
    const auto store2 = db->get_store<TestDataMessage>();
    ASSERT_EQ(srv.get_database_count(), 1);
    ASSERT_EQ(srv.get_infra_count(), 1);
    ASSERT_EQ(srv.get_data_store_count(), 1);
    const auto store3 = db->get_store<TestDataMessage>(1U);
    ASSERT_EQ(srv.get_database_count(), 1);
    ASSERT_EQ(srv.get_infra_count(), 1);
    ASSERT_EQ(srv.get_data_store_count(), 2);
    const auto db2 = com->get_database(db_name2);
    const auto store4 = db2->get_store<TestDataMessage>();
    ASSERT_EQ(srv.get_database_count(), 2);
    ASSERT_EQ(srv.get_infra_count(), 1);
    ASSERT_EQ(srv.get_data_store_count(), 3);
    const auto store5 = db2->get_store<TestDataMessage2>();
    ASSERT_EQ(srv.get_database_count(), 2);
    ASSERT_EQ(srv.get_infra_count(), 2);
    ASSERT_EQ(srv.get_data_store_count(), 4);
    const auto store6 = db2->get_store<TestDataMessage2>(1);
    ASSERT_EQ(srv.get_database_count(), 2);
    ASSERT_EQ(srv.get_infra_count(), 2);
    ASSERT_EQ(srv.get_data_store_count(), 5);
    const auto store7 = db2->get_store<TestDataMessage2>(1);
    ASSERT_EQ(srv.get_database_count(), 2);
    ASSERT_EQ(srv.get_infra_count(), 2);
    ASSERT_EQ(srv.get_data_store_count(), 5);
  }
  ASSERT_NO_THROW(r.stop());
}

TEST_F(test_storage_service, save_load) {
  const auto msg_name = rosidl_generator_traits::name<TestDataMessage>();
  {
    storage_service srv;
    service_runner r{srv};

    const auto com = communication::create(node);
    const auto db = com->get_database(db_name1);
    ASSERT_THROW(db->commit(), apex::runtime_error);
    ASSERT_FALSE(db_file_exists(db_name1));
    const auto store = db->get_store<TestDataMessage>();
    ASSERT_FALSE(db_file_exists(db_name1));
    TestDataMessage msg;
    msg.data.int64_value = 42;
    store->save(msg);
    ASSERT_FALSE(test_if_value_in_db(db_name1, common::get_data_store_name(msg_name, 0U)));
    ASSERT_NO_THROW(db->commit());
    ASSERT_TRUE(db_file_exists(db_name1));
    ASSERT_TRUE(test_if_value_in_db(db_name1, common::get_data_store_name(msg_name, 0U)));
    const auto db2 = com->get_database(db_name2);
    const auto store2 = db2->get_store<TestDataMessage>(0, 9);
    ASSERT_FALSE(db_file_exists(db_name2));
    for (auto i = 0; i < 10; ++i) {
      msg.data.int64_value = i;
      store2->save(msg);
    }
    db2->commit();
    ASSERT_TRUE(db_file_exists(db_name2));
    ASSERT_TRUE(test_if_value_in_db(db_name2, common::get_data_store_name(msg_name, 0U)));
    ASSERT_NO_THROW(r.stop());
  }
  {
    storage_service srv;
    service_runner r{srv};

    const auto com = communication::create(node);
    const auto db1 = com->get_database(db_name1);
    const auto db2 = com->get_database(db_name2);
    ASSERT_TRUE(db_file_exists(db_name1));
    ASSERT_TRUE(db_file_exists(db_name2));
    const auto store1 = db1->get_store<TestDataMessage>();
    const auto store2 = db2->get_store<TestDataMessage>();
    std::vector<TestDataMessage> history(20);
    TestDataMessage res;
    ASSERT_TRUE(store1->fetch(res));
    ASSERT_EQ(res.data.int64_value, 42);
    ASSERT_EQ(store1->fetch_history(history.begin(), history.end(), 0U), history.begin() + 1);
    ASSERT_EQ(history.front().data.int64_value, 42);
    ASSERT_TRUE(store2->fetch(res));
    ASSERT_EQ(res.data.int64_value, 9);
    ASSERT_EQ(store2->fetch_history(history.begin(), history.end(), 0U), history.begin() + 10);
    for (auto i = 0U; i < 10U; ++i) {
      ASSERT_EQ(history[i].data.int64_value, 9 - i);
    }
    ASSERT_NO_THROW(r.stop());
  }
}

TEST_F(test_storage_service, save_fetch) {
  storage_service srv;
  service_runner r{srv};
  {
    const auto com = communication::create(node);
    const auto db = com->get_database(db_name1);
    const auto store = db->get_store<TestDataMessage>();
    TestDataMessage msg;
    ASSERT_FALSE(store->fetch(msg));
    msg.data.int64_value = 42;
    ASSERT_NO_THROW(store->save(msg));
    TestDataMessage msg_res;
    ASSERT_TRUE(store->fetch(msg_res));
    ASSERT_EQ(msg_res, msg);
    msg.data.int64_value = 43;
    ASSERT_NO_THROW(store->save(msg));
    ASSERT_NE(msg_res, msg);
    ASSERT_TRUE(store->fetch(msg_res));
    ASSERT_EQ(msg_res, msg);
    const auto store2 = db->get_store<TestDataMessage>(1);
    ASSERT_FALSE(store2->fetch(msg_res));
    msg.data.int64_value = 13;
    ASSERT_NO_THROW(store2->save(msg));
    ASSERT_TRUE(store2->fetch(msg_res));
    ASSERT_EQ(msg_res, msg);
  }
  ASSERT_NO_THROW(r.stop());
}

TEST_F(test_storage_service, fetch_history) {
  storage_service srv;
  service_runner r{srv};
  {
    const auto com = communication::create(node);
    const auto db = com->get_database(db_name1);
    const auto store = db->get_store<TestDataMessage>();

    std::array<TestDataMessage, 10> msgs;
    std::array<TestDataMessage, 10> msgs_res;

    ASSERT_EQ(store->fetch_history(msgs_res.begin(), msgs_res.end()), msgs_res.begin());
    auto counter = 0;
    for (auto & msg : msgs) {
      ++counter;
      msg.data.int64_value = counter;
    }
    for (auto i = 0U; i < 10; ++i) {
      ASSERT_NO_THROW(store->save(msgs[i]));
    }

    ASSERT_EQ(store->fetch_history(msgs_res.begin(), msgs_res.end()), msgs_res.begin() + 1);
    ASSERT_EQ(msgs.back(), msgs_res.front());
    const auto store2 = db->get_store<TestDataMessage>(0, 10);
    for (auto i = 0U; i < 10; ++i) {
      ASSERT_NO_THROW(store2->save(msgs[i]));
    }

    ASSERT_EQ(store2->fetch_history(msgs_res.begin(), msgs_res.end()), msgs_res.end());
    std::reverse(msgs_res.begin(), msgs_res.end());
    for (auto i = 0U; i < msgs.size(); ++i) {
      ASSERT_EQ(msgs[i], msgs_res[i]);
    }
  }
  ASSERT_NO_THROW(r.stop());
}

TEST_F(test_storage_service, save_fetch_many) {
  storage_service srv;
  service_runner r{srv};
  {
    const auto com = communication::create(node);
    const auto db = com->get_database(db_name1);
    const auto store = db->get_store<TestDataMessage>();
    TestDataMessage msg;
    TestDataMessage msg_res;
    for (auto i = 0; i < 500; ++i) {
      msg.data.int64_value = i;
      ASSERT_NO_THROW(store->save(msg));
      ASSERT_TRUE(store->fetch(msg_res));
      ASSERT_EQ(msg, msg_res);
    }

    for (auto i = 0; i < 500; ++i) {
      msg.data.int64_value = i;
      ASSERT_NO_THROW(store->save(msg));
    }

    for (auto i = 0; i < 500; ++i) {
      ASSERT_TRUE(store->fetch(msg_res));
      ASSERT_EQ(msg, msg_res);
    }
  }
  ASSERT_NO_THROW(r.stop());
}

TEST_F(test_storage_service, service_instances) {
  const auto msg_name = rosidl_generator_traits::name<TestDataMessage>();

  auto_remove_dir dir1{"dir1"};
  auto_remove_dir dir2{"dir2"};

  storage_service srv1{dir1, dir1};
  // test how well it deals with trailing slash
  storage_service srv2{dir2.dir + "/", dir2};
  ASSERT_EQ(srv1.get_file_path(), "dir1/");
  ASSERT_EQ(srv2.get_file_path(), "dir2/");
  ASSERT_EQ(srv1.get_instance_name(), "dir1");
  ASSERT_EQ(srv2.get_instance_name(), "dir2");
  service_runner r1{srv1};
  service_runner r2{srv2};

  const auto com1 = communication::create(node, dir1);
  const auto com2 = communication::create(node, dir2);
  const auto db1 = com1->get_database(db_name1);
  const auto db2 = com2->get_database(db_name1);
  const auto store1 = db1->get_store<TestDataMessage>();
  const auto store2 = db2->get_store<TestDataMessage>();
  ASSERT_FALSE(db_file_exists(srv1.get_file_path() + db_name1));
  ASSERT_FALSE(db_file_exists(srv2.get_file_path() + db_name1));
  TestDataMessage msg;
  msg.data.int64_value = 1;
  store1->save(msg);
  msg.data.int64_value = 2;
  store2->save(msg);
  db1->commit();
  db2->commit();
  ASSERT_TRUE(db_file_exists(srv1.get_file_path() + db_name1));
  ASSERT_TRUE(db_file_exists(srv2.get_file_path() + db_name1));
  ASSERT_NO_THROW(r2.stop());
  ASSERT_NO_THROW(r1.stop());

  ASSERT_TRUE(test_if_value_in_db(srv1.get_file_path() + db_name1,
    common::get_data_store_name(msg_name, 0U)));
  ASSERT_TRUE(test_if_value_in_db(srv2.get_file_path() + db_name1,
    common::get_data_store_name(msg_name, 0U)));
}

TEST_F(test_storage_service, bad_request) {
  const auto msg_name = rosidl_generator_traits::name<TestDataMessage>();

  const auto control_srv = node->create_polling_client<Control>(
    common::get_service_name(""),
    rclcpp::DefaultQoS().keep_last(100));

  storage_service srv;
  service_runner r{srv};
  {
    Control::Request req;
    req.command_code = 255;  // does not exist
    assert_protocol_response_error(control_srv, req);
    req.command_code = common::commands::Registration;  // both msg type and db_name empty
    assert_protocol_response_error(control_srv, req);
    req.hdr.msg_type_name = msg_name;
    assert_protocol_response_error(control_srv, req);  // db name empty
    req.hdr.msg_type_name = "";
    req.hdr.db_name = db_name1;
    assert_protocol_response_error(control_srv, req);  // msg type empty
    req.hdr.msg_type_name = rosidl_generator_traits::name<BasicTypes>();
    assert_protocol_response_error(control_srv, req);  // unsupported message type
    req.command_code = common::commands::Commit;
    req.hdr.db_name = "";
    assert_protocol_response_error(control_srv, req);  // no db name
    req.hdr.db_name = "pretty_sure_no_such_file";
    assert_protocol_response_error(control_srv, req);  // bad db name
    req.command_code = common::commands::Save;
    req.hdr.db_name = db_name1;
    req.hdr.msg_type_name = msg_name;
    req.hdr.count = 1;
    assert_protocol_response_error(control_srv, req);  // no data sent
  }
  ASSERT_NO_THROW(r.stop());
}

TEST_F(test_storage_service, bad_instance_name) {
  ASSERT_THROW((storage_service{"", "*"}), apex::runtime_error);
}

TEST_F(test_storage_service, reset_run_state) {
  const auto msg_name = rosidl_generator_traits::name<TestDataMessage>();

  storage_service srv;
  service_runner r{srv};
  {
    const auto com = communication::create(node);
    const auto db = com->get_database(db_name1);
    const auto store1 = db->get_store<TestDataMessage>();
    const auto store2 = db->get_store<TestDataMessage>(1);
    const auto store3 = db->get_store<TestDataMessage2>();
    const auto store4 = db->get_store<TestDataMessage2>(1);
    ASSERT_EQ(srv.get_data_store_count(), 4U);
    ASSERT_EQ(srv.get_infra_count(), 2U);
    ASSERT_EQ(srv.get_database_count(), 1U);
  }
  ASSERT_NO_THROW(r.stop());
  ASSERT_NO_THROW(srv.reset_run_state());
  ASSERT_EQ(srv.get_data_store_count(), 0U);
  ASSERT_EQ(srv.get_infra_count(), 0U);
  ASSERT_EQ(srv.get_database_count(), 0U);
  service_runner r2{srv};

  const auto com = communication::create(node);
  const auto db = com->get_database(db_name2);
  const auto store = db->get_store<TestDataMessage>();
  ASSERT_EQ(srv.get_data_store_count(), 1U);
  ASSERT_EQ(srv.get_infra_count(), 1U);
  ASSERT_EQ(srv.get_database_count(), 1U);
  TestDataMessage msg;
  msg.data.int64_value = 42;
  store->save(msg);
  db->commit();
  ASSERT_NO_THROW(r.stop());
  ASSERT_TRUE(test_if_value_in_db(db_name2, common::get_data_store_name(msg_name, 0U)));
}

TEST_F(test_storage_service, drop) {
  storage_service srv;
  service_runner r{srv};
  {
    ASSERT_FALSE(db_file_exists(db_name1));
    const auto com = communication::create(node);
    const auto db = com->get_database(db_name1);
    const auto store1 = db->get_store<TestDataMessage>();
    const auto store2 = db->get_store<TestDataMessage2>();
    db->commit();
    ASSERT_FALSE(db_file_exists(db_name1));
    TestDataMessage msg;
    msg.data.int64_value = 42;
    store1->save(msg);
    store1->drop();
    db->commit();
    ASSERT_FALSE(db_file_exists(db_name1));
    store1->save(msg);
    db->drop();
    db->commit();
    ASSERT_FALSE(db_file_exists(db_name1));
    store1->save(msg);
    db->commit();
    ASSERT_TRUE(db_file_exists(db_name1));
    ASSERT_TRUE(store1->fetch(msg));
    TestDataMessage2 msg2;
    ASSERT_FALSE(store2->fetch(msg2));
    store1->drop();
    db->commit();
    ASSERT_TRUE(db_file_exists(db_name1));
    ASSERT_FALSE(store1->fetch(msg));
    ASSERT_FALSE(store2->fetch(msg2));
    db->drop();
    db->commit();
    ASSERT_FALSE(db_file_exists(db_name1));
    store1->save(msg);
    db->commit();
    ASSERT_TRUE(db_file_exists(db_name1));
    db->drop();
    store1->save(msg);
    ASSERT_TRUE(db_file_exists(db_name1));
    ASSERT_TRUE(store1->fetch(msg));
  }
  ASSERT_NO_THROW(r.stop());
}

TEST_F(test_storage_service, transient_messages) {
  {
    storage_service srv;
    service_runner r{srv};
    {
      const auto com = communication::create(node);
      const auto db = com->get_database(db_name1);
      const auto store1 = db->get_store<TestDataMessage>(0U, 9U);
      TestDataMessage msg;
      for (auto i = 0; i < 10; ++i) {
        msg.data.int64_value = i;
        if ((i % 2) == 0) {
          store1->save_transient(msg);
        } else {
          store1->save(msg);
        }
      }
      db->commit();
      std::array<TestDataMessage, 10> arr;
      ASSERT_EQ(store1->fetch_history(arr.begin(), arr.end()), arr.end());
    }
    ASSERT_NO_THROW(r.stop());
  }
  {
    storage_service srv;
    service_runner r{srv};
    {
      const auto com = communication::create(node);
      const auto db = com->get_database(db_name1);
      const auto store1 = db->get_store<TestDataMessage>(0U, 9U);
      std::array<TestDataMessage, 10> arr;
      ASSERT_EQ(store1->fetch_history(arr.begin(), arr.end()), arr.begin() + 5);
      std::int64_t j = 9;
      for (auto i = 0U; i < 5U; ++i, j -= 2) {
        ASSERT_EQ(arr[i].data.int64_value, j);
      }
    }
    ASSERT_NO_THROW(r.stop());
  }
}

TEST_F(test_storage_service, watches) {
  storage_service srv;
  service_runner r{srv};
  {
    std::int64_t values[10] = {0U};
    std::int64_t counters[10] = {0U};
    std::mutex m;
    std::condition_variable cv;

    const auto com = communication::create(node);
    const auto db1 = com->get_database(db_name1);
    const auto db2 = com->get_database(db_name2);
    std::vector<store<TestDataMessage>::shared_ptr> stores1(5);
    std::vector<store<TestDataMessage2>::shared_ptr> stores2(5);
    stores1[0] = db1->get_store<TestDataMessage>();
    stores1[1] = db1->get_store<TestDataMessage>();
    stores1[2] = db1->get_store<TestDataMessage>(1U);
    stores1[3] = db1->get_store<TestDataMessage>(2U);
    stores1[4] = db2->get_store<TestDataMessage>();
    stores2[0] = db1->get_store<TestDataMessage2>();
    stores2[1] = db1->get_store<TestDataMessage2>();
    stores2[2] = db1->get_store<TestDataMessage2>(1U);
    stores2[3] = db1->get_store<TestDataMessage2>(2U);
    stores2[4] = db2->get_store<TestDataMessage2>();
    ASSERT_EQ(stores1.size(), stores2.size());

    // add two watches per store while only second one signals the change
    // the order is important for the test
    for (auto i = 0U; i < stores1.size(); ++i) {
      // also makes sure by-value lambda compiles
      ASSERT_NO_THROW(stores1[i]->add_watch([&, i](TestDataMessage) {
          ++counters[i];
        }));
      ASSERT_NO_THROW(stores1[i]->add_watch([&, i](const TestDataMessage & msg) {
          ASSERT_EQ(msg.header.request_id.sequence_number, 0);
          ASSERT_TRUE(
            std::all_of(
              msg.header.request_id.writer_guid.begin(),
              msg.header.request_id.writer_guid.end(),
              [](auto v) {return v == 0;}
            )
          );
          std::unique_lock<std::mutex> l{m};
          values[i] = msg.data.int64_value;
          l.unlock();
          cv.notify_one();
        }));
      // also makes sure by-value lambda compiles
      ASSERT_NO_THROW(stores2[i]->add_watch([&, i](TestDataMessage2) {
          ++counters[5 + i];
        }));
      ASSERT_NO_THROW(stores2[i]->add_watch([&, i](const TestDataMessage2 & msg) {
          ASSERT_EQ(msg.header.request_id.sequence_number, 0);
          ASSERT_TRUE(
            std::all_of(
              msg.header.request_id.writer_guid.begin(),
              msg.header.request_id.writer_guid.end(),
              [](auto v) {return v == 0;}
            )
          );
          std::unique_lock<std::mutex> l{m};
          values[5 + i] = std::atoi(msg.data.string_value.c_str());
          l.unlock();
          cv.notify_one();
        }));
    }

    ASSERT_NO_THROW(com->start_watching());

    TestDataMessage msg;
    msg.data.int64_value = 42;
    std::unique_lock<std::mutex> l{m};

    stores1[0]->save(msg);
    {
      const std::int64_t expected_values[10] = {42, 42, 0, 0, 0, 0, 0, 0, 0, 0};
      ASSERT_TRUE(wait_for_state(values, expected_values, l, cv));
    }
    msg.data.int64_value = 43;
    stores1[1]->save(msg);
    {
      const std::int64_t expected_values[10] = {43, 43, 0, 0, 0, 0, 0, 0, 0, 0};
      ASSERT_TRUE(wait_for_state(values, expected_values, l, cv));
    }

    msg.data.int64_value = 44;
    stores1[2]->save(msg);
    {
      const std::int64_t expected_values[10] = {43, 43, 44, 0, 0, 0, 0, 0, 0, 0};
      ASSERT_TRUE(wait_for_state(values, expected_values, l, cv));
    }

    msg.data.int64_value = 45;
    stores1[3]->save(msg);
    {
      const std::int64_t expected_values[10] = {43, 43, 44, 45, 0, 0, 0, 0, 0, 0};
      ASSERT_TRUE(wait_for_state(values, expected_values, l, cv));
    }

    msg.data.int64_value = 47;
    stores1[4]->save(msg);
    {
      const std::int64_t expected_values[10] = {43, 43, 44, 45, 47, 0, 0, 0, 0, 0};
      ASSERT_TRUE(wait_for_state(values, expected_values, l, cv));
    }

    TestDataMessage2 msg2;
    msg2.data.string_value = "48";
    stores2[0]->save(msg2);
    {
      const std::int64_t expected_values[10] = {43, 43, 44, 45, 47, 48, 48, 0, 0, 0};
      ASSERT_TRUE(wait_for_state(values, expected_values, l, cv));
    }
    msg2.data.string_value = "49";
    stores2[1]->save(msg2);
    {
      const std::int64_t expected_values[10] = {43, 43, 44, 45, 47, 49, 49, 0, 0, 0};
      ASSERT_TRUE(wait_for_state(values, expected_values, l, cv));
    }

    msg2.data.string_value = "50";
    stores2[2]->save(msg2);
    {
      const std::int64_t expected_values[10] = {43, 43, 44, 45, 47, 49, 49, 50, 0, 0};
      ASSERT_TRUE(wait_for_state(values, expected_values, l, cv));
    }

    msg2.data.string_value = "51";
    stores2[3]->save(msg2);
    {
      const std::int64_t expected_values[10] = {43, 43, 44, 45, 47, 49, 49, 50, 51, 0};
      ASSERT_TRUE(wait_for_state(values, expected_values, l, cv));
    }

    msg2.data.string_value = "53";
    stores2[4]->save(msg2);
    {
      const std::int64_t expected_values[10] = {43, 43, 44, 45, 47, 49, 49, 50, 51, 53};
      ASSERT_TRUE(wait_for_state(values, expected_values, l, cv));
    }

    const std::int64_t expected_counters[10] = {2, 2, 1, 1, 1, 2, 2, 1, 1, 1};
    ASSERT_TRUE(std::equal(std::begin(counters), std::end(counters),
      std::begin(expected_counters)));
  }
  ASSERT_NO_THROW(r.stop());
}
