# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "storage_pkg",
    bin_executables = select({
        "@platforms//os:qnx": [
            ":message_storage_service",
            ":message_storage_cli",
        ],
        "//conditions:default": [
            ":message_storage_service",
            ":message_storage_cli",
            ":message_storage_web_server",
        ],
    }),
    cc_libraries = [
        ":storage",
    ],
    description = "Package containing API for working with message storage",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.2",
    visibility = ["//visibility:public"],
    deps = [
        "//common/configuration/settings:settings_pkg",
        "//common/containers:containers_pkg",
        "//common/cpputils:cpputils_pkg",
        "//common/interrupt:interrupt_pkg",
        "//common/threading:threading_pkg",
        "//grace/interfaces/storage_msgs:storage_msgs_pkg",
        "//grace/monitoring/logging:logging_pkg",
        "//grace/rmw_ida/typesupport:typesupport_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/ros/rcpputils:rcpputils_pkg",
        "//grace/ros/rmw:rmw_pkg",
        "//grace/rosidl/rosidl_runtime_c:rosidl_runtime_c_pkg",
        "//grace/rosidl/rosidl_typesupport_introspection_cpp:rosidl_typesupport_introspection_cpp_pkg",
        "@nlohmann_json//:nlohmann_json_pkg",
        "@unqlite//:unqlite_pkg",
    ],
)

storage_CERT_HDRS = [
    "include/storage/visibility.hpp",
    "include/storage/common/names.hpp",
    "include/storage/common/commands.hpp",
    "include/storage/common/request_id.hpp",
    "include/storage/common/traits.hpp",
    "include/storage/common/time.hpp",
    "include/storage/common/qos.hpp",
    "include/storage/api/parameters.hpp",
    "include/storage/api/storage.hpp",
    "include/storage/api/watch_callable.hpp",
    "include/storage/api/view.hpp",
]

storage_CERT_SRCS = [
    "src/axivion.cpp",
    "src/api/parameters.cpp",
    "src/api/storage.cpp",
]

storage_NON_CERT_SRCS = [
    "src/json_parser/from_message.cpp",
    "src/json_parser/to_message.cpp",
    "src/service/storage_service.cpp",
    "src/service/data_store.cpp",
    "src/service/database.cpp",
    "src/service/data_enumerator.cpp",
    "src/generic/ros_binary_message.cpp",
    "src/generic/msg_utils.cpp",
]

storage_NON_CERT_HDRS = [
    "include/storage/unqlitecpp/common.hpp",
    "include/storage/unqlitecpp/database.hpp",
    "include/storage/unqlitecpp/vm.hpp",
    "include/storage/unqlitecpp/scalar_value.hpp",
    "include/storage/unqlitecpp/key_value_store.hpp",
    "include/storage/unqlitecpp/cursor.hpp",
    "include/storage/unqlitecpp/transaction.hpp",
    "include/storage/json_parser/common.hpp",
    "include/storage/json_parser/conversions.hpp",
    "include/storage/json_parser/from_message.hpp",
    "include/storage/json_parser/to_message.hpp",
    "include/storage/common/file.hpp",
    "include/storage/generic/request_id.hpp",
    "include/storage/generic/msg_utils.hpp",
    "include/storage/generic/watch_callable.hpp",
    "include/storage/generic/storage.hpp",
    "include/storage/generic/dynamic_type_support.hpp",
    "include/storage/generic/ros_binary_message.hpp",
    "include/storage/service/request_error.hpp",
    "include/storage/service/data_store.hpp",
    "include/storage/service/database.hpp",
    "include/storage/service/storage_service.hpp",
    "include/storage/service/data_enumerator.hpp",
    "include/storage/ui/process_executor.hpp",
]

apex_cc_library(
    name = "storage",
    srcs = select({
        "//common/asil:d": storage_CERT_SRCS,
        "//common/asil:qm": storage_CERT_SRCS + storage_NON_CERT_SRCS,
    }),
    hdrs = select({
        "//common/asil:d": storage_CERT_HDRS,
        "//common/asil:qm": storage_CERT_HDRS + storage_NON_CERT_HDRS,
    }),
    applicable_licenses = ["//common/integrity:embedded_gold"],
    copts = select(
        {
            "@apex//common/platforms/cc_compiler_version:gcc11": ["-Wno-error=maybe-uninitialized"],
            "//conditions:default": [],
        },
    ),
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "//common/configuration/settings",
        "//common/containers",
        "//common/cpputils",
        "//common/interrupt",
        "//common/threading",
        "//grace/configuration/settings_extensions",
        "//grace/interfaces/storage_msgs",
        "//grace/monitoring/logging",
        "//grace/rmw_ida/typesupport:sample",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rcpputils",
        "//grace/ros/rmw",
        "//grace/rosidl/rosidl_runtime_c",
        "//grace/rosidl/rosidl_typesupport_introspection_cpp",
        "//grace/utils/apexcpp",
        "@nlohmann_json",
        "@unqlite",
    ],
)

cc_library(
    name = "message_storage_service_cli",
    srcs = ["src/ui/message_storage_service_cli.cpp"],
    hdrs = ["include/storage/ui/message_storage_service_cli.hpp"],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [":storage"],
)

cc_binary(
    name = "message_storage_service",
    srcs = ["src/ui/service_main.cpp"],
    applicable_licenses = ["//common/integrity:embedded_gold"],
    # TODO(kyle.marcey): #24903 Remove line below
    copts = ["-Wno-error"],
    tags = ["integrity QM"],
    visibility = ["//visibility:public"],
    deps = [":message_storage_service_cli"],
)

cc_binary(
    name = "message_storage_cli",
    srcs = [
        "src/ui/cli_main.cpp",
        "src/ui/process_executor.cpp",
    ],
    tags = ["integrity QM"],
    visibility = ["//visibility:public"],
    deps = [
        ":program_options",
        ":storage",
    ],
)

cc_library(
    name = "program_options",
    srcs = ["src/ui/program_options.cpp"],
    hdrs = [
        "include/storage/ui/program_options.hpp",
        "include/storage/visibility.hpp",
    ],
    strip_include_prefix = "include",
    target_compatible_with = select({
        "//common/asil:qm": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils",
    ],
)

cc_library(
    name = "web_api",
    srcs = ["src/web/api.cpp"],
    hdrs = [
        "include/storage/web/api.hpp",
        "include/storage/web/api_args.hpp",
        "include/storage/web/common.hpp",
        "include/storage/web/server_type.hpp",
    ],
    strip_include_prefix = "include",
    target_compatible_with = select({
        "//common/asil:qm": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
    visibility = ["//visibility:public"],
    deps = [
        ":storage",
        "//common/configuration/settings",
        "//common/cpputils",
        "//grace/ros/rclcpp/rclcpp",
        "@crow",
    ],
)

cc_binary(
    name = "message_storage_web_server",
    srcs = [
        "src/ui/web_main.cpp",
    ],
    target_compatible_with = select({
        "//common/asil:qm": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
    visibility = ["//visibility:public"],
    deps = [
        ":program_options",
        ":storage",
        ":web_api",
        "@crow",
    ],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
