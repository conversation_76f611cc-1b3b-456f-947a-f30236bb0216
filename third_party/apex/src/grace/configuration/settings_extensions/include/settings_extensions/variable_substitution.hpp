/// \copyright Copyright 2022 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file contains APIs for variables substitution in Apex.OS settings dictionaries

#ifndef SETTINGS_EXTENSIONS__VARIABLE_SUBSTITUTION_HPP_
#define SETTINGS_EXTENSIONS__VARIABLE_SUBSTITUTION_HPP_

#include <string>

#include "ament_index_cpp/get_package_prefix.hpp"
#include "ament_index_cpp/get_package_share_directory.hpp"
#include "ament_index_cpp/get_resources.hpp"
#include "cpputils/optional.hpp"
#include "rcpputils/get_env.hpp"
#include "settings/construct/types.hpp"
#include "settings_extensions/commands.hpp"
#include "settings_extensions/visibility.hpp"

/// \namespace apex
namespace apex
{
/// \namespace apex::settings_extensions
namespace settings_extensions
{

/// \brief Gets the package prefix path for a package name
/// \param command The command to handle
/// \return The package prefix path
/// \throw settings_error if the command format is wrong
std::string handle_find_pkg_prefix(const Command & command);

/// \brief Gets the package share path for a package name
/// \param command The command to handle
/// \return The package share path
/// \throw settings_error if the command format is wrong
std::string handle_find_pkg_share(const Command & tokens);

/// \brief Gets the full path for a given Bazel runfiles path
/// \param command The command to handle
/// \return The full path
/// \throw settings_error if the command format is wrong
std::string handle_rlocation(const Command & tokens);

/// \brief Gets the value of an environment variable
/// \param command The command to handle
/// \return The env variable value as a string
/// \throw settings_error if the command format is wrong
std::string handle_env_variable(const Command & tokens);

/// \brief Gets the output of a command as a string.
/// \param command The string command
/// \return The output of the command
/// \throw settings_error if the command format is wrong
std::string substitute_command(const std::string & command);

/// \brief Checks if the string contains built-in substitution commands
/// \param command The string command
/// \return true if the string contains built-in substitution commands
SETTINGS_EXTENSIONS_PUBLIC
bool is_command_substitution(const std::string & command);

/// \brief Gets the output of a string containing multiple built-in substitution commands
/// \param str The input string
/// \return The output of the string after performing all the substitutions
/// \throw settings_error if the command format is wrong
SETTINGS_EXTENSIONS_PUBLIC
apex::optional<std::string> process_substitution_commands(const std::string & str);

}  // namespace settings_extensions
}  // namespace apex

#endif  // SETTINGS_EXTENSIONS__VARIABLE_SUBSTITUTION_HPP_
