load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "settings_extensions_pkg",
    cc_libraries = [":settings_extensions"],
    description = "Package containing API to extend settings repository",
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/configuration/settings:settings_pkg",
        "//common/containers:containers_pkg",
        "//common/cpputils:cpputils_pkg",
        "//grace/ros/rcpputils:rcpputils_pkg",
        "//tools/ament/ament_index/ament_index_cpp:ament_index_cpp_pkg",
        "@yaml-cpp//:yaml-cpp_pkg",
    ],
)

apex_cc_library(
    name = "settings_extensions",
    srcs = glob(
        ["src/**"],
        exclude = ["src/axivion.cpp"],
    ),
    hdrs = glob([
        "include/settings_extensions/**",
    ]),
    applicable_licenses = ["//common/integrity:embedded_gold"],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "//common/asil:only_qm",
        "//common/bazel/rules_deployment/configured_env:runfiles",
        "//common/configuration/settings",
        "//common/containers",
        "//common/cpputils",
        "//grace/ros/rcpputils",
        "//tools/ament/ament_index/ament_index_cpp",
        "@yaml-cpp//:yaml-cpp",
    ],
)

filegroup(
    name = "api_files",
    srcs = glob(["include/**/*.hpp"]),
    visibility = [":__subpackages__"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
