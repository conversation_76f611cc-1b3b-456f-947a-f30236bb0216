cmake_minimum_required(VERSION 3.5)

project(settings_extensions)

find_package(ament_cmake_auto REQUIRED)

ament_auto_find_build_dependencies()

set(settings_extensions_SRCS
    include/settings_extensions/visibility.hpp
    include/settings_extensions/variable_substitution.hpp
    include/settings_extensions/commands.hpp
    include/settings_extensions/transform_dictionary.hpp
    include/settings_extensions/include_dictionary.hpp
    include/settings_extensions/merge_dictionary.hpp
    include/settings_extensions/from_yaml.hpp
    include/settings_extensions/value_conversion.hpp
    src/variable_substitution.cpp
    src/commands.cpp
    src/include_dictionary.cpp
    src/merge_dictionary.cpp
    src/transform_dictionary.cpp
    src/from_yaml.cpp
    src/value_conversion.cpp
)

ament_auto_add_library(settings_extensions ${settings_extensions_SRCS})

apex_set_compile_options(settings_extensions)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  list(APPEND AMENT_LINT_AUTO_EXCLUDE ament_cmake_uncrustify)
  find_package(apexutils REQUIRED)
  ament_lint_auto_find_test_dependencies()

  find_package(ament_cmake_gtest)
  find_package(yaml_cpp_vendor REQUIRED)
  find_package(yaml-cpp REQUIRED)

  set(settings_extensions_tests_SRCS
    test/test_commands.cpp
    test/test_variable_substitution.cpp
    test/test_transform_dictionary.cpp
    test/test_include_dictionary.cpp
    test/test_merge_dictionary.cpp
    test/test_include_yaml.cpp
    test/test_common.hpp
    test/test_common.cpp
    test/test_doc_examples.cpp
    test/test_value_conversion.cpp
  )

  # Fix this as part of 32661
  set_property(SOURCE test/test_include_dictionary.cpp APPEND PROPERTY COMPILE_OPTIONS -Wno-error=old-style-cast)

  ament_add_gtest(settings_extensions_tests
    ${settings_extensions_tests_SRCS}
    RUNFILES "${CMAKE_SOURCE_DIR}")
  ament_target_dependencies(settings_extensions_tests
      settings
      yaml_cpp_vendor
      yaml-cpp
  )
  target_link_libraries(settings_extensions_tests ${PROJECT_NAME} yaml-cpp::yaml-cpp)
  target_compile_definitions(settings_extensions_tests PRIVATE
      CMAKE_SOURCE_DIR="${CMAKE_SOURCE_DIR}"
  )

  apex_set_compile_options(settings_extensions_tests)
endif()

ament_auto_package(
  INSTALL_TO_SHARE
)
