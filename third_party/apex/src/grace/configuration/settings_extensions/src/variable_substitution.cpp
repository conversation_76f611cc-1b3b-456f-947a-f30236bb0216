/// \copyright Copyright 2022 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file contains APIs to replace built-in substitution commands

#include "settings_extensions/variable_substitution.hpp"

#include <regex>
#include <string>
#include <unordered_map>

#include "configured_env/runfiles/apex.hpp"
#include "settings_extensions/commands.hpp"


/// \namespace apex
namespace apex
{
/// \namespace apex::settings_extensions
namespace settings_extensions
{

using apex::settings::settings_error;

/// \namespace
namespace
{
using CommandFunc = std::function<std::string(const Command &)>;
static const std::unordered_map<std::string, CommandFunc> SubstitutionCommandHandlers = {
  {"find-pkg-prefix", &handle_find_pkg_prefix},
  {"find-pkg-share", &handle_find_pkg_share},
  {"rlocation", &handle_rlocation},
  {"env", &handle_env_variable},
};

}  // namespace

std::string handle_find_pkg_prefix(const Command & command)
{
  if (command.second.size() != 1U) {
    throw settings_error("'find-pkg-prefix' expects 1 arguments, got ", command.second.size());
  }

  const auto & package_name = command.second.at(0);
  return ament_index_cpp::get_package_prefix(package_name);
}

std::string handle_find_pkg_share(const Command & command)
{
  if (command.second.size() != 1U) {
    throw settings_error("'find-pkg-share' expects 1 arguments, got ", command.second.size());
  }

  const auto & package_name = command.second.at(0);
  return ament_index_cpp::get_package_share_directory(package_name);
}

std::string handle_rlocation(const Command & command)
{
  if (command.second.size() != 1U) {
    throw settings_error("'rlocation' expects 1 arguments, got ", command.second.size());
  }

  const auto & runfiles_path = command.second.at(0);
  const auto apex_runfiles = apex::configured_env::runfiles::ApexRunfiles();
  return apex_runfiles.Rlocation(runfiles_path);
}

std::string handle_env_variable(const Command & command)
{
  if (command.second.size() < 1U || command.second.size() > 2U) {
    throw settings_error("'env' expects 1 or 2 arguments, got ", command.second.size());
  }

  std::string default_value;
  if (command.second.size() == 2) {
    default_value = command.second.at(1);
  }

  const auto & env_variable_name = command.second.at(0);
  std::string env_var = rcpputils::get_env_var(env_variable_name.c_str());
  if (env_var.empty()) {
    if (default_value.empty()) {
      throw settings_error(
        "'env' variable '", env_variable_name, "' does not exist and there is no default value");
    } else {
      env_var = default_value;
    }
  }
  return env_var;
}

bool is_command_substitution(const std::string & command)
{
  std::string retval{command};

  auto tokens = get_command_content_as_tokens(command, false);
  if (tokens.empty()) {
    return false;
  }

  auto command_handler = SubstitutionCommandHandlers.find(tokens.at(0));
  return command_handler != SubstitutionCommandHandlers.end();
}

std::string substitute_command(const std::string & str)
{
  std::string retval{str};
  auto command = get_command(str);

  auto command_handler = SubstitutionCommandHandlers.find(command.first);
  if (command_handler != SubstitutionCommandHandlers.end()) {
    retval = command_handler->second(command);
  }
  return retval;
}

apex::optional<std::string> process_substitution_commands(const std::string & str)
{
  // this expression matches the first command with the expected format
  // blah $(cmd1 arg) blah $(cmd2 arg) -> $(cmd1 arg)
  static const std::regex rgx(R"(\$\(([^\)\n]*)\)+)");
  std::smatch matches;
  std::string converted{};
  std::string pending{str};

  // commands are matched and replaced in sequence
  while (regex_search(pending, matches, rgx)) {
    const auto command = matches.str();
    const auto prefix = matches.prefix().str();
    const auto suffix = matches.suffix().str();

    converted += prefix;
    converted += substitute_command(command);
    pending = suffix;
  }

  apex::optional<std::string> result;
  if (!converted.empty()) {
    result = converted + pending;
  }

  return result;
}

}  // namespace settings_extensions
}  // namespace apex
