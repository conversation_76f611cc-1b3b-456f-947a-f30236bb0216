// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#ifndef SETTINGS_EXTENSIONS__TEST_COMMON_HPP_
#define SETTINGS_EXTENSIONS__TEST_COMMON_HPP_

#include <gtest/gtest.h>
#include <string>
#include <list>
#include "settings/inspect.hpp"
#include "settings/construct.hpp"
#include "settings/to_yaml.hpp"

namespace test_common
{

constexpr const char * Deep1Yaml{
  CMAKE_SOURCE_DIR "/test/prefix1/share/foo/config/deep1.yaml"};
constexpr const char * Array1Yaml{
  CMAKE_SOURCE_DIR "/test/prefix1/share/foo/config/array1.yaml"};
constexpr const char * Array2Yaml{
  CMAKE_SOURCE_DIR "/test/prefix1/share/foo/config/array2.yaml"};
constexpr const char * StringYaml{
  CMAKE_SOURCE_DIR "/test/prefix1/share/foo/config/string.yaml"};

constexpr const char * FirstYaml{CMAKE_SOURCE_DIR "/test/prefix1/share/foo/config/first.yaml"};
constexpr const char * FourthYaml{CMAKE_SOURCE_DIR "/test/prefix1/share/foo/config/fourth.yaml"};

constexpr const char * CONFIG1{CMAKE_SOURCE_DIR "/test/config/config1.yaml"};
constexpr const char * CONFIG2{CMAKE_SOURCE_DIR "/test/config/config2.yaml"};
constexpr const char * CONFIG3{CMAKE_SOURCE_DIR "/test/config/config3.yaml"};
constexpr const char * CONFIG4{CMAKE_SOURCE_DIR "/test/config/config4.yaml"};
constexpr const char * CONFIG5{CMAKE_SOURCE_DIR "/test/config/config5.yaml"};

constexpr const char * TEST_CONFIG1{CMAKE_SOURCE_DIR "/test/config/test_config1.yaml"};
constexpr const char * TEST_CONFIG3{CMAKE_SOURCE_DIR "/test/config/test_config3.yaml"};
constexpr const char * TEST_CONFIG4{CMAKE_SOURCE_DIR "/test/config/test_config4.yaml"};
constexpr const char * TEST_CONFIG5{CMAKE_SOURCE_DIR "/test/config/test_config5.yaml"};

std::string generate_subfolder_path(std::string subfolder);

void set_ament_prefix_path(std::list<std::string> subfolders);

void set_environment_variables();

void set_yaml_path_environment_variables();

void dictionary_checker(
  apex::settings::construct::dictionary target,
  apex::settings::construct::dictionary expected);


class test_variable_substitution : public ::testing::Test
{
public:
  void SetUp() override
  {
    test_common::set_ament_prefix_path({"prefix1"});
    test_common::set_environment_variables();
  }
};

}  // namespace test_common

#endif  // SETTINGS_EXTENSIONS__TEST_COMMON_HPP_
