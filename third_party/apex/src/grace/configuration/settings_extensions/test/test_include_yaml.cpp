// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>
#include <string>
#include <list>

#include "test_common.hpp"
#include "settings_extensions/transform_dictionary.hpp"
#include "settings/construct.hpp"
#include "settings/from_yaml.hpp"


using apex::settings::settings_error;
namespace yaml = apex::settings::yaml;
using apex::settings::construct::dictionary;
using apex::settings::construct::array;
using apex::settings::construct::string;
using apex::settings::construct::boolean;
using apex::settings::construct::string;
using apex::settings::construct::integer;
using apex::settings::construct::floating;
using apex::settings::construct::get;
using apex::settings::construct::make_value;

namespace
{
class test_include_yaml : public ::testing::Test
{
public:
  void SetUp() override
  {
    test_common::set_ament_prefix_path({"prefix1"});

    test_common::set_environment_variables();
    test_common::set_yaml_path_environment_variables();
  }
};
}  // namespace


TEST_F(test_include_yaml, complex1) {
  dictionary dict_target;
  dictionary dict_expected;

  apex::settings::yaml::from_file(test_common::TEST_CONFIG1, dict_target);
  apex::settings_extensions::transform_dictionary(dict_target);

  apex::settings::yaml::from_file(test_common::CONFIG1, dict_expected);

  test_common::dictionary_checker(dict_target, dict_expected);

  EXPECT_EQ(get<floating>(dict_target, "first/name1"), get<floating>(dict_expected, "first/name1"));
  EXPECT_EQ(get<boolean>(dict_target, "first/name2"), get<boolean>(dict_expected, "first/name2"));
  EXPECT_EQ(get<integer>(dict_target, "second"), get<integer>(dict_expected, "second"));
  EXPECT_EQ(get<std::string>(dict_target, "third/deep1/deep2"),
    get<std::string>(dict_expected, "third/deep1/deep2"));
  EXPECT_EQ(get<array>(dict_target, "fourth/deep1"), get<array>(dict_expected, "fourth/deep1"));
  EXPECT_EQ(get<array>(dict_target, "fifth"), get<array>(dict_expected, "fifth"));
}

TEST_F(test_include_yaml, test_include_in_array) {
  dictionary dict_target;
  dictionary dict_expected;

  apex::settings::yaml::from_file(test_common::TEST_CONFIG3, dict_target);
  apex::settings_extensions::transform_dictionary(dict_target);

  apex::settings::yaml::from_file(test_common::CONFIG3, dict_expected);

  test_common::dictionary_checker(dict_target, dict_expected);

  EXPECT_EQ(get<floating>(dict_target, "first/name1"), get<floating>(dict_expected, "first/name1"));
  EXPECT_EQ(get<boolean>(dict_target, "first/name2"), get<boolean>(dict_expected, "first/name2"));
  EXPECT_EQ(get<integer>(dict_target, "second"), get<integer>(dict_expected, "second"));
  EXPECT_EQ(get<std::string>(dict_target, "third/deep1/deep2"),
    get<std::string>(dict_expected, "third/deep1/deep2"));
  EXPECT_EQ(get<array>(dict_target, "fourth/deep1"), get<array>(dict_expected, "fourth/deep1"));
  EXPECT_EQ(get<array>(dict_target, "fifth"), get<array>(dict_expected, "fifth"));
  EXPECT_EQ(get<std::string>(dict_target, "sixth/deep1/deep2"),
    get<std::string>(dict_expected, "sixth/deep1/deep2"));
}

TEST_F(test_include_yaml, test_typed_values) {
  dictionary dict_target;
  dictionary dict_expected;

  apex::settings::yaml::from_file(test_common::TEST_CONFIG4, dict_target);
  apex::settings_extensions::transform_dictionary(dict_target);

  apex::settings::yaml::from_file(test_common::CONFIG4, dict_expected);

  test_common::dictionary_checker(dict_target, dict_expected);
}

TEST_F(test_include_yaml, test_explicit_deduction_is_enforced) {
  dictionary dict_deduced;
  dictionary dict_raw;

  apex::settings::yaml::from_file(test_common::TEST_CONFIG4, dict_deduced);
  apex::settings_extensions::transform_dictionary(dict_deduced);
  apex::settings::yaml::from_file(test_common::TEST_CONFIG4, dict_raw);
  apex::settings_extensions::transform_dictionary(dict_raw, true);

  test_common::dictionary_checker(dict_deduced, dict_raw);
}

TEST_F(test_include_yaml, complex5) {
  dictionary dict_target;
  dictionary dict_expected;

  apex::settings::yaml::from_file(test_common::TEST_CONFIG5, dict_target);
  apex::settings_extensions::transform_dictionary(dict_target);

  apex::settings::yaml::from_file(test_common::CONFIG5, dict_expected);

  test_common::dictionary_checker(dict_target, dict_expected);

  EXPECT_EQ(get<floating>(dict_target, "first/name1"), get<floating>(dict_expected, "first/name1"));
  EXPECT_EQ(get<boolean>(dict_target, "first/name2"), get<boolean>(dict_expected, "first/name2"));
  EXPECT_EQ(get<integer>(dict_target, "second"), get<integer>(dict_expected, "second"));
  EXPECT_EQ(get<std::string>(dict_target, "third/deep1/deep2"),
    get<std::string>(dict_expected, "third/deep1/deep2"));
  EXPECT_EQ(get<array>(dict_target, "fourth/deep1"), get<array>(dict_expected, "fourth/deep1"));
  EXPECT_EQ(get<array>(dict_target, "fifth"), get<array>(dict_expected, "fifth"));
}
