first : "$(include $(rlocation apex/grace/configuration/settings_extensions/test/prefix1/share/foo/config/first.yaml) first)"
second : "$(env INT_VAR)"
third: "$(include $(rlocation apex/grace/configuration/settings_extensions/test/prefix1/share/foo/config/string.yaml))"
fourth:
  deep1: [1, 2, 3, "$(include $(rlocation apex/grace/configuration/settings_extensions/test/prefix1/share/foo/config/array2.yaml) deep1)"]
fifth: "$(include $(rlocation apex/grace/configuration/settings_extensions/test/prefix1/share/foo/config/array1.yaml) deep1/deep2)"
