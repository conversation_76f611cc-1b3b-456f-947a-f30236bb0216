load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_pkg//:mappings.bzl", "pkg_files")

ros_pkg(
    name = "process_manager_pkg",
    cc_libraries = [
        ":graph_lib",
        ":config_lib",
        ":process_manager_lib",
        ":introspection_lib",
        ":minimal_process",
        ":dummy_process",
    ],
    description = "Package containing Apex process manager",
    lib_executables = [
        ":process_manager",
        ":introspection",
    ],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/configuration/settings:settings_pkg",
        "//common/cpputils:cpputils_pkg",
        "//common/interrupt:interrupt_pkg",
        "//common/process:process_pkg",
        "//common/threading:threading_pkg",
        "//grace/execution/apex_init:apex_init_pkg",
        "//grace/execution/executor2:executor2_pkg",
        "//grace/interfaces/process_manager_interfaces:process_manager_interfaces_pkg",
        "//grace/monitoring/event:event_pkg",
        "//grace/monitoring/event_registry:event_registry_pkg",
        "//grace/monitoring/logging:logging_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/ros/rcpputils:rcpputils_pkg",
    ],
)

filegroup(
    name = "doc_files",
    srcs = glob(["param/**"]),
    visibility = [":__subpackages__"],
)

filegroup(
    name = "api_files",
    srcs = glob(["include/**/*.hpp"]),
    visibility = [":__subpackages__"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)

cc_library(
    name = "graph_lib",
    srcs = [
        "src/graph.cpp",
    ],
    hdrs = [
        "include/process_manager/graph.hpp",
        "include/process_manager/visibility.hpp",
    ],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils",
    ],
)

cc_library(
    name = "config_lib",
    srcs = glob(
        ["src/config/**"],
    ),
    hdrs = glob(
        ["include/process_manager/config/**"],
    ),
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        ":graph_lib",
        "//common/configuration/settings",
        "//common/cpputils",
        "//common/process",
        "//grace/execution/apex_init",
        "//grace/interfaces/process_manager_interfaces",
        "//grace/interfaces/process_manager_interfaces:process_manager_common",
        "//grace/monitoring/event",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rcpputils",
    ],
)

cc_library(
    name = "process_manager_lib",
    srcs = glob(
        [
            "src/action/*",
            "src/process_group/*",
            "src/process_manager/*",
        ],
    ),
    hdrs = glob(
        [
            "include/process_manager/action/*",
            "include/process_manager/process_group/*",
            "include/process_manager/process_manager/*",
        ],
    ),
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        ":config_lib",
        "//common/configuration/settings",
        "//common/cpputils",
        "//common/interrupt",
        "//common/process",
        "//common/threading",
        "//grace/execution/executor2",
        "//grace/interfaces/process_manager_interfaces",
        "//grace/monitoring/event",
        "//grace/monitoring/event_registry",
        "//grace/monitoring/logging",
    ],
)

cc_library(
    name = "introspection_lib",
    srcs = glob(
        ["src/introspection/*"],
    ),
    hdrs = glob(
        ["include/process_manager/introspection/*"],
    ),
    strip_include_prefix = "include",
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":config_lib",
        ":graph_lib",
        "//grace/configuration/settings_extensions",
    ],
)

pkg_files(
    name = "default_essential_config",
    srcs = ["param/rmw_ida/default_essential.launch.yaml"],
    prefix = "param",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "process_manager",
    srcs = ["src/main.cpp"] + select({
        "//common/asil:d": [
            "param/process_manager_settings.cpp",
            "param/process_manager_settings.hpp",
        ],
        "//conditions:default": [],
    }),
    applicable_licenses = ["//common/integrity:embedded_gold"],
    visibility = ["//visibility:public"],
    deps = [
        ":config_lib",
        ":graph_lib",
        ":process_manager_lib",
        "//common/configuration/settings",
        "//common/interrupt",
        "//grace/execution/apex_init:apex_main",
        "@coverage_tool//:coverage_io_lib",
    ],
)

alias(
    name = "apex_process_manager",
    actual = ":process_manager",
    deprecation = "Please use just `@apex//grace/configuration/process_manager` instead (without the :apex_process_manager)",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "introspection",
    srcs = ["src/ui/cli_introspection_main.cpp"],
    tags = [
        "exclude_sca",
        "skip_coverage",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":config_lib",
        ":graph_lib",
        ":introspection_lib",
        "//grace/configuration/settings_extensions",
        "@coverage_tool//:coverage_io_lib",
    ],
)

cc_binary(
    name = "minimal_process",
    srcs = ["src/executables/minimal_process.cpp"],
    tags = [
        "exclude_sca",
        "skip_coverage",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils",
        "//common/interrupt",
        "//grace/configuration/process_manager:config_lib",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/interfaces/process_manager_interfaces",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "dummy_process",
    srcs = ["src/executables/dummy_process.cpp"],
    tags = [
        "exclude_sca",
        "skip_coverage",
    ],
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "get_status",
    srcs = ["src/ui/cli_get_status_main.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils",
        "//common/interrupt",
        "//grace/configuration/process_manager:config_lib",
        "//grace/execution/apex_init",
        "//grace/interfaces/process_manager_interfaces",
        "@apex//grace/ros/rcutils",
    ],
)

cc_binary(
    name = "change_state",
    srcs = ["src/ui/cli_change_state_main.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils",
        "//common/interrupt",
        "//grace/configuration/process_manager:config_lib",
        "//grace/execution/apex_init",
        "//grace/interfaces/process_manager_interfaces",
        "@apex//grace/ros/rcutils",
    ],
)

cc_binary(
    name = "restart",
    srcs = ["src/ui/cli_restart_main.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils",
        "//common/interrupt",
        "//grace/configuration/process_manager:config_lib",
        "//grace/execution/apex_init",
        "//grace/interfaces/process_manager_interfaces",
        "@apex//grace/ros/rcutils",
    ],
)
