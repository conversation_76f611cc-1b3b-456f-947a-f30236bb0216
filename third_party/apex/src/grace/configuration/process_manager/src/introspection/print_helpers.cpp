/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#include <string>
#include <unordered_map>

#include "process_manager/introspection/print_helper.hpp"

/// \namespace apex
namespace apex
{
/// \namespace apex::process_manager
namespace process_manager
{
/// \namespace apex::process_manager::introspection
namespace introspection
{

std::ostream & operator<<(std::ostream & stream, const ExecutionStateReportType & report_type)
{
  switch (report_type) {
    case ExecutionStateReportType::None:
      stream << "None";
      break;
    case ExecutionStateReportType::ByStdoutStream:
      stream << "ByStdoutStream";
      break;
    case ExecutionStateReportType::ByStderrStream:
      stream << "ByStderrStream";
      break;
    case ExecutionStateReportType::ByTopic:
      stream << "ByTopic";
      break;
    default:
      stream << "UnknownReportType";
      break;
  }
  return stream;
}

std::ostream & operator<<(std::ostream & stream, const logging::LogLevel & log_level)
{
  switch (log_level) {
    case logging::LogLevel::TRACE:
      stream << "TRACE";
      break;
    case logging::LogLevel::DEBUG:
      stream << "DEBUG";
      break;
    case logging::LogLevel::INFO:
      stream << "INFO";
      break;
    case logging::LogLevel::WARN:
      stream << "WARN";
      break;
    case logging::LogLevel::ERROR:
      stream << "ERROR";
      break;
    case logging::LogLevel::FATAL:
      stream << "FATAL";
      break;
    default:
      stream << "Unknown";
      break;
  }
  return stream;
}

std::string to_string(const OnFailureBehavior & behavior)
{
  switch (behavior) {
    case OnFailureBehavior::DoNothing:
      return "DoNothing";
    case OnFailureBehavior::StopAllProcesses:
      return "StopAllProcesses";
    case OnFailureBehavior::Fatal:
      return "Fatal";
    default:
      return "Unknown";
  }
}

std::ostream & operator<<(std::ostream & stream, const OnFailureBehavior & behavior)
{
  return stream << to_string(behavior);
}

std::ostream & operator<<(std::ostream & stream, const IndexGraph & graph)
{
  stream << "graph TD\n";
  for (auto i = 0U; i < graph.get_node_count(); i++) {
    for (auto dest : graph.get_neighbors(i)) {
      stream << "    n" << i << "--> n" << dest << "\n";
    }
    if (graph.get_neighbors(i).empty()) {
      stream << "    n" << i << "\n";
    }
  }

  return stream;
}

std::string to_string(const StreamRedirection & redirection)
{
  switch (redirection) {
    case StreamRedirection::DoNotRedirect:
      return "DoNotRedirect";
    case StreamRedirection::RedirectToPipe:
      return "RedirectToProcessManager";
    case StreamRedirection::RedirectToDevNull:
      return "RedirectToDevNull";
    case StreamRedirection::RedirectToLogFile:
      return "RedirectToLogFile";
    default:
      return "Unknown";
  }
}

std::ostream & operator<<(std::ostream & stream, const StreamRedirection & redirection)
{
  return stream << to_string(redirection);
}

std::ostream & operator<<(std::ostream & stream, const StreamLoggingHandler & handler)
{
  switch (handler) {
    case StreamLoggingHandler::Console:
      stream << "Console";
      break;
    case StreamLoggingHandler::LogFile:
      stream << "LogFile";
      break;
    case StreamLoggingHandler::LogTopic:
      stream << "LogTopic";
      break;
    case StreamLoggingHandler::LogFileCombined:
      stream << "LogFileCombined";
      break;
    case StreamLoggingHandler::ProcessGroupState:
      stream << "ProcessGroupState";
      break;
    default:
      stream << "UnknownHandler";
      break;
  }
  return stream;
}

std::ostream & operator<<(std::ostream & stream, const StartupConfig & config)
{
  stream << "Command:\n\n";
  stream << "```shell\n";
  stream << config.get_path();
  const auto args = config.get_args();
  for (const auto & arg : config.get_args()) {
    stream << " " << arg;
  }
  stream << "\n```" << std::endl;

  const auto env_vars = config.get_env_vars();
  if (!env_vars.empty()) {
    stream << "\nEnv variables:\n\n";
    stream << "```shell\n";
    for (const auto & env_var : config.get_env_vars()) {
      stream << env_var.first << "=" << env_var.second << std::endl;
    }
    stream << "```" << std::endl;
  }

  return stream;
}

std::ostream & operator<<(std::ostream & stream, const ProcessDependencyGraph & graph)
{
  auto items = graph.get_items();
  auto index_graph = graph.get_index_graph();

  stream << "graph TD\n";
  for (auto origin = 0U; origin < items.size(); origin++) {
    for (auto dest : index_graph.get_neighbors(origin)) {
      stream << "    n" << origin << "(" << items[origin].first << ")"
             << " --> n" << dest << "(" << items[dest].first << ")\n";
    }
    if (index_graph.get_neighbors(origin).empty()) {
      stream << "    n" << origin << "(" << items[origin].first << ")\n";
    }
  }

  return stream;
}

std::ostream & operator<<(std::ostream & stream, const State & state)
{
  stream << "##### Process list\n\n";
  if (state.processes.empty()) {
    stream << "None\n\n";
  } else {
    for (const auto & process : state.processes) {
      stream << "###### Process: " << process.first << "\n\n";
      stream << process.second << std::endl;
    }
  }

  stream << "##### Process startup dependencies\n\n";
  if ((state.startup_dependencies == apex::nullopt) || state.startup_dependencies->size() == 0) {
    stream << "Empty" << std::endl;
  } else {
    stream << "```mermaid\n";
    stream << *state.startup_dependencies;
    stream << "```\n";
  }
  stream << std::endl;

  stream << "##### Process shutdown dependencies\n\n";
  if ((state.shutdown_dependencies == apex::nullopt) || state.shutdown_dependencies->size() == 0) {
    stream << "Empty" << std::endl;
  } else {
    stream << "```mermaid\n";
    stream << *state.shutdown_dependencies;
    stream << "```\n";
  }

  return stream;
}

std::ostream & operator<<(std::ostream & stream, const Action & action)
{
  auto process_name = action.process_name;
  if (action.type == ActionType::Start) {
    if (action.is_self_terminating) {
      stream << "StartAndWaitTerm";
    } else {
      stream << "Start";
    }
  } else if (action.type == ActionType::Stop) {
    stream << "Stop";
  } else {
    stream << "NoAction";
  }
  stream << " " << process_name;
  return stream;
}

std::ostream & operator<<(std::ostream & stream, const ActionGraph & graph)
{
  auto items = graph.get_items();
  auto index_graph = graph.get_index_graph();

  stream << "graph TD\n";
  for (auto origin = 0U; origin < items.size(); origin++) {
    for (auto dest : index_graph.get_neighbors(origin)) {
      stream << "    n" << origin << "(" << items[origin] << ")"
             << " --> n" << dest << "(" << items[dest] << ")\n";
    }
    if (index_graph.get_neighbors(origin).empty()) {
      stream << "    n" << origin << "(" << items[origin] << ")\n";
    }
  }

  return stream;
}

std::ostream & operator<<(std::ostream & stream, const ActionGraphTable & action_graph_table)
{
  for (const auto & origin : action_graph_table) {
    for (const auto & target : origin.second) {
      stream << "\n" << origin.first << " --> " << target.first << "\n\n";
      stream << "```mermaid\n";
      stream << target.second;
      stream << "```\n";
    }
  }
  return stream;
}

std::ostream & operator<<(std::ostream & stream, const ProcessConfig & config)
{
  stream << "```yaml\n";
  stream << "Is self-terminating: " << std::boolalpha << config.is_self_terminating << "\n";
  stream << "Report type: " << config.report_type << "\n";
  stream << "Startup timeout: " << config.startup_timeout.count() << "ms\n";
  stream << "SIGTERM timeout: " << config.sigterm_timeout.count() << "ms\n";
  stream << "SIGKILL timeout: " << config.sigkill_timeout.count() << "ms\n";
  stream << "Stdout stream logging: ";
  stream << "[";
  bool first = true;
  for (const auto & item : config.stdout_stream_logging) {
    if (!first) {
      stream << ", ";
    }
    stream << item;
    first = false;
  }
  stream << "]\n";
  stream << "Stderr stream logging: ";
  stream << "[";
  first = true;
  for (const auto & item : config.stderr_stream_logging) {
    if (!first) {
      stream << ", ";
    }
    stream << item;
    first = false;
  }
  stream << "]\n";
  stream << "Allow SIGTERM exit: " << std::boolalpha << config.allow_sigterm_exit << "\n";
  stream << "Stream redirection: " << config.stream_redirection << "\n";
  stream << "```";

  return stream;
}

std::ostream & operator<<(std::ostream & stream, const ProcessGroupConfig & config)
{
  stream << "## Process group: " << config.group_name << "\n\n";
  stream << "```yaml\n";
  stream << "process manager instance: \"" << config.process_manager_instance << "\"\n";
  stream << "init group state: " << config.init_group_state << "\n";
  stream << "log file buffering: " << std::to_string(config.log_file_buffering) << "\n";
  stream << "group liveliness config: { is_enabled: "
         << std::to_string(config.group_liveliness_config.is_enabled)
         << ", thread_liveliness_heartbeat_period: "
         << config.group_liveliness_config.thread_liveliness_heartbeat_period.count()
         << "ms, liveliness_check_period: "
         << config.group_liveliness_config.liveliness_check_period.count() << "ms }\n";
  stream << "log file enable: " << std::to_string(config.log_file_enable) << "\n";
  stream << "log directory: \"" << config.log_directory << "\"\n";
  stream << "log file buffering: " << std::to_string(config.log_file_buffering) << "\n";
  stream << "log level: " << config.log_level << "\n";
  stream << "min publish interval: " << config.min_publish_interval.count() << "ms\n";
  stream << "on failure behavior: " << config.on_failure_behavior << "\n";
  stream << "group log handlers: ";
  stream << "[";
  bool first = true;
  for (const auto & item : config.group_log_handlers) {
    if (!first) {
      stream << ", ";
    }
    stream << item;
    first = false;
  }
  stream << "]\n";
  stream << "wait for dispatcher: " << config.wait_for_dispatcher.count() << "ns\n";

  stream << "```\n" << std::endl;

  stream << "### Processes\n" << std::endl;
  for (const auto & process : config.processes) {
    stream << "#### Process: " << process.first << std::endl;
    stream << "\n" << process.second << "\n" << std::endl;
  }

  stream << "### States\n" << std::endl;
  for (const auto & state : config.states) {
    stream << "#### State: " << state.first << std::endl;
    stream << std::endl;
    stream << state.second;
    stream << std::endl;
  }
  stream << "### State transition action sequence\n";
  stream << config.action_graph_table;
  return stream;
}

std::ostream & operator<<(std::ostream & stream, const ProcessManagerConfig & config)
{
  stream << "# Process Manager Configuration\n\n";
  stream << "```yaml\n";
  stream << "Name: " << config.name << "\n";
  stream << "Log directory: " << config.log_directory << "\n";
  stream << "Log file buffering: " << std::boolalpha << config.log_file_buffering << "\n";
  stream << "Append timestamp to log file: " << std::boolalpha
         << config.append_timestamp_to_log_file << "\n";
  stream << "Log file enable: " << std::boolalpha << config.log_file_enable << "\n";
  stream << "Max simultaneous requests: " << config.max_simultaneous_requests << "\n";
  stream << "Wait for dispatcher: " << config.wait_for_dispatcher.count() << "ns\n";

  if (config.heartbeat_monitor_period.has_value()) {
    stream << "Heartbeat monitor period: " << config.heartbeat_monitor_period->count() << "ms\n";
  } else {
    stream << "Heartbeat monitor period: Not set\n";
  }
  stream << "```" << std::endl;

  return stream;
}

}  // namespace introspection
}  // namespace process_manager
}  // namespace apex
