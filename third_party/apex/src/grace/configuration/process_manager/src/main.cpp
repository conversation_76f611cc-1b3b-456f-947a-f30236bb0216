/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#include <chrono>
#include <string>

#include "apex_init/apex_main.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "logging/logging_macros.hpp"
#include "process/process.hpp"
#include "process_manager/process_manager/process_manager_launch.hpp"
#include "string/string.hpp"


int32_t apex_main(const int32_t argc, char ** const argv)
{
  const apex::interrupt_handler::installer interrupt_handler_installer{};
// In Non cert version, rt settings loaded from the command line(if exists) overrides
// rt settings loaded from builder functions
#ifndef APEX_CERT
  // load the user settings from command line(if exists)
  apex::settings_extensions::yaml::load_repository_from_command_line(argc, argv);
#endif  // APEX_CERT

  const auto settings = apex::settings::repository::get();
  const auto config =
    apex::process_manager::config::get_process_manager_config(settings, argc, argv);
  if (config.log_file_enable) {
    create_log_directories(config);
  }
  auto process_manager_launch = apex::process_manager::ProcessManagerLaunch(config, argc, argv);
  apex::interrupt_handler::wait();
  process_manager_launch.shutdown();
  return 0;
}
