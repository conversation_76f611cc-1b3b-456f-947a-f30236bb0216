/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.

#include "process_manager/process_manager/process_manager_node.hpp"

#include "process_manager/process_manager/group_thread_liveliness_monitor.hpp"

namespace
{
std::vector<std::shared_ptr<apex::process_manager::process_group::ProcessGroup>>
create_process_group_vector(
  const std::vector<std::shared_ptr<apex::process_manager::process_group::ProcessGroupNode>> &
    process_group_nodes,
  std::shared_ptr<apex::process_manager::process_group::EssentialGroup> essential_group = nullptr)
{
  std::vector<std::shared_ptr<apex::process_manager::process_group::ProcessGroup>> process_groups;
  const std::size_t group_size =
    essential_group ? process_group_nodes.size() + 1 : process_group_nodes.size();
  process_groups.reserve(group_size);

  for (const auto & group : process_group_nodes) {
    process_groups.push_back(group->get_process_group());
  }
  if (essential_group) {
    process_groups.push_back(essential_group->get_process_group());
  }

  return process_groups;
}
}  // namespace

/// \namespace apex
namespace apex
{
/// \namespace apex::process_manager
namespace process_manager
{

ProcessManagerNode::ProcessManagerNode(
  const ProcessManagerConfig & config,
  std::shared_ptr<process_group::EssentialGroup> essential_group)
: m_config{config},
  m_timer_service{},
  m_process_manager{create_process_manager(config, m_timer_service)},
  m_node{std::make_shared<rclcpp::Node>(config.name, config.process_manager_instance)},
  m_logger{m_node.get(), config.name},
  m_service_forwarder_node{std::make_shared<ServiceForwarderNode>(
    config, m_node, m_process_manager->get_process_group_nodes(), config.process_manager_instance)},
  m_process_execution_state_monitor{std::make_shared<ProcessExecutionStateMonitor>(
    m_node, create_process_group_vector(m_process_manager->get_process_group_nodes()))},
  m_heartbeat{m_config.heartbeat_monitor_period.has_value()
                ? std::make_shared<Heartbeat>(
                    config,
                    m_node,
                    create_process_group_vector(m_process_manager->get_process_group_nodes(),
                                                essential_group),
                    m_timer_service)
                : nullptr},
  m_group_thread_liveliness_monitor{
    m_config.group_liveliness_config.is_enabled
      ? std::make_shared<GroupThreadLivelinessMonitor>(
          config,
          m_node,
          create_process_group_vector(m_process_manager->get_process_group_nodes(),
                                      essential_group),
          m_timer_service)
      : nullptr},
  m_group_event_forwarder(std::make_shared<GroupEventForwarder>(config, m_node)),
  m_executor{apex::executor::executor_factory::create()}
{
  for (const auto & group_config : m_config.process_groups) {
    if (group_config.group_log_handlers.empty()) {
      APEX_WARN(m_logger,
                apex::no_separator{},
                "Log handlers for group '",
                group_config.group_name,
                "' are empty, ",
                "no log will be generated for this group");
    }
  }
  (void)m_executor->add(m_service_forwarder_node);
  (void)m_executor->add(m_process_execution_state_monitor);

  m_runner = std::make_unique<apex::executor::executor_runner>(
    apex::executor::executor_runner::interrupt | apex::executor::executor_runner::deferred,
    *m_executor);
}

void ProcessManagerNode::start()
{
  m_process_manager->enable_remote_state_transitions(false);
  m_runner->issue();
  m_process_manager->start_framework_group();
  // enable events only if the wait for dispatcher is enabled
  if (m_config.wait_for_dispatcher > std::chrono::seconds(0)) {
    enable_events();
  }
  m_process_manager->start_process_groups();
  m_process_manager->enable_remote_state_transitions(true);
}

void ProcessManagerNode::stop()
{
  m_process_manager->enable_remote_state_transitions(false);
  m_process_manager->stop();
  m_runner->stop();
}

void ProcessManagerNode::enable_events()
{
  m_monitored_process.emplace(m_config.wait_for_dispatcher);
  m_event_sender_executor = apex::executor::executor_factory::create(m_monitored_process.value());
  if (m_heartbeat) {
    (void)m_event_sender_executor->add(m_heartbeat);
  }
  if (m_group_thread_liveliness_monitor) {
    (void)m_event_sender_executor->add(m_group_thread_liveliness_monitor);
  }
  (void)m_event_sender_executor->add(m_group_event_forwarder);
  m_event_sender_runner = std::make_unique<apex::executor::executor_runner>(
    apex::executor::executor_runner::interrupt, *m_event_sender_executor);
  APEX_INFO(m_logger, "Process Manager events enabled");
}

}  // namespace process_manager
}  // namespace apex
