
/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#include "process_manager/process_group/event_monitor.hpp"

#include <sys/ioctl.h>

#include <algorithm>
#include <memory>
#include <string>
#include <utility>

/// \namespace apex
namespace apex
{
/// \namespace apex::process_manager
namespace process_manager
{
/// \namespace apex::process_manager::process_group
namespace process_group
{

namespace
{

bool is_enabled(const rclcpp::dynamic_waitset::posix_pollfd & fd)
{
  return fd.fd != -1;
}

bool is_fd_event(const rclcpp::dynamic_waitset::posix_pollfd & fd)
{
  // AXIVION Next Codeline MisraC++2023-7.0.4, MisraC++2023-7.0.5  : POSIX way
  return (fd.revents != 0);
}

bool is_read_event(const rclcpp::dynamic_waitset::posix_pollfd & fd)
{
  /*
   AXIVION Next Codeline MisraC++2023-7.0.5 : Reason: Code Quality (Functional suitability),
   Justification: Allow violation for POSIX idiomatic use case
   */
  return ((fd.revents & POLLIN) != 0);
}

bool is_error_event(const rclcpp::dynamic_waitset::posix_pollfd & fd)
{
  /*
   AXIVION Next Codeline MisraC++2023-7.0.5 : Reason: Code Quality (Functional suitability),
   Justification: Allow violation for POSIX idiomatic use case
   */
  return ((fd.revents & (POLLERR | POLLNVAL)) != 0);
}

bool is_closed_event(const rclcpp::dynamic_waitset::posix_pollfd & fd)
{
  /*
   AXIVION Next Codeline MisraC++2023-7.0.5 : Reason: Code Quality (Functional suitability),
   Justification: Allow violation for POSIX idiomatic use case
   */
  return ((fd.revents & POLLHUP) != 0);
}

bool is_stdout_reporting_event(const ProcessExecutionState & state,
                               StreamType stream_type,
                               const ProcessConfig & config)
{
  if (state != ProcessExecutionState::STARTING) {
    return false;
  } else {
    return (config.report_type == ExecutionStateReportType::ByStdoutStream) &&
           (stream_type == StreamType::Stdout);
  }
}

bool is_stderr_reporting_event(const ProcessExecutionState & state,
                               StreamType stream_type,
                               const ProcessConfig & config)
{
  if (state != ProcessExecutionState::STARTING) {
    return false;
  } else {
    return (config.report_type == ExecutionStateReportType::ByStderrStream) &&
           (stream_type == StreamType::Stderr);
  }
}
}  // namespace

EventMonitor::EventMonitor(const ProcessGroupConfig & config,
                           std::shared_ptr<ProcessGroup> process_group,
                           rclcpp::Node::SharedPtr node)
: m_config{config},
  m_process_group{std::move(process_group)},
  m_num_process{m_process_group->get_num_processes()},
  m_exit_collector{std::make_shared<ExitCodeCollector>(m_process_group)},
  m_worker_thread{[this] { run(); }},
  m_process_stream_logger{
    node ? std::make_optional<apex::logging::ContextualLogger<>>(node.get(), config.group_name)
         : std::nullopt},
  m_event_pub(node && (config.wait_for_dispatcher > std::chrono::seconds(0))
                ? node->create_publisher<dispatcher_interfaces::msg::Event>(
                    "/" + std::string(common::EVENT_FORWARDING_TOPIC_NAME),
                    apex::dispatcher::common::get_event_qos())
                : nullptr)
{
  auto it = m_process_group->get_processes().begin();
  for (std::size_t idx = 0U; idx < m_num_process; ++idx) {
    m_process_name_map.insert({idx, it->first});
    it++;
  }

  initialize_polling_descriptors();
  start_monitoring();
}

EventMonitor::~EventMonitor()
{
  call_and_log_exceptions("stopping event monitor", [this]() { stop_monitoring(); });
}

void EventMonitor::initialize_polling_descriptors()
{
  // each process has three descriptors (stdout, stderr, termination) + 4 for the event communicator
  const auto event_communicator_fd_offset = size_t{3} * m_num_process;
  m_poll_fds.resize(event_communicator_fd_offset + size_t{4});

  for (auto & fd : m_poll_fds) {
    fd.fd = -1;  // by default all descriptors are ignored
    fd.events = POLLIN;
    fd.revents = 0;
  }

  m_process_starting_event_idx = event_communicator_fd_offset;
  m_process_started_event_idx = event_communicator_fd_offset + size_t{1};
  m_process_terminated_event_idx = event_communicator_fd_offset + size_t{2};
  m_stop_monitoring_idx = event_communicator_fd_offset + size_t{3};

  m_poll_fds[m_process_starting_event_idx] =
    *m_process_group->get_event_communicator()->process_starting_event.get_read_end();
  m_poll_fds[m_process_started_event_idx] =
    *m_process_group->get_event_communicator()->process_started_event.get_read_end();
  m_poll_fds[m_process_terminated_event_idx] =
    *m_process_group->get_event_communicator()->process_terminated_event.get_read_end();
  m_poll_fds[m_stop_monitoring_idx] =
    *m_process_group->get_event_communicator()->stop_monitoring.get_read_end();
}

pollfd & EventMonitor::get_stream_pollfd(size_t process_index, StreamType stream_type)
{
  const auto stream_offset = (stream_type == StreamType::Stdout) ? process::Process::cout_offset
                                                                 : process::Process::cerr_offset;
  return m_poll_fds.at((process_index * size_t{3}) + stream_offset);
}

pollfd & EventMonitor::get_termination_pollfd(size_t process_index)
{
  const auto termination_offset = size_t{2};
  return m_poll_fds.at((process_index * size_t{3}) + termination_offset);
}

void EventMonitor::start_monitoring()
{
  GROUP_LOG_INFO(m_process_group, "Starting to monitor processes...");
  m_worker_thread.issue();
  m_exit_collector->start_monitoring();
}

void EventMonitor::stop_monitoring()
{
  m_exit_collector->stop_monitoring();
  if (m_worker_thread.joinable()) {
    GROUP_LOG_INFO(m_process_group, "Stopping monitoring processes...");
    m_process_group->get_event_communicator()->stop_monitoring.trigger();
    m_worker_thread.join();
  }
}

void EventMonitor::run()
{
  try {
    while (!m_stop) {
      poll_events();
      handle_events();
      report_liveliness();
    }
    // General catch is okay since it re-throws.
  } catch (const std::exception & e) {
    GROUP_LOG_FATAL(m_process_group, "Unexpected error ", e.what());
    apex::interrupt_handler::trigger();
    throw;
    // General catch is okay since it re-throws.
  } catch (...) {
    GROUP_LOG_FATAL(m_process_group, "Unexpected error ");
    apex::interrupt_handler::trigger();
    throw;
  }
}

void EventMonitor::report_liveliness() const
{
  auto & liveliness_monitor = m_process_group->get_liveliness_monitor();
  if (m_config.group_liveliness_config.is_enabled) {
    liveliness_monitor.heartbeat();
  }
}

void EventMonitor::poll_events()
{
  // if thread liveliness monitor is enabled, use the heartbeat period as timeout,
  // otherwise there is no timeout
  const std::int32_t timeout =
    m_config.group_liveliness_config.is_enabled
      ? static_cast<std::int32_t>(
          m_config.group_liveliness_config.thread_liveliness_heartbeat_period.count())
      : -1;

  /*lint -e{716}*/  // not an infinite loop
  while (true) {
    /*
     AXIVION Next Construct MisraC++2023-7.0.5: Reason: Code Quality
     (Functional suitability), Justification: double cast to prevent
     either "useless cast" on linux or a "type mismatch" on QNX
     */
    const auto res = ::poll(m_poll_fds.data(),
                            static_cast<nfds_t>(static_cast<std::int64_t>(m_poll_fds.size())),
                            timeout);
    if (res == -1) {
      if (errno != EINTR) {
        throw apex::system_error{errno};
      }
    } else {
      break;
    }
  }
}

void EventMonitor::handle_events()
{
  handle_starting_event();
  handle_started_event();
  handle_terminated_event();
  handle_stop_event();
  handle_stream_events();
  handle_termination_pipe_events();
}

void EventMonitor::handle_starting_event()
{
  auto & starting_pollfd = m_poll_fds[m_process_starting_event_idx];
  if (is_read_event(starting_pollfd)) {
    m_process_group->get_event_communicator()->process_starting_event.clear();
    on_starting_event();
    starting_pollfd.revents = 0;
  }
}

void EventMonitor::handle_started_event()
{
  auto & started_pollfd = m_poll_fds[m_process_started_event_idx];
  if (is_read_event(started_pollfd)) {
    m_process_group->get_event_communicator()->process_started_event.clear();
    // TODO(carlos): make use of this event
    //  on_started_event();
    started_pollfd.revents = 0;
  }
}

void EventMonitor::handle_terminated_event()
{
  auto & terminated_pollfd = m_poll_fds[m_process_terminated_event_idx];
  if (is_read_event(terminated_pollfd)) {
    m_process_group->get_event_communicator()->process_terminated_event.clear();
    on_terminated_event();
    terminated_pollfd.revents = 0;
  }
}

void EventMonitor::handle_stop_event()
{
  auto & stop_pollfd = m_poll_fds[m_stop_monitoring_idx];
  if (is_read_event(stop_pollfd)) {
    m_process_group->get_event_communicator()->stop_monitoring.clear();
    m_stop = true;
  }
}

void EventMonitor::handle_termination_pipe_events()
{
  for (std::size_t i = 0U; i < m_num_process; ++i) {
    auto & termination_pollfd = get_termination_pollfd(i);
    if (is_enabled(termination_pollfd) && is_fd_event(termination_pollfd)) {
      if (check_pipe_closed(termination_pollfd)) {
        const auto process = m_process_group->get_process(m_process_name_map[i]);
        remove_termination_fd_from_polling(i);
        handle_pipe_closed_event(process, i);
      }
      termination_pollfd.revents = 0;
    }
  }
}

bool EventMonitor::check_pipe_closed(const rclcpp::dynamic_waitset::posix_pollfd & pollfd)
{
  if (is_error_event(pollfd)) {
    throw std::runtime_error{"Unexpected error on a descriptor"};
  }

  // POLLHUP means the pipe was closed, but it is not guaranteed by the POSIX standard
  if (pollfd.revents & POLLHUP) {
    // The pipe was closed from the other end
    return true;
  }

  // Perform a dummy read to check if the pipe was closed with EOF
  // This is a fallback way to check if the pipe was closed

  // TODO(carlos): check if this is necessary, we were using this a approach to check if the pipe
  // was closed when reading from stream pipe because POLLHUP was not set on QNX if there was data
  // remaining in the pipe. This is not the case anymore because the termination pipe does
  // not contain any data
  char buffer[1];
  ssize_t result = ::read(pollfd.fd, buffer, 1);  // Perform a dummy read

  if (result == 0) {  // read returns 0 (EOF) when the pipe is closed
    // The pipe was closed from the other end
    return true;
  }
  if (result == -1 && errno != EAGAIN && errno != EWOULDBLOCK) {
    throw std::runtime_error{"Error checking pipe status"};
  }
  return false;
}

void EventMonitor::handle_stream_events()
{
  for (std::size_t process_index = 0U; process_index < m_num_process; ++process_index) {
    handle_process_stream_event(process_index, StreamType::Stdout);
    handle_process_stream_event(process_index, StreamType::Stderr);
  }
}

void EventMonitor::handle_process_stream_event(std::size_t process_index, StreamType stream_type)
{
  auto & pollfd = get_stream_pollfd(process_index, stream_type);
  if (is_enabled(pollfd)) {
    const auto process = m_process_group->get_process(m_process_name_map[process_index]);
    if (is_read_event(pollfd)) {
      handle_stream_read_event(process_index, stream_type);
    }
    if (is_error_event(pollfd)) {
      handle_stream_error_event(process);
      pollfd.fd = -1;
    }
    if (is_closed_event(pollfd)) {
      remove_stream_fds_from_polling(process_index, stream_type);
      handle_pipe_closed_event(process, process_index);
    }

    pollfd.revents = 0;
  }
}

void EventMonitor::handle_stream_read_event(std::size_t process_index, StreamType stream_type)
{
  const auto process = m_process_group->get_process(m_process_name_map[process_index]);
  const auto state = process->get_state();
  const auto config = process->get_config();
  const auto offset = (stream_type == StreamType::Stdout) ? process::Process::cout_offset
                                                          : process::Process::cerr_offset;

  std::int32_t bytes_available{0};
  const auto retval = ioctl(process->get_stream_fd(offset), FIONREAD, &bytes_available);
  if (retval < 0) {
    GROUP_LOG_WARN(m_process_group,
                   apex::no_separator{},
                   "got error ",
                   errno,
                   " reading available stream bytes in process ",
                   process_to_log_msg(process->get_name(), process->get_pid()));
    return;
  }

  // On QNX when the pipe is closed POLLHUP is set and the number of available bytes is 0
  // We detect this case by reading and checking EOF (0) is returned by read()
  if (bytes_available >= 0) {
    // Only if there was data available we can report the process as running
    if (bytes_available > 0) {
      if (is_stdout_reporting_event(state, stream_type, config) ||
          is_stderr_reporting_event(state, stream_type, config)) {
        process->set_is_running_reported(true);
        m_process_group->get_event_communicator()->process_starting_event.trigger();
      }
    }
    read_from_fd(process_index, stream_type, bytes_available);
  } else {
    GROUP_LOG_WARN(m_process_group,
                   apex::no_separator{},
                   "Unexpected number of available bytes to read '",
                   bytes_available,
                   "' in process: ",
                   process_to_log_msg(process->get_name(), process->get_pid()));
  }
}

// TODO(carlos): move reading to a separate working thread to log asynchronously
void EventMonitor::read_from_fd(std::size_t process_index,
                                StreamType stream_type,
                                std::int32_t bytes_to_read)
{
  const auto process = m_process_group->get_process(m_process_name_map[process_index]);
  const auto config = process->get_config();
  const auto offset = (stream_type == StreamType::Stdout) ? process::Process::cout_offset
                                                          : process::Process::cerr_offset;
  std::int32_t total_bytes_read{0U};

  // read until we read the specified bytes amount or there is no more data
  while (true) {
    stream_buffer buffer;
    const auto bytes_read =
      ::read(process->get_stream_fd(offset), buffer.data(), stream_buffer::capacity());
    if (bytes_read > 0) {
      log_streams(process, config, buffer, stream_type);
      total_bytes_read += apex::cast::safe_cast<int32_t>(bytes_read);
      // we have read enough bytes, let's return even if there is new data coming
      // to avoid blocking the main loop
      if (total_bytes_read >= bytes_to_read) {
        break;
      }
    } else {
      if ((bytes_read == 0) || (errno == EAGAIN)) {
        // According to the POSIX standard if a pipe is closed, read returns 0 to indicate
        // end-of-file This is the portable way to detect process termination We set POLLHUP to
        // indicate an exit event
        auto & pollfd = get_stream_pollfd(process_index, stream_type);
        // AXIVION Next Codeline MisraC++2023-7.0.4, MisraC++2023-7.0.5  : POSIX way
        pollfd.revents |= POLLHUP;
        break;
      }

      if (errno != EINTR) {
        GROUP_LOG_WARN(m_process_group,
                       apex::no_separator{},
                       "Got error '",
                       errno,
                       "' reading from process ",
                       process_to_log_msg(process->get_name(), process->get_pid()));
        break;
      }
    }
  }
}

void EventMonitor::log_streams(const std::shared_ptr<Process> & process,
                               const ProcessConfig & config,
                               const EventMonitor::stream_buffer & buffer,
                               StreamType stream_type) const
{
  const auto name = process->get_name();
  const auto & logging_handlers = (stream_type == StreamType::Stdout)
                                    ? config.stdout_stream_logging
                                    : config.stderr_stream_logging;
  // log stream buffer to topic
  if (logging_handlers.find(StreamLoggingHandler::LogTopic) != logging_handlers.end()) {
    const auto severity = (stream_type == StreamType::Stdout) ? apex::logging::LogLevel::INFO
                                                              : apex::logging::LogLevel::ERROR;

    // the content of the buffer is split in separate messages and published to the contextual
    // logger
    std::size_t pos = 0U;
    while (pos < buffer.size()) {
      std::size_t len = std::min(string256_t::capacity(), buffer.size() - pos);
      publish_log_message(name, severity, string256_t(buffer.substr(pos, len)));
      pos += len;
    }
  }

  // log stream buffer to console
  if (logging_handlers.find(StreamLoggingHandler::Console) != logging_handlers.end()) {
#ifndef APEX_CERT
    if (stream_type == StreamType::Stdout) {
      std::cout << buffer;
    } else {
      std::cerr << buffer;
    }
#endif  // APEX_CERT
  }

  // log stream buffer to file
  if (logging_handlers.find(StreamLoggingHandler::LogFile) != logging_handlers.end()) {
    auto & file_stream = stream_type == StreamType::Stdout ? process->get_stdout_file_stream()
                                                           : process->get_stderr_file_stream();
    if (file_stream.is_open()) {
      file_stream << buffer;
    }
  }
  // log stream to a combined file for stdout and stderr
  if (logging_handlers.find(StreamLoggingHandler::LogFileCombined) != logging_handlers.end()) {
    auto & file_stream_combined = process->get_process_file_stream();
    if (file_stream_combined.is_open()) {
      file_stream_combined << buffer;
    }
  }
}

void EventMonitor::publish_log_message(const ProcessName & name,
                                       apex::logging::LogLevel severity,
                                       const apex::string256_t & str_data) const
{
  if (m_process_stream_logger.has_value()) {
    const apex::logging::ContextualLogger<>::IntCtxT context = {
      std::make_pair("process", [&name] { return apex::logging::create_ctx_data(name); }),
    };
    m_process_stream_logger->log_message_with_context(severity, 0, "", context, str_data);
  }
}

void EventMonitor::handle_stream_error_event(const std::shared_ptr<Process> & process)
{
  const auto name = process->get_name();
  const auto pid = process->get_pid();
  GROUP_LOG_FATAL(m_process_group,
                  apex::no_separator{},
                  "Unexpected error on a descriptor for process ",
                  process_to_log_msg(name, pid));
  m_process_group->set_error(ProcessGroupError::FATAL);
  process->set_error(ProcessError::UNEXPECTED_TERMINATION);
}

void EventMonitor::handle_pipe_closed_event(const std::shared_ptr<Process> & process,
                                            std::size_t process_index)
{
  auto & stdout_pollfd = get_stream_pollfd(process_index, StreamType::Stdout);
  auto & stderr_pollfd = get_stream_pollfd(process_index, StreamType::Stderr);
  auto & termination_pollfd = get_termination_pollfd(process_index);

  // we start monitoring for process termination once all the pipes have been closed (if they were
  // enabled) otherwise the process could be restarted while there is still streams to read and they
  // will be lost
  if (!is_enabled(stdout_pollfd) && !is_enabled(stderr_pollfd) && !is_enabled(termination_pollfd)) {
    const auto & process_name = process->get_name();
    m_exit_collector->add_process(process_name);
  }
}

void EventMonitor::remove_stream_fds_from_polling(std::size_t process_index, StreamType stream_type)
{
  auto & pollfd = get_stream_pollfd(process_index, stream_type);
  pollfd.fd = -1;
}

void EventMonitor::remove_termination_fd_from_polling(std::size_t process_index)
{
  auto & termination_pollfd = get_termination_pollfd(process_index);
  termination_pollfd.fd = -1;
}

void EventMonitor::setup_process_poll_fd(std::size_t process_index)
{
  const auto & process_name = m_process_name_map[process_index];
  const auto process = m_process_group->get_process(process_name);
  auto & stdout_pollfd = get_stream_pollfd(process_index, StreamType::Stdout);
  auto & stderr_pollfd = get_stream_pollfd(process_index, StreamType::Stderr);
  // if the stream pipes were not created, the file descriptors are set to -1
  // hence the process streams are not monitored anyway
  stdout_pollfd.fd = process->get_stream_fd(process::Process::cout_offset);
  stdout_pollfd.events = POLLIN;
  stdout_pollfd.revents = 0;
  stderr_pollfd.fd = process->get_stream_fd(process::Process::cerr_offset);
  stderr_pollfd.events = POLLIN;
  stderr_pollfd.revents = 0;
  auto & termination_pollfd = get_termination_pollfd(process_index);
  termination_pollfd.fd = process->get_termination_fd();
  assert(is_enabled(termination_pollfd));
  termination_pollfd.events = POLLIN;
  termination_pollfd.revents = 0;
}

bool EventMonitor::are_stream_poll_fd_enabled(std::size_t process_index)
{
  auto & stdout_pollfd = get_stream_pollfd(process_index, StreamType::Stdout);
  auto & stderr_pollfd = get_stream_pollfd(process_index, StreamType::Stderr);
  return is_enabled(stdout_pollfd) && is_enabled(stderr_pollfd);
}

bool EventMonitor::is_process_poll_fd_enabled(std::size_t process_index)
{
  return is_enabled(get_termination_pollfd(process_index));
}

void EventMonitor::on_starting_event()
{
  for (std::size_t i = 0U; i < m_num_process; ++i) {
    const auto & process_name = m_process_name_map[i];
    const auto process = m_process_group->get_process(process_name);
    if (process->get_state() == ProcessExecutionState::STARTING) {
      if (!is_process_poll_fd_enabled(i)) {
        setup_process_poll_fd(i);
      }
      // non-reporting processes are set to RUNNING when we start monitoring their fds
      if (process->get_config().report_type == ExecutionStateReportType::None) {
        process->set_is_running_reported(true);
      }
      on_execution_report_event(process);
    }
  }
}

void EventMonitor::on_terminated_event()
{
  for (std::size_t i = 0U; i < m_num_process; ++i) {
    const auto & process_name = m_process_name_map[i];
    if (m_exit_collector->is_exit_code_collected(process_name)) {
      m_exit_collector->remove_process(process_name);
      const auto process = m_process_group->get_process(process_name);
      handle_terminated_process(process);
      process->set_state(ProcessExecutionState::TERMINATED);
    }
  }

  if (!m_process_group->is_any_processes_running()) {
    m_process_group->set_all_process_are_terminated(true);
  }
}

void EventMonitor::on_execution_report_event(const std::shared_ptr<Process> & process)
{
  if (process->is_running_reported() && (process->get_error() == ProcessError::OK)) {
    const auto & name = process->get_name();
    const auto pid = process->get_pid();
    GROUP_LOG_DEBUG(
      m_process_group, apex::no_separator{}, "Process RUNNING: ", process_to_log_msg(name, pid));
    process->set_state(ProcessExecutionState::RUNNING);
    m_process_group->get_event_communicator()->publish_report.trigger();
  }
}

void EventMonitor::on_termination_error(const std::shared_ptr<Process> & process,
                                        bool is_unexpected_termination)
{
  using event::types::int_data_t;
  const auto has_exit_code = process->has_exit_code();
  const auto has_exit_signal = process->has_exit_signal();
  const auto exit_code = has_exit_code ? process->get_exit_code() : 0;
  const auto exit_signal = has_exit_signal ? process->get_exit_signal() : 0;
  const auto name = process->get_name();
  const auto pid = process->get_pid();

  apex::string_strict256_t exit_type = has_exit_code ? "code" : "signal";
  std::int32_t code = has_exit_code ? exit_code : exit_signal;
  GROUP_LOG_ERROR(m_process_group,
                  apex::no_separator{},
                  "Error: Process ",
                  process_to_log_msg(name, pid),
                  " terminated with exit ",
                  exit_type.data(),
                  " '",
                  code,
                  "'");

  process->set_error(ProcessError::UNEXPECTED_TERMINATION);
  m_process_group->set_error();

  if (m_event_pub) {
    const auto is_graceful_termination = has_exit_code && (exit_code == 0);
    const auto pid_as_int64 = cast::safe_cast<int_data_t>(pid);
    if (is_unexpected_termination) {
      if (is_graceful_termination) {
        const auto ev = m_config.is_framework_group
                          ? apex::event::ProcessUnexpectedGracefulExitFrameworkGroup
                          : apex::event::ProcessUnexpectedGracefulExit;
        send_event(ev, pid_as_int64, name);
      } else {
        const auto ev = m_config.is_framework_group
                          ? apex::event::ProcessUnexpectedAbnormalExitFrameworkGroup
                          : apex::event::ProcessUnexpectedAbnormalExit;
        send_event(ev, pid_as_int64, name);
      }
    } else {
      send_event(apex::event::ProcessAbnormalExit, pid_as_int64, name);
    }
  }
  if (m_config.is_essential_group) {
    const auto message = apex::varargs_to_string(
      "Unexpected process termination in essential group:", process_to_log_msg(name, pid));
    base::panic(message.data());
  }
}

void EventMonitor::handle_terminated_process(const std::shared_ptr<Process> & process)
{
  const auto has_exit_code = process->has_exit_code();
  const auto has_exit_signal = process->has_exit_signal();
  const auto exit_code = has_exit_code ? process->get_exit_code() : 0;
  const auto exit_signal = has_exit_signal ? process->get_exit_signal() : 0;
  const auto name = process->get_name();
  const auto state = process->get_state();
  const auto pid = process->get_pid();
  bool is_unexpected_termination{false};
  bool is_termination_error{false};

  switch (state) {
    case ProcessExecutionState::TERMINATING:
      if (has_exit_code && (exit_code == EXIT_SUCCESS)) {
        GROUP_LOG_DEBUG(m_process_group,
                        apex::no_separator{},
                        "Normal exit for process ",
                        process_to_log_msg(name, pid));
      } else if (process->get_config().allow_sigterm_exit && has_exit_signal &&
                 (exit_signal == SIGTERM)) {
        GROUP_LOG_DEBUG(m_process_group,
                        apex::no_separator{},
                        "Normal exit for process ",
                        process_to_log_msg(name, pid),
                        " with exit signal '",
                        exit_signal,
                        "'");
      } else {
        is_termination_error = true;
      }
      break;
    case ProcessExecutionState::STARTING:
    case ProcessExecutionState::RUNNING:
      if (process->get_config().is_self_terminating && has_exit_code &&
          (exit_code == EXIT_SUCCESS)) {
        GROUP_LOG_DEBUG(m_process_group,
                        apex::no_separator{},
                        "Normal exit for self-terminating process ",
                        process_to_log_msg(name, pid));
      } else {
        is_unexpected_termination = true;
        is_termination_error = true;
      }
      break;
    default:
      GROUP_LOG_ERROR(m_process_group,
                      apex::no_separator{},
                      "Process termination in unexpected state for process: ",
                      process_to_log_msg(name, pid));
      break;
  }

  if (is_termination_error) {
    on_termination_error(process, is_unexpected_termination);
  }
}

}  // namespace process_group
}  // namespace process_manager
}  // namespace apex
