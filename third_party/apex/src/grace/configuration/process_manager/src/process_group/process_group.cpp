/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#include "process_manager/process_group/process_group.hpp"

#include <map>
#include <memory>
#include <set>

/// \namespace apex
namespace apex
{
/// \namespace apex::process_manager
namespace process_manager
{
/// \namespace apex::process_manager::process_group
namespace process_group
{
namespace
{

std::map<ProcessName, std::shared_ptr<Process>> create_processes(const ProcessGroupConfig & config)
{
  std::map<ProcessName, std::shared_ptr<Process>> processes;
  for (const auto & process : config.processes) {
    process::LogConfig log_config;
    if (config.log_file_enable) {
      if (process.second.stdout_stream_logging.find(StreamLoggingHandler::LogFile) !=
            process.second.stdout_stream_logging.end() ||
          (process.second.stream_redirection == StreamRedirection::RedirectToLogFile)) {
        log_config.stdout_log_file_name =
          config.log_directory + "/" + process.first.c_str() + ".stdout";
        log_config.stdout_log_file_buffering = config.log_file_buffering;
      }
      if (process.second.stderr_stream_logging.find(StreamLoggingHandler::LogFile) !=
            process.second.stderr_stream_logging.end() ||
          (process.second.stream_redirection == StreamRedirection::RedirectToLogFile)) {
        log_config.stderr_log_file_name =
          config.log_directory + "/" + process.first.c_str() + ".stderr";
        log_config.stderr_log_file_buffering = config.log_file_buffering;
      }
      if ((process.second.stdout_stream_logging.find(StreamLoggingHandler::LogFileCombined) !=
           process.second.stdout_stream_logging.end()) ||
          (process.second.stderr_stream_logging.find(StreamLoggingHandler::LogFileCombined) !=
           process.second.stderr_stream_logging.end())) {
        log_config.process_log_file_name =
          config.log_directory + "/" + process.first.c_str() + ".log";
        log_config.process_log_file_buffering = config.log_file_buffering;
      }
    }
    process::ProcessPipeConfig pipe_config{process.second.stream_redirection, true};

    processes.insert(
      {process.first,
       std::make_unique<Process>(
         process.first, process.second, apex::make_optional(log_config), pipe_config)});
  }

  return processes;
}
}  // namespace

ProcessGroup::ProcessGroup(const ProcessGroupConfig & config,
                           const apex::logging::LoggerBase * logger)
: m_config(config),
  m_processes(create_processes(config)),
  m_state{common::OFF_STATE_NAME},
  m_logger{logger},
  m_event_comm{std::make_shared<EventCommunicator>()},
  m_liveliness_monitor{}
{
  if (config.log_file_enable) {
    if (!config.log_directory.empty()) {
      const std::string log_file =
        config.log_directory.c_str() + std::string("/") + config.group_name.c_str() + ".log";
      open_log(log_file, config.log_file_buffering);
    }
  }
}

std::size_t ProcessGroup::get_num_processes() const noexcept
{
  return m_processes.size();
}

std::shared_ptr<Process> ProcessGroup::get_process(const ProcessName & process_name)
{
  if (m_processes.count(process_name) == 0) {
    throw apex::runtime_error{"Process not found: ", process_name};
  }
  return m_processes.find(process_name)->second;
}

bool ProcessGroup::in_transition()
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_in_transition;
}

void ProcessGroup::set_in_transition(bool in_transition)
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  m_in_transition = in_transition;
}

ProcessGroupError ProcessGroup::get_error()
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_group_error;
}

void ProcessGroup::set_error(ProcessGroupError error)
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  // fatal error cannot be changed
  if (m_group_error != ProcessGroupError::FATAL) {
    m_group_error = error;
  }
}

void ProcessGroup::set_error()
{
  if (m_config.on_failure_behavior == OnFailureBehavior::Fatal) {
    set_error(ProcessGroupError::FATAL);
  } else {
    set_error(ProcessGroupError::ERROR);
  }
}

ProcessGroupStateName ProcessGroup::get_state()
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_state;
}

const apex::logging::LoggerBase * ProcessGroup::get_logger() const
{
  return m_logger;
}

void ProcessGroup::set_state(const ProcessGroupStateName & state)
{
  std::unique_lock<std::mutex> lock(m_mutex);
  m_state = state;
  lock.unlock();
  m_state_changed.notify_one();
}

bool ProcessGroup::wait_for_state(const ProcessGroupStateName & expected_state,
                                  const std::chrono::milliseconds & timeout)
{
  std::unique_lock<std::mutex> lock(m_mutex);
  const bool is_in_target_state = m_state_changed.wait_for(
    lock, timeout, [this, expected_state] { return m_state == expected_state; });
  return is_in_target_state;
}

std::shared_ptr<EventCommunicator> ProcessGroup::get_event_communicator()
{
  return m_event_comm;
}

void ProcessGroup::reset_process_states()
{
  for (const auto & element : m_processes) {
    const auto & process = element.second;
    process->set_error(ProcessError::OK);
    process->set_state(ProcessExecutionState::IDLE);
    process->reset_state();
  }
}

bool ProcessGroup::is_any_processes_running() const
{
  return !std::all_of(m_processes.begin(), m_processes.end(), [](const auto & p) {
    return (p.second->get_state() == ProcessExecutionState::TERMINATED) ||
           (p.second->get_state() == ProcessExecutionState::IDLE);
  });
}

void ProcessGroup::set_all_process_are_terminated(bool all_process_are_terminated)
{
  std::unique_lock<std::mutex> lock(m_mutex);
  m_all_processes_terminated = all_process_are_terminated;
  if (m_all_processes_terminated) {
    GROUP_LOG_TRACE(this, "all_processes_terminated");
    lock.unlock();
    m_all_processes_terminated_cv.notify_one();
  } else {
    GROUP_LOG_TRACE(this, "all_processes_terminated: NO");
  }
}

bool ProcessGroup::wait_for_all_processes_terminated(const std::chrono::milliseconds & timeout)
{
  std::unique_lock<std::mutex> lock(m_mutex);
  return m_all_processes_terminated_cv.wait_for(
    lock, timeout, [this] { return m_all_processes_terminated; });
}

std::chrono::milliseconds ProcessGroup::get_max_sigterm_timeout() const
{
  std::chrono::milliseconds max_sigterm_timeout_process{0};
  for (const auto & element : m_processes) {
    const auto & process = element.second;
    const auto & state = process->get_state();
    if ((state != ProcessExecutionState::IDLE) && (state != ProcessExecutionState::TERMINATED)) {
      const auto sigterm_timeout = process->get_config().sigterm_timeout;
      if (sigterm_timeout > max_sigterm_timeout_process) {
        max_sigterm_timeout_process = sigterm_timeout;
      }
    }
  }
  return max_sigterm_timeout_process;
}

std::chrono::milliseconds ProcessGroup::get_max_sigkill_timeout() const
{
  std::chrono::milliseconds max_sigkill_timeout_process{0};
  for (const auto & element : m_processes) {
    const auto & process = element.second;
    const auto & state = process->get_state();
    if ((state != ProcessExecutionState::IDLE) && (state != ProcessExecutionState::TERMINATED)) {
      const auto sigkill_timeout = process->get_config().sigkill_timeout;
      if (sigkill_timeout > max_sigkill_timeout_process) {
        max_sigkill_timeout_process = sigkill_timeout;
      }
    }
  }
  return max_sigkill_timeout_process;
}

void ProcessGroup::terminate()
{
  for (const auto & element : m_processes) {
    const auto & process = element.second;
    const auto & state = process->get_state();
    const auto & name = process->get_name();
    const auto pid = process->get_pid();
    if ((state != ProcessExecutionState::IDLE) && (state != ProcessExecutionState::TERMINATED)) {
      GROUP_LOG_DEBUG(this,
                      apex::no_separator{},
                      "Process TERMINATING with SIGTERM: ",
                      process_to_log_msg(name, pid));
      process->set_state(ProcessExecutionState::TERMINATING);
      try {
        process->terminate();
      } catch (const apex::runtime_error & e) {
        GROUP_LOG_ERROR(this,
                        apex::no_separator{},
                        "Stopping process ",
                        process_to_log_msg(name, pid),
                        " failed: ",
                        e.what());
        process->set_error(ProcessError::STOP_FAILED);
        set_error();
      }
    }
  }
}

void ProcessGroup::kill()
{
  for (const auto & element : m_processes) {
    const auto & process = element.second;
    const auto & state = process->get_state();
    const auto & name = process->get_name();
    const auto pid = process->get_pid();
    if ((state != ProcessExecutionState::IDLE) && (state != ProcessExecutionState::TERMINATED)) {
      GROUP_LOG_INFO(this,
                     apex::no_separator{},
                     "Process TERMINATING with SIGKILL: ",
                     process_to_log_msg(name, pid));
      try {
        process->kill();
      } catch (const apex::runtime_error & e) {
        GROUP_LOG_ERROR(this,
                        apex::no_separator{},
                        "Stopping process failed, ",
                        process_to_log_msg(name, pid),
                        ": ",
                        e.what());
        process->set_error(ProcessError::STOP_FAILED);
        set_error(ProcessGroupError::FATAL);
      }
    }
  }
}

bool ProcessGroup::stop()
{
  GROUP_LOG_TRACE(this, "Stop all processes");
  bool all_terminated = !is_any_processes_running();
  if (all_terminated) {
    return true;
  }

  set_all_process_are_terminated(false);
  terminate();
  all_terminated = wait_for_all_processes_terminated(get_max_sigterm_timeout());
  if (!all_terminated) {
    kill();
    all_terminated = wait_for_all_processes_terminated(get_max_sigkill_timeout());
    if (!all_terminated) {
      for (const auto & element : m_processes) {
        const auto & process = element.second;
        if (process->get_state() != ProcessExecutionState::TERMINATED) {
          GROUP_LOG_ERROR(this,
                          apex::no_separator{},
                          "Failure killing process: ",
                          process_to_log_msg(element.first, element.second->get_pid()));
          process->set_error(ProcessError::STOP_FAILED);
          set_error();
        }
      }
    }
  }

  return all_terminated;
}

bool ProcessGroup::has_pid(std::int32_t pid) const
{
  return std::any_of(m_processes.begin(), m_processes.end(), [pid](const auto & p) {
    return p.second->has_pid() && (p.second->get_pid() == pid);
  });
}

void ProcessGroup::open_log(std::string log_file_name, bool buffered)
{
  if (!buffered) {
    (void)m_log_file.rdbuf()->pubsetbuf(nullptr, 0);
  }

  m_log_file_name = std::move(log_file_name);
  m_log_file.open(m_log_file_name.c_str());
  if (!m_log_file.is_open()) {
    throw apex::runtime_error{"unable to open file", m_log_file_name.c_str()};
  }
}

void ProcessGroup::close_log() noexcept
{
  if (m_log_file.is_open()) {
    try {
      m_log_file.close();
      // prevent throwing on destructor
    } catch (const std::exception & e) {
      std::cerr << e.what() << std::endl;
    } catch (...) {
      std::cerr << "Failed to close log file: " << m_log_file_name << std::endl;
    }
  }
}

}  // namespace process_group
}  // namespace process_manager
}  // namespace apex
