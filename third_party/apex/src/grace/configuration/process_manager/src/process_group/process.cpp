/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#include "process_manager/process_group/process.hpp"

#include <memory>
#include <utility>
#ifdef APEX_QNX
  #include <mutex>
#endif  // APEX_QNX

#include <thread>
/// \namespace apex
namespace apex
{
/// \namespace apex::process_manager
namespace process_manager
{
/// \namespace apex::process_manager::process_group
namespace process_group
{

Process::Process(const ProcessName & process_name,
                 ProcessConfig config,
                 apex::optional<process::LogConfig> log_config,
                 process::ProcessPipeConfig pipe_config)
: m_process{process_name.c_str(), config.default_startup_config, log_config, pipe_config},
  m_config{std::move(config)},
  m_log_config{std::move(log_config)}
{
  if (m_log_config != apex::nullopt) {
    if (!m_log_config->stdout_log_file_name.empty()) {
      open_log(m_log_config->stdout_log_file_name,
               m_stdout_log_file,
               m_log_config->stdout_log_file_buffering);
    }
    if (!m_log_config->stderr_log_file_name.empty()) {
      open_log(m_log_config->stderr_log_file_name,
               m_stderr_log_file,
               m_log_config->stderr_log_file_buffering);
    }
    if (!m_log_config->process_log_file_name.empty()) {
      open_log(m_log_config->process_log_file_name,
               m_process_log_file,
               m_log_config->process_log_file_buffering);
    }
  }
}

Process::~Process()
{
  if (m_log_config != apex::nullopt) {
    if (!m_log_config->stdout_log_file_name.empty()) {
      close_log(m_log_config->stdout_log_file_name, m_stdout_log_file);
    }
    if (!m_log_config->stderr_log_file_name.empty()) {
      close_log(m_log_config->stderr_log_file_name, m_stderr_log_file);
    }
    if (!m_log_config->process_log_file_name.empty()) {
      close_log(m_log_config->process_log_file_name, m_process_log_file);
    }
  }
}

void Process::open_log(const std::string & log_file_name, std::ofstream & log_file, bool buffered)
{
  if (!buffered) {
    (void)log_file.rdbuf()->pubsetbuf(nullptr, 0);
  }

  log_file.open(log_file_name.c_str());
  if (!log_file.is_open()) {
    throw apex::runtime_error{"unable to open file", log_file_name.c_str()};
  }
}

void Process::close_log(const std::string & log_file_name, std::ofstream & log_file)
{
  if (log_file.is_open()) {
    try {
      log_file.close();
      // prevent throwing on destructor
    } catch (const std::exception & e) {
      std::cerr << e.what() << std::endl;
    } catch (...) {
      std::cerr << "Failed to close log file: " << log_file_name << std::endl;
    }
  }
}

ProcessName Process::get_name() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.get_name();
}

ProcessExecutionState Process::get_state() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_state;
}

void Process::set_state(const ProcessExecutionState & state)
{
  std::unique_lock<std::mutex> lock(m_mutex);
  m_state = state;
  lock.unlock();
  m_state_changed.notify_one();
}

ProcessError Process::get_error() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_error;
}

void Process::set_error(const ProcessError & error)
{
  std::unique_lock<std::mutex> lock(m_mutex);
  m_error = error;
  lock.unlock();
  m_state_changed.notify_one();
}

ProcessConfig Process::get_config() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_config;
}

bool Process::wait_for_state(ProcessExecutionState expected_state,
                             const std::chrono::milliseconds & timeout)
{
  std::unique_lock<std::mutex> lock(m_mutex);
  const bool is_in_target_state = m_state_changed.wait_for(
    lock, timeout, [this, expected_state] { return m_state == expected_state; });
  return is_in_target_state;
}

bool Process::wait_for_running_state(const std::chrono::milliseconds & timeout)
{
  std::unique_lock<std::mutex> lock(m_mutex);
  const bool is_in_target_state = m_state_changed.wait_for(lock, timeout, [this] {
    return (m_state == ProcessExecutionState::RUNNING) || (m_error != ProcessError::OK);
  });
  return is_in_target_state && (m_error == ProcessError::OK);
}

bool Process::is_running_reported() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_running_reported;
}

void Process::set_is_running_reported(bool is_running_reported)
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  m_running_reported = is_running_reported;
}

apex::optional<StartupConfig> Process::get_startup_config() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.get_config();
}

void Process::start(const StartupConfig & config)
{
#ifdef APEX_QNX
  static std::mutex g_start_mutex;
#endif  // APEX_QNX
  const std::unique_lock<std::mutex> lock(m_mutex);
#ifdef APEX_QNX
  // A global mutex is required because posix_spawn is not thread-safe on QNX when creating and
  // destroying file descriptors in a multithreaded application
  {
    std::unique_lock<std::mutex> start_lock(g_start_mutex);
    m_process.start(config);
  };
#else
  m_process.start(config);
#endif  // APEX_QNX
}

void Process::terminate()
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  m_process.terminate();
}

void Process::kill()
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  m_process.kill();
}

std::int32_t Process::get_stream_fd(size_t stream_offset) const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.get_pipe(stream_offset);
}

std::int32_t Process::get_termination_fd() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.get_termination_pipe();
}

bool Process::has_pid() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.has_pid();
}

std::int32_t Process::get_pid() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.get_pid();
}

bool Process::has_started() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.has_started();
}

bool Process::has_exited() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.has_exited();
}

bool Process::has_exit_code() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.has_exit_code();
}

std::int32_t Process::get_exit_code() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.get_exit_code();
}

bool Process::has_exit_signal() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.has_exit_signal();
}

std::int32_t Process::get_exit_signal() const
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.get_exit_signal();
}

string256_t Process::take_stream_data(size_t stream_offset)
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.take_stream_data(stream_offset);
}

bool Process::wait_for_exit(std::int32_t flags)
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.wait_for_exit(flags);
}

void Process::reset_state()
{
  const std::unique_lock<std::mutex> lock(m_mutex);
  return m_process.reset_state();
}

std::ofstream & Process::get_file_stream(size_t stream_offset)
{
  return m_process.get_file_stream(stream_offset);
}

}  // namespace process_group
}  // namespace process_manager
}  // namespace apex
