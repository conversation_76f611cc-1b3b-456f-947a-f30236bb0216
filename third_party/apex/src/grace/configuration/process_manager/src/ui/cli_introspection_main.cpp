/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#include <cstring>
#include <filesystem>
#include <fstream>
#include <iostream>
#include <string>

#include "process_manager/config/config_parser.hpp"
#include "process_manager/introspection/print_helper.hpp"
#include "settings/from_yaml.hpp"
#include "settings/repository.hpp"
#include "settings_extensions/from_yaml.hpp"

using apex::process_manager::ProcessGroupConfig;
using apex::process_manager::introspection::operator<<;
using apex::process_manager::config::get_process_manager_config;
using apex::settings::repository::get;
using apex::settings_extensions::yaml::load_repository_from_command_line;

namespace
{
void print_help()
{
  std::cout
    << "Usage:\n"
       "  introspection [OPTIONS] --apex-settings-file path/to/launch.yaml\n"
       "  introspection --help\n"
       "\nOptions:\n"
       "  --help                     Display help and exit\n"
       "  --validate                 Validates a launch file. Returns 0 if it is valid and 1 if it "
       "is not\n"
       "  --print, --print-md        Prints an overview of the process group configuration in "
       "markdown format\n"
       "  --process-group <group>    Selects which process group will be printed out\n"
       "  --file <file>              Specifies the output file for the printed configuration\n"
    << std::endl;
}
}  // namespace

int main(int argc, char ** argv)
{
  bool validate = false;
  bool print_md = false;
  std::string process_group, output_file;

  for (int i = 1; i < argc; ++i) {
    if (std::strcmp(argv[i], "--help") == 0) {
      print_help();
      return EXIT_FAILURE;
    }
    if (std::strcmp(argv[i], "--print") == 0 || std::strcmp(argv[i], "--print-md") == 0) {
      print_md = true;
    }
    if (std::strcmp(argv[i], "--validate") == 0) {
      validate = true;
    }
    if (i < argc - 1) {
      if (std::strcmp(argv[i], "--process-group") == 0) {
        process_group = argv[++i];
      } else if (std::strcmp(argv[i], "--file") == 0) {
        output_file = argv[++i];
      }
    }
  }

  if (!print_md && !validate) {
    std::cerr << "\033[31mERROR:\033[39m No action specified. Printing help menu.\n\n";
    print_help();
    return EXIT_FAILURE;
  }

  try {
    auto settings = get();
    load_repository_from_command_line(argc, argv);
    auto config = get_process_manager_config(settings);

    if (print_md) {
      std::ostream * out = &std::cout;
      std::ofstream file_stream;

      if (!output_file.empty()) {
        file_stream.open(output_file, std::ios::out | std::ios::trunc);
        if (!file_stream.is_open()) {
          std::cerr << "\033[31mERROR:\033[39m Unable to open file: " << output_file << std::endl;
          return EXIT_FAILURE;
        }
        std::cout << "\033[32mOutput file created at: " << std::filesystem::absolute(output_file)
                  << std::endl;
        out = &file_stream;
      }

      *out << "# Process manager introspection\n\n";
      if (config.essential_group &&
          (process_group.empty() || config.essential_group->group_name == process_group)) {
        *out << *config.essential_group << std::endl;
      }
      if (config.framework_group &&
          (process_group.empty() || config.framework_group->group_name == process_group)) {
        *out << *config.framework_group << std::endl;
      }
      for (const auto & group_config : config.process_groups) {
        if (process_group.empty() || group_config.group_name == process_group) {
          *out << group_config;
        }
      }
    }

    if (validate) {
      std::cout << "\033[32mLaunch file is VALID" << std::endl;
      return EXIT_SUCCESS;
    }
  } catch (const std::exception & e) {
    std::cerr << "\033[31mLaunch file is INVALID:\n" << e.what();
    return EXIT_FAILURE;
  }

  return EXIT_SUCCESS;
}
