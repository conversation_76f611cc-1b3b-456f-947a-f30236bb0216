/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#include "process_manager/config/parse/process_parser.hpp"

#include <algorithm>
#include <filesystem>
#include <regex>

#include "process_manager/config/error.hpp"
#include "process_manager/config/parse/startup_config_parser.hpp"

/// \namespace apex
namespace apex
{
/// \namespace apex::process_manager
namespace process_manager
{
/// \namespace apex::process_manager::config
namespace config
{

using apex::settings::inspect::array_view;
using apex::settings::inspect::dictionary_view;
using apex::settings::inspect::get;
using apex::settings::inspect::get_or_default;
using apex::settings::inspect::maybe;
using apex::settings::inspect::no_value;
using apex::settings::inspect::string_view;
using settings::construct::boolean;
using settings::construct::integer;

namespace
{
ExecutionStateReportType get_report_type(string_view str)
{
  const std::unordered_map<string_view, ExecutionStateReportType> map = {
    {"None", ExecutionStateReportType::None},
    {"ByStdoutStream", ExecutionStateReportType::ByStdoutStream},
    {"ByStderrStream", ExecutionStateReportType::ByStderrStream},
    {"ByTopic", ExecutionStateReportType::ByTopic}};

  const auto iter = map.find(str);
  if (iter == map.end()) {
    throw std::runtime_error{"Invalid 'Execution State Report' type: '" + std::string(str) +
                             std::string("'")};
  }
  return iter->second;
}

StreamLoggingHandler get_stream_output_behavior(string_view str)
{
  const std::unordered_map<string_view, StreamLoggingHandler> map = {
    {"Console", StreamLoggingHandler::Console},
    {"LogTopic", StreamLoggingHandler::LogTopic},
    {"LogFile", StreamLoggingHandler::LogFile},
    {"LogFileCombined", StreamLoggingHandler::LogFileCombined},
    {"ProcessGroupState", StreamLoggingHandler::ProcessGroupState},
  };

  const auto iter = map.find(str);
  if (iter == map.end()) {
    throw std::runtime_error{"Invalid stream handler option type: '" + std::string(str) +
                             std::string("'")};
  }
  return iter->second;
}

StreamRedirection get_stream_redirection_option(string_view str)
{
  const std::unordered_map<string_view, StreamRedirection> map = {
    {"RedirectToProcessManager", StreamRedirection::RedirectToPipe},
    {"RedirectToDevNull", StreamRedirection::RedirectToDevNull},
    {"RedirectToLogFile", StreamRedirection::RedirectToLogFile},
    {"DoNotRedirect", StreamRedirection::DoNotRedirect},
  };

  const auto iter = map.find(str);
  if (iter == map.end()) {
    throw std::runtime_error{"Invalid stream redirection option type: '" + std::string(str) +
                             std::string("'")};
  }
  return iter->second;
}

}  // namespace

ProcessParser::ProcessParser(const dictionary_view & dv,
                             const std::string & settings_path_prefix,
                             const ProcessGroupName & group_name,
                             std::int32_t process_id,
                             bool is_essential_group)
: m_dict(dv),
  m_group_name(group_name),
  m_process_id(process_id),
  m_settings_path_prefix(settings_path_prefix),
  m_is_essential_group(is_essential_group)
{
}

ProcessName ProcessParser::get_process_name() const
{
  return m_name;
}

ProcessConfig ProcessParser::get_process_config() const
{
  return m_config;
}

void ProcessParser::parse()
{
  parse_name();
  check_entries_are_valid();
  parse_options();
  parse_startup_config();
  check_consistency();
}

void ProcessParser::check_consistency()
{
  if ((m_config.stream_redirection != StreamRedirection::RedirectToPipe) &&
      ((m_config.report_type == ExecutionStateReportType::ByStdoutStream) ||
       (m_config.report_type == ExecutionStateReportType::ByStderrStream))) {
    throw config_error(
      add_path_prefix(kEntryStreamRedirection),
      m_group_name,
      "",
      m_name,
      "ByStdoutStream and ByStderrStream requires RedirectToProcessManager redirection");
  }
}

void ProcessParser::check_entries_are_valid()
{
  std::for_each(m_dict.begin(), m_dict.end(), [this](const auto & entry) {
    const auto entry_name = std::string(entry.first);
    if (m_valid_entries.find(entry_name) == m_valid_entries.end()) {
      throw config_error(add_path_prefix(entry_name),
                         m_group_name,
                         "",
                         m_name,
                         "Found unknown entry in 'processes' options: '" + std::string(entry_name) +
                           std::string("'"));
    }
  });
}

void ProcessParser::validate_process_name(std::string_view name)
{
  if (name.empty()) {
    throw std::runtime_error{"Invalid empty process name"};
  }
  if (name.length() > common::MAX_PROCESS_NAME_LENGTH) {
    throw std::runtime_error("Process name is too large (max " +
                             std::to_string(common::MAX_PROCESS_NAME_LENGTH) + " char)");
  }
}

void ProcessParser::parse_name()
{
  std::string process_name;
  try {
    process_name = get<string_view>(m_dict, kEntryName.data());
    validate_process_name(process_name);
    m_name = ProcessName(process_name);
  } catch (const std::exception & e) {
    throw config_error(add_path_prefix(kEntryName), m_group_name, "", process_name, e.what());
  }
}

void ProcessParser::parse_options()
{
  parse_is_self_terminating();
  parse_execution_report();
  parse_startup_timeout_ms();
  parse_sigterm_timeout_ms();
  parse_sigkill_timeout_ms();
  parse_stream_redirection();
  parse_stdout_handlers();
  parse_stderr_handlers();
  parse_allow_sigterm_exit();
}


void ProcessParser::parse_is_self_terminating()
{
  try {
    const auto is_self_terminating = get<maybe<boolean>>(m_dict, kEntryIsSelfTerminating.data());
    if (is_self_terminating != no_value) {
      m_config.is_self_terminating = is_self_terminating.value();
    }
  } catch (const std::exception & e) {
    throw config_error(
      add_path_prefix(kEntryIsSelfTerminating), m_group_name, "", m_name, e.what());
  }
}

void ProcessParser::parse_execution_report()
{
  try {
    const auto execution_report = get<maybe<string_view>>(m_dict, kEntryExecutionReport.data());
    if (execution_report != no_value) {
      m_config.report_type = get_report_type(execution_report.value());
      if (m_is_essential_group && (m_config.report_type == ExecutionStateReportType::ByTopic)) {
        throw std::runtime_error{"'ByTopic' not supported in essential group"};
      }
    }
  } catch (const std::exception & e) {
    throw config_error(add_path_prefix(kEntryExecutionReport), m_group_name, "", m_name, e.what());
  }
}

void ProcessParser::parse_startup_timeout_ms()
{
  try {
    const auto startup_timeout_ms = get<maybe<integer>>(m_dict, kEntryStartupTimeoutMs.data());
    if (startup_timeout_ms != no_value) {
      if (startup_timeout_ms.value() > 0) {
        m_config.startup_timeout = std::chrono::milliseconds(startup_timeout_ms.value());
      } else if (startup_timeout_ms.value() == -1) {
        m_config.startup_timeout = std::chrono::milliseconds::max();
      } else {
        throw std::runtime_error(std::string("'") + std::string(kEntryStartupTimeoutMs) +
                                 "' entry cannot have negative values");
      }
    }
  } catch (const std::exception & e) {
    throw config_error(add_path_prefix(kEntryStartupTimeoutMs), m_group_name, "", m_name, e.what());
  }
}

void ProcessParser::parse_sigterm_timeout_ms()
{
  try {
    const auto sigterm_timeout_ms = get<maybe<integer>>(m_dict, kEntrySigtermTimeoutMs.data());
    if (sigterm_timeout_ms != no_value) {
      if (sigterm_timeout_ms.value() >= 0) {
        m_config.sigterm_timeout = std::chrono::milliseconds(sigterm_timeout_ms.value());
      } else if (sigterm_timeout_ms.value() == -1) {
        m_config.sigterm_timeout = std::chrono::milliseconds::max();
      } else {
        throw std::runtime_error(std::string("'") + std::string(kEntrySigtermTimeoutMs) +
                                 "' entry cannot have negative values");
      }
    }
  } catch (const std::exception & e) {
    throw config_error(add_path_prefix(kEntrySigtermTimeoutMs), m_group_name, "", m_name, e.what());
  }
}

void ProcessParser::parse_sigkill_timeout_ms()
{
  try {
    const auto sigkill_timeout_ms = get<maybe<integer>>(m_dict, kEntrySigkillTimeoutMs.data());
    if (sigkill_timeout_ms != no_value) {
      if (sigkill_timeout_ms.value() >= 0) {
        m_config.sigkill_timeout = std::chrono::milliseconds(sigkill_timeout_ms.value());
      } else if (sigkill_timeout_ms.value() == -1) {
        m_config.sigkill_timeout = std::chrono::milliseconds::max();
      } else {
        throw std::runtime_error(std::string("'") + std::string(kEntrySigkillTimeoutMs) +
                                 "' entry cannot have negative values");
      }
    }
  } catch (const std::exception & e) {
    throw config_error(add_path_prefix(kEntrySigkillTimeoutMs), m_group_name, "", m_name, e.what());
  }
}

void ProcessParser::parse_allow_sigterm_exit()
{
  try {
    const auto allow_sigterm_exit =
      get<maybe<boolean>>(m_dict, kEntryAllowSigtermTermination.data());
    if (allow_sigterm_exit != no_value) {
      m_config.allow_sigterm_exit = allow_sigterm_exit.value();
    }
  } catch (const std::exception & e) {
    throw config_error(
      add_path_prefix(kEntryAllowSigtermTermination), m_group_name, "", m_name, e.what());
  }
}

void ProcessParser::parse_stream_redirection()
{
  try {
    const auto entry_value = get<maybe<string_view>>(m_dict, kEntryStreamRedirection.data());
    if (entry_value != no_value) {
      m_config.stream_redirection = get_stream_redirection_option(entry_value.value());
      ;
    }
  } catch (const std::exception & e) {
    throw config_error(
      add_path_prefix(kEntryStreamRedirection), m_group_name, "", m_name, e.what());
  }
}

void ProcessParser::parse_stdout_handlers()
{
  try {
    const auto stdout_handlers = get<maybe<array_view>>(m_dict, kEntryStdoutHandlers.data());
    if (stdout_handlers != no_value) {
      for (const auto & logging_element : stdout_handlers.value()) {
        const auto logging_type = get_stream_output_behavior(get<string_view>(logging_element));
        if (logging_type == StreamLoggingHandler::ProcessGroupState) {
          throw std::runtime_error("'ProcessGroupState' logging option is deprecated");
        }
#ifdef APEX_CERT
        if (logging_type == StreamLoggingHandler::Console) {
          throw std::runtime_error("'Console' logging option is not supported in Apex.Grace Cert");
        }
#endif
        if (m_is_essential_group && (logging_type == StreamLoggingHandler::LogTopic)) {
          throw std::runtime_error{"'LogTopic' not supported in essential group"};
        }
        m_config.stdout_stream_logging.insert(logging_type);
      }
    }
  } catch (const std::exception & e) {
    throw config_error(add_path_prefix(kEntryStdoutHandlers), m_group_name, "", m_name, e.what());
  }
}

void ProcessParser::parse_stderr_handlers()
{
  try {
    const auto stderr_handlers = get<maybe<array_view>>(m_dict, kEntryStderrHandlers.data());
    if (stderr_handlers != no_value) {
      for (const auto & logging_element : stderr_handlers.value()) {
        const auto logging_type = get_stream_output_behavior(get<string_view>(logging_element));
        if (logging_type == StreamLoggingHandler::ProcessGroupState) {
          throw std::runtime_error("'ProcessGroupState' logging option is deprecated");
        }
#ifdef APEX_CERT
        if (logging_type == StreamLoggingHandler::Console) {
          throw std::runtime_error("'Console' logging option is not supported in Apex.Grace Cert");
        }
        if (m_is_essential_group && (logging_type == StreamLoggingHandler::LogTopic)) {
          throw std::runtime_error{"'LogTopic' not supported in essential group"};
        }
#endif
        m_config.stderr_stream_logging.insert(logging_type);
      }
    }
  } catch (const std::exception & e) {
    throw config_error(add_path_prefix(kEntryStderrHandlers), m_group_name, "", m_name, e.what());
  }
}

void ProcessParser::parse_startup_config()
{
  const auto default_startup_config_dict =
    get<maybe<dictionary_view>>(m_dict, kEntryDefaultStartupConfig.data());
  if (default_startup_config_dict != no_value) {
    StartupConfig startup_config;
    StartupConfigParser startup_config_parser(default_startup_config_dict.value(),
                                              add_path_prefix(kEntryDefaultStartupConfig),
                                              m_group_name,
                                              m_name,
                                              startup_config);
    startup_config_parser.parse();
    startup_config = startup_config_parser.get_startup_config();
    m_config.default_startup_config = startup_config;
  }
  try {
    set_system_env_variables(m_group_name, m_name, m_config.default_startup_config, m_process_id);
  } catch (const std::exception & e) {
    throw config_error(
      add_path_prefix(kEntryDefaultStartupConfig), m_group_name, "", m_name, e.what());
  }
}

}  // namespace config
}  // namespace process_manager
}  // namespace apex
