/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \include apex_node_executable.cpp

#include <signal.h>
#include <unistd.h>

#include <chrono>
#include <functional>
#include <memory>
#include <string>

#include "apex_init/apex_init.hpp"
#include "apexcpp/apexcpp.hpp"
#include "cpputils/common_exceptions.hpp"
#include "cpputils/safe_cast.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "logging/logging_macros.hpp"
#include "process_manager/config/types.hpp"
#include "process_manager_interfaces/msg/process_running.hpp"
#include "timer_service/clock_timer_service.hpp"

using namespace std::chrono_literals;
using process_manager_interfaces::msg::ProcessRunning;

namespace apex
{
namespace apex_os_minimal_process
{

struct Config
{
  apex::string_strict256_t name{"minimal_process"};
  std::chrono::milliseconds delay_startup = 0ms;
  std::chrono::milliseconds delay_shutdown = 0ms;
  std::chrono::milliseconds period = 500ms;
  bool print_to_stdout{false};
  bool print_to_stderr{false};
  bool exit{false};
  bool report_by_topic{false};
  std::size_t payload{0};
};

class MinimalProcess : public apex::executor::apex_node_base
{
public:
  MinimalProcess(const Config & config,
                 std::optional<apex::timer_service::steady_clock_timer_service> & timer_srv)
  : apex_node_base{config.name.c_str()},
    m_config{config},
    m_timer_sub{timer_srv.has_value() ? timer_srv->create_timer(0ms, config.period) : nullptr},
    m_logger{&get_rclcpp_node(), m_config.name}
  {
  }

  void on_startup()
  {
    std::this_thread::sleep_for(m_config.delay_startup);
    print_streams();
  }

  ~MinimalProcess()
  {
    std::this_thread::sleep_for(m_config.delay_shutdown);
  }

  void print_streams()
  {
    std::string message = m_config.name + ": " + apex::to_string(m_count);
    if (m_config.payload > 0) {
      message += " [" + std::string(m_config.payload, '.') + "]";
    }
    if (m_config.print_to_stdout) {
      std::cout << message << std::endl;
    }
    if (m_config.print_to_stderr) {
      std::cerr << message << std::endl;
    }
    ++m_count;
  }

private:
  bool execute_impl() override
  {
    if (m_timer_sub && m_timer_sub->test_and_reset()) {
      print_streams();
    }

    return true;
  }

  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    if (m_timer_sub) {
      return {m_timer_sub->to_sub_ptr()};
    } else {
      return {};
    }
  }

  Config m_config;
  apex::timer_service::timer_subscription_ptr m_timer_sub;
  apex::logging::Logger<> m_logger;
  size_t m_count{};
};

}  // namespace apex_os_minimal_process
}  // namespace apex

int32_t main(const int32_t argc, char ** const argv, char ** envp)
{
  int32_t result{};
  bool ignore_sigterm{false};
  apex::apex_os_minimal_process::Config config;

  (void)::setvbuf(stdout, NULL, _IOLBF, 0);
  (void)::setvbuf(stderr, NULL, _IOLBF, 0);

  for (auto i = 1; i < argc; ++i) {
    if (std::strcmp(argv[i], "--stdout") == 0) {
      config.print_to_stdout = true;
    }
    if (std::strcmp(argv[i], "--stderr") == 0) {
      config.print_to_stderr = true;
    }
    if (std::strcmp(argv[i], "--exit") == 0) {
      config.exit = true;
    }
    if (std::strcmp(argv[i], "--ignore-sigterm") == 0) {
      ignore_sigterm = true;
    }
    if (std::strcmp(argv[i], "--print-args") == 0) {
      std::ostringstream output;
      for (auto j = 1; j < argc; ++j) {
        output << argv[j] << " ";
      }
      std::cout << output.str() << std::endl;
    }
    if (std::strcmp(argv[i], "--print-args-stderr") == 0) {
      std::ostringstream output;
      for (auto j = 1; j < argc; ++j) {
        output << argv[j] << " ";
      }
      std::cerr << output.str() << std::endl;
    }
    if (std::strcmp(argv[i], "--report-running") == 0) {
      config.report_by_topic = true;
    }


    // arguments with parameter
    if (i < (argc - 1)) {
      if (std::strcmp(argv[i], "--print-env") == 0) {
        for (char ** env = envp; *env != nullptr; ++env) {
          const std::string var = *env;
          if (var.find(argv[i + 1]) == 0) {
            std::cout << var << std::endl;
          }
        }
      }
      if (std::strcmp(argv[i], "--name") == 0) {
        config.name = argv[i + 1];
      }
      if (std::strcmp(argv[i], "--delay-startup") == 0) {
        config.delay_startup = std::chrono::milliseconds{std::stoul(argv[i + 1])};
      }
      if (std::strcmp(argv[i], "--delay-shutdown") == 0) {
        config.delay_shutdown = std::chrono::milliseconds{std::stoul(argv[i + 1])};
      }
      if (std::strcmp(argv[i], "--period-ms") == 0) {
        config.period = std::chrono::milliseconds{std::stoul(argv[i + 1])};
      }
      if (std::strcmp(argv[i], "--exit-code") == 0) {
        result = std::stoi(argv[i + 1]);
      }
      if (std::strcmp(argv[i], "--payload") == 0) {
        config.payload = std::stoul(argv[i + 1]);
      }
      if (std::strcmp(argv[i], "--payload-stdout") == 0) {
        std::cout << "[" << std::string(std::stoul(argv[i + 1]), '.') << "]" << std::endl;
      }
      if (std::strcmp(argv[i], "--payload-stderr") == 0) {
        std::cerr << "[" << std::string(std::stoul(argv[i + 1]), '.') << "]" << std::endl;
      }
    }  // if(i < (argc - 1))
  }  // for(auto i = 1; i < argc; ++i)

  try {
    auto scoped_init = apex::scoped_init(argc, argv, false, false);
    const apex::interrupt_handler::installer interrupt_handler_installer{};

    if (ignore_sigterm) {
      signal(SIGTERM, SIG_IGN);
    }

    std::optional<apex::timer_service::steady_clock_timer_service> timer_srv;
    if (config.period.count() > 0) {
      timer_srv.emplace();
    }
    auto minimal_process =
      std::make_shared<apex::apex_os_minimal_process::MinimalProcess>(config, timer_srv);
    apex::event::monitored_process p{apex::event::sender_state::DoNotSyncWithDispatcher};
    const auto executor = apex::executor::executor_factory::create();
    if (timer_srv.has_value()) {
      (void)executor->add(minimal_process);
    }
    const apex::executor::executor_runner runner{
      apex::executor::executor_runner::deferred | apex::executor::executor_runner::interrupt,
      *executor};

    scoped_init.post_init();
    minimal_process->on_startup();
    if (config.report_by_topic) {
      p.send_post_init();
    }

    runner.issue();

    if (!config.exit) {
      apex::interrupt_handler::wait();
    }

    runner.stop();
  } catch (const std::exception & e) {
    if (rclcpp::ok()) {
      APEX_FATAL_R(e.what());
    } else {
      std::cerr << e.what() << "\n";
    }
    result = 2;
  } catch (...) {
    if (rclcpp::ok()) {
      APEX_FATAL_R("Unknown error occurred");
    } else {
      std::cerr << "Unknown error occurred"
                << "\n";
    }
    result = -1;
  }

  return result;
}
