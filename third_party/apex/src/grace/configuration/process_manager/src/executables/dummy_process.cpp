// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#include <signal.h>
#include <unistd.h>

#include <chrono>
#include <cstdint>
#include <cstring>
#include <iostream>
#include <string>
#include <thread>

namespace
{
auto dont_exit = false;
auto out_interval_ms = 10u;
std::int32_t exit_code = 0;
auto print_progress = false;
}  // namespace

std::int32_t main(const std::int32_t argc, char ** const argv, char ** envp)
{
  for (auto i = 1; i < argc; ++i) {
    if (std::strcmp(argv[i], "--print-args") == 0) {
      for (auto j = 1; j < argc; ++j) {
        std::cout << argv[j] << std::endl;
      }
    }

    if (std::strcmp(argv[i], "--dont-exit") == 0) {
      dont_exit = true;
    }

    if (std::strcmp(argv[i], "--print-progress") == 0) {
      print_progress = true;
    }

    if (std::strcmp(argv[i], "--ignore-sigterm") == 0) {
      signal(SIGTERM, SIG_IGN);
    }

    if (std::strcmp(argv[i], "--print-ld-library-path") == 0) {
      char * ld_library_path_env = getenv("LD_LIBRARY_PATH");
      if (ld_library_path_env) {
        std::cout << ld_library_path_env << std::endl;
      }
    }

    if (std::strcmp(argv[i], "--print-ament-prefix-path") == 0) {
      char * ament_prefix_path_env = getenv("AMENT_PREFIX_PATH");
      if (ament_prefix_path_env) {
        std::cout << ament_prefix_path_env << std::endl;
      }
    }

    // arguments with parameter
    if (i < (argc - 1)) {
      if (std::strcmp(argv[i], "--print-env") == 0) {
        for (char ** env = envp; *env != nullptr; ++env) {
          const std::string var = *env;
          if (var.find(argv[i + 1]) == 0) {
            std::cout << var << std::endl;
          }
        }
      }

      if (std::strcmp(argv[i], "--out-interval-ms") == 0) {
        out_interval_ms = static_cast<decltype(out_interval_ms)>(std::atoi(argv[i + 1]));
      }

      if (std::strcmp(argv[i], "--stdout") == 0) {
        std::cout << argv[i + 1];
      }

      if (std::strcmp(argv[i], "--stderr") == 0) {
        std::cerr << argv[i + 1];
      }

      if (std::strcmp(argv[i], "--exit-code") == 0) {
        exit_code = std::atoi(argv[i + 1]);
      }
    }  // if(i < (argc - 1))
  }  // for(auto i = 1; i < argc; ++i)

  (void)::setvbuf(stdout, NULL, _IOLBF, 0);
  (void)::setvbuf(stderr, NULL, _IOLBF, 0);

  while (dont_exit) {
    if (print_progress) {
      std::cout << "+" << std::endl;
      std::cerr << "*" << std::endl;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(out_interval_ms));
  }

  return exit_code;
}
