load("@apex//common/bazel/helpers:defs.bzl", "remove_duplicates")
load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_binary")

def process_manager(*, name, launch_file, data, start_default_essential_group = True, tags = None, **kwargs):
    """Creates a configured_binary target for the process manager.

    This wrapper macro creates a fully configured binary target for the process manager
    given a launch_file and data dependencies.

    Arguments:
        name: Name of the configured_binary target
        launch_file: The launch file to be used (automatically added as data dependency)
        data: List of additional data dependencies (e.g. other binary targets to be launched)
        start_default_essential_group: Start the default essential group. It contains the resource creator for Ida 3.0. Default is true.
        **kwargs: Default attributes apply (e.g. `tags`, `target_compatible_with`, ...)
    """
    default_essential_group_args = []
    default_essential_group_deps = []
    tags = (tags or [])
    if start_default_essential_group:
        default_essential_group_args = [
            "--essential-group",
            "$(rootpath @apex//grace/configuration/process_manager:default_essential_config)",
        ]
        default_essential_group_deps = [
            "@apex//ida/resource_creator:resource_creator_resources",
            "@apex//grace/configuration/process_manager:default_essential_config",
        ]
    configured_binary(
        name = name,
        args = default_essential_group_args + [
            "--apex-settings-file",
            "$(rootpath %s)" % launch_file,
        ],
        data = default_essential_group_deps + [launch_file] + data,
        tags = tags,
        executable = "@apex//grace/configuration/process_manager",
        use_runfiles_as_working_dir = True,
        **kwargs
    )

    configured_binary(
        name = name + ".introspection",
        args = [
            "--apex-settings-file",
            "$(rootpath %s)" % launch_file,
        ],
        # deps are required to resolve AMENT pkg prefixes
        data = default_essential_group_deps + [launch_file] + data,
        tags = remove_duplicates(tags + [
            "manual",
        ]),
        executable = "@apex//grace/configuration/process_manager:introspection",
        use_runfiles_as_working_dir = True,
        **kwargs
    )
