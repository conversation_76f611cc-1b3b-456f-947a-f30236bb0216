/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#ifndef PROCESS_MANAGER__CONFIG__TYPES_HPP_
#define PROCESS_MANAGER__CONFIG__TYPES_HPP_

#include <chrono>
#include <map>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <vector>

#include "cpputils/optional.hpp"
#include "cpputils/safe_cast.hpp"
#include "event/sender.hpp"
#include "logging/logging.hpp"
#include "process/process.hpp"
#include "process/startup_config.hpp"
#include "process_manager/graph.hpp"
#include "process_manager/visibility.hpp"
#include "process_manager_interfaces/common.hpp"
#include "process_manager_interfaces/msg/process_group_info.hpp"
#include "process_manager_interfaces/msg/process_info.hpp"
#include "rclcpp/qos.hpp"
#include "rmw/types.h"

/// \namespace apex
namespace apex
{
/// \namespace apex::process_manager
namespace process_manager
{

/// \class OnFailureBehavior
/// Behavior of a process group when there is a failure
/// \cert
enum class OnFailureBehavior : std::uint32_t
{
  /// \brief Do nothing to handle the failure
  /// the failure is expected to be handled externally by calling a service
  DoNothing = 0U,
  /// \brief Stop all processes and go to OFF state
  StopAllProcesses,
  /// \brief The error is not recoverable and the state is fatal
  Fatal
};

/// \class ExecutionStateReportType
/// The method used by a process to report its execution state
/// \cert
enum class ExecutionStateReportType : std::uint32_t
{
  /// \brief Process does not report its execution state
  None = 0U,
  /// \brief Process reports its execution state with a stdout stream output
  ByStdoutStream,
  /// \brief Process reports its execution state with a stderr stream output
  ByStderrStream,
  /// \brief Process reports its execution state by a pre-defined topic
  ByTopic
};

/// \class StreamLoggingHandler
/// The method used by a process to report its execution state
/// \cert
enum class StreamLoggingHandler : std::uint32_t
{
  /// \brief stream is logged to the console
  Console,
  /// \brief stream is logged to the /logging topic
  LogTopic,
  /// \brief stream is logged to a stream file (stdout or stdout)
  LogFile,
  /// \brief stream is logger to a combined file per process both for stdout and stderr
  LogFileCombined,
  /// \brief stream is to the process group state topic
  ProcessGroupState
};

/// \class StreamType
/// Type of stream output of a process
/// \cert
enum class StreamType : std::uint32_t
{
  /// \brief stdout stream type
  Stdout,
  /// \brief stderr stream type
  Stderr
};

using StreamRedirection = process::StreamRedirection;

/// \brief The type used to describe the execution state of a process
using ProcessExecutionState = process_manager_interfaces::msg::ProcessInfo::ProcessStateType;

/// \brief The type used to describe the error type of process
using ProcessError = process_manager_interfaces::msg::ProcessInfo::ProcessErrorType;

/// \brief The type used to describe the error type of process group
using ProcessGroupError = process_manager_interfaces::msg::ProcessGroupInfo::ErrorType;

/// \brief Type used for a process ID or process alias
using ProcessName = string256_t;

/// \brief Type used for a process group state name
using ProcessGroupStateName = string256_t;

/// \brief Type used for a process group name
using ProcessGroupName = string256_t;

using StartupConfig = process::StartupConfig;

/// \brief A list mapping process names with their start-up configuration
using StartupConfigList = std::unordered_map<ProcessName, StartupConfig>;

/// \brief A graph describing the dependencies between processes
using ProcessDependencyGraph = ItemGraph<StartupConfigList::value_type>;

/// \class State
/// A process group state configuration class
/// \cert
struct PROCESS_MANAGER_PUBLIC State
{
  /// \brief List of processes that should be running in this state
  StartupConfigList processes;
  /// \brief Dependencies between processes when starting processes in this state
  apex::optional<ProcessDependencyGraph> startup_dependencies;
  /// \brief Dependencies between processes when shutting down processes in this state
  apex::optional<ProcessDependencyGraph> shutdown_dependencies;
};

/// \class ProcessConfig
/// Configuration for a process
/// \cert
struct PROCESS_MANAGER_PUBLIC ProcessConfig
{
  /// \brief If true the process is of type 'Self-terminating' and it is expected to terminate
  /// by its own after it is started
  bool is_self_terminating{false};
  /// \brief Type of process execution state reporting of the process
  ExecutionStateReportType report_type{ExecutionStateReportType::None};
  /// \brief Allowed maximum duration to start a process
  std::chrono::milliseconds startup_timeout{5000};
  /// \brief Allowed maximum duration until a process terminates after sending SIGTERM
  std::chrono::milliseconds sigterm_timeout{5000};
  /// \brief Allowed maximum duration until a process terminates after sending SIGKILL
  std::chrono::milliseconds sigkill_timeout{5000};
  /// \brief Default start-up configuration for a process. This is the one used if no other
  /// start-up configuration is specified in a state
  StartupConfig default_startup_config;
  /// \brief Defines logging behavior for the stdout stream
  std::set<StreamLoggingHandler> stdout_stream_logging;
  /// \brief Defines logging behavior for the stderr stream
  std::set<StreamLoggingHandler> stderr_stream_logging;
  /// \brief If true terminating by a SIGTERM signal is not considered an error
  bool allow_sigterm_exit{false};
  /// \brief The stream redirection configuration
  StreamRedirection stream_redirection{StreamRedirection::RedirectToPipe};
};

/// \brief A list mapping process names with their configuration
using ProcessConfigList = std::unordered_map<ProcessName, ProcessConfig>;

/// \class ActionType
/// Describes a type of action inside a state transition sequence
/// \cert
enum class ActionType : uint8_t
{
  NoAction = 0,
  Stop,
  Start
};

/// \class Action
/// Describes an action inside a state transition sequence
/// \cert
struct PROCESS_MANAGER_PUBLIC Action
{
  /// \brief The name of the process for the action
  ProcessName process_name;
  /// \brief The start-up configuration.
  /// Note: This is used for Start actions and to determine if a process requires to be
  /// restarted in a state transition.
  StartupConfig startup_config;
  /// \brief If true the process streams will be forwarded to the logging topic
  ActionType type{ActionType::NoAction};
  /// \brief If true the process is self-terminating
  bool is_self_terminating{false};
};

/// \brief Compares two actions for equality
/// \param lhs The first action
/// \param rhs The second action
/// \return Whether two actions are equal
/// \cert
/// \deterministic
PROCESS_MANAGER_PUBLIC
bool operator==(const Action & lhs, const Action & rhs) noexcept;

/// \brief Compares two actions for inequality
/// \param lhs The first action
/// \param rhs The second action
/// \return Whether two actions are not equal
/// \cert
/// \deterministic
PROCESS_MANAGER_PUBLIC
inline bool operator!=(const Action & lhs, const Action & rhs) noexcept
{
  return !operator==(lhs, rhs);
}

/// \brief Graph containing a description of actions
using ActionGraph = ItemGraph<Action>;

/// \brief Map containing the corresponding action graph for a give state transition
/// First map key is the origin process group state
/// Second map key is the target process group state
using ActionGraphTable =
  std::unordered_map<ProcessGroupStateName, std::unordered_map<ProcessGroupStateName, ActionGraph>>;

/// \brief Configuration for the group thread liveliness monitor
struct GroupLivelinessMonitoringConfig
{
  /// \brief The switch to enable the monitoring
  bool is_enabled{false};
  /// \brief The period each thread signals its liveliness
  std::chrono::milliseconds thread_liveliness_heartbeat_period{-1};
  /// \brief The period to check if threads are alive
  std::chrono::milliseconds liveliness_check_period{-1};
};

/// \class ProcessGroupConfig
/// Contains the configuration required to construct a process group
/// \cert
struct PROCESS_MANAGER_PUBLIC ProcessGroupConfig
{
  /// \brief The process group name
  ProcessGroupName group_name;
  /// \brief The initial state
  /// Note: If it is not the 'Off' state it will self-transit to this state
  ProcessGroupStateName init_group_state{common::OFF_STATE_NAME};
  /// \brief List of all processes part of the process group and their configuration
  ProcessConfigList processes;
  /// \brief List of states of the process group
  std::unordered_map<ProcessGroupStateName, State> states;
  /// \brief Map containing the action graphs for each transition state
  ActionGraphTable action_graph_table;
  /// \brief Behavior when there is a failure
  OnFailureBehavior on_failure_behavior{OnFailureBehavior::DoNothing};
  /// \brief The minimal interval between reports (unless forced)
  std::chrono::milliseconds min_publish_interval{1000};
  /// \brief Root directory used to store log files for a process manager instance
  std::string log_directory;
  /// \brief The switch to enable processes' log files buffering
  bool log_file_buffering{true};
  /// \brief The switch to disable file logging globally for all process groups
  bool log_file_enable{true};
  /// \brief Sets the log level for the Apex.Grace logs reported by the process group.
  /// It is set to the global log level by default.
  logging::LogLevel log_level{logging::load_log_level()};
  /// \brief Time to wait in for the event dispatcher subscription. If the wait timeouts there
  /// will be an error. The wait can be disabled with a negative value.
  std::chrono::nanoseconds wait_for_dispatcher{apex::event::sender_state::DoNotSyncWithDispatcher};
  /// \brief Defines logging behavior for the group logging
  std::set<StreamLoggingHandler> group_log_handlers;
  /// \brief A prefix to prepend to services and topics.
  /// This is used when multiple process manager instances are expected.
  std::string process_manager_instance;
  /// \brief Flag to define if the group is the framework group
  bool is_framework_group{false};
  /// \brief Flag to define if the group is the essential group
  bool is_essential_group{false};
  /// \brief The configuration for the thread liveliness monitor
  GroupLivelinessMonitoringConfig group_liveliness_config;
};

/// \class ProcessManagerConfig
/// Contains the configuration required to construct a process manager
/// \cert
struct PROCESS_MANAGER_PUBLIC ProcessManagerConfig
{
  /// \brief Process Manager instance name, used to create a node common to all groups
  std::string name = "process_manager";
  /// \brief The process group intended for essential services
  std::optional<ProcessGroupConfig> essential_group;
  /// \brief The process group intended for framework services
  std::optional<ProcessGroupConfig> framework_group;
  /// \brief The process groups configurations
  std::vector<ProcessGroupConfig> process_groups;
  /// \brief Root directory used to store log files for each process group
  std::string log_directory;
  /// \brief The switch to disable processes' log files buffering
  bool log_file_buffering{true};
  /// \brief If true, a timestamp is added to the process group logging directory
  bool append_timestamp_to_log_file{false};
  /// \brief The switch to disable file logging globally for all process groups
  bool log_file_enable{false};
  /// \brief Time to wait in for the event dispatcher subscription. If the wait timeouts there
  /// will be an error. A negative or zero value disables event sending.
  std::chrono::nanoseconds wait_for_dispatcher{apex::event::sender_state::DoNotSyncWithDispatcher};
  /// \brief A prefix to prepend to services and topics.
  /// This is used when multiple process manager instances are expected.
  std::string process_manager_instance;
  /// \brief Maximum allowed simultaneous requests for services
  std::size_t max_simultaneous_requests{64};
  /// \brief The configuration for the thread liveliness monitor
  GroupLivelinessMonitoringConfig group_liveliness_config;
  /// \brief The period to check the thread liveliness
  std::optional<std::chrono::milliseconds> heartbeat_monitor_period;
};

/// \Brief Helper function to log the process name and PID with format
/// \cert
inline auto process_to_log_msg(const ProcessName & name, std::int32_t pid)
{
  return apex::varargs_to_string(apex::no_separator{}, "'", name, "' (pid=", pid, ")");
}

}  // namespace process_manager
}  // namespace apex

#endif  // PROCESS_MANAGER__CONFIG__TYPES_HPP_
