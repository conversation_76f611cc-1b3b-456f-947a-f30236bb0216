/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#ifndef PROCESS_MANAGER__PROCESS_GROUP__PROCESS_HPP_
#define PROCESS_MANAGER__PROCESS_GROUP__PROCESS_HPP_

#include <condition_variable>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <utility>

#include "process/process.hpp"
#include "process_manager/config/types.hpp"
#include "process_manager/visibility.hpp"
#include "rclcpp/dynamic_waitset/guard_condition.hpp"
#include "rclcpp/dynamic_waitset/shared_pollfd.hpp"
#include "threading/mutex.hpp"
#include "threading/unlock_guard.hpp"

/// \namespace apex
namespace apex
{
/// \namespace apex::process_manager
namespace process_manager
{
/// \namespace apex::process_manager::process_group
namespace process_group
{

/// \class Process
/// A descriptor of a process being monitored
/// Note: Provides proxy thread-safe proxy functions the process::Process APIs
/// It adds some additional data to keep track of the process execution state
/// \cert
class PROCESS_MANAGER_PUBLIC Process
{
public:
  /// \brief Constructs a process descriptor
  /// \param process_name The name of the process
  /// \param config The process configuration
  /// \param log_config Configuration for file logging
  /// \param pipe_config Configuration for process pipes
  /// \cert
  Process(const ProcessName & process_name,
          ProcessConfig config,
          apex::optional<process::LogConfig> log_config = apex::nullopt,
          process::ProcessPipeConfig pipe_config = process::ProcessPipeConfig{});

  ~Process();

  /// \brief Get the process name
  /// \return The process name
  /// \cert
  ProcessName get_name() const;

  /// \brief Get the process execution state
  /// \return The process execution state
  /// \cert
  ProcessExecutionState get_state() const;

  /// \brief Set the process execution state
  /// \param state The process execution state
  /// \cert
  void set_state(const ProcessExecutionState & state);

  /// \brief Get the process error state
  /// \return The process error state
  /// \cert
  ProcessError get_error() const;

  /// \brief Set the process error state
  /// \param state The process error state
  /// \cert
  void set_error(const ProcessError & error);

  /// \brief Get the process configuration
  /// \return The process configuration
  /// \cert
  ProcessConfig get_config() const;

  /// \brief Set the current process start-up configuration
  /// \param config The process start-up configuration
  /// \cert
  /// \deterministic
  void set_startup_config(const StartupConfig & config);

  /// \brief Get the current process start-up configuration
  /// \return The process start-up configuration
  /// \cert
  /// \deterministic
  apex::optional<StartupConfig> get_startup_config() const;

  /// \brief Wait until a specific execution state is reached
  /// \param expected_state The expected process execution state
  /// \param timeout The timeout used for the wait
  /// \return True if the process has reached the expected state in time (no timeout)
  /// \cert
  bool wait_for_state(ProcessExecutionState expected_state,
                      const std::chrono::milliseconds & timeout);

  /// \brief Wait until a RUNNING execution state is reached
  /// If there is an error while waiting it returns immediately. This happens when a process
  /// terminates unexpectedly.
  /// \param timeout The timeout used for the wait
  /// \return True if the process has reached RUNNING state in time and there is no error
  /// \cert
  bool wait_for_running_state(const std::chrono::milliseconds & timeout);

  /// \brief Checks if the process has reporting its running state
  /// \param The reported running state to set
  /// \cert
  /// \deterministic
  bool is_running_reported() const;

  /// \brief Sets if the process has reporting its running state
  /// \cert
  /// \deterministic
  void set_is_running_reported(bool is_running_reported);

  /// \brief Starts the process with a given configuration
  /// \param config The process start-up config
  /// \cert
  void start(const StartupConfig & config);

  /// \brief Sends SIGTERM to the process
  /// \cert
  /// \deterministic
  void terminate();

  /// \brief Sends SIGKILL to the process
  /// \cert
  /// \deterministic
  void kill();

  /// \brief Gets the reading end of the stream file descriptor
  /// \param stream_offset The offset of the stream
  /// \return The file descriptor of the reading end of the stream pipe
  /// \cert
  /// \deterministic
  std::int32_t get_stream_fd(size_t stream_offset) const;

  /// \brief Gets the reading end of the process termination fd
  /// \return The file descriptor of the reading end of the process termination pipe
  /// \cert
  /// \deterministic
  std::int32_t get_termination_fd() const;

  /// \brief Tests whether the process has id (effectively -- whether it is started)
  /// \return Whether the process has id
  /// \cert
  /// \deterministic
  bool has_pid() const;

  /// \brief Gets the process id
  /// \return The process id
  /// \cert
  /// \deterministic
  std::int32_t get_pid() const;

  /// \brief Tests whether the process has started
  /// \return Whether the process has started
  /// \cert
  /// \deterministic
  bool has_started() const;

  /// \brief Tests whether the process has exited
  /// \return Whether the process has exited
  /// \cert
  /// \deterministic
  bool has_exited() const;

  /// \brief Tests whether the process has exit code
  /// \return Whether the process has exit code
  /// \cert
  /// \deterministic
  bool has_exit_code() const;

  /// \brief Gets the process exit code
  /// \return The process exit code
  /// \cert
  /// \deterministic
  std::int32_t get_exit_code() const;

  /// \brief Tests whether the process has exit signal
  /// \return Whether the process has exit signal
  /// \cert
  /// \deterministic
  bool has_exit_signal() const;

  /// \brief Gets the process exit signal
  /// \return The process exit signal
  /// \cert
  /// \deterministic
  std::int32_t get_exit_signal() const;

  /// \brief Get and remove the current stream data as a string
  /// \param stream_offset The offset of the stream
  /// \return The current stream data as a string
  /// \cert
  /// \deterministic
  string256_t take_stream_data(size_t stream_offset);

  /// \brief Waits for process exit
  /// \param flags Flags to pass to POSIX waitpid()
  /// \return Whether process has exited
  /// \cert
  /// \deterministic if flags is WNOHANG.
  bool wait_for_exit(std::int32_t flags = 0);

  /// \brief Resets the exit state from a terminated process
  /// \cert
  /// \deterministic
  void reset_state();

  /// \brief Get the file stream used to log to file
  /// \cert
  std::ofstream & get_file_stream(size_t stream_offset);

  /// \brief Gets the file stream for the stdout log
  /// \return A reference to the ofstream object for the stdout log file
  std::ofstream & get_stdout_file_stream() noexcept
  {
    return m_stdout_log_file;
  }

  /// \brief Gets the file stream for the stderr log
  /// \return A reference to the ofstream object for the stderr log file
  std::ofstream & get_stderr_file_stream() noexcept
  {
    return m_stderr_log_file;
  }

  /// \brief Gets the file stream for the process log
  /// \return A reference to the ofstream object for the process log file
  std::ofstream & get_process_file_stream() noexcept
  {
    return m_process_log_file;
  }

  /// \brief Opens a log file with the specified name and buffering configuration
  /// \param log_file_name The name of the log file to open
  /// \param log_file A reference to the ofstream object to associate with the log file
  /// \param buffered Whether the log file should be buffered
  void open_log(const std::string & log_file_name, std::ofstream & log_file, bool buffered);

  /// \brief Closes the specified log file
  /// \param log_file_name The name of the log file to close
  /// \param log_file A reference to the ofstream object associated with the log file
  void close_log(const std::string & log_file_name, std::ofstream & log_file);

private:
  process::Process m_process;
  ProcessConfig m_config;
  ProcessExecutionState m_state{ProcessExecutionState::IDLE};
  ProcessError m_error{ProcessError::OK};
  mutable std::mutex m_mutex;
  std::condition_variable m_state_changed;
  bool m_running_reported{false};
  apex::optional<process::LogConfig> m_log_config;
  std::ofstream m_stdout_log_file;
  std::ofstream m_stderr_log_file;
  std::ofstream m_process_log_file;
};

}  // namespace process_group
}  // namespace process_manager
}  // namespace apex

#endif  // PROCESS_MANAGER__PROCESS_GROUP__PROCESS_HPP_
