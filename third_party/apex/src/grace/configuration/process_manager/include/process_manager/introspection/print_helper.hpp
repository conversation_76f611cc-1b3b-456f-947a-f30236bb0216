/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#ifndef PROCESS_MANAGER__INTROSPECTION__PRINT_HELPER_HPP_
#define PROCESS_MANAGER__INTROSPECTION__PRINT_HELPER_HPP_

#include <iostream>

#include "process_manager/config/types.hpp"
#include "process_manager/graph.hpp"
#include "process_manager/visibility.hpp"

/// \namespace apex
namespace apex
{
/// \namespace apex::process_manager
namespace process_manager
{
/// \namespace apex::process_manager::introspection
namespace introspection
{

PROCESS_MANAGER_PUBLIC
std::ostream & operator<<(std::ostream & stream, const IndexGraph & graph);

PROCESS_MANAGER_PUBLIC
std::ostream & operator<<(std::ostream & stream, const StartupConfig & config);

PROCESS_MANAGER_PUBLIC
std::ostream & operator<<(std::ostream & stream, const ProcessDependencyGraph & graph);

PROCESS_MANAGER_PUBLIC
std::ostream & operator<<(std::ostream & stream, const State & state);

PROCESS_MANAGER_PUBLIC
std::ostream & operator<<(std::ostream & stream, const Action & action);

PROCESS_MANAGER_PUBLIC
std::ostream & operator<<(std::ostream & stream, const ActionGraph & graph);

PROCESS_MANAGER_PUBLIC
std::ostream & operator<<(std::ostream & stream, const ActionGraphTable & action_graph_table);

PROCESS_MANAGER_PUBLIC
std::ostream & operator<<(std::ostream & stream, const ProcessConfig & config);

PROCESS_MANAGER_PUBLIC
std::ostream & operator<<(std::ostream & stream, const ProcessGroupConfig & config);

PROCESS_MANAGER_PUBLIC
std::ostream & operator<<(std::ostream & stream, const ProcessManagerConfig & config);

}  // namespace introspection
}  // namespace process_manager
}  // namespace apex

#endif  // PROCESS_MANAGER__INTROSPECTION__PRINT_HELPER_HPP_
