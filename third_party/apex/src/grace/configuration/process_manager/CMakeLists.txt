cmake_minimum_required(VERSION 3.5)

project(process_manager)

find_package(ament_cmake_auto REQUIRED)
set(CMAKE_CXX_STANDARD 17)
ament_auto_find_build_dependencies()

set(graph_src
    include/process_manager/visibility.hpp
    include/process_manager/graph.hpp
    src/graph.cpp)
ament_auto_add_library(graph_lib ${graph_src})
apex_set_compile_options(graph_lib)

set(config_src
    include/process_manager/visibility.hpp
    include/process_manager/config/types.hpp
    include/process_manager/config/process_action_map.hpp
    include/process_manager/config/action_graph.hpp
    include/process_manager/config/config_parser.hpp
    include/process_manager/config/parse/process_group_parser.hpp
    include/process_manager/config/parse/process_manager_parser.hpp
    include/process_manager/config/parse/process_parser.hpp
    include/process_manager/config/parse/startup_config_parser.hpp
    include/process_manager/config/parse/state_parser.hpp
    src/config/types.cpp
    src/config/process_action_map.cpp
    src/config/action_graph.cpp
    src/config/config_parser.cpp
    src/config/parse/process_group_parser.cpp
    src/config/parse/process_manager_parser.cpp
    src/config/parse/process_parser.cpp
    src/config/parse/startup_config_parser.cpp
    src/config/parse/state_parser.cpp
)
ament_auto_add_library(config_lib ${config_src})
apex_set_compile_options(config_lib)

set(process_manager_src
    include/process_manager/action/action_executor_node.hpp
    include/process_manager/action/action_executor_graph.hpp
    include/process_manager/process_group/process.hpp
    include/process_manager/process_group/process_group.hpp
    include/process_manager/process_group/process_group_node.hpp
    include/process_manager/process_group/exit_code_collector.hpp
    include/process_manager/process_group/event_monitor.hpp
    include/process_manager/process_group/state_transition.hpp
    include/process_manager/process_group/state_transition_item.hpp
    include/process_manager/process_group/process_group_runner.hpp
    include/process_manager/process_group/event_channel.hpp
    include/process_manager/process_group/event_communicator.hpp
    include/process_manager/process_group/essential_group.hpp
    include/process_manager/process_group/logging_macros.hpp
    include/process_manager/process_group/thread_liveliness_monitor.hpp
    include/process_manager/process_manager/process_manager_node.hpp
    include/process_manager/process_manager/heartbeat.hpp
    include/process_manager/process_manager/service_forwarder.hpp
    include/process_manager/process_manager/service_forwarder_node.hpp
    include/process_manager/process_manager/process_execution_state_monitor.hpp
    include/process_manager/process_manager/process_manager.hpp
    include/process_manager/process_manager/process_manager_factory.hpp
    include/process_manager/process_manager/group_event_forwarder.hpp
    include/process_manager/process_manager/group_thread_liveliness_monitor.hpp
    src/process_group/exit_code_collector.cpp
    src/process_group/reporter.cpp
    src/process_group/event_channel.cpp
    src/process_group/essential_group.cpp
    src/process_group/event_monitor.cpp
    src/process_group/state_transition.cpp
    src/process_group/state_transition_item.cpp
    src/process_group/process.cpp
    src/process_group/process_group.cpp
    src/process_group/process_group_node.cpp
    src/process_group/process_group_runner.cpp
    src/process_manager/process_manager_node.cpp
    src/process_manager/heartbeat.cpp
    src/process_manager/process_manager.cpp
    src/process_manager/service_forwarder.cpp
    src/process_manager/service_forwarder_node.cpp
    src/process_manager/process_execution_state_monitor.cpp
    src/process_manager/process_manager_factory.cpp
    src/process_manager/group_event_forwarder.cpp
    src/process_manager/group_thread_liveliness_monitor.cpp
    src/action/action_executor_node.cpp
    src/action/action_executor_graph.cpp)
ament_auto_add_library(process_manager_lib ${process_manager_src})
apex_set_compile_options(process_manager_lib)

set(introspection_src
    include/process_manager/visibility.hpp
    include/process_manager/introspection/print_helper.hpp
    src/introspection/print_helpers.cpp)
ament_auto_add_library(introspection_lib ${introspection_src})
apex_set_compile_options(introspection_lib)

ament_auto_add_executable(process_manager
    src/main.cpp
    $<$<BOOL:${APEX_CERT}>:param/process_manager_settings.hpp>
    $<$<BOOL:${APEX_CERT}>:param/process_manager_settings.cpp>)
target_include_directories(process_manager PRIVATE
    $<$<BOOL:${APEX_CERT}>:${CMAKE_CURRENT_SOURCE_DIR}/param>)
apex_set_compile_options(process_manager)
if(QNX)
  target_link_libraries(process_manager "c++fs")
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" AND CMAKE_CXX_COMPILER_VERSION VERSION_LESS 9.0)
  target_link_libraries(process_manager "stdc++fs")
endif()

if(NOT APEX_CERT)
    # Exclude the binary from Cert builds
    # due to settings_extensions dependency
    ament_auto_add_executable(introspection
        src/ui/cli_introspection_main.cpp)
    apex_set_compile_options(introspection)
endif()

ament_auto_add_executable(get_status
        src/ui/cli_get_status_main.cpp)
apex_set_compile_options(get_status)

ament_auto_add_executable(change_state
        src/ui/cli_change_state_main.cpp)
apex_set_compile_options(change_state)

ament_auto_add_executable(restart
        src/ui/cli_restart_main.cpp)
apex_set_compile_options(restart)

ament_auto_add_executable(minimal_process
        src/executables/minimal_process.cpp)
apex_set_compile_options(minimal_process)

ament_auto_add_executable(dummy_process
        src/executables/dummy_process.cpp)
apex_set_compile_options(dummy_process)

ament_auto_package()
