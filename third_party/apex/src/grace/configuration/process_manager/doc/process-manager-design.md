---
tags:
- Bazel
- Colcon
- process_manager
---

# process_manager

This is the documentation for the `process_manager` package.

## Purpose
<!-- Required -->
<!-- Things to consider:
    - Why does this package exist?
    - What is its purpose? -->

The purpose of the Process Manager is to start, stop and monitor processes.

Previously the Apex.Grace`launcher` provided a simple way to start a set of
child processes. However, in some case there is need to change states
inside an ECU, so it is required to start, stop and restart processes.

The `process_manager` was created to fulfill these requirements. It allows defining a group of
processes, so that their execution can be controlled together. It allows you to define the
start-up or shutdown dependencies between them, so they are started and stopped in a pre-defined
order. Additionally, it provides an external interface using services to control process group
execution remotely.

## Design

### Architecture

The main class of the `process_manager` is [](apex::process_manager::ProcessManager).
This class contains one or more process group nodes [](apex::process_manager::process_group::ProcessGroupNode).
Each process group instantiates some interfaces to report their status and wait for requests to
change the state of processes.

A process group state is configured with a set of processes to run in that state. A state
transition will execute a sequence of actions in a pre-defined order to start and stop processes
as specified in the configuration file.

![Process Manager architecture](ProcessManager.png){ .center }

### Process group state

The user can define multiple states for a process group. Each state characterizes the current
status of a set of processes. A state defines which processes are running, which start-up
configuration should be used to start a process, and the order in which they have to be started
and stopped.

A special built-in `OFF` state exists to define the state in which no processes are running.
This is a reserved state name, and it is not possible to define a state using this name. By
default, this is the initial state for a process group.

### Process execution state

A process group keeps track of the execution state of its processes. The following states are
defined to characterize the process lifecycle:

- `IDLE`: The state of a process before it is started. This is the expected state for all the
  processes when a process group is in the `OFF` state. When a process is terminated and the process
  group state is `OFF` its state is set to IDLE. Before starting a process its state is
  set to `IDLE`.
- `STARTING`: The process has been created successfully, and it is being initialized. In this
  state the process has a `PID` but it has not reported it is running by reporting its execution
  state.
- `RUNNING`: The process initialization is complete, and it has reported it is running and the
  process is being monitored.
- `TERMINATING`: The process is being terminated. A `SIGTERM` or `SIGKILL` signal has been sent.
- `TERMINATED`: The process has exited and the exit status information is available.

```mermaid
flowchart LR
   IDLE -- start --> STARTING -- process ready --> RUNNING -- stop --> TERMINATING -- wait for exit --> TERMINATED
```

!!! note
    `IDLE` and `TERMINATED` are similar in the sense no process is running. However, in the
    `TERMINATED` state there is information about the exit status of the process.

### State Transition

A state transition is a combination of start and stop actions which are executed in a pre-defined
order to comply with the origin state shutdown dependencies and the target state startup
dependencies.

Each state transition is implemented as an executable graph containing a sequence of `Start` and
`Stop` actions as nodes.

For more information about how a process group state transition works and how it is
defined refer to [State transition graph](state-transition-graph.md).

!!! note
    The term 'action' used in this context means a routine responsible for starting or stopping a
    process. It is not related to ROS 2 actions.

### Essential group

The essential group is a special process group intended to launch essential processes which are
required by Apex.Grace framework.

Essential processes are processes which are not expected to be shut-down or changed after the
system start-up. For example, communication, or initialization routines to run
during the machine start-up. In most cases, this group only contains the Ida `resource_creator`
process.

The main difference between a normal process group and the essential group is that it does not
rely on middleware communications being available to manage the processes. The reason is,
middleware communications may require one or several processes to be running.

In general the essential group can be configured as a normal process group with some exceptions.
Here are some relevant differences between the essential group and a normal group:

- It is not possible to change the state of the group. The only state transition allowed is the
  init-state transition
- There is no node assigned to the group. This means, the group does not report any state
  through topics or services. This information is reported by file or console logging
- [`LogTopic`](process-manager-design.md#stream-output-logging)
  option is not supported for `stdout-handlers`, `stderr-handlers` and
  `group-log-handlers`
- [Dispatcher events](process-manager-design.md#dispatcher-event-sender)
  are not reported by the essential group
- [`on-failure-behavior`](process-manager-design.md#on-failure-behavior)
  is fixed to `Fatal`
- [`ByTopic`](process-manager-design.md#execution-report-type) report type is not supported

As explained above, the essential group itself cannot rely on communications being enabled.
However, it is still possible for a process running as part of the essential group to rely on them.
This is possible by setting the proper process dependencies and making sure this process is
launched after those processes which enable the communications.

Defining an essential group is not mandatory. When using the `process_manager` Bazel rule,
if no essential group is used, a built-in default essential group is used.
See [Default essential group](how-to-use-process-manager-with-bazel.md#default-essential-group)

For more information about how to configure an essential group, refer to
[Essential group example](example-essential-framework-group.md).

### Framework group

The framework group is a special process group intended to launch framework level
processes such as:

- [`system_manager`](system-manager-design.md)
- [event dispatcher](dispatcher-design.md)
- [`apex_ecu_monitor`](apex-ecu-monitor-design.md)
- [storage](storage-design.md)
- etc.

This group, which is optional, if it is defined it is started after the essential group and before
the rest of process groups. When the Process Manager terminates, the framework group is terminated
after the other process groups and before the essential group.

## Usage
<!-- Required -->
<!-- Things to consider:
    - Place to insert code snippets
    - Explain step by step how someone should use the package -->

### Running process_manager

See the example for a minimal launch settings file:

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_minimal.launch.yaml',
{'tag': '# [launch]', "skip_prefix": "#!"},
'yaml') }}

1. Key of the Process Manager dictionary
2. List of process groups
3. Name of a process group
4. Set the [initial process group state](#init-state)
5. List of processes of a process group
6. Name of a process
7. Start-up configuration for a process. This is the default configuration used in all the
   process group states.
8. List of process group states
9. Name of a process group state
10. List of processes to run in a process group state
11. Name of a process to run in a process group state

<!-- markdownlint-disable MD046 -->

To use `process_manager` to launch the minimal process example, run the following command
in the terminal.

=== "Bazel run"

    ```shell dollar
    $ bazel run //grace/examples/process_manager_examples:example_minimal
    ```

=== "ADE"

    ```shell ade
    (ade)$ ros2 run process_manager process_manager --apex-settings-file \
        /opt/ApexGraceBilbo/share/process_manager_examples/launch/example_minimal.launch.yaml
    ```

Note the presence of the additional `/group1` node. This is the process group node
corresponding to "group1".

The following information should be logged in the terminal:

```shell ade
RMW Implementation: rmw_apex_middleware[test_process_manager.cpp]
[2023-05-25 17:58:56] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/core/process_manager/process_manager/src/process_group/state_transition.cpp @  L65:
Change state from  OFF  to  ON
[2023-05-25 17:58:56] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/core/process_manager/process_manager/src/action/action_executor_node.cpp @  L48:
Process IDLE:  minimal_process
[2023-05-25 17:58:56] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/core/process_manager/process_manager/src/action/action_executor_node.cpp @  L51:
Process STARTING:  minimal_process
[2023-05-25 17:58:56] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/core/process_manager/process_manager/src/process_group/event_monitor.cpp @  L340:
Process RUNNING:  minimal_process
[2023-05-25 17:58:56] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/core/process_manager/process_manager/src/process_group/state_transition.cpp @  L148:
State transition completed: from  OFF  to  ON
```

To shut down the process group from the example, run the following command in a different terminal:

```shell ade
(ade)$ ros2 service call /process_group_change_state process_manager_interfaces/srv/ChangeState \
    "{process_group_name: 'group1', requested_state: 'OFF'}"
RMW Implementation: rmw_apex_middleware[test_process_manager.cpp]
[2023-05-25 18:00:48] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/core/process_manager/process_manager/src/process_group/state_transition.cpp @  L65:
Change state from  ON  to  OFF
[2023-05-25 18:00:48] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/core/process_manager/process_manager/src/action/action_executor_node.cpp @  L83:
Process TERMINATING:  minimal_process
[2023-05-25 18:00:49] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/core/process_manager/process_manager/src/process_group/event_monitor.cpp @  L383:
Process TERMINATED:  minimal_process
[2023-05-25 18:00:49] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/core/process_manager/process_manager/src/process_group/state_transition.cpp @  L148:
State transition completed: from  ON  to  OFF
```

#### Process Manager start-up and shutdown

In general a process group will execute a state transition only when an external request is
received. For those cases where it is required to automatically start some processes it is
possible to define an initial state, see [Init state](#init-state). The process group will
self-transition to this state when the Process Manager is started.

Before shutting down the Process Manager, all the process groups transition to the `OFF` state
depending on the [process dependencies](#process-dependencies).

## Configuration YAML file

The input file format is YAML. The configuration file is parsed by the means of
the [Apex.Grace settings API](settings-design.md).

The Process Manager settings should be under a dictionary named `process-manager` in the
YAML file.

### Configuration options

In the following snippet there is an example YAML file showing all the configuration options
available:

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_configuration.launch.yaml',
{'tag': '# [launch]'},
'yaml') }}

1. Name of a process group
2. Process group initial state. (Optional) (Default: `OFF`)
3. Minimum interval between sending reports in milliseconds (Optional) (Default: `1000`)
4. Behavior of the process group if a failure occurs (Optional) (Default: `DoNothing`)
5. If true the process is expected to self-terminate (Optional) (Default: `False`)
6. Define how a process reports its running execution state (Optional) (Default: `None`)
7. Maximum allowed duration in milliseconds until a process is running since it is started
   (Optional) (Default: `5000`)
8. Maximum allowed duration in milliseconds until a process terminates since a SIGTERM signal
   is sent (Optional) (Default: `5000`)
9. Maximum allowed duration in milliseconds until a process terminates since a SIGKILL signal
   is sent (Optional) (Default: `5000`)
10. Define how a process `stdout` stream is logged (Optional) (Default: [])
11. Define how a process `stderr` stream is logged (Optional) (Default: [])
12. Start-up configuration for a process. This is the default configuration used in all the
    process group states. (Optional if it is defined for each state)
13. The full path to the executable file
14. The array of the command line arguments to pass to the process (Optional)
15. The array of the environment variables to pass to the process (Optional)
16. Start-up configuration for a process defined specifically for a state. This is the
    configuration used by the process in this state. The default start-up configuration
    values might be overridden or extended by this entry (Optional)
17. Define the dependencies between processes (Optional)
18. Enable logging process streams to files
19. Set the root of logging directory. Must be non-empty if file logging is enabled.
    (Optional) (Default: `""`)
20. If true, enables buffering when logging to file (Optional) (Default: `true`)
21. If true, creates a log file directory with timestamp as a suffix (Optional) (Default:
    `false`)
22. Define the real-time priority for the process (Optional) (Default: 0)
23. Define the real-time scheduling policy for the process (Optional) (Default: `other`)
24. Define the CPU affinity mask for the process (Optional) (Default: 0)
25. Override the default path (Optional)
26. Override the default arguments (Optional)
27. Override the default environment variables (Optional)
28. The extra arguments are appended to the default arguments (Optional)
29. The extra environment variables are appended to the default
    environment variables (Optional)
30. Maximum allowed duration in milliseconds until the dispatcher event subscription is matched
    The wait can be disabled with a -1 or any other negative value (Optional) (Default: 5000)
31. Define if the process is allowed to terminate by SIGTERM signal instead of by graceful termination.
    (Optional) (Default: false)
32. Set the logging level for the process group logs (Optional) (Default: INFO)
33. Define a command to add as a prefix before the executable path. (Optional)
34. Entry to define the essential group configuration
35. Define how a process group log is actually logged (Optional) (Default: [`Console`] for
    essential group, [`LogTopic`] for a normal process group)
36. Prefix to prepend to services and topics (Optional) (Default: No prefix)
37. Maximum allowed simultaneous requests for services (Optional) (Default: 64)
38. Entry to define the framework group configuration
39. Enables the monitoring of internal threads liveliness monitoring (Optional)
40. Period for Process Manager monitored thread liveliness servicing
41. Period for checking the thread liveliness in milliseconds
42. Period for sending a heartbeat event in milliseconds
43. Defines if a process streams (stdout/stderr) shall be redirected and how (Optional)
    (Default: `RedirectToProcessManager`)

#### Init state

The entry named `init-state` defines the initial process group state. The process
group will self-transition to this state when the Process Manager application starts.

The default value is `OFF` which is the reserved process group state with no processes running.

#### Minimum publishing interval

The entry named `stream-min-publish-interval-ms` defines the minimum interval
between sending reports in milliseconds. The default value is `1000`.

!!! note
    This value is applied only to reports which concern stream writes
    and **does not** affect reports about changes in process states. Such reports
    are always sent immediately.

#### On failure behavior

The entry named `on-failure-behavior` defines the behavior of the process group when there is a
failure in a state or in a state transition. The default value is `DoNothing`.

These are the options available:

- `DoNothing`: Do nothing to handle the failure is expected to be handled
   externally by calling a service
- `StopAllProcesses`: Stop all processes and go to `OFF` state
- `Fatal`: Any error is considered non-recoverable and the state is fatal

!!! warning
    `StopAllProcesses` will stop processes only when there is a failure in a state
    transition, not when the group is in a fixed state

#### Log level

The entry named `group-log-level` configures the log level for the process group node.
This option is useful to increment or reduce the verbosity for all the ongoing activity in
the process group i.e. process lifecycle, state transitions, errors, etc.

For example when debugging a failure in one process group it might be helpful to reduce the log
level for a specific process group and increment it for another one.

!!! note
    This option does not configure the log level for the processes belonging to that process group.
    The log level for each process must be configured individually

#### Group log handlers

The entry named `group-log-handlers` configures how group activity is logged (i.e. process
lifecycle events, errors, etc.).
This option is useful to increment or reduce the verbosity for all the ongoing activity in
the process group i.e. process lifecycle, state transitions, errors, etc. Note, this is an array,
so multiple simultaneous logging methods are allowed.

These are the options available:

- `Console`: log to the terminal. This option is disabled in Apex.Grace Cert
- `LogTopic`: logged using [Apex.Grace logger](logging-design.md). This option is not allowed
  for the essential group
- `LogFile`: Log the output stream to a file. If the log directory is empty the `LogFile` is
   disabled

By default, for a normal process group, `LogTopic` is set. For the essential group, `Console`
is set.

#### Dispatcher event sender

A process group can publish [events](event-design.md) in order to report
some relevant process lifecycle events.

A process group will wait until the event dispatcher subscription is matched. The wait can be
changed using the `wait-for-dispatcher-ms` entry. A zero or a negative value
disables sending of any events from the Process Manager, including the heartbeat.

This is a list of events published by Process Manager:

- `ProcessUnexpectedGracefulExit`: reports when a process is terminated unexpectedly but the process
  exits gracefully with an exit code 0.
- `ProcessUnexpectedAbnormalExit`: reports when a process is terminated unexpectedly with an exit
  code different to 0. This could be caused by a process crashing or exiting with an error.
- `ProcessAbnormalExit`: reports when a process is terminated by the Process Manager but the exit
  code is different to 0.
- `ProcessUnexpectedGracefulExitFrameworkGroup`: same as `ProcessUnexpectedGracefulExit` but for a
  process inside a Framework Group
- `ProcessUnexpectedAbnormalExitFrameworkGroup`: same as `ProcessUnexpectedAbnormalExit` but for a
  process inside a Framework Group

All these events include information about the process PID and the process name
given by the Process Manager.

#### Allow a process to exit by SIGTERM signal

In most cases, a process is expected to terminate with a graceful shutdown, that is, the process
handles the SIGTERM signal and shutdowns the process gracefully by terminating pending tasks and
returning a 0 exit code. If the process is not gracefully shutdown, Process Manager will show an
error and will consider that the state transition failed.

There are some cases where this is not possible because the process does not handle the SIGTERM
signal (i.e. a 3rd party process). In these cases, it is possible to allow a termination by
a SIGTERM signal by using setting the `allow-sigterm-exit` entry to true. The default
value is `false`.

#### Self-terminating processes

The entry named `is-self-terminating` defines whether the process of type 'Self-terminating'
and it is expected to terminate by its own after it is started. The default value is `false`.

By design, a self-terminating process can be started in the state transition start-up phase. For
this reason, it is not possible to define shutdown dependencies for self-terminating processes.
An error will be generated if a self-terminating process is included in `direct-dependencies` or
`shutdown-dependencies`.

For the start-up phase it works in a similar way as a normal process. The only difference is
the process start action includes a wait for the process termination. That is, staring a
self-terminating process includes the wait for the process start and the process termination. The
`sigterm-timeout-ms` wait time is applied for the mentioned wait.

Because the self-termination has to happen as part a state transition phase, self-terminating
processes are expected to terminate in a reasonably small amount of time (i.e. a few seconds).
Some common use cases are processes to initialize hardware devices, checker processes, clean-up
routines, etc.

See [Self-terminating example](example-self-terminating-process.md) for a usage example.

#### Execution report type

!!! warning
    This option is still under development and should be used only for experimental purposes.

The entry named `execution-report` defines how a process reports that it is ready and the
status should be changed to `RUNNING`. The default value is `None`.

These are the options available:

- `None`: the process does not report its execution state
- `ByStdoutStream`: the process reports its execution state with a `stdout` stream output (experimental)
- `ByStderrStream`: the process reports its execution state with a `stderr` stream output (experimental)
- `ByTopic`: the process reports its execution state by a pre-defined topic

When `None` is set, the process is not expected to report its status and the Process Manager will
set its state to `RUNNING` as soon as the process is started and is being monitored by the process
manager. Monitored in this context means monitoring its output streams and exit status.

When the `ByTopic` option is enabled, the Process Manager waits for a
`process_manager_interfaces::msg::ProcessRunning` message on the `/process_running` topic.

{{
code_snippet("grace/interfaces/process_manager_interfaces/msg/ProcessRunning.idl",
"idl") }}

In order to avoid boilerplate code, the user can use an [](apex::event::monitored_process) instance.
See [Post init message](event-design.md#sending#the#post#init#message).

!!! warning
    `ByStdoutStream` and `ByStderrStream` options shall not rely on one single output stream at
    the process start-up. The stream might be printed when the process streams are not yet
    being monitored.

#### Start-up timeout

The entry named `startup-timeout-ms` defines the maximum allowed duration in milliseconds until a
process state is `RUNNING` since it is started. The default value is `5000` milliseconds.

When there is a timeout starting a process an error will be set.

The start-up timeout considers the time between when we request the system to create the process
(`STARTING`) and when the process is in the "RUNNING" state.

Note that by default, `execution-report` option is set to `None`, which means the process is
considered to be in the "RUNNING" state as soon as the system creates the process.

When the `execution-report` type is other than `None`, the start-up time will include all of the
time from when the process is started until the process reports it is in the `RUNNING` state.

#### SIGTERM timeout

The entry named `sigterm-timeout-ms` defines the maximum allowed duration in milliseconds a process
can take to terminate once a `SIGTERM` signal is sent. The default value is `5000`
milliseconds.

If a process does not exit within this time, an error will be set and `SIGKILL` will be sent to
kill the process.

#### SIGKILL timeout

The entry named `sigkill-timeout-ms` defines the maximum allowed duration in milliseconds a process
can take to terminate once a `SIGKILL` signal is sent. The default value is `5000`

When there is a timeout killing a process the error will be reported.

#### Stream output redirection

The `stream-redirection` entry defines how a process's streams (stdout/stderr) shall be redirected.
This is important because different use cases may require different handling of these streams.

For example, some processes may need their output to be logged for debugging purposes, others
may need their output to be discarded to avoid cluttering the terminal, and others may need to
redirect the streams to the Process Manager. The Process Manager can handle the `stdout` and
`stderr` streams according to the `stdout-handlers` and `stderr-handlers` configuration,
allowing it to control the process output by disabling it or logging it in different ways.

These are the options available:

- `DoNotRedirect`: The process streams are not redirected. The process will handle its own stdout
   and stderr streams. A child process inherits the parent stream redirection by default when it
   is created
- `RedirectToProcessManager`: The process streams are redirected to the Process Manager
- `RedirectToDevNull`: The process streams are redirected to
   [`/dev/null`](https://en.wikipedia.org/wiki/Null_device). This means the output is discarded
- `RedirectToLogFile`: The process streams are redirected to a log file. The log file path must be
   specified in `logs-directory` the configuration and logging must be enabled with
   `log-file-enable`

Note `stdout-handlers` and `stderr-handlers` entries define only make sense when the streams are
redirected to the Process Manager when using `RedirectToProcessManager`.

!!! note
    The [system_manager](system-manager-design.md) shall not use `RedirectToProcessManager` to avoid
    a broken pipe termination if the Process Manager terminates unexpectedly. See
    [Essential group example](example-essential-framework-group.md)
    for the System Manager configuration example.

#### Stream output logging

The entries named `stdout-handlers` and `stderr-handlers` define how a process output streams
`stdout` and `stderr` are logged respectively when `RedirectToProcessManager` is set.
Note, this is an array, so multiple simultaneous logging methods are allowed. By default, there
are no logging outputs enabled.

These are the options available:

- `Console`: log output stream to the terminal. This option is disabled in Apex.Grace Cert
- `LogTopic`: log output stream using [Apex.Grace logger](logging-design.md). Each stream will
  be published as a [`ContextLogMessage`](apex-msgs-design.md#contextlogmessage) adding the
  process name in a `ContextField` with `process` as a key.
- `LogFile`: Log the output stream to a file. If the log directory is empty the `LogFile` is
  globally disabled for all the processes, see [File logging](#file-logging).
- `LogFileCombined`: Log both `stdout` and `stderr` streams to the same log file for each process.
  Adding this to a specific stream (e.g., `stdout`) enables logging that stream to the file.
- `ProcessGroupState`: Publishes the output stream as part of the [Process group info topic](#process-group-info-topic)
  `/process_group_state`. This option has been deprecated. Enabling this option no longer adds the
   stream output to this topic.

!!! warning
    `ProcessGroupState` has been deprecated

#### File logging

The entry `log-file-enable` enables file logging for process streams. The entry named
`logs-directory` holds the root path for storing the stream logs of the running
processes. Inside this directory, the Process Manager will create a directory for each process
group named as the process group name. Inside each process group directory a log file is
created for `stdout` and `stderr` streams with the process name as a prefix.
Every stream log of the related run is going to be under this directory. Thus, the full path of a
log file will look like:

```shell
['logs-directory' content]/[process group name]/[process name].[stdout|stderr]
```

For the `LogFileCombined` option the log file will be named as the process name with the suffix
`.log`. Thus, the full path of a log file will look like:

```shell
['logs-directory' content]/[process group name]/[process name].log
```

For example, in the case of [Logging example](example-logging.md), it will result in the
following log directory content:

```shell
tree my_logs
my_logs
├── essential_group
│         ├── essential_group.log
│         ├── resource_creator.stderr
│         └── resource_creator.stdout
├── framework_group
│         ├── framework_group.log
│         ├── framework_process1.log
│         ├── framework_process1.stderr
│         └── framework_process1.stdout
├── group1
│         ├── group1.log
│         ├── minimal_process1.stderr
│         ├── minimal_process1.stdout
│         ├── minimal_process2.log
│         ├── minimal_process3.stderr
│         └── minimal_process3.stdout
└── group2
    └── group2.log
```

!!! note
    If the logging directory exists, and it is not empty it will result in an error

Additionally, if the `append-timestamp-to-log-file-name` entry is set to true, a new directory
will be created with a timestamp as a suffix. In this case the last directory name from
`logs-directory` is used a name prefix for the logging directory root. Does not have an effect
if logs-directory is not specified. Thus, the full path of a log file will look like:

```shell
['logs-directory' content]-[Y]-[m]-[d]-[H]-[M]-[S]/[process group name]/[process name].[stdout|stderr]
```

For example, for the example above, setting `append-timestamp-to-log-file-name` to true will
result in the following log directory content:

```shell
tree ~/my_logs
my_logs
└── process_manager-2023-09-20-12-05-33
    ├── group1
    │         ├── minimal_process1.stderr
    │         ├── minimal_process1.stdout
    │         ├── minimal_process2.stderr
    │         └── minimal_process2.stdout
    └── group2
        ├── minimal_process3.stderr
        ├── minimal_process3.stdout
        ├── minimal_process4.stderr
        └── minimal_process4.stdout
```

Finally, `log-file-buffering` allows to enable or disable log files buffering and thus force
flushing on each write call. By default, the buffering is enabled. Does not have an effect if
`logs-directory` is not specified.

#### Process start-up configuration

The entry named `default-startup-config` defines the default configuration options used to
start-up a process.

The following entries are available

- `path`: the full path to the executable file
- `args`: the array of the command line arguments to pass to the process
- `env-vars`: the array of dictionaries; each dictionary should have exactly one
  element. Each element represents a name and a value of an environment variable.
- `rt-settings/proc_prio`: the real-time priority used by the OS scheduler. The same priority is
  given to each thread of the process
- `rt-settings/scheduler`: selects the process scheduler policy. The options available are:
  `fifo`, `round_robin` and `normal` (See `apex::threading::scheduler`). `other` is also
  accepted and it is the equivalent to `normal`.
- `rt-settings/proc_cpu_mask` or `rt-settings/proc_cpu_list`: selects the process CPU affinity.
- `prefix`: allows to run the executable under another tool. This is useful for debugging or
  profiling purposes. Some use cases might be using gdbserver, valgrind, perf, heaptrack, etc

!!! note
    In order to preserve the compatibility with `apex_init` style both `rt_settings` and
    `rt-settings` are accepted

The `rt-settings` parameters are the same ones used by `apex_init`, refer to
[apex_init](apex-init-design.md#configure-real-time-settings) for more details about how to use
`rt-settings`. Note it is possible to use simultaneously `rt-settings` from `apex_init` and in the
Process Manager. The Process manager will apply `rt-settings` when starting the process and
`apex_init` applies these settings once the process is started inside the process. Therefore,
`apex_init` may override the configuration set by the Process Manager.

The default start-up configuration can be set using
`default-startup-config`. This is the configuration used in all the states unless it is
overridden by a specific start-up configuration specific for that state.

Every started process is additionally set the following environmental variables:

- `__APEX_PM_GROUP_NAME__` -- the group name the process belongs to
- `__APEX_PM_PROCESS_NAME__` -- the logical process name as set in the config
- `__APEX_PM_STABLE_PROCESS_ID__` -- a stable process id that only changes if the config file changes

These names are reserved and cannot be used explicitly in the config file.

#### States

The value of array named states defines the list of the states of the process group. Every element
of this array is a dictionary containing the following entries:

- `name`: name of the state. Note this is user defined except for the reserved state name `OFF`.
  The name is limited to 255 characters and cannot be empty.
- `processes`: list of processes that are expected to be running in this state. Additionally, it
  is possible to specify a start-up configuration specific for this state.
- `process-dependencies`: defines the dependencies between processes for this state

In each state, an entry named `startup-config` will modify the default start-up configuration
either by overriding the default value or extending the existing values.

Here is a list of the options available under the `startup-config` entry:

- `path`: if exists, it overrides the default path
- `env-vars`: if exists, it overrides the default environment variables
- `args`: if exists, it overrides the default arguments
- `extra-args`: if exists, the extra arguments are appended to the default arguments
- `extra-env-vars`: if exists, the extra environment variables are appended to the default
  environment variables
- `rt-settings`: if any of the entries under `rt-settings` exists, it will override that entry

#### Process dependencies

In each state it is possible to define dependencies between processes. There are start-up and
shutdown dependencies, each one will determine the order in which the processes are started and
stopped for state transition sequence.

A process `A` having a start-up dependency with process `B` means that process `B` will start
before process `A`.

A shutdown dependency works in reverse order. That is, process `A` having a shutdown
dependency with process `B` means that process `A` will be terminated before process `B`.

In order to define the start-up and shutdown dependencies the following options are available:

- `direct-dependency`: defines start-up and shutdown dependencies
- `startup-dependency`: defines start-up dependencies
- `shutdown-dependency`: defines shutdown dependencies

`startup-dependency` and `shutdown-dependency` can be used when a process has different
start-up and shutdown dependencies. Otherwise, `direct-dependency` can be used to set both
dependency types at the same time.

Here is an example showing different options:

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_dependencies.launch.yaml',
{'tag': '# [process-dependencies]'},
'yaml') }}

The dependencies defined above result in the following dependency graphs.

Process startup dependencies:

```mermaid
graph TD
 n0(D)
 n1(C) --> n0(D)
 n2(B) --> n3(A)
 n3(A)
```

Process shutdown dependencies:

```mermaid
graph TD
 n0(D) --> n1(C)
 n1(C)
 n2(B) --> n3(A)
 n3(A)
```

!!! note
    The order in which processes are actually started and stopped in each state transition
    depend on the combination of the shutdown dependencies of the origin state and start-up
    dependencies of the target state. See [State transition graph](state-transition-graph.md)

!!! note
    In some cases a process has to wait for other process (i.e. waiting for a publisher
    subscription-matching). In these cases it is preferable not to rely on process dependencies
    if it is not strictly necessary. Dependencies between processes should be used only for those
    cases where a process really need to start or stop before another.

#### Compatibility with `launcher` launch files

It is possible to launch old launch files with the Process Manager. The launch file will be
parsed and will implicitly create a process group node with an `ON` state with all the processes
in the launch file. The process group is configured to set the init state to `ON` to make the
behavior consistent with `launcher`.

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_launcher.launch.yaml',
{'tag': '# [launch]'},
'yaml') }}

!!! note
    Since `launcher` launch files processes do not set any names for the processes, some
    predefined names are used based on the array index: `launcher_process0`, `launcher_process1`,
    etc

#### Sets user ID and group ID of a process

It is possible to launch a process with a user ID and group ID different from the process
manager's. Currently, this is possible if the set-user-ID mode bit (`setuid`) of the process image
file is set (e.g. using  `chmod u+s` in Linux). If that is the case, the process will be launched
with the user ID and group ID from the process image instead of just inheriting the ones from the
Process Manager.

!!! note
    When `setuid` is used, in some systems such as Linux, `LD_LIBRARY_PATH` will be ignored by the
    [dynamic loader (`ld`)](https://man7.org/linux/man-pages/man8/ld.so.8.html) for security reasons

#### Process Manager instance

The `process-manager-instance` entry allows to define a prefix for topics and services created by
the Process Manager.

This is useful to create different instances of the Process Manager and avoid service names
conflicts.

By default, not prefix is used.

#### Maximum allowed service requests

The `max-simultaneous-requests` entry allows to configure the maximum allowed simultaneous
requests allowed for the Process Manager services.

If the amount of simultaneous requests is exceeded the request is rejected.

The reason this setting exists, is because the Process Manager forward requests to each group and
needs to keep track of ongoing service requests.

The default is 64. It should not be necessary to change this value.

#### Heartbeat

The `heartbeat-period-ms` entry allows to enable the Process Manager heartbeat mechanism to
report its liveliness.

The heartbeat sending is enabled when the value is higher than zero. It is disabled otherwise.
When enabled, the Process Manager sends a heartbeat event to the event dispatcher to report it is
alive. This is a safety mechanism to detect when the Process Manager is not
responding, or when the Process Manager terminates unexpectedly.

If the
`wait-for-dispatcher-ms` entry is set to zero or to a negative value the heartbeat mechanism is
disabled because this implies there is no event dispatcher in the system and therefore there is no
need to send event messages.

#### Group thread liveliness monitoring

The `group-threads-liveliness-monitoring` entry allows to enable the monitoring of internal
Process Manager threads to detect if they are still alive or not responding. This is a safety
mechanism to report if there is an internal problem in the Process Manager.

The `heartbeat` entry is a dictionary with the following entries:

- `thread-liveliness-report-period-ms`: period for Process Manager monitored thread liveliness
   servicing
- `liveliness-check-period-ms`: period for checking the thread liveliness in milliseconds

The purpose of this mechanism is to detect when a monitored thread is not responding. The Process
Manager sends an event to the event dispatcher when a monitored thread is not responding. This
ensures that the Process Manager reports if there is a problem with the monitored threads. The
threads that monitor the managed processes in order to detect their termination are considered
critical and, in turn, are monitored by the Process Manager.

For example, given the following configuration:

```yaml
  group-threads-liveliness-monitoring:
    thread-liveliness-report-period-ms: 200
    liveliness-check-period-ms: 1000
```

Each monitored thread will report its liveliness at every `thread-liveliness-report-period-ms`
(200 milliseconds). The monitored threads liveliness is checked every `liveliness-check-period-ms`
(1000 milliseconds). If the Process Manager detects a thread is not responding, it sends an event
reporting there was a failure.

If the `wait-for-dispatcher-ms` entry is set to zero or to a negative value, this mechanism is
disabled because this implies there is no event dispatcher in the system and therefore there is no
need to send event messages.

### Examples

More launch file examples are available at
[Process Manager examples](process-manager-examples.md).

## Interfaces

The Process Manager uses topics and services in order to report information and execute
state transition requests.

Each process group is a node which has its own subscribers, publishers and services. All the
node use the pre-defined name and the target process group is specified as part of a message or
service field.

### Change process group state service

The `process_manager_interfaces::srv::ChangeState` service allows to request a state transition
for a specific process group

The name of the service is `/process_group_change_state`.

{{
code_snippet("grace/interfaces/process_manager_interfaces/srv/ChangeState.idl",
"idl") }}

Usage example:

```shell ade
(ade)$ ros2 service call /process_group_change_state process_manager_interfaces/srv/ChangeState \
    "{process_group_name: 'group1', requested_state: 'ON'}"
```

The previous command shows the result of the service request, something similar to the
snapshot below:

```yaml
requester: making request: process_manager_interfaces.srv.ChangeState_Request(process_group_name='group1', requested_state='ON')

response:
process_manager_interfaces.srv.ChangeState_Response(return_code=<ReturnCodeType.OK: 1>, message='State transition completed successfully')
```

### Get process group info service

The  `/process_group_get_state` service allows to request the state
of a process group.

The service type is `process_manager_interfaces::srv::GetProcessGroupInfo`:

{{
code_snippet("grace/interfaces/process_manager_interfaces/srv/GetProcessGroupInfo.idl",
"idl") }}

Usage example:

```shell ade
(ade)$ ros2 service call /process_group_get_state process_manager_interfaces/srv/GetProcessGroupInfo \
    "{process_group_name: 'group1'}"
```

The previous command shows a process group information, something similar to the snapshot below:

```yaml
requester: making request: process_manager_interfaces.srv.GetProcessGroupInfo_Request(process_group_name='group1')

response:
process_manager_interfaces.srv.GetProcessGroupInfo_Response(process_group_info=process_manager_interfaces.msg.ProcessGroupInfo(process_group_name='group1', src_info=apex_msgs.msg.AppInfo(host_name='ade', exec_name='process_manager', node_name='group1'), state='ON', error=<ErrorType.OK: 0>, in_transition=False, process_info=[process_manager_interfaces.msg.ProcessInfo(name='B', state=<ProcessStateType.RUNNING: 2>, error=<ProcessErrorType.OK: 0>, path='/opt/ApexGraceBilbo/lib/process_manager_examples/minimal_process', has_pid=True, pid=4093385, has_exited=False, has_exit_code=False, exit_code=0, has_exit_signal=False, exit_signal=0, stream_out='', stream_err='vicente/gc/apex_ws/src/grace/execution/apex_init/src/apex_init.cpp @  L239:\nInitializing root logger.\n[2023-05-24 15:49:29] [INFO] B | /home/<USER>/gc/apex_ws/src/grace/examples/process_manager_examples/src/minimal_process.cpp @  L46:\nB : Hello world!\n'), process_manager_interfaces.msg.ProcessInfo(name='A', state=<ProcessStateType.RUNNING: 2>, error=<ProcessErrorType.OK: 0>, path='/opt/ApexGraceBilbo/lib/process_manager_examples/minimal_process', has_pid=True, pid=4093386, has_exited=False, has_exit_code=False, exit_code=0, has_exit_signal=False, exit_signal=0, stream_out='', stream_err='vicente/gc/apex_ws/src/grace/execution/apex_init/src/apex_init.cpp @  L239:\nInitializing root logger.\n[2023-05-24 15:49:29] [INFO] A | /home/<USER>/gc/apex_ws/src/grace/examples/process_manager_examples/src/minimal_process.cpp @  L46:\nA : Hello world!\n')]))
```

### Process group info topic

The `/process_group_state` topic reports the state of a process group as a
`process_manager_interfaces::msg::ProcessGroupInfo`:

{{
code_snippet("grace/interfaces/process_manager_interfaces/msg/ProcessGroupInfo.idl",
"idl") }}

Usage example:

```shell ade
ros2 topic echo /process_group_state
```

The previous command shows a process group information, something similar to the snapshot below:

```yaml
process_group_name: group2
src_info:
  host_name: ade
  exec_name: process_manager
  node_name: group2
state: 'OFF'
error: !!python/object/apply:process_manager_interfaces.msg._process_group_info.ErrorType
  - 0
in_transition: false
process_info:
  - name: C
    state: !!python/object/apply:process_manager_interfaces.msg._process_info.ProcessStateType
      - 0
    error: !!python/object/apply:process_manager_interfaces.msg._process_info.ProcessErrorType
      - 0
    path: /opt/ApexGraceBilbo/share/process_manager_examples/lib/process_manager_examples/minimal_process
    has_pid: false
    pid: 0
    has_exited: false
    has_exit_code: false
    exit_code: 0
    has_exit_signal: false
    exit_signal: 0
    stream_out: ''
    stream_err: ''
---
process_group_name: group1
src_info:
  host_name: ade
  exec_name: process_manager
  node_name: group1
state: 'ON'
error: !!python/object/apply:process_manager_interfaces.msg._process_group_info.ErrorType
  - 0
in_transition: false
process_info:
  - name: B
    state: !!python/object/apply:process_manager_interfaces.msg._process_info.ProcessStateType
      - 2
    error: !!python/object/apply:process_manager_interfaces.msg._process_info.ProcessErrorType
      - 0
    path: /opt/ApexGraceBilbo/share/process_manager_examples/lib/process_manager_examples/minimal_process
    has_pid: true
    pid: 4093385
    has_exited: false
    has_exit_code: false
    exit_code: 0
    has_exit_signal: false
    exit_signal: 0
    stream_out: ''
    stream_err: ''
  - name: A
    state: !!python/object/apply:process_manager_interfaces.msg._process_info.ProcessStateType
      - 2
    error: !!python/object/apply:process_manager_interfaces.msg._process_info.ProcessErrorType
      - 0
    path: /opt/ApexGraceBilbo/share/process_manager_examples/lib/process_manager_examples/minimal_process
    has_pid: true
    pid: 4093386
    has_exited: false
    has_exit_code: false
    exit_code: 0
    has_exit_signal: false
    exit_signal: 0
    stream_out: ''
    stream_err: ''
---
```

### Restart process group state service

The `/process_group_restart` service allows to request a restart of the current
process group state.

The service type is `process_manager_interfaces::srv::Restart`.

{{
code_snippet("grace/interfaces/process_manager_interfaces/srv/Restart.idl",
"idl") }}

Usage example:

```shell ade
(ade)$ ros2 service call /process_group_restart process_manager_interfaces/srv/Restart \
    "{process_group_name: 'group1'}"
```

The previous command shows the result of the service request, something similar to the
snapshot below:

```yaml
requester: making request: process_manager_interfaces.srv.Restart_Request(process_group_name='group1')

response:
process_manager_interfaces.srv.Restart_Response(return_code=<ReturnCodeType.OK: 1>, message='Restart completed successfully')
```

## CLI tools

### Launch file introspection tool

A very simple CLI tool has been added in order help introspecting the Process Manager
configuration. It is specially helpful to visualize dependency and state transition action graphs.

In order to visualize all the options available use `--help`:

```shell ade
(ade)$ ros2 run process_manager introspection --help
Usage:
  introspection [OPTIONS] --apex-settings-file path/to/launch.yaml
  introspection --help

Options:
  --help                     Display help and exit
  --validate                 Validates a launch file. Returns 0 if it is valid and 1 if it is not
  --print, --print-md        Prints an overview of the process group configuration in markdown format
  --process-group <group>    Selects which process group will be printed out
  --file <file>              Specifies the output file for the printed configuration
```

For example, in order to introspect a launch file in Markdown format:

<!-- markdownlint-disable MD046 -->
=== "ADE/colcon"

    ```shell ade
    (ade)$ ros2 run process_manager introspection --print --apex-settings-file \
        /opt/ApexGraceBilbo/share/process_manager_examples/launch/example_state_transitions.launch.yaml --file /tmp/example_state_transitions.md
    ```

=== "Bazel"

    ```shell ade
    (ade)$ bazel run @apex//grace/examples/process_manager_examples:example_settings.introspection -- --print --file /tmp/example_state_transitions.md
    ```

<!-- markdownlint-disable restore -->

Open the output file with a Markdown editor.

!!! note
    The functionality offered by this tool is just experimental. In the future, this tool will
    be extended to provide more information and filtering options.

### Get status cli tool

The `get_status` cli tool allows to get the status of Process Manager process groups.

For example:

<!-- markdownlint-disable MD046 -->
=== "ADE/colcon"

    ```shell ade
    ros2 run process_manager get_status --watch
    ```

=== "Bazel"

    ```shell ade
    bazel run grace/configuration/process_manager:get_status -- --watch
    ```

<!-- markdownlint-disable restore -->

### Change state cli tool

The `change_state` cli tool allows to change the state of a process group.

For example:

<!-- markdownlint-disable MD046 -->
=== "ADE/colcon"

    ```shell ade
    ros2 run process_manager change_state --group group1 --state OFF
    ```

=== "Bazel"

    ```shell ade
    bazel run grace/configuration/process_manager:change_state -- --group group1 --state OFF
    ```

<!-- markdownlint-disable restore -->

### Restart cli tool

The `restart` cli tool allows to restart a process group.

For example:

<!-- markdownlint-disable MD046 -->
=== "ADE/colcon"

    ```shell ade
    ros2 run process_manager restart --group group1
    ```

=== "Bazel"

    ```shell ade
    bazel run grace/configuration/process_manager:restart -- --group group1
    ```

<!-- markdownlint-disable restore -->

## Error detection and handling
<!-- Required -->
<!-- Things to consider:
    - Prefer doxygen comments whenever possible, do not repeat what is written in the doxygen comments
    - Which sources of error are considered?
    - What is the reaction to those errors? -->

### Error reading the launch file

When reading the configuration file and any error will throw an exception.

### Unexpected process termination

While a process group is in specific state, that is, no state transition ongoing, if a process
unexpectedly exists, the process group will detect this it will set its error state to
`process_manager_interfaces::msg::ProcessGroupInfo::ErrorType::ERROR`. In the same way, an
error is set for the terminated process
`process_manager_interfaces::msg::ProcessInfo::ProcessErrorType::UNEXPECTED_TERMINATION`.

### State transition error

During a state transition there could be several types of failures:

- A process fails to start or takes too long to start.
  In this case an `process_manager_interfaces::msg::ProcessInfo::ProcessErrorType::START_FAILED`
  error is set.
- A process fails to stop or takes too long to terminate.
  In this case an `process_manager_interfaces::msg::ProcessInfo::ProcessErrorType::STOP_FAILED`
  error is set.
- A process unexpectedly exists. In this case an
  `process_manager_interfaces::msg::ProcessInfo::ProcessErrorType::UNEXPECTED_TERMINATION`
  error is set. Note this may happen while a process is being started if it terminates soon
  after it starts.

When a failure happens in a state transition, the failure will be returned with an error
message as part of the service response. Additionally, the process group will set change its
error state to `process_manager_interfaces::msg::ProcessGroupInfo::ErrorType::ERROR`.

A state transition request will be rejected in the following cases:

- The requested state does not exist
- The requested state is the same as the current state, and it is not the `OFF` state
- The process group has an error and the requested state is not the `OFF` state

### Error recovery

It is possible to recover from an error state by transitioning the process group to the `OFF`
state. If the transition succeeds and all processes are stopped the error will be removed as
well as the process errors.

It is possible to specify how a state transition failure should be handled using the
[On failure behavior](#on-failure-behavior) option.

!!! note
    If a process terminates unexpectedly this could leave the system in an undetermined state.
    It is not the responsibility of the Process Manager to ensure this is not the case.

If there is an expected or unrecoverable failure, the process group error state will change to
`process_manager_interfaces::msg::ProcessGroupInfo::ErrorType::FATAL`. With this error, it is
still possible to transition to the `OFF` state, but no more state transitions are allowed and
it is not possible to recover from the failure.

## Conventions and limits

Naming conventions for process groups names:

- A process group name must be at most 255 characters long. (enforced)
- A process group name must be a valid ROS 2 name. (enforced)
- A process group name must be unique. (not enforced)

Naming conventions for process names:

- A process name must be at most 255 characters long. (enforced)
- A process name must be unique within its process group. (enforced)

Naming conventions for process group state names:

- A process name must be at most 255 characters long. (enforced)
- A state name must be unique within its process group. (enforced)
- A user defined state cannot have the same name as the reserved state `OFF`

The number of processes a process group can contain is limited to 256.

## Assumptions / Known limits
<!-- Required -->

- This package is still experimental until other Apex.Grace components are developed. There may
  be some breaking changes in the coming releases
- The number of processes allowed in a process group is limited to 256
- Processes with a high rate of `stdout` and `stderr` streams may impact start-up and
  termination timing
- `StopAllProcesses` option will stop processes when a state transition fails
- In order to detect the termination of a process, the Process Manager uses the
  child process file descriptor 3. If the process is using this file descriptor, this will
  result in an error.

## Inner-workings / Algorithms
<!-- If applicable -->
<!-- Things to consider:
    - What are the time and space complexities?
    - Which data structures are used? -->

For more information about how a process group state transition works and how it is
defined refer to [State transition graph](state-transition-graph.md).

## Security considerations
<!-- Optional -->
<!-- Things to consider:
- Spoofing (How do you check for and handle fake input?)
- Tampering (How do you check for and handle tampered input?)
- Repudiation (How are you affected by the actions of external actors?).
- Information Disclosure (Can data leak?).
- Denial of Service (How do you handle spamming?).
- Elevation of Privilege (Do you need to change permission levels during execution?) -->

- Child processes inherit the parent environment variables. This will be restricted in
  the future
- The Process Manager interfaces are expected to be secured by restricting the communication to
  certain processes
- Child processes are allowed to create their own child processes. This will be restricted in
  the future.

## Maintainer notes

<!-- Things to consider:
    - What someone should know to maintain or extend this package
    - Prefer doxygen comments whenever possible
     -->
