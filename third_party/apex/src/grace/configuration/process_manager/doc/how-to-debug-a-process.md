---
tags:
- Bazel
- process_manager
---

# How to debug or profile a process

In this tutorial, a process launched with the Process Manager is debugged using `gdb-server`.
A similar approach can be used for other debugging or profiling tools.

## Compile with debug flags

In order to debug the process it is required to compile the process executable with debug flags:

```shell ade
bazel build //grace/examples/process_manager_examples:example_debug -c dbg
```

## Configure launch file

In this tutorial we will use `minimal_process` executable as an example.

The start-up configuration entry [prefix](process-manager-design.md#process-start-up-configuration)
can be used to specify the command to prepend to the executable path.

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_debug.launch.yaml',
{'tag': '# [launch]', "skip_prefix": "#!"},
'yaml') }}

Note the start-up timeout can be disabled with `-1` so there is no timeout when
`gdbserver` is waiting for the client to connect.

### Launch

```shell ade
bazel run //grace/examples/process_manager_examples:example_debug -c dbg
```

The output should be:

```shell ade
    [2023-05-23 15:45:19] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/execution/apex_init/src/apex_init.cpp @  L239:
    Initializing root logger.
    [2023-05-23 15:45:19] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/configuration/process_manager/src/process_group/event_monitor.cpp @  L116:
    Starting to monitor processes...
    Change state from  OFF  to  ON
    [2023-05-25 17:58:56] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/configuration/process_manager/src/action/action_executor_node.cpp @  L48:
    Process IDLE:  minimal_process
    [2023-05-25 17:58:56] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/configuration/process_manager/src/action/action_executor_node.cpp @  L51:
    Process STARTING:  minimal_process
    [2023-05-25 17:58:56] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/configuration/process_manager/src/process_group/event_monitor.cpp @  L340:
    Process RUNNING:  minimal_process
    [2023-05-25 17:58:56] [INFO] group1 | /builds/ApexAI/grand_central/apex_ws/src/grace/configuration/process_manager/src/process_group/state_transition.cpp @  L148:
    State transition completed: from  OFF  to  ON
    Process /opt/ApexGraceBilbo/lib/process_manager/minimal_process created; pid = 235110
    Listening on port 3000
    Remote debugging from host 127.0.0.1, port 54412
```

At this point the process has been launched and gdbserver is waiting
for the gdb client to connect.

Refer to [How to debug an application](debugging-an-application.md) for more information
about how to debug an application.
