load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_binary")
load("@apex//common/bazel/rules_pkg_extra:defs.bzl", "exe_archive")
load("@apex//common/bazel/rules_pkg_extra:defs.bzl", "self_extracting_archive")
load("@apex//tools/bazel/rules_containers:defs.bzl", "run_in_containers")

# ""
package(default_visibility = ["//visibility:public"])

#! [exe_archive]
exe_archive(
    name = "minimal_pub_sub_app_archive",
    out = "minimal_pub_sub_app_archive.tar.gz",
    executable = "@apex//grace/examples/apex_os_minimal_pub_sub:minimal_pub_sub_app",
    # Tagged as "manual" to prevent this target from being built automatically during a full build.
    tags = ["manual"],
)
#! [exe_archive]

#! [self_extracting_archive]
self_extracting_archive(
    name = "minimal_pub_sub_app_installer",
    out = "minimal_pub_sub_app_installer.sh",  # (1)!
    executable = "@apex//grace/examples/apex_os_minimal_pub_sub:minimal_pub_sub_app",
    expand_rpath_origin = "auto",  # (2)!
    # Tagged as "manual" to prevent this target from being built automatically during a full build.
    tags = ["manual"],
)
#! [self_extracting_archive]

#! [configured_binary]
sh_binary(
    # (1)!
    name = "executable",
    srcs = ["script_for_configured_binary_example.bash"],
    # The "manual" tag is used to prevent this target from being built automatically
    # by default when running `bazel build //...`. This is typically done for targets
    # that are not meant to be included in regular builds, such as examples, tests,
    # or experimental code, and should only be built explicitly by the user.
    tags = ["manual"],
    deps = ["@bazel_tools//tools/bash/runfiles"],
)

configured_binary(
    # (2)!
    name = "configured_binary_example",
    args = [
        # (3)!
        "\"first arg\"",
        "$(rootpath some_data.txt)",
    ],
    data = ["some_data.txt"],  # (4)!
    env = {
        "SOME_ENV": "a \"value\"",
        "ANOTHER": "$(rootpaths executable)",
    },
    executable = ":executable",  # (5)!
    searchpath_executables = [":executable"],
    searchpaths = True,
    use_runfiles_as_working_dir = None,  # (6)!
)
#! [configured_binary]

# Experimental launch target using the Apex.OS process manager and a container-based setup
run_in_containers(
    name = "run_on_two_hosts",
    compose_file = "@apex//tools/bazel/rules_containers:two-hosts-docker-compose.yaml",
    enable_tmux = True,
    host_executable_map = {
        "host1": "@apex//grace/examples/apex_os_minimal_pub_sub:publisher_host",
        "host2": "@apex//grace/examples/apex_os_minimal_pub_sub:subscriber_host",
    },
)

#! [joint_configured_binary]
sh_binary(
    # (1)!
    name = "runner",
    srcs = [":runner.sh"],
)

configured_binary(
    # (2)!
    name = "pub_sub_cli",
    executable = ":runner",
    searchpath_executables = [
        # (3)!
        "//grace/examples/apex_os_minimal_pub_sub:publisher_host",
        "//grace/examples/apex_os_minimal_pub_sub:subscriber_host",
    ],
)
#! [joint_configured_binary]

filegroup(
    name = "doc_files",
    srcs = [
        "BUILD.bazel",
        "runner.sh",
    ],
    visibility = [
        "//grace/doc/tutorials/environment_configuration_and_building/building_with_bazel:__pkg__",
    ],
)
