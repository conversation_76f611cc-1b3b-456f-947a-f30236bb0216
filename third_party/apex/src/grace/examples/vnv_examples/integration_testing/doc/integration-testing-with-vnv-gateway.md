# Integration testing with the V&V Gateway

## Overview

Verification and validation, when scaled up, is typically at least semi-automated and involves
additional conversion or transformation steps. Creating a robust V&V pipeline requires testing it,
in the same fashion as any other software component.

The V&V gateway can be used in two ways. In the default case, it is part of a standalone gateway
process, piping data from the network into other Apex.OS processes. For testability, it can also
be used as a library inside integration or unit tests to avoid the complexity of going through
the network stack and to avoid elevated privileges; e.g. in CI environments. Data can be fed
into the gateway one packet at a time, either from a file (e.g. a pcap file) or from a custom
source. The gateway will then decode the data allowing regular topic subscriptions to be matched.

## Test API

The test API is located in the target `@apex//ida/connectors/vnv_gateway:test_api`, which provides
everything needed in the `ida/connectors/vnv_gateway/test_api.hpp` include.

`apex::connectors::vnv::test::VnVGateway` is the main class, which takes as a constructor argument
the generated configuration (created by `vnv_gateway` during the build). Two member functions are
provided for the test:

1. `wait_for_matched` to await the match against a specific mapped topic (note that calling
    `wait_for_matched` on the corresponding Apex.OS subscriber is *not* sufficient to
    ensure a match on both sides)
2. `ingest_ethernet_frame` to ingest an arbitrary ethernet frame (e.g. read from a pcap file)

## Example test

The example test can be run like any other unit test:
`bazel test @apex//grace/examples/vnv_examples/integration_testing:test_with_vnv_gateway`.

It features a simple "node under test" which subscribes to a V&V topic and publishes
on another output topic. The test is located in `@apex//grace/examples/vnv_examples/integration_testing/test_with_vnv_gateway.cpp`.
