// Copyright 2025 Apex.AI, Inc.
// All rights reserved.

#include "apex_init/apex_init.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "grace/examples/vnv_examples/integration_testing/node_under_test/converter_node.hpp"
#include "gtest/gtest.h"
#include "ida/connectors/vnv_gateway/test_api.hpp"

namespace
{

class TestNode final : public apex::executor::apex_node_base
{
public:
  TestNode(const apex::string_strict256_t & node_name, const apex::string_strict256_t & input_topic)
  : apex_node_base{node_name.c_str()},
    subscriber{get_rclcpp_node().create_polling_subscription<std_msgs::msg::String>(
      input_topic.c_str(), rclcpp::DefaultQoS())}
  {
  }

  void wait_for_matched()
  {
    subscriber->wait_for_matched(1U);
  }

  std::optional<std_msgs::msg::String> get_last_message() const
  {
    std::lock_guard lock(mutex);
    return last_message;
  }

protected:
  bool execute_impl() override
  {
    for (auto msg : subscriber->take()) {
      if (msg.info().valid()) {
        std::lock_guard lock(mutex);
        last_message = std::move(msg);
      }
    }
    return true;
  }

  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {subscriber};
  }

  mutable std::mutex mutex;
  std::optional<std_msgs::msg::String> last_message;
  const rclcpp::PollingSubscription<std_msgs::msg::String>::SharedPtr subscriber;
};


TEST(TestVnV, example_test)
{
  // Initialize Apex.OS
  auto scoped_init = apex::scoped_init(0, nullptr, false, false);

  // Start the test VnV gateway
  const apex::connectors::vnv::test::VnVGateway gateway{
    "grace/examples/vnv_examples/integration_testing/gateway_gateway_config.yaml"};

  // Instantiate the node-under-test and await matches (also in the gateway)
  const auto node_under_test = std::make_shared<apex::examples::ConverterNode>(
    "converter_node", "input_topic_from_gateway", "output_topic_from_node");
  node_under_test->wait_for_matched();
  gateway.wait_for_matched("rt/input_topic_from_gateway");

  // Instantiate test node and await the match to the output topic
  const auto test_node = std::make_shared<TestNode>("test_node", "output_topic_from_node");
  test_node->wait_for_matched();

  // Set up and run executor
  const auto executor = apex::executor::executor_factory::create();
  executor->add(node_under_test);
  executor->add(test_node);
  apex::executor::executor_runner runner{*executor};

  // Inject the packet into the VnV gateway (could also be reading from a pcap here)
  const uint8_t packet[] = {0x01, 0x00, 0x5e, 0x00, 0x00, 0x00, 0x38, 0x2a, 0x19, 0x80, 0x19, 0xce,
                            0x99, 0xfe, 0x00, 0x42, 0x86, 0x86, 0x03, 0x03, 0x00, 0x02, 0x00, 0x00,
                            0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x41, 0x4f,
                            0xc8, 0xd8, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x07, 0x78,
                            0x01, 0x13, 0x80, 0xf4, 0x01, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00};
  gateway.ingest_ethernet_frame(packet, sizeof(packet));

  // Await and compare the expected output message from the node-under-test
  while (!test_node->get_last_message()) {
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
  }

  EXPECT_EQ(test_node->get_last_message()->data, std::string(R"(byte_value: 120
bool_value: 1
short_value: 2432
uint_value: 0
int_value: -92
)"));
}

}  // namespace
