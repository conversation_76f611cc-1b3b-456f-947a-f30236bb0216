/// Copyright 2025 Apex.AI, Inc.
/// All rights reserved.

#ifndef GRACE_EXAMPLES_VNV_CONVERTER_NODE_HPP
#define GRACE_EXAMPLES_VNV_CONVERTER_NODE_HPP

#include "executor2/apex_node_base.hpp"
#include "logging/logging_macros.hpp"

#include "std_msgs/msg/string.hpp"
#include "test_msgs/test_messages.hpp"

namespace apex::examples
{

class ConverterNode final : public executor::apex_node_base
{
public:
  ConverterNode(const string_strict256_t & node_name,
                const string_strict256_t & input_topic,
                const string_strict256_t & output_topic)
  : apex_node_base{node_name.c_str()},
    m_subscriber{get_rclcpp_node().create_polling_subscription<test_messages_dbc::generic_message>(
      input_topic.c_str(), rclcpp::DefaultQoS())},
    m_publisher{get_rclcpp_node().create_publisher<std_msgs::msg::String>(output_topic.c_str(),
                                                                          rclcpp::DefaultQoS())},
    m_logger{&get_rclcpp_node(), node_name}
  {
  }

  void wait_for_matched() const
  {
    APEX_INFO(m_logger, "waiting for matched publisher...");
    m_subscriber->wait_for_matched(1U);
  }

private:
  bool execute_impl() override
  {
    const auto loaned_msgs{m_subscriber->take()};
    for (const auto & msg : loaned_msgs) {
      if (msg.info().valid()) {
        APEX_INFO(m_logger, "Received input, publishing output...");
        auto pub_msg = m_publisher->borrow_loaned_message();
        pub_msg->data = rosidl_generator_traits::to_yaml(msg.data());
        m_publisher->publish(std::move(pub_msg));
      }
    }
    return true;
  }

  executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {m_subscriber};
  }

  const rclcpp::PollingSubscription<test_messages_dbc::generic_message>::SharedPtr m_subscriber;
  const rclcpp::Publisher<std_msgs::msg::String>::SharedPtr m_publisher;
  logging::Logger<> m_logger;
};

}  // namespace apex::examples

#endif  // GRACE_EXAMPLES_VNV_CONVERTER_NODE_HPP
