VERSION "1.0"


NS_ :
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: My_ECU can_network

BO_ 1 generic_message: 7 My_ECU
 SG_ byte_value : 0|8@1+ (1,0) [0|180] "km/h"  can_network
 SG_ bool_value : 8|1@1+ (1,0) [0|1] ""  can_network
 SG_ short_value  : 9|16@1+ (1,0) [0|5000] "" can_network
 SG_ uint_value : 25|4@1+ (1,0) [0|10] "" can_network
 SG_ int_value  : 29|8@1- (1,0) [-128|127] "" can_network

BA_DEF_ SG_  "GenSigStartValue" INT 0 2147483647;
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","J1939PG","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_ BO_  "CANFD_BRS" ENUM  "0","1";
BA_DEF_  "DBName" STRING ;
BA_DEF_  "BusType" STRING ;
BA_DEF_DEF_  "CANFD_BRS" "1";
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "VFrameFormat" "StandardCAN";
BA_DEF_DEF_  "GenSigStartValue" 0;

BA_ "BusType" "CAN FD";
BA_ "DBName" "test_messages_dbc";
