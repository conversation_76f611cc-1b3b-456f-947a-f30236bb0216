load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")

msgs_library(
    name = "test_msgs",
    dbc_srcs = ["test_messages.dbc"],
    pkg_name = "test_msgs",
    visibility = ["@apex//grace/examples/vnv_examples:__subpackages__"],
)

cc_library(
    name = "converter_node",
    hdrs = ["converter_node.hpp"],
    visibility = ["@apex//grace/examples/vnv_examples:__subpackages__"],
    deps = [
        ":test_msgs",
        "@apex//grace/execution/executor2",
        "@apex//grace/interfaces/std_msgs",
    ],
)

cc_binary(
    name = "converter",
    srcs = ["converter.cpp"],
    visibility = ["@apex//grace/examples/vnv_examples:__subpackages__"],
    deps = [
        ":converter_node",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)
