/// Copyright 2025 Apex.AI, Inc.
/// All rights reserved.

#include "apex_init/apex_init.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "grace/examples/vnv_examples/integration_testing/node_under_test/converter_node.hpp"

int main(const int argc, char ** const argv)
{
  using namespace std::chrono_literals;

  int result{};

  try {
    const auto scoped_init = apex::scoped_init(argc, argv, false);

    const apex::interrupt_handler::installer interrupt_handler_installer{};

    const auto converter = std::make_shared<apex::examples::ConverterNode>(
      "converter_node", "input_topic_from_gateway", "output_topic_from_node");

    apex::event::monitored_process p{apex::event::sender_state::DoNotSyncWithDispatcher};
    const auto executor = apex::executor::executor_factory::create(p);
    converter->wait_for_matched();
    (void)executor->add(converter);
    const apex::executor::executor_runner runner{apex::executor::executor_runner::deferred,
                                                 *executor};

    scoped_init.post_init(p);
    runner.issue();
    apex::interrupt_handler::wait();
    runner.stop();
  } catch (const std::exception & e) {
    std::cerr << e.what() << std::endl;
    result = 2;
  } catch (...) {
    std::cerr << "Unknown error occurred" << std::endl;
    result = -1;
  }

  return result;
}
