load("@apex//ida/bazel:defs.bzl", "vnv_gateway")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")

vnv_gateway(
    name = "gateway",
    config = "resources/gateway.yaml",
    visibility = ["@apex//grace/examples/vnv_examples:__subpackages__"],
    deps = ["@apex//grace/examples/vnv_examples/integration_testing/node_under_test:test_msgs"],
)

apex_cc_test(
    name = "test_with_vnv_gateway",
    srcs = ["test_with_vnv_gateway.cpp"],
    data = [":gateway"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "@apex//grace/examples/vnv_examples/integration_testing/node_under_test:converter",
        "@apex//grace/examples/vnv_examples/integration_testing/node_under_test:test_msgs",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@apex//ida/connectors/vnv_gateway:test_api",
        "@googletest//:gtest_main",
    ],
)
