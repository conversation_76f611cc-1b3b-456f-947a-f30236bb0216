load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

cc_library(
    name = "global_executor_examples_lib",
    hdrs = glob(["include/**"]),
    strip_include_prefix = "include",
    tags = ["exclude_sca"],
    visibility = ["//visibility:private"],
    deps = [
        "//grace/execution/executor2",
        "//grace/interfaces/geometry_msgs",
        "//grace/interfaces/sensor_msgs",
    ],
)

cc_binary(
    name = "sensor_fusion_coordinator",
    srcs = ["src/coordinators/sensor_fusion.cpp"],
    tags = ["exclude_sca"],
    deps = [
        ":global_executor_examples_lib",
        "//common/interrupt",
    ],
)

cc_binary(
    name = "odometry_app",
    srcs = ["src/apps/odometry_app.cpp"],
    tags = ["exclude_sca"],
    deps = [
        ":global_executor_examples_lib",
        "//common/interrupt",
    ],
)

cc_binary(
    name = "camera_app",
    srcs = ["src/apps/camera_app.cpp"],
    tags = ["exclude_sca"],
    deps = [
        ":global_executor_examples_lib",
        "//common/interrupt",
    ],
)

cc_binary(
    name = "radar_app",
    srcs = ["src/apps/radar_app.cpp"],
    tags = ["exclude_sca"],
    deps = [
        ":global_executor_examples_lib",
        "//common/interrupt",
    ],
)

cc_binary(
    name = "lidar_app",
    srcs = ["src/apps/lidar_app.cpp"],
    tags = ["exclude_sca"],
    deps = [
        ":global_executor_examples_lib",
        "//common/interrupt",
    ],
)

cc_binary(
    name = "fusion_app",
    srcs = ["src/apps/fusion_app.cpp"],
    tags = ["exclude_sca"],
    deps = [
        ":global_executor_examples_lib",
        "//common/interrupt",
    ],
)

ros_pkg(
    name = "global_executor_examples",
    description = "Examples showing how to implement a global (multi-process) executor",
    lib_executables = [
        "sensor_fusion_coordinator",
        "odometry_app",
        "camera_app",
        "radar_app",
        "lidar_app",
        "fusion_app",
    ],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Jeff Ithier",
    pkg_name = "global_executor_examples",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)
