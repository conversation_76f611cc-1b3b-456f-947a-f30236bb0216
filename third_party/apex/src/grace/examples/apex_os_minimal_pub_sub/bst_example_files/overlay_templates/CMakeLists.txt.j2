cmake_minimum_required(VERSION 3.19)
project(apex_os_minimal_pub_sub_{{variant}})

{% if variant == "modern" %}
if(POLICY CMP0144)
    cmake_policy(SET CMP0144 NEW)
endif()

# Modern CMake: Use "find packages"
find_package(apex_init CONFIG REQUIRED)
find_package(apexcpp CONFIG REQUIRED)
find_package(cpputils CONFIG REQUIRED)
find_package(executor2 CONFIG REQUIRED)
find_package(interrupt CONFIG REQUIRED)

add_compile_options(-Wno-error=overloaded-virtual -Wno-error=missing-field-initializers)

add_executable(publisher_modern src/minimal_publisher.cpp)
target_link_libraries(publisher_modern
    PUBLIC
        apexutils::apexutils
        apex_init::apex_init
        apex_init::apex_main
        executor2::executor2
        interrupt::interrupt
        builtin_interfaces::builtin_interfaces
)

add_executable(subscriber_modern src/minimal_subscriber.cpp)
target_link_libraries(subscriber_modern
    PUBLIC
        apexutils::apexutils
        apex_init::apex_init
        apex_init::apex_main
        executor2::executor2
        interrupt::interrupt
        builtin_interfaces::builtin_interfaces
)

install(
    TARGETS publisher_modern subscriber_modern
    EXPORT exported_targets
)

# Minimal ament integration
find_package(ament_cmake QUIET)
if(ament_cmake_FOUND)
    ament_export_targets(exported_targets)
    ament_package()
endif()
{% elif variant == "ament_auto" %}
#! [Find Dependencies]
find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()
#! [Find Dependencies]

#! [Auto Add Executables]
#! [Build Target Publisher]
ament_auto_add_executable(publisher src/minimal_publisher.cpp)
#! [Build Target Publisher]
ament_auto_add_executable(subscriber src/minimal_subscriber.cpp)
#! [Auto Add Executables]

#! [Install Package]
ament_auto_package(
    INSTALL_TO_SHARE
    launch
)
#! [Install Package]
#! [Minimal CMake]
{% else %}
{{ fail("Unknown variant") }}
{% endif %}
