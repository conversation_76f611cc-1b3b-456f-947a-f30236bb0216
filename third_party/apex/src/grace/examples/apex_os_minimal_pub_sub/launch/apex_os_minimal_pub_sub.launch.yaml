process-manager:
  process-groups:
    - name: "pubsub"
      init-state: "ON"
      group-log-level: "FATAL"
      processes:
        - name: "publisher"
          execution-report: "ByTopic"
          stdout-handlers: ["Console"]
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "$(find-pkg-prefix apex_os_minimal_pub_sub)/lib/apex_os_minimal_pub_sub/publisher"
            env-vars:
              - { APEX_IDA_LOG_LEVEL: "fatal"}
              - { APEX_GRACE_LOG_LEVEL: "FATAL"}
        - name: "subscriber"
          execution-report: "ByTopic"
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "$(find-pkg-prefix apex_os_minimal_pub_sub)/lib/apex_os_minimal_pub_sub/subscriber"
            env-vars:
              - { APEX_IDA_LOG_LEVEL: "fatal" }
              - { APEX_GRACE_LOG_LEVEL: "FATAL" }
      states:
        - name: "ON"
          processes:
            - name: "publisher"
            - name: "subscriber"