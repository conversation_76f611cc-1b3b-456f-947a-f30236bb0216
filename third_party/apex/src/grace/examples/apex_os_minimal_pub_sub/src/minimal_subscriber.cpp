/// \copyright Copyright 2019-2024 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \include minimal_subscriber.cpp
/// \brief writing a simple subscriber

//! [Minimal subscriber]
#include <chrono>
#include <memory>
#include <utility>
#include <exception>
#include <iostream>
#include <cstdlib>

#include "apex_init/apex_init.hpp"
#include "apex_init/apex_main.hpp"
#include "cpputils/common_exceptions.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "logging/logging_macros.hpp"
#include "std_msgs/msg/string.hpp"
#include "event/monitored_process.hpp"

namespace apex
{
namespace apex_os_minimal_pub_sub
{

class MinimalSubscriber : public apex::executor::apex_node_base
{
public:
  //! [Constructor]
  MinimalSubscriber(
    const apex::string_strict256_t & node_name,
    const apex::string_strict256_t & topic)
  : apex_node_base{node_name.c_str()},
    m_subscription{get_rclcpp_node().create_polling_subscription<std_msgs::msg::String>(
        topic.c_str(),
        rclcpp::DefaultQoS())},  // (1)!
    m_logger{&get_rclcpp_node(), "subscriber"}  // (2)!
  {}
  //! [Constructor]

  //! [wait_for_matched]
  void wait_for_matched() const
  {
    APEX_INFO(m_logger, "waiting for matched publisher...");
    m_subscription->wait_for_matched(1U, std::chrono::seconds{300});
  }
  //! [wait_for_matched]

private:
  //! [function]
  bool execute_impl() override
  {
    auto loaned_msgs{m_subscription->take()};  // (1)!
    for (const auto & msg : loaned_msgs) {
      if (msg.info().valid()) {  // (2)!
        APEX_INFO(m_logger, "Received message:", msg.data().data);  // (3)!
      }
    }
    return true;
  }
  //! [function]

  //! [Get triggering subscriptions]
  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {m_subscription};
  }
  //! [Get triggering subscriptions]

  //! [Private members]
  const rclcpp::PollingSubscription<std_msgs::msg::String>::SharedPtr m_subscription;
  apex::logging::Logger<> m_logger;
  //! [Private members]
};

}  // namespace apex_os_minimal_pub_sub
}  // namespace apex

int32_t apex_main(const int32_t argc, char ** const argv)
{
  //! [Initialization phase]
  auto scoped_init = apex::scoped_init(argc, argv, false);
  const apex::interrupt_handler::installer interrupt_handler_installer{};
  auto minimal_subscriber =
    std::make_shared<apex::apex_os_minimal_pub_sub::MinimalSubscriber>("minimal_sub",
      "minimal_chatter");
  apex::event::monitored_process p{apex::event::sender_state::DoNotSyncWithDispatcher};
  const auto executor = apex::executor::executor_factory::create(p);
  minimal_subscriber->wait_for_matched();
  (void)executor->add(minimal_subscriber);
  const apex::executor::executor_runner runner{
    apex::executor::executor_runner::deferred | apex::executor::executor_runner::interrupt,
    *executor};
  scoped_init.post_init(p);
  //! [Initialization phase]
  runner.issue();
  apex::interrupt_handler::wait();
  //! [Shutdown phase]
  runner.stop();
  //! [Shutdown phase]
  return 0;
}
//! [Minimal subscriber]
