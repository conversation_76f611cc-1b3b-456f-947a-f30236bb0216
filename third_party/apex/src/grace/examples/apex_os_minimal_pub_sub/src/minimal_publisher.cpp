/// \copyright Copyright 2019-2024 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \include minimal_publisher.cpp
/// \brief writing a simple publisher

//! [Minimal publisher]
#include <chrono>
#include <memory>
#include <utility>

//! [Publisher includes]
#include "apex_init/apex_init.hpp"  // (1)!
#include "apex_init/apex_main.hpp"
#include "cpputils/common_exceptions.hpp"  // (2)!
#include "executor2/apex_node_base.hpp"  // (3)!
#include "executor2/executor_factory.hpp"  // (4)!
#include "executor2/executor_runner.hpp"  // (5)!
#include "interrupt/interrupt_handler.hpp"  // (6)!
#include "logging/logging_macros.hpp"  // (7)!
#include "std_msgs/msg/string.hpp"  // (8)!
#include "timer_service/clock_timer_service.hpp"  // (9)!
#include "event/monitored_process.hpp"  // (10)!
//! [Publisher includes]

namespace apex
{
namespace apex_os_minimal_pub_sub
{

//! [Node class]
class MinimalPublisher : public apex::executor::apex_node_base
//! [Node class]
{
public:
  //! [Constructor]
  MinimalPublisher(
    const apex::string_strict256_t & node_name,
    const apex::string_strict256_t & topic,
    apex::timer_service::timer_subscription_ptr timer_sub)
  : apex_node_base{node_name.c_str()},  // (1)!
    m_publisher{get_rclcpp_node().create_publisher<std_msgs::msg::String>(  // (2)!
        topic.c_str(),
        rclcpp::DefaultQoS())},  // (3)!
    m_timer_sub{std::move(timer_sub)},  // (4)!
    m_logger{&get_rclcpp_node(), "publisher"}
  {}
  //! [Constructor]

  //! [wait_for_matched]
  void wait_for_matched() const
  {
    APEX_INFO(m_logger, "waiting for matched subscriber...");
    m_publisher->wait_for_matched(1U, std::chrono::seconds{300});
  }
  //! [wait_for_matched]

private:
  //! [function]
  bool execute_impl() override
  {
    if (m_timer_sub->test_and_reset()) {
      auto msg = m_publisher->borrow_loaned_message();
      msg->data = "Hello, world! " + apex::to_string(m_count);  // (1)!
      APEX_INFO(m_logger, "Published message:", msg->data);  // (2)!
      m_publisher->publish(std::move(msg));
      ++m_count;
    }
    return true;
  }
  //! [function]

  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {m_timer_sub->to_sub_ptr()};
  }

  //! [Private members]
  size_t m_count{};
  const rclcpp::Publisher<std_msgs::msg::String>::SharedPtr m_publisher;
  apex::timer_service::timer_subscription_ptr m_timer_sub;
  apex::logging::Logger<> m_logger;
  //! [Private members]
};

}  // namespace apex_os_minimal_pub_sub
}  // namespace apex

//! [Minimal publisher main function]
int32_t apex_main(const int32_t argc, char ** const argv)  // (1)!
{
  using namespace std::chrono_literals;
  //! [Initialization phase]
  //! [Signal handler]
  auto scoped_init = apex::scoped_init(argc, argv, false);  // (2)!
  const apex::interrupt_handler::installer interrupt_handler_installer{};
  //! [Signal handler]
  apex::timer_service::steady_clock_timer_service srv;
  auto timer = srv.create_timer(0s, 500ms);  // (3)!
  auto minimal_publisher =
    std::make_shared<apex::apex_os_minimal_pub_sub::MinimalPublisher>("minimal_pub",  // (4)!
      "minimal_chatter", timer);
  apex::event::monitored_process p{apex::event::sender_state::DoNotSyncWithDispatcher};  // (5)!
  const auto executor = apex::executor::executor_factory::create(p);  // (6)!
  minimal_publisher->wait_for_matched();  // (7)!
  (void)executor->add(minimal_publisher);  // (8)!
  const apex::executor::executor_runner runner{
    apex::executor::executor_runner::deferred | apex::executor::executor_runner::interrupt,
    *executor};  // (9)!
  scoped_init.post_init(p);  // (10)!
  //! [Initialization phase]
  //! [Runtime phase]
  runner.issue();  // (11)!
  apex::interrupt_handler::wait();  // (12)!
  //! [Runtime phase]
  //! [Shutdown phase]
  runner.stop();  // (13)!
  //! [Shutdown phase]
  return 0;
}
//! [Minimal publisher main function]
//! [Minimal publisher]
