#! [load_statements]
load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")

#! [load_statements]
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_pkg//:mappings.bzl", "pkg_files")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "apex_os_minimal_pub_sub",
    description = "Example of Apex.OS minimal publisher and subscriber.",
    lib_executables = [
        "publisher",
        "subscriber",
    ],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "<PERSON>",
    pkg_name = "apex_os_minimal_pub_sub",
    share_data = [
        ":launch_files",
    ],
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

pkg_files(
    name = "launch_files",
    srcs = glob(["launch/apex_os_minimal*.launch.yaml"]),
    prefix = "launch",
)

filegroup(
    name = "doc_files",
    srcs = [
        "BUILD.bazel",
        "CMakeLists.txt",
        "launch/apex_os_minimal_pub_sub.launch.yaml",
        "launch/ida.settings.yaml",
        "package.xml",
        "src/minimal_publisher.cpp",
        "src/minimal_subscriber.cpp",
    ],
    visibility = [
        ":__subpackages__",
        "//common/interrupt/design:__pkg__",
        "//grace/configuration/process_manager:__subpackages__",
        "//grace/doc:__subpackages__",
        "//grace/execution/apex_init/doc:__pkg__",
        "//grace/ros/rclcpp/rclcpp/doc/design:__pkg__",
        "//ida/doc:__pkg__",
        "//tools/ament/ament_cmake/ament_cmake/doc:__pkg__",
    ],
)

#! [publisher_cc_binary]
cc_binary(
    name = "publisher",
    srcs = ["src/minimal_publisher.cpp"],
    deps = [
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/apex_init:apex_main",
        "@apex//grace/execution/executor2",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)
#! [publisher_cc_binary]

cc_binary(
    name = "subscriber",
    srcs = ["src/minimal_subscriber.cpp"],
    deps = [
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/apex_init:apex_main",
        "@apex//grace/execution/executor2",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

#! [process_manager_and_ament_pkg_resources]
ament_pkg_resources(
    name = "apex_os_minimal_pub_sub_resources",
    package = "apex_os_minimal_pub_sub",
    resources = {
        ":publisher": "executable",
        ":subscriber": "executable",
    },
)

process_manager(
    name = "minimal_pub_sub_app",
    data = [
        ":apex_os_minimal_pub_sub_resources",
        "@apex//ida/connectors/rtps/cyclone_gateway",
    ],
    launch_file = ":launch/apex_os_minimal_pub_sub.launch.yaml",
)
#! [process_manager_and_ament_pkg_resources]

process_manager(
    name = "publisher_host",
    data = [
        ":publisher",
        "@apex//ida/connectors/rtps/cyclone_gateway",
    ],
    launch_file = "launch/apex_os_minimal_publisher.launch.yaml",
)

process_manager(
    name = "subscriber_host",
    data = [
        ":subscriber",
        "@apex//ida/connectors/rtps/cyclone_gateway",
    ],
    launch_file = "launch/apex_os_minimal_subscriber.launch.yaml",
)
