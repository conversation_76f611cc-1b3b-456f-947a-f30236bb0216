# Configuring a distributed system

## Overview

A distributed system is used to share compute load of numerous autonomous
vehicle applications, and can also be useful in isolating production and R&D
applications. This article explains how to configure two or more Apex.Grace
supported PCs and ECUs as a distributed system.

This article assumes the following two systems are being configured within the
same distributed system: **Renesas R-Car H3** and **Spectra PC**. However, note
that any two (or more) Apex.Grace [supported platforms](supported-platforms.md)
can be configured following the same guidelines defined in this article.

![Simple Distributed System](simple-distributed-system-light-07.png){: .center }

### Distributed system definition

Within the context of Apex.Grace, a distributed system consists of two or more
machines connected to the same network where each machine is configured to
communicate on the same subnet. Furthermore, each Apex.Grace system must be
configured to use the same `ROS_DOMAIN_ID`.

Through the advantages of DDS and Apex.Grace, it's possible to connect many
machines. The maximum number of machines is highly dependent on the system
architecture and data throughput requirements. It's common to have up to three
ECUs and PCs in an autonomous vehicle which are dedicated to autonomous driving
related computation.

### Quickstart

1. Start `ade` on the R-Car H3 with `--net=host`:
    - `$ ade start -- --net=host`
    - For more information, see [Passing commands directly to `docker run`](https://ade-cli.readthedocs.io/en/latest/addargs.html#addargs)
1. Start `ade` on the Spectra PC with `--net=host`
    - `$ ade start -- --net=host`
1. On both machines, configure **the same `ROS_DOMAIN_ID`**; see [configuring a distributed
   system](configuring-a-distributed-system.md#configure-the-ros-domain-id) for more information

## Configure the ROS domain ID

DDS supports a segmentation of the network based on the so-called domain ID. Different
domain IDs use [disjoint ranges of
ports](https://github.com/eclipse-cyclonedds/cyclonedds/blob/51aa411347bb56d7e5c90f5bb62b9cb926e0feea/docs/manual/config/cyclonedds_specifics.rst#controlling-port-numbers).
The domain ID is exposed via the `ROS_DOMAIN_ID` environment variable, which is
used to divide machines on the same network into different DDS domains.

By default, ADE (via docker) creates a separate virtual network. Apex.Grace ADE environments therefore
cannot interfere with Apex.Grace deployments on other machines. For Apex.Grace deployments outside docker,
or ADE environments using the `--net=host` option, each deployment should [have a distinct
ROS_DOMAIN_ID](https://docs.ros.org/en/eloquent/Tutorials/Configuring-ROS2-Environment.html).

!!! tip "Important"
    For ADE containers that are started with `--net=host`, the domain ID has to be configured
    both in the ADE environment and on the host machine.

!!! warning
    If two or more Apex.Grace-enabled development machines on
    the same network share the same (or an empty) `ROS_DOMAIN_ID`,
    non-deterministic behavior is expected.

To **isolate** system environments, set `ROS_DOMAIN_ID` to a unique number between `1` and `101`.
Note that `0` is not a valid ID.

## Configure the R-Car H3

The configurations below are used for bench or in-vehicle networking.

- `Address` represents the **desired IP address**, in this case `************`
- `Name` represents the **desired Ethernet interface**, in this case `enp3s0`
- `DNS` and `Gateway` configuration is optional

### R-Car H3 network configuration file

On the R-Car H3 host system, open the `/etc/systemd/network/wired_t186.network`
file and add the following contents.

```plain
# /etc/systemd/network/wired_t186.network

[Match]
Name=enp3s0

[Network]
Address=************
Gateway=**********
DNS=***********
```

### R-Car H3 ROS_DOMAIN_ID

On the R-Car H3 host system, update the `~/.bashrc` file and update the
`ROS_DOMAIN_ID` environment variable.

```shell dollar
vim ~/.bashrc
...
export ROS_DOMAIN_ID=100
...
```

## Configure the Spectra PC

The configurations below are used for bench or in-vehicle networking.

- `Address` represents the **desired IP address**, in this case `**********01`
- `Name` represents the **desired Ethernet interface**, in this case `eth0`
- `DNS` and `gateway` configuration is optional

### Spectra PC network configuration file

On the Spectra PC host system, open the `/etc/network/interfaces` file and add
the following contents.

```plain
# /etc/network/interfaces

auto eth0
  iface eth0 inet static
  address **********01
  netmask *************
  gateway **********
  dns-nameservers ***********
  metric 100
```

!!! note
    The interface name `eth0` may be different depending on the host
    machine. Use `ifconfig` or a similar command to introspect the Ethernet
    interfaces on the machine.

### Spectra PC ROS_DOMAIN

On the Spectra PC host system, update the `~/.bashrc` file and update the `ROS_DOMAIN_ID`environment
variable.

```shell dollar
vim ~/.bashrc
...
export ROS_DOMAIN_ID=100
...
```

## Start the distributed applications

The system is now configured as a distributed compute system. Through the DDS
middleware, applications are able to publish and subscribe to data regardless of
where the data originates. There are limitations to data throughput, and system
performance is highly dependent on the system architecture. Ask Apex.AI support
for help with a specific use-case.

On each machine, start ADE with the additional argument `--net=host`, so that
the host machine network configuration that was set previously are imported into
the ADE container:

```shell dollar
ade start -- --net=host
ade enter
```

!!! note
    The ADE start command must be run on all machines in the distributed system.

See the steps below to validate the distributed system.

## Validate the distributed system setup

This section contains a sanity check which launches an Apex.Grace publisher node
and a subscriber node, and verifies that both nodes publish and receive messages
through Apex.Grace successfully. Open one terminal on each of the two machines:

Terminal 1 - R-Car H3:
<!-- LAUNCH_STEP: publisher-->
```shell ade
ros2 run apex_os_minimal_pub_sub publisher
```
<!-- EXPECTED_OUTPUT: publisher-->
<!-- FILTER ^.*(Hello, world! \d) -->
```shell
[INFO] minimal_pub | /home/<USER>/workspace/apex_ws/src/grace/examples/apex_os_minimal_pub_sub/src/minimal_publisher.cpp @  L59: Published message: Hello, world! 14
[INFO] minimal_pub | /home/<USER>/workspace/apex_ws/src/grace/examples/apex_os_minimal_pub_sub/src/minimal_publisher.cpp @  L59: Published message: Hello, world! 15
```

Terminal 2 - Spectra PC:
<!-- LAUNCH_STEP: subscriber-->
```shell ade
ros2 run apex_os_minimal_pub_sub subscriber
```
<!-- EXPECTED_OUTPUT: subscriber-->
<!-- FILTER ^.*(\[INFO\] minimal_sub|Hello, world!) -->
```shell
[INFO] minimal_sub | /home/<USER>/workspace/apex_ws/src/grace/examples/apex_os_minimal_pub_sub/src/minimal_subscriber.cpp @  L56:
Received message:  Hello, world! 14
[INFO] minimal_sub | /home/<USER>/workspace/apex_ws/src/grace/examples/apex_os_minimal_pub_sub/src/minimal_subscriber.cpp @  L56:
Received message:  Hello, world! 15
```

The talker publishes messages on a topic named: `minimal_chatter`.
<!-- TOPIC_LIST -->
```shell
/minimal_chatter
```

If the terminal output matches what's shown above, congratulations! The
environment is properly setup as a distributed system.

Enter ++ctrl+c++ to stop the applications.

### Troubleshooting

If the validation step from above fails:

1. Ensure that the two machines can `ping` the other machine using the static IP
   address; for example, from the Spectra PC issue the following command to ping
   the R-Car H3:

    ```shell ade
    ping ************
    PING ************ (************) 56(84) bytes of data.
    64 bytes from ************: icmp_seq=1 ttl=64 time=0.539 ms
    64 bytes from ************: icmp_seq=2 ttl=64 time=0.312 ms
    ```

2. Verify that ADE was started with the `--net=host` option, as defined in
   [start the distributed
   system](#start-the-distributed-applications)
   section above

<!-- DOCUMENTATION_TEST: create_distributed_system.doctest.py -->
