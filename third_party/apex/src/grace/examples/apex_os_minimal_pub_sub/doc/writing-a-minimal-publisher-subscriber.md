# Writing a minimal publisher and subscriber

This tutorial covers the basic steps to create, build, and run a new package from scratch:

1. [A quick description of the application](#description-of-the-publisher-and-subscriber-application)
1. [Commands to create a new package in Apex.Grace](#create-an-apexgrace-package)
1. [Walk through of the minimal code of a publisher-subscriber](#the-publisher-node)
1. [Running and introspecting the application](#build-and-run-the-package)

## Description of the publisher and subscriber application

In this tutorial, the publisher application sends data in the form of string messages to the
subscriber node over the `/minimal_chatter` topic. The message is defined in the `String.idl` file,
which is already shipped with the Apex.Grace. Code is generated to read and write the messages in
a variety of languages, including C++.

The ROS graph of the system looks as follows:

```mermaid
    flowchart LR
        p-->|topic /minimal_chatter|s
        subgraph Publisher application
        p[Apex.Grace node]
        end
        subgraph Subscriber application
        s[Apex.Grace node]
        end
```

!!! note
    This example is adapted from the [writing a simple C++ publisher and
    subscriber](https://docs.ros.org/en/rolling/Tutorials/Beginner-Client-Libraries/Writing-A-Simple-Cpp-Publisher-And-Subscriber.html)
    ROS 2 article.

## Create an Apex.Grace package

Apex.Grace like ROS organizes the code into packages. A package can be considered as a small
contained unit. Packages make it easier to share code and artifacts to others.

Apex.AI provides the `apex_create_pkg` script that enables developers to generate a boiler-plate
package with the basic configuration to compile and run their application.

The packages need to be created in [Apex.Grace Bazel workspace](/grace/doc/tutorials/environment_configuration_and_building/building_with_bazel/getting-started-with-apex-grace-bazel.md#set-up-an-initial-bazel-workspace).
Create a package by either specifying all required information on the command line, as follows:

{{ console_snippet(
    "grace/integration_tests/os_verify_docs/test/test_writing_a_minimal_publisher_subscriber.py",
    config={
      "remove_indent": 4,
      "add_shell_prefix": "$",
      "tag": "#! CREATE",
      "stdout_tag": "#! CREATE_OUTPUT"
      }
)}}

1. In case of `/usr/bin/env: ‘python3.8’: No such file or directory` error, specify the Python
   version installed on the system by providing Bazel `--repo_env` option with `APEX_PYTHON_VERSION`
   variable, e.g., for Ubuntu 22.04 it's `--repo_env=APEX_PYTHON_VERSION=3.10.12`

Or by not passing arguments, in which case the tool will prompt for all required information:

```shell dollar
bazel run @apex//tools/apex_create_pkg:apex_create_pkg_exe
```

By default, the `apex_create_pkg` command creates minimal publisher and minimal subscriber nodes
that can be used as a template for more complex nodes.

See the `bazel run @apex//tools/apex_create_pkg:apex_create_pkg_exe -- --help` output for more
information on the command-line arguments, especially for the choices of `--license` and
`--copyright`.

### Package contents

The generated package has the following structure:

```shell dollar
$ tree -L 1 src/apex_os_minimal_pub_sub
src/apex_os_minimal_pub_sub
├── BUILD.bazel
├── CMakeLists.txt
├── design
├── include
├── launch
├── package.xml
├── src
└── test

5 directories, 3 files
```

- `BUILD.bazel` (Bazel only)
    - the equivalent of `CMakeLists.txt` for [Bazel](https://bazel.build/)
- `CMakeLists.txt` (colcon/CMake only)
    - Deprecated and will be removed in the following releases
- `design`
    - A folder to keep documentation on the design of the libraries and executables in the package
    - These design documents describe the details of the code in an organized, human-readable way
      such that others can get an overview of the package
- `include`
    - A folder with header files
- `launch`
    - A folder for [`process_manager`](process-manager-design.md) configuration files
- `package.xml` (colcon only)
    - Deprecated and will be removed in the following releases
- `src`
    - A folder for source files
- `test`
    - A folder for all the test sources
- A package may also include other folders such as `config`, `launch`, `param`,
  `scripts`, etc, depending on the specific use-case (not covered in this tutorial)
- The Apex.Grace package layout is compatible with the [ROS 2 package
  layout](https://docs.ros.org/en/rolling/Contributing/Developer-Guide.html#filesystem-layout)

!!! tip "Important"
    The generated package from `apex_create_pkg` is slightly different from the included
    `apex_os_minimal_pub_sub` Apex.Grace package, which is referenced below. Both include a
    minimal Apex.Grace publisher node binary and a minimal Apex.Grace subscription node binary,
    however, the setup/configuration of each is slightly different.

## The publisher node

### Included headers

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_publisher.cpp",
    {"tag": "//! [Publisher includes]", "skip_prefix": "//!"}, "cpp") }}

1. Includes APIs to initialize the Apex.Grace and ROS 2 context
2. Includes APIs for the Apex.Grace exceptions
3. Includes APIs for the Apex.Grace node
4. Includes APIs to instantiate the Apex.Grace executor
5. Includes APIs to configure the Apex.Grace executor
6. Includes APIs to set up the signal handler
7. Includes APIs for logging macros
8. Includes message definitions
9. Includes APIs for the Apex.Grace timer service
10. Includes APIs for process monitoring

### Constructor implementation

The `MinimalPublisher` class inherits from the `apex_node_base` class.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_publisher.cpp",
    {"tag": "//! [Constructor]", "skip_prefix": "//!"}, "cpp") }}

1. The public delegated constructor `apex_node_base` names the node `node_name`
2. The `get_rclcpp_node()` method provides related [](rclcpp::Node)
3. The publisher `m_publisher` is initialized with the `create_publisher()`
   method, `std_msgs::msg::String` message type, the topic name `topic`,
   and the QOS profile [](rclcpp::DefaultQoS)
4. Pointer to timer subscription which will trigger the execution and cause the
   publisher to publish

### Waiting for matched subscriber

In publish/subscribe systems like Apex.Grace, publishers do not know ahead of time how many
other nodes will subscribe to a given topic. They will therefore publish messages as soon as the
`publish` method is invoked, even if not all subscribers have been matched yet.

In deterministic production systems, where the number of expected subscribers is usually known
in advance, it is therefore good practice to explicitly wait for enough subscribers to match
before leaving the node's initialization phase.

In this example, the application waits for at least one subscriber to match, with a timeout of 5
minutes. The method should be called before the start of the executor.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_publisher.cpp",
    {"tag": "//! [wait_for_matched]", "skip_prefix": "//!"}, "cpp") }}

### Implementation of the `execute_impl()` method

This is where the algorithm is implemented. In this example, a message is set and published
in the `execute_impl()` method. The `execute_impl()` method will be called by the executor.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_publisher.cpp",
    {"tag": "//! [function]", "skip_prefix": "//!"}, "cpp") }}

1. Sets the `std_msgs::msg::String` message data
2. Logs the passed string with the `INFO` level

### Implementation of the `main()` function

In the `main()` function, the task execution strategy is defined,
and interrupt signals, as well as exceptions, are handled.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_publisher.cpp",
    {"tag": "//! [Minimal publisher main function]", "skip_prefix": "//!"}, "cpp") }}

1. Apex.Grace handles the parsing of command line arguments. During the initialization
   phase, memory allocation is allowed
2. [](apex::scoped_init) initializes Apex.Grace and ROS 2.
   This has to happen before a node can be used. By default this also registers a default
   `terminate_handler` supplied with Apex.Grace. To opt-out, set the `scoped_init::pre_init()`
   argument `set_terminate_handler` to `false`.
3. Create a timer to execute the publisher periodically
4. Instantiates the publisher node
5. Opt in for [process monitoring](event-design.md#process-monitoring). `DoNotSyncWithDispatcher` means
   that no error will be thrown if the Event Dispatcher has not yet been started.
6. Creates an Apex.Grace executor
7. Waits for expected subscribers and prevents unnecessary execution of the node.
   This is a blocking call, it returns once a subscriber has subscribed to
   the publisher's topic or throws on timeout
8. `minimal_publisher` executable item is added to the executor with a
   periodic timeout of `500 ms`. When the timer expires,
   the executor calls the `execute_impl` method of the `minimal_publisher`.
9. Instantiates an RAII runner to run the executor on a separate thread
   while the main thread is handling interrupt signals
10. After this line, memory allocation should not take place. The
   [](apex::event::monitored_process) is passed in
   order to report the process has been initialized successfully to the
   [Process Manager](process-manager-design.md)
11. Starts an execution thread. This call returns immediately
    and the `runner` keeps going indefinitely
12. The method [](apex::interrupt_handler::wait) returns when a signal is received
13. Shuts down the runner
14. Any caught exceptions are then handled. Note that `rclcpp` needs to be
    initialized before calling the Apex.Grace logger (e.g. `APEX_FATAL_R`)

## The subscriber node

### Constructor implementation

The `MinimalSubscriber` class inherits from the `apex_node_base` class.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_subscriber.cpp",
    {"tag": "//! [Constructor]", "skip_prefix": "//!"}, "cpp") }}

1. The subscriber `m_subscription` is initialized with the `create_polling_subscription()` method.
   The topic name, message type, and QOS used by the publisher and subscriber must match to allow
   the publisher and subscriber to communicate. For more details see the
   [QOS policies](configuring-qos.md) article
2. The Apex.Grace logger is initialized with the pointer to the
   related [](rclcpp::Node) and logger name

### Implementation of the `execute_impl()` method

A message is received and processed in the `execute_impl()` method.
The `execute_impl()` method will be called by the executor.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_subscriber.cpp",
    {"tag": "//! [function]", "skip_prefix": "//!"}, "cpp") }}

1. `take()` removes all the messages from the `PollingSubscription` queue while `read()`
   leaves the messages in the queue so that it can be accessed again later.
   Returns a sequence of messages. The size of the sequence corresponds to the number of messages
   in the queue. For more details refer to the [PollingSubscriptions](polling-subscriptions.md)
   article
2. Checks that the message is valid. The middleware may deliver
   samples that only have a valid header and no valid message. Accessing
   an invalid message payload can lead to undefined behavior
3. The method `data()` is part of the
   [](apex::dds_typesupport::LoanedSample) API and
   the second `data` is a member particular to the current message type,
   in this case the `std_msgs::msg::String` type

### Triggering subscriptions

Override the `get_triggering_subscriptions_impl()` method to declare the item's subscription as
a triggering subscription.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_subscriber.cpp",
    {"tag": "//! [Get triggering subscriptions]", "skip_prefix": "//!"}, "cpp") }}

## BUILD.bazel

The `BUILD.bazel` file is the input to the Bazel build system for building software packages.

Firstly load Bazel rules used in this build file:

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/BUILD.bazel",
    {"tag": "#! [load_statements]"}, "python") }}

Declares a minimal publisher C++ executable build target and lists all required dependencies. The
minimal subscriber executable is declared in the same way.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/BUILD.bazel",
    {"tag": "#! [publisher_cc_binary]"}, "python") }}

Additionally, [Apex.Grace Process Manager](/grace/configuration/process_manager/doc/process-manager-design.md)
can launch the publisher and subscriber with the resource creator daemon. For this, corresponding
targets need to be declared:

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/BUILD.bazel",
    {"tag": "#! [process_manager_and_ament_pkg_resources]"}, "python") }}

- [`ament_pkg_resources`](/tools/ament/rules_ament/doc/using-rules-ament.md)
    - to create an ament prefix directory within the Bazel runfiles environment (similar to
      Colcon install space)
- [`process_manager`](/grace/configuration/process_manager/doc/how-to-use-process-manager-with-bazel.md#defining-a-process_manager-target)
    - process manager executable with launch file, publisher, and subscriber executables

## Build and run the package

Build the target by `bazel build` command from any repository subfolder by specifying the path to a
package:

```shell dollar
bazel build //src/apex_os_minimal_pub_sub/...  # (1)!
```

1. `...` means build everything beneath this folder

Now, run the nodes with `bazel run`:

1. Terminal 1: run [Apex.Ida Resource Creator](<APEX_MIDDLEWARE_DOCS>/ida/resource_creator/doc/resource-creator.md):
    - `bazel run @apex//ida/resource_creator`
1. Terminal 2: run `pub_sub_exe` executable target with nodes:
    - `bazel run //src/apex_os_minimal_pub_sub:pub_sub_exe`

Press ++ctrl+c++ to stop the nodes.
