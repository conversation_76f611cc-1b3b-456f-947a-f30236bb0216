// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#include <chrono>
#include <memory>
#include <utility>

#include "apex_init/apex_init.hpp"
#include "cpputils/common_exceptions.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "logging/logging_macros.hpp"

#include "example_msgs/msg/example_interface.hpp"

namespace apex::examples
{

//! [Server class]
class Server : public apex::executor::apex_node_base
{
public:
  Server(const apex::string_strict256_t & node_name)
  : apex_node_base{node_name.c_str()}, m_logger{&get_rclcpp_node(), "Server"}
  {
    m_service =
      get_rclcpp_node()
        .create_polling_service<example_msgs::msg::ExampleInterface::ExampleMethod>(  //  (1)!
          "example_method",  //  (2)!
          rclcpp::DefaultQoS());
  }

private:
  bool execute_impl() override
  {
    for (const auto & request : m_service->take_request()) {
      if (request.info().valid()) {
        APEX_INFO(m_logger, "Request: ", request.data().input);

        // echo back
        auto response = m_service->borrow_loaned_response();
        response->output = request.data().input;

        auto header = request.request_header();
        m_service->send_response(header, std::move(response));
      }
    }
    return true;
  }

  apex::executor::service_list get_triggering_services_impl() const override
  {
    return {m_service};
  }

  rclcpp::PollingService<example_msgs::msg::ExampleInterface::ExampleMethod>::SharedPtr m_service;
  apex::logging::Logger<> m_logger;
};
//! [Server class]

}  // namespace apex::examples

int32_t main(const int32_t argc, char ** const argv)
{
  int32_t result{};

  try {
    auto scoped_init = apex::scoped_init(argc, argv, false);

    const apex::interrupt_handler::installer interrupt_handler_installer{};

    auto server = std::make_shared<apex::examples::Server>("server_node");

    apex::event::monitored_process p{apex::event::sender_state::DoNotSyncWithDispatcher};
    const auto executor = apex::executor::executor_factory::create(p);

    (void)executor->add(server);
    const apex::executor::executor_runner runner{
      apex::executor::executor_runner::deferred | apex::executor::executor_runner::interrupt,
      *executor};

    scoped_init.post_init(p);

    runner.issue();
    apex::interrupt_handler::wait();
    runner.stop();
  } catch (const std::exception & e) {
    if (rclcpp::ok()) {
      APEX_FATAL_R(e.what());
    } else {
      std::cerr << e.what() << "\n";
    }
    result = 2;
  } catch (...) {
    if (rclcpp::ok()) {
      APEX_FATAL_R("Unknown error occurred");
    } else {
      std::cerr << "Unknown error occurred"
                << "\n";
    }
    result = -1;
  }

  return result;
}
