// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#include <chrono>
#include <memory>
#include <utility>

#include "apex_init/apex_init.hpp"
#include "cpputils/common_exceptions.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "logging/logging_macros.hpp"
#include "timer_service/clock_timer_service.hpp"

//! [interface headers]
#include "example_msgs/msg/example_interface.hpp"  //  (1)!
//! [interface headers]

using namespace std::chrono_literals;

namespace apex::examples
{

//! [Publisher class]
class Publisher : public apex::executor::apex_node_base
{
public:
  Publisher(const apex::string_strict256_t & node_name) : apex_node_base{node_name.c_str()}
  {
    // Adjusting the QoS setting for the serialization size of samples might be needed for big
    // message types.
    auto qos = rclcpp::DefaultQoS{};
    qos.resource_limits_max_non_self_contained_type_serialized_size(128);  //  (1)!

    m_publisher = get_rclcpp_node()
                    .create_publisher<example_msgs::msg::ExampleInterface::ExampleEvent>(  //  (2)!
                      "example_event_topic",  //  (3)!
                      qos);
  }

  void wait_for_matched() const
  {
    APEX_INFO(m_logger, "waiting for matched subscriber...");
    m_publisher->wait_for_matched(1U, std::chrono::seconds{300});
  }

private:
  bool execute_impl() override  //  (4)!
  {
    auto loaned_sample = m_publisher->borrow_loaned_message();
    loaned_sample->data = m_counter++;
    APEX_INFO(m_logger, "Publishing: ", loaned_sample->data);
    m_publisher->publish(std::move(loaned_sample));
    return true;
  }

  rclcpp::Publisher<example_msgs::msg::ExampleInterface::ExampleEvent>::SharedPtr m_publisher;
  int64_t m_counter{0};
  apex::logging::Logger<> m_logger{&get_rclcpp_node(), "Publisher"};
};
//! [Publisher class]

}  // namespace apex::examples

int32_t main(const int32_t argc, char ** const argv)
{
  int32_t result{};


  try {
    auto scoped_init = apex::scoped_init(argc, argv, false);

    const apex::interrupt_handler::installer interrupt_handler_installer{};

    auto publisher = std::make_shared<apex::examples::Publisher>("publisher_node");

    apex::event::monitored_process p{apex::event::sender_state::DoNotSyncWithDispatcher};
    const auto executor = apex::executor::executor_factory::create(p);
    apex::timer_service::steady_clock_timer_service timer_srv;
    publisher->wait_for_matched();
    (void)executor->add_periodic(publisher, timer_srv, 1s);
    const apex::executor::executor_runner runner{
      apex::executor::executor_runner::deferred | apex::executor::executor_runner::interrupt,
      *executor};

    scoped_init.post_init(p);

    runner.issue();
    apex::interrupt_handler::wait();
    runner.stop();
  } catch (const std::exception & e) {
    if (rclcpp::ok()) {
      APEX_FATAL_R(e.what());
    } else {
      std::cerr << e.what() << "\n";
    }
    result = 2;
  } catch (...) {
    if (rclcpp::ok()) {
      APEX_FATAL_R("Unknown error occurred");
    } else {
      std::cerr << "Unknown error occurred"
                << "\n";
    }
    result = -1;
  }

  return result;
}
