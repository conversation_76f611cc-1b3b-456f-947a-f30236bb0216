// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#include <chrono>
#include <memory>
#include <utility>

#include "apex_init/apex_init.hpp"
#include "cpputils/common_exceptions.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "logging/logging_macros.hpp"
#include "timer_service/clock_timer_service.hpp"

#include "example_msgs/msg/example_interface.hpp"

using namespace std::chrono_literals;

namespace apex::examples
{

class Client : public apex::executor::apex_node_base
{
public:
  Client(const apex::string_strict256_t & node_name, const apex::string_strict256_t & topic)
  : apex_node_base{node_name.c_str()},
    m_client{
      get_rclcpp_node().create_polling_client<example_msgs::msg::ExampleInterface::ExampleMethod>(
        topic.c_str(), rclcpp::DefaultQoS())},
    m_logger{&get_rclcpp_node(), "Client"}
  {
  }

  void wait_for_matched() const
  {
    APEX_INFO(m_logger, "waiting for matched service...");
    m_client->wait_for_service(std::chrono::seconds{300});
  }

private:
  bool execute_impl() override
  {
    const auto responses = m_client->take_response(1);
    if (!responses.empty()) {
      const auto response = responses[0];
      if (response.info().valid()) {
        APEX_INFO(m_logger, "Response: ", response.data().output);
      }
    } else {
      auto request = m_client->borrow_loaned_request();
      request->input = m_counter;

      if (const auto result = m_client->try_async_send_request(std::move(request));
          !result.has_error()) {
        APEX_INFO(m_logger, "Requested: ", m_counter);
        ++m_counter;
      } else {
        const auto error = result.error();
        APEX_WARN(m_logger, "Failure sending request: ", error);
        if (error == RCL_RET_PENDING_REQUEST_QUEUE_IS_FULL) {
          APEX_INFO(m_logger, "Pending request queue is full, removing all pending requests");
          m_client->remove_all_pending_requests();
        } else {
          throw apex::runtime_error("Unexpected error sending a request: ", error);
        }
      }
    }
    return true;
  }

  apex::executor::client_list get_triggering_clients_impl() const override
  {
    return {m_client};
  }

  const rclcpp::PollingClient<example_msgs::msg::ExampleInterface::ExampleMethod>::SharedPtr
    m_client;
  std::int64_t m_counter{};
  apex::logging::Logger<> m_logger;
};

}  // namespace apex::examples

int32_t main(const int32_t argc, char ** const argv)
{
  int32_t result{};

  try {
    auto scoped_init = apex::scoped_init(argc, argv, false);

    const apex::interrupt_handler::installer interrupt_handler_installer{};

    auto client = std::make_shared<apex::examples::Client>("client_node", "example_method");

    apex::event::monitored_process p{apex::event::sender_state::DoNotSyncWithDispatcher};
    const auto executor = apex::executor::executor_factory::create(p);
    apex::timer_service::steady_clock_timer_service timer_srv;
    client->wait_for_matched();
    (void)executor->add_periodic(client, timer_srv, 1500ms);
    const apex::executor::executor_runner runner{
      apex::executor::executor_runner::deferred | apex::executor::executor_runner::interrupt,
      *executor};

    scoped_init.post_init(p);

    runner.issue();
    apex::interrupt_handler::wait();
    runner.stop();
  } catch (const std::exception & e) {
    if (rclcpp::ok()) {
      APEX_FATAL_R(e.what());
    } else {
      std::cerr << e.what() << "\n";
    }
    result = 2;
  } catch (...) {
    if (rclcpp::ok()) {
      APEX_FATAL_R("Unknown error occurred");
    } else {
      std::cerr << "Unknown error occurred"
                << "\n";
    }
    result = -1;
  }
  return result;
}
