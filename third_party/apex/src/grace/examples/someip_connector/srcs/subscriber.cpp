// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#include <chrono>
#include <memory>
#include <utility>

#include "apex_init/apex_init.hpp"
#include "cpputils/common_exceptions.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "logging/logging_macros.hpp"

#include "example_msgs/msg/example_interface.hpp"

namespace apex::examples
{

class Subscriber : public apex::executor::apex_node_base
{
public:
  Subscriber(const apex::string_strict256_t & node_name, const apex::string_strict256_t & topic)
  : apex_node_base{node_name.c_str()},
    m_subscription{
      get_rclcpp_node()
        .create_polling_subscription<example_msgs::msg::ExampleInterface::ExampleEvent>(
          topic.c_str(), rclcpp::DefaultQoS())},
    m_logger{&get_rclcpp_node(), "Subscriber"}
  {
  }

  void wait_for_matched() const
  {
    APEX_INFO(m_logger, "waiting for matched publisher...");
    m_subscription->wait_for_matched(1U, std::chrono::seconds{300});
  }

private:
  bool execute_impl() override
  {
    for (const auto & msg : m_subscription->take()) {
      if (msg.info().valid()) {
        APEX_INFO(m_logger, "Received: ", msg.data().data);
      }
    }
    return true;
  }

  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {m_subscription};
  }

  const rclcpp::PollingSubscription<example_msgs::msg::ExampleInterface::ExampleEvent>::SharedPtr
    m_subscription;
  apex::logging::Logger<> m_logger;
};

}  // namespace apex::examples

int32_t main(const int32_t argc, char ** const argv)
{
  int32_t result{};

  try {
    auto scoped_init = apex::scoped_init(argc, argv, false);
    const apex::interrupt_handler::installer interrupt_handler_installer{};

    auto subscriber =
      std::make_shared<apex::examples::Subscriber>("subscriber_node", "example_event_topic");

    apex::event::monitored_process p{apex::event::sender_state::DoNotSyncWithDispatcher};
    const auto executor = apex::executor::executor_factory::create(p);
    subscriber->wait_for_matched();
    (void)executor->add(subscriber);
    const apex::executor::executor_runner runner{
      apex::executor::executor_runner::deferred | apex::executor::executor_runner::interrupt,
      *executor};

    scoped_init.post_init(p);

    runner.issue();
    apex::interrupt_handler::wait();
    runner.stop();
  } catch (const std::exception & e) {
    if (rclcpp::ok()) {
      APEX_FATAL_R(e.what());
    } else {
      std::cerr << e.what() << "\n";
    }
    result = 2;
  } catch (...) {
    if (rclcpp::ok()) {
      APEX_FATAL_R("Unknown error occurred");
    } else {
      std::cerr << "Unknown error occurred"
                << "\n";
    }
    result = -1;
  }
  return result;
}
