logging:
  level: TRACE
  enabled: true

process-manager:
  process-groups:
    - name: "functional_processes"
      init-state: "ON"
      processes:
        - name: "gateway1"
          stderr-handlers: ["Console"]
          stdout-handlers: ["Console"]
          default-startup-config:
            path: "../apex/grace/examples/someip_connector/arxml/host1_someip_gateway"
        - name: "publisher"
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "../apex/grace/examples/someip_connector/arxml/publisher"
        - name: "server"
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "../apex/grace/examples/someip_connector/arxml/server"

        - name: "gateway2"
          stderr-handlers: ["Console"]
          stdout-handlers: ["Console"]
          default-startup-config:
            path: "../apex/grace/examples/someip_connector/arxml/host2_someip_gateway"
        - name: "subscriber"
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "../apex/grace/examples/someip_connector/arxml/subscriber"
        - name: "client"
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "../apex/grace/examples/someip_connector/arxml/client"

      states:
        - name: "ON"
          processes:
            - name: "gateway1"
            - name: "publisher"
            - name: "server"

            - name: "gateway2"
              startup-config:
                extra-env-vars:
                  - { ROS_DOMAIN_ID: "2" }
            - name: "subscriber"
              startup-config:
                extra-env-vars:
                  - { ROS_DOMAIN_ID: "2" }
            - name: "client"
              startup-config:
                extra-env-vars:
                  - { ROS_DOMAIN_ID: "2" }
          process-dependencies:
            direct-dependency:
              - process: "server"
                depends-on: "gateway1"
              - process: "publisher"
                depends-on: "gateway1"

              - process: "client"
                depends-on: "gateway2"
              - process: "subscriber"
                depends-on: "gateway2"

