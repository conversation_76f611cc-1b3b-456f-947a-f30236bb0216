load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")

#! [cc_msgs_library]
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cc_msgs_library")

#! [cc_msgs_library]

#! [host1 vsomeip_gateway]
load("@apex//ida/bazel:defs.bzl", "vsomeip_gateway")

#! [host1 vsomeip_gateway]

load("@apex//tools/bazel/rules_containers:defs.bzl", "run_in_containers")

# ""
package(default_visibility = ["//visibility:public"])

#! [cc_msgs_library]
cc_msgs_library(
    name = "example_interfaces",
    arxml_srcs = [
        "msg/ExampleInterface.arxml",  #  (1)!
        "msg/MachineDesign.arxml",
        "msg/SomeipDeployment.arxml",
        "msg/SomeipInstances.arxml",  #  (2)!
        "msg/StdTypes.arxml",
    ],
    pkg_name = "example_msgs",
)
#! [cc_msgs_library]

#! [publisher]
cc_binary(
    name = "publisher",
    srcs = ["@apex//grace/examples/someip_connector:srcs/publisher.cpp"],
    deps = [
        ":example_interfaces",  #  (1)!
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)
#! [publisher]

cc_binary(
    name = "subscriber",
    srcs = ["@apex//grace/examples/someip_connector:srcs/subscriber.cpp"],
    deps = [
        ":example_interfaces",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "client",
    srcs = ["@apex//grace/examples/someip_connector:srcs/client.cpp"],
    deps = [
        ":example_interfaces",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "server",
    srcs = ["@apex//grace/examples/someip_connector:srcs/server.cpp"],
    deps = [
        ":example_interfaces",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

#! [host1 vsomeip_gateway]
vsomeip_gateway(
    name = "host1_someip_gateway",
    config = "@apex//grace/examples/someip_connector:config/host1.vsomeip_gateway.yaml",  # (1)!
    deps = [":example_interfaces"],  #  (2)!
)
#! [host1 vsomeip_gateway]

#! [host2 vsomeip_gateway]
vsomeip_gateway(
    name = "host2_someip_gateway",
    config = "@apex//grace/examples/someip_connector:config/host2.vsomeip_gateway.yaml",
    deps = [":example_interfaces"],
)
#! [host2 vsomeip_gateway]

#! [host1 process_manager]
process_manager(
    name = "host1_process_manager",
    data = [
        ":host1_someip_gateway",
        ":publisher",
        ":server",
    ],
    launch_file = "config/host1.launch.yaml",
)
#! [host1 process_manager]

process_manager(
    name = "host2_process_manager",
    data = [
        ":client",
        ":host2_someip_gateway",
        ":subscriber",
    ],
    launch_file = "config/host2.launch.yaml",
)

run_in_containers(
    name = "example",
    compose_file = "@apex//grace/examples/someip_connector:config/docker-compose.yml",
    enable_tmux = True,
    host_executable_map = {
        "host1": ":host1_process_manager",
        "host2": ":host2_process_manager",
    },
)

# Used by the documentation
filegroup(
    name = "doc_files",
    srcs = [
        "BUILD.bazel",
        "config/host1.launch.yaml",
        "config/host2.launch.yaml",
        "msg/ExampleInterface.arxml",
        "msg/SomeipInstances.arxml",
    ],
    visibility = ["//grace/connectors/someip/doc:__subpackages__"],
)

process_manager(
    name = "host_all_process_manager",
    data = [
        ":client",
        ":host1_someip_gateway",
        ":host2_someip_gateway",
        ":publisher",
        ":server",
        ":subscriber",
    ],
    launch_file = "config/host_all.launch.yaml",
)
