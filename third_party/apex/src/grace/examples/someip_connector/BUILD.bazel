# ""
package(default_visibility = ["//visibility:public"])

exports_files(
    glob([
        "config/*",
        "srcs/*",
    ]),
    visibility = [":__subpackages__"],
)

# Used by the documentation
filegroup(
    name = "doc_files",
    srcs = [
        "config/host1.vsomeip_gateway.yaml",
        "config/host2.vsomeip_gateway.yaml",
        "srcs/publisher.cpp",
        "srcs/server.cpp",
    ],
    visibility = ["//grace/connectors/someip:__subpackages__"],
)
