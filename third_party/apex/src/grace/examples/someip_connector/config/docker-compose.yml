version: '2.4'

# Common base settings shared across all services
x-base: &base-mixin
  cap_add: [NET_ADMIN]
  image: 'registry-customer.apex.ai/apexai/apexos/apex_ai_integration_testing:22.04-amd64'
  restart: 'no'
  shm_size: '4gb'
  command: "bash -c 'echo \"container startup complete\" && trap : TERM INT; sleep infinity & wait'"

# Definition of services (aka containers)
services:
    host1:
      <<: *base-mixin
      hostname: host1
      networks:
        vsomeip-net:
          ipv4_address: ************
    host2:
      <<: *base-mixin
      hostname: host2
      networks:
        vsomeip-net:
          ipv4_address: ************
networks:
  vsomeip-net:
    driver: bridge
    ipam:
     config:
       - subnet: ************/24
         gateway: ************
