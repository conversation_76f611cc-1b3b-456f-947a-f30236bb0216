load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cc_msgs_library")
load("@apex//ida/bazel:defs.bzl", "vsomeip_gateway")
load("@apex//tools/bazel/rules_containers:defs.bzl", "run_in_containers")

# ""
package(default_visibility = ["//visibility:public"])

cc_msgs_library(
    name = "example_interfaces",
    e2e_config = "config/e2e_config.json",
    fdepl_srcs = ["config/Deployment.fdepl"],
    fidl_srcs = ["msg/ExampleInterface.fidl"],
    pkg_name = "example_msgs",
)

cc_binary(
    name = "publisher",
    srcs = ["@apex//grace/examples/someip_connector:srcs/publisher.cpp"],
    deps = [
        ":example_interfaces",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "subscriber",
    srcs = ["@apex//grace/examples/someip_connector:srcs/subscriber.cpp"],
    deps = [
        ":example_interfaces",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "client",
    srcs = ["@apex//grace/examples/someip_connector:srcs/client.cpp"],
    deps = [
        ":example_interfaces",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "server",
    srcs = ["@apex//grace/examples/someip_connector:srcs/server.cpp"],
    deps = [
        ":example_interfaces",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

vsomeip_gateway(
    name = "host1_someip_gateway",
    config = "@apex//grace/examples/someip_connector:config/host1.vsomeip_gateway.yaml",
    deps = [":example_interfaces"],
)

vsomeip_gateway(
    name = "host2_someip_gateway",
    config = "@apex//grace/examples/someip_connector:config/host2.vsomeip_gateway.yaml",
    deps = [":example_interfaces"],
)

process_manager(
    name = "host1_process_manager",
    data = [
        ":host1_someip_gateway",
        ":publisher",
        ":server",
    ],
    launch_file = "config/host1.launch.yaml",
)

process_manager(
    name = "host2_process_manager",
    data = [
        ":client",
        ":host2_someip_gateway",
        ":subscriber",
    ],
    launch_file = "config/host2.launch.yaml",
)

run_in_containers(
    name = "example",
    compose_file = "@apex//grace/examples/someip_connector:config/docker-compose.yml",
    enable_tmux = True,
    host_executable_map = {
        "host1": ":host1_process_manager",
        "host2": ":host2_process_manager",
    },
)
