# ""
package(default_visibility = ["//visibility:public"])

cc_binary(
    name = "poll",
    srcs = ["src/poll.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/interrupt",
    ],
)

cc_binary(
    name = "pollfd",
    srcs = ["src/pollfd.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/interrupt",
    ],
)

cc_binary(
    name = "wait",
    srcs = ["src/wait.cpp"],
    linkopts = select({
        "@platforms//os:linux": ["-lpthread"],
        "//conditions:default": [],
    }),
    tags = ["exclude_sca"],
    deps = [
        "//common/interrupt",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "src/poll.cpp",
        "src/pollfd.cpp",
        "src/wait.cpp",
    ],
    visibility = [
        "//common/interrupt/design:__pkg__",
    ],
)
