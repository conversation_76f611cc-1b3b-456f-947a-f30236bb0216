/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file declares a fake graphical framework for demonstration purposes.

#ifndef TYPE_ADAPTATION_EXAMPLES_FAKE_GRAPHICAL_FRAMEWORK_HPP
#define TYPE_ADAPTATION_EXAMPLES_FAKE_GRAPHICAL_FRAMEWORK_HPP

#include <cstdint>
#include <iostream>
#include <memory>

//! [fake_graphical_framework]
namespace gfw
{

class Image  // (1)!
{
public:
  Image() = default;
  Image(const std::uint8_t * data, std::uint32_t width, std::uint32_t height, std::uint32_t step);
  ~Image() = default;
  Image(const Image & other);
  Image & operator=(const Image & other);
  Image(Image && other) noexcept;
  Image & operator=(Image && other) noexcept;

  const std::uint8_t * data() const;
  std::uint32_t width() const;
  std::uint32_t height() const;
  std::uint32_t step() const;

private:
  std::unique_ptr<std::uint8_t[]> data_{};
  std::uint32_t width_{0};
  std::uint32_t height_{0};
  std::uint32_t step_{0};
};

class GraphicalFramework  // (2)!
{
public:
  void detect_objects(gfw::Image & image) const
  {
    std::cout << "Detecting objects on the image of size: " << image.width() << "x"
              << image.height() << " with step_: " << image.step() << std::endl;
    // Simulate some processing
  }

  void render_image(const gfw::Image & image) const
  {
    std::cout << "Rendering image of size: " << image.width() << "x" << image.height()
              << " with step_: " << image.step() << std::endl;
    // Simulate rendering
  }
};
}  // namespace gfw
//! [fake_graphical_framework]

#endif  // TYPE_ADAPTATION_EXAMPLES_FAKE_GRAPHICAL_FRAMEWORK_HPP
