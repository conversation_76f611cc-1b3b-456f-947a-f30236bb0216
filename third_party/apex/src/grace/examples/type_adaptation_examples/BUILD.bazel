load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")

# ""
package(default_visibility = ["//visibility:public"])

cc_library(
    name = "fake_graphical_framework",
    testonly = True,
    srcs = ["fake_graphical_framework.cpp"],
    hdrs = ["fake_graphical_framework.hpp"],
)

cc_library(
    name = "type_adapters",
    testonly = True,
    hdrs = ["type_adapters.hpp"],
    deps = [
        ":fake_graphical_framework",
        "@apex//grace/interfaces/sensor_msgs",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

apex_cc_test(
    name = "type_adaptation_example",
    srcs = ["type_adaptation_example.cpp"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":fake_graphical_framework",
        ":type_adapters",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "fake_graphical_framework.hpp",
        "type_adaptation_example.cpp",
        "type_adapters.hpp",
    ],
    visibility = [":__subpackages__"],
)
