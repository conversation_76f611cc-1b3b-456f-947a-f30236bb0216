# ROS 2 Type Adaptation

## Introduction

Type Adaptation in ROS 2 is a mechanism that allows nodes to internally use different message types
than what they publish or subscribe to. This feature enables developers to work with custom or
optimized data structures while maintaining compatibility with standard ROS interfaces.

After defining a type adapter, custom data structures can be used directly by publishers and
subscribers, which helps to avoid additional work for the programmer and potential sources of
errors. This is especially useful when working with complex data types, such as when converting
OpenCV's `cv::Mat` to ROS's `sensor_msgs/msg/Image` type.

## Usage

In order to adapt a custom type to a ROS type, the user must create a template specialization of
`rclcpp::TypeAdapter` structure for the custom type. This specialization must:

- change `is_specialized` to `std::true_type`
- specify the custom type with `using custom_type = ...`
- specify the ROS type with `using ros_message_type = ...`
- provide static convert functions with the signatures:
    - `static void convert_to_ros(const custom_type &, ros_message_type &)`
    - `static void convert_to_custom(const ros_message_type &, custom_type &)`

Here is an example for adapting `std::string` to the `std_msgs::msg::String` ROS message type:

{{ code_snippet("grace/examples/ros2_examples/rclcpp/topics/minimal_publisher/member_function_with_type_adapter.cpp",
    {"tag": "//! [type_adapter_example]"}, "cpp") }}

The adapter can then be used when creating a publisher or subscription, e.g.:

```cpp
using MyAdaptedType = TypeAdapter<std::string, std_msgs::msg::String>;
auto pub = node->create_publisher<MyAdaptedType>("topic", 10);
auto sub = node->create_subscription<MyAdaptedType>("topic", 10, [](const std::string & msg) {...});
```

For a more declarative approach with less ambiguous syntax, consider using the `adapt_type::as`
metafunctions:

```cpp
using AdaptedType = rclcpp::adapt_type<std::string>::as<std_msgs::msg::String>;
auto pub = node->create_publisher<AdaptedType>(...);
```

It's possible to associate a custom type with a single ROS message type, allowing for more concise
code when creating entities, e.g.:

```cpp
// First, declare the association, similar to avoiding
// namespace usage in C++ with `using std::vector;`.
RCLCPP_USING_CUSTOM_TYPE_AS_ROS_MESSAGE_TYPE(std::string, std_msgs::msg::String);

// After this declaration, entities can be created using only the
// custom type, as the ROS message type is automatically implied.
auto pub = node->create_publisher<std::string>(...);
```

### Intra-process optimization

!!! note
    Intra-process optimization is available only for callback-based [](rclcpp::Subscription).

When the publisher and the subscriber use the same custom type within a single process, enabling
intra-process optimization is beneficial. This can be done by setting `use_intra_process_comms` in
the node options during node construction:

```cpp

MinimalSubscriber() : rclcpp::Node("minimal_subscriber", rclcpp::NodeOptions().use_intra_process_comms(true))
{}
```

Enabling intra-process communication provides two main performance-related advantages:

- Avoids potentially costly and unnecessary conversions to and from ROS message types at both the
  publisher and subscriber ends
- Allows a custom type to retain custom "handles", enabling the publisher to pass these handles
  directly to the subscription. These handles may refer to hardware accelerators, which means that
  the system can use the hardware accelerators without requiring CPU synchronization for publishing
  to the next node

### Example

For demonstration purposes, this example simulates the following setup:

```mermaid
graph LR
    A[CameraNode] -->|/camera_image_rgb| B[VisionNode]
    B -->|/image_with_objects| C[RendererNode]
```

- `CameraNode`: simulates capturing images from a camera and publishing them as
  `sensor_msgs::msg::Image`
- `VisionNode`: uses a type adapter to convert received `sensor_msgs::msg::Image` messages into the
  `gfw::Image` type utilized by the mock `GraphicalFramework`
- `RendererNode`: also uses a type adapter to work directly with `GraphicalFramework`'s types rather
  than handling `sensor_msgs::msg::Image` messages explicitly

#### Simulated `GraphicalFramework`

Let's start by declaring a fake `GraphicalFramework` and a custom `gfw::Image` type:

{{ code_snippet("grace/examples/type_adaptation_examples/fake_graphical_framework.hpp",
    {"tag": "//! [fake_graphical_framework]"}, "cpp") }}

1. A dummy data type that holds a simple byte array representing an RGB image
2. A non-functional framework implementation for demonstration purposes. In a real-world scenario,
   this might be a more complex graphics or computer vision library with its own data structures

#### Type adapters

Below is the header file containing the type adapters:

{{ code_snippet("grace/examples/type_adaptation_examples/type_adapters.hpp",
    {"tag": "//! [type_adapter]"}, "cpp") }}

1. The `TypeAdapter` specialization must be declared inside the `rclcpp` namespace
2. `std::true_type` indicates that this `TypeAdapter` exists and can be used
3. Use `gfw::Image` from `GraphicalFramework` as our custom type
4. Define `sensor_msgs::msg::Image` as the ROS message type for communication
5. Implement the static function that converts from the custom type to the ROS message type
6. Implement the static function that converts from the ROS message type to the custom type
7. This macro enables direct use of the custom type when creating publishers and subscriptions,
   e.g., `node->create_publisher<gfw::Image>(...);`
8. Create a type alias for convenience, which will be used when creating publishers and
   subscriptions

The following example demonstrates the implementation of three nodes that use type adaptation.

#### `CameraNode` implementation

{{ code_snippet("grace/examples/type_adaptation_examples/type_adaptation_example.cpp",
    {"tag": "//! [camera_node]"}, "cpp") }}

1. Creates a standard publisher using the `sensor_msgs::msg::Image` ROS message type
2. The node publishes [a loaned `sensor_msgs::msg::Image`
   message](/grace/rmw_ida/typesupport/design/loaned-sample-design.md)

#### `VisionNode` implementation

{{ code_snippet("grace/examples/type_adaptation_examples/type_adaptation_example.cpp",
    {"tag": "//! [vision_node]"}, "cpp") }}

1. Creates a [`PollingSubscription`](/grace/ros/rclcpp/rclcpp/doc/design/polling-subscriptions.md)
   with `GfwImageTypeAdapter`, which is an alias for `rclcpp::TypeAdapter<gfw::Image, sensor_msgs::msg::Image>`
2. Creates a publisher directly using the custom type, which is enabled by the
   `RCLCPP_USING_CUSTOM_TYPE_AS_ROS_MESSAGE_TYPE` macro declared in the type adapters header file
3. The [](apex::dds_typesupport::TypeAdaptedLoanedSample::data) method converts the received ROS
   message to the custom type (note that data copying occurs during this conversion) and returns a
   const reference. The allocation of the custom type and conversion are done lazily
4. Publishes the resulting `gfw::Image` data directly

#### `RendererNode` implementation

{{ code_snippet("grace/examples/type_adaptation_examples/type_adaptation_example.cpp",
    {"tag": "//! [renderer_node]"}, "cpp") }}

1. Creates a subscription with a callback that directly receives the `gfw::Image` data type
   (conversion happens internally when the ROS message is received)
2. Implements a callback method that receives the `gfw::Image` data type, allowing direct handling
   by the framework without conversion boilerplate code

#### Conclusion

This example demonstrates how ROS 2 Type Adaptation can simplify development when working with
custom data types:

1. The conversion code is defined once and used across multiple nodes
1. Node code focuses on business logic rather than data marshaling
1. The pattern enables easier integration with external libraries like the simulated
  `GraphicalFramework` in this example
1. However, it comes with memory and runtime overhead, as zero-copy is eliminated when data is
   transformed between different representations

Observe how the `VisionNode` receives ROS messages and adapts them to `gfw::Image` for processing
while the `RendererNode` works exclusively with the custom type. This approach decouples application
logic from ROS message handling, resulting in more maintainable code and allowing developers to work
with the most convenient data representations for their specific use cases.

## Usage considerations

### Benefits

- **Encapsulated Conversion Logic**: The conversion code is defined once in the adapter rather than
  being scattered throughout application code
- **Specialized Processing**: Adapt messages to formats that better suit specific algorithms or
  libraries
- **Legacy Support**: Interface with older systems while using newer data structures

### Drawbacks

- **Runtime Cost**: The adaptation process introduces overhead by converting (eventually coping)
  data from one type to another
- **Memory Overhead**: Having multiple representations of the same data simultaneously increases
  memory usage
- **Maintenance Overhead**: Need to maintain adapter code alongside message definitions
- **Debugging Challenges**: May complicate tracing issues across type boundaries
- **Additional Complexity**: Introduces another layer in the communication pipeline

## References

- [REP 2007: Type Adaptation Feature](https://ros.org/reps/rep-2007.html)
- [ROS 2 Humble Hawksbill: Support Type Adaption for Publishers and
  Subscriptions](https://docs.ros.org/en/rolling/Releases/Release-Humble-Hawksbill.html#support-type-adaption-for-publishers-and-subscriptions)
- [`PollingSubscription`](/grace/ros/rclcpp/rclcpp/doc/design/polling-subscriptions.md)
- [`LoanedSamples`](/grace/rmw_ida/typesupport/design/loaned-sample-design.md)
