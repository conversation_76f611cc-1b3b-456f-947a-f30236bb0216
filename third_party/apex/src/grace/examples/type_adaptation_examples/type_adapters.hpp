/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file contains the type adaptors for the type adaptation example.

#ifndef TYPE_ADAPTATION_EXAMPLES_TYPE_ADAPTORS_HPP
#define TYPE_ADAPTATION_EXAMPLES_TYPE_ADAPTORS_HPP

#include <cstring>

#include "fake_graphical_framework.hpp"
#include "rclcpp/type_adapter.hpp"

#include "sensor_msgs/msg/image.hpp"

//! [type_adapter]
namespace rclcpp
{
template <>
struct TypeAdapter<gfw::Image, sensor_msgs::msg::Image>  // (1)!
{
  using is_specialized = std::true_type;  // (2)!
  using custom_type = gfw::Image;  // (3)!
  using ros_message_type = sensor_msgs::msg::Image;  // (4)!

  static void convert_to_ros_message(const custom_type & source,
                                     ros_message_type & destination)  // (5)!
  {
    destination.width = source.width();
    destination.height = source.height();
    destination.step = source.step();
    destination.encoding = "rgb8";

    const std::size_t data_size = source.height() * source.step();
    destination.data.resize(data_size);
    std::memcpy(destination.data.data(), source.data(), data_size);
  }

  static void convert_to_custom(const ros_message_type & source, custom_type & destination)  // (6)!
  {
    destination = custom_type(source.data.data(), source.width, source.height, source.step);
  }
};
}  // namespace rclcpp

RCLCPP_USING_CUSTOM_TYPE_AS_ROS_MESSAGE_TYPE(gfw::Image, sensor_msgs::msg::Image);  // (7)!

using GfwImageTypeAdapter = rclcpp::TypeAdapter<gfw::Image, sensor_msgs::msg::Image>;  // (8)!
//! [type_adapter]

#endif  // TYPE_ADAPTATION_EXAMPLES_TYPE_ADAPTORS_HPP
