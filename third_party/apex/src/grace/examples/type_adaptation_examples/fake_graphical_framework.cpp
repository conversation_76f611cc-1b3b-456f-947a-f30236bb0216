/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.

#include "fake_graphical_framework.hpp"

#include <cstring>

namespace gfw
{

Image::Image(const std::uint8_t * data,
             std::uint32_t width,
             std::uint32_t height,
             std::uint32_t step)
: width_(width), height_(height), step_(step)
{
  if (data) {
    const std::size_t buffer_size = step_ * height_;
    data_ = std::make_unique<std::uint8_t[]>(buffer_size);
    std::memcpy(data_.get(), data, buffer_size);
  }
}

Image::Image(const Image & other)
: Image(other.data_.get(), other.width_, other.height_, other.step_)
{
}

Image & Image::operator=(const Image & other)
{
  return *this = Image(other);
}

Image::Image(Image && other) noexcept
: data_{std::move(other.data_)}, width_{other.width_}, height_{other.height_}, step_{other.step_}
{
  other.width_ = 0;
  other.height_ = 0;
  other.step_ = 0;
}

Image & Image::operator=(Image && other) noexcept
{
  if (this != &other) {
    data_ = std::move(other.data_);
    width_ = other.width_;
    height_ = other.height_;
    step_ = other.step_;

    other.width_ = 0;
    other.height_ = 0;
    other.step_ = 0;
  }
  return *this;
}

const std::uint8_t * Image::data() const
{
  return data_.get();
}

std::uint32_t Image::width() const
{
  return width_;
}

std::uint32_t Image::height() const
{
  return height_;
}

std::uint32_t Image::step() const
{
  return step_;
}

}  // namespace gfw
