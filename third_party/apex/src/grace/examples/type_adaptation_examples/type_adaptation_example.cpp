// Copyright 2025 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>

#include <functional>
#include <memory>

#include "apex_init/apex_init.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "fake_graphical_framework.hpp"
#include "type_adapters.hpp"

//! [camera_node]
class CameraNode : public rclcpp::Node
{
public:
  explicit CameraNode(const std::string & node_name, const std::string & topic)
  : rclcpp::Node{node_name},
    pub_{this->create_publisher<sensor_msgs::msg::Image>(topic, rclcpp::DefaultQoS())}  // (1)!
  {
  }

  void process()
  {
    auto image = pub_->borrow_loaned_message();
    pub_->publish(std::move(image));  // (2)!
  }

private:
  const rclcpp::Publisher<sensor_msgs::msg::Image>::SharedPtr pub_;
};
//! [camera_node]

//! [vision_node]
class VisionNode : public apex::executor::apex_node_base
{
public:
  explicit VisionNode(const apex::string_strict256_t & node_name,
                      const apex::string_strict256_t & sub_topic,
                      const apex::string_strict256_t & pub_topic)
  : apex_node_base{node_name.c_str()},
    sub_{
      get_rclcpp_node().create_polling_subscription<GfwImageTypeAdapter>(sub_topic.c_str(),  // (1)!
                                                                         rclcpp::DefaultQoS())},
    pub_{get_rclcpp_node().create_publisher<gfw::Image>(pub_topic, rclcpp::DefaultQoS())}  // (2)!
  {
  }

private:
  bool execute_impl() override
  {
    auto loaned_msgs{sub_->take()};
    for (const auto & msg : loaned_msgs) {
      if (msg.info().valid()) {
        auto img = msg.data();  // (3)!
        gfw_.detect_objects(img);
        pub_->publish(std::move(img));  // (4)!
      }
    }
    return true;
  }

  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {sub_};
  }

  const rclcpp::PollingSubscription<GfwImageTypeAdapter>::SharedPtr sub_;
  const rclcpp::Publisher<gfw::Image>::SharedPtr pub_;
  gfw::GraphicalFramework gfw_;
};
//! [vision_node]

//! [renderer_node]
class RendererNode : public rclcpp::Node
{
public:
  explicit RendererNode(const std::string & node_name, const std::string & topic)
  : rclcpp::Node{node_name},
    sub_{this->create_subscription<gfw::Image>(
      topic,
      rclcpp::DefaultQoS(),
      std::bind(&RendererNode::callback, this, std::placeholders::_1)  // (1)!
      )}
  {
  }

  void callback(const gfw::Image & img)  // (2)!
  {
    gfw_.render_image(img);
  }

private:
  const rclcpp::Subscription<gfw::Image>::SharedPtr sub_;
  gfw::GraphicalFramework gfw_;
};
//! [renderer_node]

TEST(TypeAdaptationExample, main_function_to_instantiate_example_nodes_making_sure_it_compiles)
{
  // This test is a placeholder to ensure that the example compiles and runs.
  // It does not perform any assertions or checks.

  auto scoped_init = apex::scoped_init(0U, nullptr, false);

  auto camera_node = std::make_shared<CameraNode>("camera_1", "camera_image_rgb");

  auto vision_node =
    std::make_shared<VisionNode>("computer_vision_node", "camera_image_rgb", "image_with_objects");

  auto legacy_vision_node = std::make_shared<RendererNode>("renderer_node", "image_with_objects");

  const auto executor = apex::executor::executor_factory::create();
  (void)executor->add(vision_node);
  const apex::executor::executor_runner runner{apex::executor::executor_runner::deferred,
                                               *executor};

  scoped_init.post_init();

  runner.issue();
  camera_node->process();
  runner.stop();
}
