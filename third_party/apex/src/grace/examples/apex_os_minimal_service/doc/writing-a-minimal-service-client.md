# Writing a minimal service and client

The service abstraction is based on the request-and-response communication pattern. As explained in
[Apex.Grace services](polling-client-service-design.md), a client (`rclcpp::PollingClient`)
may send requests to a service server (`rclcpp::PollingService`), which is responsible for processing
them and subsequently responding to the `rclcpp::PollingClient` with a result.

In this tutorial, two applications communicate with each other over a service. The client
application sends a request for the sum of two integers and the server application responds with
the result.

The `/add_two_ints` service is defined in the `AddTwoInts.srv` file and is comprised of two parts:
the request and the response.

The following shows the definition for the service:

{{ code_snippet("grace/examples/example_interfaces/srv/AddTwoInts.srv",
{"tag": "#! [AddTwoInts]", "skip_prefix": "#!"}, "srv") }}

The first section defines the request message and the second, separated by `---`, defines the
response message.

The corresponding ROS graph of the system is as follows:

```mermaid
    flowchart LR
        p<-->|service /add_two_ints|s
        subgraph Service application
        p[Apex.Grace node]
        end
        subgraph Client application
        s[Apex.Grace node]
        end
```

!!! note
    This example is adapted from the [Writing a simple service and client (C++)](https://docs.ros.org/en/rolling/Tutorials/Beginner-Client-Libraries/Writing-A-Simple-Cpp-Service-And-Client.html)
    ROS 2 article.

## The client node

### Included headers

{{ code_snippet("grace/examples/apex_os_minimal_service/src/minimal_client.cpp",
    {"tag": "//! [Client includes]", "skip_prefix": "//!"}, "cpp") }}

1. Includes APIs to initialize the Apex.Grace and ROS 2 context
2. Includes APIs for the Apex.Grace exceptions
3. Includes APIs for the Apex.Grace node
4. Includes APIs to instantiate the Apex.Grace executor
5. Includes APIs to configure the Apex.Grace executor
6. Includes APIs to set up the signal handler
7. Includes APIs for logging macros
8. Includes service definitions
9. Includes APIs for the Apex.Grace timer service

### Constructor implementation

The `MinimalClient` class inherits from the `apex_node_base` class.

{{ code_snippet("grace/examples/apex_os_minimal_service/src/minimal_client.cpp",
    {"tag": "//! [Constructor]", "skip_prefix": "//!"}, "cpp") }}

1. The public delegated constructor `apex_node_base` names the node `node_name`
2. The `get_rclcpp_node()` method provides related [](rclcpp::Node)
3. The publisher `m_client` is initialized with the `create_polling_client()`
   method, `example_interfaces::srv::AddTwoInts` service type, the service name `service_name`,
   and the QOS profile `rclcpp::ServicesQoS()`
4. Pointer to timer subscription which will trigger the execution and cause the
   client to either send out requests or take responses

### Waiting for matched server

As with publishers and subscribers, it is good practice for clients to wait for service servers to match
with them before leaving the initialization phase.

In this example, the application waits for a service server to match with a timeout of 5
seconds. The method should be called before the start of the executor.

{{ code_snippet("grace/examples/apex_os_minimal_service/src/minimal_client.cpp",
    {"tag": "//! [wait_for_matched]", "skip_prefix": "//!"}, "cpp") }}

### Implementation of the `execute_impl()` method

This is where the client logic is implemented. In this example, a request is prepared and published
periodically and any responses received are handled.

{{ code_snippet("grace/examples/apex_os_minimal_service/src/minimal_client.cpp",
    {"tag": "//! [function]", "skip_prefix": "//!"}, "cpp") }}

1. `take_response()` removes all the messages from the `PollingClient` queue while `read_response()`
   leaves the messages in the queue so that it can be accessed again later.
   Returns a sequence of messages. The size of the sequence corresponds to the number of messages
   in the queue. For more details refer to the [PollingClient & PollingService]
   (polling-client-service-design.md) article
2. Checks that the response message is valid. The middleware may deliver
   samples that only have valid metadata (SampleInfo) and no valid message. Accessing
   an invalid message payload can lead to undefined behavior
3. Logs the result of the service response with the `INFO` level. The method `data()` is part of the
   [](apex::dds_typesupport::ServiceLoanedSample) API and
   `sum` is a member particular to the response type,
   in this case the `example_interfaces::srv::AddTwoInts::Response` type
4. Sets the `example_interfaces::srv::AddTwoInts::Request` data. One of the integers to sum is
   increased by one each iteration
5. Logs the published request with the `INFO` level
6. `try_async_send_request` sends the service request. The function returns an expected result
   containing either an error or the request id.
7. The sending request result is checked to see if the request was sent successfully
8. The error code can be checked to see if the sending failed because the pending request queue
   was full
9. If the pending request queue was full it can be cleared and continue

### Triggering clients

Like with subscriptions (and service servers), clients can be designated to be "triggering" for the
underlying executable item. Override the `get_triggering_clients_impl()` method to declare a
clients as triggering.

{{ code_snippet("grace/examples/apex_os_minimal_service/src/minimal_client.cpp",
{"tag": "//! [Get triggering clients]", "skip_prefix": "//!"}, "cpp") }}

### Implementation of the `main()` function

In the `main()` function, the execution strategy is defined and interrupt signals, as well as
exceptions, are handled.

{{ code_snippet("grace/examples/apex_os_minimal_service/src/minimal_client.cpp",
    {"tag": "//! [Minimal client main function]", "skip_prefix": "//!"}, "cpp") }}

1. Apex.Grace handles the parsing of command line arguments. During the initialization
   phase, memory allocation is allowed
2. The [](apex::pre_init) method initializes Apex.Grace and ROS 2.
   This has to happen before a node can be used
3. Create a timer to execute the publisher periodically
4. Instantiates the client node
5. Creates an Apex.Grace executor
6. Waits for expected server and prevents unnecessary execution of the node.
   This is a blocking call, it returns once a server is matched or throws on timeout
7. `minimal_client` executable item is added to the executor with a
   periodic timeout of `500 ms`. When the timer expires,
   the executor calls the `execute_impl` method of the `minimal_client`.
8. Instantiates an RAII runner to run the executor on a separate thread
   while the main thread is handling interrupt signals
9. After this line, memory allocation should not take place
10. Starts an execution thread. This call returns immediately
   and the `runner` keeps going indefinitely
11. The method [](apex::interrupt_handler::wait) returns when a signal is received
12. Here `runner` goes out of scope and will automatically stop and join
    the worker thread so that potential stored exceptions are thrown
13. Any caught exceptions are then handled. Note that `rclcpp` needs to be
    initialized before calling the Apex.Grace logger (e.g. `APEX_FATAL_R`)

## The server node

### Constructor implementation

The `MinimalServer` class inherits from the `apex_node_base` class.

{{ code_snippet("grace/examples/apex_os_minimal_service/src/minimal_server.cpp",
    {"tag": "//! [Constructor]", "skip_prefix": "//!"}, "cpp") }}

1. The server `m_service` is initialized with the `create_polling_service()` method.
   The service name, service type, and QOS must match to allow
   the client and server to communicate. For more details see the
   [QOS policies](configuring-qos.md) article
2. The Apex.Grace logger is initialized with the pointer to the
   related [](rclcpp::Node) and logger name

### Implementation of the `execute_impl()` method

This is where the server logic is implemented. The request messages are received and processed in
the `execute_impl()` method.

{{ code_snippet("grace/examples/apex_os_minimal_service/src/minimal_server.cpp",
    {"tag": "//! [function]", "skip_prefix": "//!"}, "cpp") }}

1. `take_request()` removes all the messages from the `PollingService` queue while `read_request()`
   leaves the messages in the queue so that it can be accessed again later.
   Returns a sequence of messages. The size of the sequence corresponds to the number of messages
   in the queue. For more details refer to the [PollingClient & PollingService]
   (polling-client-service-design.md) article
2. Checks that the request is valid. The middleware may deliver
   samples that only have a valid header and no valid message. Accessing
   an invalid message payload can lead to undefined behavior
3. The method `request_header()` is part of the
   [](apex::dds_typesupport::ServiceLoanedSample) API and
   allows to get the Service request header containing information about the client ID and
   the service sequence number. The request header is required to send back the response.
4. The response is set by summing the request `a` and `b` data members
5. Logs the published response with the `INFO` level
6. `send_response()` sends the response. Note this function requires the response and the request
   header in order to relate the response with the corresponding request.

### Triggering services

Like with subscriptions (and clients), service servers can be designated to be "triggering" for the
underlying executable item. Override the `get_triggering_services_impl()` method to declare a
server as triggering.

{{ code_snippet("grace/examples/apex_os_minimal_service/src/minimal_server.cpp",
    {"tag": "//! [Get triggering services]", "skip_prefix": "//!"}, "cpp") }}

## package.xml

The `package.xml` manifest lists the dependencies to the other packages.

The following line defines the package name:

{{ code_snippet("grace/examples/apex_os_minimal_service/package.xml",
    {"tag": "<!--! [Name] -->"}, "xml") }}

The following build tool dependencies are always required:

{{ code_snippet("grace/examples/apex_os_minimal_service/package.xml",
    {"tag": "<!--! [Buildtool Dependencies] -->"}, "xml") }}

There are several includes in the `minimal_client.cpp` and the `minimal_server.cpp` files,
hence the following build dependencies:

{{ code_snippet("grace/examples/apex_os_minimal_service/package.xml",
    {"tag": "<!--! [Build Dependencies] -->"}, "xml") }}

The `apex_os_minimal_service` package depends on the following shared libraries when
its code is executed:

{{ code_snippet("grace/examples/apex_os_minimal_service/package.xml",
    {"tag": "<!--! [Execution Dependencies] -->"}, "xml") }}

## CMakeLists.txt

The `CMakeLists.txt` file is the input to the CMake build system for building
software packages.

{{ code_snippet("grace/examples/apex_os_minimal_service/CMakeLists.txt",
    {"tag": "#! [Project]"}, "cmake") }}

defines the name of the project. The name should match the package name defined
in the `package.xml` manifest.

The following lines are always required:

{{ code_snippet("grace/examples/apex_os_minimal_service/CMakeLists.txt",
    {"tag": "#! [Find Dependencies]"}, "cmake") }}

`ament_auto_find_build_dependencies()` uses the `<buildtool_depend>` and
`<build_depend>` entries in the `package.xml` to run `find_package()`.

Then the build targets need to be specified:

{{ code_snippet("grace/examples/apex_os_minimal_service/CMakeLists.txt",
    {"tag": "#! [Auto Add Executables]", "skip_prefix": "#!"}, "cmake") }}

Finally,

{{ code_snippet("grace/examples/apex_os_minimal_service/CMakeLists.txt",
    {"tag": "#! [Install Package]"}, "cmake") }}

uses the `<exec_depend>`, `<buildtool_export_depend>` and `<build_export_depend>`
entries in the `package.xml` to call `ament_export_dependencies` which ensures
that this package's dependents will load the correct transient dependencies.

For more information, see the [ament_cmake best practices article](best-practices-for-ament-cmake.md).

## Build and run the package

ROS 2 provides a build tool called `colcon` to build and test packages. The `colcon build` command
should always be invoked at the root of the workspace.

Build the package using the `--packages-select <package-name>` argument.

Once the package has been built, the workspace architecture typically looks as follows:

```plain
workspace
    ├── build # contains intermediate build artifacts
    ├── install # contains the installed software and scripts to enable it
    ├── log # contains console colcon command output
    └── src # contains the source files
```

Now, run the service node with `ros2 run`:

{{ console_snippet(
"grace/integration_tests/os_verify_docs/test/test_writing_a_minimal_service.py",
config={
"remove_indent": 4,
"add_shell_prefix": "(ade)$",
"tag": "#! RUN_CLIENT",
"stdout_tag": "#! CLIENT_OUTPUT"
}
)}}

{{ console_snippet(
"grace/integration_tests/os_verify_docs/test/test_writing_a_minimal_service.py",
config={
"remove_indent": 4,
"add_shell_prefix": "(ade)$",
"tag": "#! RUN_SERVER",
"stdout_tag": "#! SERVER_OUTPUT"
}
)}}

Press ++ctrl+c++ to stop the nodes.
