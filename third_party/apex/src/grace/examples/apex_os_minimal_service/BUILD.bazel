load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "apex_os_minimal_service",
    description = "Example of Apex.OS minimal client and service.",
    lib_executables = [
        "client",
        "client_multiple_requests",
        "server",
    ],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "<PERSON>",
    pkg_name = "apex_os_minimal_service",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "client",
    srcs = ["src/minimal_client.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/interrupt",
        "//grace/examples/example_interfaces",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "server",
    srcs = ["src/minimal_server.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/interrupt",
        "//grace/examples/example_interfaces",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "client_multiple_requests",
    srcs = ["src/minimal_client_multiple_requests.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/interrupt",
        "//grace/examples/example_interfaces",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

ament_pkg_resources(
    name = "apex_os_minimal_service_resources",
    package = "apex_os_minimal_service",
    resources = {
        ":client": "executable",
        ":client_multiple_requests": "executable",
        ":server": "executable",
    },
)

process_manager(
    name = "minimal_service_launch",
    data = [
        ":apex_os_minimal_service_resources",
    ],
    launch_file = "launch/apex_os_minimal_service.launch.yaml",
)

process_manager(
    name = "multiple_requests_launch",
    data = [
        ":apex_os_minimal_service_resources",
    ],
    launch_file = "launch/multiple_requests.launch.yaml",
)

process_manager(
    name = "multiple_clients_launch",
    data = [
        ":apex_os_minimal_service_resources",
    ],
    launch_file = "launch/multiple_clients.launch.yaml",
)

filegroup(
    name = "doc_files",
    srcs = [
        "CMakeLists.txt",
        "package.xml",
        "src/minimal_client.cpp",
        "src/minimal_client_multiple_requests.cpp",
        "src/minimal_server.cpp",
    ],
    visibility = ["//grace/examples/apex_os_minimal_service/doc:__subpackages__"],
)
