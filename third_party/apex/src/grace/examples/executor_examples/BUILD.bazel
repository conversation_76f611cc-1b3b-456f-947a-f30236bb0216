load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "executor_examples",
    description = "Package examples of usage of Apex executor and dynamic waitset facilities",
    lib_executables = [
        "waitset_and_two_subscriptions",
        "waitset_and_timer",
        "waitset_and_graph",
        "waitset_and_graph2",
        "reschedule_timer",
        "linked_sequential_execution",
        "linked_parallel_execution",
        "linked_exclusive_sequential_execution",
        "linked_execution_with_selector",
    ] + select({
        "//common/asil:qm": ["fusion_workload"],
        "//conditions:default": [],
    }),
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "executor_examples",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "waitset_and_two_subscriptions",
    srcs = ["src/waitset/waitset_and_two_subscriptions.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/execution/executor2",
        "//grace/interfaces/test_msgs",
    ],
)

cc_binary(
    name = "waitset_and_timer",
    srcs = ["src/waitset/waitset_and_timer.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/test_msgs",
    ],
)

cc_binary(
    name = "waitset_and_graph",
    srcs = ["src/waitset/waitset_and_graph.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/execution/executor2",
        "//grace/interfaces/test_msgs",
    ],
)

cc_binary(
    name = "waitset_and_graph2",
    srcs = ["src/waitset/waitset_and_graph2.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/execution/executor2",
        "//grace/interfaces/test_msgs",
    ],
)

cc_binary(
    name = "reschedule_timer",
    srcs = ["src/waitset/reschedule_timer.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/test_msgs",
    ],
)

cc_binary(
    name = "linked_sequential_execution",
    srcs = ["src/executor/linked_sequential_execution.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
    ],
)

cc_binary(
    name = "linked_parallel_execution",
    srcs = ["src/executor/linked_parallel_execution.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
    ],
)

cc_binary(
    name = "linked_exclusive_sequential_execution",
    srcs = ["src/executor/linked_exclusive_sequential_execution.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
    ],
)

cc_binary(
    name = "linked_execution_with_selector",
    srcs = ["src/executor/linked_execution_with_selector.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
    ],
)

cc_binary(
    name = "fusion_workload",
    srcs = [
        "include/executor_examples/fusion_workload_nodes.hpp",
        "src/executor/fusion_workload.cpp",
    ],
    includes = [
        "include",
    ],
    tags = [
        "exclude_sca",
        "integrity QM",
    ],
    deps = [
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/interfaces/sensor_msgs",
    ],
)

apex_cc_test(
    name = "test_fusion_workload",
    srcs = [
        "include/executor_examples/fusion_workload_nodes.hpp",
        "test/test_fusion_workload.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    includes = [
        "include",
    ],
    tags = [
        "exclusive",
        "integrity QM",
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        "//common/interrupt",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/sensor_msgs",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "include/executor_examples/fusion_workload_nodes.hpp",
        "src/executor/fusion_workload.cpp",
    ],
    visibility = ["//grace/examples/executor_examples:__subpackages__"],
)
