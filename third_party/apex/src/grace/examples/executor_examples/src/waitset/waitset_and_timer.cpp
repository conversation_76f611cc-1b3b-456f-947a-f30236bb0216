#include <atomic>
#include <chrono>

#include <executor2/apex_node_base.hpp>
#include <rclcpp/dynamic_waitset/waitset.hpp>
#include <rclcpp/rclcpp.hpp>
#include <threading/thread.hpp>
#include <timer_service/clock_timer_service.hpp>

#include <test_msgs/msg/message_with_id.hpp>

namespace dw = rclcpp::dynamic_waitset;
using apex::executor::apex_node_base;
using apex::executor::subscription_list;
using apex::executor::subscription_type;
using test_msgs::msg::MessageWithId;
using namespace std::chrono_literals;

//! [Node]
class my_node : public apex_node_base  // (1)!
{
public:
  explicit my_node(std::int32_t id)
  : apex_node_base{"receiver" + std::to_string(id)}, m_id{id}  // (2)!
  {
  }

private:
  bool execute_impl() override  // (3)!
  {
    std::cout << "I am node " << m_id;
    if (!m_sub->take().empty()) {
      std::cout << " ... and I have some data";
    }
    std::cout << std::endl;
    return true;
  }

  subscription_list get_non_triggering_subscriptions_impl() const override
  {
    return {m_sub};  // (4)!
  }

  std::int32_t m_id;

  rclcpp::PollingSubscription<MessageWithId>::SharedPtr m_sub{
    get_rclcpp_node().create_polling_subscription<MessageWithId>(
      "my_topic" + std::to_string(m_id), rclcpp::QoS(rclcpp::KeepLast(10)))};
};
//! [Node]

// This is NOT an example of how to write a node, just an ad-hoc testing device
class sender_node
{
public:
  sender_node()
  : m_thread([this] {
      auto i = 0U;
      while (!m_stop) {
        MessageWithId msg;

        if (i % 2U == 0U) {
          m_pub1->publish(msg);
        } else {
          m_pub2->publish(msg);
        }
        std::this_thread::sleep_for(100ms);
        ++i;
      }
    })
  {
    m_thread.issue();
  }

  void stop()
  {
    m_stop = true;
  }

private:
  std::atomic_bool m_stop{false};

  rclcpp::Node m_node{"sender"};

  rclcpp::Publisher<MessageWithId>::SharedPtr m_pub1{
    m_node.create_publisher<MessageWithId>("my_topic1", rclcpp::QoS(rclcpp::KeepLast(10)))};

  rclcpp::Publisher<MessageWithId>::SharedPtr m_pub2{
    m_node.create_publisher<MessageWithId>("my_topic2", rclcpp::QoS(rclcpp::KeepLast(10)))};

  apex::threading::thread m_thread;
};

//! [example]
void example()
{
  my_node node1{1};  // (1)!
  my_node node2{2};

  apex::timer_service::steady_clock_timer_service timer_srv;  // (2)!

  // (4)!
  sender_node sender;  // (5)!

  auto timer = timer_srv.create_timer(0ms, 100ms);  // (6)!

  dw::Waitset ws;

  ws.add(timer->to_sub_ptr(), [&] {
    timer->test_and_reset();
    node1.execute();
    node2.execute();
  });  // (7)!

  auto start = std::chrono::steady_clock::now();
  while (std::chrono::steady_clock::now() - start < 3s) {  // (8)!
    if (!ws.wait_and_dispatch_all(1s)) {  // (9)!
      std::cerr << "Timeout while waiting for a timer!\n";
    }
  }

  sender.stop();  // (10)!

  std::cout << "All done\n";
}
//! [example]

//! [main]
int main()
{
  rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);  // (1)!
  example();
  rclcpp::shutdown();  // (2)!
  return 0;
}
//! [main]
