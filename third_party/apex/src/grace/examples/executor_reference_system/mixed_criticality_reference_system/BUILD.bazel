load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_binary")
load("@apex//common/bazel/rules_pkg_extra:defs.bzl", "self_extracting_archive")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

PROCESSES = [
    "highly_critical",
    "somewhat_critical",
    "not_critical",
]

ros_pkg(
    name = "mixed_criticality_reference_system_pkg",
    cc_libraries = [":lib"],
    description = "mixed_criticality reference system",
    lib_executables = [process_name for process_name in PROCESSES],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "mixed_criticality_reference_system",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

cc_library(
    name = "lib",
    hdrs = glob(["include/**/*.hpp"]),
    strip_include_prefix = "include",
    deps = [
        "//grace/examples/executor_reference_system/reference_system2:reference_system",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

[
    cc_binary(
        name = process_name,
        srcs = [
            "src/{name}.cpp".format(name = process_name),
        ],
        visibility = ["//visibility:public"],
        deps = [
            ":lib",
            "//common/interrupt",
            "//grace/examples/executor_reference_system/reference_interfaces",
            "//grace/examples/executor_reference_system/reference_system2:reference_system",
            "//grace/execution/apex_init",
            "//grace/execution/executor2",
            "//grace/monitoring/execution_monitor",
        ],
    )
    for process_name in PROCESSES
]

cc_binary(
    name = "system_manager",
    srcs = ["src/system_manager.cpp"],
    deps = [
        ":lib",
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/process_manager_interfaces",
        "//grace/interfaces/process_manager_interfaces:process_manager_common",
        "//grace/monitoring/dispatcher:dispatcher_item",
        "//grace/monitoring/event",
        "//grace/monitoring/event_registry",
        "//grace/monitoring/execution_monitor_service:execution_monitor_service_lib",
        "//grace/monitoring/logging",
        "//grace/monitoring/system_manager:system_manager_lib",
    ],
)

configured_binary(
    name = "mixed_criticality_launch",
    args = ["--apex-settings-file $(rootpath config/process_manager.yaml)"],
    data = [
        "config/highly_critical.yaml",
        "config/not_critical.yaml",
        "config/process_manager.yaml",
        "config/somewhat_critical.yaml",
        "config/system_manager.yaml",
        ":highly_critical",
        ":not_critical",
        ":somewhat_critical",
        ":system_manager",
        "//grace/monitoring/execution_monitor_service:execution_monitor_service_exe",
    ],
    executable = "//grace/configuration/process_manager",
    use_runfiles_as_working_dir = True,
)

self_extracting_archive(
    name = "mixed_criticality_installer",
    executable = ":mixed_criticality_launch",
    # The "manual" tag is used to indicate that this target should not be built automatically
    # by default when running Bazel commands like `bazel build //...`. This is typically done
    # for targets that are intended to be built or tested explicitly by the user, such as
    # experimental, example, or reference system targets.
    tags = ["manual"],
    visibility = ["//visibility:public"],
)
