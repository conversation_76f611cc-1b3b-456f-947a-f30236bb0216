# Profiling executors using the mixed-criticality reference system

## Quick start on Linux

Build with:

```shell dollar
bazel build -c opt --copt=-O3 --config=ida --@apex//:tracing=false //grace/examples/executor_reference_system/mixed_criticality_reference_system/...
```

Run with:

```shell dollar title="Resource creator"
bazel run --config=ida -c opt --copt=-O3 --@apex//:tracing=false @apex//ida/resource_creator
```

```shell dollar title="System manager"
bazel run --config=ida -c opt --copt=-O3 --@apex//:tracing=false //grace/examples/executor_reference_system/mixed_criticality_reference_system:system_manager -- --apex-settings-file /home/<USER>/ade-home/gc/apex_ws/src/grace/examples/executor_reference_system/mixed_criticality_reference_system/config/system_manager.yaml
```

```shell dollar title="Process manager startup"
bazel run --config=ida -c opt --copt=-O3 --@apex//:tracing=false grace/examples/executor_reference_system/mixed_criticality_reference_system:mixed_criticality_launch
```

## Building for QNX on Nvidia HW

```shell dollar
bazel build --config=ida -c opt --copt=-O3 --@apex//:tracing=false --platforms=@apex//common/platforms:qcc71_nvidia-qnx-aarch64 //grace/examples/executor_reference_system/mixed_criticality_reference_system:mixed_criticality_installer
```

And then follow [the `self_extracting_archive` instructions here](/grace/doc/tutorials/environment_configuration_and_building/building_with_bazel/deploying-apex-os-applications-to-target-systems-bazel.md)
to copy the built artifact to the target.

```shell dollar
scp bazel-bin/grace/examples/executor_reference_system/mixed_criticality_reference_system/mixed_criticality_installer.sh root@pa-ci-drvagx-01:/opt/mixed_criticality_reference_system
```

### Running on QNX

```shell dollar
/opt/mixed_criticality_reference_system/mixed_criticality_launch/mixed_criticality_launch.runfiles/apex/ida/resource_creator/resource_creator
```

```shell dollar
/opt/mixed_criticality_reference_system/mixed_criticality_launch/mixed_criticality_launch.runfiles/apex/grace/examples/executor_reference_system/mixed_criticality_reference_system/system_manager --apex-settings-file /opt/mixed_criticality_reference_system/mixed_criticality_launch/mixed_criticality_launch.runfiles/apex/grace/examples/executor_reference_system/mixed_criticality_reference_system/config/system_manager.yaml
```

```shell dollar
/opt/mixed_criticality_reference_system/mixed_criticality_launch/mixed_criticality_launch
```

Optionally, generate a kernal trace for analysis later:

```shell dollar
tracelogger -b 2048 -k 1024 -M -S 10M -s 3 -f <name_of_the_file>
```

## Running everything manually

Below are all the commands ran within the process manager. Each line in the code block below
is meant to be ran in its own terminal.


=== "Linux workstation"

```shell dollar title="Run each command in separate terminals"
bazel run --config=ida -c opt --copt=-O3 --@apex//:tracing=false @apex//ida/resource_creator
bazel run --config=ida -c opt --copt=-O3 --@apex//:tracing=false //grace/examples/executor_reference_system/mixed_criticality_reference_system:system_manager -- --apex-settings-file /home/<USER>/ade-home/gc/apex_ws/src/grace/examples/executor_reference_system/mixed_criticality_reference_system/config/system_manager.yaml
bazel run --config=ida -c opt --copt=-O3 --@apex//:tracing=false //grace/examples/executor_reference_system/mixed_criticality_reference_system:highly_critical
bazel run --config=ida -c opt --copt=-O3 --@apex//:tracing=false //grace/examples/executor_reference_system/mixed_criticality_reference_system:somewhat_critical
bazel run --config=ida -c opt --copt=-O3 --@apex//:tracing=false //grace/examples/executor_reference_system/mixed_criticality_reference_system:not_critical
```

=== "QNX"

```shell dollar title="Run each command in separate terminals"
on -C 0 /opt/mixed_criticality_reference_system/mixed_criticality_launch/mixed_criticality_launch.runfiles/apex/ida/resource_creator/resource_creator
/opt/mixed_criticality_reference_system/mixed_criticality_launch/mixed_criticality_launch.runfiles/apex/grace/examples/executor_reference_system/mixed_criticality_reference_system/system_manager -- --apex-settings-file /opt/mixed_criticality_reference_system/mixed_criticality_launch/mixed_criticality_launch.runfiles/apex/grace/examples/executor_reference_system/mixed_criticality_reference_system/config/system_manager.yaml
/opt/mixed_criticality_reference_system/mixed_criticality_launch/mixed_criticality_launch.runfiles/apex/grace/examples/executor_reference_system/mixed_criticality_reference_system/highly_critical -- --apex-settings-file /opt/mixed_criticality_reference_system/mixed_criticality_launch/mixed_criticality_launch.runfiles/apex/grace/examples/executor_reference_system/mixed_criticality_reference_system/config/highly_critical.yaml
/opt/mixed_criticality_reference_system/mixed_criticality_launch/mixed_criticality_launch.runfiles/apex/grace/examples/executor_reference_system/mixed_criticality_reference_system/somewhat_critical -- --apex-settings-file /opt/mixed_criticality_reference_system/mixed_criticality_launch/mixed_criticality_launch.runfiles/apex/grace/examples/executor_reference_system/mixed_criticality_reference_system/config/somewhat_critical.yaml
/opt/mixed_criticality_reference_system/mixed_criticality_launch/mixed_criticality_launch.runfiles/apex/grace/examples/executor_reference_system/mixed_criticality_reference_system/not_critical -- --apex-settings-file /opt/mixed_criticality_reference_system/mixed_criticality_launch/mixed_criticality_launch.runfiles/apex/grace/examples/executor_reference_system/mixed_criticality_reference_system/config/not_critical.yaml
```
