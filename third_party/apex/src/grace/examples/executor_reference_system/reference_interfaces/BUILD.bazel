load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "ROSIDL_COMMON_PKGS")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "reference_interfaces_pkg",
    description = "Reference interfaces package",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    msg_libraries = [
        ":reference_interfaces",
    ],
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = ROSIDL_COMMON_PKGS,
)

msgs_library(
    name = "reference_interfaces",
    srcs = glob([
        "msg/*.idl",
    ]),
    visibility = ["//visibility:public"],
    wheel_data = ":reference_interfaces_pkg.wheel_data",
)
