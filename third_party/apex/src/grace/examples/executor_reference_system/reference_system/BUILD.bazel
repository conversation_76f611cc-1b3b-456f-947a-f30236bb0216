load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_pkg//:mappings.bzl", "pkg_files")
load("@rules_python//python:defs.bzl", "py_library")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "reference_system_pkg",
    cc_libraries = [
        ":reference_system",
    ],
    description = "Reference system to measure KPIs",
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "reference_system",
    py_libraries = [
        ":reference_system_py",
    ],
    share_data = [":share_data"],
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//tools/ament/ament_index/ament_index_python:ament_index_python_pkg",
        "@apex//tools/psrecord:psrecord_pkg",
        "@iceoryx//:iceoryx_pkg",
    ],
)

pkg_files(
    name = "share_data",
    srcs = glob(["cfg/**"]),
    prefix = "cfg",
)

cc_library(
    name = "reference_system",
    hdrs = glob(["include/**/*.hpp"]),
    includes = ["include/"],
    visibility = ["//visibility:public"],
)

py_library(
    name = "reference_system_py",
    srcs = glob(["reference_system_py/*.py"]),
    data = [
        ":reference_system_pkg.wheel_data",
        "@iceoryx//:roudi",
    ],
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        requirement("jinja2"),
        requirement("bokeh"),
        requirement("pandas"),
        requirement("numpy"),
        requirement("networkx"),
        "@apex//tools/ament/ament_index/ament_index_python",
    ],
)

apex_cc_test(
    name = "test_sample_management",
    srcs = [
        "test/test_sample_management.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":reference_system",
        "@apex//grace/examples/executor_reference_system/reference_interfaces",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_number_cruncher",
    srcs = [
        "test/test_number_cruncher.cpp",
    ],
    deps = [
        ":reference_system",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_reference_system_apex",
    srcs = [
        "test/test_fixtures.hpp",
        "test/test_reference_system_apex.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
    ],
    deps = [
        ":reference_system",
        "@apex//grace/examples/executor_reference_system/reference_interfaces",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_reference_system_rclcpp",
    srcs = [
        "test/test_fixtures.hpp",
        "test/test_reference_system_rclcpp.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
    ],
    deps = [
        ":reference_system",
        "@apex//grace/examples/executor_reference_system/reference_interfaces",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@apex//grace/tools/ros_domain_coordinator:gtest_main",
    ],
)
