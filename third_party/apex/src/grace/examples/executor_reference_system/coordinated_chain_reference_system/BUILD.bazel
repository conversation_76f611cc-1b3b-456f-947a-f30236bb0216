load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_pkg//:mappings.bzl", "pkg_files")
load("@rules_python//python:defs.bzl", "py_library")

# ""
package(default_visibility = ["//visibility:public"])

BENCHMARK_TARGETS = [
    ("coordinated_chain", "coordinated_chain.cpp", "COORDINATED_CHAIN_USE_SHADOW_NODES"),
    ("uncoordinated_chain", "coordinated_chain.cpp", "DUMMY_NONE"),
    ("uncoordinated_chain_explicit_chain", "coordinated_chain.cpp", "COORDINATED_CHAIN_USE_EXPLICIT_CHAIN"),
    ("uncoordinated_chain_multiexec", "coordinated_chain_multiexec.cpp", "DUMMY_NONE"),
    ("coordinated_chain_multiexe", "coordinated_chain_multiexec.cpp", "COORDINAED_CHAIN_USE_SHADOW_NODES"),
]

ros_pkg(
    name = "coordinated_chain_reference_system_pkg",
    description = "coordinated_chain reference system",
    lib_executables = [":{target}".format(target = target) for target, _, _ in BENCHMARK_TARGETS],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "coordinated_chain_reference_system",
    py_libraries = [":coordinated_chain_benchmark"],
    share_data = [":share_data"],
    version = "0.0.1",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Environment :: Console",
        "Intended Audience :: Developers",
        "License :: Apex.AI Proprietary License",
        "Programming Language :: Python",
    ],
    whl_entry_points = {
        "console_scripts": [
            "coordinated_chain_reference_system_benchmark = coordinated_chain_benchmark:main",
        ],
    },
    deps = [
        "@apex//grace/examples/executor_reference_system/reference_system:reference_system_pkg",
        "@apex//tools/ament/ament_index/ament_index_python:ament_index_python_pkg",
        "@iceoryx//:iceoryx_pkg",
    ],
)

pkg_files(
    name = "share_data",
    srcs = ["param/roudi.toml"],
    prefix = "param",
)

cc_library(
    name = "coordinated_chain_reference_system",
    hdrs = glob(["include/**/*.hpp"]),
    includes = ["include/"],
)

[
    cc_binary(
        name = "{target}".format(target = target),
        srcs = [
            "src/{source}".format(source = source),
        ],
        defines = [defines],
        visibility = ["//visibility:public"],
        deps = [
            ":coordinated_chain_reference_system",
            "@apex//common/allocator",
            "@apex//common/interrupt",
            "@apex//grace/examples/executor_reference_system/reference_interfaces",
            "@apex//grace/examples/executor_reference_system/reference_system",
            "@apex//grace/execution/apex_init",
            "@apex//grace/execution/executor2",
        ],
    )
    for target, source, defines in BENCHMARK_TARGETS
]

py_library(
    name = "coordinated_chain_benchmark",
    srcs = ["scripts/coordinated_chain_benchmark.py"],
    data = [":coordinated_chain_reference_system_pkg.wheel_data"],
    imports = ["scripts"],
    deps = [
        "@apex//grace/examples/executor_reference_system/reference_system:reference_system_py",
    ],
)
