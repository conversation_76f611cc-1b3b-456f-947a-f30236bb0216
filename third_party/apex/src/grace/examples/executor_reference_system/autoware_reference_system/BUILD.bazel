load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_pkg//:mappings.bzl", "pkg_files")
load("@rules_python//python:defs.bzl", "py_library")

# ""
package(default_visibility = ["//visibility:public"])

BENCHMARK_TARGETS = [
    "three_exes",
    "low_level_optimized",
    "executor_per_node",
    "executor_per_chain",
    "multithreaded",
    "singlethreaded",
]

ros_pkg(
    name = "autoware_reference_system_pkg",
    description = "Autoware reference system",
    lib_executables = [
        ":number_cruncher_benchmark",
    ] + [":{target}".format(target = target) for target in BENCHMARK_TARGETS],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "autoware_reference_system",
    py_libraries = [":autoware_benchmark"],
    share_data = [":share_data"],
    version = "0.0.1",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Environment :: Console",
        "Intended Audience :: Developers",
        "License :: Apex.AI Proprietary License",
        "Programming Language :: Python",
    ],
    whl_entry_points = {
        "console_scripts": [
            "autoware_reference_system_benchmark = autoware_benchmark:main",
        ],
    },
    deps = [
        "@apex//grace/examples/executor_reference_system/reference_system:reference_system_pkg",
    ],
)

pkg_files(
    name = "share_data",
    srcs = ["param/roudi.toml"],
    prefix = "param",
)

cc_binary(
    name = "number_cruncher_benchmark",
    srcs = ["src/number_cruncher_benchmark.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/examples/executor_reference_system/reference_system",
    ],
)

cc_library(
    name = "autoware_reference_system",
    hdrs = glob(["include/**/*.hpp"]),
    includes = ["include/"],
)

[
    cc_binary(
        name = "{target}".format(target = target),
        srcs = [
            "src/grace/{target}.cpp".format(target = target),
            "src/priorities.cpp",
        ],
        visibility = ["//visibility:public"],
        deps = [
            ":autoware_reference_system",
            "@apex//common/allocator",
            "@apex//common/interrupt",
            "@apex//grace/examples/executor_reference_system/reference_interfaces",
            "@apex//grace/examples/executor_reference_system/reference_system",
            "@apex//grace/execution/apex_init",
            "@apex//grace/execution/executor2",
        ],
    )
    for target in BENCHMARK_TARGETS
]

py_library(
    name = "autoware_benchmark",
    srcs = ["scripts/autoware_benchmark.py"],
    data = [":autoware_reference_system_pkg.wheel_data"],
    imports = ["scripts"],
    deps = [
        "@apex//grace/examples/executor_reference_system/reference_system:reference_system_py",
    ],
)
