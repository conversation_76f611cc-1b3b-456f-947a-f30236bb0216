load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")

# [msgs_library load]
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
# [msgs_library load]

# [py_msgs_library load]
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
# [py_msgs_library load]

load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")

# ""
package(default_visibility = ["//visibility:public"])

# [msgs_library target]
msgs_library(
    name = "foo_msgs",  # (1)!
    srcs = glob(["msg/*.idl"]),  # (2)!
    deps = ["@apex//grace/interfaces/std_msgs"],  # (3)!
)
# [msgs_library target]

# [msgs_library use cpp]
apex_cc_test(
    name = "test_foo_msgs",
    srcs = ["test/test_foo_msgs.cpp"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":foo_msgs",  # (1)!
        "@googletest//:gtest_main",
    ],
)
# [msgs_library use cpp]

# [msgs_library use py]
py_msgs_library(
    name = "foo_msgs_py",
    msgs = ":foo_msgs",  # (1)!
)

apex_py_test(
    name = "test_foo_msgs_py",
    srcs = ["test/test_foo_msgs_py.py"],
    deps = [
        ":foo_msgs_py",  # (2)!
    ],
)
# [msgs_library use py]

filegroup(
    name = "doc_files",
    srcs = glob(["**/*"]),
    visibility = ["//grace/doc:__subpackages__"],
)
