# ""
package(default_visibility = ["//visibility:public"])

cc_library(
    name = "asil_d_settings",
    srcs = ["param/example_settings.cpp"],
    hdrs = ["param/example_settings.hpp"],
    visibility = [":__subpackages__"],
    deps = ["//common/configuration/settings"],
)

cc_binary(
    name = "settings_example",
    srcs = [
        "src/main.cpp",
    ],
    includes = [
        "include",
    ],
    deps = [
        "//grace/execution/apex_init",
    ] + select({
        "//common/asil:d": [":asil_d_settings"],
        "//conditions:default": [],
    }),
)

filegroup(
    name = "referenced_srcs",
    srcs = [
        "param/settings.yaml",
    ],
    visibility = [":__subpackages__"],
)

filegroup(
    name = "doc_files",
    srcs = [
        "CMakeLists.txt",
        "src/main.cpp",
    ],
    visibility = ["//grace/tools/settings_converter/design:__subpackages__"],
)
