load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "ROSIDL_COMMON_PKGS")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "example_interfaces_pkg",
    description = "Contains message and service definitions used by the examples.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    msg_libraries = [":example_interfaces"],
    version = "0.9.2",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/interfaces/action_msgs:action_msgs_pkg",
        "//grace/interfaces/builtin_interfaces:builtin_interfaces_pkg",
    ] + ROSIDL_COMMON_PKGS,
)

idl_interfaces = glob([
    "msg/*.idl",
])

asil_d_rosmsg_interfaces = glob([
    "srv/*.srv",
])

rosmsg_interfaces = glob([
    "srv/*.srv",
    "action/*.action",
])

msgs_library(
    name = "example_interfaces",
    srcs = idl_interfaces,
    ament_runfiles = True,
    proto_structure = {
        "grace/examples/example_interfaces/example_interfaces/msg/Bool.idl": [
            "example_interfaces.msg.Bool",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Byte.idl": [
            "example_interfaces.msg.Byte",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/ByteMultiArray.idl": [
            "example_interfaces.msg.ByteMultiArray",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/MultiArrayDimension.idl": [
            "example_interfaces.msg.MultiArrayDimension",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/MultiArrayLayout.idl": [
            "example_interfaces.msg.MultiArrayLayout",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Char.idl": [
            "example_interfaces.msg.Char",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Empty.idl": [
            "example_interfaces.msg.Empty",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Float32.idl": [
            "example_interfaces.msg.Float32",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Float32MultiArray.idl": [
            "example_interfaces.msg.Float32MultiArray",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Float64.idl": [
            "example_interfaces.msg.Float64",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Float64MultiArray.idl": [
            "example_interfaces.msg.Float64MultiArray",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Int16.idl": [
            "example_interfaces.msg.Int16",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Int16MultiArray.idl": [
            "example_interfaces.msg.Int16MultiArray",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Int32.idl": [
            "example_interfaces.msg.Int32",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Int32MultiArray.idl": [
            "example_interfaces.msg.Int32MultiArray",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Int64.idl": [
            "example_interfaces.msg.Int64",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Int64MultiArray.idl": [
            "example_interfaces.msg.Int64MultiArray",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Int8.idl": [
            "example_interfaces.msg.Int8",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/Int8MultiArray.idl": [
            "example_interfaces.msg.Int8MultiArray",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/String.idl": [
            "example_interfaces.msg.String",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/UInt16.idl": [
            "example_interfaces.msg.UInt16",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/UInt16MultiArray.idl": [
            "example_interfaces.msg.UInt16MultiArray",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/UInt32.idl": [
            "example_interfaces.msg.UInt32",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/UInt32MultiArray.idl": [
            "example_interfaces.msg.UInt32MultiArray",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/UInt64.idl": [
            "example_interfaces.msg.UInt64",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/UInt64MultiArray.idl": [
            "example_interfaces.msg.UInt64MultiArray",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/UInt8.idl": [
            "example_interfaces.msg.UInt8",
        ],
        "grace/examples/example_interfaces/example_interfaces/msg/UInt8MultiArray.idl": [
            "example_interfaces.msg.UInt8MultiArray",
        ],
        "grace/examples/example_interfaces/srv/AddTwoInts.srv": [
            "example_interfaces.srv.AddTwoInts_Request",
            "example_interfaces.srv.AddTwoInts_Response",
            "example_interfaces.srv.AddTwoInts",
        ],
        "grace/examples/example_interfaces/srv/SetBool.srv": [
            "example_interfaces.srv.SetBool_Request",
            "example_interfaces.srv.SetBool_Response",
            "example_interfaces.srv.SetBool",
        ],
        "grace/examples/example_interfaces/srv/Trigger.srv": [
            "example_interfaces.srv.Trigger_Request",
            "example_interfaces.srv.Trigger_Response",
            "example_interfaces.srv.Trigger",
        ],
    },
    rosmsg_srcs = select({
        "//common/asil:d": asil_d_rosmsg_interfaces,
        "//common/asil:qm": rosmsg_interfaces,
    }),
    visibility = ["//visibility:public"],
    wheel_data = ":example_interfaces_pkg.wheel_data",
    deps = [
        "//grace/interfaces/action_msgs",
        "//grace/interfaces/builtin_interfaces",
    ],
)

filegroup(
    name = "doc_files",
    srcs = ["srv/AddTwoInts.srv"],
    visibility = ["//grace:__subpackages__"],
)
