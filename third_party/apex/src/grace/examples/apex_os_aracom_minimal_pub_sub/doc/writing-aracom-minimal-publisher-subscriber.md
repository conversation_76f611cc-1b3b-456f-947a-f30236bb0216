# Writing a minimal ara::com publisher and subscriber

## Introduction

This example is based on the [Writing a minimal publisher and subscriber](writing-a-minimal-publisher-subscriber.md)
example. It shows how to implement the `ara::com::EventSender` and
`ara::com::EventReceiver` based on the Apex.Grace publish-subscribe mechanism.
As in the linked article, there is one sender and one receiver. The information is serialized to a
string to easily send it via ROS. This could also be done by different means, e.g., DDS IDL.
The example shows only the event-based communication, not the service methods,
and targets the `Adaptive Platform 19.03` release.

### Useful documents

The following documents from `Adaptive Platform 19.11` were used to create the example:

- [AUTOSAR_SWS_CommunicationManagement](https://www.autosar.org/fileadmin/standards/R22-11/AP/AUTOSAR_SWS_CommunicationManagement.pdf)
- [AUTOSAR_EXP_ARAComAPI](https://www.autosar.org/fileadmin/standards/R22-11/AP/AUTOSAR_EXP_ARAComAPI.pdf)

### Terminology

| Name              | Description                                               |
| :---------------- | :-------------------------------------------------------- |
| EventSender       | A class to transmit data, comparable to a publisher       |
| EventReceiver     | A class to receive data, comparable to a subscriber       |
| Skeleton          | A class with a collection of EventSenders representing a service |
| Proxy             | A class with a collection of EventReceivers using the service |
| Runtime           | A class to initialize some Proxy/Skeleton essentials; used as a singleton |

## ROS publisher and subscriber

ROS nodes are used for the actual communication. Two ROS nodes are created for
the publisher and the subscriber as parts of `EventSender` and `EventReceiver`
correspondingly.

The subscriber passes the number of messages to keep as a QOS setting. This will be
used by the `EventSubscriber::Subscribe` method, which has a `maxSampleCount` parameter.

## ARA types

The ARA API specifies some types. Some of them must be defined in `types.hpp`. For simplicity
reasons, all types needed by this example are defined in the file shown below.

{{ code_snippet(
    "grace/examples/apex_os_aracom_minimal_pub_sub/include/ara/types.hpp",
    {"tag": "//! [ARA types]", "skip_prefix": "//!"},
    "cpp") }}

1. In this example, only the `ara::core::Result` is used; for simplicity reasons
   this is just an alias to the template parameter
2. There is also the `SampleAllocateePtr`, but this one is just used for a stub
   function for the zero-copy API

## ARA runtime

The ARA runtime class is used to encapsulate the initialization of Apex.Grace.

{{ code_snippet(
    "grace/examples/apex_os_aracom_minimal_pub_sub/include/ara/runtime.hpp",
    {"tag": "//! [ARA runtime]", "skip_prefix": "//!"},
    "cpp") }}

1. Since there is only one instance per application allowed, the constructor is
   private and a singleton is used to access the runtime
2. Initialization is done in the constructor, where also a signal handler is
   registered to stop an application with ++ctrl+c++

## ARA event sender

{{ code_snippet(
    "grace/examples/apex_os_aracom_minimal_pub_sub/include/ara/com/event_sender.hpp",
    {"tag": "//! [ARA com event sender]", "skip_prefix": "//!"},
    "cpp") }}

1. Some types required by ARA
1. Creates the class `EventSender` with a template parameter for the data type to publish
1. The constructor stores the arguments for later use in the `Offer` method
1. The `Send` method takes a reference to the data, serializes it, and sends it to the
   subscriber
1. There is also a zero-copy API; `Allocate` is used to get an initialized sample
   which can be mutated and sent with the overloaded `Send` method
1. Since the skeleton offers and stop offers all services at once, these methods
   are protected and will only be accessed by the skeleton class
1. The `Offer` method creates the ROS node and the publisher; the node is created on the
   heap
      1. It could also be created on the stack with an appropriate container, e.g., a
      stack-based implementation of a `std::optional`
1. The `StopOffer` method deletes the ROS objects to stop offering the service
1. These are the private members of the implementation

## ARA event receiver

{{ code_snippet(
    "grace/examples/apex_os_aracom_minimal_pub_sub/include/ara/com/event_receiver.hpp",
    {"tag": "//! [ARA com event receiver]", "skip_prefix": "//!"},
 "cpp") }}

1. Some types required by ARA
1. Create the class `EventReceiver` with a template parameter for the data type to receive
1. The constructor stores the arguments for later use in the `Subscribe` method
1. The `Subscribe` method creates the ROS node and a subscription, passing the
   `maxSampleCount` to be used as a QOS setting
      1. The node is created on the heap; it could also be created on the stack with
      an appropriate container, e.g., a stack-based implementation of a `std::optional`
1. The `Unsubscribe` method deletes the ROS objects to unsubscribe
1. According to the `ara::com` API, the samples are obtained with the `GetNewSamples` method
      1. The method takes a user-defined callable, which is called for each received sample
      2. The signature of the callable should be `void(ara::com::SamplePtr<SampleType const>)`, but
      since the `ara::com::SamplePtr` is not available, this was changed for the example to a
   `void(SampleType const &)`
      3. The `maxNumberOfSamples` parameter is supposed to control how many samples should be
      fetched from the underlying buffer, but is ignored in this example
      4. Further details are found in `******** Receive event` in
      `AUTOSAR_SWS_CommunicationManagement.pdf`
1. These are the private members of the implementation

## Message type

The message which is transmitted by the publisher and received by the subscriber is defined in the
file shown below. It contains the data that will be serialized into a string to easily send it via
ROS. For more complex examples, the serialization could also be directly done by ROS itself.

{{ code_snippet(
    "grace/examples/apex_os_aracom_minimal_pub_sub/include/aracom_minimal_pub_sub_types.hpp",
    {"tag": "//! [Message type]", "skip_prefix": "//!"},
 "cpp") }}

1. The struct defines a `count` integer and a `text` string
2. The constructor is used to create a new struct with the data
3. The serialization consists of a simple comma-separated string
4. The deserialization is also simple and splits the string at the first comma

## ARA publisher or "skeleton" in ARA terminology

{{ code_snippet(
    "grace/examples/apex_os_aracom_minimal_pub_sub/src/aracom_minimal_publisher.cpp",
    {"tag": "//! [Minimal publisher]", "skip_prefix": "//!"},
 "cpp") }}

1. Can be used to initialize proxy/skeleton essentials
2. The class to offer and transmit data
3. The data type to transmit
4. According to the `ara::com` API, a skeleton can be constructed either with a
   `InstanceIdentifier` or `InstanceSpecifier` and a `MethodCallProcessingMode`
    - Further details are found in _8.1.3.3 Service skeleton creation_ from
      `AUTOSAR_SWS_CommunicationManagement.pdf`
    - In _6.3 Skeleton Class_ from `AUTOSAR_EXP_ARAComAPI.pdf` there is a thorough example
      of a skeleton
    - These constructs are omitted for this simple example and the skeleton is hard-wired to a ROS
      topic
5. Inside the constructor, the event sender `m_minimalEvent` is initialized with
   node name `minimal_pub`, max cycle time `600 ms`, topic name `topic`, and node
   namespace `minimal_example`
6. The `OfferService` and `StopOfferService` methods are defined; they are
   used to offer or stop offering the events from a skeleton at once
7. Finally, there is an instance of an `EventSender` to publish the data
8. To be able to access the protected `Offer` and `StopOffer` methods of the
   `EventSender`, a `EventSenderAccessor` class is used; this class inherits from
   `ara::com::EventSender` and moves `Offer` and `StopOffer` into the public
   scope
9. In the `main` function, the runtime is initialized as a first step; afterwards, the
   skeleton is instantiated and the service is offered
10. The runtime phase is entered, and the data is sent every 500 milliseconds.

## ARA subscriber or "proxy" in ARA terminology

{{ code_snippet(
    "grace/examples/apex_os_aracom_minimal_pub_sub/src/aracom_minimal_subscriber.cpp",
    {"tag": "//! [Minimal subscriber]", "skip_prefix": "//!"},
 "cpp") }}

1. The first lines of the code includes the same header as the skeleton, except the
   `"ara/com/event_receiver.hpp"` is used instead of the `"ara/com/event_sender.hpp"`
2. According to the `ara::com` API, a proxy can be constructed with a `HandleType`, which is
   returned by a `FindService` call
    - In _6.2 Proxy Class_ from `AUTOSAR_EXP_ARAComAPI.pdf` there is a thorough example
      of a skeleton
    - For the simplicity of the example, the `HandleType` is omitted and the Proxy is
      hard-wired to a ROS topic
3. Inside the constructor, the event receiver `m_minimalEvent` is initialized with the
   node name `minimal_pub`, max cycle time `600`ms, topic name `topic`, and node
   namespace `minimal_example`
4. There is an instance of an `EventReceiver` to receive the data
5. In the `main` function, the runtime is initialized as the first step; afterwards, the
   proxy is instantiated and the event receiver subscribed with a max sample count
   of `4`
6. The runtime phase is now entered and the data is polled every second; this is
   done by the `EventReceiver::GetNewSamples` method, which executes the lambda for
   each new sample arrived since the last call

## Build and run

### Build

Build the package and source the setup files with the following commands:

```shell ade
cd ~/ApexOS-src/apex_ws
source /opt/ApexGraceBilbo/setup.bash
colcon build --merge-install --packages-select apex_os_aracom_minimal_pub_sub
source install/setup.bash
```

This generates the executables specified in the `CMakeLists.txt` in the `install` folder.

### Run applications separately

Now, run the publisher application:

<!-- LAUNCH_STEP: publisher -->
```shell ade
ros2 run apex_os_aracom_minimal_pub_sub publisher
```

<!-- EXPECTED_OUTPUT: publisher -->
<!-- FILTER ^.*(\[INFO\] |Hello World \d) -->
```shell
Sending: Hello World 0
Sending: Hello World 1
Sending: Hello World 2
```

Open another terminal and run the subscriber application:

<!-- LAUNCH_STEP: subscriber -->
```shell ade
ros2 run apex_os_aracom_minimal_pub_sub subscriber
```

<!-- EXPECTED_OUTPUT: subscriber -->
<!-- FILTER ^.*(\[INFO\] |Hello World \d) -->
```shell
Receiving: Hello World 5
Receiving: Hello World 6
Receiving: Hello World 7
```

Press ++ctrl+c++ in each terminal to stop the nodes.

<!-- DOCUMENTATION_TEST: writing-aracom-minimal-pub-sub.doctest.py -->
