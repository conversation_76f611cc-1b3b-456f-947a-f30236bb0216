load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

cc_library(
    name = "ara",
    hdrs = [
        "include/ara/com/event_receiver.hpp",
        "include/ara/com/event_sender.hpp",
        "include/ara/runtime.hpp",
        "include/ara/types.hpp",
        "include/aracom_minimal_pub_sub_types.hpp",
    ],
    strip_include_prefix = "include",
    tags = ["exclude_sca"],
)

cc_binary(
    name = "publisher",
    srcs = ["src/aracom_minimal_publisher.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":ara",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/interfaces/std_msgs",
    ],
)

cc_binary(
    name = "subscriber",
    srcs = ["src/aracom_minimal_subscriber.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":ara",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/interfaces/std_msgs",
    ],
)

# TODO(frederik.beaujean) Migrate test to os_verify_docs once that's bazelized (&356, #17999, #18165, #18249)
# apex_cc_test(
#     name = "test_aracom_minimal_pub_sub",
#     srcs = [
#         "test/writing-aracom-minimal-pub-sub.doctest.py",
#     ],
#     deps = [
#     ],
# )

ros_pkg(
    name = "apex_os_aracom_minimal_pub_sub",
    description = "Example of minimal aracom publisher and subscriber with Apex OS node",
    lib_executables = [
        "publisher",
        "subscriber",
    ],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "apex_os_aracom_minimal_pub_sub",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

filegroup(
    name = "doc_files",
    srcs = [
        "include/ara/com/event_receiver.hpp",
        "include/ara/com/event_sender.hpp",
        "include/ara/runtime.hpp",
        "include/ara/types.hpp",
        "include/aracom_minimal_pub_sub_types.hpp",
        "src/aracom_minimal_publisher.cpp",
        "src/aracom_minimal_subscriber.cpp",
    ],
    visibility = ["//grace/examples/apex_os_aracom_minimal_pub_sub/doc:__subpackages__"],
)
