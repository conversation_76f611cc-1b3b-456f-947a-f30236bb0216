load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "examples_rclcpp_multithreaded_executor_pkg",
    description = "Package containing example of how to implement a multithreaded executor",
    lib_executables = [":multithreaded_executor"],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclcpp_multithreaded_executor",
    version = "0.11.2",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
    ],
)

cc_binary(
    name = "multithreaded_executor",
    srcs = ["multithreaded_executor.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)
