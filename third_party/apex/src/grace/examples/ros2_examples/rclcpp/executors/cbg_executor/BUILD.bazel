load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "examples_rclcpp_cbg_executor_pkg",
    description = "Example for multiple Executor instances in one process, using the callback-group-level interface of the Executor class.",
    lib_executables = [
        ":ping",
        ":pong",
        ":ping_pong",
    ],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclcpp_cbg_executor",
    version = "0.11.2",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
    ],
)

cc_library(
    name = "headers",
    hdrs = glob(["include/**/*.hpp"]),
    strip_include_prefix = "include",
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "ping",
    srcs = [
        "src/examples_rclcpp_cbg_executor/ping_node.cpp",
        "src/examples_rclcpp_cbg_executor/utilities.hpp",
        "src/ping.cpp",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
    ],
)

cc_binary(
    name = "pong",
    srcs = [
        "src/examples_rclcpp_cbg_executor/pong_node.cpp",
        "src/examples_rclcpp_cbg_executor/utilities.hpp",
        "src/pong.cpp",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
    ],
)

cc_binary(
    name = "ping_pong",
    srcs = [
        "src/examples_rclcpp_cbg_executor/ping_node.cpp",
        "src/examples_rclcpp_cbg_executor/pong_node.cpp",
        "src/examples_rclcpp_cbg_executor/utilities.hpp",
        "src/ping_pong.cpp",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
    ],
)
