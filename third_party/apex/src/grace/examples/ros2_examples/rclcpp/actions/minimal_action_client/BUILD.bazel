load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "examples_rclcpp_minimal_action_client",
    description = "Minimal action client examples",
    lib_executables = [
        "action_client_member_functions",
        "action_client_not_composable",
        "action_client_not_composable_with_cancel",
        "action_client_not_composable_with_feedback",
    ],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclcpp_minimal_action_client",
    version = "0.11.2",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "action_client_member_functions",
    srcs = ["member_functions.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/examples/example_interfaces",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rclcpp/rclcpp_action",
    ],
)

cc_binary(
    name = "action_client_not_composable",
    srcs = ["not_composable.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/examples/example_interfaces",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rclcpp/rclcpp_action",
    ],
)

cc_binary(
    name = "action_client_not_composable_with_cancel",
    srcs = ["not_composable_with_cancel.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/examples/example_interfaces",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rclcpp/rclcpp_action",
    ],
)

cc_binary(
    name = "action_client_not_composable_with_feedback",
    srcs = ["not_composable_with_feedback.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/examples/example_interfaces",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rclcpp/rclcpp_action",
    ],
)
