load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "examples_rclcpp_minimal_action_server",
    description = "Minimal action server examples",
    lib_executables = [
        "action_server_not_composable",
        "action_server_member_functions",
    ],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclcpp_minimal_action_server",
    version = "0.11.2",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "action_server_not_composable",
    srcs = ["not_composable.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/examples/example_interfaces",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rclcpp/rclcpp_action",
    ],
)

cc_binary(
    name = "action_server_member_functions",
    srcs = ["member_functions.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/examples/example_interfaces",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rclcpp/rclcpp_action",
    ],
)
