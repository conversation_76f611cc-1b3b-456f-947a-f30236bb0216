load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

cc_binary(
    name = "minimal_service",
    srcs = [
        "main.cpp",
    ],
    deps = [
        "//grace/examples/example_interfaces",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

ros_pkg(
    name = "examples_rclcpp_minimal_service",
    description = "A minimal service server which adds two numbers",
    lib_executables = [
        "minimal_service",
    ],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclcpp_minimal_service",
    version = "0.11.2",
    visibility = ["//visibility:public"],
)
