load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

cc_binary(
    name = "client_main",
    srcs = ["main.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/examples/example_interfaces",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

ros_pkg(
    name = "examples_rclcpp_minimal_client",
    description = "Examples of minimal service clients",
    lib_executables = [
        "client_main",
    ],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclcpp_minimal_client",
    version = "0.11.2",
    visibility = ["//visibility:public"],
)
