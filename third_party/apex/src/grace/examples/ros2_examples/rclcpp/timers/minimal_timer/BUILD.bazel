load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

cc_binary(
    name = "timer_lambda",
    srcs = ["lambda.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "timer_member_function",
    srcs = ["member_function.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/ros/rclcpp/rclcpp",
    ],
)

ros_pkg(
    name = "examples_rclcpp_minimal_timer",
    description = "Examples of minimal nodes which have timers",
    lib_executables = [
        "timer_lambda",
        "timer_member_function",
    ],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclcpp_minimal_timer",
    version = "0.11.2",
    visibility = ["//visibility:public"],
)
