load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "examples_rclcpp_minimal_subscriber_pkg",
    description = "Examples of minimal subscribers",
    lib_executables = [
        ":subscriber_lambda",
        ":subscriber_member_function",
        ":subscriber_member_function_with_topic_statistics",
        ":subscriber_member_function_with_type_adapter",
        ":subscriber_member_function_with_unique_network_flow_endpoints",
        ":subscriber_not_composable",
    ],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclcpp_minimal_subscriber",
    version = "0.11.2",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
    ],
)

cc_binary(
    name = "subscriber_lambda",
    srcs = ["lambda.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "subscriber_member_function",
    srcs = ["member_function.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "subscriber_member_function_with_topic_statistics",
    srcs = ["member_function_with_topic_statistics.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "subscriber_member_function_with_type_adapter",
    srcs = ["member_function_with_type_adapter.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "subscriber_member_function_with_unique_network_flow_endpoints",
    srcs = ["member_function_with_unique_network_flow_endpoints.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "subscriber_not_composable",
    srcs = ["not_composable.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)
