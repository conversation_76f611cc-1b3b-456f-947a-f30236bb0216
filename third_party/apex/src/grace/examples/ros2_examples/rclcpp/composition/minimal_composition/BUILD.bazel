load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_rclcpp_component")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

apex_cc_library(
    name = "publisher",
    srcs = ["src/publisher_node.cpp"],
    hdrs = [
        "include/minimal_composition/publisher_node.hpp",
        "include/minimal_composition/visibility.h",
    ],
    copts = select({
        "@bazel_tools//tools/cpp:gcc": ["-Wno-suggest-override"],
        "//conditions:default": [],
    }),
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@apex//grace/ros/rclcpp/rclcpp_components:component_manager",
    ],
)

apex_cc_library(
    name = "subscriber",
    srcs = ["src/subscriber_node.cpp"],
    hdrs = [
        "include/minimal_composition/subscriber_node.hpp",
        "include/minimal_composition/visibility.h",
    ],
    copts = select({
        "@bazel_tools//tools/cpp:gcc": ["-Wno-suggest-override"],
        "//conditions:default": [],
    }),
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@apex//grace/ros/rclcpp/rclcpp_components:component_manager",
    ],
)

ament_rclcpp_component(
    name = "composition_nodes",
    package = "minimal_composition",
    plugins = {
        "PublisherNode": ":publisher",
        "SubscriberNode": ":subscriber",
    },
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "composition_composed",
    srcs = ["src/composed.cpp"],
    tags = ["exclude_sca"],
    deps = [
        ":publisher",
        ":subscriber",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "composition_publisher",
    srcs = ["src/standalone_publisher.cpp"],
    copts = select({
        "@bazel_tools//tools/cpp:gcc": ["-Wno-suggest-override"],
        "//conditions:default": [],
    }),
    tags = ["exclude_sca"],
    deps = [
        ":publisher",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "composition_subscriber",
    srcs = ["src/standalone_subscriber.cpp"],
    copts = select({
        "@bazel_tools//tools/cpp:gcc": ["-Wno-suggest-override"],
        "//conditions:default": [],
    }),
    tags = ["exclude_sca"],
    deps = [
        ":subscriber",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

ros_pkg(
    name = "examples_rclcpp_minimal_composition",
    cc_libraries = [
        ":publisher",
        ":subscriber",
    ],
    description = "Minimalist examples of composing nodes in the same process",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclcpp_minimal_composition",
    version = "0.11.2",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/interfaces/std_msgs:std_msgs_pkg",
        "@apex//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "@apex//grace/ros/rclcpp/rclcpp_components:rclcpp_components_pkg",
    ],
)
