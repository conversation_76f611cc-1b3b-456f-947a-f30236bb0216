load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_binary")

# ""
package(default_visibility = ["//visibility:public"])

ENTRY_POINTS = {
    "console_scripts": [
        "client = examples_rclpy_minimal_action_client.client:main",
        "client_cancel = examples_rclpy_minimal_action_client.client_cancel:main",
        "client_not_composable = examples_rclpy_minimal_action_client.client_not_composable:main",
        "client_asyncio = examples_rclpy_minimal_action_client.client_asyncio:main",
    ],
}

ros_pkg(
    name = "examples_rclpy_minimal_action_client_pkg",
    description = "Examples of minimal action clients using rclpy.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclpy_minimal_action_client",
    version = "0.11.2",
    visibility = ["//visibility:public"],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "//grace/examples/example_interfaces:example_interfaces_pkg",
        "//grace/interfaces/action_msgs:action_msgs_pkg",
        "//grace/ros/rclpy/rclpy:rclpy_pkg",
    ],
)

py_msgs_library(
    name = "action_msgs_py",
    msgs = "@apex//grace/interfaces/action_msgs",
)

py_msgs_library(
    name = "example_interfaces_py",
    msgs = "@apex//grace/examples/example_interfaces",
)

py_entry_points_library(
    name = "examples_rclpy_minimal_action_client",
    srcs = glob(["examples_rclpy_minimal_action_client/*.py"]),
    entry_points = ENTRY_POINTS,
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        ":action_msgs_py",
        ":example_interfaces_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

py_entry_points_binary(
    name = "client",
    py_entry_points_library = ":examples_rclpy_minimal_action_client",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "client_asyncio",
    py_entry_points_library = ":examples_rclpy_minimal_action_client",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "client_cancel",
    py_entry_points_library = ":examples_rclpy_minimal_action_client",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "client_not_composable",
    py_entry_points_library = ":examples_rclpy_minimal_action_client",
    visibility = ["//visibility:public"],
)
