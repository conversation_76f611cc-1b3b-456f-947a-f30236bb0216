load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_binary")

# ""
package(default_visibility = ["//visibility:public"])

ENTRY_POINTS = {
    "console_scripts": [
        "server = examples_rclpy_minimal_action_server.server:main",
        "server_defer = examples_rclpy_minimal_action_server.server_defer:main",
        "server_not_composable = examples_rclpy_minimal_action_server.server_not_composable:main",
        "server_queue_goals = examples_rclpy_minimal_action_server.server_queue_goals:main",
        "server_single_goal = examples_rclpy_minimal_action_server.server_single_goal:main",
    ],
}

ros_pkg(
    name = "examples_rclpy_minimal_action_server_pkg",
    description = "Examples of minimal action servers using rclpy.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclpy_minimal_action_server",
    version = "0.11.2",
    visibility = ["//visibility:public"],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "//grace/examples/example_interfaces:example_interfaces_pkg",
        "//grace/ros/rclpy/rclpy:rclpy_pkg",
    ],
)

py_msgs_library(
    name = "example_interfaces_py",
    msgs = "@apex//grace/examples/example_interfaces",
)

py_entry_points_library(
    name = "examples_rclpy_minimal_action_server",
    srcs = glob(["examples_rclpy_minimal_action_server/*.py"]),
    entry_points = ENTRY_POINTS,
    visibility = ["//visibility:public"],
    deps = [
        ":example_interfaces_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

py_entry_points_binary(
    name = "server",
    py_entry_points_library = ":examples_rclpy_minimal_action_server",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "server_defer",
    py_entry_points_library = ":examples_rclpy_minimal_action_server",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "server_not_composable",
    py_entry_points_library = ":examples_rclpy_minimal_action_server",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "server_queue_goals",
    py_entry_points_library = ":examples_rclpy_minimal_action_server",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "server_single_goal",
    py_entry_points_library = ":examples_rclpy_minimal_action_server",
    visibility = ["//visibility:public"],
)
