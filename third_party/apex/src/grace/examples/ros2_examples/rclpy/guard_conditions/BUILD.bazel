load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_binary")

# ""
package(default_visibility = ["//visibility:public"])

ENTRY_POINTS = {
    "console_scripts": [
        "trigger_guard_condition = examples_rclpy_guard_conditions.trigger_guard_condition:main",
    ],
}

ros_pkg(
    name = "examples_rclpy_guard_conditions_pkg",
    description = "Examples of using guard conditions.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclpy_guard_conditions",
    py_libraries = [":examples_rclpy_guard_conditions"],
    version = "0.11.2",
    visibility = ["//visibility:public"],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "//grace/ros/rclpy/rclpy:rclpy_pkg",
    ],
)

py_entry_points_library(
    name = "examples_rclpy_guard_conditions",
    srcs = [
        "examples_rclpy_guard_conditions/__init__.py",
        "examples_rclpy_guard_conditions/trigger_guard_condition.py",
    ],
    entry_points = ENTRY_POINTS,
    visibility = ["//visibility:public"],
    deps = [
        "//grace/ros/rclpy/rclpy",
    ],
)

py_entry_points_binary(
    name = "trigger_guard_condition",
    py_entry_points_library = ":examples_rclpy_guard_conditions",
    visibility = ["//visibility:public"],
)
