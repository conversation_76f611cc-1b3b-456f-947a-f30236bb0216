load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_binary")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

# bazelization_report ignore: test/test_copyright.py
# bazelization_report ignore: test/test_flake8.py
# bazelization_report ignore: test/test_pep257.py

ENTRY_POINTS = {
    "console_scripts": [
        "client = examples_rclpy_minimal_client.client:main",
        "client_async = examples_rclpy_minimal_client.client_async:main",
        "client_async_member_function = examples_rclpy_minimal_client.client_async_member_function:main",
        "client_async_callback = examples_rclpy_minimal_client.client_async_callback:main",
    ],
}

ros_pkg(
    name = "examples_rclpy_minimal_client_pkg",
    description = "Examples of minimal service clients using rclpy.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclpy_minimal_client",
    py_libraries = ["examples_rclpy_minimal_client"],
    version = "0.11.2",
    visibility = ["//visibility:public"],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "//grace/examples/example_interfaces:example_interfaces_pkg",
        "//grace/ros/rclpy/rclpy:rclpy_pkg",
    ],
)

py_msgs_library(
    name = "example_interfaces_py",
    msgs = "@apex//grace/examples/example_interfaces",
)

py_entry_points_library(
    name = "examples_rclpy_minimal_client",
    srcs = glob(["examples_rclpy_minimal_client/*.py"]),
    entry_points = ENTRY_POINTS,
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        ":example_interfaces_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

py_entry_points_binary(
    name = "client",
    py_entry_points_library = ":examples_rclpy_minimal_client",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "client_async",
    py_entry_points_library = ":examples_rclpy_minimal_client",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "client_async_callback",
    py_entry_points_library = ":examples_rclpy_minimal_client",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "client_async_member_function",
    py_entry_points_library = ":examples_rclpy_minimal_client",
    visibility = ["//visibility:public"],
)
