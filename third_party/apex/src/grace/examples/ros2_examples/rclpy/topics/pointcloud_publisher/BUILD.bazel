load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_binary")

# ""
package(default_visibility = ["//visibility:public"])

ENTRY_POINTS = {
    "console_scripts": [
        "pointcloud_publisher = examples_rclpy_pointcloud_publisher.pointcloud_publisher:main",
    ],
}

ros_pkg(
    name = "examples_rclpy_pointcloud_publisher_pkg",
    description = "Examples of a pointcloud publisher using rclpy.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI, Inc.",
    pkg_name = "examples_rclpy_pointcloud_publisher",
    py_libraries = [":examples_rclpy_pointcloud_publisher"],
    version = "0.11.2",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Intended Audience :: Developers",
        "License :: OSI Approved :: Apache Software License",
        "Programming Language :: Python",
        "Topic :: Software Development",
    ],
    whl_entry_points = ENTRY_POINTS,
)

py_msgs_library(
    name = "sensor_msgs_py",
    msgs = "@apex//grace/interfaces/sensor_msgs",
)

py_entry_points_library(
    name = "examples_rclpy_pointcloud_publisher",
    srcs = [
        "examples_rclpy_pointcloud_publisher/__init__.py",
        "examples_rclpy_pointcloud_publisher/pointcloud_publisher.py",
    ],
    entry_points = ENTRY_POINTS,
    visibility = ["//visibility:public"],
    deps = [
        ":sensor_msgs_py",
        "//grace/interfaces/sensor_msgs_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

py_entry_points_binary(
    name = "pointcloud_publisher",
    py_entry_points_library = ":examples_rclpy_pointcloud_publisher",
    visibility = ["//visibility:public"],
)
