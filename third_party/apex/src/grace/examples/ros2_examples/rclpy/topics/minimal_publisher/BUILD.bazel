load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_python//python:defs.bzl", "py_library")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "examples_rclpy_minimal_publisher_pkg",
    description = "Examples of minimal publishers using rclpy.",
    license = "Apache License, Version 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclpy_minimal_publisher",
    py_libraries = [
        ":examples_rclpy_minimal_publisher",
    ],
    version = "0.11.2",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Intended Audience :: Developers",
        "License :: OSI Approved :: Apache Software License",
        "Programming Language :: Python",
        "Topic :: Software Development",
    ],
    whl_entry_points = {
        "console_scripts": [
            "publisher_old_school = examples_rclpy_minimal_publisher.publisher_old_school:main",
            "publisher_local_function = examples_rclpy_minimal_publisher.publisher_local_function:main",
            "publisher_member_function = examples_rclpy_minimal_publisher.publisher_member_function:main",
        ],
    },
    deps = [
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/ros/rclpy/rclpy:rclpy_pkg",
    ],
)

py_msgs_library(
    name = "std_msgs_py",
    msgs = "@apex//grace/interfaces/std_msgs",
)

py_library(
    name = "examples_rclpy_minimal_publisher",
    srcs = [
        "examples_rclpy_minimal_publisher/__init__.py",
        "examples_rclpy_minimal_publisher/publisher_local_function.py",
        "examples_rclpy_minimal_publisher/publisher_member_function.py",
        "examples_rclpy_minimal_publisher/publisher_old_school.py",
    ],
    data = [":examples_rclpy_minimal_publisher_pkg.wheel_data"],
    imports = ["."],
    deps = [
        ":std_msgs_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

# bazelization_report ignore: test/test_copyright.py
# bazelization_report ignore: test/test_flake8.py
# bazelization_report ignore: test/test_pep257.py
