load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_python//python:defs.bzl", "py_library")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "examples_rclpy_minimal_subscriber_pkg",
    description = "Examples of minimal subscribers using rclpy.",
    license = "Apache License, Version 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclpy_minimal_subscriber",
    py_libraries = [
        ":subscriber_lambda",
        ":subscriber_member_function",
        ":subscriber_old_school",
    ],
    version = "0.11.2",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Intended Audience :: Developers",
        "License :: OSI Approved :: Apache Software License",
        "Programming Language :: Python",
        "Topic :: Software Development",
    ],
    whl_entry_points = {
        "console_scripts": [
            "subscriber_old_school = examples_rclpy_minimal_subscriber.subscriber_old_school:main",
            "subscriber_lambda = examples_rclpy_minimal_subscriber.subscriber_lambda:main",
            "subscriber_member_function = examples_rclpy_minimal_subscriber.subscriber_member_function:main",
        ],
    },
)

py_msgs_library(
    name = "std_msgs_py",
    msgs = "//grace/interfaces/std_msgs:std_msgs",
)

py_library(
    name = "subscriber_lambda",
    srcs = ["examples_rclpy_minimal_subscriber/subscriber_lambda.py"],
    data = [":examples_rclpy_minimal_subscriber_pkg.wheel_data"],
    imports = ["."],
    deps = [
        ":std_msgs_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

py_library(
    name = "subscriber_member_function",
    srcs = ["examples_rclpy_minimal_subscriber/subscriber_member_function.py"],
    data = [":examples_rclpy_minimal_subscriber_pkg.wheel_data"],
    imports = ["."],
    deps = [
        ":std_msgs_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

py_library(
    name = "subscriber_old_school",
    srcs = ["examples_rclpy_minimal_subscriber/subscriber_old_school.py"],
    data = [":examples_rclpy_minimal_subscriber_pkg.wheel_data"],
    imports = ["."],
    deps = [
        ":std_msgs_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

# bazelization_report ignore: test/test_copyright.py
# bazelization_report ignore: test/test_flake8.py
# bazelization_report ignore: test/test_pep257.py
