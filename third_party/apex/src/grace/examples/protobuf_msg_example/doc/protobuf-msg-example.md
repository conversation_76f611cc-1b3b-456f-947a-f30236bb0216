---
tags:
  - Ba<PERSON>
  - <PERSON><PERSON>
---
# protobuf_msg_example

The purpose of `protobuf_msg_example` is to demonstrate how to use the typetraits to get
the Protobuf message type given a ROS 2 message type.

## Usage

### Running

Run the binary with:

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/protobuf_msg_example:protobuf_msg_example_main
    ```

=== "Colcon/ros2cli"

    {{ source_admonition("ApexGraceBilbo", {"indent": 4}) }}

    ```shell ade
    ros2 run protobuf_msg_example protobuf_msg_example_main
    ```

### Building from source

Build the binary with:

=== "Bazel"

    ```shell dollar
    bazel build @apex//grace/examples/protobuf_msg_example:protobuf_msg_example_main
    ```

=== "Colcon/ros2cli"

    ```shell ade
    export ENABLE_PROTOBUF=ON  # (1)!
    source /opt/ApexIdaBilbo/setup.sh
    colcon build --packages-up-to protobuf_msg_example  # (2)!
    source ./install/setup.sh
    ros2 run protobuf_msg_example protobuf_msg_example_main
    ```

    1. Enable protobuf support by setting the `ENABLE_PROTOBUF` environment variable
    2. Build up to the `protobuf_msg_example` package

<!-- markdownlint-restore -->

## Explanation

1. In `CMakeLists.txt` and `BUILD.bazel` of this package, an executable is created using the `.cpp`
   file provided in the `src` folder. `std_msgs` is added as the dependency of the executable
   target.

    If the environment variable `ENABLE_PROTOBUF` is set to `ON`, all the protobuf
    typesupport and typetraits packages will be build. In addition, the message headers
    generated in `std_msgs` will also contain the typetraits implementation for that
    specific package. This is only needed for Colcon; protobuf support is enabled for Bazel builds.

1. In `protobuf_msg_example_main.cpp`, `<std_msgs/msg/string.hpp>` is included which contains
   the definition of typetraits.

    Given a ROS 2 message type of `ROS2_TYPE`, its corresponding Protobuf type can be deduced
    from `apex::apex_os::PROTOBUF_ROS_TypeTraits<ROS2_TYPE>::PROTOBUFType`. Two helper functions
    `apex::apex_os::PROTOBUF_ROS_TypeTraits<ROS2_TYPE>::convert_ros_message_to_protobuf` and
    `apex::apex_os::PROTOBUF_ROS_TypeTraits<ROS2_TYPE>::convert_protobuf_message_to_ros` can be used
    to convert the message from different types.

    Example of converting `std_msgs::msg::String` to `std_msgs::msg::Pb:String`:

{{ code_snippet(
    "grace/examples/protobuf_msg_example/src/protobuf_msg_example_main.cpp",
    {"tag": "//! [Use typetraits]"}) }}
