load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_protobuf_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "c_msgs_protobuf_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "protobuf_msg_example_pkg",
    description = "Example package to demonstrate serialization to protobuf msg.",
    lib_executables = [":protobuf_msg_example_main"],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "1.1.0",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/rosidl/protobuf_typesupport:protobuf_typesupport_pkg",
    ],
)

cpp_msgs_protobuf_library(
    name = "std_msgs_proto",
    msgs = "//grace/interfaces/std_msgs",
)

c_msgs_protobuf_library(
    name = "std_msgs_proto_c",
    msgs = "//grace/interfaces/std_msgs",
)

cc_binary(
    name = "protobuf_msg_example_main",
    srcs = ["src/protobuf_msg_example_main.cpp"],
    tags = ["exclude_sca"],
    deps = [
        ":std_msgs_proto",
        ":std_msgs_proto_c",
        "//grace/interfaces/std_msgs",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "src/protobuf_msg_example_main.cpp",
    ],
    visibility = ["//grace/examples/protobuf_msg_example/doc:__subpackages__"],
)
