# Using simulation time with rosbag2

This example shows how to use simulation time with rosbag2.

## Record

First, record the topic from the simple minimal publisher and subscriber nodes.

In terminal 1:
<!-- LAUNCH_STEP: publisher -->
```shell
ros2 run sim_time_example publisher
```

In terminal 2:

```shell
ros2 bag record /topic -o my_data
```

In terminal 3:
<!-- LAUNCH_STEP: subscriber -->
```shell
ros2 run sim_time_example subscriber
```

Press ++ctrl+c++ to stop nodes.

In the standard output, the timestamps taken from the system clock are observed:

```shell
Count: 0  Pub stamp: 1646041814214573424 ns  Sub stamp: 1646041814214769864 ns  Latency: 196440 ns
Count: 1  Pub stamp: 1646041814714562607 ns  Sub stamp: 1646041814714745707 ns  Latency: 183100 ns
Count: 2  Pub stamp: 1646041815214559849 ns  Sub stamp: 1646041815214677515 ns  Latency: 117666 ns
Count: 3  Pub stamp: 1646041815714555854 ns  Sub stamp: 1646041815714684811 ns  Latency: 128957 ns
```

## Replay with time simulation

The bag is replayed using the `--clock-topics` option.

In terminal 1:
<!-- LAUNCH_STEP: subscriber_replay -->
<!-- REPLACE opt\/ApexOS/.+ -->
```shell
ros2 run sim_time_example subscriber --ros-args --params-file /opt/ApexGraceBilbo/share/sim_time_example/param/ros_node_settings.yaml
```

In terminal 2:

```shell
ros2 bag play my_data --clock-topics /topic
```

Press ++ctrl+c++ to stop nodes.

In the standard output it is seen that the timestamps are using the simulated time, the time
at which the bag was recorded:

```shell
Count: 0  Pub stamp: 1646039036464139860 ns Sub stamp: 1646039036464467194 ns  Latency: 327334 ns
Count: 1  Pub stamp: 1646039036964119308 ns Sub stamp: 1646039036964308921 ns  Latency: 189613 ns
Count: 2  Pub stamp: 1646039037464103677 ns Sub stamp: 1646039037464318649 ns  Latency: 214972 ns
Count: 3  Pub stamp: 1646039037964089382 ns Sub stamp: 1646039037964256345 ns  Latency: 166963 ns
```

<!-- DOCUMENTATION_TEST: sim_time_rosbag_example.doctest.py -->
