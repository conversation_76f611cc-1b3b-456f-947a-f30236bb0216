# Using simulation time with ROS nodes

This example shows how to configure and activate the ROS time simulation with ROS 2 nodes using
different approaches.

`ros_node_example` shows different clock times:

- A node's internal ROS clock
- A node's internal ROS clock using a global time source
- A ROS clock attached to a time source
- A system clock
- A steady clock

{{ source_admonition("ApexGraceBilbo") }}

## Simulation time disabled

ROS clock defaults to the system clock when ROS time is not active.

Terminal 1:

<!-- LAUNCH_STEP: clock_publisher-->
```shell ade
ros2 run sim_time_example clock_publisher
```

Terminal 2:

<!-- LAUNCH_STEP: ros_node_clock_no_args-->
```shell ade
ros2 run sim_time_example ros_node_example
RMW Implementation: rmw_apex_middleware
Count: 1
- use_sim_time: false
- Node global    ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873065333316936
- Node builtin   ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873065333316761
- ROS clock      ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873065333317561
- System clock   ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873065333317617
- Steady clock   ros_time_is_active: 0, seconds: 25444.6, nanoseconds: 25444565584123
Count: 2
- use_sim_time: false
- Node global    ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873066333409937
- Node builtin   ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873066333409577
- ROS clock      ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873066333410504
- System clock   ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873066333410624
- Steady clock   ros_time_is_active: 0, seconds: 25445.6, nanoseconds: 25445565683244
```

## Enable simulation time programmatically on node construction

This case shows how to enable simulation time by setting the `use_sim_time` parameter when
the node is constructed (through the `--use_sim_time_in_node` CLI option). This enables
time simulation in both the node's internal time source and in the standalone time source
attached to the node. The messages published by `clock_publisher` on the `/clock` topic
therefore have an effect on those clocks.

Terminal 1:

<!-- LAUNCH_STEP: clock_publisher-->
```shell ade
ros2 run sim_time_example clock_publisher
```

Terminal 2:

<!-- LAUNCH_STEP: ros_node_clock_example-->
```shell ade
ros2 run sim_time_example ros_node_example --use_sim_time_in_node
Enable simulation time on node construction
RMW Implementation: rmw_apex_middleware
Count: 1
- use_sim_time: true
- Node global    ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873045415349030
- Node builtin   ros_time_is_active: 1, seconds: 0, nanoseconds: 0
- ROS clock      ros_time_is_active: 1, seconds: 0, nanoseconds: 0
- System clock   ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873045415349925
- Steady clock   ros_time_is_active: 0, seconds: 25424.6, nanoseconds: 25424647488987
Count: 2
- use_sim_time: true
- Node global    ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873046415354372
- Node builtin   ros_time_is_active: 1, seconds: 1.2345e-05, nanoseconds: 12345
- ROS clock      ros_time_is_active: 1, seconds: 1.2345e-05, nanoseconds: 12345
- System clock   ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873046415356734
- Steady clock   ros_time_is_active: 0, seconds: 25425.6, nanoseconds: 25425647501956
```

## Using a parameter file

This case shows how to enable simulation time by setting the `use_sim_time` parameter in a YAML file.

```shell ade
ros2 run sim_time_example ros_node_example --ros-args --params-file /opt/ApexGraceBilbo/share/sim_time_example/param/ros_node_settings.yaml
```

## Using a launch file

Time simulation can be enabled by setting the parameter `use_sim_time` to true from a launch file.

```shell ade
ros2 launch sim_time_example ros_node_example.launch.py
```

## Enable global time simulation

This case shows how to enable simulation time using the shared global time source.

Terminal 1:

<!-- LAUNCH_STEP: clock_publisher-->
```shell ade
ros2 run sim_time_example clock_publisher
```

Terminal 2:

<!-- LAUNCH_STEP: ros_clock_global -->
```shell ade
ros2 run sim_time_example ros_node_example --enable_global_sim_time
Enable time simulation in global time source
RMW Implementation: rmw_apex_middleware
Count: 1
- use_sim_time: false
- Node global    ros_time_is_active: 1, seconds: 0, nanoseconds: 0
- Node builtin   ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647872999877660069
- ROS clock      ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647872999877667318
- System clock   ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647872999877667375
- Steady clock   ros_time_is_active: 0, seconds: 25379.1, nanoseconds: 25379109514112
Count: 2
- use_sim_time: false
- Node global    ros_time_is_active: 1, seconds: 4.00001, nanoseconds: 4000012345
- Node builtin   ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873000877756594
- ROS clock      ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873000877774361
- System clock   ros_time_is_active: 0, seconds: 1.64787e+09, nanoseconds: 1647873000877774405
- Steady clock   ros_time_is_active: 0, seconds: 25380.1, nanoseconds: 25380109627272
```

<!-- DOCUMENTATION_TEST: sim_time_ros_node_example.doctest.py -->
