# Using simulation time with Apex.<PERSON>des

This example shows how to configure and activate the ROS simulation time with Apex.Grace nodes.

The examples uses the following nodes

1. `apex_node_example` is an Apex.Grace node that shows the time from different time sources used by
   the node
    - Using the default internal node time source
    - Using the global time source
2. `clock_publisher` is a node that publishes simulation time to the `/clock` topic

{{ source_admonition("ApexGraceBilbo") }}

For more information on simulation time in Apex.Grace, see the
[Using Apex.Grace time simulation](using-apex-os-time-simulation.md) article.

## Time simulation disabled

The ROS clock defaults to the system clock when ROS time is not active. Therefore the simulation time
published by `clock_publisher` on the `/clock` topic will not have any effect on the time seen by `apex_node_example`

Terminal 1:

<!-- LAUNCH_STEP: clock_publisher -->
```shell ade
ros2 run sim_time_example clock_publisher
```

Terminal 2:

<!-- LAUNCH_STEP: apex_node -->
```shell ade
(ade)$ ros2 run sim_time_example apex_node_example
Count: 1 ros_time_is_active: 0 seconds: 1647879861.314014 nanoseconds: 1647879861314014572
Count: 2 ros_time_is_active: 0 seconds: 1647879862.314196 nanoseconds: 1647879862314195987
```

## Enable simulation time using a parameter file

This case shows how to enable simulation time by setting the `use_sim_time` ROS parameter from
a YAML file. This enables time simulation in the `apex_node_example` node's internal time source.
The simulation time published by `clock_publisher` on the `/clock` topic will therefore be used
to determine the time seen by `apex_node_example`

Terminal 1:

<!-- LAUNCH_STEP: clock_publisher -->
```shell ade
ros2 run sim_time_example clock_publisher
```

Terminal 2:

<!-- LAUNCH_STEP: apex_node_sim_enabled -->
<!-- REPLACE opt\/ApexOS/.+ -->
```shell ade
(ade)$ ros2 run sim_time_example apex_node_example --ros-args --params-file /opt/ApexGraceBilbo/share/sim_time_example/param/apex_node_settings.yaml
Count: 1 ros_time_is_active: 1 seconds: 38.000012 nanoseconds: 38000012345
Count: 2 ros_time_is_active: 1 seconds: 39.000012 nanoseconds: 39000012345
```

## Enable global time simulation

This case shows how to enable simulation time using the shared global time source, which makes all
nodes within a process share the same time source. This enables time simulation in the
`apex_node_example` node's internal time source. The simulation time published by `clock_publisher`
on the `/clock` topic will therefore be used to determine the time seen by `apex_node_example`

Terminal 1:

<!-- LAUNCH_STEP: clock_publisher -->
```shell ade
ros2 run sim_time_example clock_publisher
```

Terminal 2:

<!-- LAUNCH_STEP: apex_node_global_sim_enabled -->
<!-- REPLACE opt\/ApexOS/.+ -->
```shell ade
(ade)$ ros2 run sim_time_example apex_node_example --enable_global_sim_time --apex-settings-file /opt/ApexGraceBilbo/share/sim_time_example/param/time_settings.yaml
Count: 1 ros_time_is_active: 1 seconds: 90.000012 nanoseconds: 90000012345
Count: 2 ros_time_is_active: 1 seconds: 91.000012 nanoseconds: 91000012345
```

<!-- DOCUMENTATION_TEST: sim_time_apex_node_example.doctest.py -->
