load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

cc_binary(
    name = "ros_node_example",
    srcs = ["src/ros_node_example.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "clock_publisher",
    srcs = ["src/clock_publisher.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/interfaces/rosgraph_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "apex_node_example",
    srcs = ["src/apex_node_example.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/monitoring/logging",
    ],
)

cc_binary(
    name = "publisher",
    srcs = ["src/publisher.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/interfaces/sensor_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "subscriber",
    srcs = ["src/subscriber.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/interfaces/sensor_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

# TODO(carlos) Migrate test to os_verify_docs once that's bazelized (&356, #17999, #18249, #21244)
# test/sim_time_rosbag_example.doctest.py
ros_pkg(
    name = "sim_time_example",
    description = "Examples of ROS simulation time using ROS 2",
    lib_executables = [
        "ros_node_example",
        "clock_publisher",
        "apex_node_example",
        "publisher",
        "subscriber",
    ],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Carlos San Vicente",
    pkg_name = "sim_time_example",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

filegroup(
    name = "doc_files",
    srcs = glob([
        "param/*",
        "src/*",
    ]),
    visibility = ["//grace/examples/sim_time_example:__subpackages__"],
)
