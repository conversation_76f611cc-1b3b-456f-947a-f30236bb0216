load("@apex//grace/examples/latency_measurements:defs.bzl", "MSG_SIZES")
load("@apex//grace/examples/latency_measurements:defs.bzl", "MSG_TYPES")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

[
    cc_binary(
        name = "inter_process_sub_{}".format(msg_type),
        srcs = [
            "src/iox_subscriber.cpp",
            "src/topic_data.hpp",
        ],
        defines = [
            "MSG_SIZE={}".format(size),
        ],
        tags = ["exclude_sca"],
        deps = [
            "@iceoryx//:iceoryx_binding_c",
        ],
    )
    for size, msg_type in zip(MSG_SIZES, MSG_TYPES)
]

[
    cc_binary(
        name = "inter_process_pub_{}".format(msg_type),
        srcs = [
            "src/iox_publisher.cpp",
            "src/topic_data.hpp",
        ],
        defines = [
            "MSG_SIZE={}".format(size),
        ],
        tags = ["exclude_sca"],
        deps = [
            "@iceoryx//:iceoryx_binding_c",
        ],
    )
    for size, msg_type in zip(MSG_SIZES, MSG_TYPES)
]

[
    cc_binary(
        name = "chain_{}".format(msg_type),
        srcs = [
            "src/chain.cpp",
            "src/topic_data.hpp",
        ],
        defines = [
            "MSG_SIZE={}".format(size),
        ],
        tags = ["exclude_sca"],
        deps = [
            "@iceoryx//:iceoryx_binding_c",
        ],
    )
    for size, msg_type in zip(MSG_SIZES, MSG_TYPES)
]

[
    cc_binary(
        name = "inter_thread_{}".format(msg_type),
        srcs = [
            "src/inter_thread.cpp",
            "src/topic_data.hpp",
        ],
        defines = [
            "MSG_SIZE={}".format(size),
        ],
        tags = ["exclude_sca"],
        deps = [
            "@iceoryx//:iceoryx_binding_c",
        ],
    )
    for size, msg_type in zip(MSG_SIZES, MSG_TYPES)
]

ros_pkg(
    name = "latency_measurements_iox",
    description = "Code to compare latency in different setups when using standalone iceoryx",
    lib_executables = [
        "{}_{}".format(exe, msg_type)
        for exe in [
            "inter_process_sub",
            "inter_process_pub",
            "chain",
            "inter_thread",
        ]
        for msg_type in MSG_TYPES
    ],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Sumanth Nirmal",
    pkg_name = "latency_measurements_iox",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)
