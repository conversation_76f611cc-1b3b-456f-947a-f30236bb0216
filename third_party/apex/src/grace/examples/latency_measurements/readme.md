# Latency measurements

## Description

The package defines several setups to measure message latencies.

The setups consists of a publisher and a subscriber with 3 different type of messages. The pub/sub
app is written for ROS 2, Apex.Grace (with the Apex.Ida), and iceoryx.

The following scenarios can be tested:

- Inter-process communication
- Inter-thread communication
- Intra-thread communication (with waitset)
- Chain communication (without waitset).

## Building instructions

### Apex.Grace

```shell ade
source /opt/ApexGraceBilbo/setup.bash
export COLCON_HOME='/usr/local/share/colcon/colcon-home/development'
colcon build --packages-select debug_msgs latency_measurements_apex
```

### ROS 2

```shell ade
# Use ROS 2 ADE https://gitlab.com/ApexAI/ros2-ade
source /opt/ros
colcon build --packages-select debug_msgs latency_measurements_apex
```

### iceoryx

<!-- markdownlint-disable MD046 -->

=== Linux

    ```shell ade
    export CMAKE_PREFIX_PATH=$CMAKE_PREFIX_PATH:/opt/iceoryx/x86_64/lib/cmake
    cd ~/gc/apex_ws/src/grace/examples/latency_measurements/latency_measurements_iox/ && mkdir build
    cd build
    cmake .. && make
    ```

=== QNX

    ```shell ade
    cd latency_measurements/latency_measurements_iox/  && mkdir build && cd build
    export TOOLCHAIN=<path_to_qnx_toolchain>
    export CMAKE_PREFIX_PATH=/opt/iceoryx/<qnx_version>/lib/cmake
    cmake .. -DCMAKE_TOOLCHAIN_FILE=$TOOLCHAIN
    make
    ```

<!-- markdownlint-enable MD046 -->

## Running instructions

```shell
# Without zero-copy
./inter_process_sub_1b > inter_process_1b_cyclone.csv
./inter_process_pub_1b

./inter_thread_1b > inter_thread_1b_cyclone.csv

./chain_1b > chain_1b_cyclone.csv

# With zero-copy
/opt/ApexIdaBilbo/bin/iox-roudi -c ~/gc/apex_ws/src/grace/examples/latency_measurements/param/roudi_config.yaml
./inter_process_sub_4mb --apex-settings-file ~/gc/apex_ws/src/grace/examples/latency_measurements/param/shm_config.param.yaml
./inter_process_pub_4mb --apex-settings-file ~/gc/apex_ws/src/grace/examples/latency_measurements/param/shm_config.param.yaml
```

## Associated paper and measurements

Paper and measurements are found in the published-papers repository.

### Measurements processing

There are some basic scripts included in the package to process csv files.
