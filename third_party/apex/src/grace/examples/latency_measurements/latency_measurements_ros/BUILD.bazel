load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

cc_binary(
    name = "inter_process_sub",
    srcs = [
        "src/subscriber.hpp",
        "src/subscriber_main.cpp",
    ],
    tags = ["exclude_sca"],
    deps = [
        "//grace/examples/latency_measurements/debug_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "inter_process_pub",
    srcs = [
        "src/publisher.hpp",
        "src/publisher_main.cpp",
    ],
    tags = ["exclude_sca"],
    deps = [
        "//grace/examples/latency_measurements/debug_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "intra_thread",
    srcs = [
        "src/pub_sub_main.cpp",
        "src/publisher.hpp",
        "src/subscriber.hpp",
    ],
    tags = ["exclude_sca"],
    deps = [
        "//grace/examples/latency_measurements/debug_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

ros_pkg(
    name = "latency_measurements_ros",
    description = "Code to compare different setups",
    lib_executables = [
        "inter_process_sub",
        "inter_process_pub",
        "intra_thread",
    ],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Alban Tamisier",
    pkg_name = "latency_measurements_ros",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)
