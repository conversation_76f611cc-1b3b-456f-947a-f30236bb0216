load("@apex//grace/examples/latency_measurements:defs.bzl", "MSG_SIZES")
load("@apex//grace/examples/latency_measurements:defs.bzl", "MSG_TYPES")
load("@apex//grace/examples/latency_measurements:defs.bzl", "PUB_FREQUENCY")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

[
    cc_binary(
        name = "inter_process_sub_{}".format(msg_type),
        srcs = [
            "src/subscriber.hpp",
            "src/subscriber_main.cpp",
        ],
        defines = [
            "MSG_SIZE={}".format(size),
            "PUB_FREQUENCY={}".format(PUB_FREQUENCY),
        ],
        tags = ["exclude_sca"],
        deps = [
            "//grace/examples/latency_measurements/debug_msgs",
            "//grace/execution/apex_init",
            "//grace/execution/executor2",
            "//grace/execution/timer_service",
            "//grace/ros/rclcpp/rclcpp",
        ],
    )
    for size, msg_type in zip(MSG_SIZES, MSG_TYPES)
]

[
    cc_binary(
        name = "inter_process_pub_{}".format(msg_type),
        srcs = [
            "src/publisher.hpp",
            "src/publisher_main.cpp",
        ],
        defines = [
            "MSG_SIZE={}".format(size),
            "PUB_FREQUENCY={}".format(PUB_FREQUENCY),
        ],
        tags = ["exclude_sca"],
        deps = [
            "//grace/examples/latency_measurements/debug_msgs",
            "//grace/execution/apex_init",
            "//grace/execution/executor2",
            "//grace/execution/timer_service",
            "//grace/ros/rclcpp/rclcpp",
        ],
    )
    for size, msg_type in zip(MSG_SIZES, MSG_TYPES)
]

[
    cc_binary(
        name = "chain_{}".format(msg_type),
        srcs = [
            "src/pub_sub_chain_main.cpp",
            "src/publisher.hpp",
            "src/subscriber.hpp",
        ],
        defines = [
            "MSG_SIZE={}".format(size),
            "PUB_FREQUENCY={}".format(PUB_FREQUENCY),
        ],
        tags = ["exclude_sca"],
        deps = [
            "//grace/examples/latency_measurements/debug_msgs",
            "//grace/execution/apex_init",
            "//grace/execution/executor2",
            "//grace/execution/timer_service",
            "//grace/ros/rclcpp/rclcpp",
        ],
    )
    for size, msg_type in zip(MSG_SIZES, MSG_TYPES)
]

[
    cc_binary(
        name = "inter_thread_{}".format(msg_type),
        srcs = [
            "src/pub_sub_inter_main.cpp",
            "src/publisher.hpp",
            "src/subscriber.hpp",
        ],
        defines = [
            "MSG_SIZE={}".format(size),
            "PUB_FREQUENCY={}".format(PUB_FREQUENCY),
        ],
        tags = ["exclude_sca"],
        deps = [
            "//common/interrupt",
            "//grace/examples/latency_measurements/debug_msgs",
            "//grace/execution/apex_init",
            "//grace/execution/executor2",
            "//grace/execution/timer_service",
            "//grace/ros/rclcpp/rclcpp",
        ],
    )
    for size, msg_type in zip(MSG_SIZES, MSG_TYPES)
]

ros_pkg(
    name = "latency_measurements_apex",
    description = "Code to compare different setups",
    lib_executables = [
        "{}_{}".format(exe, msg_type)
        for exe in [
            "inter_process_sub",
            "inter_process_pub",
            "chain",
            "inter_thread",
        ]
        for msg_type in MSG_TYPES
    ],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Alban Tamisier",
    pkg_name = "latency_measurements_apex",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)
