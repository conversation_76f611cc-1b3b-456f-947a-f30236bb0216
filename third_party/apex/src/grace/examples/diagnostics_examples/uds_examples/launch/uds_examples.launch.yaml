# Copyright 2024 Apex.AI, Inc.
# All rights reserved.
process-manager:
  process-groups:
    - name: "uds_examples"
      init-state: "ON"
      group-log-handlers: ["Console"]
      group-log-level: "INFO"
      processes:
        - name: "doip_transport"
          stdout-handlers: ["Console"]
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "$(find-pkg-prefix doip_transport)/lib/doip_transport/doip_transport_node"
            args:
              - "--apex-settings-file"
              - "$(find-pkg-share uds_examples)/settings/uds_doip.yaml"
        - name: "uds_server"
          stdout-handlers: ["Console"]
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "$(find-pkg-prefix uds_server)/lib/uds_server/uds_server_node"
            args:
              - "--apex-settings-file"
              - "$(find-pkg-share uds_examples)/settings/uds_doip.yaml"
        - name: "uds_example"
          stdout-handlers: ["Console"]
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "$(find-pkg-prefix uds_examples)/lib/uds_examples/uds_example"
            args:
              - "--apex-settings-file"
              - "$(find-pkg-share uds_examples)/settings/app.yaml"
        - name: "docan_transport"
          stdout-handlers: ["Console"]
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "$(find-pkg-prefix docan_transport)/lib/docan_transport/docan_node"
            args:
              - "--apex-settings-file"
              - "$(find-pkg-share docan_transport)/config/docan_config.yaml"
        - name: "uds_gateway"
          stdout-handlers: ["Console"]
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "$(find-pkg-prefix uds_gateway)/lib/uds_gateway/uds_gateway"
            args:
              - "--apex-settings-file"
              - "$(find-pkg-share uds_gateway)/example_config.yaml"
        - name: "stub_ecu"
          stdout-handlers: ["Console"]
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "$(find-pkg-prefix stub_ecu_pkg)/lib/stub_ecu_pkg/stub_ecu"

      states:
        - name: "ON"
          processes:
            - name: "doip_transport"
            - name: "docan_transport"
            - name: "uds_gateway"
            - name: "stub_ecu"
            - name: "uds_server"
            - name: "uds_example"
