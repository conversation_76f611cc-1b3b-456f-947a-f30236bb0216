load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

cc_library(
    name = "handler",
    srcs = [
        "src/config_parser.cpp",
        "src/diagnostic_value.cpp",
        "src/did_handler.cpp",
        "src/ioctrl_handler.cpp",
        "src/uds_did_handler_node.cpp",
        "src/uds_security_handler.cpp",
    ],
    hdrs = [
        "include/uds_example_app/config_parser.hpp",
        "include/uds_example_app/diagnostic_value.hpp",
        "include/uds_example_app/did_handler.hpp",
        "include/uds_example_app/ioctrl_handler.hpp",
        "include/uds_example_app/uds_did_handler_node.hpp",
        "include/uds_example_app/uds_security_handler.hpp",
        "include/uds_example_app/visibility_control.hpp",
    ],
    strip_include_prefix = "include",
    deps = [
        "//grace/automotive_diagnose/uds_msgs",
        "//grace/execution/executor2",
        "//grace/monitoring/logging",
    ],
)

cc_binary(
    name = "uds_example",
    srcs = ["src/main.cpp"],
    deps = [
        ":handler",
        "//common/interrupt",
        "//grace/execution/apex_init",
    ],
)

ros_pkg(
    name = "uds_examples",
    description = "Example applications to interact with UDS implementation",
    lib_executables = ["uds_example"],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "uds_examples",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

ament_pkg_resources(
    name = "uds_example_resources",
    package = "uds_examples",
    resources = {
        ":uds_example": "executable",
        "settings/app.yaml": "share",
        "settings/uds_doip.yaml": "share",
    },
)

process_manager(
    name = "uds_examples_launch",
    data = [
        ":uds_example_resources",
        "//grace/automotive_diagnose/doip_transport:doip_transport_resources",
        "//grace/automotive_diagnose/uds_gateway:uds_gateway_resources",
        "//grace/automotive_diagnose/uds_server:uds_server_resources",
        "//grace/examples/diagnostics_examples/uds_examples/src/docan_node:docan_transport_resources",
        "//grace/examples/diagnostics_examples/uds_examples/src/stub_ecu:stub_ecu_resources",
    ],
    launch_file = "launch/uds_examples.launch.yaml",
)

filegroup(
    name = "doc_files",
    srcs = [
        "include/uds_example_app/ioctrl_handler.hpp",
        "settings/app.yaml",
        "settings/uds_doip.yaml",
        "src/main.cpp",
        "src/uds_did_handler_node.cpp",
        "src/uds_security_handler.cpp",
    ],
    visibility = ["//visibility:public"],
)
