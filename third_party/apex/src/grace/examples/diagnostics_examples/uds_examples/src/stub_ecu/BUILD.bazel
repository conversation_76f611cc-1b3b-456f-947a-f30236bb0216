load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")

cc_binary(
    name = "stub_ecu",
    srcs = ["stub_ecu.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        "//common/interrupt",
        "//grace/automotive_diagnose/docan_transport/docan_transport",
        "//grace/automotive_diagnose/docan_transport/docan_transport:docan_ticker",
        "//grace/connectors/can/raw_frame",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/ros/rclcpp/rclcpp",
        "@apex//grace/automotive_diagnose/uds_msgs",
    ],
)

ament_pkg_resources(
    name = "stub_ecu_resources",
    package = "stub_ecu_pkg",
    resources = {
        ":stub_ecu": "executable",
    },
    visibility = ["//visibility:public"],
)
