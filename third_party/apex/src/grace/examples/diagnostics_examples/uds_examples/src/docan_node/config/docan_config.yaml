diagnostic:
  docan:
    - can_read_topic: "raw/reader" #topic to receive Raw CAN Frame messages from the can connector
      can_write_topic: "raw/writer" #topic to send Raw Can Frame messages to the can connector
      uds_topic_in: "to_docan" #topic to receive UDS Request message from the UDS Gateway/Doip Transport node
      uds_topic_out: "from_docan" #topic to send UDS Response message to the UDS Gateway/Doip Transport node
      uds_to_can_address_mapping:
        - uds_ecu_address: 0x2234 # UDS address of the ECU on the CAN Bus
          can_send_id: 0x100 # CAN Identifier for the UDS request message
          can_receive_id: 0x101 # CAN Identifier for the UDS response message
      isotp_flow_control:
        separation_time_ms: 0 # separation time between frames for sending multi frame messages
        block_size: 5 # size of a block of frames when ending multi frame messages
      is_can_fd: true # sets the frame size, false for standard CAN 8byte, true for CAN-FD 64 byte
      docan_padding_byte: 0xCC # Optional. Default: 0xCC padding byte set if message is not fully filled
      docan_request_timeout_ms: 2000 # Optional. Default: 2000. Timeout for the UDS request in ms
