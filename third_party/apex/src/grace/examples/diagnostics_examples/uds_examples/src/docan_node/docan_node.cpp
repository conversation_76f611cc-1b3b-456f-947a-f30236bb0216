/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief DoCAN transport node
///
#include <assert.h>
#include <stdlib.h>
#include <sys/ioctl.h>
#include <sys/select.h>
#include <sys/types.h>

#include "apex_init/apex_init.hpp"
#include "cpputils/common_exceptions.hpp"
#include "event/monitored_process.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "grace/automotive_diagnose/docan_transport/docan_transport/include/docan_factory.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "logging/logging_macros.hpp"
#include "settings/inspect.hpp"
#include "timer_service/clock_timer_service.hpp"
#include <settings/construct/getters.hpp>
#include <settings/inspect/types.hpp>

int32_t main(const int32_t argc, char ** const argv)
{
  using namespace std::chrono_literals;
  int32_t result{};

  try {
    auto scoped_init = apex::scoped_init(argc, argv, false);

    const apex::interrupt_handler::installer interrupt_handler_installer{};

    using apex::settings::inspect::array_view;
    using apex::settings::inspect::get;
    using apex::settings::inspect::maybe;
    auto global_config = apex::settings::repository::get();
    auto timer_service = std::make_shared<apex::timer_service::steady_clock_timer_service>();
    rclcpp::Node node{"docan"};
    auto const executable_items = apex::automotive_diagnose::docan::create_docan_executable_items(
      global_config, node, timer_service);

    apex::event::monitored_process process{apex::event::sender_state::DoNotSyncWithDispatcher};
    const auto executor = apex::executor::executor_factory::create(process);

    for (const auto & executable_item : executable_items) {
      (void)executor->add(executable_item);
    }
    const apex::executor::executor_runner runner{apex::executor::executor_runner::deferred,
                                                 *executor};

    scoped_init.post_init(process);

    runner.issue();
    apex::interrupt_handler::wait();

    runner.stop();
  } catch (const std::exception & e) {
    std::cerr << e.what() << std::endl;
    result = 2;
  } catch (...) {
    std::cerr << "Unknown error occurred" << std::endl;
    result = -1;
  }
  return result;
}
