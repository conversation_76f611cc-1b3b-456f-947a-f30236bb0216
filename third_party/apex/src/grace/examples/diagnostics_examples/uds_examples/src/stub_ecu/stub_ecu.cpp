/// \copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief DoCAN example nodes
///
#include <assert.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/select.h>
#include <sys/types.h>

#include <array>
#include <iostream>
#include <mutex>
#include <thread>

#include "apex_init/apex_init.hpp"
#include "cpputils/common_exceptions.hpp"
#include "event/monitored_process.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "grace/automotive_diagnose/docan_transport/docan_transport/detail/docan_wrapper.hpp"
#include "grace/automotive_diagnose/docan_transport/docan_transport/include/docan_ticker.hpp"
#include "grace/automotive_diagnose/docan_transport/docan_transport/include/docan_transport.hpp"
#include "grace/connectors/can/raw_frame/raw_frame.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "logging/logging_macros.hpp"
#include "timer_service/clock_timer_service.hpp"

namespace apex::automotive_diagnose::examples
{
namespace
{

uds_msgs::msg::UdsTransportMessage get_response(const uds_msgs::msg::UdsTransportMessage & msg)
{
  // Vehicle_Identification_Read 0x22,0xF1,0x90
  // UDS_Version_Read 0x22,0xFF,0x00
  // HydraulicsConductivitySensor9_Write 0x2E, 0x10, 0x08,...
  static std::vector<std::pair<std::vector<uint8_t>, std::vector<uint8_t>>> responses{
    {{0x22, 0xF1, 0x90}, {0x62, 0xF1, 0x90, 0x57, 0x42, 0x53, 0x50, 0x4D, 0x39, 0x43,
                          0x35, 0x32, 0x42, 0x45, 0x32, 0x30, 0x32, 0x35, 0x31, 0x34}},
    {{0x22, 0xFF, 0x00}, {0x62, 0xFF, 0x00, 0x03, 0x00, 0x00, 0x00}},
    {{0x2E, 0x10, 0x08, 0x01, 0x00, 0x00, 0x00, 0x06, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x04,
      0x00, 0x05, 0x01, 0x00, 0x00, 0x00, 0x06, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05,
      0x01, 0x00, 0x00, 0x00, 0x06, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05, 0x01, 0x00,
      0x00, 0x00, 0x06, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05},
     {0x6E, 0x10, 0x08}}

  };
  uds_msgs::msg::UdsTransportMessage ret{};
  ret.payload = {0x7F, 0x22, 0x31};  // Request out of range
  base::span<const uint8_t> payload_view{msg.payload.data(), msg.payload.size()};
  for (const auto & [key, value] : responses) {
    if (std::equal(key.begin(), key.end(), payload_view.begin())) {
      ret.payload = value;
    }
  }
  return ret;
}
}  // namespace

using CanRequestTopicType = apex_can_builtin::RawCanFrame;  // from tester
using CanResponseTopicType = apex_can_builtin::RawCanFrame;  // to ECU

class StubEcu : public apex::executor::apex_node_base
{
public:
  StubEcu(const apex::string_strict256_t & node_name,
          const apex::string_strict256_t & subscribe_topic,
          const apex::string_strict256_t & publish_topic,
          apex::timer_service::timer_subscription_ptr timer_sub,
          const apex::automotive_diagnose::docan::DoCANStackParameters & config_params)
  : apex_node_base{node_name.c_str()},
    m_publisher{get_rclcpp_node().create_publisher<CanResponseTopicType>(publish_topic.c_str(),
                                                                         rclcpp::DefaultQoS())},
    m_subscription{get_rclcpp_node().create_polling_subscription<CanRequestTopicType>(
      subscribe_topic.c_str(), rclcpp::DefaultQoS().keep_last(10U))},
    m_timer_sub{std::move(timer_sub)},
    m_logger{&get_rclcpp_node(), "stub_ecu"},
    m_docan_wrapper{[this](const uint8_t * data, size_t length, uint32_t msgid) {
                      // Can publish callback
                      this->publish(data, length, msgid);
                    },
                    [this]() -> std::vector<apex::automotive_diagnose::docan::CanFrameSimple> {
                      return handle_sub();
                    },
                    [this](const uint8_t * data, size_t length) { publish_response(data, length); },
                    [this]() -> apex::automotive_diagnose::docan::SubscriberMessages<
                               uds_msgs::msg::UdsTransportMessage> {
                      if (!this->m_responses.empty()) {
                        auto const ele = this->m_responses;
                        this->m_responses.clear();
                        return ele;
                      }
                      return {};
                    },
                    m_logger,
                    config_params},
    m_docan_parameters{config_params}
  {
  }

  void wait_for_matched() const
  {
    APEX_INFO(m_logger, "waiting for matched subscriber...");
    m_publisher->wait_for_matched(1U, std::chrono::seconds{300});
  }

private:
  bool execute_impl() override
  {
    m_docan_wrapper.spin_once();
    return true;
  }
  void publish(const uint8_t * data, const size_t length, const uint32_t msgid)
  {
    auto loaned_msg = m_publisher->borrow_loaned_message();
    auto & msg_data = loaned_msg.get();
    for (size_t i = 0U; i < length; ++i) {
      msg_data.payload.push_back(data[i]);
    }
    msg_data.can_id = m_docan_parameters.response_id;
    m_publisher->publish(std::move(loaned_msg));
  }

  std::vector<apex::automotive_diagnose::docan::CanFrameSimple> handle_sub()
  {
    auto loaned_msgs{m_subscription->take()};
    if (loaned_msgs.empty()) {
      return {};
    }
    std::vector<apex::automotive_diagnose::docan::CanFrameSimple> vec{};
    for (const auto & msg : loaned_msgs) {
      if (msg.info().valid()) {
        if (!(apex::automotive_diagnose::docan::CAN_FRAME_DATA_SIZE >= msg.data().payload.size())) {
          APEX_ERROR(m_logger, "Not enough capacity in CAN frame type");
          assert(false);
        }
        // Map to valid CAN FD lengths: 8, 12, 16, 20, 24, 32, 48, or 64 bytes
        constexpr size_t valid_canfd_lengths[] = {8, 12, 16, 20, 24, 32, 48, 64};
        constexpr size_t CANFD_MAX_LENGTH{64};
        size_t selected_length =
          CANFD_MAX_LENGTH;  // Default to max if can_length exceeds all valid lengths
        for (const auto & length : valid_canfd_lengths) {
          if (msg.data().payload.size() <= length) {
            selected_length = length;
            break;
          }
        }
        m_docan_parameters.candlength = selected_length;
      } else {
        // Set the CAN message length in the docan parameters
        constexpr size_t CAN_MAX_LENGTH{8};
        m_docan_parameters.candlength = CAN_MAX_LENGTH;
      }
      APEX_DEBUG(m_logger, "m_docan_parameters.candlength", m_docan_parameters.candlength);
      apex::automotive_diagnose::docan::CanFrameSimple frame{};
      std::copy(
        msg.data().payload.begin(), msg.data().payload.end(), static_cast<uint8_t *>(frame.data));
      frame.size = msg.data().payload.size();
      frame.msg_id = msg.data().can_id;
      vec.push_back(frame);
    }
    return vec;
  }

  void publish_response(const uint8_t * data, size_t length)
  {
    uds_msgs::msg::UdsTransportMessage docan_frame{};
    docan_frame.payload.reserve(length);
    apex::base::span<uint8_t const> const data_view(data, length);
    std::copy(data_view.begin(), data_view.end(), std::back_inserter(docan_frame.payload));
    auto response = get_response(docan_frame);
    m_responses.push_back(response);
  }

  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {m_subscription, m_timer_sub->to_sub_ptr()};
  }

  const rclcpp::Publisher<CanResponseTopicType>::SharedPtr m_publisher;
  const rclcpp::PollingSubscription<CanRequestTopicType>::SharedPtr m_subscription;
  apex::timer_service::timer_subscription_ptr m_timer_sub;
  apex::logging::Logger<> m_logger;
  apex::automotive_diagnose::docan::DoCANWrapper m_docan_wrapper;
  apex::automotive_diagnose::docan::DoCANStackParameters m_docan_parameters;
  apex::automotive_diagnose::docan::SubscriberMessages<uds_msgs::msg::UdsTransportMessage>
    m_responses;
};
}  // namespace apex::automotive_diagnose::examples

int32_t main(const int32_t argc, char ** const argv)
{
  using namespace std::chrono_literals;

  int32_t result{};

  try {
    auto scoped_init = apex::scoped_init(argc, argv, false);

    const apex::interrupt_handler::installer interrupt_handler_installer{};

    rclcpp::Node node{"example_server"};
    const apex::automotive_diagnose::docan::DoCANStackParameters config_params = []() {
      apex::automotive_diagnose::docan::DoCANStackParameters parameters{};
      parameters.physical_id = 0x100U;
      parameters.response_id = 0x101U;
      parameters.functional_id = 0x7dfU;
      parameters.separation_time_min = 0U;
      parameters.blocksize = 3U;
      parameters.candlength = 64U;
      parameters.padding_byte = 0xCC;
      return parameters;
    }();

    const apex::string_strict256_t can_read_topic{"raw/writer"};
    const apex::string_strict256_t can_write_topic{"raw/reader"};

    apex::timer_service::steady_clock_timer_service srv;
    auto ecu_timer = srv.create_timer(0s, 50ms);
    auto example_can_node = std::make_shared<apex::automotive_diagnose::examples::StubEcu>(
      "stub_ecu", can_read_topic, can_write_topic, ecu_timer, config_params);
    auto ticker_timer = srv.create_timer(0s, 1000ms);
    auto const ticker =
      std::make_shared<apex::automotive_diagnose::docan::DoCANTimerTicker>(node, ticker_timer);

    apex::event::monitored_process process{apex::event::sender_state::DoNotSyncWithDispatcher};
    const auto executor = apex::executor::executor_factory::create(process);
    example_can_node->wait_for_matched();
    (void)executor->add(example_can_node);
    (void)executor->add(ticker);
    const apex::executor::executor_runner runner{apex::executor::executor_runner::deferred,
                                                 *executor};

    scoped_init.post_init(process);

    runner.issue();
    apex::interrupt_handler::wait();

    runner.stop();
  } catch (const std::exception & e) {
    std::cerr << e.what() << std::endl;
    result = 2;
  } catch (...) {
    std::cerr << "Unknown error occurred" << std::endl;
    result = -1;
  }
  return result;
}
