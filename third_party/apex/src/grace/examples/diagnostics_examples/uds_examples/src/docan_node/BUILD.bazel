load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")

exports_files(
    ["config/docan_config.yaml"],
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "docan_node",
    srcs = ["docan_node.cpp"],
    data = ["config/docan_config.yaml"],
    visibility = ["//visibility:public"],
    deps = [
        "//common/interrupt",
        "//grace/automotive_diagnose/docan_transport/docan_transport:docan_factory",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/ros/rclcpp/rclcpp",
        "@apex//grace/automotive_diagnose/uds_msgs",
    ],
)

ament_pkg_resources(
    name = "docan_transport_resources",
    package = "docan_transport",
    resources = {
        ":docan_node": "executable",
        "config/docan_config.yaml": "share",
    },
    visibility = ["//visibility:public"],
)
