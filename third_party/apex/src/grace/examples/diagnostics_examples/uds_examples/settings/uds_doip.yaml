diagnostic:
  doip:
    # 0x01: ISO/DIS 13400-2:2010
    # 0x02: ISO 13400-2:2012
    # 0x03: this document
    protocol_version: 0x03

    gid: "GID123"
    vin: "VIN12345678901234"
    eid: "EID123"

    network_interface: "eth0"  # Not used for now.
    mac_as_eid: true  # Not used for now.
    max_payload_size: 1024

    tcp_server_address: "127.0.0.1"
    tcp_server_port: 13400
    max_connections: 2

    udp_connection_address: "127.0.0.1"
    udp_discovery_port: 13400

    uds_node: # In the future, it can be replaced with the array of UDS nodes.
      approved_client_addresses: [0x3344, 0x4455]
      can_ecu_addresses: [0x2233,0x2234]
      uds_server_address: 0x2233
      req_topic: "/uds/req"
      res_topic: "/uds/rsp"

    tcp_initial_inactivity_timeout_ms: 2000 # 2s
    tcp_general_inactivity_timeout_ms: 300000 # 5min

    vehicle_announcement_interval_ms: 500
    vehicle_announcement_count: 3

  uds:
    # definition of all Diagnostic IDs (DIDs) supported
    # by the UDS Server instance
    req_topic: /to_uds_server
    res_topic: /from_uds_server
    env_topic: /uds/env
    session_event_topic: /uds/session_event
    ecu_logical_address: 0x2233

    dids:
      # type (required): "static" or "async"
      # did (required): uint16 hex value of did
      # data (required if type="static"): the static payload of did
      # read_service (required if no write_service specified):
      # write_service (required if no read_service specified):

      -
        type: "static"
        did: 0xFF00 # UDS version
        data: [0x03, 0x00, 0x00, 0x00]
      -
        type: "async"
        did: 0xF190 #vin
        read_service: "/uds/did/example/read_vin"
        write_service: "/uds/did/example/write_vin"
      -
        type: "async"
        did: 0x0042 #threshold - custom did
        read_service: "/uds/did/example/read"
        write_service: "/uds/did/example/write"
      -
        type: "async"
        did: 0x0054 #threshold - custom did
        read_service: "/uds/did/example/read"

#! [ioctrl_section]
    ioctrl:
      - # For controlling the threshold with InputOutputControlByIdentifier  (0x2F)
        did: 0x0042 # (1)
        control_service: "/ioctrl/threshold" # (2)
#! [ioctrl_section]

    # definitions of enabled diagnostic services
    # with their parameters
    services:
      # sid (required): the ID of a service that shall be enabled

      - # Read Data By Identifier service
        sid: 0x22
      - # Write Data By Identifier service
        sid: 0x2E
      - # Security Access Service
        sid: 0x27
        get_seed_service: "/get_seed"
        compare_key_service: "/compare_key"
        level_availability_service: "/is_security_level_available"
        level_unlocking_result_topic: "/security_level_unlocking_result"
        static_seed: false
        enabled_security_levels: [0x01]
      - # Diagnostic Session Control Service
        sid: 0x10
      - # Tester Present Service
        sid: 0x3E

#! [ioctrl_enabled]
      - # Input Output Control By Identifier Service
        sid: 0x2F
#! [ioctrl_enabled]

    # Addons configuration
    addons:
      # Security Level Controller configuration
      # use_external_service (required): true/false - tells if UDS server built-in instance of
      # the Security Level Controller should be created or an external controller service shall
      # be used
      security_level_controller:
        use_external_service: false
        built_in_controller_settings: # [Optional], required in case use_external_service is false
          level_availability_service: "/is_security_level_available"
          level_unlocking_result_topic: "/security_level_unlocking_result"
          type: "common" # possible values: ["permissive", "common", "individual"]
          common_controller_settings: # [Optional], required in case of "common" type
            failure_threshold: 3
            delay_ms: 1000

    # definitions of all supported diagnostic sessions
    # the default session with id 0x01 is required
    sessions:
      # id (required): the id of a session
      # p2 (required): Server Time P2
      # p2_star (required): Server Time P2*
      # s3 (optional): Server S3 Time, the session timeout default is 5000ms
      # the possible range is <2000ms, 30000ms>
      # max_pending_responses (optional): Maximum number of pending responses, default value 4
      # The P4 server = P2 + P2* x (max_pending_responses - 1)
      -
        id: 0x01 # default session
        p2: 1000
        p2_star: 2000
        max_pending_responses: 5
      -
        id: 0x03 # extended session
        p2: 500
        p2_star: 1000
        s3: 60000 # Keep this long to avoid resetting back to default session to fast
        max_pending_responses: 3

    # definitions of security accesses that enable
    # service and did in a session
    access_control:
      # session_id (required): the ID of a session for which security access is set
      # sid (required): the ID of a service for which security access is set
      # security_levels (optional): an array of security levels allowed to access; no restrictions by default
      # role (optional): the array of roles that can access this service; if empty - no role is needed
      # env (optional): the map of condition/value that specifies environment conditions needed to access
      # dids (required for SID 0x22 and 0x2E): the map of did and their security access
      # did (required for did access): the did value
      # allowed_addresses (optional for did and service access): the list of allowed tester addresses
      # subfunctions (required for subfunctions supporting services): the map of subfunctions and their access restrictions
      # subfunction: the subfunction byte value

      # default session services
      - # default session Diagnostic Session Control Service
        session_id: 0x01
        sid: 0x10
        subfunctions:
          -
            subfunction: 0x03
      -  # default session Read By Did Service
        session_id: 0x01
        sid: 0x22
        dids: # default session read dids
          -
            did: 0xFF00
          -
            did: 0xF190
          -
            did: 0x0042
          -
            did: 0x0054
      # extended session services
      - # extended session Diagnostic Session Control Service
        session_id: 0x03
        sid: 0x10
      - # extended session Security Access Service
        session_id: 0x03
        sid: 0x27
        subfunctions:
          -
            subfunction: 0x01
          -
            subfunction: 0x02
        # no conditions are needed to change session while in an extended session
      - # extended session read by did service
        session_id: 0x03
        sid: 0x22
        security_levels: [0x01]
        dids: # extended session read dids
          -
            did: 0xFF00
            security_levels: [0x01]
          -
            did: 0xFF01
            security_levels: [0x01]
          -
            did: 0xF190
            security_levels: [0x01]
          -
            did: 0x0042
            security_levels: [0x01]
          -
            did: 0x0054
            security_levels: [0x01]
      - # extended session Write By DID Service
        session_id: 0x03
        sid: 0x2E
        security_levels: [0x01]
        dids: # extended session write dids
          -
            did: 0xF190
            security_levels: [0x01]
          -
            did: 0x0042
            security_levels: [0x01]

#! [ioctrl_access_control]
      - # extended session Input Output Control By Identifier Service
        session_id: 0x03
        sid: 0x2F
        security_levels: [0x01]
        dids: # extended session write dids
          -
            did: 0x0042
            security_levels: [0x01]
#! [ioctrl_access_control]
