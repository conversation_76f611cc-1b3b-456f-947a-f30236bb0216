---
tags:
  - Colcon
  - MQTT
  - connector
---

# MQTT connector example

## Introduction

This article provides an example of how to use the [Apex.Grace MQTT connector](mqtt-connector-design.md).
The MQTT connector provides an easy way to republish Apex.Grace messages to and from an MQTT broker.
The MQTT connector can work with
[AWS IoT Core](https://docs.aws.amazon.com/iot/latest/developerguide/connect-to-iot.html).

!!! note
    When compiling from source, this feature is only available if
    environment variable `ENABLE_PROTOBUF=ON` is set.

The Apex.Grace `mqtt_connector_example` package demonstrates republishing `std_msgs::msg::Bool`

1. from the ROS topic `from_topic` to the MQTT topic `mqtt_topic`,
1. and then back from `mqtt_topic` to the ROS topic `to_topic`.

See additional details in the [MQTT connector design article](mqtt-connector-design.md#mqttconnector-usage).

## Requirements

The connector requires [AWS IoT Thing](https://docs.aws.amazon.com/iot/latest/developerguide/iot-thing-management.html)
in order to communicate with the AWS MQTT broker.
This Amazon service makes an AWS account or AWS IAM user necessary.
Ask the company IT department to provide AWS access details or create a new AWS account.

<!-- notes for article validation:

- use endpoint: `a21pn3kj5g71f9-ats.iot.us-west-2.amazonaws.com`
- ask credentials or certificate files to Apex.AI IT
-->

## Create the AWS IoT resources and configuration file

The AWS CLI tool `aws` is already installed in ADE. Configure it as described
[here](https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-quickstart.html).

Then, some AWS IoT resources need to be created:

- AWS IoT _thing_
- AWS IoT certificate and RSA key
- AWS IoT policy

To set up these resources, first, create a folder for the secrets and configuration files:

```shell ade
mkdir ~/mqtt_connector
cd ~/mqtt_connector
```

Save `create_aws_iot_resources.sh` with these contents:

{{ code_snippet(
    'grace/connectors/mqtt/test/create_aws_iot_resources.sh',
    {"tag": "##! [Create_AWS_IoT_Resources Script]"} )}}

Execute the script:

```shell ade
bash ./create_aws_iot_resources.sh <name-of-thing>
```

If successful, the following files will have been created:

- `AmazonRootCA1.pem`
- `cert.pem`
- `public.key`
- `private.key`
- `settings.yaml`

## Run the MQTT connector

Terminal 0 - Apex.Ida resource creator

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell ade
    bazel run //ida/resource_creator
    ```

=== "colcon"

    ```shell ade
    /opt/ApexIdaBilbo/bin/iox-roudi # Optional, only if zero-copy is active
    ```
<!-- markdownlint-disable MD046 -->

Terminal 1 - MQTT connector

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell ade
    bazel run //grace/examples/mqtt_connector_example -- --apex-settings-file /opt/ApexGraceBilbo/share/mqtt_connector_example/param/settings_example.yaml
    ```

=== "colcon"

    ```shell ade
    source /opt/ApexGraceBilbo/setup.bash
    ros2 run mqtt_connector_example mqtt_connector_example --apex-settings-file \
    /opt/ApexGraceBilbo/share/mqtt_connector_example/param/settings_example.yaml
    ```
<!-- markdownlint-disable MD046 -->

Terminal 2 - publish data

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell ade
    bazel run //grace/cli -- topic pub /from_topic std_msgs/msg/Bool "data: true"
    ```

=== "colcon"

    ```shell ade
    source /opt/ApexGraceBilbo/setup.bash
    ros2 topic pub /from_topic std_msgs/msg/Bool "data: true"
    ```
<!-- markdownlint-disable MD046 -->

Terminal 3 - receive data

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell ade
    bazel run //grace/cli -- topic echo /to_topic
    ```

=== "colcon"

    ```shell ade
    source /opt/ApexGraceBilbo/setup.bash
    ros2 topic echo /to_topic
    ```
<!-- markdownlint-disable MD046 -->

Terminal 3 should continuously output `data: true`. This message travelled from Apex.Grace to MQTT
and then back to Apex.Grace.

## Run the MQTT5 service connector

In this case the example is running a [](rclcpp::PollingService) to be able to respond
to incoming MQTT requests.

Terminal 1 - run the connector

```shell ade
source /opt/ApexGraceBilbo/setup.bash
ros2 run mqtt_connector_example mqtt5_connector_service_example --apex-settings-file \
/opt/ApexGraceBilbo/share/mqtt_connector_example/param/settings_example.yaml
```

Terminal 2 - send a ROS request

```shell ade
source /opt/ApexGraceBilbo/setup.bash
ros2 service call /input_ros_service example_interfaces/srv/AddTwoInts "{"a": 1, "b": 4 }"
```

Terminal 2 should have the following output:

```shell
requester: making request: example_interfaces.srv.AddTwoInts_Request(a=1, b=4)

response:
example_interfaces.srv.AddTwoInts_Response(sum=5)
```

This request travelled from Apex.Grace to MQTT and back, where it received a response. This MQTT
response was sent to the broker and back to Apex.Grace and finally to the client
making the request.

## Limitations and Known Issues

See the [known issues and limitations](known-issues-and-limitations-ApexOS.md) for more details.
