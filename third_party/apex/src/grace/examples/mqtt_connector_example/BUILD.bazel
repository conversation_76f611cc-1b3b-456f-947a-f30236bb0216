load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_protobuf_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "c_msgs_protobuf_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_pkg//:mappings.bzl", "pkg_files")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "mqtt_connector_example_pkg",
    description = "Example package to demonstrate mqtt connector.",
    lib_executables = [
        ":mqtt_connector_example",
        ":mqtt5_connector_example",
    ],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    share_data = [":share_data"],
    version = "1.1.0",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/connectors/mqtt:mqtt_connector_pkg",
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/rosidl/protobuf_typesupport:protobuf_typesupport_pkg",
    ],
)

pkg_files(
    name = "share_data",
    srcs = [
        "param/settings_example.yaml",
    ],
)

cpp_msgs_protobuf_library(
    name = "std_msgs_proto",
    msgs = "//grace/interfaces/std_msgs",
)

c_msgs_protobuf_library(
    name = "std_msgs_proto_c",
    msgs = "//grace/interfaces/std_msgs",
)

cpp_msgs_protobuf_library(
    name = "test_msgs_proto",
    msgs = "//grace/interfaces/test_msgs",
)

c_msgs_protobuf_library(
    name = "test_msgs_proto_c",
    msgs = "//grace/interfaces/test_msgs",
)

cpp_msgs_protobuf_library(
    name = "example_interfaces_proto",
    msgs = "//grace/examples/example_interfaces",
)

c_msgs_protobuf_library(
    name = "example_interfaces_proto_c",
    msgs = "//grace/examples/example_interfaces",
)

cc_binary(
    name = "mqtt_connector_example",
    srcs = ["src/mqtt_connector_example.cpp"],
    data = ["param/settings_example.yaml"],
    tags = ["exclude_sca"],
    deps = [
        ":std_msgs_proto",
        ":std_msgs_proto_c",
        "//grace/connectors/mqtt:mqtt_connector",
        "//grace/interfaces/std_msgs",
    ],
)

cc_binary(
    name = "mqtt5_connector_example",
    srcs = ["src/mqtt5_connector_example.cpp"],
    data = ["param/settings_example.yaml"],
    tags = ["exclude_sca"],
    deps = [
        ":test_msgs_proto",
        ":test_msgs_proto_c",
        "//grace/connectors/mqtt:mqtt5_connector",
        "//grace/interfaces/test_msgs",
    ],
)

cc_binary(
    name = "mqtt5_connector_service_example",
    srcs = ["src/mqtt5_connector_service_example.cpp"],
    data = ["param/settings_example.yaml"],
    tags = ["exclude_sca"],
    deps = [
        ":example_interfaces_proto",
        ":example_interfaces_proto_c",
        "//grace/connectors/mqtt:mqtt5_connector",
        "//grace/examples/example_interfaces",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "src/mqtt5_connector_example.cpp",
        "src/mqtt5_connector_service_example.cpp",
        "src/mqtt_connector_example.cpp",
    ],
    visibility = [
        "//grace/connectors/mqtt/doc:__subpackages__",
    ],
)
