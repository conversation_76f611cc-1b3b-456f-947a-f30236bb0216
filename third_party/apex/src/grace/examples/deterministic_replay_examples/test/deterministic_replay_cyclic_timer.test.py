# Copyright 2022 Apex.AI, Inc.
# All rights reserved.

from datetime import datetime, timedelta
import unittest

import launch_ros.actions
import launch_testing
import launch_testing.actions
import rclpy
from rclpy.node import Node
from std_msgs.msg import Int32

from ament_index_python.packages import get_package_share_directory


def generate_test_description():

    # Begin: SYSTEM UNDER TEST
    sut = launch_ros.actions.Node(
        executable="sut_cyclic_timer_exe",
        output='screen')

    coordinator = launch_ros.actions.Node(
        executable="replay_coordinator",
        arguments=[
            "--rosbag-path",
            "{}/test/rosbag2_deterministic_replay_test".format(
                get_package_share_directory("deterministic_replay_examples"))
        ],
        output='screen')
    # End: SYSTEM UNDER TEST

    return launch_testing.LaunchDescription([
        sut,
        coordinator,
        launch_testing.actions.ReadyToTest()
    ])


class ReceiverNode(Node):
    """Receiver node to collect the processed messages."""

    def __init__(self, name, topic):
        super().__init__(name)
        self.sub = self.create_subscription(Int32, topic,
                                            self.callback, 10)
        self.received_data = []

    def callback(self, msg):
        self.received_data.append(msg.data)


class TestNode(unittest.TestCase):

    @classmethod
    def setUpClass(cls, launch_service, proc_info, proc_output):
        rclpy.init(args=None)

    def test_deterministic_execution(self):
        test_node = ReceiverNode(
            name="receiver_node",
            topic="test_topic"
        )
        expected_data = [2, 906, 2, 1206, 1506, 2, 1806, 2406]
        start_time = datetime.now()
        wait_limit = timedelta(seconds=180)
        while (len(test_node.received_data) < len(expected_data)):
            if datetime.now() - start_time > wait_limit:
                raise Exception("Test timed out!")
            rclpy.spin_once(test_node, timeout_sec=0.5)
        print(test_node.received_data)
        self.assertTrue(
            expected_data == test_node.received_data,
            "Replay was not deterministic"
        )
