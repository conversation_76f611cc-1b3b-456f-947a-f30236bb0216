cmake_minimum_required(VERSION 3.5)

project(deterministic_replay_examples)

if(CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  # The package has a dependency on Boost program_options,
  # which we currently only build for GNU libstdc++
  message("Building the 'replay' package with Clang is not currently supported,
  so this package will be skipped")
  return()
endif()

find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()

ament_auto_add_library(sut_lib
    include/deterministic_replay_examples/deterministic_node.hpp
    include/deterministic_replay_examples/utils.hpp
    src/deterministic_node.cpp
)

# Single process targets
ament_auto_add_executable(sut_exe src/main.cpp)
# Workaround to include the compile definition since it is not
# exported to this package
# This should be replaced with a target level export (see #13048)
target_compile_definitions(sut_exe PRIVATE REPLAY_SUPPORT)

ament_auto_add_executable(sut_chain_of_nodes_exe src/main_chain_of_nodes.cpp)
target_compile_definitions(sut_chain_of_nodes_exe PRIVATE REPLAY_SUPPORT)

ament_auto_add_executable(sut_graph_of_nodes_exe src/main_graph_of_nodes.cpp)
target_compile_definitions(sut_graph_of_nodes_exe PRIVATE REPLAY_SUPPORT)

ament_auto_add_executable(sut_cyclic_timer_exe src/main_cyclic_timer.cpp)
target_compile_definitions(sut_cyclic_timer_exe PRIVATE REPLAY_SUPPORT)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()

  find_package(ros_testing REQUIRED)
  add_ros_test(test/deterministic_replay.test.py)
  add_ros_test(test/deterministic_replay_chain_of_nodes.test.py
      TIMEOUT 120)
  add_ros_test(test/deterministic_replay_cyclic_timer.test.py
      TIMEOUT 120)
  add_ros_test(test/deterministic_replay_graph_of_nodes.test.py
      TIMEOUT 120)
endif()

ament_auto_package(INSTALL_TO_SHARE launch test/rosbag2_deterministic_replay_test)