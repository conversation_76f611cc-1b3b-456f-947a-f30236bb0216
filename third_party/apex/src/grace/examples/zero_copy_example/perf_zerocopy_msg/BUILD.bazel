load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")

# ""
package(default_visibility = ["//visibility:public"])

msgs_library(
    name = "perf_zerocopy_msg",
    srcs = [
        "msg/Array100k.idl",
        "msg/FlatSequencesBounded.idl",
        "srv/FlatSequencesBoundedSrv.idl",
    ],
    visibility = ["//visibility:public"],
)

filegroup(
    name = "doc_files",
    srcs = glob([
        "msg/*.idl",
        "srv/*.idl",
    ]),
    visibility = ["//grace/examples/zero_copy_example/zero_copy_example/doc:__pkg__"],
)
