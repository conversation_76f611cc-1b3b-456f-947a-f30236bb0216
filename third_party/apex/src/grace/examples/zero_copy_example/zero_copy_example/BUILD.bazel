load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "zero_copy_example",
    description = "Example how to use zero-copy with Apex.OS",
    lib_executables = [
        "zero_copy_polling_subscription",
        "zero_copy_publisher",
        "zero_copy_service_client",
        "zero_copy_service",
        "zero_copy_subscription",
    ],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "zero_copy_example",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "zero_copy_publisher",
    srcs = ["src/zero_copy_publisher.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/test_msgs",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "zero_copy_polling_subscription",
    srcs = ["src/zero_copy_polling_subscription.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/interfaces/test_msgs",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "zero_copy_subscription",
    srcs = ["src/zero_copy_subscription.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/interfaces/test_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "zero_copy_service",
    srcs = ["src/zero_copy_service.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/interrupt",
        "//grace/examples/zero_copy_example/perf_zerocopy_msg",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "zero_copy_service_client",
    srcs = ["src/zero_copy_service_client.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/interrupt",
        "//grace/examples/zero_copy_example/perf_zerocopy_msg",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

filegroup(
    name = "doc_files",
    srcs = glob([
        "src/*.cpp",
    ]),
    visibility = ["//grace/examples/zero_copy_example/zero_copy_example/doc:__pkg__"],
)
