---
tags:
  - Zero-copy
  - Shared-memory
---

# Transmit large messages efficiently with zero copy

When a publisher and subscriber are on the same host (in the same or different processes) and do not
use a connector, Apex.Ida automatically selects shared memory as the transport mechanism. Although
the shared memory transport may require serialization, deserialization, and copying messages into
shared memory, this overhead is generally acceptable for small messages. However, it can become a
performance bottleneck for large messages such as point clouds or camera images.

To efficiently transmit large messages, Apex.Ida provides two key optimizations:

1. **Avoid copies in shared memory** by loaning shared memory and write data directly to shared
   memory instead of copying it
1. **Avoid message serialization** by using self-contained messages[^2]

When both optimizations are used, no copies are made at any stage, an approach known as
*zero-copy communication*.

## Avoid copies in shared memory

To avoid copying the data from the publisher to the shared memory, it is required to loan a
memory chunk from the shared memory and fill it directly with the data to send (as shown in the
minimal pub/sub example).

{{ code_snippet("grace/examples/zero_copy_example/zero_copy_example/src/zero_copy_publisher.cpp",
    {"tag": "//! [zero-copy publisher]"}) }}

When the subscriber receives a message, it is given a loan to the data in the same physical memory
location where it was published. When it is done processing the message, the loan is returned and
the memory can be reused. Borrowing the loan from the shared memory is automatically done when
taking or reading the message from the subscription.

### Common pitfall: copies in the publisher

A common pitfall is creating data first and then copying (moving isn't possible) it into the loaned
message. Instead, fill the loaned message in-place as shown below.

```cpp
// DON'T
std::vector<int32_t> my_data;
fill_vector(my_data);

auto loaned_message = this->publisher_->borrow_loaned_message();

// Leads to potential expensive copy
loaned_message.get().int32_values = my_data;
loaned_message.get().int32_values = std::move(my_data);
std::copy(my_data.begin(), my_data.begin() + my_data.size(), loaned_message.get().data.begin());
 std::memcpy(my_data.data(), loaned_message.get().int32_values.data(), my_data.size() * sizeof(int));

// INSTEAD DO
auto loaned_message = this->publisher_->borrow_loaned_message();
for (std::size_t i = 0; i < size; ++i) {
    loaned_message.get().data[i] = static_cast<int>(i * 2);
}
```

## Avoid message serialization

To prevent a message from being serialized or deserialized during communication, it must be
self-contained[^2]. In other words, its maximum size must be determined at compile time, and it
must not contain pointers to memory outside of the allocated shared memory chunk. If a type is not
self-contained[^1], it cannot be stored directly in shared memory and must be serialized.

Examples of bounded, self-contained[^2] types:

- `uint32`
- `char data[42]`
- `sequence<string<64>,3>`

Examples of unbounded, non self-contained[^1] types:

- `string`
- `sequence<int>`

More information about the message types is available in the [Defining a custom message type
article](/grace/doc/communication/defining-a-custom-message-type.md).

### Common pitfall: not using the `BorrowedType` message type

Although it's recommended to infer the type of the loan message with the `auto` keyword when
borrowing a message, there are cases—such as function parameters (ROS 2 callbacks, or
message-filling functions)— where the type must be explicitly specified. In these scenarios, use the
`BorrowedType` to avoid serialization. However, be aware that if `BorrowedType` is used with a non
self-contained (unbounded)[^1] type, the code may still compile but will trigger
serialization/deserialization.

Example:

```cpp
// DON'T
void topic_callback(const test_msgs::msg::BoundedSequences & msg) const  {...}

// INSTEAD DO
void topic_callback(const test_msgs::msg::BoundedSequences::BorrowedType & msg) const  {...}
```

## Examples

The `zero_copy_example` package contains examples that can be copied/pasted to help getting started
with different scenarios:

- zero-copy communication between a publisher and a  `rclcpp::PollingSubscription`
- zero-copy communication between a publisher and a ROS 2 `rclcpp::Subscription`
- zero-copy communication between a client and a service

To see a zero-copy communication in action (for example between a publisher and a polling
subscription), run the following commands:

```shell dollar
# Terminal 1
$ bazel run @apex//ida/resource_creator
```

```shell dollar
# Terminal 2
$ bazel run @apex//grace/examples/zero_copy_example/zero_copy_example:zero_copy_publisher
```

```shell dollar
# Terminal 3
$ bazel run @apex//grace/examples/zero_copy_example/zero_copy_example:zero_copy_polling_subscription
```

If communication is successful, the publisher and subscriber consoles should look like the
following:

Publisher output:

```shell
...
Publishing message:

    string_values\[0\]: hello 0
    string_values\[1\]: hello 1
    string_values\[2\]: hello 2
...
```

Subscriber output:

```shell
...
Received message:

    string_values\[0\]:  hello 0
    string_values\[1\]:  hello 1
    string_values\[2\]:  hello 2
...
```

## FAQ

### How to check if zero-copy is enabled?

There is currently no direct way to verify that zero-copy is being used, however profiling the
application can reveal any unexpected copies.

### How to avoid copying a received message into a loaned message to be published?

In scenarios where a message is received and then needs to be republished, copying the message
becomes necessary because the loaned buffer cannot be modified in-place. This additional copy
introduces overhead and can degrade performance. However, these situations can typically be avoided
by adopting a more efficient architecture.

{%
include-markdown "grace/doc/communication/communication.md"
start="<!-- START FOOTNOTE SNIPPET -->"
end="<!-- END FOOTNOTE SNIPPET -->"
heading-offset=0
%}
