load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "execution_monitor_examples_pkg",
    description = "Examples and integration tests for the execution monitor",
    lib_executables = [
        "monitored_executor_task",
    ],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "execution_monitor_examples",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

# TODO(carlos): install to share param directory

cc_binary(
    name = "minimal_monitoring_example",
    srcs = ["src/minimal_monitoring_example.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/std_msgs",
        "//grace/monitoring/execution_monitor",
        "//grace/monitoring/logging",
    ],
)

cc_binary(
    name = "monitored_executor_task",
    srcs = ["src/monitored_executor_task.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/std_msgs",
        "//grace/monitoring/execution_monitor",
        "//grace/monitoring/execution_monitor_settings_utils",
        "//grace/monitoring/logging",
    ],
)

ament_pkg_resources(
    name = "execution_monitor_examples_resources",
    package = "execution_monitor_examples",
    resources = {
        "param/failing_expectation_too_few_act_per_win.yaml": "share",
        "param/failing_expectation_too_many_act_per_win.yaml": "share",
        "param/fulfilled_expectations.yaml": "share",
    },
    visibility = ["//visibility:public"],
)

# TODO(carlos) Migrate test to os_verify_docs once that's bazelized (&356, #17999, #18249, #21244)
#py_test(
#    name = "integration_tests",
#    srcs = [
#        "test/integration_test.py",
#    ],
#    deps = [
#        requirement("pytest"),
#    ],
#)

filegroup(
    name = "doc_files",
    srcs = [
        "param/fulfilled_expectations.yaml",
        "src/minimal_monitoring_example.cpp",
    ],
    visibility = [
        "//grace/examples/execution_monitor_examples/design:__subpackages__",
        "//grace/monitoring/execution_monitor/design:__subpackages__",
    ],
)
