/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief An example of how to avoid runtime memory allocation

#include "apex_init/apex_init.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "timer_service/clock_timer_service.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "logging/logging_macros.hpp"

#include <vector>
#include <iostream>
#include <memory>
#include <utility>

using namespace std::chrono_literals;

namespace apex
{
namespace examples
{
//! [RingBufferDemo Constructor]
template<size_t BUFFER_SIZE>
class RingBufferDemo : public apex::executor::apex_node_base
{
public:
  explicit RingBufferDemo(const apex::string_strict256_t & node_name)
  : apex::executor::apex_node_base{node_name}
  {
    m_vector.reserve(BUFFER_SIZE);
    m_vector.resize(BUFFER_SIZE);
    m_it = m_vector.begin();
  }

//! [RingBufferDemo Constructor]

private:
  bool execute_impl() override
  {
    static int32_t i = 0;
    APEX_INFO(m_logger, "Looping...");
    insert(i++);
    print();
    return true;
  }

  //! [RingBufferDemo pre_loop_init]
  void pre_loop_init()
  {
    // The pre-allocated memory code is redundant with that in the constructor.
    // It is intended to show a different way to pre-allocate the memory.
    m_vector.reserve(BUFFER_SIZE);
    m_vector.resize(BUFFER_SIZE);
    m_it = m_vector.begin();
  }
  //! [RingBufferDemo pre_loop_init]

  void print() noexcept
  {
    APEX_INFO(m_logger, "======Ring buffer content======");
    apex::string_strict256_t values = "";
    for (auto & v : m_vector) {
      values += apex::string_strict16_t::to_string(v) + ", ";
    }
    APEX_INFO(m_logger, values);
  }

  void insert(const int32_t element)
  {
    if (m_it == m_vector.end()) {
      m_it = m_vector.begin();
    }
    *m_it = element;
    (void)m_it++;
  }

  // We use std::vector here but pre-allocate the memory in `pre_loop_init` or constructor
  std::vector<int32_t> m_vector;
  std::vector<int32_t>::iterator m_it;
  apex::logging::Logger<> m_logger{&get_rclcpp_node()};
};

}  // namespace examples
}  // namespace apex


int32_t apex_main(const int32_t argc, char ** const argv)
{
  //! [Initialization phase]
  auto scoped_init = apex::scoped_init(argc, argv, false);
  const apex::interrupt_handler::installer interrupt_handler_installer{};

  // Create the ring buffer node
  auto ring_buffer_demo =
    std::make_shared<apex::examples::RingBufferDemo<4U>>("ring_buffer_demo");

  const auto exec = apex::executor::executor_factory::create();
  apex::timer_service::steady_clock_timer_service timer_srv;

  // Add the ring buffer node to the executor
  (void)exec->add_periodic(std::move(ring_buffer_demo), timer_srv, 500ms);

  apex::executor::executor_runner runner{apex::executor::executor_runner::deferred, *exec};

  scoped_init.post_init();
  // Start the executor runner
  runner.issue();
  apex::interrupt_handler::wait();
  runner.stop();

  return 0;
}
