/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \include apex_rt_ex.cpp
/// \brief An example of Apex.OS real time settings

#include "apex_init/apex_init.hpp"
#include "apex_init/apex_main.hpp"
#include "settings/repository.hpp"
#include "settings/from_yaml.hpp"
#include "logging/logging_macros.hpp"

int32_t apex_main(const int32_t argc, char ** const argv)
{
  apex::settings::construct::dictionary dict;
  apex::settings::yaml::from_string(
    R"(
    rt_settings:
      proc_prio: 4
      proc_cpu_mask: 1
      proc_max_mem_mb: 1024
      proc_scheduler: fifo
    )",
    dict);
  apex::settings::repository::set(dict);

  // initialize Apex.OS framework
  auto scoped_init = apex::scoped_init(argc, argv);
  scoped_init.post_init();

  return 0;
}
