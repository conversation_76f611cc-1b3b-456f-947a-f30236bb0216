/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \include apex_string.cpp
/// \brief Example usage of Apex.OS bounded strings

#include "apex_init/apex_init.hpp"
#include "logging/logging_macros.hpp"

int32_t apex_main(const int32_t argc, char ** const argv)
{
  // initializes Apex.OS framework
  auto scoped_init = apex::scoped_init(argc, argv);
  // create a string of length 255 characters + trailing 0
  const apex::string256_t s256 = "12345678";
  scoped_init.post_init();

  // print to memory, view with e.g.
  APEX_INFO_R(
    "Created bounded string with size of",
    sizeof(s256),
    "and contents of",
    s256.c_str()
  );

  return 0;
}
