/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Example on how to measure the latency of just Apex.OS message transfer

#include <utility>

#include "executor2/apex_node_base.hpp"
#include "logging/logging_macros.hpp"
#include "std_msgs/msg/int32.hpp"

namespace message_logging_latency
{

using MessageType = std_msgs::msg::Int32;

class publisher_base : public apex::executor::apex_node_base
{
public:
  publisher_base()
  : apex::executor::apex_node_base("foo"),
    m_publisher{get_rclcpp_node().create_publisher<MessageType>(
        "topic", rclcpp::DefaultQoS())} {}

protected:
  rclcpp::Publisher<MessageType>::SharedPtr m_publisher;
  apex::logging::Logger<> m_logger{&get_rclcpp_node(), "A publisher"};
};

class subscriber_base : public apex::executor::apex_node_base
{
public:
  subscriber_base()
  : apex::executor::apex_node_base("bar"),
    m_subscription{
      get_rclcpp_node().create_polling_subscription<MessageType>(
        "topic", rclcpp::DefaultQoS())} {}

protected:
  rclcpp::PollingSubscription<MessageType>::SharedPtr m_subscription;
};

namespace no_logging
{
/*lint --e{753} This is just a MWE for the docs, so the classes A and B are not used anywhere */
class A : public publisher_base
{
  bool execute_impl() override;
};

class B : public subscriber_base
{
  bool execute_impl() override;
};

/*lint --e{1788} This is just a MWE for the docs, so variables are unused */

//! [publish no logging]
bool A::execute_impl()
{
  auto loaned_msg{m_publisher->borrow_loaned_message()};
  auto & msg = loaned_msg.get();
  msg.data = 42;
  const auto time_of_departure = get_rclcpp_node().get_clock()->now();  // (1)!
  m_publisher->publish(std::move(msg));
  return true;
}
//! [publish no logging]

//! [subscribe no logging]
bool B::execute_impl()
{
  auto loaned_msgs{m_subscription->take()};
  for (const auto & msg : loaned_msgs) {
    if (msg.info().valid()) {
      const auto time_of_arrival = get_rclcpp_node().get_clock()->now();  // (1)!
      //! [subscribe no logging]
    }
  }
  return true;
}
}  // namespace no_logging

namespace logging
{

/*lint --e{753} This is just a MWE for the docs, so the classes A and B are not used anywhere */
class A : public publisher_base
{
  bool execute_impl() override;
};

class B : public subscriber_base
{
  bool execute_impl() override;
};

/*lint --e{1788} This is just a MWE for the docs, so some variables are unused */

//! [publish logging]
bool A::execute_impl()
{
  auto loaned_msg{m_publisher->borrow_loaned_message()};
  auto & msg = loaned_msg.get();
  msg.data = 42;
  const auto time_of_departure = get_rclcpp_node().get_clock()->now();
  APEX_INFO(m_logger, "LOGGING IN THE PUBLISHER AFTER TIME STAMPING IS BAD!");
  m_publisher->publish(std::move(msg));
  return true;
}
//! [publish logging]

//! [subscribe logging]
bool B::execute_impl()
{
  auto loaned_msgs{m_subscription->take()};
  std::cout << "WRITING TO STDOUT IN THE SUBSCRIBER BEFORE TIME STAMPING IS BAD" <<
    std::endl;
  for (const auto & msg : loaned_msgs) {
    if (msg.info().valid()) {
      const auto time_of_arrival = get_rclcpp_node().get_clock()->now();
      //! [subscribe logging]
    }
  }
  return true;
}
}  // namespace logging

}  // namespace message_logging_latency
