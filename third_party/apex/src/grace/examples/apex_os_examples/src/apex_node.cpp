/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief An example Apex.OS node

#include "apex_init/apex_init.hpp"
#include "apex_init/apex_main.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "std_msgs/msg/string.hpp"
#include "logging/logging_macros.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "rclcpp/polling_subscription.hpp"
#include "rclcpp/publisher.hpp"
#include "timer_service/clock_timer_service.hpp"

#include <chrono>
#include <utility>
#include <memory>

namespace apex
{
namespace node_example
{

using apex::executor::apex_node_base;
using apex::executor::subscription_list;
using apex::logging::Logger;
using rclcpp::LoanedSamples;
using rclcpp::LoanedMessage;
using rclcpp::Publisher;
using rclcpp::PollingSubscription;
using OutputX = std_msgs::msg::String;
using OutputY = std_msgs::msg::String;
using OutputA = std_msgs::msg::String;
using OutputB = std_msgs::msg::String;

//! [Node X]
class NodeX : public apex_node_base
{
public:
  NodeX();

private:
  bool execute_impl() override;

  void do_work(
    LoanedSamples<OutputA::BorrowedType> &,
    LoanedSamples<OutputB::BorrowedType> &,
    LoanedMessage<OutputX::BorrowedType> &);

  subscription_list get_triggering_subscriptions_impl() const override
  {
    return {m_subscription_a};
  }

  subscription_list get_non_triggering_subscriptions_impl() const override
  {
    return {m_subscription_b};
  }

  const Publisher<OutputX>::SharedPtr m_publisher_x;
  const PollingSubscription<OutputA>::SharedPtr m_subscription_a;
  const PollingSubscription<OutputB>::SharedPtr m_subscription_b;
  Logger<> m_logger;
};
//! [Node X]

NodeX::NodeX()
: apex_node_base{"node_x"},
  m_publisher_x{get_rclcpp_node().create_publisher<OutputX>(
      "node_x_topic", rclcpp::DefaultQoS())},
  m_subscription_a{get_rclcpp_node().create_polling_subscription<OutputA>("node_a_topic",
      rclcpp::DefaultQoS())},
  m_subscription_b{get_rclcpp_node().create_polling_subscription<OutputB>("node_b_topic",
      rclcpp::DefaultQoS())},
  m_logger{&get_rclcpp_node(), "node_x"}
{
  APEX_INFO(m_logger, "Initialized Node X");
}

bool NodeX::execute_impl()
{
  //! [Zero-copy]
  // read inputs from the middleware memory
  auto inputs_a{m_subscription_a->take()};
  auto inputs_b{m_subscription_b->take()};
  // loan memory from the middleware to write the output to
  auto output_x{m_publisher_x->borrow_loaned_message()};
  // process inputs and prepare the output
  do_work(inputs_a, inputs_b, output_x);
  // publish the output, releasing the loan on the memory
  m_publisher_x->publish(std::move(output_x));
  //! [Zero-copy]
  return true;
}
//lint -e{9175, 1762} example code only NOLINT
void NodeX::do_work(
  LoanedSamples<OutputA::BorrowedType> &,
  LoanedSamples<OutputB::BorrowedType> &,
  LoanedMessage<OutputX::BorrowedType> &)
{
  APEX_INFO(m_logger, "Node X doing work!");
// this is where the node's work logic would be implemented
}

class NodeA : public apex_node_base
{
public:
  NodeA()
  : apex_node_base{"node_a"},
    m_publisher_a{get_rclcpp_node().create_publisher<OutputX>(
        "node_a_topic", rclcpp::DefaultQoS())}
  {}

private:
  //lint -e{9175} example code only NOLINT
  bool execute_impl() override
  {
    auto output_a{m_publisher_a->borrow_loaned_message()};
    m_publisher_a->publish(std::move(output_a));
    return true;
  }

  const Publisher<OutputX>::SharedPtr m_publisher_a;
};

class NodeB : public apex_node_base
{
public:
  NodeB()
  : apex_node_base{"node_b"},
    m_publisher_b{get_rclcpp_node().create_publisher<OutputX>(
        "node_b_topic", rclcpp::DefaultQoS())}
  {}

private:
  //lint -e{9175} example code only NOLINT
  bool execute_impl() override
  {
    auto output_b{m_publisher_b->borrow_loaned_message()};
    m_publisher_b->publish(std::move(output_b));
    return true;
  }

  const Publisher<OutputX>::SharedPtr m_publisher_b;
};

class NodeY : public apex_node_base
{
public:
  NodeY()
  : apex_node_base{"node_y"},
    m_publisher_y{get_rclcpp_node().create_publisher<OutputX>(
        "node_x_topic", rclcpp::DefaultQoS())},
    m_subscription_x{get_rclcpp_node().create_polling_subscription<OutputA>("node_y_topic",
        rclcpp::DefaultQoS())}
  {}

private:
  bool execute_impl() override
  {
    auto output_y{m_publisher_y->borrow_loaned_message()};
    m_publisher_y->publish(std::move(output_y));
    return true;
  }

  subscription_list get_triggering_subscriptions_impl() const override
  {
    return {m_subscription_x};
  }

  const Publisher<OutputY>::SharedPtr m_publisher_y;
  const PollingSubscription<OutputX>::SharedPtr m_subscription_x;
};
class NodeZ : public apex_node_base
{
public:
  NodeZ()
  : apex_node_base{"node_z"},
    m_logger{&get_rclcpp_node(), "node_z"}
  {}

private:
  //lint -e{9175} example code only NOLINT
  bool execute_impl() override
  {
    APEX_INFO(m_logger, "NodeZ triggered!");
    return true;
  }

  Logger<> m_logger;
};

}  // namespace node_example
}  // namespace apex

//! [Main boilerplate]
int32_t apex_main(const int32_t argc, char ** const argv)
{
  using namespace std::chrono_literals;

  auto scoped_init = apex::scoped_init(argc, argv, false);
  const apex::interrupt_handler::installer interrupt_handler_installer{};
//! [Main boilerplate]

  apex::timer_service::steady_clock_timer_service srv;

  auto node_a = std::make_shared<apex::node_example::NodeA>();
  auto node_b = std::make_shared<apex::node_example::NodeB>();
  auto node_x = std::make_shared<apex::node_example::NodeX>();
  auto node_y = std::make_shared<apex::node_example::NodeY>();
  auto node_z = std::make_shared<apex::node_example::NodeZ>();

  //! [Execution]
  const auto executor_b = apex::executor::executor_factory::create();  // (1)!
  const auto executor_chain = apex::executor::executor_factory::create();

  (void)executor_b->add_periodic(node_b, srv, 70ms);  // (2)!
  (void)executor_chain->add_periodic(node_a, srv, 100ms);  // (3)!
  (void)executor_chain->add({node_x, node_y, node_z});  // (4)!

  //lint -e{1788} example code only NOLINT
  const apex::executor::executor_runner runner_b{*executor_b};
  //lint -e{1788} example code only NOLINT
  const apex::executor::executor_runner runner_chain{*executor_chain};
  //! [Execution]

//! [Main boilerplate]
  scoped_init.post_init();
  apex::interrupt_handler::wait();
  runner_b.stop();
  runner_chain.stop();
  return 0;
}
//! [Main boilerplate]
