/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief The goal of this example is to show how much time it takes to
/// initialize Apex.OS context

#include "apex_init/apex_init.hpp"
#include "apex_init/apex_main.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "timer_service/clock_timer_service.hpp"
#include "logging/logging_macros.hpp"
#include "rcutils/cmdline_parser.h"

#include <chrono>
#include <memory>
#include <vector>

#define ONE_KB (1024U)
#define TEN_KB (10U * ONE_KB)
#define HUN_KB (10U * TEN_KB)
#define ONE_MB (ONE_KB * ONE_KB)

using namespace std::chrono_literals;

namespace
{

typedef struct lat_tmp_st_t
{
  char a[HUN_KB];  /*lint !e754*/
  uint32_t b[HUN_KB];  /*lint !e754*/
} lat_tmp_st;  /*lint !e9416*/

class ApexNodeLat : public apex::executor::apex_node_base
{
public:
  ApexNodeLat(const char * const node_name, const uint32_t num_allocs)
  : apex_node_base(node_name), m_num_allocs(num_allocs)
  {
    for (uint32_t i = 0U; i < m_num_allocs; i++) {
      m_tmp_addr = malloc(sizeof(lat_tmp_st));  /*lint !e586 */
      if (nullptr == m_tmp_addr) {
        throw std::runtime_error("Cannot allocate memory");
      }
    }
  }

private:
  bool execute_impl() override
  {
    // This free does not free all allocations. Just to make cppcheck happy
    free(m_tmp_addr); /*lint !e586 */
    return true;
  }

  size_t m_num_allocs;
  void * m_tmp_addr;
};

}  // anonymous namespace

int32_t apex_main(const int32_t argc, char ** const argv)
{
  size_t num_nodes = 1U;
  size_t tot_mem_sz = 10U * ONE_MB;
  int32_t num_args = 1;

  auto scoped_init = apex::scoped_init(argc, argv, false);

  static const char * const help_string =
    "Usage:\n"
    "apex_node_latency <options>\n"
    "Options:\n"
    "--help : print this help text and exit\n"
    "--num_nodes : number of nodes to be created (max 1000)\n"
    "--mem_to_alloc : size of memory(in MB) to be allocated\n"
    "\n";
  // Number of nodes
  const char * arg = rcutils_cli_get_option(argv, &argv[argc], "--num_nodes");
  if (nullptr != arg) {
    num_nodes = std::stoul(arg);
    num_args += 2;
    assert(0 < num_nodes && num_nodes <= 1000);
  }

  // Node memory size
  arg = rcutils_cli_get_option(argv, &argv[argc], "--mem_to_alloc");
  if (nullptr != arg) {
    tot_mem_sz = (std::stoul(arg) * ONE_MB);
    num_args += 2;
  }

  if (rcutils_cli_option_exist(argv, &argv[argc], "--help")) {
    std::cout << help_string;
    return 0;
  }

  // Check for the correctness of input arg names
  if (argc != num_args) {
    std::cout << help_string;
    return 0;
  }

  const auto node_mem_sz = tot_mem_sz / num_nodes;
  std::cout << "Num Nodes: " << num_nodes << "\nMem sz: " << tot_mem_sz << "\n";
  std::cout << "Mem sz per node: " << node_mem_sz << "\n";

  if (node_mem_sz < sizeof(lat_tmp_st)) {
    std::cout << "Memory size per node should be greater than " << sizeof(lat_tmp_st) << " bytes\n";
    return 0;
  }
  const uint32_t num_allocs = static_cast<uint32_t>(node_mem_sz / sizeof(lat_tmp_st));
  std::cout << num_allocs << " allocations of size " << sizeof(lat_tmp_st) << " per node\n";

  const auto start_tm = std::chrono::steady_clock::now();

  const std::vector<apex::executor::executable_item_ptr> nodes {num_nodes,
    std::make_shared<ApexNodeLat>("apex_node_latency", num_allocs)};
  scoped_init.post_init();
  // Note: If we need to calculate the exact latency, need to put a hook in the
  // threading framework to get the time just before entering the while loop in
  // thread_function()
  const auto end_tm = std::chrono::steady_clock::now();
  const auto startup_lat =
    std::chrono::duration_cast<std::chrono::milliseconds>(end_tm - start_tm);
  std::cout << "\nLatency is: " << startup_lat.count() << "ms\n";
  auto exec = apex::executor::executor_factory::create();
  apex::timer_service::steady_clock_timer_service timer_srv;
  (void)exec->add_periodic(nodes, timer_srv, 100ms);
  // Run the nodes to free memory
  exec->run(apex::executor::InfiniteWait, 1U);
  return 0;
}
