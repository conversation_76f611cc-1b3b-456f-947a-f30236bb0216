/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \include apex_udp.cpp
/// \brief Standalone example for UDP sender and receiver.

#include "apexcpp/apexcpp.hpp"
#include "apex_init/apex_init.hpp"
#include "apex_init/apex_main.hpp"
#include "cpputils/common_exceptions.hpp"
#include "rclcpp/rclcpp.hpp"

#include <valarray>
#include <string>
#include <array>
#include <cstdint>

namespace
{
//! [UDP run sender]
void run_sender(const apex::string_strict16_t & ip4_address, const uint32_t port)
{
  (void)printf("Sending UDP messages %s:%u\n", ip4_address.c_str(), port);
  const apex::networking::udp::UdpSender<apex::string_strict64_t>
  udp_sender(ip4_address.c_str(), uint16_t(port));
  for (uint32_t i = 0U; rclcpp::ok(); ++i) {
    apex::string_strict32_t str_to_send = apex::string_strict16_t::to_string(i);
    str_to_send += " to port ";
    str_to_send += apex::string_strict16_t::to_string(port);
    (void)printf("Sending \"%s\"\n", str_to_send.c_str());
    const apex_ret_t retval = udp_sender.send(str_to_send);
    if (retval != APEX_RET_OK) {
      (void)printf("Send failed with error %d\n", retval);
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    (void)fflush(stdout);
  }
}
//! [UDP run sender]

//! [UDP run receiver]
void run_receiver(const apex::string_strict16_t & ip4_address, const uint32_t port)
{
  (void)printf("Receiving UDP messages %s:%u\n", ip4_address.c_str(), port);
  const apex::networking::udp::UdpReceiver<apex::string_strict64_t>
  udp_receiver(ip4_address.c_str(), uint16_t(port));
  for (; rclcpp::ok(); ) {
    apex::string_strict64_t received_str;
    const int32_t timeout_ms = 2000;
    const apex_ret_t retval = udp_receiver.receive_ms(received_str, timeout_ms);
    if (retval == APEX_RET_OK) {
      (void)printf("Received: \"%s\"\n", received_str.c_str());
    } else if (retval == APEX_RET_TIMEOUT) {
      (void)printf("Timeout: no data in %d milliseconds\n", timeout_ms);
    } else {
      (void)printf("Receive failed with error %d\n", retval);
    }
    (void)fflush(stdout);
  }
}
//! [UDP run receiver]

}  // anonymous namespace


int32_t apex_main(const int32_t argc, char ** const argv)
{
  auto scoped_init = apex::scoped_init(argc, argv);
  scoped_init.post_init();
  const std::string send_option = "--send";
  const std::string rcv_option = "--receive";
  const std::string ip4_option = "--ip4:";
  const std::string port_option = "--p:";
  const apex::string_strict16_t default_ip4 = "127.0.0.1";
  const uint32_t default_port = 2370U;

  const std::valarray<const char *> args(argv, static_cast<uint32_t>(argc));

  const auto print_help = [&]()
    {
      (void)printf("Usage: apex_udp (%s|%s) [%s<addr=%s>] [%s<port=%u>]\n",
        send_option.c_str(),
        rcv_option.c_str(),
        ip4_option.c_str(),
        default_ip4.c_str(),
        port_option.c_str(),
        default_port);
    };

  if (args.size() < 2U) {
    print_help();
  } else {
    bool is_send = false;
    bool is_rcv = false;
    apex::string_strict16_t ip4 = default_ip4;
    uint32_t port = default_port;

    for (size_t i = 1U; i < args.size(); ++i) {
      const std::string argi(args[i]);
      if (send_option == argi) {
        is_send = true;
      } else if (rcv_option == argi) {
        is_rcv = true;
      } else if (ip4_option == argi.substr(0U, ip4_option.size())) {
        ip4 = argi.substr(ip4_option.size()).c_str();
      } else if (port_option == argi.substr(0U, port_option.size())) {
        port = static_cast<uint32_t>(std::stoi(argi.substr(port_option.size())));
      } else {
        // Ignore unknown, possibly ROS-related command line parameter
      }
    }

    if ((!is_send) && ((!is_rcv))) {
      throw apex::runtime_error("No `-send` and no `-receive` parameter. Nothing to do.");
    }

    if (is_send) {
      run_sender(ip4, port);
    } else {
      run_receiver(ip4, port);
    }
  }
  return 0;
}
