/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \include apex_tf.cpp
/// \brief Example usage of Apex.OS ApexTf implementation

#include "apex_init/apex_init.hpp"
#include "tf2/apex_tf.hpp"
#include "geometry_msgs/msg/transform.hpp"
#include "logging/logging_macros.hpp"
#include "std_msgs/msg/header.hpp"

#include <memory>
#include <thread>

namespace
{
// A simple object that we can pretend is rotation invariant
struct Foo
{
  std_msgs::msg::Header header;
  float x;
  float y;
};
}  // namespace

namespace apex
{
namespace tf
{
namespace message_adapters
{
//lint -e{1576} Possible false positive, transform is not defined in apex_tf, just declared NOLINT
template<>
Foo transform(Foo msg, const tf2::TransformStampedStatic & tf)  // NOLINT not std::transform
{
  msg.x += static_cast<float>(tf.transform.translation.x);
  msg.y += static_cast<float>(tf.transform.translation.y);
  return msg;
}
}  // namespace message_adapters
}  // namespace tf
}  // namespace apex

int32_t apex_main(const int32_t argc, char ** const argv)
{
  using tf2_msgs::msg::TFMessage;

  // initializes Apex.OS framework
  auto scoped_init = apex::scoped_init(argc, argv);

  // misc stuff needed for publishing a transform
  const rclcpp::Node::SharedPtr nd_ptr = std::make_shared<rclcpp::Node>("example_tf_node");

  geometry_msgs::msg::TransformStamped tf;
  tf.header.stamp = apex::to_msg_time(apex::system_clock::now());
  tf.header.frame_id = "frame_to";
  tf.child_frame_id = "frame_from";
  tf.transform.translation.x = 1.0;
  tf.transform.translation.y = -2.0;
  tf.transform.rotation.w = 1.0;

  // everything else is 0
  TFMessage tfs;
  tfs.transforms.push_back(tf);

  // instantiate the frame graph with the frame IDs
  apex::tf::Tf my_tf_handle(
    nd_ptr->create_polling_subscription<TFMessage>("tf_static", rmw_qos_profile_default.depth),
    nd_ptr->create_polling_subscription<TFMessage>("tf", rmw_qos_profile_default.depth),
    {},
    {"frame_from", "frame_to"},
    nullptr,
    nd_ptr->create_publisher<TFMessage>("tf", rmw_qos_profile_default.depth));

  // transform not available yet
  apex::tf::CanTransformResult can_transform =
    my_tf_handle.can_transform("frame_to", "frame_from");
  APEX_INFO_R("can_transform:", apex::tf::successfully(can_transform));

  scoped_init.post_init();

  // broadcast a transform
  my_tf_handle.broadcast_transforms(tfs);
  // wait a bit
  std::this_thread::sleep_for(std::chrono::seconds(2LL));
  // update the frame graph
  (void) my_tf_handle.update();

  // now the handle should contain the transform
  const apex::optional<geometry_msgs::msg::Transform> maybe_tf =
    my_tf_handle.lookup_transform("frame_to", "frame_from");
  if (!maybe_tf) {
    throw apex::runtime_error("lookup transform failed");
  }
  const auto & tf1 = *maybe_tf;
  APEX_INFO_R("lookup transform succeeded");
  APEX_INFO_R("tf1 dx", static_cast<float>(tf1.translation.x));
  APEX_INFO_R("tf1 dy", static_cast<float>(tf1.translation.y));
  APEX_INFO_R("tf1 dz", static_cast<float>(tf1.translation.z));
  APEX_INFO_R("tf1 w", static_cast<float>(tf1.rotation.w));
  APEX_INFO_R("tf1 x", static_cast<float>(tf1.rotation.x));
  APEX_INFO_R("tf1 y", static_cast<float>(tf1.rotation.y));
  APEX_INFO_R("tf1 z", static_cast<float>(tf1.rotation.z));

  // and you can transform using your message adapter
  std_msgs::msg::Header header;
  header.frame_id = "frame_from";
  const Foo f0{header, 0.0F, 0.0F};
  APEX_INFO_R("f0 x", f0.x);
  APEX_INFO_R("f0 y", f0.y);
  const apex::optional<Foo> f1 = my_tf_handle.transform(f0, "frame_to");
  APEX_INFO_R("f1 x", f1->x);
  APEX_INFO_R("f1 y", f1->y);

  // the message header is updated
  APEX_INFO_R("target frame", f1->header.frame_id);
  return 0;
}
