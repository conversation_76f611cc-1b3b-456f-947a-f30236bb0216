/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Example on how to use the Apex.OS waitset

#include "rclcpp/rclcpp.hpp"
#include "rclcpp/dynamic_waitset/waitset.hpp"
#include "rclcpp/timeout_error.hpp"
#include "apex_init/apex_init.hpp"
#include "apex_init/apex_main.hpp"
#include "std_msgs/msg/string.hpp"
#include "logging/logging_macros.hpp"


int32_t apex_main(const int32_t argc, char ** const argv)
{
  // Issue: #2103
  /*lint -e{9144} */
  using namespace std::chrono_literals;

  auto scoped_init = apex::scoped_init(argc, argv);

  rclcpp::Node node("WaitSetExampleMain");
  using std_msgs::msg::String;

  auto sub1 =
    node.create_polling_subscription<String>("a11", rclcpp::DefaultQoS());
  auto sub2 =
    node.create_polling_subscription<String>("a22", rclcpp::DefaultQoS());
  auto sub3 =
    node.create_polling_subscription<String>("a33", rclcpp::DefaultQoS());

  String msg1;
  msg1.data = "2355";

  String msg2;
  msg2.data = "34623";

  String msg3;
  msg3.data = "345211";

  //! [create_waitset]
  rclcpp::dynamic_waitset::Waitset ws({sub1, sub2, sub3});
  //! [create_waitset]

  rclcpp::Node thread_node("thread");

  const auto pub1 =
    thread_node.create_publisher<String>("a11", rclcpp::DefaultQoS());
  const auto pub2 =
    thread_node.create_publisher<String>("a22", rclcpp::DefaultQoS());
  const auto pub3 =
    thread_node.create_publisher<String>("a33", rclcpp::DefaultQoS());

  scoped_init.post_init();

  pub1->wait_for_matched(1U, std::chrono::milliseconds(500U));
  pub2->wait_for_matched(1U, std::chrono::milliseconds(500U));
  pub3->wait_for_matched(1U, std::chrono::milliseconds(500U));

  sub1->wait_for_matched(1U, std::chrono::milliseconds(500U));
  sub2->wait_for_matched(1U, std::chrono::milliseconds(500U));
  sub3->wait_for_matched(1U, std::chrono::milliseconds(500U));

  auto thread = std::thread([msg1, msg2, msg3, pub1, pub2, pub3]() {
        pub1->publish(msg1);
        APEX_INFO_R("Publishing msg1:", msg1.data);
        pub2->publish(msg2);
        APEX_INFO_R("Publishing msg2:", msg2.data);
        pub3->publish(msg3);
        APEX_INFO_R("Publishing msg3:", msg3.data);
      });

  //! [ReadTakeExample]
  auto num_recv = std::size_t();
  try {
    while (num_recv < 3U) {
      //! [waitset_wait]
      // Waiting up to 5s for a sample to arrive.
      if (!ws.wait(5s)) {
        throw rclcpp::TimeoutError{};
      }

      if (ws[sub1]) {  // (1)!
        const auto recv1 = sub1->take(1);
        for (const auto msg : recv1) {
          if (msg.info().valid()) {
            APEX_INFO_R("msg1 data:", msg.data().data);
            //! [waitset_wait]
            ++num_recv;
            //! [waitset_wait]
          }
        }
      }
      //! [waitset_wait]

      if (ws[sub2]) {
        const auto recv2 = sub2->take(1);
        for (const auto msg : recv2) {
          if (msg.info().valid()) {
            APEX_INFO_R("msg2 data:", msg.data().data);
            ++num_recv;
          }
        }
      }

      if (ws[sub3]) {
        const auto recv3 = sub3->take(1);
        for (const auto msg : recv3) {
          if (msg.info().valid()) {
            APEX_INFO_R("msg3 data:", msg.data().data);
            ++num_recv;
          }
        }
      }
      APEX_INFO_R("Number of messages already received:", num_recv, "of 3");
    }

    APEX_INFO_R("Got all messages!");
  } catch (rclcpp::TimeoutError &) {
    throw apex::runtime_error("No message received after 5s");
  }
  //! [ReadTakeExample]
  thread.join();
  return 0;
}
