load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test")
load("//common/bazel/rules_deployment:defs.bzl", "executables_collection")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "apex_os_examples_pkg",
    description = "Collection of examples for Apex.OS.",
    lib_executables = [
        "apex_node_example",
    ] + select({
        "//common/asil:qm": [
            "apex_rt_ex_exe",
            "apex_string_exe",
            "apex_tf_exe",
            "apex_udp_exe",
            "waitset_example",
            "apex_measure_startup_time_exe",
            "ring_buffer_demo",
        ],
        "//conditions:default": [],
    }),
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "apex_os_examples",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "apex_rt_ex_exe",
    srcs = ["src/apex_rt_ex.cpp"],
    tags = [
        "exclude_sca",
        "integrity QM",
    ],
    deps = [
        "//grace/execution/apex_init",
        "//grace/execution/apex_init:apex_main",
    ],
)

cc_binary(
    name = "apex_string_exe",
    srcs = ["src/apex_string.cpp"],
    tags = [
        "exclude_sca",
        "integrity QM",
    ],
    deps = [
        "//grace/execution/apex_init",
        "//grace/execution/apex_init:apex_main",
    ],
)

cc_binary(
    name = "apex_tf_exe",
    srcs = ["src/apex_tf.cpp"],
    tags = ["exclude_sca"],
    target_compatible_with = select({
        "@platforms//os:qnx": ["@platforms//:incompatible"],
        "//conditions:default": [],
    }),
    deps = [
        "//grace/execution/apex_init",
        "//grace/execution/apex_init:apex_main",
        "@ros2.geometry2//tf2",
    ],
)

cc_binary(
    name = "apex_udp_exe",
    srcs = ["src/apex_udp.cpp"],
    tags = [
        "exclude_sca",
        "integrity QM",
    ],
    deps = [
        "//grace/execution/apex_init",
        "//grace/execution/apex_init:apex_main",
        "//grace/utils/apexcpp",
    ],
)

cc_binary(
    name = "waitset_example",
    srcs = ["src/waitset_example_main.cpp"],
    tags = [
        "exclude_sca",
        "integrity QM",
    ],
    deps = [
        "//grace/execution/apex_init",
        "//grace/execution/apex_init:apex_main",
        "//grace/interfaces/std_msgs",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "apex_node_example",
    srcs = ["src/apex_node.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/apex_init:apex_main",
        "//grace/execution/executor2",
    ],
)

cc_binary(
    name = "apex_measure_startup_time_exe",
    srcs = [
        "src/apex_measure_startup_time.cpp",
    ],
    tags = [
        "exclude_sca",
        "integrity QM",
    ],
    deps = [
        "//grace/execution/apex_init",
        "//grace/execution/apex_init:apex_main",
        "//grace/execution/executor2",
    ],
)

cc_binary(
    name = "ring_buffer_demo",
    srcs = [
        "src/ring_buffer_demo.cpp",
    ],
    tags = [
        "exclude_sca",
        "integrity QM",
    ],
    deps = [
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/apex_init:apex_main",
        "//grace/execution/executor2",
    ],
)

cc_library(
    name = "message_logging_latency",
    srcs = [
        "src/message_logging_latency.cpp",
    ],
    tags = [
        "exclude_sca",
        "integrity QM",
    ],
    deps = [
        "//grace/execution/executor2",
        "//grace/monitoring/logging",
    ],
)

# TODO(carlos) Migrate test to os_verify_docs once that's bazelized (&356, #17999, #18249, #21244)
# test/transform_data_coordinate_frames.doctest.py
# test/log_writer.doctest.py

executables_collection(
    name = "test_integration_udp_executables",
    executables = [
        ":apex_udp_exe",
    ],
)

launch_test(
    name = "test_integration_udp_example",
    data = [":test_integration_udp_executables"],
    launch_test_file = "test/udp_example.test.py",
    tags = [
        "constrained_test",
        "no-remote",
    ],
    deps = [
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//tools/apex_pytest_utils",
        "@apex//tools/launch/launch",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "src/apex_node.cpp",
        "src/message_logging_latency.cpp",
    ],
    visibility = [
        "//common/interrupt/design:__pkg__",
        "//grace/doc:__subpackages__",
        "//grace/utils/apexcpp/doc:__pkg__",
    ],
)
