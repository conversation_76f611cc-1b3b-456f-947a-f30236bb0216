# Transforming data between coordinate frames

## Overview

Robotic systems typically involve many coordinate frames, each of which is
moving with respect to one another. Often times, data is generated in one
coordinate frame, but needs to be represented in another. This necessitates
a consistent system for transforming data between coordinate frames using the
most up-to-date and accurate transforms.

The [apex_tf](apex-tf-design.md)
package is provided to keep transforms between coordinate frames up-to-date
and query transforms at a given time.

Currently, `apex_tf` uses a static and MISRA compliant version of
[tf2](https://github.com/ros2/geometry2/tree/ros2/tf2) as the underlying frame-graph
implementation.

## Quick reference

- A transform represents a rigid body translation and rotation from the
  `TransformStamped::child_frame_id` coordinate frame to the
  `TransformStamped::Header::frame_id` coordinate frame
- The ROS convention is to publish arrays of transforms on the `/tf` and
  `/tf_static` topics
- Apex.Grace provides the [](apex::tf::Tf) class to listen to
  transforms on the `/tf` topic, provide interpolation, and transform methods

For more details, see the [ROS TF2 tutorials](http://wiki.ros.org/tf2/Tutorials).

For more details on `apex_tf`, see the [design
document](apex-tf-design.md).

## The transform message

The `geometry_msgs::msg::TransformStamped` message, an instance of which will be
referred to as `msg`, consists of the following components:

- `msg.header.frame_id`: name of the coordinate frame, *to* which the transform
  acts
- `msg.child_frame_id`: name of the coordinate frame, *from* which the transform
  acts
- `msg.transform.translation`: linear translation vector of the transform
- `msg.transform.rotation`: rotation, represented as a quaternion

## The transform topics: /tf and /tf_static

By convention, systems built on ROS typically publish to two transform topics,
`/tf` and `/tf_static`.

These topics are `tf2_msgs::msg::TFMessage` messages, which are arrays of
`geometry_msgs::msg::TransformStamped` messages. These topics can either be fed
directly into a `tf2::BufferCore` or `tf2_ros::Buffer` object for interpolation
and chaining of transforms, or directly subscribed to and parsed for the
transform of interest.

Another standard convention is that individual transforms are typically composed
into their own `TFMessage` and published onto the `/tf` topic.

See the [ROS 2
`robot_state_publisher`](https://github.com/ros/robot_state_publisher/blob/772a88a4c8cf973bf4bcf36276db32b2c75a8113/src/robot_state_publisher.cpp#L140-L146)
for an example of how to publish a transform.

## Transforming data

On construction, [](apex::tf::Tf) accepts a subscription to
the transform topic for updates.

The core API has routines to check if a transform exists, to get the raw
transform between coordinate frames, and directly transform an object.
In addition, functionality exists to deterministically select a window of
transforms according to a timestamp.

Note that using an invalid time as the timestamp will result in the latest
transform being output, if it is available.

### Using raw transforms

To transform data, the desired transform may be harvested directly from the
relevant topic, commonly implemented as a quaternion and a translation vector.
For more details on using quaternions, refer to [this external
reference](https://en.wikipedia.org/wiki/Quaternions_and_spatial_rotation).

### Adding message transforms

To allow the [](apex::tf::ApexTf::transform) method to work for some
new message type, specializing the
[](apex::tf::message_adapters::transform) method is required.

Below is an example of specializing for a `DummyStampedMessage` message type with a header and
two `double` members:

{{ code_snippet("grace/rviz2_utils/geometry2/tf2/test/test_apex_tf.cpp",
     {"tag": "//! [custom message specialization]"}) }}

### Interpolation policies

Currently the interpolation policy handle has no meaningful implementations. It
will eventually be used for more exotic interpolation mechanisms for finding
transforms between discrete frames, and can control how much, if any,
extrapolation is done.

## Full example

A full working transform example is found in the `apex_tf.cpp` file under `src/` in the
`apex_os_examples` package.

Run the following commands:

{{ source_admonition("ApexGraceBilbo") }}

<!-- LAUNCH_STEP: apex_tf-->
```shell ade
ros2 run apex_os_examples apex_tf_exe
```

<!-- Verifying the output is not needed since the package is tested in many places -->
This should result in an output that looks like the following:

??? example "Terminal output"
    <!-- markdownlint-disable MD038 MD013 -->
    ```log
    [2022-05-18 16:03:57] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/execution/apex_init/src/apex_init.cpp @  L239:
    Initializing root logger.
    [2022-05-18 16:03:57] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L82:
    can_transform: false
    [2022-05-18 16:03:59] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L105:
    lookup transform succeeded
    [2022-05-18 16:03:59] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L106:
    tf1 dx 1.0e+0
    [2022-05-18 16:03:59] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L107:
    tf1 dy -2.0e+0
    [2022-05-18 16:03:59] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L108:
    tf1 dz 0
    [2022-05-18 16:03:59] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L109:
    tf1 w 1.0e+0
    [2022-05-18 16:03:59] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L110:
    tf1 x 0
    [2022-05-18 16:03:59] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L111:
    tf1 y 0
    [2022-05-18 16:03:59] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L112:
    tf1 z 0
    [2022-05-18 16:03:59] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L119:
    f0 x 0
    [2022-05-18 16:03:59] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L120:
    f0 y 0
    [2022-05-18 16:03:59] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L122:
    f1 x 1.0e+0
    [2022-05-18 16:03:59] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L123:
    f1 y -2.0e+0
    [2022-05-18 16:03:59] [INFO] root_logger_node | /builds/ApexAI/grand_central/apex_ws/src/grace/examples/apex_os_examples/src/apex_tf.cpp @  L126:
    target frame frame_to
    ```
    <!-- DOCUMENTATION_TEST: transform_data_coordinate_frames.doctest.py -->
    <!-- markdownlint restore -->
