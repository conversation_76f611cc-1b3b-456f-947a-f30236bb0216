# Copyright 2019 Apex.AI, Inc.
# All rights reserved.

import unittest

from launch_testing import LaunchDescription
from launch.actions import Register<PERSON>vent<PERSON>andler
from launch.actions import OpaqueFunction
from launch_ros.actions import Node

from launch_testing import ReadyAggregator
from launch_testing import post_shutdown_test
from launch_testing.event_handlers import StdoutReadyListener
from launch_testing.asserts import assertExitCodes, EXIT_OK

from apex_pytest_utils import APEX_SKIP_TEST_IF
from rclpy.utilities import get_rmw_implementation_identifier


def generate_test_description(ready_fn):
    # This test checks two processes:  apex_udp_exe --send and apex_udp_exe --receive
    # We want to see that they launch, and can successfully send data from sender to
    # receiver
    both_ready = ReadyAggregator(ready_fn, 2)
    dut_rx = Node(
        executable='apex_udp_exe',
        arguments=['--receive']
    )

    dut_tx = Node(
        executable='apex_udp_exe',
        arguments=['--send']
    )

    return LaunchDescription([
        dut_rx,

        # Delay the start of the 'send' node until we see 'Receiving UDP messages' in stdout
        RegisterEventHandler(
            StdoutReadyListener(
                target_action=dut_rx,
                ready_txt="Receiving UDP messages",
                actions=[
                    # Start the sender node now that we're receiving
                    dut_tx,
                    # Also signal that one of our two nodes has started up
                    OpaqueFunction(function=lambda contexted: both_ready.ready_fn())
                ]
            )
        ),
        # Once the sender node is started up, signal a 2nd time to the both_ready aggregator
        RegisterEventHandler(
            StdoutReadyListener(
                target_action=dut_tx,
                ready_txt="Sending UDP messages",
                actions=[
                    OpaqueFunction(function=lambda context: both_ready.ready_fn())
                ]
            )
        ),
    ])


class TestUdpRuns(unittest.TestCase):

    def test_run_for_time(self, proc_output):
        # Allow the test to run until the we see 'Received: "3' in the output
        proc_output.assertWaitFor('Received: "3', timeout=10, stream='stdout')


@APEX_SKIP_TEST_IF(
    34242,
    "The signal handler is causing static order initialization fiasco",
    get_rmw_implementation_identifier() == "rmw_ida",
)
@post_shutdown_test()
class TestCommunication(unittest.TestCase):

    def test_process_exit_codes(self, proc_info):
        # Checks that all processes exited cleanly.
        assertExitCodes(proc_info, [EXIT_OK])
