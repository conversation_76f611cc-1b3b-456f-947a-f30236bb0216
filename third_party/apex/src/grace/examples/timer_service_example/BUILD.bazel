load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "timer_service_examples_pkg",
    description = "Package examples of usage of timer service",
    lib_executables = [
        "timer_service_with_replay",
        "timeout_timer",
        "minimal_example",
        "timer_subscription",
    ],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "timer_service_examples_pkg",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils:cpputils_pkg",
        "//common/interrupt:interrupt_pkg",
        "//common/threading:threading_pkg",
        "//grace/execution/apex_init:apex_init_pkg",
        "//grace/execution/executor2:executor2_pkg",
        "//grace/execution/timer_service:timer_service_pkg",
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/recording/replay:replay_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
    ],
)

cc_binary(
    name = "minimal_example",
    srcs = ["src/timer_service_examples/minimal_example.cpp"],
    deps = [
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "timer_service_with_replay",
    srcs = ["src/timer_service_examples/timer_service_with_replay.cpp"],
    deps = [
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/recording/replay",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "timeout_timer",
    srcs = ["src/timer_service_examples/timeout_timer.cpp"],
    deps = [
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "timer_subscription",
    srcs = ["src/timer_service_examples/timer_subscription.cpp"],
    deps = [
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)
