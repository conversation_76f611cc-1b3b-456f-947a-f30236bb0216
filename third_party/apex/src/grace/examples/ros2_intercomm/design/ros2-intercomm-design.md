# ros2_intercomm

## Purpose

This collection of packages provides simple applications that can be used to set up an
integration test to verify that communication between Apex.Grace and ROS 2 is functional.

## Design

1. Packages here include simple pub-sub amd request-reply applications that exchange messages
   supported by Apex.Grace and ROS 2.
1. Message IDL files must be compatible with both Grace, and ROS 2 IDL frontents. This means, it is
   best to only use those from ROS 2 packages `std_msgs` and `std_srvs`.
1. The messages are serialized to YAML before being sent and after being received
1. The serialzed YAML is compared to verify:
1. All messages have been received
1. The YAML representation of the received content matches the YAML representation of the
     sent content
1. The applications are separated into Apex.Grace and ROS 2 packages to ensure that ROS 2 components
   will be compilable using only ROS 2 packages
1. `intercomm_apex` contains components that should be compiled using Apex.Grace
1. `intercomm_ros2` contains the components that should be compiled using ROS 2
1. `intercomm_test` contains scripts for validating the YAML representations of the messages
1. `ros2_grace_intercomm` package contains test applications which can be built against both
   Grace, and ROS 2 - i.e. they should be compatible in terms of used APIs (including IDLs) and
   communication.

## Usage

1. Unless already done, create virtual network for the test containers

    shell
    ```
    docker network create ade_net
    ```

1. Compile `intercomm_apex` in an Apex.Grace environment (ideally using `ade`)

    ```shell ade
    cd grand_central/apex_ws/
    source /opt/ApexGraceBilbo/setup.bash
    export COLCON_HOME=/usr/local/share/colcon/colcon-home/development/
    colcon build --packages-select intercomm_apex
    ```

1. Compile `intercomm_ros2` in a ROS 2 environment (ideally using `ade`)

    ```shell dollar title="Setup a ROS 2 environment"
    git clone https://gitlab.com/ApexAI/ros2-ade.git
    cd ros2-ade
    ADE_NAME=galactic ade --rc .aderc-galactic start --enter -- --net=ade_net
    ```


    ```shell prefix="(galactic)$" title="Fetch introspection packages for ROS 2"
    cd ~
    git clone https://github.com/osrf/dynamic_message_introspection.git
    ```

    ```shell prefix="(galactic)$" title="Set up the ROS 2 workspace"
    cd ~
    mkdir -p ~/ws_galactic/src
    cd ~/ws_galactic/src
    ln -s ~/dynamic_message_introspection/
    ```

    ```shell prefix="(galactic)$" title="The required packages should be linked from grand_central"
    ln -s ~/grand_central/apex_ws/src/tools/apex_cmake
    ln -s ~/grand_central/apex_ws/src/grace/interfaces/test_interface_files
    ln -s ~/grand_central/apex_ws/src/grace/interfaces/test_msgs
    ln -s ~/grand_central/apex_ws/src/grace/examples/ros2_intercomm/intercomm_ros2/
    ```

    WARN: `test_msgs` is currently incompatible with ROS 2 IDL compiler (#32697).
    NOTE: No need to symlink `std_msgs` and `std_srvs` as those should be in the underlay.

    ```shell prefix="(galactic)$" title="The packages can be built using purely ROS 2 libraries"
    source /opt/ros/galactic/setup.bash
    colcon build
    ```

2. Start the subscriber application

    ```shell prefix="(galactic)$"
    cd ~/ws_galactic/src
    source install/setup.bash
    ros2 run intercomm_ros2 ros_subscriber
    ```

3. Start the publisher application, specifying a run time via `--apex-settings-file`

    ```shell ade
    cd ~/grand_central/apex_ws/
    echo "runtime_ms: 5000" > intercomm.param.yaml
    source install/setup.bash
    ros2 run intercomm_apex apex_publisher --apex-settings-file intercomm.param.yaml
    ```

4. The publisher will send messages once per second until the run time is elapsed, then exit
5. The subscriber will exit after it stops receiving messages for some time
6. The applications will write messages sent/received to yaml files
7. The provided script can be used to verify the communication was successful

    ```shell ade
    (ade)$ python3 verify_intercomm.py basic_types.sent.yml basic_types.recv.yml
      Success!
    ```

## Future Extensions / Unimplemented Parts

1. Add applications to test ROS 2 -> Apex.Grace communication
1. Add topics with unbounded sequences
