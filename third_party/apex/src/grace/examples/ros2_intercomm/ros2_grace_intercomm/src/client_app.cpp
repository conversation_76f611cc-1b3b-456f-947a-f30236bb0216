// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#include "rclcpp/exceptions.hpp"
#include "rclcpp/rclcpp.hpp"
#include <std_srvs/srv/set_bool.hpp>


void print_current_node_graph(rclcpp::Node::SharedPtr node)
{
  auto graph_api = node->get_node_graph_interface();

  auto nodes_and_namespaces = graph_api->get_node_names_and_namespaces();

  std::cout << "== GRAPH ==\n";
  for (const auto & [node, node_namespace] : nodes_and_namespaces) {
    std::cout << "Node: '" << node_namespace << "/" << node << "'\n";
    {
      const auto node_pubs_and_types =
        graph_api->get_publisher_names_and_types_by_node(node, node_namespace);

      for (const auto & [topic, types] : node_pubs_and_types) {
        std::cout << "- publisher: " << topic << ":" << types[0] << "\n";
      }
    }

    {
      const auto node_subs_and_types =
        graph_api->get_subscriber_names_and_types_by_node(node, node_namespace);

      for (const auto & [topic, types] : node_subs_and_types) {
        std::cout << "- subscriber: " << topic << ":" << types[0] << "\n";
      }
    }

    {
      const auto svcs_and_types =
        graph_api->get_service_names_and_types_by_node(node, node_namespace);

      for (const auto & [topic, types] : svcs_and_types) {
        std::cout << "- service: " << topic << ":" << types[0] << "\n";
      }
    }
  }
  std::cout << "== GRAPH END ==\n";
}

int main(int argc, char * argv[])
{
  using namespace std::chrono_literals;

  // == Init ==
  rclcpp::init(argc, argv);

  // ===== Create server node
  using SetBoolService = std_srvs::srv::SetBool;
  auto client_node = std::make_shared<rclcpp::Node>("test_apex_client");
  auto client = client_node->create_client<SetBoolService>("test_ros2_service");

  RCLCPP_INFO(client_node->get_logger(), "Waiting for the service...");
  while (!client->wait_for_service(1s)) {
    if (!rclcpp::ok()) {
      RCLCPP_ERROR(client_node->get_logger(),
                   "Interrupted while waiting for the service. Exiting.");
      return EXIT_FAILURE;
    }
    print_current_node_graph(client_node);
  }
  RCLCPP_INFO(client_node->get_logger(), "Found service!");
  print_current_node_graph(client_node);

  constexpr size_t N_REQUESTS = 3;

  for (size_t i = 1; i <= N_REQUESTS; i++) {
    auto request = std::make_shared<SetBoolService::Request>();
    request->data = ((i % 2) == 0);

    RCLCPP_INFO_STREAM(client_node->get_logger(), "Sending request " << i << "...");
    auto reply_future = client->async_send_request(request);

    // Wait for the result.
    RCLCPP_INFO_STREAM(client_node->get_logger(), "Waiting for reply " << i << "...");
    if (rclcpp::spin_until_future_complete(client_node, reply_future, 5s) ==
        rclcpp::FutureReturnCode::SUCCESS) {
      auto & reply_ptr = reply_future.get();
      RCLCPP_INFO_STREAM(client_node->get_logger(),
                         "Got reply! Success: " << reply_ptr->success << ", message: '"
                                                << reply_ptr->message << "'");
    } else {
      RCLCPP_ERROR(client_node->get_logger(), "Failure while waiting for reply.");
    }
  }

  // ===== Shutdown
  rclcpp::shutdown();

  return EXIT_SUCCESS;
}
