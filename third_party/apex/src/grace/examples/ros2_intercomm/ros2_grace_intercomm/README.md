# ros2_grace_intercomm package

This package contains test applications which can be built against both Grace, and ROS 2.

## Building and running

Unless already done, create a virtual network for the test containers:

```shell
docker network create ade_net
```

Sharing the same network will allow the apps from separate Docker containers to communicate with
each other.

### Using Bazel

Just build and run normally. Keep in mind, that if built with Ida3, you also have to:

```shell
bazel run ida:runtime
```

### Build in a Grace `ade`

```shell
ADE_NAME=grace ade start --enter -- --net=ade_net
```

```shell grace
cd grand_central/apex_ws/
source /opt/ApexGraceBilbo/setup.bash
colcon build --packages-select ros2_grace_intercomm
...
source install/setup.bash
ros2 run ros2_grace_intercomm server_app  # or client_app
```

### Build in a ROS 2 `ade`:

```shell
git clone https://gitlab.com/ApexAI/ros2-ade.git
cd ros2-ade
ADE_NAME=galactic ade --rc .aderc-galactic start --enter -- --net=ade_net
```

```shell galactic
cd ~
mkdir -p ~/ws_galactic/src
cd ~/ws_galactic/src
ln -s ~/grand_central/apex_ws/src/tools/apex_cmake
ln -s ~/grand_central/apex_ws/src/grace/examples/ros2_intercomm/ros2_grace_intercomm/
```

NOTE: No need to symlink `std_msgs` and `std_srvs` as those should be in the underlay.

```shell galactic
source /opt/ros/galactic/setup.sh
colcon build
...
source install/setup.bash
ros2 run ros2_grace_intercomm server_app  # or client_app
```
