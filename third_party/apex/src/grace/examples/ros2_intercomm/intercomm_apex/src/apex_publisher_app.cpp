// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include <yaml-cpp/yaml.h>

#include <cstdlib>
#include <filesystem>
#include <fstream>
#include <iostream>
#include <memory>
#include <random>
#include <string>

#include "intercomm_apex/create_apex_publisher_node.hpp"
#include "intercomm_apex/topics.hpp"
#include <dynmsg/message_reading.hpp>
#include <executor2/executor_factory.hpp>
#include <executor2/executor_runner.hpp>
#include <interrupt/interrupt_handler.hpp>
#include <settings/from_yaml.hpp>
#include <settings/inspect.hpp>
#include <settings/repository.hpp>
#include <timer_service/clock_timer_service.hpp>

#include <test_msgs/msg/arrays.hpp>
#include <test_msgs/msg/basic_types.hpp>
#include <test_msgs/msg/bounded_sequences.hpp>
#include <test_msgs/msg/flat_sequences_bounded.hpp>
#include <test_msgs/msg/unbounded_sequences.hpp>

using namespace std::chrono_literals;

constexpr const char * FILE_NAME__BASIC_TYPES = "basic_types.sent.yml";
constexpr const char * FILE_NAME__FIXED_ARRAYS = "fixed_arrays.sent.yml";
constexpr const char * FILE_NAME__BOUNDED_SEQUENCES = "bounded_sequences.sent.yml";
constexpr const char * FILE_NAME__UNBOUNDED_SEQUENCES = "unbounded_sequences.sent.yml";
constexpr const char * FILE_NAME__FLATTENED_SEQUENCES = "flattened_sequences.sent.yml";

/**
 * @brief Collection of methods to generate dummy values.
 * @detail Each method provides a new pseudo-random value each time it is called.
 */
class DummyDataGenerator
{
  using BasicTypes = test_msgs::msg::BasicTypes;

public:
  bool bool_val()
  {
    return dist_uint(e) > 49;
  }
  unsigned char byte_val()
  {
    return static_cast<uint8_t>(dist_uint(e));
  }
  unsigned char char_val()
  {
    return static_cast<uint8_t>(dist_uint(e));
  }
  std::string string_val()
  {
    return generate_string(dist_string_length(e));
  }
  std::string short_string_val()
  {
    return generate_string(dist_short_string_length(e));
  }
  float float32_val()
  {
    return static_cast<float>(dist_real(e));
  }
  double float64_val()
  {
    return dist_real(e);
  }
  uint8_t uint8_val()
  {
    return static_cast<uint8_t>(dist_int(e));
  }
  uint16_t uint16_val()
  {
    return static_cast<uint16_t>(dist_int(e));
  }
  uint32_t uint32_val()
  {
    return static_cast<uint32_t>(dist_int(e));
  }
  uint64_t uint64_val()
  {
    return static_cast<uint64_t>(dist_int(e));
  }
  int8_t int8_val()
  {
    return static_cast<int8_t>(dist_int(e));
  }
  int16_t int16_val()
  {
    return static_cast<int16_t>(dist_int(e));
  }
  int32_t int32_val()
  {
    return static_cast<int32_t>(dist_int(e));
  }
  int64_t int64_val()
  {
    return dist_int(e);
  }
  size_t sequence_length()
  {
    return dist_sequence_length(e);
  }
  BasicTypes basic_types_msg()
  {
    return BasicTypes::create_msg(bool_val(),
             byte_val(),
             char_val(),
             float32_val(),
             float64_val(),
             int8_val(),
             uint8_val(),
             int16_val(),
             uint16_val(),
             int32_val(),
             uint32_val(),
             int64_val(),
             uint64_val());
  }

private:
  static std::string chars;
  std::random_device rd;
  std::default_random_engine e{rd()};
  std::uniform_int_distribution<uint64_t> dist_uint{0, 99};
  std::uniform_int_distribution<int64_t> dist_int{-99, 99};
  std::uniform_real_distribution<double> dist_real{1.0, 100.0};
  std::uniform_int_distribution<uint64_t> dist_char{0, chars.size() - 1};
  std::uniform_int_distribution<size_t> dist_string_length{0, 50};
  std::uniform_int_distribution<size_t> dist_short_string_length{0, 9};
  std::uniform_int_distribution<size_t> dist_sequence_length{0, 2};

  std::string generate_string(size_t length)
  {
    std::string str;
    for (size_t i = 0; i < length; ++i) {
      str.push_back(chars[dist_char(e)]);
    }
    return str;
  }
};

// Characters are randomly selected from this when generating a string value.
std::string DummyDataGenerator::chars{
  // NOLINT
  "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890"};  // NOLINT

int main(int argc, char * argv[])
{
  // ===== Dependencies
  using namespace std::chrono_literals;
  using apex::intercomm_apex::create_publisher_node;
  using apex::intercomm_apex::TOPIC__BASIC_TYPES;
  using apex::intercomm_apex::TOPIC__BOUNDED_SEQUENCES;
  using apex::intercomm_apex::TOPIC__FIXED_ARRAYS;
  using apex::intercomm_apex::TOPIC__FLATTENED_SEQUENCES;
  using apex::intercomm_apex::TOPIC__UNBOUNDED_SEQUENCES;

  using apex::settings::inspect::get_or_default;
  using apex::settings::inspect::integer;

  using test_msgs::msg::Arrays;
  using test_msgs::msg::BasicTypes;
  using test_msgs::msg::BoundedSequences;
  using test_msgs::msg::FlatSequencesBounded;
  using test_msgs::msg::UnboundedSequences;

  // ===== Load Settings
  apex::settings::yaml::load_repository_from_command_line(argc, argv);
  auto config = apex::settings::repository::get();
  auto runtime = get_or_default<integer>(config, "runtime_ms", 0);

  // ===== Random Generator
  DummyDataGenerator dummy{};

  // ===== File Output
  auto basic_types_file = std::ofstream{FILE_NAME__BASIC_TYPES};
  auto fixed_arrays_file = std::ofstream{FILE_NAME__FIXED_ARRAYS};
  auto bounded_sequences_file = std::ofstream{FILE_NAME__BOUNDED_SEQUENCES};
  auto unbounded_sequences_file = std::ofstream{FILE_NAME__UNBOUNDED_SEQUENCES};
  auto flattened_sequences_file = std::ofstream{FILE_NAME__FLATTENED_SEQUENCES};

  // ===== YAML Object
  YAML::Node basic_types_yaml;
  YAML::Node fixed_arrays_yaml;
  YAML::Node bounded_sequences_yaml;
  YAML::Node unbounded_sequences_yaml;
  YAML::Node flattened_sequences_yaml;

  // ===== Nodes
  rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);

  const auto basic_types_publisher = create_publisher_node<BasicTypes>(TOPIC__BASIC_TYPES, [&]() {
        auto msg = dummy.basic_types_msg();

        auto msg_yaml = dynmsg::cpp::message_to_yaml(msg);
        basic_types_yaml.push_back(msg_yaml);

        return msg;
      });

  const auto fixed_arrays_publisher = create_publisher_node<Arrays>(TOPIC__FIXED_ARRAYS, [&]() {
        Arrays msg{};

        for (size_t i = 0; i < 3; i++) {
          msg.bool_values.at(i) = dummy.bool_val();
          msg.byte_values.at(i) = dummy.byte_val();
          msg.char_values.at(i) = dummy.char_val();
          msg.float32_values.at(i) = dummy.float32_val();
          msg.float64_values.at(i) = dummy.float64_val();
          msg.int8_values.at(i) = dummy.int8_val();
          msg.uint8_values.at(i) = dummy.uint8_val();
          msg.int16_values.at(i) = dummy.int16_val();
          msg.uint16_values.at(i) = dummy.uint16_val();
          msg.int32_values.at(i) = dummy.int32_val();
          msg.uint32_values.at(i) = dummy.uint32_val();
          msg.int64_values.at(i) = dummy.int64_val();
          msg.uint64_values.at(i) = dummy.uint64_val();
          msg.string_values.at(i) = dummy.string_val();
          msg.basic_types_values.at(i) = dummy.basic_types_msg();
        }

        auto msg_yaml = dynmsg::cpp::message_to_yaml(msg);
        fixed_arrays_yaml.push_back(msg_yaml);

        return msg;
      });

  const auto bounded_sequences_publisher =
    create_publisher_node<BoundedSequences>(TOPIC__BOUNDED_SEQUENCES, [&]() {
        BoundedSequences msg{};

        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.bool_values.push_back(dummy.bool_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.byte_values.push_back(dummy.byte_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.char_values.push_back(dummy.char_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.float32_values.push_back(dummy.float32_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.float64_values.push_back(dummy.float64_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.int8_values.push_back(dummy.int8_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.uint8_values.push_back(dummy.uint8_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.int16_values.push_back(dummy.int16_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.uint16_values.push_back(dummy.uint16_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.int32_values.push_back(dummy.int32_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.uint32_values.push_back(dummy.uint32_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.int64_values.push_back(dummy.int64_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.uint64_values.push_back(dummy.uint64_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.string_values.push_back(dummy.string_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.basic_types_values.push_back(dummy.basic_types_msg());
        }

        auto msg_yaml = dynmsg::cpp::message_to_yaml(msg);
        bounded_sequences_yaml.push_back(msg_yaml);

        return msg;
      });

  const auto unbounded_sequences_publisher =
    create_publisher_node<UnboundedSequences>(TOPIC__UNBOUNDED_SEQUENCES, [&]() {
        UnboundedSequences msg{};

        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.bool_values.push_back(dummy.bool_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.byte_values.push_back(dummy.byte_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.char_values.push_back(dummy.char_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.float32_values.push_back(dummy.float32_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.float64_values.push_back(dummy.float64_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.int8_values.push_back(dummy.int8_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.uint8_values.push_back(dummy.uint8_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.int16_values.push_back(dummy.int16_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.uint16_values.push_back(dummy.uint16_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.int32_values.push_back(dummy.int32_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.uint32_values.push_back(dummy.uint32_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.int64_values.push_back(dummy.int64_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.uint64_values.push_back(dummy.uint64_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.string_values.push_back(dummy.string_val());
        }
        for (size_t i = 0; i < dummy.sequence_length(); i++) {
          msg.basic_types_values.push_back(dummy.basic_types_msg());
        }

        auto msg_yaml = dynmsg::cpp::message_to_yaml(msg);
        unbounded_sequences_yaml.push_back(msg_yaml);

        return msg;
      });

  const auto flattened_sequences_publisher =
    create_publisher_node<FlatSequencesBounded>(TOPIC__FLATTENED_SEQUENCES, [&]() {
        FlatSequencesBounded msg{};

        for (size_t i = 0; i < msg.bool_value.capacity(); i++) {
          msg.bool_value.push_back(dummy.bool_val());
        }
        for (size_t i = 0; i < msg.strings_value.capacity(); i++) {
          msg.strings_value.push_back(dummy.short_string_val());
        }
        msg.string_value = dummy.short_string_val();

        auto msg_yaml = dynmsg::cpp::message_to_yaml(msg);
        flattened_sequences_yaml.push_back(msg_yaml);

        return msg;
      });

  // ===== Wait for matching subscribers
  basic_types_publisher->wait_for_matched();
  fixed_arrays_publisher->wait_for_matched();
  bounded_sequences_publisher->wait_for_matched();
  unbounded_sequences_publisher->wait_for_matched();
  flattened_sequences_publisher->wait_for_matched();

  // ===== Execution
  const auto executor = apex::executor::executor_factory::create();
  apex::timer_service::steady_clock_timer_service timer_srv;
  executor->add_periodic(std::vector<apex::executor::executable_item_ptr>{
    basic_types_publisher,
    fixed_arrays_publisher,
    bounded_sequences_publisher,
    unbounded_sequences_publisher,
    flattened_sequences_publisher,
  }, timer_srv, 1s);
  apex::executor::executor_runner runner{*executor};

  // === Start shutdown timer (if configured)
  std::thread shutdown_timer_thread;
  if (runtime > 0) {
    std::cerr << "Max runtime has been configured to: " << runtime << "ms" << std::endl;
    std::cerr << "Starting shutdown timer." << std::endl;
    shutdown_timer_thread = std::thread([&runtime]() {
          std::this_thread::sleep_for(std::chrono::milliseconds(runtime));
          std::cerr << "Shutdown timer expired. Shutting down..." << std::endl;
          apex::interrupt_handler::trigger();
        });
  }

  // ===== Wait for signal
  const apex::interrupt_handler::installer interrupt_handler_installer{};
  apex::interrupt_handler::wait();

  // ===== Shutdown
  runner.stop();
  rclcpp::shutdown();
  if (shutdown_timer_thread.joinable()) {
    shutdown_timer_thread.join();
  }

  // ==== Dump data to file
  basic_types_file << basic_types_yaml << std::endl;
  fixed_arrays_file << fixed_arrays_yaml << std::endl;
  bounded_sequences_file << bounded_sequences_yaml << std::endl;
  unbounded_sequences_file << unbounded_sequences_yaml << std::endl;
  flattened_sequences_file << flattened_sequences_yaml << std::endl;

  return EXIT_SUCCESS;
}
