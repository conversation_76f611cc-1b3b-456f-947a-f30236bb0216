load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_pkg//:mappings.bzl", "pkg_files")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "intercomm_apex_pkg",
    description = "Apex.OS application to verify on-the-wire intercommunication between Apex.OS and ROS 2",
    lib_executables = ["apex_publisher"],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "intercomm_apex",
    share_data = [":share_data"],
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/configuration/settings:settings_pkg",
        "//common/interrupt:interrupt_pkg",
        "//grace/execution/executor2:executor2_pkg",
        "//grace/interfaces/test_msgs:test_msgs_pkg",
        "//grace/monitoring/logging:logging_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/tools/dynamic_message_introspection/dynmsg:dynmsg_pkg",
        "@yaml-cpp//:yaml-cpp_pkg",
    ],
)

pkg_files(
    name = "share_data",
    srcs = glob([
        "config/*.yml",
    ]),
)

# Extract the typesupport introspection lib from the message lib
cpp_msgs_introspection_library(
    name = "test_msgs_introspection_cpp",
    msgs = "//grace/interfaces/test_msgs",
)

cc_binary(
    name = "apex_publisher",
    srcs = [
        "include/intercomm_apex/apex_publisher_node.hpp",
        "include/intercomm_apex/create_apex_publisher_node.hpp",
        "include/intercomm_apex/topics.hpp",
        "src/apex_publisher_app.cpp",
    ],
    includes = ["include"],
    tags = ["exclude_sca"],
    deps = [
        ":test_msgs_introspection_cpp",
        "//common/configuration/settings",
        "//common/interrupt",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/test_msgs",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/tools/dynamic_message_introspection/dynmsg",
        "@yaml-cpp//:yaml-cpp",
    ],
)
