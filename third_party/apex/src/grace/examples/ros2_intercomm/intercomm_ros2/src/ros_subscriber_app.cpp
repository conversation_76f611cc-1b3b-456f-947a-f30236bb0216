// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include <dynmsg/message_reading.hpp>

#include <rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp>

#include <test_msgs/msg/basic_types.hpp>
#include <test_msgs/msg/arrays.hpp>
#include <test_msgs/msg/bounded_sequences.hpp>
#include <test_msgs/msg/unbounded_sequences.hpp>
#include <test_msgs/msg/flat_sequences_bounded.hpp>

#include <filesystem>
#include <fstream>
#include <chrono>
#include <iostream>
#include <functional>
#include <memory>
#include <string>

#include <cstdlib>

#include "intercomm_ros2/topics.hpp"
#include "intercomm_ros2/ros_subscriber_node.hpp"

constexpr const char * FILE_NAME__BASIC_TYPES = "basic_types.recv.yml";
constexpr const char * FILE_NAME__FIXED_ARRAYS = "fixed_arrays.recv.yml";
constexpr const char * FILE_NAME__BOUNDED_SEQUENCES = "bounded_sequences.recv.yml";
constexpr const char * FILE_NAME__UNBOUNDED_SEQUENCES = "unbounded_sequences.recv.yml";
constexpr const char * FILE_NAME__FLATTENED_SEQUENCES = "flattened_sequences.recv.yml";

int main(int argc, char * argv[])
{
  using namespace std::chrono_literals;

  using apex::intercomm_ros2::RosSubscriberNode;

  using apex::intercomm_ros2::TOPIC__BASIC_TYPES;
  using apex::intercomm_ros2::TOPIC__FIXED_ARRAYS;
  using apex::intercomm_ros2::TOPIC__BOUNDED_SEQUENCES;
  using apex::intercomm_ros2::TOPIC__UNBOUNDED_SEQUENCES;
  using apex::intercomm_ros2::TOPIC__FLATTENED_SEQUENCES;

  using test_msgs::msg::BasicTypes;
  using test_msgs::msg::Arrays;
  using test_msgs::msg::BoundedSequences;
  using test_msgs::msg::UnboundedSequences;
  using test_msgs::msg::FlatSequencesBounded;

  rclcpp::init(argc, argv);

  // ===== YAML Node
  YAML::Node basic_types_yaml;
  YAML::Node fixed_arrays_yaml;
  YAML::Node bounded_sequences_yaml;
  YAML::Node unbounded_sequences_yaml;
  YAML::Node flattened_sequences_yaml;

  // ===== File Output
  auto basic_types_file = std::ofstream{FILE_NAME__BASIC_TYPES};
  auto fixed_arrays_file = std::ofstream{FILE_NAME__FIXED_ARRAYS};
  auto bounded_sequences_file = std::ofstream{FILE_NAME__BOUNDED_SEQUENCES};
  auto unbounded_sequences_file = std::ofstream{FILE_NAME__UNBOUNDED_SEQUENCES};
  auto flattened_sequences_file = std::ofstream{FILE_NAME__FLATTENED_SEQUENCES};

  // ===== Nodes
  auto has_started = false;
  auto track_start = [&has_started]() {
      if (!has_started) {
        has_started = true;
        std::cout << "Started receiving messages." << std::endl;
      }
    };
  std::chrono::steady_clock::time_point latest_timestamp{};

  auto basic_types_subscription_node = std::make_shared<RosSubscriberNode<BasicTypes>>(
    TOPIC__BASIC_TYPES,
    [&](BasicTypes msg) {
      track_start();
      latest_timestamp = std::chrono::steady_clock::now();
      auto msg_yaml = dynmsg::cpp::message_to_yaml(msg);
      basic_types_yaml.push_back(msg_yaml);
    });
  auto fixed_arrays_subscription_node = std::make_shared<RosSubscriberNode<Arrays>>(
    TOPIC__FIXED_ARRAYS,
    [&](Arrays msg) {
      track_start();
      latest_timestamp = std::chrono::steady_clock::now();
      auto msg_yaml = dynmsg::cpp::message_to_yaml(msg);
      fixed_arrays_yaml.push_back(msg_yaml);
    });
  auto bounded_sequences_subscription_node = std::make_shared<RosSubscriberNode<BoundedSequences>>(
    TOPIC__BOUNDED_SEQUENCES,
    [&](BoundedSequences msg) {
      track_start();
      latest_timestamp = std::chrono::steady_clock::now();
      auto msg_yaml = dynmsg::cpp::message_to_yaml(msg);
      bounded_sequences_yaml.push_back(msg_yaml);
    });
  auto unbounded_sequences_subscription_node =
    std::make_shared<RosSubscriberNode<UnboundedSequences>>(
    TOPIC__UNBOUNDED_SEQUENCES,
    [&](UnboundedSequences msg) {
      track_start();
      latest_timestamp = std::chrono::steady_clock::now();
      auto msg_yaml = dynmsg::cpp::message_to_yaml(msg);
      unbounded_sequences_yaml.push_back(msg_yaml);
    });
  auto flattened_sequences_subscription_node =
    std::make_shared<RosSubscriberNode<FlatSequencesBounded>>(
    TOPIC__FLATTENED_SEQUENCES,
    [&](FlatSequencesBounded msg) {
      track_start();
      latest_timestamp = std::chrono::steady_clock::now();
      auto msg_yaml = dynmsg::cpp::message_to_yaml(msg);
      flattened_sequences_yaml.push_back(msg_yaml);
    });

  std::cerr << "Waiting for messages..." << std::endl;

  try {
    while (rclcpp::ok()) {
      // Check shutdown condition
      if (has_started && (std::chrono::steady_clock::now() - latest_timestamp) > 3s) {
        std::cerr << "No new messages being received. Shutting down." << std::endl;
        break;
      }
      // Process new messages
      rclcpp::spin_some(basic_types_subscription_node);
      rclcpp::spin_some(fixed_arrays_subscription_node);
      rclcpp::spin_some(bounded_sequences_subscription_node);
      rclcpp::spin_some(unbounded_sequences_subscription_node);
      rclcpp::spin_some(flattened_sequences_subscription_node);
    }
  } catch (const rclcpp::exceptions::RCLError & err) {
    std::cerr << "Failed with " << err.what() << std::endl;
  }

  // ==== Dump data to file
  basic_types_file << basic_types_yaml << std::endl;
  fixed_arrays_file << fixed_arrays_yaml << std::endl;
  bounded_sequences_file << bounded_sequences_yaml << std::endl;
  unbounded_sequences_file << unbounded_sequences_yaml << std::endl;
  flattened_sequences_file << flattened_sequences_yaml << std::endl;

  // ===== Shutdown
  rclcpp::shutdown();

  return EXIT_SUCCESS;
}
