load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

# TODO(christophe.bedard): this is needed for now QNX/qcc builds with dynamic_deps (#32754)
cpp_msgs_introspection_library(
    name = "action_msgs_intro_cpp",
    msgs = "//grace/interfaces/action_msgs",
)

# TODO(christophe.bedard): this is needed for now QNX/qcc builds with dynamic_deps (#32754)
cpp_msgs_introspection_library(
    name = "builtin_interfaces_intro_cpp",
    msgs = "//grace/interfaces/builtin_interfaces",
)

cpp_msgs_introspection_library(
    name = "test_msgs_intro_cpp",
    msgs = "//grace/interfaces/test_msgs",
)

# TODO(christophe.bedard): this is needed for now QNX/qcc builds with dynamic_deps (#32754)
cpp_msgs_introspection_library(
    name = "unique_identifier_msgs_intro_cpp",
    msgs = "//grace/interfaces/unique_identifier_msgs",
)

# TODO(christophe.bedard): this is needed for now QNX/qcc builds with dynamic_deps (#32754)
apex_cc_shared_library(
    name = "action_msgs_intro_cpp_shared",
    apex_cc_library = ":action_msgs_intro_cpp",
    shared_lib_name = "libaction_msgs__rosidl_typesupport_introspection_cpp.so",
)

# TODO(christophe.bedard): this is needed for now QNX/qcc builds with dynamic_deps (#32754)
apex_cc_shared_library(
    name = "builtin_interfaces_intro_cpp_shared",
    apex_cc_library = ":builtin_interfaces_intro_cpp",
    shared_lib_name = "libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so",
)

apex_cc_shared_library(
    name = "test_msgs_intro_cpp_shared",
    apex_cc_library = ":test_msgs_intro_cpp",
    shared_lib_name = "libtest_msgs__rosidl_typesupport_introspection_cpp.so",
)

# TODO(christophe.bedard): this is needed for now QNX/qcc builds with dynamic_deps (#32754)
apex_cc_shared_library(
    name = "unique_identifier_msgs_intro_cpp_shared",
    apex_cc_library = ":unique_identifier_msgs_intro_cpp",
    shared_lib_name = "libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so",
)

cc_binary(
    name = "ros_subscriber",
    srcs = [
        "include/intercomm_ros2/ros_subscriber_node.hpp",
        "include/intercomm_ros2/topics.hpp",
        "src/ros_subscriber_app.cpp",
    ],
    dynamic_deps = [
        ":test_msgs_intro_cpp_shared",
        ":action_msgs_intro_cpp_shared",
        ":builtin_interfaces_intro_cpp_shared",
        ":unique_identifier_msgs_intro_cpp_shared",
    ],
    includes = [
        "include",
    ],
    linkopts = select({
        "@platforms//os:linux": ["-lpthread"],
        "//conditions:default": [],
    }),
    tags = ["exclude_sca"],
    deps = [
        "//grace/interfaces/test_msgs",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/rosidl/rosidl_typesupport_introspection_cpp",
        "//grace/tools/dynamic_message_introspection/dynmsg",
    ],
)

ros_pkg(
    name = "intercomm_ros2_pkg",
    description = "ROS application to verify on-the-wire intercommunication between Apex.OS and ROS 2",
    lib_executables = [
        "ros_subscriber",
    ],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "intercomm_ros2_pkg",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)
