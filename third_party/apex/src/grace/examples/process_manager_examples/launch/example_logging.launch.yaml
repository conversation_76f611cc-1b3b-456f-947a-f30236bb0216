# [launch]
process-manager:
  log-file-enable: true
  logs-directory: "$(env LOG_DIR)" # LOG_DIR must be defined
  log-file-buffering: false
  append-timestamp-to-log-file-name: false
  framework-group:
    name: "framework_group"
    init-state: "ON"
    group-log-level: "INFO"
    group-log-handlers: [ "LogFile" ]
    processes:
      - name: "framework_process1"
        execution-report: "ByTopic"
        stdout-handlers: [ "LogTopic", "LogFile", "LogFileCombined", "Console"]
        stderr-handlers: [ "LogTopic", "LogFile", "LogFileCombined", "Console"]
        default-startup-config:
          path: "$(find-pkg-prefix process_manager)/lib/process_manager/minimal_process"
          args:
            - "--name"
            - "framework_process1"
            - "--stdout"
            - "--stderr"
            - "--report-running"
    states:
      - name: "ON"
        processes:
          - name: "framework_process1"
  process-groups:
    - name: "group1"
      init-state: "ON"
      group-log-level: "INFO"
      group-log-handlers: ["LogTopic"]
      processes:
        - name: "minimal_process1"
          execution-report: "ByTopic"
          stdout-handlers: ["LogFile"]
          stderr-handlers: ["LogFile"]
          default-startup-config:
            path: "$(find-pkg-prefix process_manager)/lib/process_manager/minimal_process"
            args:
              - "--name"
              - "process1"
              - "--print-args"
              - "--report-running"
              - "--print-env"
              - "APEX_GRACE_LOG_LEVEL"
              - "--print-env"
              - "APEX_IDA_LOG_LEVEL"
            env-vars:
              - { APEX_IDA_LOG_LEVEL: "warn"}
              - { APEX_GRACE_LOG_LEVEL: "FATAL"}
        - name: "minimal_process2"
          execution-report: "ByTopic"
          stdout-handlers: ["LogFileCombined"] # stdout/stderr streams are logged to the same file
          stderr-handlers: ["LogFileCombined"]
          default-startup-config:
            path: "$(find-pkg-prefix process_manager)/lib/process_manager/minimal_process"
            args:
              - "--name"
              - "process1"
              - "--print-args"
              - "--stderr"
              - "--report-running"
        - name: "minimal_process3"
          execution-report: "ByTopic"
          stream-redirection: "RedirectToLogFile"
          default-startup-config:
            path: "$(find-pkg-prefix process_manager)/lib/process_manager/minimal_process"
            args:
              - "--name"
              - "process2"
              - "--stdout"
              - "--stderr"
              - "--report-running"
      states:
        - name: "ON"
          processes:
            - name: "minimal_process1"
            - name: "minimal_process2"
            - name: "minimal_process3"
    - name: "group2"
      init-state: "ON"
      group-log-level: "INFO"
      processes:
        - name: "minimal_process3"
          execution-report: "ByTopic"
          stream-redirection: "RedirectToDevNull"
          default-startup-config:
            path: "$(find-pkg-prefix process_manager)/lib/process_manager/minimal_process"
            args:
              - "--name"
              - "process3"
              - "--stdout"
              - "--stderr"
              - "--report-running"
        - name: "minimal_process4"
          execution-report: "ByTopic"
          stream-redirection: "DoNotRedirect"
          default-startup-config:
            path: "$(find-pkg-prefix process_manager)/lib/process_manager/minimal_process"
            args:
              - "--name"
              - "process4"
              - "--stdout"
              - "--stderr"
              - "--report-running"
      states:
        - name: "ON"
          processes:
            - name: "minimal_process3"
            - name: "minimal_process4"
# [launch]
