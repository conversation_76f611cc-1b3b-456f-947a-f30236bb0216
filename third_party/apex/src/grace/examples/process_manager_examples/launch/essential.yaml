# [essential_group]
name: "essential_group"
init-state: "ON"
group-log-handlers: ["Console"]
group-log-level: "DEBUG"
processes:
  - name: "resource_creator" # (2)!
    stdout-handlers: ["Console"]
    stderr-handlers: ["Console"]
    execution-report: "ByStdoutStream"
    default-startup-config:
      path: "$(find-pkg-prefix resource_creator)/lib/resource_creator/resource_creator"
      env-vars:
        - { APEX_IDA_LOG_LEVEL: "warn" } # (4)!
  - name: "essential_process1" # (1)!
    allow-sigterm-exit: true
    stdout-handlers: [ "Console" ]
    stderr-handlers: [ "Console" ]
    default-startup-config:
      path: "$(find-pkg-prefix process_manager)/lib/process_manager/dummy_process"
      args:
        - "--dont-exit"
  - name: "essential_process2" # (3)!
    stdout-handlers: ["Console"]
    stderr-handlers: ["Console"]
    default-startup-config:
      path: "$(find-pkg-prefix process_manager)/lib/process_manager/minimal_process"
      args:
        - "--name"
        - "essential_process2"
states:
  - name: "ON"
    processes:
      - name: "essential_process1"
      - name: "essential_process2"
      - name: "resource_creator"
    process-dependencies:
      direct-dependency:
        - process: "essential_process2"
          depends-on: "resource_creator"
        - process: "resource_creator"
          depends-on: "essential_process1"
# [essential_group]