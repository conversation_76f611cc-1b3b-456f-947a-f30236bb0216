# [launch]
process-manager:
  essential-group: "$(include $(find-pkg-share essential_pkg)/launch/essential.yaml)" # (1)!
  framework-group: "$(include $(find-pkg-share process_manager_examples)/launch/framework_group.launch.yaml process-manager/framework-group)" # (2)!
  process-groups:
    - "$(include $(find-pkg-share process_manager_examples)/launch/group1.yaml process-manager/process-groups/0)"
    - "$(include $(find-pkg-share process_manager_examples)/launch/group2.yaml process-manager/process-groups/0)"
# [launch]