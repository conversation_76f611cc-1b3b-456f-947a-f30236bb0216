process-manager:
  framework-group:
    name: "framework_group"
    init-state: "ON"
    processes:
      - name: "system_manager"
        execution-report: "ByTopic"
        stream-redirection: "DoNotRedirect"
        default-startup-config:
          path: "grace/monitoring/system_manager/system_manager_exe"
      - name: "apex_ecu_data_publisher"
        default-startup-config:
          path: "$(find-pkg-prefix apex_ecu_monitor)/lib/apex_ecu_monitor/apex_ecu_data_publisher_exe"
          args:
            - "--apex-settings-file"
            - "$(find-pkg-share apex_ecu_monitor)/param/apex_ecu_monitor_default_settings.yaml" # needs to be adapted for each ECU
      - name: "apex_ecu_data_analysis"
        default-startup-config:
          path: "$(find-pkg-prefix apex_ecu_monitor)/lib/apex_ecu_monitor/apex_ecu_data_analysis_exe"
          args:
            - "--apex-settings-file"
            - "$(find-pkg-share apex_ecu_monitor)/param/apex_ecu_monitor_default_settings.yaml" # needs to be adapted for each ECU
    states:
      - name: "ON"
        processes:
          - name: "system_manager"
          - name: "apex_ecu_data_publisher"
          - name: "apex_ecu_data_analysis"
