load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_pkg//:mappings.bzl", "pkg_files")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "process_manager_examples_pkg",
    description = "Package showing different launch file examples for process manager",
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "process_manager_examples",
    share_data = [":launch_files"],
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/interrupt:interrupt_pkg",
        "//grace/configuration/process_manager:process_manager_pkg",
        "//grace/execution/apex_init:apex_init_pkg",
        "//grace/execution/executor2:executor2_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
    ],
)

pkg_files(
    name = "launch_files",
    srcs = glob(["launch/*.yaml"]),
    visibility = ["//visibility:public"],
)

ament_pkg_resources(
    name = "process_manager_resources",
    package = "process_manager",
    resources = {
        "@apex//grace/configuration/process_manager:minimal_process": "executable",
        "@apex//grace/configuration/process_manager:dummy_process": "executable",
    },
    visibility = ["//visibility:public"],
)

ament_pkg_resources(
    name = "process_manager_examples_resources",
    package = "process_manager_examples",
    resources = {
        "launch/framework_group.launch.yaml": "share",
        "launch/group0.yaml": "share",
        "launch/group1.yaml": "share",
        "launch/group2.yaml": "share",
        "launch/many_processes.yaml": "share",
        "settings/my_settings.yaml": "share",
    },
)

process_manager(
    name = "example_complex_graph",
    data = [":process_manager_resources"],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_complex_graph.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_composed",
    data = [
        ":process_manager_examples_resources",
        ":process_manager_resources",
    ],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_composed.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_configuration",
    data = [":process_manager_resources"],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_configuration.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_dependencies",
    data = [":process_manager_resources"],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_dependencies.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_logging",
    data = [":process_manager_resources"],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_logging.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_many_processes",
    data = [":process_manager_resources"],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_many_processes.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_many_processes_and_groups",
    data = [
        ":process_manager_examples_resources",
        ":process_manager_resources",
    ],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_many_processes_and_groups.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_minimal",
    data = [":process_manager_resources"],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_minimal.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_multiple_groups",
    data = [":process_manager_resources"],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_multiple_groups.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_restart",
    data = [":process_manager_resources"],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_restart.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_self_terminating_process",
    data = [":process_manager_resources"],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_self_terminating_process.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_state_transitions",
    data = [":process_manager_resources"],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_state_transitions.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_debug",
    data = [":process_manager_resources"],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_debug.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_bazel",
    data = [":process_manager_resources"],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_bazel.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_settings",
    data = [
        ":process_manager_examples_resources",
        ":process_manager_resources",
    ],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_settings.launch.yaml",
    visibility = ["//visibility:public"],
)

pkg_files(
    name = "essential",
    srcs = ["launch/essential.yaml"],
    prefix = "launch",
    visibility = ["//visibility:public"],
)

ament_pkg_resources(
    name = "essential_resources",
    package = "essential_pkg",
    resources = {":essential": "share"},
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_essential_and_framework_group",
    data = [
        ":essential_resources",
        ":process_manager_examples_resources",
        ":process_manager_resources",
        "//grace/monitoring/system_manager:system_manager_exe",
    ],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_essential_and_framework_group.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_framework_group",
    data = [
        ":process_manager_examples_resources",
        ":process_manager_resources",
        "//grace/monitoring/system_manager:system_manager_exe",
    ],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/framework_group.launch.yaml",
    visibility = ["//visibility:public"],
)

process_manager(
    name = "example_framework_group_with_ecu_monitor",
    data = [
        ":process_manager_examples_resources",
        ":process_manager_resources",
        "//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor:apex_ecu_monitor_resources",
        "//grace/monitoring/system_manager:system_manager_exe",
    ],
    env = {
        "APEX_IDA_LOG_LEVEL": "error",
        "APEX_GRACE_LOG_LEVEL": "INFO",
    },
    launch_file = "launch/example_framework_group_with_ecu_monitor.yaml",
    visibility = ["//visibility:public"],
)

filegroup(
    name = "doc_files",
    srcs = glob(["launch/*.yaml"]),
    visibility = [
        ":__subpackages__",
        "//grace/configuration/process_manager/doc:__pkg__",
    ],
)
