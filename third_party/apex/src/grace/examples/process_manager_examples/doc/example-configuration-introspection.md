# Process manager introspection

## Process group: essential_group

```yaml
process manager instance: "/my_namespace"
init group state: ON
log file buffering: 0
group liveliness config: { is_enabled: 1, thread_liveliness_heartbeat_period: 1000ms, liveliness_check_period: 1000ms }
log file enable: 1
log directory: "/home/<USER>/my_logs/process_manager/essential_group"
log file buffering: 0
log level: DEBUG
min publish interval: 1000ms
on failure behavior: Fatal
group log handlers: [Console]
wait for dispatcher: 5000000000ns
```

### Processes

#### Process: essential_process1

```yaml
Is self-terminating: false
Report type: None
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: true
Stream redirection: RedirectToProcessManager
```

### States

#### State: ON

##### Process list

###### Process: essential_process1

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/dummy_process --dont-exit
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=essential_group
__APEX_PM_PROCESS_NAME__=essential_process1
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(essential_process1)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(essential_process1)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start essential_process1)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop essential_process1)
```

## Process group: framework_group

```yaml
process manager instance: "/my_namespace"
init group state: ON
log file buffering: 0
group liveliness config: { is_enabled: 1, thread_liveliness_heartbeat_period: 1000ms, liveliness_check_period: 1000ms }
log file enable: 1
log directory: "/home/<USER>/my_logs/process_manager/framework_group"
log file buffering: 0
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [Console, LogTopic]
wait for dispatcher: 5000000000ns
```

### Processes

#### Process: framework_process1

```yaml
Is self-terminating: false
Report type: None
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: true
Stream redirection: RedirectToProcessManager
```

### States

#### State: ON

##### Process list

###### Process: framework_process1

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/dummy_process --dont-exit
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=framework_group
__APEX_PM_PROCESS_NAME__=framework_process1
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(framework_process1)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(framework_process1)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start framework_process1)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop framework_process1)
```

## Process group: group1

```yaml
process manager instance: "/my_namespace"
init group state: OFF
log file buffering: 0
group liveliness config: { is_enabled: 1, thread_liveliness_heartbeat_period: 1000ms, liveliness_check_period: 1000ms }
log file enable: 1
log directory: "/home/<USER>/my_logs/process_manager/group1"
log file buffering: 0
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogTopic]
wait for dispatcher: 5000000000ns
```

### Processes

#### Process: minimal_process2

```yaml
Is self-terminating: false
Report type: None
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: minimal_process1

```yaml
Is self-terminating: false
Report type: None
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: [Console, LogTopic]
Stderr stream logging: [Console, LogTopic]
Allow SIGTERM exit: true
Stream redirection: RedirectToProcessManager
```

### States

#### State: DEBUG

##### Process list

###### Process: minimal_process2

Command:

```shell
/usr/bin/gdbserver localhost:3000 /opt/ApexGraceBilbo/lib/process_manager/minimal_process --name minimal_process2
```

Env variables:

```shell
MYVAR=V3.0
ANOTHER_VAR=84
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=minimal_process2
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: minimal_process1

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name minimal_process --period-ms 1000
```

Env variables:

```shell
MYVAR=V2.0
ANOTHER_VAR=42
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=minimal_process1
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(minimal_process2)
    n1(minimal_process1)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(minimal_process2)
    n1(minimal_process1)
```

#### State: ON

##### Process list

###### Process: minimal_process2

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name minimal_process2 --stdout --print-args
```

Env variables:

```shell
MYVAR=V3.0
ANOTHER_VAR=84
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=minimal_process2
__APEX_PM_STABLE_PROCESS_ID__=2
EXTRA_VAR=V4.0
```

###### Process: minimal_process1

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name minimal_process --period-ms 1000
```

Env variables:

```shell
MYVAR=V2.0
ANOTHER_VAR=42
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=minimal_process1
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(minimal_process2) --> n1(minimal_process1)
    n1(minimal_process1)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(minimal_process2) --> n1(minimal_process1)
    n1(minimal_process1)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start minimal_process2)
    n1(Start minimal_process1) --> n0(Start minimal_process2)
```

OFF --> DEBUG

```mermaid
graph TD
    n0(Start minimal_process2)
    n1(Start minimal_process1)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop minimal_process2) --> n1(Stop minimal_process1)
    n1(Stop minimal_process1)
```

ON --> DEBUG

```mermaid
graph TD
    n0(Stop minimal_process2) --> n1(Start minimal_process2)
    n1(Start minimal_process2)
```

DEBUG --> OFF

```mermaid
graph TD
    n0(Stop minimal_process2)
    n1(Stop minimal_process1)
```

DEBUG --> ON

```mermaid
graph TD
    n0(Stop minimal_process2) --> n1(Start minimal_process2)
    n1(Start minimal_process2)
```
