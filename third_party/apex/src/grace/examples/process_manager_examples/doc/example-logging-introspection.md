# Process manager introspection

## Process group: framework_group

```yaml
process manager instance: ""
init group state: ON
log file buffering: 0
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 1
log directory: "/home/<USER>/my_logs/framework_group"
log file buffering: 0
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogFile]
wait for dispatcher: -1ns
```

### Processes

#### Process: framework_process1

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: [LogTopic, LogFile]
Stderr stream logging: [LogTopic, LogFile]
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

### States

#### State: ON

##### Process list

###### Process: framework_process1

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name framework_process1 --stdout --stderr --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=framework_group
__APEX_PM_PROCESS_NAME__=framework_process1
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(framework_process1)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(framework_process1)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start framework_process1)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop framework_process1)
```

## Process group: group1

```yaml
process manager instance: ""
init group state: ON
log file buffering: 0
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 1
log directory: "/home/<USER>/my_logs/group1"
log file buffering: 0
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogTopic]
wait for dispatcher: -1ns
```

### Processes

#### Process: minimal_process2

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: DoNotRedirect
```

#### Process: minimal_process1

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: [LogFile]
Stderr stream logging: [Console]
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

### States

#### State: ON

##### Process list

###### Process: minimal_process2

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process2 --stdout --stderr --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=minimal_process2
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: minimal_process1

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process1 --stdout --stderr --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=minimal_process1
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(minimal_process2)
    n1(minimal_process1)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(minimal_process2)
    n1(minimal_process1)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start minimal_process2)
    n1(Start minimal_process1)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop minimal_process2)
    n1(Stop minimal_process1)
```

## Process group: group2

```yaml
process manager instance: ""
init group state: ON
log file buffering: 0
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 1
log directory: "/home/<USER>/my_logs/group2"
log file buffering: 0
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogTopic]
wait for dispatcher: -1ns
```

### Processes

#### Process: minimal_process4

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToLogFile
```

#### Process: minimal_process3

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToDevNull
```

### States

#### State: ON

##### Process list

###### Process: minimal_process4

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process4 --stdout --stderr --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group2
__APEX_PM_PROCESS_NAME__=minimal_process4
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: minimal_process3

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process3 --stdout --stderr --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group2
__APEX_PM_PROCESS_NAME__=minimal_process3
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(minimal_process4)
    n1(minimal_process3)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(minimal_process4)
    n1(minimal_process3)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start minimal_process4)
    n1(Start minimal_process3)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop minimal_process4)
    n1(Stop minimal_process3)
```
