# Process manager introspection

## Process group: group1

```yaml
process manager instance: ""
init group state: ON
log file buffering: 1
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 0
log directory: ""
log file buffering: 1
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogTopic]
wait for dispatcher: -1ns
```

### Processes

#### Process: minimal_process

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

### States

#### State: ON

##### Process list

###### Process: minimal_process

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name minimal_process --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=minimal_process
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(minimal_process)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(minimal_process)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start minimal_process)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop minimal_process)
```
