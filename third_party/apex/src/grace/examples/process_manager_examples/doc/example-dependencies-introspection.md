# Process manager introspection

## Process group: group1

```yaml
process manager instance: ""
init group state: ON
log file buffering: 1
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 0
log directory: ""
log file buffering: 1
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogTopic]
wait for dispatcher: -1ns
```

### Processes

#### Process: D

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: C

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: B

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: A

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

### States

#### State: ON

##### Process list

###### Process: D

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name D --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=D
__APEX_PM_STABLE_PROCESS_ID__=4
```

###### Process: C

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name C --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=C
__APEX_PM_STABLE_PROCESS_ID__=3
```

###### Process: B

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name B --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=B
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: A

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name A --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=A
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(D)
    n1(C) --> n0(D)
    n2(B) --> n3(A)
    n3(A)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(D) --> n1(C)
    n1(C)
    n2(B) --> n3(A)
    n3(A)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start D) --> n1(Start C)
    n1(Start C)
    n2(Start B)
    n3(Start A) --> n2(Start B)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop D) --> n1(Stop C)
    n1(Stop C)
    n2(Stop B) --> n3(Stop A)
    n3(Stop A)
```
