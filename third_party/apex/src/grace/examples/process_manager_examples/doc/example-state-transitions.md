---
tags:
  - Bazel
  - Colcon
  - process_manager
---

# State transition example

## Description

This example shows how different state transition are defined depending on process dependencies
and depending on the processes running in each state.

Specifically this example aims to show how:

- How processes are started and stopped concurrently and sequentially simultaneously
- How state transitions are different depending on the origin and target state when each state
  adds new processes incrementally

It is interesting to observe the sequence of actions executed when executing the following
state transitions:

- `OFF` -> `INIT`: Only process `A` is started
- `INIT` -> `LEFT`: process `A` is already running, only processes `B`, `C`, `D` are started
- `LEFT` -> `LEFT_AND_RIGHT`: process `A`, `B`, `C`, `D` are already running, only processes
  `E`, `F`, `G` are started
- `LEFT` -> `RIGHT`: processes `B`, `C`, `D` are stopped concurrently with the processes
  `E`, `F`, `G` start
- `OFF` -> `FINAL`: all the processes are started in a single state transition

## Launch file

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_state_transitions.launch.yaml',
{'tag': '# [launch]', "skip_prefix": "#!"},
'yaml') }}

## Usage

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_state_transitions
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager process_manager --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_state_transitions.launch.yaml
    ```

<!-- markdownlint-disable restore -->

## Introspection

In order to introspect the launch file it is possible to use the introspection tool:

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_state_transitions.introspection -- --print
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager introspection --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_state_transitions.launch.yaml --print
    ```

<!-- markdownlint-disable restore -->

The following information should be printed out, and it can be visualized with a Markdown editor:

- [Introspection output](example-state-transitions-introspection.md)
