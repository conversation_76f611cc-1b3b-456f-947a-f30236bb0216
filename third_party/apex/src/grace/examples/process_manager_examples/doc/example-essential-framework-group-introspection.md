# Process manager introspection

## Process group: essential_group

```yaml
process manager instance: ""
init group state: ON
log file buffering: 1
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 0
log directory: ""
log file buffering: 1
log level: DEBUG
min publish interval: 1000ms
on failure behavior: Fatal
group log handlers: [Console]
wait for dispatcher: -1ns
```

### Processes

#### Process: essential_process2

```yaml
Is self-terminating: false
Report type: None
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: [Console]
Stderr stream logging: [Console]
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: essential_process1

```yaml
Is self-terminating: false
Report type: None
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: [Console]
Stderr stream logging: [Console]
Allow SIGTERM exit: true
Stream redirection: RedirectToProcessManager
```

#### Process: resource_creator

```yaml
Is self-terminating: false
Report type: ByStdoutStream
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: [Console]
Stderr stream logging: [Console]
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

### States

#### State: ON

##### Process list

###### Process: resource_creator

Command:

```shell
/opt/ApexIda/bin/resource_creator
```

Env variables:

```shell
APEX_IDA_LOG_LEVEL=warn
__APEX_PM_GROUP_NAME__=essential_group
__APEX_PM_PROCESS_NAME__=resource_creator
__APEX_PM_STABLE_PROCESS_ID__=1
```

###### Process: essential_process2

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name essential_process2
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=essential_group
__APEX_PM_PROCESS_NAME__=essential_process2
__APEX_PM_STABLE_PROCESS_ID__=3
```

###### Process: essential_process1

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/dummy_process --dont-exit
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=essential_group
__APEX_PM_PROCESS_NAME__=essential_process1
__APEX_PM_STABLE_PROCESS_ID__=2
```

##### Process startup dependencies

```mermaid
graph TD
    n0(resource_creator) --> n2(essential_process1)
    n1(essential_process2) --> n0(resource_creator)
    n2(essential_process1)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(resource_creator) --> n2(essential_process1)
    n1(essential_process2) --> n0(resource_creator)
    n2(essential_process1)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start resource_creator) --> n1(Start essential_process2)
    n1(Start essential_process2)
    n2(Start essential_process1) --> n0(Start resource_creator)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop resource_creator) --> n2(Stop essential_process1)
    n1(Stop essential_process2) --> n0(Stop resource_creator)
    n2(Stop essential_process1)
```

## Process group: framework_group

```yaml
process manager instance: ""
init group state: ON
log file buffering: 1
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 0
log directory: ""
log file buffering: 1
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogTopic]
wait for dispatcher: -1ns
```

### Processes

#### Process: system_manager

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: DoNotRedirect
```

### States

#### State: ON

##### Process list

###### Process: system_manager

Command:

```shell
grace/monitoring/system_manager/system_manager_exe
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=framework_group
__APEX_PM_PROCESS_NAME__=system_manager
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(system_manager)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(system_manager)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start system_manager)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop system_manager)
```

## Process group: group1

```yaml
process manager instance: ""
init group state: ON
log file buffering: 1
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 0
log directory: ""
log file buffering: 1
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogTopic]
wait for dispatcher: -1ns
```

### Processes

#### Process: B

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: A

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

### States

#### State: ON

##### Process list

###### Process: B

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name B --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=B
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: A

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name A --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=A
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(B)
    n1(A)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(B)
    n1(A)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start B)
    n1(Start A)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop B)
    n1(Stop A)
```

## Process group: group2

```yaml
process manager instance: ""
init group state: ON
log file buffering: 1
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 0
log directory: ""
log file buffering: 1
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogTopic]
wait for dispatcher: -1ns
```

### Processes

#### Process: C

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

### States

#### State: ON

##### Process list

###### Process: C

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name C --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group2
__APEX_PM_PROCESS_NAME__=C
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(C)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(C)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start C)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop C)
```
