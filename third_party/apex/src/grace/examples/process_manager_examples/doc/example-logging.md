---
tags:
  - Bazel
  - Colcon
  - process_manager
---

# Logging launch example

## Description

This example shows a how to use the logging options.

## Launch file

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_logging.launch.yaml',
{'tag': '# [launch]', "skip_prefix": "#!"},
'yaml') }}

## Usage

If the logging directory does not exist it must be created:

```shell ade
mkdir ~/my_logs
```

Now the process manager executable is run:

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_logging
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager process_manager --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_logging.launch.yaml
    ```

<!-- markdownlint-disable restore -->

The following log files should be available in the logging directory:

```shell ade
tree my_logs
my_logs
└── process_manager
    ├── group1
    │         ├── minimal_process1.stderr
    │         ├── minimal_process1.stdout
    │         ├── minimal_process2.stderr
    │         └── minimal_process2.stdout
    └── group2
        ├── minimal_process3.stderr
        ├── minimal_process3.stdout
        ├── minimal_process4.stderr
        └── minimal_process4.stdout
```

## Introspection

In order to introspect the launch file it is possible to use the introspection tool:

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_logging.introspection -- --print
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager introspection --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_logging.launch.yaml --print
    ```

<!-- markdownlint-disable restore -->

The following information should be printed out, and it can be visualized with a Markdown editor:

- [Introspection output](example-logging-introspection.md)
