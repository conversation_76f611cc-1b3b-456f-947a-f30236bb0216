# Process manager introspection

## Process group: group1

```yaml
process manager instance: ""
init group state: PARALLEL
log file buffering: 1
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 0
log directory: ""
log file buffering: 1
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogTopic]
wait for dispatcher: -1ns
```

### Processes

#### Process: process30

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process12

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process28

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process20

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process23

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process10

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process13

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process3

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process7

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process17

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process29

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process6

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process2

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process11

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process1

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process5

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process15

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process14

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process4

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process16

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process9

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process27

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process19

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process8

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process26

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process21

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process22

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process18

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process24

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: process25

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

### States

#### State: SEQUENCE

##### Process list

###### Process: process30

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process30 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process30
__APEX_PM_STABLE_PROCESS_ID__=30
```

###### Process: process12

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process12 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process12
__APEX_PM_STABLE_PROCESS_ID__=12
```

###### Process: process28

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process28 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process28
__APEX_PM_STABLE_PROCESS_ID__=28
```

###### Process: process20

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process20 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process20
__APEX_PM_STABLE_PROCESS_ID__=20
```

###### Process: process23

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process23 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process23
__APEX_PM_STABLE_PROCESS_ID__=23
```

###### Process: process10

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process10 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process10
__APEX_PM_STABLE_PROCESS_ID__=10
```

###### Process: process13

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process13 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process13
__APEX_PM_STABLE_PROCESS_ID__=13
```

###### Process: process3

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process3 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process3
__APEX_PM_STABLE_PROCESS_ID__=3
```

###### Process: process7

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process7 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process7
__APEX_PM_STABLE_PROCESS_ID__=7
```

###### Process: process17

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process17 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process17
__APEX_PM_STABLE_PROCESS_ID__=17
```

###### Process: process29

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process29 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process29
__APEX_PM_STABLE_PROCESS_ID__=29
```

###### Process: process6

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process6 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process6
__APEX_PM_STABLE_PROCESS_ID__=6
```

###### Process: process2

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process2 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process2
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: process11

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process11 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process11
__APEX_PM_STABLE_PROCESS_ID__=11
```

###### Process: process1

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process1 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process1
__APEX_PM_STABLE_PROCESS_ID__=1
```

###### Process: process5

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process5 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process5
__APEX_PM_STABLE_PROCESS_ID__=5
```

###### Process: process15

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process15 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process15
__APEX_PM_STABLE_PROCESS_ID__=15
```

###### Process: process14

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process14 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process14
__APEX_PM_STABLE_PROCESS_ID__=14
```

###### Process: process4

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process4 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process4
__APEX_PM_STABLE_PROCESS_ID__=4
```

###### Process: process16

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process16 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process16
__APEX_PM_STABLE_PROCESS_ID__=16
```

###### Process: process9

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process9 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process9
__APEX_PM_STABLE_PROCESS_ID__=9
```

###### Process: process27

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process27 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process27
__APEX_PM_STABLE_PROCESS_ID__=27
```

###### Process: process19

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process19 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process19
__APEX_PM_STABLE_PROCESS_ID__=19
```

###### Process: process8

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process8 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process8
__APEX_PM_STABLE_PROCESS_ID__=8
```

###### Process: process26

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process26 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process26
__APEX_PM_STABLE_PROCESS_ID__=26
```

###### Process: process21

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process21 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process21
__APEX_PM_STABLE_PROCESS_ID__=21
```

###### Process: process22

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process22 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process22
__APEX_PM_STABLE_PROCESS_ID__=22
```

###### Process: process18

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process18 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process18
__APEX_PM_STABLE_PROCESS_ID__=18
```

###### Process: process24

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process24 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process24
__APEX_PM_STABLE_PROCESS_ID__=24
```

###### Process: process25

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process25 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process25
__APEX_PM_STABLE_PROCESS_ID__=25
```

##### Process startup dependencies

```mermaid
graph TD
    n0(process30) --> n10(process29)
    n1(process12) --> n13(process11)
    n2(process28) --> n21(process27)
    n3(process20) --> n22(process19)
    n4(process23) --> n26(process22)
    n5(process10) --> n20(process9)
    n6(process13) --> n1(process12)
    n7(process3) --> n12(process2)
    n8(process7) --> n11(process6)
    n9(process17) --> n19(process16)
    n10(process29) --> n2(process28)
    n11(process6) --> n15(process5)
    n12(process2) --> n14(process1)
    n13(process11) --> n5(process10)
    n14(process1)
    n15(process5) --> n18(process4)
    n16(process15) --> n17(process14)
    n17(process14) --> n6(process13)
    n18(process4) --> n7(process3)
    n19(process16) --> n16(process15)
    n20(process9) --> n23(process8)
    n21(process27) --> n24(process26)
    n22(process19) --> n27(process18)
    n23(process8) --> n8(process7)
    n24(process26) --> n29(process25)
    n25(process21) --> n3(process20)
    n26(process22) --> n25(process21)
    n27(process18) --> n9(process17)
    n28(process24) --> n4(process23)
    n29(process25) --> n28(process24)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(process30) --> n10(process29)
    n1(process12) --> n13(process11)
    n2(process28) --> n21(process27)
    n3(process20) --> n22(process19)
    n4(process23) --> n26(process22)
    n5(process10) --> n20(process9)
    n6(process13) --> n1(process12)
    n7(process3) --> n12(process2)
    n8(process7) --> n11(process6)
    n9(process17) --> n19(process16)
    n10(process29) --> n2(process28)
    n11(process6) --> n15(process5)
    n12(process2) --> n14(process1)
    n13(process11) --> n5(process10)
    n14(process1)
    n15(process5) --> n18(process4)
    n16(process15) --> n17(process14)
    n17(process14) --> n6(process13)
    n18(process4) --> n7(process3)
    n19(process16) --> n16(process15)
    n20(process9) --> n23(process8)
    n21(process27) --> n24(process26)
    n22(process19) --> n27(process18)
    n23(process8) --> n8(process7)
    n24(process26) --> n29(process25)
    n25(process21) --> n3(process20)
    n26(process22) --> n25(process21)
    n27(process18) --> n9(process17)
    n28(process24) --> n4(process23)
    n29(process25) --> n28(process24)
```

#### State: PARALLEL

##### Process list

###### Process: process30

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process30 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process30
__APEX_PM_STABLE_PROCESS_ID__=30
```

###### Process: process12

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process12 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process12
__APEX_PM_STABLE_PROCESS_ID__=12
```

###### Process: process28

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process28 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process28
__APEX_PM_STABLE_PROCESS_ID__=28
```

###### Process: process20

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process20 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process20
__APEX_PM_STABLE_PROCESS_ID__=20
```

###### Process: process23

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process23 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process23
__APEX_PM_STABLE_PROCESS_ID__=23
```

###### Process: process10

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process10 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process10
__APEX_PM_STABLE_PROCESS_ID__=10
```

###### Process: process13

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process13 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process13
__APEX_PM_STABLE_PROCESS_ID__=13
```

###### Process: process3

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process3 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process3
__APEX_PM_STABLE_PROCESS_ID__=3
```

###### Process: process7

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process7 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process7
__APEX_PM_STABLE_PROCESS_ID__=7
```

###### Process: process17

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process17 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process17
__APEX_PM_STABLE_PROCESS_ID__=17
```

###### Process: process29

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process29 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process29
__APEX_PM_STABLE_PROCESS_ID__=29
```

###### Process: process6

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process6 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process6
__APEX_PM_STABLE_PROCESS_ID__=6
```

###### Process: process2

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process2 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process2
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: process11

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process11 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process11
__APEX_PM_STABLE_PROCESS_ID__=11
```

###### Process: process1

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process1 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process1
__APEX_PM_STABLE_PROCESS_ID__=1
```

###### Process: process5

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process5 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process5
__APEX_PM_STABLE_PROCESS_ID__=5
```

###### Process: process15

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process15 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process15
__APEX_PM_STABLE_PROCESS_ID__=15
```

###### Process: process14

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process14 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process14
__APEX_PM_STABLE_PROCESS_ID__=14
```

###### Process: process4

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process4 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process4
__APEX_PM_STABLE_PROCESS_ID__=4
```

###### Process: process16

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process16 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process16
__APEX_PM_STABLE_PROCESS_ID__=16
```

###### Process: process9

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process9 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process9
__APEX_PM_STABLE_PROCESS_ID__=9
```

###### Process: process27

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process27 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process27
__APEX_PM_STABLE_PROCESS_ID__=27
```

###### Process: process19

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process19 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process19
__APEX_PM_STABLE_PROCESS_ID__=19
```

###### Process: process8

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process8 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process8
__APEX_PM_STABLE_PROCESS_ID__=8
```

###### Process: process26

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process26 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process26
__APEX_PM_STABLE_PROCESS_ID__=26
```

###### Process: process21

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process21 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process21
__APEX_PM_STABLE_PROCESS_ID__=21
```

###### Process: process22

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process22 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process22
__APEX_PM_STABLE_PROCESS_ID__=22
```

###### Process: process18

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process18 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process18
__APEX_PM_STABLE_PROCESS_ID__=18
```

###### Process: process24

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process24 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process24
__APEX_PM_STABLE_PROCESS_ID__=24
```

###### Process: process25

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name process25 --report-running --delay-startup 1000 --delay-shutdown 1000
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=process25
__APEX_PM_STABLE_PROCESS_ID__=25
```

##### Process startup dependencies

```mermaid
graph TD
    n0(process30)
    n1(process12)
    n2(process28)
    n3(process20)
    n4(process23)
    n5(process10)
    n6(process13)
    n7(process3)
    n8(process7)
    n9(process17)
    n10(process29)
    n11(process6)
    n12(process2)
    n13(process11)
    n14(process1)
    n15(process5)
    n16(process15)
    n17(process14)
    n18(process4)
    n19(process16)
    n20(process9)
    n21(process27)
    n22(process19)
    n23(process8)
    n24(process26)
    n25(process21)
    n26(process22)
    n27(process18)
    n28(process24)
    n29(process25)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(process30)
    n1(process12)
    n2(process28)
    n3(process20)
    n4(process23)
    n5(process10)
    n6(process13)
    n7(process3)
    n8(process7)
    n9(process17)
    n10(process29)
    n11(process6)
    n12(process2)
    n13(process11)
    n14(process1)
    n15(process5)
    n16(process15)
    n17(process14)
    n18(process4)
    n19(process16)
    n20(process9)
    n21(process27)
    n22(process19)
    n23(process8)
    n24(process26)
    n25(process21)
    n26(process22)
    n27(process18)
    n28(process24)
    n29(process25)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> PARALLEL

```mermaid
graph TD
    n0(Start process30)
    n1(Start process12)
    n2(Start process28)
    n3(Start process20)
    n4(Start process23)
    n5(Start process10)
    n6(Start process13)
    n7(Start process3)
    n8(Start process7)
    n9(Start process17)
    n10(Start process29)
    n11(Start process6)
    n12(Start process2)
    n13(Start process11)
    n14(Start process1)
    n15(Start process5)
    n16(Start process15)
    n17(Start process14)
    n18(Start process4)
    n19(Start process16)
    n20(Start process9)
    n21(Start process27)
    n22(Start process19)
    n23(Start process8)
    n24(Start process26)
    n25(Start process21)
    n26(Start process22)
    n27(Start process18)
    n28(Start process24)
    n29(Start process25)
```

OFF --> SEQUENCE

```mermaid
graph TD
    n0(Start process30)
    n1(Start process12) --> n6(Start process13)
    n2(Start process28) --> n10(Start process29)
    n3(Start process20) --> n25(Start process21)
    n4(Start process23) --> n28(Start process24)
    n5(Start process10) --> n13(Start process11)
    n6(Start process13) --> n17(Start process14)
    n7(Start process3) --> n18(Start process4)
    n8(Start process7) --> n23(Start process8)
    n9(Start process17) --> n27(Start process18)
    n10(Start process29) --> n0(Start process30)
    n11(Start process6) --> n8(Start process7)
    n12(Start process2) --> n7(Start process3)
    n13(Start process11) --> n1(Start process12)
    n14(Start process1) --> n12(Start process2)
    n15(Start process5) --> n11(Start process6)
    n16(Start process15) --> n19(Start process16)
    n17(Start process14) --> n16(Start process15)
    n18(Start process4) --> n15(Start process5)
    n19(Start process16) --> n9(Start process17)
    n20(Start process9) --> n5(Start process10)
    n21(Start process27) --> n2(Start process28)
    n22(Start process19) --> n3(Start process20)
    n23(Start process8) --> n20(Start process9)
    n24(Start process26) --> n21(Start process27)
    n25(Start process21) --> n26(Start process22)
    n26(Start process22) --> n4(Start process23)
    n27(Start process18) --> n22(Start process19)
    n28(Start process24) --> n29(Start process25)
    n29(Start process25) --> n24(Start process26)
```

PARALLEL --> OFF

```mermaid
graph TD
    n0(Stop process30)
    n1(Stop process12)
    n2(Stop process28)
    n3(Stop process20)
    n4(Stop process23)
    n5(Stop process10)
    n6(Stop process13)
    n7(Stop process3)
    n8(Stop process7)
    n9(Stop process17)
    n10(Stop process29)
    n11(Stop process6)
    n12(Stop process2)
    n13(Stop process11)
    n14(Stop process1)
    n15(Stop process5)
    n16(Stop process15)
    n17(Stop process14)
    n18(Stop process4)
    n19(Stop process16)
    n20(Stop process9)
    n21(Stop process27)
    n22(Stop process19)
    n23(Stop process8)
    n24(Stop process26)
    n25(Stop process21)
    n26(Stop process22)
    n27(Stop process18)
    n28(Stop process24)
    n29(Stop process25)
```

PARALLEL --> SEQUENCE

```mermaid
graph TD
```

SEQUENCE --> OFF

```mermaid
graph TD
    n0(Stop process30) --> n10(Stop process29)
    n1(Stop process12) --> n13(Stop process11)
    n2(Stop process28) --> n21(Stop process27)
    n3(Stop process20) --> n22(Stop process19)
    n4(Stop process23) --> n26(Stop process22)
    n5(Stop process10) --> n20(Stop process9)
    n6(Stop process13) --> n1(Stop process12)
    n7(Stop process3) --> n12(Stop process2)
    n8(Stop process7) --> n11(Stop process6)
    n9(Stop process17) --> n19(Stop process16)
    n10(Stop process29) --> n2(Stop process28)
    n11(Stop process6) --> n15(Stop process5)
    n12(Stop process2) --> n14(Stop process1)
    n13(Stop process11) --> n5(Stop process10)
    n14(Stop process1)
    n15(Stop process5) --> n18(Stop process4)
    n16(Stop process15) --> n17(Stop process14)
    n17(Stop process14) --> n6(Stop process13)
    n18(Stop process4) --> n7(Stop process3)
    n19(Stop process16) --> n16(Stop process15)
    n20(Stop process9) --> n23(Stop process8)
    n21(Stop process27) --> n24(Stop process26)
    n22(Stop process19) --> n27(Stop process18)
    n23(Stop process8) --> n8(Stop process7)
    n24(Stop process26) --> n29(Stop process25)
    n25(Stop process21) --> n3(Stop process20)
    n26(Stop process22) --> n25(Stop process21)
    n27(Stop process18) --> n9(Stop process17)
    n28(Stop process24) --> n4(Stop process23)
    n29(Stop process25) --> n28(Stop process24)
```

SEQUENCE --> PARALLEL

```mermaid
graph TD
```
