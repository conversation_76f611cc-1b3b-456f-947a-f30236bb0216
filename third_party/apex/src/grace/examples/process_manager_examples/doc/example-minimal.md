---
tags:
   - Bazel
   - Colcon
  - process_manager
---

# Minimal launch example

## Description

This example shows a minimal `process_manager` launch file with one process group and
one process, one state and the default options.

## Launch file

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_minimal.launch.yaml',
{'tag': '# [launch]', "skip_prefix": "#!"},
'yaml') }}

1. Key of the process manager dictionary
2. List of process groups
3. Name of a process group
4. List of processes of a process group
5. Name of a process
6. Start-up configuration for a process. This is the default configuration used in all the
   process group states.
7. List of process group states
8. Name of a process group state
9. List of processes to run in a process group state
10. Name of a process to run in a process group state

## Usage

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_minimal
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager process_manager --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_minimal.launch.yaml
    ```

<!-- markdownlint-disable restore -->

## Introspection

In order to introspect the launch file it is possible to use the introspection tool:

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_minimal.introspection -- --print
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager introspection --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_minimal.launch.yaml --print
    ```

<!-- markdownlint-disable restore -->

The following information should be printed out, and it can be visualized with a Markdown editor:

- [Introspection output](example-minimal-introspection.md)
