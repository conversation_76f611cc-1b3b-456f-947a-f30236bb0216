# Process manager introspection

## Process group: group1

```yaml
process manager instance: ""
init group state: ON
log file buffering: 1
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 0
log directory: ""
log file buffering: 1
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogTopic]
wait for dispatcher: -1ns
```

### Processes

#### Process: ConfigReader

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: [Console]
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: StorageConsistencyChecker

```yaml
Is self-terminating: true
Report type: None
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: [Console]
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: Storage

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: [Console]
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

### States

#### State: ON

##### Process list

###### Process: ConfigReader

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name ConfigReader --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=ConfigReader
__APEX_PM_STABLE_PROCESS_ID__=3
```

###### Process: StorageConsistencyChecker

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name StorageConsistencyChecker --delay-startup 200 --delay-shutdown 200 --exit
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=StorageConsistencyChecker
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: Storage

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name Storage --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=Storage
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(ConfigReader) --> n1(StorageConsistencyChecker)
    n1(StorageConsistencyChecker) --> n2(Storage)
    n2(Storage)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(ConfigReader)
    n1(StorageConsistencyChecker)
    n2(Storage)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start ConfigReader)
    n1(StartAndWaitTerm StorageConsistencyChecker) --> n0(Start ConfigReader)
    n2(Start Storage) --> n1(StartAndWaitTerm StorageConsistencyChecker)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop ConfigReader)
    n1(Stop Storage)
```
