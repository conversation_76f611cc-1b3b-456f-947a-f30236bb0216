---
tags:
  - Bazel
  - Colcon
  - process_manager
---

# Launcher example

## Description

This example shows how to launch a `launcher` launch file with
`process_manager`.

!!! warning
    Apex.Grace Launcher package has been removed. However, it is still possible to use
    `launcher` launch files with the Process Manager

## Usage

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_launcher
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager process_manager --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_launcher.launch.yaml
    ```

<!-- markdownlint-disable restore -->

## Introspection

In order to introspect the launch file it is possible to use the introspection tool:

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_launcher.introspection -- --print
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager introspection --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_launcher.launch.yaml --print
    ```

<!-- markdownlint-disable restore -->

The following information should be printed out, and it can be visualized with a Markdown editor:

- [Introspection output](example-launcher-introspection.md)
