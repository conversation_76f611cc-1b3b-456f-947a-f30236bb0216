---
tags:
  - Bazel
  - Colcon
  - process_manager
---

# Essential and framework group example

## Description

This example shows a `process_manager` launch file with a user defined essential group and
a framework group.

## Launch file

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_essential_and_framework_group.launch.yaml',
{'tag': '# [launch]', "skip_prefix": "#!", 'indent': 4},
'yaml') }}

1. Essential group entry including a dictionary using composition
2. Framework group entry including a dictionary using composition

{{ code_snippet(
'grace/examples/process_manager_examples/launch/essential.yaml',
{'tag': '# [essential_group]', "skip_prefix": "#!", 'indent': 4},
'yaml') }}

1. Simulates a self-terminating process which must be run before the resource creator
2. The resource creator which enables Ida communications
3. Simulates a process which must be run after the resource creator
4. Tune the resource creator log level to reduce the verbosity

<!-- markdownlint-disable restore -->

{{ code_snippet(
'grace/examples/process_manager_examples/launch/framework_group.launch.yaml',
{'tag': '# [launch]', "skip_prefix": "#!"},
'yaml') }}

1. A non zero value ensures the Process Manager waits for the System Manager dispatcher service
2. Enables the Process Manager threads liveliness monitoring
3. Enables the Process Manager heartbeat
4. Required to ensure the System Manager is initialized successfully
5. Required to avoid broken pipe failures in case the Process Manager exits unexpectedly

## Usage

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_essential_and_framework_group
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager process_manager --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_essential_and_framework_group.launch.yaml
    ```

<!-- markdownlint-disable restore -->

## Introspection

In order to introspect the launch file it is possible to use the introspection tool:

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_essential_and_framework_group.introspection -- --print
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager introspection --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_essential_and_framework_group.launch.yaml --print
    ```

<!-- markdownlint-disable restore -->

The following information should be printed out, and it can be visualized with a Markdown editor:

- [Introspection output](example-essential-framework-group-introspection.md)
