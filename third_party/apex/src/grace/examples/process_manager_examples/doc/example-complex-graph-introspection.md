# Process manager introspection

## Process group: group1

```yaml
process manager instance: ""
init group state: STATE1
log file buffering: 1
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 0
log directory: ""
log file buffering: 1
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogTopic]
wait for dispatcher: -1ns
```

### Processes

#### Process: G

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: F

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: E

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: D

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: C

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: B

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: A

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

### States

#### State: STATE2

##### Process list

###### Process: G

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name G --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=G
__APEX_PM_STABLE_PROCESS_ID__=7
```

###### Process: F

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name F --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=F
__APEX_PM_STABLE_PROCESS_ID__=6
```

###### Process: E

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name E --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=E
__APEX_PM_STABLE_PROCESS_ID__=5
```

###### Process: D

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name D --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=D
__APEX_PM_STABLE_PROCESS_ID__=4
```

###### Process: C

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name C --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=C
__APEX_PM_STABLE_PROCESS_ID__=3
```

###### Process: B

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name B --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=B
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: A

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name A --report-running --stdout
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=A
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(G)
    n1(F)
    n2(E) --> n1(F)
    n3(D)
    n4(C) --> n0(G)
    n5(B) --> n3(D)
    n5(B) --> n4(C)
    n6(A)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(G)
    n1(F)
    n2(E) --> n1(F)
    n3(D)
    n4(C) --> n0(G)
    n5(B) --> n3(D)
    n5(B) --> n4(C)
    n6(A)
```

#### State: STATE1

##### Process list

###### Process: F

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name F --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=F
__APEX_PM_STABLE_PROCESS_ID__=6
```

###### Process: E

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name E --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=E
__APEX_PM_STABLE_PROCESS_ID__=5
```

###### Process: D

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name D --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=D
__APEX_PM_STABLE_PROCESS_ID__=4
```

###### Process: C

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name C --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=C
__APEX_PM_STABLE_PROCESS_ID__=3
```

###### Process: B

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name B --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=B
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: A

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name A --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=A
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(F)
    n1(E) --> n0(F)
    n2(D)
    n3(C)
    n4(B) --> n2(D)
    n4(B) --> n3(C)
    n5(A)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(F)
    n1(E) --> n0(F)
    n2(D)
    n3(C)
    n4(B) --> n2(D)
    n4(B) --> n3(C)
    n5(A)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> STATE1

```mermaid
graph TD
    n0(Start F) --> n1(Start E)
    n1(Start E)
    n2(Start D) --> n4(Start B)
    n3(Start C) --> n4(Start B)
    n4(Start B)
    n5(Start A)
```

OFF --> STATE2

```mermaid
graph TD
    n0(Start G) --> n4(Start C)
    n1(Start F) --> n2(Start E)
    n2(Start E)
    n3(Start D) --> n5(Start B)
    n4(Start C) --> n5(Start B)
    n5(Start B)
    n6(Start A)
```

STATE1 --> OFF

```mermaid
graph TD
    n0(Stop F)
    n1(Stop E) --> n0(Stop F)
    n2(Stop D)
    n3(Stop C)
    n4(Stop B) --> n2(Stop D)
    n4(Stop B) --> n3(Stop C)
    n5(Stop A)
```

STATE1 --> STATE2

```mermaid
graph TD
    n0(Stop A) --> n4(Start A)
    n1(Stop C) --> n3(Start G)
    n2(Stop B) --> n1(Stop C)
    n3(Start G) --> n5(Start C)
    n4(Start A)
    n5(Start C) --> n6(Start B)
    n6(Start B)
```

STATE2 --> OFF

```mermaid
graph TD
    n0(Stop G)
    n1(Stop F)
    n2(Stop E) --> n1(Stop F)
    n3(Stop D)
    n4(Stop C) --> n0(Stop G)
    n5(Stop B) --> n3(Stop D)
    n5(Stop B) --> n4(Stop C)
    n6(Stop A)
```

STATE2 --> STATE1

```mermaid
graph TD
    n0(Stop G) --> n5(Start C)
    n1(Stop A) --> n4(Start A)
    n2(Stop C) --> n0(Stop G)
    n3(Stop B) --> n2(Stop C)
    n4(Start A)
    n5(Start C) --> n6(Start B)
    n6(Start B)
```
