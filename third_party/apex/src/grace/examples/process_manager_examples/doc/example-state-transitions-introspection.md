# Process manager introspection

## Process group: group1

```yaml
process manager instance: ""
init group state: INIT
log file buffering: 1
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 0
log directory: ""
log file buffering: 1
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogTopic]
wait for dispatcher: -1ns
```

### Processes

#### Process: H

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: G

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: F

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: E

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: D

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: C

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: B

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: A

```yaml
Is self-terminating: false
Report type: ByTopic
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: []
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

### States

#### State: FINAL

##### Process list

###### Process: H

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name H --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=H
__APEX_PM_STABLE_PROCESS_ID__=8
```

###### Process: G

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name G --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=G
__APEX_PM_STABLE_PROCESS_ID__=7
```

###### Process: F

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name F --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=F
__APEX_PM_STABLE_PROCESS_ID__=6
```

###### Process: E

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name E --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=E
__APEX_PM_STABLE_PROCESS_ID__=5
```

###### Process: D

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name D --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=D
__APEX_PM_STABLE_PROCESS_ID__=4
```

###### Process: C

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name C --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=C
__APEX_PM_STABLE_PROCESS_ID__=3
```

###### Process: B

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name B --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=B
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: A

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name A --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=A
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(H) --> n1(G)
    n0(H) --> n4(D)
    n1(G) --> n2(F)
    n2(F) --> n3(E)
    n3(E) --> n7(A)
    n4(D) --> n5(C)
    n5(C) --> n6(B)
    n6(B) --> n7(A)
    n7(A)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(H) --> n1(G)
    n0(H) --> n4(D)
    n1(G) --> n2(F)
    n2(F) --> n3(E)
    n3(E) --> n7(A)
    n4(D) --> n5(C)
    n5(C) --> n6(B)
    n6(B) --> n7(A)
    n7(A)
```

#### State: RIGHT

##### Process list

###### Process: G

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name G --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=G
__APEX_PM_STABLE_PROCESS_ID__=7
```

###### Process: F

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name F --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=F
__APEX_PM_STABLE_PROCESS_ID__=6
```

###### Process: E

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name E --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=E
__APEX_PM_STABLE_PROCESS_ID__=5
```

###### Process: A

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name A --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=A
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(G) --> n1(F)
    n1(F) --> n2(E)
    n2(E) --> n3(A)
    n3(A)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(G) --> n1(F)
    n1(F) --> n2(E)
    n2(E) --> n3(A)
    n3(A)
```

#### State: LEFT_AND_RIGHT

##### Process list

###### Process: G

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name G --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=G
__APEX_PM_STABLE_PROCESS_ID__=7
```

###### Process: F

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name F --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=F
__APEX_PM_STABLE_PROCESS_ID__=6
```

###### Process: E

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name E --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=E
__APEX_PM_STABLE_PROCESS_ID__=5
```

###### Process: D

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name D --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=D
__APEX_PM_STABLE_PROCESS_ID__=4
```

###### Process: C

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name C --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=C
__APEX_PM_STABLE_PROCESS_ID__=3
```

###### Process: B

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name B --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=B
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: A

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name A --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=A
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(G) --> n1(F)
    n1(F) --> n2(E)
    n2(E) --> n6(A)
    n3(D) --> n4(C)
    n4(C) --> n5(B)
    n5(B) --> n6(A)
    n6(A)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(G) --> n1(F)
    n1(F) --> n2(E)
    n2(E) --> n6(A)
    n3(D) --> n4(C)
    n4(C) --> n5(B)
    n5(B) --> n6(A)
    n6(A)
```

#### State: LEFT

##### Process list

###### Process: D

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name D --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=D
__APEX_PM_STABLE_PROCESS_ID__=4
```

###### Process: C

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name C --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=C
__APEX_PM_STABLE_PROCESS_ID__=3
```

###### Process: B

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name B --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=B
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: A

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name A --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=A
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(D) --> n1(C)
    n1(C) --> n2(B)
    n2(B) --> n3(A)
    n3(A)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(D) --> n1(C)
    n1(C) --> n2(B)
    n2(B) --> n3(A)
    n3(A)
```

#### State: INIT

##### Process list

###### Process: A

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name A --report-running
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=A
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(A)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(A)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> INIT

```mermaid
graph TD
    n0(Start A)
```

OFF --> LEFT

```mermaid
graph TD
    n0(Start D)
    n1(Start C) --> n0(Start D)
    n2(Start B) --> n1(Start C)
    n3(Start A) --> n2(Start B)
```

OFF --> LEFT_AND_RIGHT

```mermaid
graph TD
    n0(Start G)
    n1(Start F) --> n0(Start G)
    n2(Start E) --> n1(Start F)
    n3(Start D)
    n4(Start C) --> n3(Start D)
    n5(Start B) --> n4(Start C)
    n6(Start A) --> n2(Start E)
    n6(Start A) --> n5(Start B)
```

OFF --> RIGHT

```mermaid
graph TD
    n0(Start G)
    n1(Start F) --> n0(Start G)
    n2(Start E) --> n1(Start F)
    n3(Start A) --> n2(Start E)
```

OFF --> FINAL

```mermaid
graph TD
    n0(Start H)
    n1(Start G) --> n0(Start H)
    n2(Start F) --> n1(Start G)
    n3(Start E) --> n2(Start F)
    n4(Start D) --> n0(Start H)
    n5(Start C) --> n4(Start D)
    n6(Start B) --> n5(Start C)
    n7(Start A) --> n3(Start E)
    n7(Start A) --> n6(Start B)
```

INIT --> OFF

```mermaid
graph TD
    n0(Stop A)
```

INIT --> LEFT

```mermaid
graph TD
    n0(Start D)
    n1(Start C) --> n0(Start D)
    n2(Start B) --> n1(Start C)
```

INIT --> LEFT_AND_RIGHT

```mermaid
graph TD
    n0(Start G)
    n1(Start F) --> n0(Start G)
    n2(Start E) --> n1(Start F)
    n3(Start D)
    n4(Start C) --> n3(Start D)
    n5(Start B) --> n4(Start C)
```

INIT --> RIGHT

```mermaid
graph TD
    n0(Start G)
    n1(Start F) --> n0(Start G)
    n2(Start E) --> n1(Start F)
```

INIT --> FINAL

```mermaid
graph TD
    n0(Start H)
    n1(Start G) --> n0(Start H)
    n2(Start F) --> n1(Start G)
    n3(Start E) --> n2(Start F)
    n4(Start D) --> n0(Start H)
    n5(Start C) --> n4(Start D)
    n6(Start B) --> n5(Start C)
```

LEFT --> OFF

```mermaid
graph TD
    n0(Stop D) --> n1(Stop C)
    n1(Stop C) --> n2(Stop B)
    n2(Stop B) --> n3(Stop A)
    n3(Stop A)
```

LEFT --> INIT

```mermaid
graph TD
    n0(Stop D) --> n1(Stop C)
    n1(Stop C) --> n2(Stop B)
    n2(Stop B)
```

LEFT --> LEFT_AND_RIGHT

```mermaid
graph TD
    n0(Start G)
    n1(Start F) --> n0(Start G)
    n2(Start E) --> n1(Start F)
```

LEFT --> RIGHT

```mermaid
graph TD
    n0(Stop D) --> n1(Stop C)
    n1(Stop C) --> n2(Stop B)
    n2(Stop B)
    n3(Start G)
    n4(Start F) --> n3(Start G)
    n5(Start E) --> n4(Start F)
```

LEFT --> FINAL

```mermaid
graph TD
    n0(Start H)
    n1(Start G) --> n0(Start H)
    n2(Start F) --> n1(Start G)
    n3(Start E) --> n2(Start F)
```

LEFT_AND_RIGHT --> OFF

```mermaid
graph TD
    n0(Stop G) --> n1(Stop F)
    n1(Stop F) --> n2(Stop E)
    n2(Stop E) --> n6(Stop A)
    n3(Stop D) --> n4(Stop C)
    n4(Stop C) --> n5(Stop B)
    n5(Stop B) --> n6(Stop A)
    n6(Stop A)
```

LEFT_AND_RIGHT --> INIT

```mermaid
graph TD
    n0(Stop G) --> n1(Stop F)
    n1(Stop F) --> n2(Stop E)
    n2(Stop E)
    n3(Stop D) --> n4(Stop C)
    n4(Stop C) --> n5(Stop B)
    n5(Stop B)
```

LEFT_AND_RIGHT --> LEFT

```mermaid
graph TD
    n0(Stop G) --> n1(Stop F)
    n1(Stop F) --> n2(Stop E)
    n2(Stop E)
```

LEFT_AND_RIGHT --> RIGHT

```mermaid
graph TD
    n0(Stop D) --> n1(Stop C)
    n1(Stop C) --> n2(Stop B)
    n2(Stop B)
```

LEFT_AND_RIGHT --> FINAL

```mermaid
graph TD
    n0(Start H)
```

RIGHT --> OFF

```mermaid
graph TD
    n0(Stop G) --> n1(Stop F)
    n1(Stop F) --> n2(Stop E)
    n2(Stop E) --> n3(Stop A)
    n3(Stop A)
```

RIGHT --> INIT

```mermaid
graph TD
    n0(Stop G) --> n1(Stop F)
    n1(Stop F) --> n2(Stop E)
    n2(Stop E)
```

RIGHT --> LEFT

```mermaid
graph TD
    n0(Stop G) --> n1(Stop F)
    n1(Stop F) --> n2(Stop E)
    n2(Stop E)
    n3(Start D)
    n4(Start C) --> n3(Start D)
    n5(Start B) --> n4(Start C)
```

RIGHT --> LEFT_AND_RIGHT

```mermaid
graph TD
    n0(Start D)
    n1(Start C) --> n0(Start D)
    n2(Start B) --> n1(Start C)
```

RIGHT --> FINAL

```mermaid
graph TD
    n0(Start H)
    n1(Start D) --> n0(Start H)
    n2(Start C) --> n1(Start D)
    n3(Start B) --> n2(Start C)
```

FINAL --> OFF

```mermaid
graph TD
    n0(Stop H) --> n1(Stop G)
    n0(Stop H) --> n4(Stop D)
    n1(Stop G) --> n2(Stop F)
    n2(Stop F) --> n3(Stop E)
    n3(Stop E) --> n7(Stop A)
    n4(Stop D) --> n5(Stop C)
    n5(Stop C) --> n6(Stop B)
    n6(Stop B) --> n7(Stop A)
    n7(Stop A)
```

FINAL --> INIT

```mermaid
graph TD
    n0(Stop H) --> n1(Stop G)
    n0(Stop H) --> n4(Stop D)
    n1(Stop G) --> n2(Stop F)
    n2(Stop F) --> n3(Stop E)
    n3(Stop E)
    n4(Stop D) --> n5(Stop C)
    n5(Stop C) --> n6(Stop B)
    n6(Stop B)
```

FINAL --> LEFT

```mermaid
graph TD
    n0(Stop H) --> n1(Stop G)
    n1(Stop G) --> n2(Stop F)
    n2(Stop F) --> n3(Stop E)
    n3(Stop E)
```

FINAL --> LEFT_AND_RIGHT

```mermaid
graph TD
    n0(Stop H)
```

FINAL --> RIGHT

```mermaid
graph TD
    n0(Stop H) --> n1(Stop D)
    n1(Stop D) --> n2(Stop C)
    n2(Stop C) --> n3(Stop B)
    n3(Stop B)
```
