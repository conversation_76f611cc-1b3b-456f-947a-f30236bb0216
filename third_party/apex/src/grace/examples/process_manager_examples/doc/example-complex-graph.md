---
tags:
  - Bazel
  - Colcon
  - process_manager
---

# Complex graph example

## Description

This example shows a `process_manager` launch file with a complex state transition action graph.
This example should illustrate how the dependency resolution process works.

## Launch file

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_complex_graph.launch.yaml',
{'tag': '# [launch]', "skip_prefix": "#!"},
'yaml') }}

## Usage

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_complex_graph
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager process_manager --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_complex_graph.launch.yaml
    ```

<!-- markdownlint-disable restore -->

## Introspection

In order to introspect the launch file it is possible to use the introspection tool:

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_complex_graph.introspection -- --print
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager introspection --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_complex_graph.launch.yaml --print
    ```

<!-- markdownlint-disable restore -->

The following information should be printed out, and it can be visualized with a Markdown editor:

- [Introspection output](example-complex-graph-introspection.md)
