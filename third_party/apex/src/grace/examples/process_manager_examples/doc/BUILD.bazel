load("//tools/bazel/rules_docs:defs.bzl", "docs_chapter")

# ""
package(default_visibility = ["//visibility:public"])

docs_chapter(
    homepage = "process-manager-examples.md",
    references = [
        "//common/configuration/settings/doc",
        "//grace/configuration/settings_extensions/doc",
    ],
    subchapters = [
        ("Example minimal", ":minimal"),
        ("Example configuration options", ":configuration"),
        ("Process restart example", ":restart"),
        ("Process dependencies example", ":dependencies"),
        ("State transition example", ":state_transitions"),
        ("Launcher example", ":launcher"),
        ("Composition example", ":composed"),
        ("Complex graph example", ":complex_graph"),
        ("Many processes example", ":many_processes"),
        ("Logging example", ":logging"),
        ("Self-terminating example", ":self_terminating_process"),
        ("Essential and framework group example", ":example_essential_and_framework_group"),
    ],
)

docs_chapter(
    name = "minimal",
    homepage = "example-minimal.md",
    subchapters = [
        ("Introspection output", "example-minimal-introspection.md"),
    ],
)

docs_chapter(
    name = "configuration",
    homepage = "example-configuration.md",
    subchapters = [
        ("Introspection output", "example-configuration-introspection.md"),
    ],
)

docs_chapter(
    name = "restart",
    homepage = "example-restart.md",
    subchapters = [
        ("Example minimal introspection", "example-restart-introspection.md"),
    ],
)

docs_chapter(
    name = "dependencies",
    homepage = "example-dependencies.md",
    subchapters = [
        ("Introspection output", "example-dependencies-introspection.md"),
    ],
)

docs_chapter(
    name = "state_transitions",
    homepage = "example-state-transitions.md",
    subchapters = [
        ("Introspection output", "example-state-transitions-introspection.md"),
    ],
)

docs_chapter(
    name = "launcher",
    homepage = "example-launcher.md",
    subchapters = [
        ("Introspection output", "example-launcher-introspection.md"),
    ],
)

docs_chapter(
    name = "composed",
    homepage = "example-composed.md",
    subchapters = [
        ("Introspection output", "example-composed-introspection.md"),
    ],
)

docs_chapter(
    name = "complex_graph",
    homepage = "example-complex-graph.md",
    referenced_srcs = [
        "//grace/examples/process_manager_examples:doc_files",
    ],
    subchapters = [
        ("Introspection output", "example-complex-graph-introspection.md"),
    ],
)

docs_chapter(
    name = "many_processes",
    homepage = "example-many-processes.md",
    subchapters = [
        ("Introspection output", "example-many-processes-introspection.md"),
    ],
)

docs_chapter(
    name = "logging",
    homepage = "example-logging.md",
    subchapters = [
        ("Introspection output", "example-logging-introspection.md"),
    ],
)

docs_chapter(
    name = "self_terminating_process",
    homepage = "example-self-terminating-process.md",
    subchapters = [
        ("Introspection output", "example-self-terminating-process-introspection.md"),
    ],
)

docs_chapter(
    name = "example_essential_and_framework_group",
    homepage = "example-essential-framework-group.md",
    subchapters = [
        ("Introspection output", "example-essential-framework-group-introspection.md"),
    ],
)
