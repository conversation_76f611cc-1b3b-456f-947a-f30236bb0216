# Process manager introspection

This is the result of the `process_manager` introspection tool.

## Process group name: group1

Group options:

```yaml
log file enable: 1
log directory: "~/my_logs/launcher"
log file buffering: 1
```

### State: On

#### Process list

##### Process: launcher_process0

Command:

```shell ade
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name minimal_process1
```

##### Process: launcher_process1

Command:

```shell ade
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name minimal_process2
```

#### Process startup dependencies

Empty

#### Process shutdown dependencies

Empty

### State: OFF

#### Process list

None

#### Process startup dependencies

Empty

#### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> On

```mermaid
graph TD
    n0(Start launcher_process0)
    n1(Start launcher_process1)
```

On --> OFF

```mermaid
graph TD
    n0(Stop launcher_process0)
    n1(Stop launcher_process1)
```
