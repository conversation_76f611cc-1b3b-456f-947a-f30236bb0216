---
tags:
  - Bazel
  - Colcon
  - process_manager
---

# Self-terminating process example

## Description

This example shows a launch file using a self-terminating process.

There are three processes:

1. `Storage`
2. `StorageConsistencyChecker`: this is a self-terminating process which has to be run after
   `Storage`
3. `ConfigReader`: this process has to be run after `StorageConsistencyChecker` is terminated

!!! note
    This example is based on Example 7.3 from AUTOSAR "Specification of Execution Management"

## Launch file

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_self_terminating_process.launch.yaml',
{'tag': '# [launch]', "skip_prefix": "#!"},
'yaml') }}

## Usage

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_self_terminating_process
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager process_manager --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_self_terminating_process.launch.yaml
    ```

<!-- markdownlint-disable restore -->

## Introspection

In order to introspect the launch file it is possible to use the introspection tool:

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_self_terminating_process.introspection -- --print
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager introspection --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_self_terminating_process.launch.yaml --print
    ```

<!-- markdownlint-disable restore -->

The following information should be printed out, and it can be visualized with a Markdown editor:

- [Introspection output](example-self-terminating-process-introspection.md)
