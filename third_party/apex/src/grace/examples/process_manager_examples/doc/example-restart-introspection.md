# Process manager introspection

## Process group: group1

```yaml
process manager instance: ""
init group state: ON
log file buffering: 1
group liveliness config: { is_enabled: 0, thread_liveliness_heartbeat_period: -1ms, liveliness_check_period: -1ms }
log file enable: 0
log directory: ""
log file buffering: 1
log level: INFO
min publish interval: 1000ms
on failure behavior: DoNothing
group log handlers: [LogTopic]
wait for dispatcher: -1ns
```

### Processes

#### Process: minimal_process2

```yaml
Is self-terminating: false
Report type: ByStderrStream
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: [Console]
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

#### Process: minimal_process1

```yaml
Is self-terminating: false
Report type: ByStdoutStream
Startup timeout: 5000ms
SIGTERM timeout: 5000ms
SIGKILL timeout: 5000ms
Stdout stream logging: [Console]
Stderr stream logging: []
Allow SIGTERM exit: false
Stream redirection: RedirectToProcessManager
```

### States

#### State: DEBUG

##### Process list

###### Process: minimal_process2

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name minimal_process2 --stderr --period-ms 0
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=minimal_process2
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: minimal_process1

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name minimal_process1 --stdout --period-ms 0 --stderr
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=minimal_process1
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(minimal_process2)
    n1(minimal_process1)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(minimal_process2)
    n1(minimal_process1)
```

#### State: ON

##### Process list

###### Process: minimal_process2

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name minimal_process2 --stderr --period-ms 0
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=minimal_process2
__APEX_PM_STABLE_PROCESS_ID__=2
```

###### Process: minimal_process1

Command:

```shell
/opt/ApexGraceBilbo/lib/process_manager/minimal_process --name minimal_process1 --stdout --period-ms 0
```

Env variables:

```shell
__APEX_PM_GROUP_NAME__=group1
__APEX_PM_PROCESS_NAME__=minimal_process1
__APEX_PM_STABLE_PROCESS_ID__=1
```

##### Process startup dependencies

```mermaid
graph TD
    n0(minimal_process2)
    n1(minimal_process1)
```

##### Process shutdown dependencies

```mermaid
graph TD
    n0(minimal_process2)
    n1(minimal_process1)
```

#### State: OFF

##### Process list

None

##### Process startup dependencies

Empty

##### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start minimal_process2)
    n1(Start minimal_process1)
```

OFF --> DEBUG

```mermaid
graph TD
    n0(Start minimal_process2)
    n1(Start minimal_process1)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop minimal_process2)
    n1(Stop minimal_process1)
```

ON --> DEBUG

```mermaid
graph TD
    n0(Stop minimal_process1) --> n1(Start minimal_process1)
    n1(Start minimal_process1)
```

DEBUG --> OFF

```mermaid
graph TD
    n0(Stop minimal_process2)
    n1(Stop minimal_process1)
```

DEBUG --> ON

```mermaid
graph TD
    n0(Stop minimal_process1) --> n1(Start minimal_process1)
    n1(Start minimal_process1)
```
