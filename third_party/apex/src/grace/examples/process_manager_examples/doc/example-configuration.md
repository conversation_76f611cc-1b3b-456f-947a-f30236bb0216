---
tags:
  - Bazel
  - Colcon
  - process_manager
---

# Configuration options example

## Description

This example shows a `process_manager` launch file with all the configuration options available.

## Launch file

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_configuration.launch.yaml',
{'tag': '# [launch]', "skip_prefix": "#!"},
'yaml') }}

1. Name of a process group
2. Process group initial state. (Optional) (Default: `OFF`)
3. Minimal interval between sending reports in milliseconds (Optional) (Default: `1000`)
4. Behavior of the process group when a failure happens (Optional) (Default: `NoAction`)
5. If true the process is expected to self-terminate (Optional) (Default: `False`)
6. Defines how a process reports its running execution state (Optional) (Default: `None`)
7. Maximum allowed duration in milliseconds until a process is running since it is started
   (Optional) (Default: `5000`)
8. Maximum allowed duration in milliseconds until a process terminates since a SIGTERM signal
   is sent (Optional) (Default: `5000`)
9. Maximum allowed duration in milliseconds until a process terminates since a SIGKILL signal
   is sent (Optional) (Default: `5000`)
10. Defines how a process `stdout` stream is logged (Optional) (Default: [])
11. Defines how a process `stderr` stream is logged (Optional) (Default: [])
12. Start-up configuration for a process. This is the default configuration used in all the
    process group states. (Optional if it is defined for each state)
13. The full path to the executable file
14. The array of the command line arguments to pass to the process (Optional)
15. The array of the environment variables to pass to the process (Optional)
16. Start-up configuration for a process defined specifically for a state. This is the
    configuration used by the process in this state. The default start-up configuration
    values might be overridden or extended by this entry.(Optional)
17. Defines the dependencies between processes (Optional)
18. Enables logging process streams to files.
19. Sets the root of logging directory. Must be non empty if file logging is enabled.
    (Optional) (Default: `""`)
20. If true, enables buffering when logging to file (Optional) (Default: `false`)
21. If true, creates a log file directory with timestamp as a suffix (Optional) (Default:
    `false`)
22. Defines the real-time priority for the process (Optional) (Default: 0)
23. Defines the real-time scheduling policy for the process (Optional) (Default: `other`)
24. Defines the CPU affinity mask for the process (Optional) (Default: 0)
25. Overrides the default path (Optional)
26. Overrides the default arguments (Optional)
27. Overrides the default environment variables (Optional)
28. The extra arguments are appended to the default arguments (Optional)
29. The extra environment variables are appended to the default
    environment variables (Optional)
30. Maximum allowed duration in milliseconds until the dispatcher event subscription is matched.
    The wait can be disabled with a -1 or any other negative value. (Optional) (Default: 5000)

## Usage

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_configuration
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager process_manager --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_configuration.launch.yaml
    ```

<!-- markdownlint-disable restore -->

## Introspection

In order to introspect the launch file it is possible to use the introspection tool:

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_configuration.introspection -- --print
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager introspection --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_configuration.launch.yaml --print
    ```

<!-- markdownlint-disable restore -->

The following information should be printed out, and it can be visualized with a Markdown editor:

- [Introspection output](example-configuration-introspection.md)
