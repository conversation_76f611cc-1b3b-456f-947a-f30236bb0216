#!/bin/bash

# versions
PERFORMANCE_TEST_VERSION=1.4.2
RMW_IMPLEMENTATION="rmw_apex_middleware"

# environment
WORKSPACE=~/perf_test_ws
BUILD_DIR=$WORKSPACE/build_"$RMW_IMPLEMENTATION"
INSTALL_DIR=$WORKSPACE/install_"$RMW_IMPLEMENTATION"
RESULTS_DIR=$WORKSPACE/experiment

PERF_TEST_BIN=$INSTALL_DIR/lib/performance_test/perf_test
ROUDI_BIN=/opt/ApexIdaBilbo/bin/iox-roudi

# packet loss params
LOSS_PER="1%"        # loss %

# stress params
VM=2                 # no of threads for malloc
VM_BYTES="20G"       # malloc size

# performance test params
MAX_RUNTIME=35       # max runtime in seconds
RATE=100             # rate in hz
HISTORY_DEPTH=100
TOPIC_NAME=TestTopic

# trap ctrl-c and call ctrl_c()
trap cleanup INT

function cleanup() {
    echo "Cleaning up"
    if [[ $LOAD_ENABLED -eq 1 ]]; then
      disable_load
    fi
    if [[ $ROUDI_ENABLED -eq 1 ]]; then
      echo "Stopping RouDi"
      killall iox-roudi
    fi
    exit 1
}

display_help() {
    echo "Usage: $0 <COMMAND> [<OPTIONS>] [-h] [--help]"
    echo ""
    echo "Command:"
    echo "  setup                 Setup the system with required tools and performance test"
    echo "  apex-middleware       Run an experiment using Apex.Middleware"
    echo "  iceoryx               Run an experiment using iceoryx"
    echo ""
    echo "Options:"
    echo "  -m|--msg-type)        Specify message type. Prints available types if no option provided."
    echo "  -q|--qos)             Specify QoS.              Either: reliable best-effort"
    echo "  -l|--load)            Specify load to simulate. Either: mem-load packet-loss"
    echo "  -z|--zero-copy)       Enable zero-copy."
    echo ""
}

#unused
function valid_ip()
{
    local ip=$1
    local stat=1

    if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        OIFS=$IFS
        IFS='.'
        ip=($ip)
        IFS=$OIFS
        [[ ${ip[0]} -le 255 && ${ip[1]} -le 255 \
            && ${ip[2]} -le 255 && ${ip[3]} -le 255 ]]
        stat=$?
    fi
    return $stat
}


setup() {
  echo "The following ROS 2 binary will be used:"
  if ! which ros2; then
      echo "Error: ROS 2 binary not found, source Apex.OS compiled with " "$1"
      exit 1
  fi

  setup_perf_test
  setup_perfplot

  # should exit sucessfully
  exit 0
}

setup_perf_test() {
  # clone performance_test
  if [[ ! -d $WORKSPACE/src/performance_test ]]; then
    echo "Performance test not found"
    echo "Cloning into $WORKSPACE/src"
    mkdir -p $WORKSPACE
    cd $WORKSPACE || exit 1
    git clone --single-branch --branch $PERFORMANCE_TEST_VERSION https://gitlab.com/ApexAI/performance_test.git src/performance_test
    cd - || exit 1
  fi
  # build performance test
  if [ ! -f $PERF_TEST_BIN ]; then
    echo "Building performance test with rmw: $RMW_IMPLEMENTATION"

    cd $WORKSPACE || exit 1
    rm -rf $BUILD_DIR $INSTALL_DIR
    colcon build \
    	--build-base $BUILD_DIR \
    	--install-base $INSTALL_DIR \
      --merge-install \
	    --cmake-args \
        -DPERFORMANCE_TEST_APEX_OS_POLLING_SUBSCRIPTION_ENABLED=ON \
        -DPERFORMANCE_TEST_ICEORYX_ENABLED=ON \
        -DPERFORMANCE_TEST_RCLCPP_ENABLED=OFF
  fi
  echo "Using performance test build at: $PERF_TEST_BIN"
}

setup_perfplot() {
  echo "Installing perfplot to open pdfs in browser"
  cd $WORKSPACE/src/performance_test/performance_test/helper_scripts/apex_performance_plotter || exit 1
  pip3 install . --no-deps
}

create_load() {
  if [ $LOAD_TYPE == "packet-loss" ]; then
     echo "Simulate packet loss"
     echo "Triggering $LOSS_PER packet loss"
     sudo tc qdisc add dev lo root netem loss $LOSS_PER
     tc qdisc show dev lo
     LOAD_ENABLED=1
  # TODO(Sumanth)  bug in graceful exit unders stress
  # https://github.com/ApexAI/performance_test/issues/56
  elif [ $LOAD_TYPE == "mem-load" ]; then
     echo "Stress CPU and memory"
     stress -c "$(nproc)" --vm "$VM" --vm-bytes "$VM_BYTES" -q &
     LOAD_ENABLED=1
  else
     echo "Running without load"
  fi
}

disable_load() {
  if [ $LOAD_TYPE == "packet-loss" ]; then
     echo "Resetting $LOSS_PER packet loss"
     sudo tc qdisc delete dev lo root netem loss $LOSS_PER
     tc qdisc show dev lo
  elif [ $LOAD_TYPE == "mem-load" ]; then
     echo "Stopping stress"
     killall stress
  fi
}

run_apex_middleware() {
  echo "Sourcing $INSTALL_DIR/setup.bash"
  source $INSTALL_DIR/setup.bash

  echo "Running performance test using apex_middleware"
  cd $WORKSPACE || exit 1
  mkdir -p $RESULTS_DIR
  cd $RESULTS_DIR || exit 1

  if [[ $USE_ZERO_COPY -eq 1 ]]; then
    echo "Configuring to run with zero-copy"
    ZERO_COPY_FLAG="--zero-copy"
    export APEX_MIDDLEWARE_SETTINGS=/tmp/perf_test_zc.yml
    echo -e 'domain:\n  shared_memory:\n    enable: true' > ${APEX_MIDDLEWARE_SETTINGS}
    start_roudi
  fi

  create_load
  if [ $QOS == "reliable" ] ; then
    echo "Using reliable QOS"
    $PERF_TEST_BIN -c $COMMUNICATION_PLUGIN $ZERO_COPY_FLAG -l log_pub.csv -t $TOPIC_NAME -m $MSG_TYPE --max-runtime $MAX_RUNTIME -r $RATE --ignore 5 --reliability RELIABLE --history KEEP_LAST --history-depth $HISTORY_DEPTH --num-sub-threads 0 --num-pub-threads 1 &>/dev/null &
    $PERF_TEST_BIN -c $COMMUNICATION_PLUGIN $ZERO_COPY_FLAG -l log_sub.csv --print-to-console -t $TOPIC_NAME -m $MSG_TYPE --max-runtime $MAX_RUNTIME -r $RATE --ignore 5 --reliability RELIABLE --history KEEP_LAST --history-depth $HISTORY_DEPTH --num-sub-threads 1 --num-pub-threads 0
  else
    echo "Using best effort QOS"
    $PERF_TEST_BIN -c $COMMUNICATION_PLUGIN $ZERO_COPY_FLAG -l log_pub.csv -t $TOPIC_NAME -m $MSG_TYPE --max-runtime $MAX_RUNTIME -r $RATE --ignore 5 --reliability BEST_EFFORT --history KEEP_LAST --history-depth $HISTORY_DEPTH --num-sub-threads 0 --num-pub-threads 1 &>/dev/null &
    $PERF_TEST_BIN -c $COMMUNICATION_PLUGIN $ZERO_COPY_FLAG -l log_sub.csv --print-to-console -t $TOPIC_NAME -m $MSG_TYPE --max-runtime $MAX_RUNTIME -r $RATE --ignore 5 --reliability BEST_EFFORT --history KEEP_LAST --history-depth $HISTORY_DEPTH --num-sub-threads 1 --num-pub-threads 0
  fi
  disable_load
  plot_results
}

run_iceoryx() {
    echo "Sourcing $INSTALL_DIR/setup.bash"
    source $INSTALL_DIR/setup.bash

    echo "Running iceoryx performance test using iceoryx"
    cd $WORKSPACE || exit 1
    mkdir -p $RESULTS_DIR
    cd $RESULTS_DIR || exit 1

    start_roudi
    $PERF_TEST_BIN -c $COMMUNICATION_PLUGIN --zero-copy -l log_pub.csv -t $TOPIC_NAME -m $MSG_TYPE --max-runtime $MAX_RUNTIME -r $RATE --ignore 5 --reliability RELIABLE --history KEEP_LAST --history-depth $HISTORY_DEPTH --num-sub-threads 0 --num-pub-threads 1 &>/dev/null &
    $PERF_TEST_BIN -c $COMMUNICATION_PLUGIN --zero-copy -l log_sub.csv --print-to-console -t $TOPIC_NAME -m $MSG_TYPE --max-runtime $MAX_RUNTIME -r $RATE --ignore 5 --reliability RELIABLE --history KEEP_LAST --history-depth $HISTORY_DEPTH --num-sub-threads 1 --num-pub-threads 0
    plot_results
}

start_roudi() {
    # configure mempools
    cat <<EOF > /tmp/roudi_config.toml
[general]
version = 1

[[segment]]

[[segment.mempool]]
size = 1024
count = 5000

[[segment.mempool]]
size = 16384
count = 1000

[[segment.mempool]]
size = 131072
count = 1000

[[segment.mempool]]
size = 524288
count = 500

[[segment.mempool]]
size=2621440
count=200
EOF

    # start with proper configuration
    HISTORY_DEPTH=16 # iceoryx compiled for history depth of 16, must be recompiled to increase
    $ROUDI_BIN -c /tmp/roudi_config.toml &
    ROUDI_ENABLED=1
    sleep 2
}

plot_results() {
  unset -v latest
  for file in $RESULTS_DIR/log_sub*; do
    [[ $file -nt $latest ]] && latest=$file
  done
  echo "Generating the performance plot $latest"
  perfplot "$latest"
  sensible-browser "$latest".pdf
}

check_setup() {
  if [[ ! -d $INSTALL_DIR ]]; then
    echo "Install directory does not exist: $INSTALL_DIR"
    echo "Try running: `basename $0` setup"
    exit 1
  fi
  if [[ ! -f $PERF_TEST_BIN ]]; then
    echo "Unable to locate performance_test at: $PERF_TEST_BIN"
    echo "Try running: `basename $0` setup"
    exit 1
  fi
  if [[ ! -f $ROUDI_BIN ]]; then
    echo "Unable to locate roudi at: $ROUDI_BIN"
    echo "Try running: `basename $0` setup"
    exit 1
  fi
}

print_options() {
    echo "Options:"
    echo "    MSG_TYPE=$MSG_TYPE"
    echo "    QOS=$QOS"
    echo "    LOAD=$LOAD_TYPE"
    echo "    ZERO_COPY=$USE_ZERO_COPY"
}

# Default configuration
MSG_TYPE="Array1k"
QOS="best-effort"
LOAD_TYPE="no-load"
USE_ZERO_COPY=0

# Parse options
POSITIONAL=()
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    -q|--qos)
      if [ "$2" != "reliable" ] && [ "$2" != "best-effort" ]; then
         echo "Error: Unknown QOS option: $2"
         display_help
         exit 1
      fi
      QOS="$2"
      shift
      shift
      ;;
    -l|--load)
      if [ "$2" != "mem-load" ] && [ "$2" != "packet-loss" ]; then
        echo "Error: Unknown load option: $2"
        display_help
        exit 1
      fi
      LOAD_TYPE="$2"
      shift
      shift
      ;;
    -m|--msg-type)
      source $INSTALL_DIR/setup.bash
      # Get list of supported messages from performance_test
      mapfile -t msg_types < <( $PERF_TEST_BIN --msg-list )
      if [[ ! " ${msg_types[*]} " =~ " $2 " ]]; then
        echo "Error: Unknown message type: $2"
        echo "Available message types:"
        printf '  %s\n' "${msg_types[@]}"
        exit 1
      fi
      MSG_TYPE=$2
      shift
      shift
      ;;
    -z|--zero-copy)
      USE_ZERO_COPY=1
      shift
      ;;
    *)
      POSITIONAL+=("$1")
      shift
      ;;
  esac
done
set -- "${POSITIONAL[@]}" # restore positional parameters

# Run application
while :
do
    case "$1" in
      -h | --help)
          display_help
          exit 0
          ;;
      setup)
	        shift
          #if requested for help
          if [ "$1" == "-h" ] || [ "$1" == "--help" ] ; then
              display_help
              exit 0
          fi
          setup
          exit 0 # setup should run alone
          break
          ;;
      apex-middleware)
          echo "Running Apex.Middleware measurement"
          print_options
          check_setup
          COMMUNICATION_PLUGIN="ApexOSPollingSubscription"
          run_apex_middleware
          cleanup
          break
          ;;
      iceoryx)
          echo "Running iceoryx measurement"
          print_options
          check_setup
          COMMUNICATION_PLUGIN="iceoryx"
          run_iceoryx
          cleanup
          break
          ;;
      --) # End of all options
          shift
          break
          ;;
      -* | *)
          echo "Error: Unknown option: $1" >&2
          display_help
          exit 1
          ;;
      *)  # No more options
          break
          ;;
    esac
done
