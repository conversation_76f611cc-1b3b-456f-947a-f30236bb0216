#!/usr/bin/env bash
# Copyright 2022 Apex.AI, Inc.
# All rights reserved.

set -eEo pipefail

WORKSPACE=$HOME/security_store_ws

# This script only runs inside ADE
if [[ -z "${ADE_HOME_HOSTPATH}" ]]; then
  echo "Please start this script in the ADE!"
  exit 1
fi

if [ -d "$WORKSPACE" ]; then
  rm -rf $WORKSPACE
  echo "Previously generated security store has been removed"
fi

source /opt/ApexGraceBilbo/setup.bash

mkdir -p $HOME/security_store_ws
cd $HOME/security_store_ws

# Generate a keystore, keys, and certificates:
# ![Create Keystore]
ros2 security create_keystore demo_keystore
# ![Create Keystore]
echo "Keystore has been generated"

# Generate keys and certificates:
# ![Generate Keys]
ros2 security create_enclave demo_keystore /talker_listener/talker
ros2 security create_enclave demo_keystore /talker_listener/listener
# ![Generate Keys]
echo "Keys and certificates have been generated"
