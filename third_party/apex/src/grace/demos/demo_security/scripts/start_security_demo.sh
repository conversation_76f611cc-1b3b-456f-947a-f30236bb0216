#!/usr/bin/env bash
# Copyright 2022 Apex.AI, Inc.
# All rights reserved.

set -eEuo pipefail

session="security_encryption_demo"

window0="talker"
current_window=$window0

# check if security store exist
SECURITY_STORE=$HOME/security_store_ws/demo_keystore

if [ ! -d "$SECURITY_STORE" ]; then
  echo "Please create security store first"
  exit
fi

# check if .tmux.conf exists
FILE="$HOME/.tmux.conf"

if ! grep -s -q -i 'set -g pane-border-status top' "$FILE"; then
    cat <<EOF >> $FILE
set -g pane-border-status top
EOF
fi

echo "Press any key to start the demo"
while [ true ] ; do
read -t 60 -n 1
if [ $? = 0 ] ; then
break
else
echo "waiting for any key..."
fi
done

tmux new-session -d -s $session
tmux rename-window -t 0 $window0
tmux set -t "$window0" -g mouse on

send_command() { tmux send-keys -t $current_window -- "$@"; }

tmux split-window -v
tmux split-window -v

talker_pane=0
listener_pane=1
wireshark_pane=2

######### talker node #########
tmux select-pane -t $talker_pane
send_command "printf '\033]2;%s\033\\' 'talker node'" C-m
send_command "source /opt/ApexGraceBilbo/setup.bash" C-m
# ![Security Controlling Variables]
send_command "export ROS_SECURITY_KEYSTORE=$SECURITY_STORE" C-m # (1)!
send_command "export ROS_SECURITY_STRATEGY=Enforce" C-m # (2)!
send_command "export ROS_SECURITY_ENABLE=true" C-m # (3)!
# ![Security Controlling Variables]
# ![Enclave ROS Args]
send_command "ros2 run demo_nodes_cpp talker --ros-args --enclave /talker_listener/talker" C-m # (1)!
# ![Enclave ROS Args]

######### listener node #########
tmux select-pane -t $listener_pane
send_command "printf '\033]2;%s\033\\' 'listener node'" C-m
send_command "source /opt/ApexGraceBilbo/setup.bash" C-m
send_command "export ROS_SECURITY_KEYSTORE=$SECURITY_STORE" C-m
send_command "export ROS_SECURITY_STRATEGY=Enforce" C-m
send_command "export ROS_SECURITY_ENABLE=true" C-m
send_command "ros2 run demo_nodes_cpp listener --ros-args --enclave /talker_listener/listener" C-m

######### wireshark #########
tmux select-pane -t $wireshark_pane
send_command "printf '\033]2;%s\033\\' 'Wireshark'" C-m
send_command "sudo wireshark -i lo -k" C-m

tmux select-layout even-vertical
tmux attach-session -t $session:0
