load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_binary")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ENTRY_POINTS = {
    "console_scripts": [
        "fibonacci_action_client = action_tutorials_py.fibonacci_action_client:main",
        "fibonacci_action_server = action_tutorials_py.fibonacci_action_server:main",
    ],
}

ros_pkg(
    name = "action_tutorials_py_pkg",
    description = "Python action tutorial code",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "action_tutorials_py",
    py_libraries = [":fibonacci_action"],
    version = "0.14.2",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Intended Audience :: Developers",
        "License :: OSI Approved :: Apache Software License",
        "Programming Language :: Python",
        "Topic :: Software Development",
    ],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "//grace/demos/ros2_demos/action_tutorials/action_tutorials_interfaces:action_tutorials_interfaces_pkg",
        "//grace/ros/rclpy/rclpy:rclpy_pkg",
    ],
)

py_msgs_library(
    name = "action_tutorials_interfaces_py",
    msgs = "@apex//grace/demos/ros2_demos/action_tutorials/action_tutorials_interfaces",
)

py_entry_points_library(
    name = "fibonacci_action",
    srcs = [
        "action_tutorials_py/__init__.py",
        "action_tutorials_py/fibonacci_action_client.py",
        "action_tutorials_py/fibonacci_action_server.py",
    ],
    entry_points = ENTRY_POINTS,
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        ":action_tutorials_interfaces_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

py_entry_points_binary(
    name = "fibonacci_action_client",
    py_entry_points_library = ":fibonacci_action",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "fibonacci_action_server",
    py_entry_points_library = ":fibonacci_action",
    visibility = ["//visibility:public"],
)
