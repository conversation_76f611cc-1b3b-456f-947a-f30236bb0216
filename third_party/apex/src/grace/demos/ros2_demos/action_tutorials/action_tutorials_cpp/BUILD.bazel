load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_rclcpp_component")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "action_tutorials_cpp_pkg",
    cc_libraries = [
        ":client",
        ":server",
    ],
    description = "Action tutorials action",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "action_tutorials_cpp",
    version = "0.14.2",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/demos/ros2_demos/action_tutorials/action_tutorials_interfaces:action_tutorials_interfaces_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/ros/rclcpp/rclcpp_action:rclcpp_action_pkg",
        "//grace/ros/rclcpp/rclcpp_components:rclcpp_components_pkg",
    ],
)

apex_cc_library(
    name = "headers",
    hdrs = ["include/action_tutorials_cpp/visibility_control.h"],
    strip_include_prefix = "include",
)

apex_cc_library(
    name = "client",
    srcs = ["src/fibonacci_action_client.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
        "//grace/demos/ros2_demos/action_tutorials/action_tutorials_interfaces",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rclcpp/rclcpp_action",
        "//grace/ros/rclcpp/rclcpp_components:component_manager",
    ],
)

apex_cc_library(
    name = "server",
    srcs = ["src/fibonacci_action_server.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
        "//grace/demos/ros2_demos/action_tutorials/action_tutorials_interfaces",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rclcpp/rclcpp_action",
        "//grace/ros/rclcpp/rclcpp_components:component_manager",
    ],
)

ament_rclcpp_component(
    name = "action_tutorials_cpp",
    package = "action_tutorials",
    plugins = {
        "action_tutorials_cpp::FibonacciActionClient": ":client",
        "action_tutorials_cpp::FibonacciActionServer": ":server",
    },
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
)
