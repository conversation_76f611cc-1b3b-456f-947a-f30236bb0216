load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_rclcpp_component")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "quality_of_service_demo_cpp_pkg",
    cc_libraries = [
        ":headers",
        ":message_lost_listener_lib",
        ":message_lost_talker_lib",
        ":qos_overrides_listener_lib",
        ":qos_overrides_talker_lib",
    ],
    description = "C++ Demo applications for Quality of Service features",
    lib_executables = [
        ":liveliness",
        ":lifespan",
        ":deadline",
        ":interactive_publisher",
        ":interactive_subscriber",
        ":incompatible_qos",
    ],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "quality_of_service_demo_cpp",
    version = "0.14.2",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/interfaces/sensor_msgs:sensor_msgs_pkg",
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/ros/rclcpp/rclcpp_components:rclcpp_components_pkg",
        "//grace/ros/rcutils:rcutils_pkg",
    ],
)

cc_library(
    name = "headers",
    hdrs = [
        "include/quality_of_service_demo/common_nodes.hpp",
        "include/quality_of_service_demo/visibility_control.h",
    ],
    strip_include_prefix = "include",
    deps = [
        "//grace/interfaces/sensor_msgs",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rcutils",
    ],
)

cc_binary(
    name = "liveliness_cpp",
    srcs = [
        "src/common_nodes.cpp",
        "src/liveliness.cpp",
        "src/utils.cpp",
        "src/utils.hpp",
    ],
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

# To avoid name collision when generating CMake files for BST
alias(
    name = "liveliness",
    actual = ":liveliness_cpp",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "lifespan_cpp",
    srcs = [
        "src/common_nodes.cpp",
        "src/lifespan.cpp",
        "src/utils.cpp",
        "src/utils.hpp",
    ],
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

# To avoid name collision when generating CMake files for BST
alias(
    name = "lifespan",
    actual = ":lifespan_cpp",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "deadline_cpp",
    srcs = [
        "src/common_nodes.cpp",
        "src/deadline.cpp",
        "src/utils.cpp",
        "src/utils.hpp",
    ],
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

# To avoid name collision when generating CMake files for BST
alias(
    name = "deadline",
    actual = ":deadline_cpp",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "interactive_publisher",
    srcs = [
        "src/common_nodes.cpp",
        "src/interactive_publisher.cpp",
        "src/utils.cpp",
        "src/utils.hpp",
    ],
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

cc_binary(
    name = "interactive_subscriber",
    srcs = [
        "src/common_nodes.cpp",
        "src/interactive_subscriber.cpp",
        "src/utils.cpp",
        "src/utils.hpp",
    ],
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

cc_binary(
    name = "incompatible_qos_cpp",
    srcs = [
        "src/common_nodes.cpp",
        "src/incompatible_qos.cpp",
        "src/utils.cpp",
        "src/utils.hpp",
    ],
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

# To avoid name collision when generating CMake files for BST
alias(
    name = "incompatible_qos",
    actual = ":incompatible_qos_cpp",
    visibility = ["//visibility:public"],
)

apex_cc_library(
    name = "message_lost_listener_lib",
    srcs = ["src/message_lost_listener.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
        "//grace/ros/rclcpp/rclcpp_components:component_manager",
    ],
)

apex_cc_library(
    name = "message_lost_talker_lib",
    srcs = ["src/message_lost_talker.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
        "//grace/ros/rclcpp/rclcpp_components:component_manager",
    ],
)

apex_cc_library(
    name = "qos_overrides_listener_lib",
    srcs = ["src/qos_overrides_listener.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
        "//grace/ros/rclcpp/rclcpp_components:component_manager",
    ],
)

apex_cc_library(
    name = "qos_overrides_talker_lib",
    srcs = ["src/qos_overrides_talker.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
        "//grace/ros/rclcpp/rclcpp_components:component_manager",
    ],
)

ament_rclcpp_component(
    name = "quality_of_service",
    package = "quality_of_service_demo",
    plugins = {
        "quality_of_service_demo::MessageLostListener": ":message_lost_listener_lib",
        "quality_of_service_demo::MessageLostTalker": ":message_lost_talker_lib",
        "quality_of_service_demo::QosOverridesListener": ":qos_overrides_listener_lib",
        "quality_of_service_demo::QosOverridesTalker": ":qos_overrides_talker_lib",
    },
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
)

filegroup(
    name = "params_file",
    srcs = glob(["params_file/*.yaml"]),
    visibility = ["//visibility:public"],
)
