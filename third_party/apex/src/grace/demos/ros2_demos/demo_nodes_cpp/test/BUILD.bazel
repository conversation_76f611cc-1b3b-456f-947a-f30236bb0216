load("@apex//common/bazel/helpers:defs.bzl", "remove_duplicates")
load("@apex//common/bazel/rules_cmake:defs.bzl", "cmake_configure_file")
load("@apex//common/bazel/rules_deployment:defs.bzl", "executables_collection")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test")
load("@python_vendor//:requirements.bzl", "requirement")

TEST_EXECUTABLES = [
    "list_parameters_async",
    "list_parameters",
    "parameter_events_async",
    "parameter_events",
    "set_and_get_parameters_async",
    "set_and_get_parameters",
]

executables_collection(
    name = "first_test_executables",
    executables = [
        "//grace/demos/ros2_demos/demo_nodes_cpp:{}".format(exec)
        for exec in TEST_EXECUTABLES
    ],
)

# limitation: The tests are currently only generated for one rmw implementation
[cmake_configure_file(
    name = "generate_" + exec,
    src = "test_executables_tutorial.py.in",
    out = "test_{}.py".format(exec),
    defines = {
        "DEMO_NODES_CPP_EXPECTED_OUTPUT": "grace/demos/ros2_demos/demo_nodes_cpp/test/{}".format(exec),
        "DEMO_NODES_CPP_EXECUTABLE": exec,
        "rmw_implementation": "ida",
    },
    strict = True,
) for exec in TEST_EXECUTABLES]

[launch_test(
    name = "test_" + exec,
    data = [
        ":{}".format(exec + ".txt" if exec != "listener" else "listener.regex"),
        ":first_test_executables",
    ],
    env = {"APEX_IDA_LOG_LEVEL": "FATAL"},
    flaky = True,
    launch_test_file = "test_{}.py".format(exec),
    local = True,  # TODO: figure out why RBE is failing
    ros_domain_id_isolation = True,
    tags = ["constrained_test"] + (["skip_coverage"] if "test_parameter_events_async" == "test_" + exec else []),
    deps = [
        "@apex//grace/ros/rclpy/rclpy",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//grace/tools/launch_ros/launch_testing_ros",
        "@apex//tools/apex_pytest_utils",
        "@apex//tools/launch/launch",
        requirement("pytest"),
    ],
) for exec in TEST_EXECUTABLES]

# note: we have to hand over two test targets at once to cmake_configure_file and launch_test
# The python test will then split them up accordingly
TEST_EXECUTABLES_RECEIVER_SENDER = [
    ("listener", "talker"),
    ("add_two_ints_server", "add_two_ints_client_async"),
    ("add_two_ints_server", "add_two_ints_client"),
]

TEST_EXECUTABLES_RECEIVER_SENDER_IDS = [
    "7",
    "8",
    "9",
]

executables_collection(
    name = "second_test_executables",
    executables = remove_duplicates([
        "//grace/demos/ros2_demos/demo_nodes_cpp:{}".format(exec)
        for item in TEST_EXECUTABLES_RECEIVER_SENDER
        for exec in item
    ]),
)

# We iterate through the test ids and tutorial_test_two_binaries in parallel
[cmake_configure_file(
    name = "gen_{}_{}".format(sender, receiver),
    src = "test_executables_tutorial.py.in",
    out = "test_{}_{}.py".format(sender, receiver),
    defines = {
        "DEMO_NODES_CPP_EXPECTED_OUTPUT": "grace/demos/ros2_demos/demo_nodes_cpp/test/{};grace/demos/ros2_demos/demo_nodes_cpp/test/{}".format(receiver, sender),
        "DEMO_NODES_CPP_EXECUTABLE": "{};{}".format(receiver, sender),
        "rmw_implementation": "ida",
        "ID_SUFFIX": id,
    },
    strict = True,
) for id, (receiver, sender) in zip(TEST_EXECUTABLES_RECEIVER_SENDER_IDS, TEST_EXECUTABLES_RECEIVER_SENDER)]

[launch_test(
    name = "test_" + sender + "_" + receiver,
    data = [
        "{}.txt".format(sender),
        "{}".format(receiver + ".txt" if receiver != "listener" else "listener.regex"),
        ":second_test_executables",
        "//grace/demos/ros2_demos/demo_nodes_cpp:{}".format(sender),
        "//grace/demos/ros2_demos/demo_nodes_cpp:{}".format(receiver),
    ],
    env = {"APEX_IDA_LOG_LEVEL": "FATAL"},
    flaky = True,
    launch_test_file = "test_{}_{}.py".format(sender, receiver),
    local = True,  # TODO: figure out why RBE is failing
    ros_domain_id_isolation = True,
    tags = ["constrained_test"],
    deps = [
        "@apex//grace/ros/rclpy/rclpy",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//grace/tools/launch_ros/launch_testing_ros",
        "@apex//tools/apex_pytest_utils",
        "@apex//tools/launch/launch",
        requirement("pytest"),
    ],
) for receiver, sender in TEST_EXECUTABLES_RECEIVER_SENDER]
