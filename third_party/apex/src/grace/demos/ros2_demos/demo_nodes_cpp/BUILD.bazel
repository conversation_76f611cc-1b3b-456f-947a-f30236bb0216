load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//grace/ros/rclcpp/rclcpp_components/bazel:defs.bzl", "rclcpp_component_binary")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_rclcpp_component")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

apex_cc_library(
    name = "headers",
    hdrs = ["include/demo_nodes_cpp/visibility_control.h"],
    strip_include_prefix = "include",
)

demo_nodes_deps = [
    ":headers",
    "//grace/examples/example_interfaces",
    "//grace/interfaces/std_msgs",
    "//grace/ros/rclcpp/rclcpp",
    "//grace/ros/rclcpp/rclcpp_components:component_manager",
    "//grace/ros/rcutils",
]

apex_cc_library(
    name = "one_off_timer_lib",
    srcs = ["src/timers/one_off_timer.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "reuse_timer_lib",
    srcs = ["src/timers/reuse_timer.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "add_two_ints_client_async_lib",
    srcs = ["src/services/add_two_ints_client_async.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "add_two_ints_server_lib",
    srcs = ["src/services/add_two_ints_server.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "even_parameters_lib",
    srcs = ["src/parameters/even_parameters_node.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "list_parameters_lib",
    srcs = ["src/parameters/list_parameters.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "parameter_blackboard_lib",
    srcs = ["src/parameters/parameter_blackboard.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "parameter_events_async_lib",
    srcs = ["src/parameters/parameter_events_async.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "set_and_get_parameters_lib",
    srcs = ["src/parameters/set_and_get_parameters.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "listener_lib",
    srcs = ["src/topics/listener.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "listener_best_effort_lib",
    srcs = ["src/topics/listener_best_effort.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "serialized_message_listener_lib",
    srcs = ["src/topics/listener_serialized_message.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "talker_lib",
    srcs = ["src/topics/talker.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "talker_loaned_message_lib",
    srcs = ["src/topics/talker_loaned_message.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

apex_cc_library(
    name = "talker_serialized_message_lib",
    srcs = ["src/topics/talker_serialized_message.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = demo_nodes_deps,
)

DEMO_COMPONENTS = {
    # Timers
    "demo_nodes_cpp::OneOffTimerNode": ":one_off_timer_lib",
    "demo_nodes_cpp::ReuseTimerNode": ":reuse_timer_lib",
    # Services,
    "demo_nodes_cpp::ClientNode": ":add_two_ints_client_async_lib",
    "demo_nodes_cpp::ServerNode": ":add_two_ints_server_lib",
    # Parameters,
    "demo_nodes_cpp::EvenParameterNode": ":even_parameters_lib",
    "demo_nodes_cpp::ListParameters": ":list_parameters_lib",
    "demo_nodes_cpp::ParameterBlackboard": ":parameter_blackboard_lib",
    "demo_nodes_cpp::ParameterEventsAsyncNode": ":parameter_events_async_lib",
    "demo_nodes_cpp::SetAndGetParameters": ":set_and_get_parameters_lib",
    # Topics,
    "demo_nodes_cpp::Listener": ":listener_lib",
    "demo_nodes_cpp::ListenerBestEffort": ":listener_best_effort_lib",
    "demo_nodes_cpp::SerializedMessageListener": ":serialized_message_listener_lib",
    "demo_nodes_cpp::Talker": ":talker_lib",
    "demo_nodes_cpp::LoanedMessageTalker": ":talker_loaned_message_lib",
    "demo_nodes_cpp::SerializedMessageTalker": ":talker_serialized_message_lib",
}

ament_rclcpp_component(
    name = "demo_nodes_cpp",
    package = "demo_nodes_cpp",
    plugins = DEMO_COMPONENTS,
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
)

[
    rclcpp_component_binary(
        name = library.strip(":").rstrip(r"\_lib"),
        apex_cc_library = library,
        component = ":demo_nodes_cpp",
        plugin = plugin,
    )
    for plugin, library in DEMO_COMPONENTS.items()
]

cc_binary(
    name = "allocator_tutorial",
    srcs = ["src/topics/allocator_tutorial.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "list_parameters_async",
    srcs = ["src/parameters/list_parameters_async.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "parameter_events",
    srcs = ["src/parameters/parameter_events.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "parameter_event_handler",
    srcs = ["src/parameters/parameter_event_handler.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "set_and_get_parameters_async",
    srcs = ["src/parameters/set_and_get_parameters_async.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "add_two_ints_client",
    srcs = ["src/services/add_two_ints_client.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/examples/example_interfaces",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

ros_pkg(
    name = "demo_nodes_cpp_pkg",
    cc_libraries = DEMO_COMPONENTS.values(),
    description = "C++ nodes which were previously in the ros2/examples repository but are now just used for demo purposes.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "William Woodall",
    pkg_name = "demo_nodes_cpp",
    version = "0.14.2",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/examples/example_interfaces:example_interfaces_pkg",
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/ros/rclcpp/rclcpp_components:rclcpp_components_pkg",
        "//grace/ros/rcutils:rcutils_pkg",
    ],
)

filegroup(
    name = "referenced_srcs",
    srcs = [
        "launch/topics/talker_listener.launch.py",
    ],
    visibility = ["//grace/configuration/process_manager/doc:__pkg__"],
)
