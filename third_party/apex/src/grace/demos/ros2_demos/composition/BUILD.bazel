load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_shared_library")
load("@apex//grace/ros/rclcpp/rclcpp_components/bazel:defs.bzl", "rclcpp_component_binary")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_rclcpp_component")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//common/bazel/rules_deployment:defs.bzl", "configured_binary")

ros_pkg(
    name = "composition_pkg",
    cc_libraries = [
        ":headers",
        ":talker_lib",
        ":listener_lib",
        ":node_like_listener_lib",
        ":server_lib",
        ":client_lib",
    ],
    description = "Examples for composing multiple nodes in a single process.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "composition",
    version = "0.14.2",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/examples/example_interfaces:example_interfaces_pkg",
        "@apex//grace/interfaces/std_msgs:std_msgs_pkg",
        "@apex//grace/ros/class_loader:class_loader_pkg",
        "@apex//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "@apex//grace/ros/rclcpp/rclcpp_components:rclcpp_components_pkg",
    ],
)

apex_cc_library(
    name = "headers",
    hdrs = glob(["include/**/*"]),
    strip_include_prefix = "include",
    deps = [
        "@apex//grace/examples/example_interfaces",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@apex//grace/ros/rclcpp/rclcpp_components:component_manager",
    ],
)

apex_cc_library(
    name = "talker_lib",
    srcs = ["src/talker_component.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

apex_cc_library(
    name = "listener_lib",
    srcs = ["src/listener_component.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

apex_cc_library(
    name = "node_like_listener_lib",
    srcs = ["src/node_like_listener_component.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

apex_cc_library(
    name = "server_lib",
    srcs = ["src/server_component.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

apex_cc_library(
    name = "client_lib",
    srcs = ["src/client_component.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [":headers"],
)

ament_rclcpp_component(
    name = "composition",
    package = "composition",
    plugins = {
        "composition::Talker": ":talker_lib",
        "composition::Listner": ":listener_lib",
        "composition::NodeLikeListenerComponent": ":node_like_listener_lib",
        "composition::Server": ":server_lib",
        "composition::Client": ":client_lib",
    },
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
)

configured_binary(
    name = "cli_composition",
    data = [":composition"],
    executable = "@apex//grace/ros/rclcpp/rclcpp_components:component_container",
    visibility = [":__subpackages__"],
)

# See: https://docs.ros.org/en/galactic/Tutorials/Intermediate/Composition.html#compile-time-composition-using-ros-services
# Run directly:
# $ bazel run @apex//grace/demos/ros2_demos/composition:manual_composition
cc_binary(
    name = "manual_composition",
    srcs = ["src/manual_composition.cpp"],
    visibility = [":__subpackages__"],
    deps = [
        ":client_lib",
        ":listener_lib",
        ":node_like_listener_lib",
        ":server_lib",
        ":talker_lib",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

# Run directly:
# $ bazel run @apex//grace/demos/ros2_demos/composition:linktime_composition
apex_cc_shared_library(
    name = "talker_shared",
    apex_cc_library = ":talker_lib",
)

apex_cc_shared_library(
    name = "listener_shared",
    apex_cc_library = ":listener_lib",
)

apex_cc_shared_library(
    name = "server_shared",
    apex_cc_library = ":server_lib",
)

apex_cc_shared_library(
    name = "client_shared",
    apex_cc_library = ":client_lib",
)

cc_binary(
    name = "linktime_composition",
    srcs = ["src/linktime_composition.cpp"],
    dynamic_deps = [
        ":talker_shared",
        ":listener_shared",
        ":server_shared",
        ":client_shared",
    ],
    visibility = [":__subpackages__"],
    deps = [
        ":client_lib",
        ":listener_lib",
        ":server_lib",
        ":talker_lib",
        "@apex//grace/ros/class_loader",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@apex//grace/ros/rclcpp/rclcpp_components:node_factory",
    ],
)

# See: https://docs.ros.org/en/galactic/Tutorials/Intermediate/Composition.html#run-time-composition-using-dlopen
# Then run and provide paths to libraries to be loaded:
# $ bazel run @apex//grace/demos/ros2_demos/composition:dlopen_composition -- \
#   .../bazel-bin/grace/demos/ros2_demos/composition/dlopen_composition.runfiles/ros_runtime/lib/libtalker_component.so
cc_binary(
    name = "dlopen_composition",
    srcs = ["src/dlopen_composition.cpp"],
    data = [":composition"],
    visibility = [":__subpackages__"],
    deps = [
        ":headers",
        "@apex//grace/ros/class_loader",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@apex//grace/ros/rclcpp/rclcpp_components:node_factory",
    ],
)

rclcpp_component_binary(
    name = "talker",
    apex_cc_library = ":talker_lib",
    component = "composition",
    plugin = "composition::Talker",
)

filegroup(
    name = "doc_files",
    srcs = glob([
        "BUILD.bazel",
        "src/manual_composition.cpp",
    ]),
    visibility = ["//grace/ros/rclcpp/rclcpp_components/doc:__subpackages__"],
)
