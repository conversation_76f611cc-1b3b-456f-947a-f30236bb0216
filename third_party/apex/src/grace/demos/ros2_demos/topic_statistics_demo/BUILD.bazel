load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "topic_statistics_demo_pkg",
    description = "C++ demo application for topic statistics feature.",
    lib_executables = [
        ":display_topic_statistics",
    ],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "topic_statistics_demo",
    version = "0.14.2",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/interfaces/sensor_msgs:sensor_msgs_pkg",
        "//grace/interfaces/statistics_msgs:statistics_msgs_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/ros/rcutils:rcutils_pkg",
    ],
)

cc_binary(
    name = "display_topic_statistics",
    srcs = glob([
        "src/*.cpp",
        "include/**/*.hpp",
    ]),
    includes = ["include"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/interfaces/sensor_msgs",
        "//grace/interfaces/statistics_msgs",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rcutils",
    ],
)
