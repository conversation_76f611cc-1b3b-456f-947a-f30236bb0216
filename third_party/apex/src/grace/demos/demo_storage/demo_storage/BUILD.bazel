load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_pkg//:mappings.bzl", "pkg_files")

# ""
package(default_visibility = ["//visibility:public"])

# TODO: add ros2topic and ros2pkg to deps
ros_pkg(
    name = "demo_storage_pkg",
    description = "Demo applications for Apex.OS storage",
    lib_executables = [
        "pipeline_parameters_ros_prototype",
        "pipeline_parameters_ros_production",
        "pipeline_parameters_apex_prototype",
        "pipeline_parameters_apex_production",
        "events_storage",
    ],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    share_data = [":share_data"],
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/cpputils:cpputils_pkg",
        "//common/interrupt:interrupt_pkg",
        "//common/process:process_pkg",
        "//common/threading:threading_pkg",
        "//grace/configuration/storage/storage:storage_pkg",
        "//grace/demos/demo_storage/demo_storage_msgs:demo_storage_msgs_pkg",
        "//grace/execution/apex_init:apex_init_pkg",
        "//grace/execution/executor2:executor2_pkg",
        "//grace/execution/timer_service:timer_service_pkg",
        "//grace/monitoring/logging:logging_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
    ],
)

pkg_files(
    name = "share_data",
    srcs = glob([
        "scripts/*.yaml",
        "param/*.yaml",
    ]),
)

cc_binary(
    name = "pipeline_parameters_ros_prototype",
    srcs = ["src/pipeline_parameters_ros_prototype.cpp"],
    tags = ["exclude_sca"],
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    deps = [
        "//common/configuration/settings",
        "//common/interrupt",
        "//grace/configuration/storage/storage",
        "//grace/execution/apex_init",
        "//grace/interfaces/std_msgs",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "pipeline_parameters_ros_production",
    srcs = ["src/pipeline_parameters_ros_production.cpp"],
    tags = ["exclude_sca"],
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    deps = [
        "//common/configuration/settings",
        "//common/interrupt",
        "//grace/configuration/storage/storage",
        "//grace/execution/apex_init",
        "//grace/interfaces/std_msgs",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "pipeline_parameters_apex_prototype",
    srcs = ["src/pipeline_parameters_apex_prototype.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/configuration/settings",
        "//common/interrupt",
        "//grace/configuration/storage/storage",
        "//grace/demos/demo_storage/demo_storage_msgs",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/std_msgs",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "pipeline_parameters_apex_production",
    srcs = ["src/pipeline_parameters_apex_production.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/configuration/settings",
        "//common/interrupt",
        "//grace/configuration/storage/storage",
        "//grace/demos/demo_storage/demo_storage_msgs",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/std_msgs",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "events_storage",
    srcs = ["src/events_storage.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//common/configuration/settings",
        "//common/interrupt",
        "//grace/configuration/storage/storage",
        "//grace/demos/demo_storage/demo_storage_msgs",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/std_msgs",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "scripts/start_parameters_demo.sh",
        "src/events_storage.cpp",
        "src/pipeline_parameters_apex_production.cpp",
        "src/pipeline_parameters_apex_prototype.cpp",
        "src/pipeline_parameters_ros_production.cpp",
        "src/pipeline_parameters_ros_prototype.cpp",
    ],
    visibility = ["//grace/demos/demo_storage/demo_storage/design:__subpackages__"],
)

#TODO: add integration test
