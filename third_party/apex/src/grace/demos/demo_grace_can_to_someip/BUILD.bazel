load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//ida/bazel:defs.bzl", "socketcan_gateway", "vsomeip_gateway")
load("@apex//tools/bazel/rules_containers:defs.bzl", "run_in_containers")

# 1. CAN related targets
msgs_library(
    name = "dbc",
    dbc_srcs = ["databases/example.dbc"],
    pkg_name = "dbc_msgs",
)

socketcan_gateway(
    name = "can_gateway",
    config = ":config/mapping.yaml",
    deps = [":dbc"],
)

# 2. SOME/IP related targets
msgs_library(
    name = "example_interfaces",
    arxml_srcs = glob(["msg/*.arxml"]),
    pkg_name = "example_msgs",
)

vsomeip_gateway(
    name = "host1_someip_gateway",
    config = "config/host1.vsomeip_gateway.yaml",
    deps = [":example_interfaces"],
)

vsomeip_gateway(
    name = "host2_someip_gateway",
    config = "config/host2.vsomeip_gateway.yaml",
    deps = [":example_interfaces"],
)

# 3. SSG Node (topic mapper CAN-DDS -> SOME/IP-DDS)
cc_binary(
    name = "ssg_server_node",
    srcs = ["ssg_server_node.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":dbc",
        ":example_interfaces",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

# 4. CAN application using direct socketCAN access to send CAN message A
cc_binary(
    name = "can_sender",
    srcs = ["socketcan_main.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        "//ida/common",
        "//ida/connectors/can/socketcan_gateway/socketcan",
    ],
)

# 5. SOME/IP application using someip_gateway to receive SOME/IP message
cc_binary(
    name = "someip_subscriber",
    srcs = ["someip_subscriber.cpp"],
    deps = [
        ":example_interfaces",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

process_manager(
    name = "host1_process_manager",
    data = [
        ":can_gateway",
        ":can_sender",
        ":host1_someip_gateway",
        ":ssg_server_node",
    ],
    launch_file = "config/host1.launch.yaml",
)

process_manager(
    name = "host2_process_manager",
    data = [
        ":host2_someip_gateway",
        ":someip_subscriber",
    ],
    launch_file = "config/host2.launch.yaml",
)

run_in_containers(
    name = "demo",
    compose_file = "config/docker-compose.yml",
    enable_tmux = True,
    host_executable_map = {
        "host1": ":host1_process_manager",
        "host2": ":host2_process_manager",
    },
)
