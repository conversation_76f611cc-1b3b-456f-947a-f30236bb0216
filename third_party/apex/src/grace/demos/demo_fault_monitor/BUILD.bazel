load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_pkg//:mappings.bzl", "pkg_files")

# ""
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "demo_fault_monitor_pkg",
    description = "Demo usage of the fault monitors.",
    lib_executables = [
        "demo_fault_monitor_exe",
        "fault_event_printer_exe",
    ],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "demo_fault_monitor",
    share_data = [":share_data"],
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/cpputils:cpputils_pkg",
        "@apex//common/interrupt:interrupt_pkg",
        "@apex//grace/automotive_diagnose/common:diagnostic_common_pkg",
        "@apex//grace/automotive_diagnose/diagnostic_fault_manager_msgs:diagnostic_fault_manager_msgs_pkg",
        "@apex//grace/automotive_diagnose/fault_monitor:fault_monitor_pkg",
        "@apex//grace/execution/apex_init:apex_init_pkg",
        "@apex//grace/execution/executor2:executor2_pkg",
        "@apex//grace/interfaces/sensor_msgs:sensor_msgs_pkg",
        "@apex//grace/monitoring/logging:logging_pkg",
        "@apex//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
    ],
)

pkg_files(
    name = "share_data",
    srcs = [
        "launch/demo_fault_monitor.launch.yaml",
        "param/default_monitors_setting.param.yaml",
        "recording",
    ],
)

cc_library(
    name = "demo_fault_monitor_lib",
    srcs = [
        "src/bringup.cpp",
        "src/config_parser.cpp",
        "src/lidar_data_handler.cpp",
        "src/utils/driving_cycle_publisher.cpp",
        "src/utils/lidar_data_listener.cpp",
    ],
    hdrs = [
        "include/demo_fault_monitor/bringup.hpp",
        "include/demo_fault_monitor/config_parser.hpp",
        "include/demo_fault_monitor/lidar_data_handler.hpp",
        "include/demo_fault_monitor/utils/driving_cycle_publisher.hpp",
        "include/demo_fault_monitor/utils/lidar_data_listener.hpp",
    ],
    copts = ["-std=c++17"],
    strip_include_prefix = "include",
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/cpputils",
        "@apex//grace/automotive_diagnose/common",
        "@apex//grace/automotive_diagnose/diagnostic_fault_manager_msgs",
        "@apex//grace/automotive_diagnose/fault_monitor",
        "@apex//grace/interfaces/sensor_msgs",
    ],
)

cc_library(
    name = "fault_event_printer_lib",
    srcs = [
        "src/utils/printer/fault_event_printer.cpp",
    ],
    hdrs = [
        "include/demo_fault_monitor/utils/printer/fault_event_printer.hpp",
    ],
    copts = ["-std=c++17"],
    strip_include_prefix = "include",
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/cpputils",
        "@apex//grace/automotive_diagnose/common",
        "@apex//grace/automotive_diagnose/diagnostic_fault_manager_msgs",
        "@apex//grace/monitoring/logging",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "demo_fault_monitor_exe",
    srcs = [
        "src/main.cpp",
    ],
    copts = ["-std=c++17"],
    tags = ["exclude_sca"],
    deps = [
        ":demo_fault_monitor_lib",
        "@apex//common/cpputils",
        "@apex//common/interrupt",
        "@apex//grace/automotive_diagnose/common",
        "@apex//grace/automotive_diagnose/fault_monitor",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/monitoring/logging",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "fault_event_printer_exe",
    srcs = [
        "src/utils/printer/printer_main.cpp",
    ],
    copts = ["-std=c++17"],
    tags = ["exclude_sca"],
    deps = [
        ":fault_event_printer_lib",
        "@apex//common/cpputils",
        "@apex//common/interrupt",
        "@apex//grace/automotive_diagnose/common",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/monitoring/logging",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

ament_pkg_resources(
    name = "demo_fault_monitor_resources",
    package = "demo_fault_monitor",
    resources = {
        ":demo_fault_monitor_exe": "executable",
        ":fault_event_printer_exe": "executable",
        "launch/demo_fault_monitor.launch.yaml": "share",
        "param/default_monitors_setting.param.yaml": "share",
    },
    visibility = ["//visibility:public"],
)

filegroup(
    name = "doc_files",
    srcs = [
        "param/default_monitors_setting.param.yaml",
    ],
    visibility = ["//grace/demos/demo_fault_monitor/doc:__subpackages__"],
)
