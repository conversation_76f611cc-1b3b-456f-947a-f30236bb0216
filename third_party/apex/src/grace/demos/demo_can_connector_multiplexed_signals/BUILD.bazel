load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//ida/bazel:defs.bzl", "socketcan_gateway")

# ""
package(default_visibility = ["//visibility:public"])

msgs_library(
    name = "multiplexed_signals_msgs",
    dbc_srcs = ["databases/demo.dbc"],
    pkg_name = "multiplexed_signals_msgs",
)

socketcan_gateway(
    name = "command_gateway",
    config = ":config/command_mapping.yaml",
    deps = [":multiplexed_signals_msgs"],
)

cc_library(
    name = "messages",
    srcs = ["messages.cpp"],
    hdrs = ["messages.hpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:private"],
    deps = [
        ":multiplexed_signals_msgs",
    ],
)

cc_binary(
    name = "command_service",
    srcs = ["command_service.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":messages",
        ":multiplexed_signals_msgs",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

socketcan_gateway(
    name = "status_gateway",
    config = ":config/status_mapping.yaml",
    deps = [":multiplexed_signals_msgs"],
)

cc_binary(
    name = "status_service",
    srcs = ["status_service.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":messages",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

sh_binary(
    name = "demo_can_connector_multiplexed_signals",
    srcs = ["run_example.sh"],
    data = [
        ":command_gateway",
        ":command_service",
        ":status_gateway",
        ":status_service",
        ":tmux.conf",
        "@apex//ida/resource_creator",
    ],
    visibility = ["//visibility:public"],
)
