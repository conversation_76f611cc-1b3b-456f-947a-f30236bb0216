load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//ida/bazel:defs.bzl", "socketcan_gateway")

# ""
package(default_visibility = ["//visibility:public"])

#! [grace_msgs_library]
msgs_library(
    name = "ping_pong_msgs",  # (1)!
    dbc_srcs = ["databases/demo.dbc"],  # (2)!
    pkg_name = "ping_pong_msgs",
)
#! [grace_msgs_library]

socketcan_gateway(
    name = "ping_gateway",
    config = ":config/ping_mapping.yaml",
    deps = [":ping_pong_msgs"],
)

cc_binary(
    name = "ping_service",
    srcs = ["ping.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":ping_pong_msgs",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

socketcan_gateway(
    name = "pong_gateway",
    config = ":config/pong_mapping.yaml",
    deps = [":ping_pong_msgs"],
)

cc_binary(
    name = "pong_service",
    srcs = ["pong.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":ping_pong_msgs",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

sh_binary(
    name = "demo_grace_can_connector",
    srcs = ["run_example.sh"],
    data = [
        ":ping_gateway",
        ":ping_service",
        ":pong_gateway",
        ":pong_service",
        ":tmux.conf",
        "@apex//ida/resource_creator",
    ],
    visibility = ["//visibility:public"],
)

filegroup(
    name = "doc_files",
    srcs = glob(["*.cpp"]) + [
        "BUILD.bazel",
        "config/ping_mapping.yaml",
        "config/pong_mapping.yaml",
    ],
    visibility = ["//grace/connectors/can:__subpackages__"],
)
