load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//ida/bazel:defs.bzl", "socketcan_gateway")

# ""
package(default_visibility = ["//visibility:public"])

msgs_library(
    name = "dbc",
    dbc_srcs = ["databases/example.dbc"],
    pkg_name = "dbc",
)

socketcan_gateway(
    name = "combining_signals_gateway",
    config = ":config/mapping.yaml",
    deps = [":dbc"],
)

msgs_library(
    name = "combined_topics",
    srcs = ["msg/combined_topics.idl"],
    pkg_name = "demo_grace_combining_signals_msgs",
)

cc_binary(
    name = "combiner_node",
    srcs = ["combiner_node.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":combined_topics",
        ":dbc",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "receiver_app",
    srcs = ["receiver_app.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":combined_topics",
        ":dbc",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

# An application using direct socketCAN access to send message A and B
cc_binary(
    name = "can_sender",
    srcs = ["socketcan_main.cpp"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        "//ida/common",
        "//ida/connectors/can/socketcan_gateway/socketcan",
    ],
)

sh_binary(
    name = "demo_grace_combining_signals",
    srcs = ["run_example.sh"],
    data = [
        ":can_sender",
        ":combiner_node",
        ":combining_signals_gateway",
        ":receiver_app",
        ":tmux.conf",
        "@apex//ida/resource_creator",
    ],
    visibility = ["//visibility:public"],
)
