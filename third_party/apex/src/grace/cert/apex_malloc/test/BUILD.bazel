load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")

# TODO (alfonso.ros): Missing support for VectorCAST build

cc_library(
    name = "some_other_library",
    testonly = True,
    srcs = [
        "some_other_class.cpp",
    ],
    hdrs = [
        "some_other_class.hpp",
    ],
    # TODO(kyle.marcey): #24903 Remove line below
    copts = ["-Wno-error"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:private"],
)

cc_library(
    name = "some_library",
    testonly = True,
    srcs = [
        "some_class.cpp",
    ],
    hdrs = [
        "some_class.hpp",
    ],
    # TODO(kyle.marcey): #24903 Remove line below
    copts = ["-Wno-error"],
    tags = ["exclude_sca"],
    visibility = ["//visibility:private"],
    deps = [
        ":some_other_library",
    ],
)

filegroup(
    name = "test_apex_malloc_srcs_with_req_ids",
    srcs = ["apex_malloc_test.cpp"],
    visibility = ["//grace/cert/apex_malloc/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_apex_malloc",
    srcs = [
        "apex_malloc_test.hpp",
        ":test_apex_malloc_srcs_with_req_ids",
    ],
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
    ],
    deps = [
        ":some_library",
        "//grace/cert/apex_malloc",
    ],
)

apex_cc_test(
    name = "test_apex_malloc_error_malloc",
    srcs = [
        "apex_malloc_test.hpp",
        "test_apex_malloc_error_malloc.cpp",
    ],
    # TODO(kyle.marcey): #24903 Remove line below
    copts = ["-Wno-error"],
    tags = [
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
    ],
    deps = [
        ":some_library",
        "//grace/cert/apex_malloc",
    ],
)

apex_cc_test(
    name = "test_apex_malloc_error_calloc",
    srcs = [
        "apex_malloc_test.hpp",
        "test_apex_malloc_error_calloc.cpp",
    ],
    deps = [
        "//grace/cert/apex_malloc",
    ],
)
