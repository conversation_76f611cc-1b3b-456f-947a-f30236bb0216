/// \copyright Copyright 2024 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file contains API for 'execution_monitor_service'

#ifndef EXECUTION_MONITOR_SERVICE__EXECUTION_MONITOR_SERVICE_HPP_
#define EXECUTION_MONITOR_SERVICE__EXECUTION_MONITOR_SERVICE_HPP_

#include <memory>
#include <string>
#include <tuple>
#include <utility>

#include "allocator/memory_pool_resource.hpp"
#include "allocator/pmr/map.hpp"
#include "allocator/pmr/set.hpp"
#include "containers/static_vector.hpp"
#include "dispatcher_interfaces/srv/event_handler.hpp"
#include "execution_monitor/backend.hpp"
#include "execution_monitor/task_identifier.hpp"
#include "execution_monitor_service/visibility.hpp"
#include "executor2/executable_item.hpp"
#include "logging/logging.hpp"
#include "process_manager_interfaces/msg/process_meta_data.hpp"
#include "rclcpp/rclcpp.hpp"
#include "timer_service/timer_service.hpp"

#include "execution_monitor_msgs/msg/infraction.hpp"
#include "execution_monitor_msgs/srv/register_execution_monitor.hpp"
#include "execution_monitor_msgs/srv/unregister_execution_monitor.hpp"

namespace apex::execution_monitor
{
namespace components
{
/// \brief The infraction message type
using infraction_msg_t = execution_monitor_msgs::msg::Infraction;
/// \brief The registration service type
using registration_srv_t = execution_monitor_msgs::srv::RegisterExecutionMonitor;
/// \brief The unregistration service type
using unregistration_srv_t = execution_monitor_msgs::srv::UnregisterExecutionMonitor;

// Conservatively assume that each monitor will have a full allotment of 256 tasks.
const std::size_t MAX_TASKS_PER_MONITOR = registration_srv_t::Request::_tasks_type::capacity();

class EXECUTION_MONITOR_SERVICE_PUBLIC backend_item : public apex::executor::executable_item
{
public:
  /// \brief Creates a backend executable item
  /// \param node The main service node
  /// \param timer The timer for cyclical execution
  /// \param instance_name The name of the execution monitor service instance
  /// \param infraction_topic_name topic to publish infractions on
  /// \param max_num_monitors The maximum number of execution monitors that can be
  /// handled by the backend
  /// \param max_infraction_history_depth History depth for infraction publisher
  backend_item(rclcpp::Node & node,
               timer_service::timer_ptr timer,
               std::string instance_name,
               const apex::string_strict256_t & infraction_topic_name,
               std::size_t max_num_monitors,
               std::size_t max_infraction_history_depth);

  /// \brief Unregisters all execution monitors of a certain process
  /// \param pid Specifies the pid of the process to unregister the monitors from
  /// \cert
  /// \deterministic
  void unregister_monitors(std::int32_t pid);

private:
  friend class com_item;

  /// \brief Returns the instance name
  /// \cert
  /// \deterministic
  const std::string & get_instance_name() const noexcept
  {
    return m_instance_name;
  }

  /// \brief Returns the related application id
  /// \cert
  /// \deterministic
  std::string_view get_app_id_impl() const noexcept override;

  /// \brief Tests for infractions
  /// \cert
  /// \deterministic
  bool execute_impl() override;

  /// \brief Processes execution monitor registration request helper
  /// \param req The registration request
  /// \param resp The registration response
  /// \cert
  /// \deterministic
  void register_tasks(const registration_srv_t::Request & req,
                      registration_srv_t::Response::BorrowedType & resp);

  /// \brief Store additional task info from the registration request
  /// that this service uses to provide more human-readable context to infraction
  /// messages
  /// \param req The registration request
  /// \cert
  /// \deterministic
  void store_task_info(const registration_srv_t::Request & req);

  /// \brief Inspects the registered expectations and reports any infractions
  /// \cert
  /// \deterministic
  void inspect_execution_monitor_deadlines();

  /// \brief Returns the timer subscription
  /// \cert
  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {m_timer->to_sub_ptr()};
  }

  /// \brief Unregisters an execution monitor of a certain process
  /// \param pid Specifies the pid of the process to unregister the monitors from
  /// \param monitor_name Name of the monitor to unregister
  /// \cert
  /// \deterministic
  void unregister_monitor(std::int32_t pid, const apex::string256_t & monitor_name);

  /// \brief The timer to run on
  timer_service::timer_ptr m_timer;

  /// \brief The instance name
  const std::string m_instance_name;

  /// \brief The publisher for discovered infractions
  const rclcpp::Publisher<infraction_msg_t>::SharedPtr m_infraction_pub;

  /// \brief The execution monitor backend
  backend m_backend;

  struct metadata_comparator
  {
    bool operator()(const process_manager_interfaces::msg::ProcessMetaData & left,
                    const process_manager_interfaces::msg::ProcessMetaData & right) const noexcept
    {
      return std::tie(left.group_name, left.process_name, left.id) <
             std::tie(right.group_name, right.process_name, right.id);
    }
  };

  using metadata_set_t = apex::allocator::pmr::set<process_manager_interfaces::msg::ProcessMetaData,
                                                   metadata_comparator>;
  apex::allocator::pmr::memory_pool_resource m_task_metadata_memres;
  metadata_set_t m_process_metadata{m_task_metadata_memres};

  /// \brief Additional information for each registered task, which is useful for human-readable
  /// error messages
  struct execution_monitor_task_info final
  {
    apex::static_vector<apex::string256_t> expectations{256U};
    metadata_set_t::iterator metadata_iter;
    execution_monitor_task_info() = default;
    execution_monitor_task_info(const execution_monitor_task_info &) = delete;
    execution_monitor_task_info & operator=(const execution_monitor_task_info &) = delete;
  };

  std::mutex m_mutex;
  using task_to_info_map = apex::allocator::pmr::map<task_identifier, execution_monitor_task_info>;
  apex::allocator::pmr::memory_pool_resource m_task_info_memres;
  task_to_info_map m_task_info{m_task_info_memres};
  logging::LoggerBase m_logger;
};

/// \brief The communication executable item for the execution monitor service
class EXECUTION_MONITOR_SERVICE_PUBLIC com_item : public apex::executor::executable_item
{
public:
  /// \brief Creates a communication executable item
  /// \param backend The backend item to communicate with
  /// \param event_handler_srv Name of the service to unregister tasks
  /// on unexpected process failure
  com_item(backend_item & backend, const apex::string_strict256_t & event_hnd_srv_name);

private:
  /// \brief Returns the related application id
  /// \cert
  /// \deterministic
  std::string_view get_app_id_impl() const noexcept override;

  /// \brief Handles incoming requests
  /// \cert
  /// \deterministic
  bool execute_impl() override;

  /// \brief Returns the list of triggering subscriptions for the service
  /// \cert
  apex::executor::service_list get_triggering_services_impl() const override
  {
    apex::executor::service_list res{m_register_srv, m_unreg_srv};
    if (m_event_handler != nullptr) {
      res.push_back(m_event_handler);
    }
    return res;
  }

  /// \brief Processes execution monitor registrations requests
  /// \cert
  /// \deterministic
  void process_registration_request();

  /// \brief Processes execution monitor task un-registration requests
  /// \cert
  /// \deterministic
  void process_unregistration_request();

  /// \brief Cleans up tasks if the originating process is reported to be
  /// unexpectedly terminated
  /// \cert
  /// \deterministic
  void handle_unexpected_process_exit_events();

  backend_item & m_backend_item;

  /// \brief The registration service for execution monitor's expectations
  const rclcpp::PollingService<registration_srv_t>::SharedPtr m_register_srv;
  /// \brief The un-registration service for execution monitor's expectations
  const rclcpp::PollingService<unregistration_srv_t>::SharedPtr m_unreg_srv;
  /// \brief The event handler for unexpected process termination events
  const rclcpp::PollingService<dispatcher_interfaces::srv::EventHandler>::SharedPtr m_event_handler;
  logging::LoggerBase m_logger;
};
}  // namespace components

namespace detail
{
/// \brief The base node for the execution monitor service
struct exec_monitor_node_base
{
  rclcpp::Node node_base;
  explicit exec_monitor_node_base(const std::string & name) : node_base{name} {}
};
}  // namespace detail

/// \class execution_monitor_service
/// A stand-alone backend service for execution monitor.
/// It is intended to be run by the executor periodically
/// It combines the backend and communication items to run on the same execution thread
class EXECUTION_MONITOR_SERVICE_PUBLIC execution_monitor_service
: private detail::exec_monitor_node_base,
  public apex::executor::executable_item
{
public:
  /// \brief Creates a service
  /// \param timer The timer for cyclical execution
  /// \param instance_name The name of the execution monitor service instance
  /// \param event_handler_srv Name of the service to unregister tasks
  /// on unexpected process failure
  /// \param infraction_topic_name topic to publish infractions on
  /// \param max_num_monitors The maximum number of execution monitors that can be
  /// handled by the backend
  /// \param max_infraction_history_depth History depth for infraction publisher
  execution_monitor_service(timer_service::timer_ptr timer,
                            std::string instance_name,
                            const apex::string_strict256_t & event_hnd_srv_name,
                            const apex::string_strict256_t & infraction_topic_name,
                            std::size_t max_num_monitors,
                            std::size_t max_infraction_history_depth);

  /// \brief Unregisters all execution monitors of a certain process
  /// \param pid Specifies the pid of the process to unregister the monitors from
  /// \cert
  /// \deterministic
  void unregister_monitors(std::int32_t pid)
  {
    m_backend_item->unregister_monitors(pid);
  }

private:
  /// \brief Enables sending execution events
  /// \param sender The sender for using in this executable item
  /// \cert
  /// \deterministic
  void set_event_sender(apex::event::sender_ptr sender) override
  {
    m_backend_item->set_event_sender(sender);
    m_com_item->set_event_sender(sender);
    apex::executor::executable_item::set_event_sender(sender);
  }

  /// \brief Sets the executor id
  /// \param executor_id The executor id
  /// \cert
  /// \deterministic
  void set_executor_id(apex::executor::executor_id_t executor_id) noexcept override
  {
    m_backend_item->set_executor_id(executor_id);
    m_com_item->set_executor_id(executor_id);
    apex::executor::executable_item::set_executor_id(executor_id);
  }

  /// \brief Returns the related application id
  /// \cert
  /// \deterministic
  std::string_view get_app_id_impl() const noexcept override;

  /// \brief Tests for infractions and handles incoming requests
  /// \cert
  /// \deterministic
  bool execute_impl() override
  {
    (void)m_com_item->execute();
    (void)m_backend_item->execute();
    return true;
  }

  /// \brief Returns the timer subscription
  /// \cert
  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return m_backend_item->get_subscriptions(apex::executor::subscription_type::Triggering);
  }

  /// \brief Returns the list of triggering subscriptions for the service
  /// \cert
  apex::executor::service_list get_triggering_services_impl() const override
  {
    return m_com_item->get_services();
  }

  std::unique_ptr<components::backend_item> m_backend_item;
  std::unique_ptr<components::com_item> m_com_item;
};
}  // namespace apex::execution_monitor

#endif  // EXECUTION_MONITOR_SERVICE__EXECUTION_MONITOR_SERVICE_HPP_
