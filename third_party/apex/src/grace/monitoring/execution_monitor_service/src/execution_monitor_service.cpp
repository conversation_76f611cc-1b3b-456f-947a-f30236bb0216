// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#include "execution_monitor_service/execution_monitor_service.hpp"

#include <algorithm>
#include <chrono>
#include <iterator>
#include <memory>
#include <string>
#include <utility>

#include "allocator/traits/map.hpp"
#include "allocator/traits/set.hpp"
#include "execution_monitor/common.hpp"
#include "execution_monitor_service/common.hpp"
#include "execution_monitor_service/events/events.hpp"
#include "logging/logging_macros.hpp"
#include "string/to_string.hpp"

namespace apex::execution_monitor
{
execution_monitor_service::execution_monitor_service(
  timer_service::timer_ptr timer,
  std::string instance_name,
  const apex::string_strict256_t & event_hnd_srv_name,
  const apex::string_strict256_t & infraction_topic_name,
  std::size_t max_num_monitors,
  std::size_t max_infraction_history_depth)
: detail::exec_monitor_node_base{common::get_executor_service_node_name(instance_name)},
  apex::executor::executable_item{node_base},
  m_backend_item{std::make_unique<components::backend_item>(get_rclcpp_node(),
                                                            std::move(timer),
                                                            instance_name,
                                                            infraction_topic_name,
                                                            max_num_monitors,
                                                            max_infraction_history_depth)},
  m_com_item{std::make_unique<components::com_item>(*m_backend_item, event_hnd_srv_name)}
{
}

std::string_view execution_monitor_service::get_app_id_impl() const noexcept
{
  return execution_monitor_app::AppId;
}

namespace components
{
backend_item::backend_item(rclcpp::Node & node,
                           timer_service::timer_ptr timer,
                           std::string instance_name,
                           const apex::string_strict256_t & infraction_topic_name,
                           std::size_t max_num_monitors,
                           std::size_t max_infraction_history_depth)
: apex::executor::executable_item{node},
  m_timer{std::move(timer)},
  m_instance_name{std::move(instance_name)},
  m_infraction_pub{get_rclcpp_node().create_publisher<infraction_msg_t>(
    infraction_topic_name,
    rclcpp::DefaultQoS().reliable().keep_last(max_infraction_history_depth))},
  m_backend{max_num_monitors, max_num_monitors * MAX_TASKS_PER_MONITOR, m_instance_name},
  m_logger{&get_rclcpp_node(), m_instance_name}
{
  m_task_info_memres.add_buckets_for<task_to_info_map>(max_num_monitors * MAX_TASKS_PER_MONITOR);
  m_task_metadata_memres.add_buckets_for<metadata_set_t>(max_num_monitors);
}

bool backend_item::execute_impl()
{
  (void)m_timer->test_and_reset();
  inspect_execution_monitor_deadlines();
  return true;
}

void backend_item::inspect_execution_monitor_deadlines()
{
  using apex::execution_monitor::expectations::types::deviation_unit_type;
  using apex::execution_monitor::expectations::types::get_deviation_units;
  using std::chrono::duration_cast;

  // Take the time at the beginning of the inspection. This is the official cut-off time for the
  // check: anything expired by that time will be flagged. Anything expired after that time is
  // considered okay, even if the time has advanced since. It will be caught next time.
  const std::chrono::nanoseconds now{std::chrono::steady_clock::now().time_since_epoch()};

  std::unique_lock lock{m_mutex};
  for (auto segment_it = m_backend.segments_begin(); segment_it != m_backend.segments_end();
       ++segment_it) {
    const auto num_slots = segment_it->active_slots().size();
    for (std::uint32_t i = 0; i < num_slots; i++) {
      auto & slot = segment_it->active_slots()[i];
      const auto infractions = slot.inspect_deadline(now);
      if (infractions.counter > 0) {
        const auto task_offset_pair = segment_it.task_and_offset_of(i);
        const auto task_id = task_offset_pair.first;
        const auto & task_info = m_task_info.at(task_id);
        assert(task_offset_pair.second < task_info.expectations.size());
        const auto & expectation = task_info.expectations[task_offset_pair.second];
        const auto & metadata = *task_info.metadata_iter;
        auto msg = m_infraction_pub->borrow_loaned_message();
        msg->task_id = task_id.task_id;
        msg->monitor_name = task_id.monitor_name;
        msg->pid = task_id.pid;
        msg->process_manager_metadata = metadata;
        msg->expectation_type = infractions.expectation_type;
        msg->expectation = expectation;
        msg->counter = infractions.counter;
        msg->deviation = infractions.deviation;
        msg->backend_overrun_ns = infractions.backend_overrun.count();
        m_infraction_pub->publish(std::move(msg));

        auto infraction_message = apex::varargs_to_string(
          apex::no_separator{}, task_id.to_string(), ": ", expectation, " failed");

        if (infractions.deviation != 0) {
          infraction_message += apex::varargs_to_string(apex::no_separator{}, "\nDeviation: ");
          const auto units = get_deviation_units(infractions.expectation_type);
          if (units == deviation_unit_type::nanoseconds) {
            infraction_message +=
              apex::varargs_to_string(apex::no_separator{},
                                      duration_cast<std::chrono::microseconds>(
                                        std::chrono::nanoseconds{infractions.deviation})
                                        .count(),
                                      " us");
          } else if (units == deviation_unit_type::activations) {
            infraction_message +=
              apex::varargs_to_string(apex::no_separator{}, infractions.deviation, " activations");
          } else {
            infraction_message +=
              apex::varargs_to_string(apex::no_separator{}, infractions.deviation);
          }
        }

        if (infractions.backend_overrun.count() != 0) {
          infraction_message += apex::varargs_to_string(
            apex::no_separator{},
            "\nBackend overrun: ",
            duration_cast<std::chrono::microseconds>(infractions.backend_overrun).count(),
            " us @ ",
            duration_cast<std::chrono::milliseconds>(m_timer->interval()).count(),
            " ms");
        }

        if (!metadata.process_name.empty()) {
          infraction_message += apex::varargs_to_string(
            apex::no_separator{},
            "\nPM [group: " + metadata.group_name + ", proc: " + metadata.process_name +
              ", id: " + apex::to_string(metadata.id) + "]");
        }

        APEX_WARN(m_logger, apex::no_separator{}, infraction_message);
      }
    }
  }
}

void backend_item::register_tasks(const registration_srv_t::Request & req,
                                  registration_srv_t::Response::BorrowedType & resp)
{
  std::unique_lock lock{m_mutex};
  for (const auto & task_data : req.tasks) {
    const auto allocated_range = m_backend.register_execution_monitor_task(
      task_identifier{req.pid, req.monitor_name, task_data.id}, task_data.expectation_names.size());
    if (resp.shm_path_or_error_msg.empty()) {
      resp.shm_path_or_error_msg = allocated_range.filename;
    } else {
      if (resp.shm_path_or_error_msg != allocated_range.filename.data()) {
        throw backend_error{"allocation across multiple segments is not supported"};
      }
    }
    resp.allocated_ranges.emplace_back();
    resp.allocated_ranges.back().start = allocated_range.first_slot;
    resp.allocated_ranges.back().len = allocated_range.len;
  }
  store_task_info(req);
}

void backend_item::store_task_info(const registration_srv_t::Request & req)
{
  const auto iter = m_process_metadata.insert(req.process_manager_metadata).first;
  for (const auto & task_data : req.tasks) {
    const task_identifier task_id{req.pid, req.monitor_name, task_data.id};
    assert(m_task_info.find(task_id) == m_task_info.end());
    auto & info = m_task_info[task_id];
    (void)std::copy(task_data.expectation_names.begin(),
                    task_data.expectation_names.end(),
                    std::back_inserter(info.expectations));
    info.metadata_iter = iter;
    APEX_INFO(m_logger, "Task registered:", task_id.to_string());
  }
}

void backend_item::unregister_monitors(std::int32_t pid)
{
  std::unique_lock lock{m_mutex};
  auto itr = m_task_info.begin();
  while (itr != m_task_info.end()) {
    const auto & task_id = itr->first;
    if (task_id.pid == pid) {
      m_backend.unregister_execution_monitor(task_id.pid, task_id.monitor_name);
      APEX_INFO(m_logger,
                apex::no_separator{},
                "Monitor unregistered: pid: ",
                task_id.pid,
                ", monitor: ",
                task_id.monitor_name);
      itr = m_task_info.erase(itr);
    } else {
      ++itr;
    }
  }
}

void backend_item::unregister_monitor(std::int32_t pid, const apex::string256_t & monitor_name)
{
  std::unique_lock lock{m_mutex};
  m_backend.unregister_execution_monitor(pid, monitor_name);
  auto itr = m_task_info.begin();
  while (itr != m_task_info.end()) {
    const auto & task_id = itr->first;
    if ((task_id.pid == pid) && (task_id.monitor_name == monitor_name)) {
      itr = m_task_info.erase(itr);
    } else {
      itr++;
    }
  }
  APEX_INFO(m_logger,
            apex::no_separator{},
            "Monitor unregistered: pid: ",
            pid,
            ", monitor: ",
            monitor_name);
}

std::string_view backend_item::get_app_id_impl() const noexcept
{
  return execution_monitor_app::AppId;
}

com_item::com_item(backend_item & backend, const apex::string_strict256_t & event_hnd_srv_name)
: apex::executor::executable_item{backend.get_rclcpp_node()},
  m_backend_item{backend},
  m_register_srv{get_rclcpp_node().create_polling_service<registration_srv_t>(
    common::combine_name(m_backend_item.get_instance_name(), common::RegistrationServiceName))},
  m_unreg_srv{get_rclcpp_node().create_polling_service<unregistration_srv_t>(
    common::combine_name(m_backend_item.get_instance_name(), common::UnregistrationServiceName))},
  m_event_handler{
    event_hnd_srv_name.empty()
      ? nullptr
      : get_rclcpp_node().create_polling_service<dispatcher_interfaces::srv::EventHandler>(
          event_hnd_srv_name.c_str())},
  m_logger{&get_rclcpp_node(), m_backend_item.get_instance_name()}
{
}


bool com_item::execute_impl()
{
  process_unregistration_request();
  process_registration_request();
  handle_unexpected_process_exit_events();
  return true;
}

std::string_view com_item::get_app_id_impl() const noexcept
{
  return execution_monitor_app::AppId;
}

void com_item::process_registration_request()
{
  using ReturnCode = registration_srv_t::Response::BorrowedType::ReturnCode;
  const auto loaned_requests = m_register_srv->take_request();
  for (const auto & req : loaned_requests) {
    if (req.info().valid()) {
      const auto & data = req.data();
      auto hdr = req.request_header();
      auto loaned_response = m_register_srv->borrow_loaned_response();
      try {
        m_backend_item.register_tasks(data, loaned_response.get());
        loaned_response.get().retcode = ReturnCode::OK;
        const auto & backend_token_segment_name =
          m_backend_item.m_backend.get_backend_token_segment_name().as_string();
        loaned_response.get().ensure_local_backend_segment_path.assign(
          backend_token_segment_name.c_str(), backend_token_segment_name.size());
      } catch (const std::exception & e) {
        loaned_response.get().retcode = ReturnCode::ERROR;
        loaned_response.get().shm_path_or_error_msg = e.what();
        loaned_response.get().allocated_ranges.clear();
        const auto error_string = apex::varargs_to_string(apex::no_separator{},
                                                          "Error registering tasks from pid: ",
                                                          data.pid,
                                                          ", monitor: ",
                                                          data.monitor_name,
                                                          ": ",
                                                          e.what());
        APEX_WARN(m_logger, error_string);
        send_event(execution_monitor_app::event::RegistrationFailed,
                   static_cast<event::types::int_data_t>(get_executor_id()),
                   error_string);
      }
      m_register_srv->send_response(hdr, std::move(loaned_response));
    }
  }
}

void com_item::process_unregistration_request()
{
  const auto loaned_requests = m_unreg_srv->take_request();
  for (const auto & req : loaned_requests) {
    if (req.info().valid()) {
      const auto & data = req.data();
      auto hdr = req.request_header();
      auto loaned_response = m_unreg_srv->borrow_loaned_response();
      m_backend_item.unregister_monitor(data.pid, data.monitor_name);
      m_unreg_srv->send_response(hdr, std::move(loaned_response));
    }
  }
}

void com_item::handle_unexpected_process_exit_events()
{
  static const std::set unexpected_exit_events{
    apex::event::ProcessUnexpectedAbnormalExit,
    apex::event::ProcessAbnormalExit,
    apex::event::ProcessUnexpectedGracefulExit,
    apex::event::ProcessUnexpectedGracefulExitFrameworkGroup,
    apex::event::ProcessUnexpectedAbnormalExitFrameworkGroup};

  if (m_event_handler != nullptr) {
    const auto loaned_requests = m_event_handler->take_request();
    for (const auto & req : loaned_requests) {
      if (req.info().valid()) {
        const auto & event = req.data().event;
        if (unexpected_exit_events.find(event.event_id) != unexpected_exit_events.end()) {
          const auto & process_pid = event.int_data;
          const auto & process_name = event.string_data;
          if (process_pid == 0) {
            APEX_WARN(m_logger,
                      apex::no_separator{},
                      "Bad process id for process '",
                      process_name,
                      "' in handle_unexpected_process_exit_events()");
          } else {
            m_backend_item.unregister_monitors(apex::cast::safe_cast<std::int32_t>(process_pid));
          }
          auto hdr = req.request_header();
          auto rsp = m_event_handler->borrow_loaned_response();
          m_event_handler->send_response(hdr, std::move(rsp));
        }
      }
    }
  }
}
}  // namespace components
}  // namespace apex::execution_monitor
