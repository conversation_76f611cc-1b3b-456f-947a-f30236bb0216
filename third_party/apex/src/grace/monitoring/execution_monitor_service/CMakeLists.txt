# Copyright 2022 Apex.AI, Inc.
# All rights reserved.

cmake_minimum_required(VERSION 3.5)

project(execution_monitor_service)
set(CMAKE_CXX_STANDARD 17)

find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()

set(LIB_SOURCES
    src/execution_monitor_service.cpp
    include/execution_monitor_service/common.hpp
    include/execution_monitor_service/visibility.hpp
    include/execution_monitor_service/execution_monitor_service.hpp
)

ament_auto_add_library(${PROJECT_NAME} ${LIB_SOURCES})
apex_set_compile_options(${PROJECT_NAME})

generate_event_headers_for(
    ${PROJECT_NAME}
    events/events.yaml
    include/execution_monitor_service/events/events.hpp
)

ament_auto_add_executable(execution_monitor_service_exe src/execution_monitor_service_main.cpp)
apex_set_compile_options(execution_monitor_service_exe)

if(BUILD_TESTING)
    find_package(ament_lint_auto)
    list(APPEND AMENT_LINT_AUTO_EXCLUDE ament_cmake_uncrustify)
    find_package(ament_cmake_gmock REQUIRED)
    ament_lint_auto_find_test_dependencies()

    set(TEST_SOURCES test/test_execution_monitor_service.cpp)

    ament_add_gtest(test_execution_monitor_service ${TEST_SOURCES})
    target_link_libraries(test_execution_monitor_service ${PROJECT_NAME})
    apex_set_compile_options(test_execution_monitor_service)
endif()

ament_auto_package()
