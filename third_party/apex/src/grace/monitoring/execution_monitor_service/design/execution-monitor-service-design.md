# execution-monitor-service

## Purpose / Use cases

Execution monitor service is a stand-alone backend service for
[execution monitor](execution-monitor-design.md) that allows for expectation
infractions to be detected and reported even if the monitored thread deadlocks or crashes.
Multiple named instance of the execution monitor service can be run in parallel.

## Design

The service is an executable that accepts its settings in the
[usual Apex.Grace format](settings-design.md#loading-the-repository-from-command-line-arguments)
and must run before any
[execution monitor communication object](execution-monitor-design.md#the-communication-objects)
is created and keep running in the background while
[execution monitors](execution-monitor-design.md) that use it are active.

## Settings

Example:

{{ code_snippet("grace/monitoring/execution_monitor_service/param/config.yaml",
    "yaml") }}

### Communication with the client

The service name must fit the name used by the
[clients](execution-monitor-design.md#the-communication-objects).
The name can be omitted on both sides in which case the default name will be
used for the instance.

### Process-terminated handler

In order to allow for proper clean-up for unexpectedly terminated process,  
the `handlers/process-termination-handler` handler of the service must be registered with the
[event dispatcher](dispatcher-design.md) for the events of type `ProcessUnexpectedAbnormalExit` and
`ProcessAbnormalExit`.
For example, like this:

```yaml
external:
  - name: "_apex_execution_monitor_process_termination"
    timeout-ms: 1000 # Adjust according to the needs
    filter:
      events: ["ProcessUnexpectedAbnormalExit", "ProcessAbnormalExit"]
```

Note, the source of this event is the
[Process Manager](process-manager-design.md#dispatcher-event-sender) and it is
supposed to be properly configured and running to support this functionality.

### Infractions reporting

The infraction are reported on `infractions/topic`.
See the [format](#infractions-reporting-format) below.
The value of `infractions/history` define the history QoS for this topic.

### Poll time

The `poll-time-ms` property defines how often the monitored expectations
are checked for infractions (in milliseconds).

### Maximum number of monitors

The `max-execution-monitors` is the maximum number of supported
execution monitors.

## Infractions reporting format

On each discovered infraction an event of the following format is sent
on `infractions/topic`:

{{ code_snippet("grace/interfaces/execution_monitor_msgs/msg/Infraction.idl",
    {"tag": "//! [InfractionEvent]"}, "idl") }}

* `pid`: The process id of the monitored process
* `monitor_name`: The name of the execution monitor as set by the client
* `task_id`: The task id as [set by the client](api-usage.md#adding-tasks-to-executor)
* `expectation_type`: The type of the expectation being infringed upon (by `ExpectationType` enum)
* `expectation`: A string representation of the expectation being infringed upon
* `counter`: The value of [the infraction counter](execution-monitor-design.md#the-deadline-and-infraction-counter-slots)
* `deviation`: The deviation from the expectation. The meaning depends on the `ExpectationType`:
    * `WALL_CLOCK_RUNTIME`, `CPU_CLOCK_RUNTIME`: relative deviation from the expected
    time limits in nanoseconds. If this value
    is negative, it means the task finished before the expected minimum limit; if positive,
    it means the task finished after the expected maximum limit
    * `ACTIVATIONS_PER_WINDOW`: relative deviation from the expected number of activations
    in the time window. If this value is negative, it means the task was activated fewer times
    than the expected minimum; if positive, it means the task was activated more times than
    the expected maximum
    * `ACTIVATION_DISTANCE`: relative deviation from the expected activation distance
    in the time period in nanoseconds. If this value is negative,
    it means the task was activated sooner than the expected minimum limit;
    if positive, it means the task was activated later than the expected maximum limit
* `backend_overrun_ns`: This value indicates an overrun of the upper limit of a
    time-bound expectation as discovered by the backend. It is a relative value in nanoseconds
    that is always positive. Note, that the shorter the [poll time](#poll-time) is,
    the more chances are that the backend discovers the overrun before the client's
    side. Since the backend is not aware of exact execution context,
    the only information provided is this overrun value while the `deviation` may not be set.
    The backend overrun is reported to the log along with the frequency of the
    [polling](#poll-time)

!!! note
    The log will only include the `deviation` and/or `backend_overrun_ns` values
    if they are different from zero.

## Service execution errors

The service is implemented as an Apex Grace application, meaning it reports
its state via [system status events](system-status-design.md). If a non-critical
error happens during registration of monitored tasks, it will be reported as
a warning and the request will return `Error` to the client. In all other cases,
the exception is thrown from the application and subsequently reported in the
[usual manner](api-usage.md#enabling-event-reporting-from-an-executor-and-executable-items).

!!! note
    The type of the warning event is `RegistrationFailed` in `execution_monitor_app`.
    If it is desirable to receive this event as a part of the system status, please make
    sure it is properly registered via [event dispatcher](dispatcher-design.md)
    and custom user events are allowed.
    [See documentation.](system-status-design.md#enabling-custom-application-statuses)
