// Copyright 2024 Apex.AI, Inc.
// All rights reserved.
#include <gtest/gtest.h>

#include <algorithm>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "cpputils/fake_clock.hpp"
#include "dispatcher_interfaces/common.hpp"
#include "dispatcher_interfaces/srv/event_handler.hpp"
#include "event/monitored_process.hpp"
#include "event_registry/event_registry.hpp"
#include "event_registry/event_registry_remote.hpp"
#include "execution_monitor/common.hpp"
#include "execution_monitor/communication.hpp"
#include "execution_monitor/communication_stub.hpp"
#include "execution_monitor/execution_monitor.hpp"
#include "execution_monitor_service/common.hpp"
#include "execution_monitor_service/events/events.hpp"
#include "execution_monitor_service/execution_monitor_service.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "process_manager_interfaces/common.hpp"
#include "rclcpp/dynamic_waitset/waitset.hpp"
#include "rclcpp/rclcpp.hpp"
#include "timer_service/clock_timer_service.hpp"

#include "execution_monitor_msgs/msg/infraction.hpp"

using namespace std::chrono_literals;

namespace common = apex::execution_monitor::common;
namespace dw = rclcpp::dynamic_waitset;
using communication = apex::execution_monitor::communication_client;
using communication_stub = apex::execution_monitor::communication_stub;
using execution_monitor =
  apex::execution_monitor::execution_monitor<apex::execution_monitor::task_identifier::task_id_t>;
using apex::executor::executor_runner;
namespace factory = apex::executor::executor_factory;
namespace expectations = apex::execution_monitor::expectations;
using wc_runtime_exp = expectations::wall_clock_runtime;
using cpu_runtime_exp = expectations::cpu_clock_runtime;
using act_window_exp = expectations::activations_per_window;
using act_delta_exp = expectations::activation_distance;
namespace types = apex::execution_monitor::expectations::types;
namespace event = apex::event;
using apex::process_manager::common::ENV_VAR_GROUP_NAME;
using apex::process_manager::common::ENV_VAR_PROCESS_NAME;
using apex::process_manager::common::ENV_VAR_STABLE_PROCESS_ID;
using sim_execution_monitor =
  apex::execution_monitor::details::execution_monitor<apex::string32_t, apex::FakeClock>;

namespace
{
class execution_monitor_monitoring_test : public ::testing::Test
{
public:
  void SetUp() override
  {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
    m_node = std::make_shared<rclcpp::Node>("test_node");
    m_timer_srv = std::make_unique<apex::timer_service::steady_clock_timer_service>();
    (void)::setenv(ENV_VAR_GROUP_NAME, "test_group_name", 1);
    (void)::setenv(ENV_VAR_PROCESS_NAME, "test_process_name", 1);
    (void)::setenv(ENV_VAR_STABLE_PROCESS_ID, "42", 1);
    apex::FakeClock::reset();
  }

  void TearDown() override
  {
    (void)rclcpp::shutdown();
  }

  auto create_service(const std::string & service_name = "", bool no_event_handler = false)
  {
    auto srv = std::make_shared<apex::execution_monitor::execution_monitor_service>(
      m_timer_srv->create_timer(0ms, 50ms),
      service_name,
      no_event_handler ? "" : common::ProcessTerminationEventHandlerName,
      common::InfractionsTopicName,
      100,
      100);
    m_sub = m_node->create_polling_subscription<execution_monitor_msgs::msg::Infraction>(
      common::InfractionsTopicName, rclcpp::DefaultQoS().reliable().keep_last(100));
    m_sub->wait_for_matched(1U);
    m_event = m_node->create_polling_client<dispatcher_interfaces::srv::EventHandler>(
      common::ProcessTerminationEventHandlerName);
    if (!no_event_handler) {
      m_event->wait_for_service(3s);
    }
    return srv;
  }

  auto create_com(const std::string & service_name = "", std::chrono::milliseconds timeout = 3s)
  {
    return std::make_unique<communication>(service_name, timeout);
  }

  auto get_infractions(std::chrono::milliseconds wait_ms, std::size_t expected_count)
  {
    dw::Waitset ws{m_sub};
    std::vector<execution_monitor_msgs::msg::Infraction> retval;
    while (true) {
      if (!ws.wait(wait_ms)) {
        throw apex::runtime_error{"timeout"};
      }

      const auto msgs = m_sub->take();
      for (const auto & m : msgs) {
        if (m.info().valid()) {
          retval.push_back(m.data());
          if (retval.size() == expected_count) {
            return retval;
          }
        }
      }
    }
  }

  auto send_process_terminated(std::int32_t pid, std::chrono::milliseconds wait_ms)
  {
    auto ev = m_event->borrow_loaned_request();
    ev->event.int_data = pid;
    ev->event.string_data = "test";
    ev->event.event_id = apex::event::ProcessUnexpectedAbnormalExit;
    m_event->async_send_request(std::move(ev));
    dw::Waitset ws{m_event};
    if (!ws.wait(wait_ms)) {
      throw apex::runtime_error{"timeout"};
    }
    (void)m_event->take_response();
  }

  std::shared_ptr<rclcpp::Node> m_node;
  rclcpp::PollingSubscription<execution_monitor_msgs::msg::Infraction>::SharedPtr m_sub;
  rclcpp::PollingClient<dispatcher_interfaces::srv::EventHandler>::SharedPtr m_event;
  std::unique_ptr<apex::timer_service::steady_clock_timer_service> m_timer_srv;
};

struct compare_msgs
{
  bool operator()(const execution_monitor_msgs::msg::Infraction & left,
                  const execution_monitor_msgs::msg::Infraction & right)
  {
    return left.task_id < right.task_id;
  }
};
}  // namespace

TEST_F(execution_monitor_monitoring_test, infractions)
{
  auto service = create_service();
  ASSERT_EQ(service->get_app_id(), execution_monitor_app::AppId);
  ASSERT_EQ(service->get_app_instance_id(), 0U);
  const auto exec = factory::create();
  exec->add(service);
  executor_runner r{*exec};
  const auto com = create_com();
  execution_monitor monitor1{"test_monitor1", *com};
  monitor1.register_task("id1", {wc_runtime_exp(10ms)});
  monitor1.register_task("id2", {cpu_runtime_exp(10s, 20s)});
  monitor1.registrations_complete();
  execution_monitor monitor2{"test_monitor2", *com};
  monitor2.register_task("id3", {wc_runtime_exp(10ms)});
  monitor2.register_task("id4", {cpu_runtime_exp(10s, 20s)});
  monitor2.registrations_complete();
  monitor1.task_starts("id1");
  monitor1.task_starts("id2");
  monitor2.task_starts("id3");
  monitor2.task_starts("id4");
  std::this_thread::sleep_for(500ms);
  auto infractions = get_infractions(3s, 2U);
  std::sort(infractions.begin(), infractions.end(), compare_msgs{});
  ASSERT_EQ(infractions[0].task_id, "id1");
  ASSERT_EQ(infractions[0].monitor_name, "test_monitor1");
  ASSERT_EQ(infractions[0].pid, ::getpid());
  ASSERT_NE(infractions[0].counter, 0U);
  ASSERT_EQ(infractions[0].deviation, 0U);
  ASSERT_NE(infractions[0].backend_overrun_ns, 0U);
  ASSERT_EQ(infractions[0].expectation_type, types::expectation_type_id_t::WALL_CLOCK_RUNTIME);
  ASSERT_EQ(infractions[0].process_manager_metadata.group_name, "test_group_name");
  ASSERT_EQ(infractions[0].process_manager_metadata.process_name, "test_process_name");
  ASSERT_EQ(infractions[0].process_manager_metadata.id, 42);
  ASSERT_EQ(infractions[1].task_id, "id3");
  ASSERT_EQ(infractions[1].monitor_name, "test_monitor2");
  ASSERT_EQ(infractions[1].pid, ::getpid());
  ASSERT_NE(infractions[1].counter, 0U);
  ASSERT_EQ(infractions[1].deviation, 0U);
  ASSERT_NE(infractions[1].backend_overrun_ns, 0U);
  ASSERT_EQ(infractions[1].expectation_type, types::expectation_type_id_t::WALL_CLOCK_RUNTIME);
  ASSERT_EQ(infractions[1].process_manager_metadata.group_name, "test_group_name");
  ASSERT_EQ(infractions[1].process_manager_metadata.process_name, "test_process_name");
  ASSERT_EQ(infractions[1].process_manager_metadata.id, 42);
  monitor1.task_completes("id2");
  monitor2.task_completes("id4");
  infractions = get_infractions(3s, 2U);
  std::sort(infractions.begin(), infractions.end(), compare_msgs{});
  ASSERT_EQ(infractions[0].task_id, "id2");
  ASSERT_EQ(infractions[0].expectation_type, types::expectation_type_id_t::CPU_CLOCK_RUNTIME);
  ASSERT_EQ(infractions[0].monitor_name, "test_monitor1");
  ASSERT_EQ(infractions[0].pid, ::getpid());
  ASSERT_NE(infractions[0].counter, 0U);
  ASSERT_EQ(infractions[0].process_manager_metadata.group_name, "test_group_name");
  ASSERT_EQ(infractions[0].process_manager_metadata.process_name, "test_process_name");
  ASSERT_EQ(infractions[0].process_manager_metadata.id, 42);
  ASSERT_EQ(infractions[1].task_id, "id4");
  ASSERT_EQ(infractions[1].expectation_type, types::expectation_type_id_t::CPU_CLOCK_RUNTIME);
  ASSERT_EQ(infractions[1].monitor_name, "test_monitor2");
  ASSERT_EQ(infractions[1].pid, ::getpid());
  ASSERT_NE(infractions[1].counter, 0U);
  ASSERT_EQ(infractions[1].process_manager_metadata.group_name, "test_group_name");
  ASSERT_EQ(infractions[1].process_manager_metadata.process_name, "test_process_name");
  ASSERT_EQ(infractions[1].process_manager_metadata.id, 42);
  ASSERT_NO_THROW(monitor2.unregister_monitor_tasks());
  ASSERT_NO_THROW(monitor1.unregister_monitor_tasks());
  ASSERT_NO_THROW(r.stop());
}

TEST_F(execution_monitor_monitoring_test, unexpected_termination)
{
  auto service = create_service();
  const auto exec = factory::create();
  exec->add(service);
  executor_runner r{*exec};
  const auto com = create_com();
  execution_monitor monitor{"test_monitor", *com};
  monitor.register_task("id1", {wc_runtime_exp(50ms)});
  monitor.registrations_complete();
  monitor.task_starts("id1");
  send_process_terminated(::getpid(), 1s);
  std::this_thread::sleep_for(500ms);
  ASSERT_THROW(get_infractions(1s, 1U), apex::runtime_error);
  ASSERT_NO_THROW(monitor.unregister_monitor_tasks());
  ASSERT_NO_THROW(r.stop());
}

TEST_F(execution_monitor_monitoring_test, unexpected_termination_no_event_handler)
{
  auto service = create_service("", true);
  const auto exec = factory::create();
  exec->add(service);
  executor_runner r{*exec};
  const auto com = create_com();
  execution_monitor monitor{"test_monitor", *com};
  monitor.register_task("id1", {wc_runtime_exp(50ms)});
  monitor.registrations_complete();
  monitor.task_starts("id1");
  ASSERT_THROW(send_process_terminated(::getpid(), 1s), apex::runtime_error);
  ASSERT_NO_THROW(monitor.unregister_monitor_tasks());
  ASSERT_NO_THROW(r.stop());
}

TEST_F(execution_monitor_monitoring_test, unexpected_termination_bad_pid)
{
  auto service = create_service();
  const auto exec = factory::create();
  exec->add(service);
  executor_runner r{*exec};
  const auto com = create_com();
  execution_monitor monitor{"test_monitor", *com};
  monitor.register_task("id1", {wc_runtime_exp(50ms)});
  monitor.registrations_complete();
  monitor.task_starts("id1");
  send_process_terminated(0, 1s);
  std::this_thread::sleep_for(500ms);
  ASSERT_NO_THROW(get_infractions(1s, 1U));
  ASSERT_NO_THROW(monitor.unregister_monitor_tasks());
  ASSERT_NO_THROW(r.stop());
}

TEST_F(execution_monitor_monitoring_test, non_default_service_name)
{
  auto service = create_service("my_service");
  ASSERT_EQ(service->get_app_id(), execution_monitor_app::AppId);
  ASSERT_EQ(service->get_app_instance_id(), 0U);
  ASSERT_THROW(create_com("some other_service"), std::exception);
  const auto exec = factory::create();
  exec->add(service);
  executor_runner r{*exec};
  const auto com = create_com("my_service");
  execution_monitor monitor{"test_monitor", *com};
  monitor.register_task("id1", {wc_runtime_exp(50ms)});
  monitor.registrations_complete();
  monitor.task_starts("id1");
  std::this_thread::sleep_for(500ms);
  ASSERT_NO_THROW(get_infractions(1s, 1U));
  ASSERT_NO_THROW(monitor.unregister_monitor_tasks());
  ASSERT_NO_THROW(r.stop());
}

TEST_F(execution_monitor_monitoring_test, registration_warning_event)
{
  auto sub = m_node->create_polling_subscription<event::types::event_t>(
    apex::dispatcher::common::get_default_event_topic_name(),
    apex::dispatcher::common::get_event_qos());
  apex::event::event_registry_publisher pub{*m_node,
                                            apex::dispatcher::common::get_default_instance_name()};
  pub.publish(apex::event::get_local_registry());
  dw::Waitset ws{sub};
  auto service = create_service();
  event::monitored_process p;
  const auto exec = factory::create(p);
  exec->add(service);
  executor_runner r{*exec};
  const auto com = create_com();
  execution_monitor monitor1{"test_monitor", *com};
  // duplicate id
  execution_monitor monitor2{"test_monitor", *com};
  monitor1.register_task("id1", {wc_runtime_exp(50ms)});
  monitor2.register_task("id1", {wc_runtime_exp(50ms)});
  monitor1.registrations_complete();
  ASSERT_THROW(monitor2.registrations_complete(), apex::runtime_error);
  while (true) {
    ASSERT_TRUE(ws.wait(3s));
    const auto msgs = sub->take();
    ASSERT_FALSE(msgs.empty());
    for (const auto & msg : msgs) {
      if (msg.info().valid() &&
          (msg.data().event_id == execution_monitor_app::event::RegistrationFailed)) {
        ASSERT_FALSE(msg.data().string_data.empty());
        ASSERT_NE(msg.data().int_data, 0);
        ASSERT_NO_THROW(r.stop());
        return;
      }
    }
  }
  FAIL();
}

TEST_F(execution_monitor_monitoring_test, failed_registration_does_not_bring_service_down)
{
  auto service = create_service();
  const auto exec = factory::create();
  exec->add(service);
  executor_runner r{*exec};
  const auto com = create_com();
  execution_monitor monitor1{"test_monitor", *com};
  // duplicate id
  execution_monitor monitor2{"test_monitor", *com};
  monitor1.register_task("id1", {wc_runtime_exp(50ms)});
  monitor2.register_task("id1", {wc_runtime_exp(50ms)});
  monitor1.registrations_complete();
  ASSERT_THROW(monitor2.registrations_complete(), apex::runtime_error);
  monitor1.task_starts("id1");
  std::this_thread::sleep_for(500ms);
  ASSERT_NO_THROW(get_infractions(3s, 1U));
  ASSERT_NO_THROW(r.stop());
}

TEST_F(execution_monitor_monitoring_test, multiple_monitors)
{
  auto service = create_service("", true);
  const auto exec = factory::create();
  exec->add(service);
  executor_runner r{*exec};
  const auto com1 = create_com();
  execution_monitor monitor1{"test_monitor1", *com1};
  const auto com2 = create_com();
  execution_monitor monitor2{"test_monitor2", *com2};
  monitor1.register_task("id1", {wc_runtime_exp(50ms)});
  monitor2.register_task("id2", {wc_runtime_exp(50ms)});
  monitor1.registrations_complete();
  monitor2.registrations_complete();
  monitor1.task_starts("id1");
  monitor2.task_starts("id2");
  std::this_thread::sleep_for(500ms);
  ASSERT_NO_THROW(get_infractions(3s, 2U));
  monitor1.task_completes("id1");
  monitor2.task_completes("id2");
  ASSERT_NO_THROW(r.stop());
}

TEST_F(execution_monitor_monitoring_test, no_negative_com_timeout)
{
  auto service = create_service("", true);
  const auto exec = factory::create();
  exec->add(service);
  executor_runner r{*exec};
  ASSERT_THROW(create_com("test_service", -1s), apex::runtime_error);
}

TEST_F(execution_monitor_monitoring_test, wall_clock_underrun)
{
  communication_stub com;
  sim_execution_monitor monitor{"test_monitor", com};
  monitor.register_task("id1", {wc_runtime_exp(50ms, 100ms)});
  monitor.registrations_complete();
  monitor.issue();
  monitor.task_starts("id1");
  apex::FakeClock::advance(15ms);
  monitor.task_completes("id1");
  auto & slot = com.slot_for("test_monitor", "id1", 0);
  ASSERT_EQ(slot.expectation_type_id, types::expectation_type_id_t::WALL_CLOCK_RUNTIME);
  ASSERT_EQ(slot.infractions_counter, 1);
  ASSERT_EQ(std::chrono::nanoseconds{slot.deviation}, -35ms);
}

TEST_F(execution_monitor_monitoring_test, wall_clock_overrun_backend)
{
  communication_stub com;
  sim_execution_monitor monitor{"test_monitor", com};
  monitor.register_task("id1", {wc_runtime_exp(50ms, 100ms)});
  monitor.registrations_complete();
  monitor.issue();
  monitor.task_starts("id1");
  auto & slot = com.slot_for("test_monitor", "id1", 0);
  const auto infractions = slot.inspect_deadline(105ms);
  ASSERT_EQ(infractions.expectation_type, types::expectation_type_id_t::WALL_CLOCK_RUNTIME);
  ASSERT_EQ(infractions.counter, 1);
  ASSERT_EQ(std::chrono::nanoseconds{infractions.deviation}, 0ms);
  ASSERT_EQ(infractions.backend_overrun, 5ms);
}

TEST_F(execution_monitor_monitoring_test, wall_clock_overrun)
{
  communication_stub com;
  sim_execution_monitor monitor{"test_monitor", com};
  monitor.register_task("id1", {wc_runtime_exp(50ms, 100ms)});
  monitor.registrations_complete();
  monitor.issue();
  monitor.task_starts("id1");
  apex::FakeClock::advance(115ms);
  monitor.task_completes("id1");
  const auto & slot = com.slot_for("test_monitor", "id1", 0);
  ASSERT_EQ(slot.expectation_type_id, types::expectation_type_id_t::WALL_CLOCK_RUNTIME);
  ASSERT_EQ(slot.infractions_counter, 1);
  ASSERT_EQ(std::chrono::nanoseconds{slot.deviation}, 15ms);
}

TEST_F(execution_monitor_monitoring_test, cpu_clock_underrun)
{
  communication_stub com;
  sim_execution_monitor monitor{"test_monitor", com};
  monitor.register_task("id1", {cpu_runtime_exp(50ms, 100ms)});
  monitor.registrations_complete();
  monitor.issue();
  monitor.task_starts("id1");
  apex::FakeClock::advance(15ms);
  monitor.task_completes("id1");
  const auto & slot = com.slot_for("test_monitor", "id1", 0);
  ASSERT_EQ(slot.expectation_type_id, types::expectation_type_id_t::CPU_CLOCK_RUNTIME);
  ASSERT_EQ(slot.infractions_counter, 1);
  ASSERT_EQ(std::chrono::nanoseconds{slot.deviation}, -35ms);
}

TEST_F(execution_monitor_monitoring_test, cpu_clock_overrun)
{
  communication_stub com;
  sim_execution_monitor monitor{"test_monitor", com};
  monitor.register_task("id1", {cpu_runtime_exp(50ms, 100ms)});
  monitor.registrations_complete();
  monitor.issue();
  monitor.task_starts("id1");
  apex::FakeClock::advance(115ms);
  monitor.task_completes("id1");
  const auto & slot = com.slot_for("test_monitor", "id1", 0);
  ASSERT_EQ(slot.expectation_type_id, types::expectation_type_id_t::CPU_CLOCK_RUNTIME);
  ASSERT_EQ(slot.infractions_counter, 1);
  ASSERT_EQ(std::chrono::nanoseconds{slot.deviation}, 15ms);
}

TEST_F(execution_monitor_monitoring_test, too_few_activations_per_window)
{
  communication_stub com;
  sim_execution_monitor monitor{"test_monitor", com};
  monitor.register_task("id1", {act_window_exp(3, 5, 10ms)});
  monitor.registrations_complete();
  monitor.issue();
  monitor.task_starts("id1");
  monitor.task_completes("id1");
  monitor.task_starts("id1");
  monitor.task_completes("id1");
  apex::FakeClock::advance(15ms);
  monitor.task_starts("id1");
  monitor.task_completes("id1");
  const auto & slot = com.slot_for("test_monitor", "id1", 0);
  ASSERT_EQ(slot.expectation_type_id, types::expectation_type_id_t::ACTIVATIONS_PER_WINDOW);
  ASSERT_EQ(slot.infractions_counter, 1);
  ASSERT_EQ(slot.deviation, -1);
}

TEST_F(execution_monitor_monitoring_test, too_many_activations_per_window)
{
  communication_stub com;
  sim_execution_monitor monitor{"test_monitor", com};
  monitor.register_task("id1", {act_window_exp(3, 5, 10ms)});
  monitor.registrations_complete();
  monitor.issue();
  for (auto i = 0; i < 7; ++i) {
    monitor.task_starts("id1");
    monitor.task_completes("id1");
  }
  const auto & slot = com.slot_for("test_monitor", "id1", 0);
  ASSERT_EQ(slot.expectation_type_id, types::expectation_type_id_t::ACTIVATIONS_PER_WINDOW);
  ASSERT_EQ(slot.infractions_counter, 2);
  ASSERT_EQ(slot.deviation, 2);
}

TEST_F(execution_monitor_monitoring_test, actiavtions_distance_too_short)
{
  communication_stub com;
  sim_execution_monitor monitor{"test_monitor", com};
  monitor.register_task("id1", {act_delta_exp(50ms, 100ms)});
  monitor.registrations_complete();
  monitor.issue();
  monitor.task_starts("id1");
  monitor.task_completes("id1");
  apex::FakeClock::advance(15ms);
  monitor.task_starts("id1");
  monitor.task_completes("id1");
  const auto & slot = com.slot_for("test_monitor", "id1", 0);
  ASSERT_EQ(slot.expectation_type_id, types::expectation_type_id_t::ACTIVATION_DISTANCE);
  ASSERT_EQ(slot.infractions_counter, 1);
  ASSERT_EQ(std::chrono::nanoseconds{slot.deviation}, -35ms);
}

TEST_F(execution_monitor_monitoring_test, actiavtions_distance_too_long)
{
  communication_stub com;
  sim_execution_monitor monitor{"test_monitor", com};
  monitor.register_task("id1", {act_delta_exp(50ms, 100ms)});
  monitor.registrations_complete();
  monitor.issue();
  monitor.task_starts("id1");
  monitor.task_completes("id1");
  apex::FakeClock::advance(115ms);
  monitor.task_starts("id1");
  monitor.task_completes("id1");
  const auto & slot = com.slot_for("test_monitor", "id1", 0);
  ASSERT_EQ(slot.expectation_type_id, types::expectation_type_id_t::ACTIVATION_DISTANCE);
  ASSERT_EQ(slot.infractions_counter, 1);
  ASSERT_EQ(std::chrono::nanoseconds{slot.deviation}, 15ms);
}
