load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "install_space")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "cc_test_in_install_space")

ros_pkg(
    name = "system_status_pkg",
    bin_executables = [
        ":system_status_service",
        ":system_status",
    ],
    cc_libraries = [
        ":system_status_lib",
    ],
    description = "Package containing system status service and UI",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/apexutils:apexutils_pkg",
        "@apex//common/configuration/settings:settings_pkg",
        "@apex//common/cpputils:cpputils_pkg",
        "@apex//common/interrupt:interrupt_pkg",
        "@apex//common/threading:threading_pkg",
        "@apex//grace/configuration/storage/storage:storage_pkg",
        "@apex//grace/monitoring/dispatcher_interfaces:dispatcher_interfaces_pkg",
        "@apex//grace/monitoring/event_registry:event_registry_pkg",
        "@apex//grace/monitoring/logging:logging_pkg",
        "@apex//grace/monitoring/system_status_interfaces:system_status_interfaces_pkg",
        "@apex//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "@apex//grace/ros/rcutils:rcutils_pkg",
        "@apex//grace/utils/apexcpp:apexcpp_pkg",
        "@apex//grace/utils/system_utils:system_utils_pkg",
        "@nlohmann_json//:nlohmann_json_pkg",
    ],
)

apex_cc_library(
    name = "system_status_lib",
    srcs = glob(
        ["src/**"],
        exclude = [
            "src/ui/**",
        ],
    ),
    hdrs = glob(["include/**"]),
    strip_include_prefix = "include",
    tags = ["integrity QM"],
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/cpputils",
        "@apex//common/threading",
        "@apex//grace/configuration/storage/storage",
        "@apex//grace/monitoring/dispatcher_interfaces",
        "@apex//grace/monitoring/event_registry",
        "@apex//grace/monitoring/logging",
        "@apex//grace/monitoring/system_status_interfaces",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@apex//grace/utils/apexcpp",
        "@apex//grace/utils/system_utils",
        "@nlohmann_json",
    ],
)

cc_binary(
    name = "system_status_service",
    srcs = [
        "src/ui/service_main.cpp",
    ],
    tags = ["integrity QM"],
    deps = [
        ":system_status_lib",
        "@apex//common/configuration/settings",
        "@apex//common/interrupt",
        "@apex//grace/monitoring/dispatcher_interfaces:dispatcher_common",
        "@apex//grace/ros/rcutils",
    ],
)

cc_binary(
    name = "system_status",
    srcs = [
        "src/ui/cli_main.cpp",
    ],
    tags = ["integrity QM"],
    deps = [
        ":system_status_lib",
        "@apex//common/interrupt",
        "@apex//grace/monitoring/dispatcher_interfaces:dispatcher_common",
        "@apex//grace/ros/rcutils",
    ],
)

install_space(
    name = "install_space_for_tests",
    message_library_kinds = [
        "cpp",
        "introspection_cpp",
    ],
    ros_pkgs = [
        "@apex//grace/configuration/storage/storage:storage_pkg",
        "@googletest//:googletest_pkg",
        "@apex//grace/monitoring/system_status:system_status_pkg",
        "@apex//grace/monitoring/system_status_interfaces:system_status_interfaces_pkg",
    ],
)

cc_test_in_install_space(
    name = "system_status_tests",
    srcs = glob(["test/**"]),
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    install_space = ":install_space_for_tests",
    ros_domain_id_isolation = True,
    tags = [
        "constrained_test",
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        ":system_status_lib",
        "@apex//common/apexutils",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "config/example.yaml",
    ],
    visibility = [":__subpackages__"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
