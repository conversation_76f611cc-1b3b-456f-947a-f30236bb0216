load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "logging_pkg",
    cc_libraries = [":logging"],
    description = "Logging via DDS",
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/apexutils:apexutils_pkg",
        "//grace/interfaces/apex_msgs:apex_msgs_pkg",
        "//grace/utils/apexcpp:apexcpp_pkg",
    ],
)

config_setting(
    name = "off",
    flag_values = {"@apex//:log_level": "OFF"},
    visibility = ["//visibility:public"],
)

config_setting(
    name = "use_default",
    flag_values = {"@apex//:log_level": "LOCAL_DEFAULT"},
    visibility = ["//visibility:public"],
)

apex_cc_library(
    name = "logging",
    srcs = [
        "src/axivion.cpp",
        "src/contextual_logging.cpp",
        "src/logging.cpp",
    ],
    hdrs = [
        "include/logging/contextual_logging.hpp",
        "include/logging/details/base_logging.hpp",
        "include/logging/details/root_logger.hpp",
        "include/logging/levels.hpp",
        "include/logging/logging.hpp",
        "include/logging/logging_macros.hpp",
        "include/logging/visibility_control.hpp",
    ],
    applicable_licenses = ["//common/integrity:embedded_gold"],
    defines = select({
        ":off": ["APEX_DISABLE_LOGGING"],
        "//conditions:default": ["APEX_PRINT_LOGS_TO_TERMINAL"],
    }) + select({
        ":use_default": ["APEX_GRACE_LOG_LEVEL=\\\"$(APEX_GRACE_LOG_LEVEL)\\\""],
        # this will not be used but needs to be set since OFF is not a valid log level
        ":off": ["APEX_GRACE_LOG_LEVEL=\\\"FATAL\\\""],
        "//conditions:default": ["APEX_GRACE_LOG_LEVEL=\\\"$(APEX_LOG_LEVEL)\\\""],
    }),
    strip_include_prefix = "include",
    toolchains = [
        "@apex//:log_level",
        "@apex//grace:log_level",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//common/apexutils",
        "//grace/interfaces/apex_msgs",
        "//grace/utils/apexcpp",
        "@coverage_tool//:coverage_io_lib",
    ],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
