// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

/// \file
/// \brief This file defines the base logging class

#ifndef LOGGING__DETAILS__BASE_LOGGING_HPP_
#define LOGGING__DETAILS__BASE_LOGGING_HPP_

#include <apexcpp/apexcpp.hpp>
#include <logging/details/root_logger.hpp>
#include <logging/visibility_control.hpp>
#include <logging/levels.hpp>
#include <rclcpp/rclcpp.hpp>
#include <string/string.hpp>
#include <settings/inspect.hpp>
#include <settings/inspect/types.hpp>
#include <settings/repository.hpp>
#include <cpputils/common_exceptions.hpp>

#include <utility>

#if defined(APEX_PRINT_LOGS_TO_TERMINAL) && defined(APEX_CERT)
  #error "APEX_PRINT_LOGS_TO_TERMINAL is not supported with APEX_CERT"
#endif

/// \brief Namespace for apex
namespace apex
{
/// \brief Namespace for logging related classes, constants, and functions
namespace logging
{

/// \brief The default logging QoS history depth
constexpr static std::size_t const DEFAULT_HISTORY_DEPTH = 16U;

/// \brief Default QoS for logging related publishers and subscribers. It uses the default ROS 2 QoS
/// except 'durability' is set to Transient Local and custom history depth is used
class LOGGING_PUBLIC DefaultLoggingQoS : public rclcpp::DefaultQoS
{
public:
  explicit DefaultLoggingQoS(
    rclcpp::QoSInitialization const & qos_initialization =
    rclcpp::KeepLast(DEFAULT_HISTORY_DEPTH))
  : rclcpp::DefaultQoS(qos_initialization)
  {
    // The maximum size of a non-self-contained type serialized message is set to 12704 bytes,
    // which is the maximum size needed for a contextual log message
    resource_limits_max_non_self_contained_type_serialized_size(12704);
    (void)durability(RMW_QOS_POLICY_DURABILITY_TRANSIENT_LOCAL);
  }
};

/// \brief Extract log level from application settings file
/// \throw apex::domain_error if the LogLevel value is invalid
LogLevel LOGGING_PUBLIC load_log_level();

/// \brief Namespace for logging implementation details, i.e. logging base class
namespace details
{

/// \brief This class contains the functionality to publish debugging messages on the 'logging'
/// topic
/// \tparam MessageT Type of the log message
template<typename MessageT>
class LOGGING_LOCAL BaseLogger
{
public:
  using LogMsgT = MessageT;

//lint -e{1793} NOLINT - Builder pattern is ok

  /// \brief Constructor for the BaseLogger. Meant to live within a node.
  /// Wrapper around a rclcpp::Publisher, which is used to publish messages
  /// \param[in] node_ptr Pointer of the node associated with this logger. It is used to
  /// register the context of the logger and create a publisher
  /// \param[in] logger_name Name of the logger instance. Must be unique inside a node
  /// \param[in] history_depth The depth of message history
  /// \param[in] logging_topic_name Topic name for log messages
  /// Warning: Bursting more messages before the publisher can publish a single message from the
  /// queue will result in messages getting lost
  explicit BaseLogger(
    rclcpp::Node * const node_ptr,
    apex::string_strict256_t const & logger_name,
    const size_t history_depth,
    apex::string_strict256_t const & logging_topic_name)
  : m_pub_ptr(nullptr),
    m_host_name(apex::get_host_name()),
    m_exec_name(apex::get_executable_name()),
    m_node_name(node_ptr->get_name()),
    m_logger_name(logger_name.empty() ?
      apex::string_strict256_t(node_ptr->get_name()) +
      "_logger" :
      logger_name.c_str()),
    m_log_level(load_log_level())
  {
    m_enabled = load_enabled();
    m_print_to_terminal_enabled = load_print_logs_to_terminal_enabled();
    // Only create the logging publisher if logging is enabled to avoid
    // creating the /logging topic (even if no messages are published)
    if (m_enabled) {
      m_pub_ptr = node_ptr->create_publisher<MessageT>(logging_topic_name,
          DefaultLoggingQoS(rclcpp::KeepLast(history_depth)));
    }
  }

  /// \brief Default virtual destructor to properly destruct derived classes
  virtual ~BaseLogger() = default;

  /// \brief Get the logger publisher
  /// \return the logger publisher
  /// \cert
  /// \deterministic
  typename rclcpp::Publisher<MessageT>::SharedPtr get_publisher() const
  {
    return m_pub_ptr;
  }

  /// \brief Log a message
  /// \param[in] severity Logging level of the message
  /// \param[in] line_num Line number 'log_message()' is called from
  /// \param[in] file_name Name of the file the 'log_message()' is called from
  /// \param[in] args Arguments that will be parsed into a string and logged
  /// \cert
  /// \deterministic
  template<typename ... Args>
  void log_message(
    LogLevel const severity, uint32_t const line_num, apex::string256_t const & file_name, Args
    const & ... args) const noexcept(false)
  {
    log_message(severity, line_num, file_name, apex::system_clock::now(), args ...);
  }

  /// \brief Log a message with a custom timestamp
  /// \param[in] severity Logging level of the message
  /// \param[in] line_num Line number 'log_message()' is called from
  /// \param[in] file_name Name of the file the 'log_message()' is called from
  /// \param[in] stamp Timestamp for the message
  /// \param[in] args Arguments that will be parsed into a string and logged
  /// \cert
  /// \deterministic
  template<typename ... Args>
  void log_message(
    LogLevel const severity, uint32_t const line_num, apex::string256_t const & file_name,
    apex::system_clock::time_point const stamp, Args const & ... args) const noexcept(false)
  {
    if (!m_enabled) {
      return;
    }
    if (!is_log_message_allowed(severity)) {
      return;
    }
    log_message_impl(severity, line_num, file_name, stamp, args ...);
  }

  /// \brief Fill a pre-allocated MessageT with logging data
  /// \param[out] msg Pre-allocated message to be filled
  /// \param[in] severity Logging level of the message
  /// \param[in] line_num Line number 'log_message()' is called from
  /// \param[in] file_name Name of the file the 'log_message()' is called from
  /// \param[in] stamp Timestamp for the message
  /// \param[in] args Arguments that will be parsed into a string and logged
  /// \cert
  /// \deterministic
  template<
    typename T = MessageT,
    std::enable_if_t<std::is_same<T, typename MessageT::FlatType>::value || std::is_same<T,
    typename MessageT::NonFlatType>::value> * = nullptr,
    typename ... Args>
  void fill_log_message(
    T & msg,
    LogLevel const severity, uint32_t const line_num, apex::string256_t const & file_name,
    apex::system_clock::time_point const stamp, Args const & ... args) const noexcept(false)
  {
    fill_log_message(msg, severity, line_num, file_name, stamp, apex::varargs_to_string(args ...));
  }

  /// \brief Fill a pre-allocated MessageT with logging data. Designed to be implemented in the
  /// derived classes with specific MessageT message
  /// \param[out] msg Pre-allocated message to be filled
  /// \param[in] severity Logging level of the message
  /// \param[in] line_num Line number 'log_message()' is called from
  /// \param[in] file_name Name of the file the 'log_message()' is called from
  /// \param[in] stamp Timestamp for the message
  /// \param[in] text String that will be logged logged
  virtual void fill_log_message(
    typename MessageT::NonFlatType & msg,
    LogLevel const severity, uint32_t const line_num, apex::string256_t const & file_name,
    apex::system_clock::time_point const stamp,
    apex::string256_t const & text) const noexcept(false) = 0;

  /// \brief Fill a pre-allocated MessageT with logging data. Designed to be implemented in the
  /// derived classes with specific MessageT message
  /// \param[out] msg Pre-allocated message to be filled
  /// \param[in] severity Logging level of the message
  /// \param[in] line_num Line number 'log_message()' is called from
  /// \param[in] file_name Name of the file the 'log_message()' is called from
  /// \param[in] stamp Timestamp for the message
  /// \param[in] text String that will be logged logged
  virtual void fill_log_message(
    typename MessageT::FlatType & msg,
    LogLevel const severity, uint32_t const line_num, apex::string256_t const & file_name,
    apex::system_clock::time_point const stamp,
    apex::string256_t const & text) const noexcept(false) = 0;

  /// \brief Change the log level threshold
  /// \param[in] log_level new log level to set
  void set_log_severity_threshold(const LogLevel log_level)
  {
    m_log_level = log_level;
  }

protected:
#if defined(APEX_PRINT_LOGS_TO_TERMINAL) && !defined(APEX_CERT)
  /// \brief Print the content of the message to console if APEX_PRINT_LOGS_TO_TERMINAL is set.
  /// It is useful for debugging
  /// \param[in] msg Message to be printed
  virtual void print_to_console(typename MessageT::NonFlatType const & msg) const = 0;

  /// \brief Print the content of the message to console if APEX_PRINT_LOGS_TO_TERMINAL is set.
  /// It is useful for debugging
  /// \param[in] msg Message to be printed
  virtual void print_to_console(typename MessageT::FlatType const & msg) const = 0;
#endif

  /// \brief Check if the message logging level is below the specified threshold
  /// \param[in] log_level Logging level of the message
  /// \return true if the logging level is above the configured level, false otherwise
  bool is_log_message_allowed(LogLevel const log_level) const
  {
    return log_level >= m_log_level;
  }

  /// \brief Extract enabled setting from application settings file and/or compile time option
  /// \return true if logging is enabled, false otherwise
  bool load_enabled()
  {
    const auto settings = apex::settings::repository::get();
    const auto enabled_setting =
      apex::settings::inspect::get<
      apex::settings::inspect::maybe<
        apex::settings::inspect::boolean>>(
      settings, "logging/enabled");
    auto enabled = true;
    if (enabled_setting) {
      enabled = *enabled_setting;
    }
#ifdef APEX_DISABLE_LOGGING
    if (*enabled_setting) {
      log_message_impl(LogLevel::WARN, static_cast<uint32_t>(__LINE__), __FILE__,
        apex::system_clock::now(),
        "Logging disabled by compile-time definition \"APEX_DISABLE_LOGGING\"");
    }
    enabled = false;
#endif  // #ifdef APEX_DISABLE_LOGGING
    return enabled;
  }

  /// \brief Extract print_logs_to_terminal setting from application settings file
  /// \return true if print_logs_to_terminal is enabled, false otherwise
  bool load_print_logs_to_terminal_enabled()
  {
    constexpr const char * PRINT_LOGS_TO_TERMINAL_SETTINGS_KEY = "logging/print_logs_to_terminal";
    const auto settings = apex::settings::repository::get();
    #if defined(APEX_PRINT_LOGS_TO_TERMINAL)
    constexpr bool default_value = true;
    #else
    constexpr bool default_value = false;
    #endif
    const bool enabled = apex::settings::inspect::get_or_default<
      apex::settings::inspect::boolean>(
      settings, PRINT_LOGS_TO_TERMINAL_SETTINGS_KEY, default_value);
    #if !defined(APEX_PRINT_LOGS_TO_TERMINAL)
    if (enabled) {
      log_message_impl(LogLevel::WARN, static_cast<uint32_t>(__LINE__), __FILE__,
        apex::system_clock::now(),
        "compile-time definition \"APEX_DISABLE_LOGGING\" overrides setting",
        PRINT_LOGS_TO_TERMINAL_SETTINGS_KEY);
    }
    #endif
    return enabled;
  }

  /// \brief Actually log a message, no checks for logging level or verbosity
  /// \param[in] severity Logging level of the message
  /// \param[in] line_num Line number 'log_message()' is called from
  /// \param[in] file_name Name of the file the 'log_message()' is called from
  /// \param[in] stamp Timestamp for the message
  /// \param[in] args Arguments that will be parsed into a string and logged
  /// \cert
  /// \deterministic
  template<typename ... Args>
  void log_message_impl(
    LogLevel const severity, uint32_t const line_num, apex::string256_t const & file_name,
    apex::system_clock::time_point const stamp, Args const & ... args) const noexcept(false)
  {
#ifndef APEX_DISABLE_LOGGING
    auto loaned_msg = m_pub_ptr->borrow_loaned_message();
    fill_log_message(loaned_msg.get(), severity, line_num, file_name, stamp, args ...);
#if defined(APEX_PRINT_LOGS_TO_TERMINAL) && !defined(APEX_CERT)
    if (m_print_to_terminal_enabled) {
      print_to_console(loaned_msg.get());
    }
#endif
    m_pub_ptr->publish(std::move(loaned_msg));
#endif
  }

  /// The logger publisher
  typename rclcpp::Publisher<MessageT>::SharedPtr m_pub_ptr;
  /// The logger host name
  apex::string_strict256_t const m_host_name;
  /// The logger executable name
  apex::string_strict256_t const m_exec_name;
  /// The logger node name
  apex::string_strict256_t const m_node_name;
  /// The logger name
  apex::string_strict256_t const m_logger_name;
  /// The logger level threshold
  LogLevel m_log_level;
  /// The logger state
  bool m_enabled;
  /// Whether the logger prints the log message to stderr
  bool m_print_to_terminal_enabled;
};

}  // namespace details
}  // namespace logging
}  // namespace apex

#endif  // LOGGING__DETAILS__BASE_LOGGING_HPP_
