// Copyright 2019 Apex.AI, Inc.
// All rights reserved.

/// \file
/// \brief This file defines the logging class

#ifndef LOGGING__LOGGING_HPP_
#define LOGGING__LOGGING_HPP_

#include <apex_msgs/msg/log_message.hpp>
#include <apexcpp/apexcpp.hpp>
#include <iceoryx_posh/runtime/posh_runtime.hpp>
#include <logging/details/base_logging.hpp>
#include <logging/visibility_control.hpp>
#include <rclcpp/rclcpp.hpp>
#include <string/string.hpp>

#ifdef APEX_PRINT_LOGS_TO_TERMINAL
#include <iomanip>  // put_time
#include <ctime>   // localtime
#endif  // #ifdef APEX_PRINT_LOGS_TO_TERMINAL

/// \brief Namespace for apex
namespace apex
{
/// \brief Namespace for logging related classes, constants, and functions
namespace logging
{
template<typename MessageT = apex_msgs::msg::LogMessage>
class LOGGING_PUBLIC Logger final : public details::BaseLogger<MessageT>
{
public:
  /// \brief The default logging topic name
  static constexpr char const * DEFAULT_LOGGING_TOPIC_NAME = "/logging";

  /// \brief Constructor for the Logger. Meant to live within a node
  /// Wrapper around a rclcpp::Publisher, which is used to publish messages
  /// \param[in] node_ptr Pointer of the node associated with this logger. It is used to
  /// register the context of the logger and create a publisher
  /// \param[in] logger_name Name of the logger instance. Must be unique inside a node
  /// \param[in] history_depth The depth of message history.
  /// Warning: Bursting more messages before the publisher can publish a single message from the
  /// queue will result in messages getting lost
  /// \cert
  explicit Logger(
    rclcpp::Node * const node_ptr,
    apex::string_strict256_t const & logger_name = "",
    const size_t history_depth = DEFAULT_HISTORY_DEPTH,
    apex::string_strict256_t const & logging_topic_name = DEFAULT_LOGGING_TOPIC_NAME)
  : details::BaseLogger<MessageT>(node_ptr, logger_name, history_depth, logging_topic_name)
  {}

  /// \brief Get the root logger. May be empty if rclcpp::shutdown has been called
  /// \return std::optional containing the root logger if it is available
  /// \cert
  static const apex::optional<std::reference_wrapper<const Logger<MessageT>>> get_root_logger()
  {
    if (!g_root_logger) {
      const auto & ctx = rclcpp::contexts::get_global_default_context();
      std::scoped_lock lock(ctx->get_init_mutex());
      if (!rclcpp::ok()) {
        return std::nullopt;
      }
      if (!g_root_logger) {
        g_root_logger = std::make_unique<details::RootLogger<Logger<MessageT>>>(
          "_root_logger_node", "root_logger");
        ctx->on_shutdown([]() {
            g_root_logger.reset();
          });
      }
    }
    return apex::make_optional(std::cref(*g_root_logger->get_logger()));
  }

  /// \brief Get the root logger
  /// \return the root logger
  /// \cert
  [[deprecated("The root logger instance is invalid after rclcpp::shutdown has been called. Please use get_root_logger() instead which can handle this.")]]
  static const Logger<MessageT> & root_logger()
  {
    const auto maybe_root_logger = get_root_logger();
    if (!maybe_root_logger) {
      throw apex::runtime_error{
              "The root logger instance is invalid after rclcpp::shutdown has been called."};
    }
    return *maybe_root_logger;
  }

  /// \brief Fill a pre-allocated MessageT with logging data
  /// \param[out] msg Pre-allocated message to be filled
  /// \param[in] severity Logging level of the message
  /// \param[in] line_num Line number 'log_message()' is called from
  /// \param[in] file_name Name of the file the 'log_message()' is called from
  /// \param[in] stamp Timestamp for the message
  /// \param[in] text String that will be logged logged
  /// \cert
  /// \deterministic
  void fill_log_message(
    typename MessageT::NonFlatType & msg,
    LogLevel const severity, uint32_t const line_num, apex::string256_t const & file_name,
    apex::system_clock::time_point const stamp,
    apex::string256_t const & text) const noexcept(false) override
  {
    msg.app_info.host_name = details::BaseLogger<MessageT>::m_host_name;
    msg.app_info.exec_name = details::BaseLogger<MessageT>::m_exec_name;
    msg.app_info.node_name = details::BaseLogger<MessageT>::m_node_name;
    msg.logger_name = details::BaseLogger<MessageT>::m_logger_name;
    msg.level = static_cast<uint8_t>(severity);
    msg.text = text;
    msg.stamp = apex::to_msg_time(stamp);  // time of msg creation
    msg.file_name = file_name;
    msg.line = line_num;
  }

  /// \brief Fill a pre-allocated MessageT with logging data
  /// \param[out] msg Pre-allocated message to be filled
  /// \param[in] severity Logging level of the message
  /// \param[in] line_num Line number 'log_message()' is called from
  /// \param[in] file_name Name of the file the 'log_message()' is called from
  /// \param[in] stamp Timestamp for the message
  /// \param[in] text String that will be logged logged
  /// \cert
  /// \deterministic
  void fill_log_message(
    typename MessageT::FlatType & msg,
    LogLevel const severity, uint32_t const line_num, apex::string256_t const & file_name,
    apex::system_clock::time_point const stamp,
    apex::string256_t const & text) const noexcept(false) override
  {
    msg.app_info.host_name = details::BaseLogger<MessageT>::m_host_name;
    msg.app_info.exec_name = details::BaseLogger<MessageT>::m_exec_name;
    msg.app_info.node_name = details::BaseLogger<MessageT>::m_node_name;
    msg.logger_name = details::BaseLogger<MessageT>::m_logger_name;
    msg.level = static_cast<uint8_t>(severity);
    msg.text = text;
    msg.stamp = apex::to_msg_time(stamp);  // time of msg creation
    msg.file_name = file_name;
    msg.line = line_num;
  }

private:
#if defined(APEX_PRINT_LOGS_TO_TERMINAL) && !defined(APEX_CERT)
  /// \brief Print the content of the message to console if APEX_PRINT_LOGS_TO_TERMINAL is set.
  /// It is useful for debugging
  /// \param[in] msg Message to be printed
  void print_to_console(typename MessageT::NonFlatType const & msg) const override
  {
    const auto in_time_t = apex::system_clock::to_time_t(apex::from_msg_time(msg.stamp));

    struct tm timeinfo;
    (void)localtime_r(&in_time_t, &timeinfo);

    // The string is assembled first to avoid a TSAN error
    std::ostringstream oss;
    oss << "[" << std::put_time(&timeinfo, "%Y-%m-%d %X") << "] " <<
      "[" << level_to_str(static_cast<LogLevel>(msg.level)) << "] " <<
      msg.app_info.node_name << " | " << msg.file_name << " @  L" << msg.line << ":\n" <<
      msg.text;

    std::cerr << oss.str() << std::endl;
  }

  /// \brief Print the content of the message to console if APEX_PRINT_LOGS_TO_TERMINAL is set.
  /// It is useful for debugging
  /// \param[in] msg Message to be printed
  void print_to_console(typename MessageT::FlatType const & msg) const override
  {
    const auto in_time_t = apex::system_clock::to_time_t(apex::from_msg_time(msg.stamp));

    struct tm timeinfo;
    (void)localtime_r(&in_time_t, &timeinfo);

    // The string is assembled first to avoid a TSAN error
    std::ostringstream oss;
    oss << "[" << std::put_time(&timeinfo, "%Y-%m-%d %X") << "] " <<
      "[" << level_to_str(static_cast<LogLevel>(msg.level)) << "] " <<
      msg.app_info.node_name << " | " << msg.file_name << " @  L" << msg.line << ":\n" <<
      msg.text;

    std::cerr << oss.str() << std::endl;
  }
#endif
  /*
   AXIVION Next Codeline MisraC++2023-6.7.2: Reason: Code Quality (Functional suitability),
   Justification: The member is private and is required to implement the desired singleton
   semantics without a function local static with unclear lifetime
  */
  inline static std::unique_ptr<details::RootLogger<Logger<MessageT>>> g_root_logger;
};

/// \typedef LoggerBase
/// \brief Define an alias for Logger<apex_msgs::msg::LogMessage>
using LoggerBase = Logger<apex_msgs::msg::LogMessage>;

}  // namespace logging
}  // namespace apex

#endif  // LOGGING__LOGGING_HPP_
