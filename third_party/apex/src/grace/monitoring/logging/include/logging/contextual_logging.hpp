// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

/// \file
/// \brief This file defines the contextual logging class

#ifndef LOGGING__CONTEXTUAL_LOGGING_HPP_
#define LOGGING__CONTEXTUAL_LOGGING_HPP_

#include <apex_msgs/msg/context_field.hpp>
#include <apex_msgs/msg/context_log_message.hpp>
#include <apexcpp/apexcpp.hpp>
#include <bounded_vector/bounded_vector.hpp>
#include <cpputils/common_exceptions.hpp>
#include <logging/details/base_logging.hpp>
#include <logging/visibility_control.hpp>
#include <logging/levels.hpp>
#include <rclcpp/rclcpp.hpp>
#include <string/string.hpp>

#include <functional>
#include <utility>

#ifdef APEX_PRINT_LOGS_TO_TERMINAL
#include <sstream>
#include <iomanip>
#include <ctime>
#endif  // #ifdef APEX_PRINT_LOGS_TO_TERMINAL

/// \brief Namespace for apex
namespace apex
{
/// \brief Namespace for logging related classes, constants, and functions
namespace logging
{

/// \brief Read the context field value associated with provided label from the message
/// \param[in] msg ContextLogMessage message from which context field is read
/// \param[in] label Label of the context field to be read
/// \return Context field value associated with requested label
/// \throw apex::runtime_error if message does not contain requested field label
apex_msgs::ContextData LOGGING_PUBLIC read_context_field(
  apex_msgs::msg::ContextLogMessage const & msg,
  apex_msgs::msg::ContextLogMessage::_context_fields_type::value_type::_label_type const & label)
noexcept(false);

/// \brief Convert logging context data union to string
/// Check which value is set according to the union discriminator and convert it to a string
/// \param[in] data Context data field to be printed
/// \return Currently set union value converted to a string. Returns an empty string if no value
/// is set
/// \cert
/// \deterministic
apex::string256_t LOGGING_PUBLIC context_field_data_to_string(apex_msgs::ContextData const & data);

/// \brief Constructs ContextData contextual data type with integer member and assigns the
/// corresponding union discriminator
/// \param[in] value An integer value will be assigned to the ContextData union
/// \return ContextData with integer value
/// \cert
/// \deterministic
apex_msgs::ContextData LOGGING_PUBLIC create_ctx_data(
  apex_msgs::ContextData::_int_data_type const value);

/// \brief Constructs ContextData contextual data type with string member and assigns the
/// corresponding union discriminator
/// \param[in] value A string value will be assigned to the ContextData union
/// \return ContextData with string value
/// \cert
/// \deterministic
apex_msgs::ContextData LOGGING_PUBLIC create_ctx_data(
  apex_msgs::ContextData::_str_data_type const & value);

/// \brief Constructs ContextData contextual data type with floating point member and assigns the
/// corresponding union discriminator
/// \param[in] value A floating point value will be assigned to the ContextData union
/// \return ContextData with floating point value
/// \cert
/// \deterministic
apex_msgs::ContextData LOGGING_PUBLIC create_ctx_data(
  apex_msgs::ContextData::_float_data_type const value);

/// \brief This class contains the functionality to publish debugging messages on the 'logging'
/// topic
/// \tparam MessageT Type of the log message. ContextLogMessage type by default
template<typename MessageT = apex_msgs::msg::ContextLogMessage>
class LOGGING_PUBLIC ContextualLogger final : public details::BaseLogger<MessageT>
{
public:
  using CtxFieldsT = typename MessageT::_context_fields_type;
  using CtxFieldLabelT = typename CtxFieldsT::value_type::_label_type;
  using CtxFieldDataT = typename CtxFieldsT::value_type::_data_type;
  using CtxFieldDataDiscriminatorT =
    typename CtxFieldsT::value_type::_data_type::_discriminator_type;
  using CtxFieldDataGetter = std::function<CtxFieldDataT(void)>;
  using CtxFieldLabelListT = apex::BoundedVector<CtxFieldLabelT, CtxFieldsT::capacity()>;
  using IntCtxFieldT = std::pair<CtxFieldLabelT, CtxFieldDataGetter>;
  using IntCtxT = apex::BoundedVector<IntCtxFieldT, CtxFieldsT::capacity()>;

  /// \brief The default logging topic name
  static constexpr char const * DEFAULT_LOGGING_TOPIC_NAME = "/context_logging";

  /// \brief Constructor for the ContextualLogger. Meant to live within a node
  /// Wrapper around a rclcpp::Publisher, which is used to publish messages
  /// \param[in] node_ptr Pointer of the node associated with this logger. It is used to
  /// register the context of the logger and create a publisher
  /// \param[in] logger_name Name of the logger instance. Must be unique inside a node
  /// \param[in] history_depth The depth of message history.
  /// Warning: Bursting more messages before the publisher can publish a single message from the
  /// queue will result in messages getting lost
  explicit LOGGING_PUBLIC ContextualLogger(
    rclcpp::Node * const node_ptr,
    apex::string_strict256_t const & logger_name = "",
    const size_t history_depth = DEFAULT_HISTORY_DEPTH,
    apex::string_strict256_t const & logging_topic_name = DEFAULT_LOGGING_TOPIC_NAME)
  : details::BaseLogger<MessageT>(node_ptr, logger_name, history_depth, logging_topic_name)
  {
    details::BaseLogger<MessageT>::m_enabled = details::BaseLogger<MessageT>::load_enabled();
    details::BaseLogger<MessageT>::m_print_to_terminal_enabled =
      details::BaseLogger<MessageT>::load_print_logs_to_terminal_enabled();
  }

  /// \brief Get the contextual root logger. May be empty if rclcpp::shutdown has been called
  /// \return std::optional containing the root logger if it is available
  static const apex::optional<std::reference_wrapper<const ContextualLogger<MessageT>>>
  get_root_logger()
  {
    if (!g_root_logger) {
      const auto & ctx = rclcpp::contexts::get_global_default_context();
      std::scoped_lock lock(ctx->get_init_mutex());
      if (!rclcpp::ok()) {
        return std::nullopt;
      }
      if (!g_root_logger) {
        g_root_logger = std::make_unique<details::RootLogger<ContextualLogger<MessageT>>>(
          "_root_logger_node", "root_logger");
        ctx->on_shutdown([]() {
            g_root_logger.reset();
          });
      }
    }
    return apex::make_optional(std::cref(*g_root_logger->get_logger()));
  }

  /// \brief Get the contextual root logger
  /// \return the root logger
  [[deprecated("The root logger instance is invalid after rclcpp::shutdown has been called. Please use get_root_logger() instead which can handle this.")]]
  static const ContextualLogger<MessageT> & root_logger()
  {
    const auto maybe_root_logger = get_root_logger();
    if (!maybe_root_logger) {
      throw apex::runtime_error{
              "The root logger instance is invalid after rclcpp::shutdown has been called."};
    }
    return *maybe_root_logger;
  }

  /// \brief Log a message with local context fields
  /// \param[in] severity Logging level of the message
  /// \param[in] line_num Line number 'log_message_with_context()' is called from
  /// \param[in] file_name Name of the file the 'log_message_with_context()' is called from
  /// \param[in] context Local context fields
  /// \param[in] args Arguments that will be parsed into a string and logged
  /// \cert
  /// \deterministic
  template<typename ... Args>
  void log_message_with_context(
    LogLevel const severity, std::uint32_t const line_num,
    apex::string256_t const & file_name, IntCtxT const & context,
    Args const & ... args) const noexcept(false)
  {
    auto loaned_msg = details::BaseLogger<MessageT>::m_pub_ptr->borrow_loaned_message();
    fill_log_message_with_context(loaned_msg.get(), severity, line_num, file_name,
      apex::system_clock::now(), apex::varargs_to_string(args ...), context);
#if defined(APEX_PRINT_LOGS_TO_TERMINAL) && !defined(APEX_CERT)
    if (this->m_print_to_terminal_enabled) {
      this->print_to_console(loaned_msg.get());
    }
#endif
    details::BaseLogger<MessageT>::m_pub_ptr->publish(std::move(loaned_msg));
  }

  /// \brief Add context data field that will be logged with each log statement. Replace context
  /// field value if provided label is already existing
  /// \param[in] label Label of the context field
  /// \param[in] getter Callee to get context field data on demand
  /// \throw apex::runtime_error if define more than MessageT context fields capacity
  /// \cert
  /// \deterministic
  void add_context_field(
    CtxFieldLabelT const & label,
    CtxFieldDataGetter const & getter) noexcept(false)
  {
    constexpr auto max_size = decltype(m_ctx)::capacity();
    auto itr = std::find_if(m_ctx.begin(), m_ctx.end(), [&label](IntCtxFieldT const & field) {
          return field.first == label;
        });
    const bool not_yet_defined = (itr == m_ctx.end());
    if (not_yet_defined) {
      if ((m_ctx.size() + 1U) > max_size) {
        throw apex::runtime_error("Not possible to define more than", max_size, "context fields");
      }
      m_ctx.emplace_back(std::make_pair(label, getter));
    } else {
      (*itr).second = getter;
    }
  }

  /// \brief Remove context data field
  /// \param[in] label Label of the context field to be removed
  /// \return True if field has been removed, false if there is no such field
  /// \cert
  /// \deterministic
  bool remove_context_field(CtxFieldLabelT const & label) noexcept(false)
  {
    auto itr = std::find_if(m_ctx.begin(), m_ctx.end(), [&label](IntCtxFieldT const & field) {
          return field.first == label;
        });
    if (itr != m_ctx.end()) {
      m_ctx.erase(itr);
      return true;
    }
    return false;
  }

  /// \brief Remove all context data fields
  /// \cert
  /// \deterministic
  void clear_context_fields() noexcept
  {
    m_ctx.clear();
  }

  /// \brief Get a list of added logging context labels
  /// \return A list of logging context labels
  /// \cert
  /// \deterministic
  CtxFieldLabelListT get_context_fields_labels() const noexcept(false)
  {
    CtxFieldLabelListT labels;
    for (auto itr = m_ctx.begin(); itr != m_ctx.end(); ++itr) {
      labels.push_back((*itr).first);
    }
    return labels;
  }

  /// \brief Fill a pre-allocated MessageT with logging data
  /// \param[out] msg Pre-allocated message to be filled
  /// \param[in] severity Logging level of the message
  /// \param[in] line_num Line number 'log_message()' is called from
  /// \param[in] file_name Name of the file the 'log_message()' is called from
  /// \param[in] stamp Timestamp for the message
  /// \param[in] text String that will be logged logged
  /// \cert
  /// \deterministic
  void fill_log_message(
    typename MessageT::NonFlatType & msg,
    LogLevel const severity, uint32_t const line_num, apex::string256_t const & file_name,
    apex::system_clock::time_point const stamp,
    apex::string256_t const & text) const noexcept(false) override
  {
    fill_log_message_with_context(msg, severity, line_num, file_name, stamp, text, m_ctx);
  }

  /// \brief Fill a pre-allocated MessageT with logging data
  /// \param[out] msg Pre-allocated message to be filled
  /// \param[in] severity Logging level of the message
  /// \param[in] line_num Line number 'log_message()' is called from
  /// \param[in] file_name Name of the file the 'log_message()' is called from
  /// \param[in] stamp Timestamp for the message
  /// \param[in] text String that will be logged logged
  /// \cert
  /// \deterministic
  void fill_log_message(
    typename MessageT::FlatType & msg,
    LogLevel const severity, uint32_t const line_num, apex::string256_t const & file_name,
    apex::system_clock::time_point const stamp,
    apex::string256_t const & text) const noexcept(false) override
  {
    fill_log_message_with_context(msg, severity, line_num, file_name, stamp, text, m_ctx);
  }

  /// \brief Fill a pre-allocated MessageT with logging data
  /// \param[out] msg Pre-allocated message to be filled
  /// \param[in] severity Logging level of the message
  /// \param[in] line_num Line number 'log_message()' is called from
  /// \param[in] file_name Name of the file the 'log_message()' is called from
  /// \param[in] stamp Timestamp for the message
  /// \param[in] text String that will be logged logged
  /// \param[in] context Context fields
  /// \cert
  /// \deterministic
  template<
    typename T = MessageT,
    std::enable_if_t<std::is_same<T, typename MessageT::FlatType>::value || std::is_same<T,
    typename MessageT::NonFlatType>::value> * = nullptr>
  void fill_log_message_with_context(
    T & msg, LogLevel const severity, uint32_t const line_num,
    apex::string256_t const & file_name,
    apex::system_clock::time_point const stamp, apex::string256_t const & text,
    IntCtxT const & context) const noexcept(false)
  {
    msg.app_info.host_name = details::BaseLogger<MessageT>::m_host_name;
    msg.app_info.exec_name = details::BaseLogger<MessageT>::m_exec_name;
    msg.app_info.node_name = details::BaseLogger<MessageT>::m_node_name;
    msg.logger_name = details::BaseLogger<MessageT>::m_logger_name;
    msg.level = static_cast<uint8_t>(severity);
    msg.text = text;
    msg.stamp = apex::to_msg_time(stamp);
    msg.file_name = file_name;
    msg.line = line_num;

    fill_log_message_context(msg, context);
  }

private:
  /// \brief Fill a pre-allocated MessageT with context data
  /// \param[out] msg Pre-allocated message to be filled
  /// \param[in] context Context fields
  /// \cert
  /// \deterministic
  template<
    typename T = MessageT,
    std::enable_if_t<std::is_same<T, typename MessageT::FlatType>::value || std::is_same<T,
    typename MessageT::NonFlatType>::value> * = nullptr>
  void fill_log_message_context(T & msg, IntCtxT const & context) const noexcept(false)
  {
    for (auto itr = context.begin(); itr != context.end(); ++itr) {
      if ((*itr).second) {
        msg.context_fields.push_back(
          T::_context_fields_type::value_type::create_msg((*itr).first, (*itr).second()));
      }
    }
  }

#if defined(APEX_PRINT_LOGS_TO_TERMINAL) && !defined(APEX_CERT)
  /// \brief Print the content of the message to console if APEX_PRINT_LOGS_TO_TERMINAL is defined.
  /// It is useful for debugging
  /// \param[in] msg Message to be printed
  void print_to_console(typename MessageT::NonFlatType const & msg) const override
  {
    std::stringstream ss;
    for (auto itr = msg.context_fields.begin(); itr != msg.context_fields.end(); ++itr) {
      ss << itr->label << ": " << context_field_data_to_string(itr->data);
      if ((itr + 1U) != msg.context_fields.end()) {
        ss << ", ";
      }
    }

    const auto in_time_t = apex::system_clock::to_time_t(apex::from_msg_time(msg.stamp));

    struct tm timeinfo;
    localtime_r(&in_time_t, &timeinfo);

    std::cerr << "[" << std::put_time(&timeinfo, "%Y-%m-%d %X") << "] " <<
      "[" << level_to_str(static_cast<LogLevel>(msg.level)) << "] " <<
      msg.app_info.node_name << " | " << msg.file_name << " @  L" << msg.line << ":\n" <<
      msg.text;

    if (ss.rdbuf()->in_avail() > 0U) {
      std::cerr << "\ncontext: {" << ss.str() << "}";
    }

    std::cerr << std::endl;
  }

  /// \brief Print the content of the message to console if APEX_PRINT_LOGS_TO_TERMINAL is defined.
  /// It is useful for debugging
  /// \param[in] msg Message to be printed
  void print_to_console(typename MessageT::FlatType const & msg) const override
  {
    std::stringstream ss;
    for (auto itr = msg.context_fields.begin(); itr != msg.context_fields.end(); ++itr) {
      ss << itr->label << ": " << context_field_data_to_string(itr->data);
      if ((itr + 1U) != msg.context_fields.end()) {
        ss << ", ";
      }
    }

    const auto in_time_t = apex::system_clock::to_time_t(apex::from_msg_time(msg.stamp));

    struct tm timeinfo;
    localtime_r(&in_time_t, &timeinfo);

    std::cerr << "[" << std::put_time(&timeinfo, "%Y-%m-%d %X") << "] " <<
      "[" << level_to_str(static_cast<LogLevel>(msg.level)) << "] " <<
      msg.app_info.node_name << " | " << msg.file_name << " @  L" << msg.line << ":\n" <<
      msg.text;

    if (ss.rdbuf()->in_avail() > 0U) {
      std::cerr << "\ncontext: {" << ss.str() << "}";
    }

    std::cerr << std::endl;
  }
#endif

  IntCtxT m_ctx;
  /*
   AXIVION Next Codeline MisraC++2023-6.7.2: Reason: Code Quality (Functional suitability),
   Justification: The member is private and is required to implement the desired singleton
   semantics without a function local static with unclear lifetime
  */
  inline static std::unique_ptr<details::RootLogger<ContextualLogger<MessageT>>> g_root_logger;
};

}  // namespace logging
}  // namespace apex

#endif  // LOGGING__CONTEXTUAL_LOGGING_HPP_
