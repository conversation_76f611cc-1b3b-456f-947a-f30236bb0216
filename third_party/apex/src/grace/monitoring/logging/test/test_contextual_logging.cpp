// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <logging/contextual_logging.hpp>
#include <logging/logging_macros.hpp>
#include <rclcpp/dynamic_waitset/waitset.hpp>
#include <apex_test_tools/apex_skip.hpp>

#include <chrono>
#include <memory>
#include <stdexcept>
#include <utility>
#include <vector>

using namespace std::chrono_literals;

class TestContextualLogging : public ::testing::Test
{
public:
  static void SetUpTestCase()
  {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
  }
  static void TearDownTestCase()
  {
    rclcpp::shutdown();
  }

protected:
  void SetUp() override
  {
    m_logger_node = std::make_shared<rclcpp::Node>("logger_node");
    m_test_logger = std::make_shared<apex::logging::ContextualLogger<>>(
      m_logger_node.get(), "test_logger");
  }

  std::shared_ptr<rclcpp::Node> m_logger_node;
  std::shared_ptr<apex::logging::ContextualLogger<>> m_test_logger;
};

TEST_F(TestContextualLogging, add_context_field_no_throw) {
  EXPECT_NO_THROW(m_test_logger->add_context_field("ctx_field_label_1", [] {
      return apex::logging::create_ctx_data("ctx_field_value_1");
    }));

  const auto test_ctx_field_getter = [] {
      return apex::logging::create_ctx_data("ctx_field_value_2");
    };
  EXPECT_NO_THROW(m_test_logger->add_context_field("ctx_field_label_2", test_ctx_field_getter));

  EXPECT_NO_THROW(m_test_logger->add_context_field("int_ctx_field_label", [] {
      return apex::logging::create_ctx_data(42);
    }));
  EXPECT_NO_THROW(m_test_logger->add_context_field("float_ctx_field_label", [] {
      return apex::logging::create_ctx_data(42.F);
    }));
}

TEST_F(TestContextualLogging, add_context_field_redefine_field_successfully) {
  const apex::string64_t ctx_field_label = "ctx_field_label";
  m_test_logger->add_context_field(ctx_field_label, [] {
      return apex::logging::create_ctx_data("ctx_field_value");
    });
  EXPECT_NO_THROW(m_test_logger->add_context_field(ctx_field_label, [] {
      return apex::logging::create_ctx_data("new_ctx_field_value");
    }));
}

TEST_F(TestContextualLogging, add_context_field_throw_on_capacity_overflow) {
  constexpr auto MAX_CTX_FIELDS_N = apex::logging::ContextualLogger<>::CtxFieldsT::capacity();

  for (auto i{0U}; i < MAX_CTX_FIELDS_N; ++i) {
    EXPECT_NO_THROW(m_test_logger->add_context_field(apex::string64_t{"ctx_field_label_"} +
      apex::to_string(i),
      [i] {
        return apex::logging::create_ctx_data(apex::string256_t{"ctx_field_value_"} +
        apex::to_string(i));
      }));
  }
  EXPECT_THROW(m_test_logger->add_context_field("ctx_field_label_X", [] {
      return apex::logging::create_ctx_data("ctx_field_value_X");
    }), apex::runtime_error);
}

TEST_F(TestContextualLogging, remove_context_field_works_as_expected) {
  m_test_logger->add_context_field("ctx_field_label_1", [] {
      return apex::logging::create_ctx_data("ctx_field_value_1");
    });

  EXPECT_TRUE(m_test_logger->remove_context_field("ctx_field_label_1"));
  EXPECT_FALSE(m_test_logger->remove_context_field("ctx_field_label_1"));
}

TEST_F(TestContextualLogging, clear_context_fields_works_as_expected) {
  m_test_logger->add_context_field("ctx_field_label_1", [] {
      return apex::logging::create_ctx_data("ctx_field_value_1");
    });
  m_test_logger->add_context_field("ctx_field_label_2", [] {
      return apex::logging::create_ctx_data("ctx_field_value_2");
    });
  m_test_logger->add_context_field("ctx_field_label_3", [] {
      return apex::logging::create_ctx_data("ctx_field_value_3");
    });

  m_test_logger->clear_context_fields();

  // Expecting that all fields have been already removed
  EXPECT_FALSE(m_test_logger->remove_context_field("ctx_field_label_1"));
  EXPECT_FALSE(m_test_logger->remove_context_field("ctx_field_label_2"));
  EXPECT_FALSE(m_test_logger->remove_context_field("ctx_field_label_3"));
}

TEST_F(TestContextualLogging, get_context_fields_labels_works_as_expected) {
  m_test_logger->add_context_field("ctx_field_label_1", [] {
      return apex::logging::create_ctx_data("ctx_field_value_1");
    });
  m_test_logger->add_context_field("ctx_field_label_2", [] {
      return apex::logging::create_ctx_data("ctx_field_value_2");
    });
  m_test_logger->add_context_field("ctx_field_label_3", [] {
      return apex::logging::create_ctx_data("ctx_field_value_3");
    });

  const auto ctx_labels = m_test_logger->get_context_fields_labels();

  EXPECT_TRUE(std::find(ctx_labels.begin(), ctx_labels.end(),
    "ctx_field_label_1") != ctx_labels.end());
  EXPECT_TRUE(std::find(ctx_labels.begin(), ctx_labels.end(),
    "ctx_field_label_2") != ctx_labels.end());
  EXPECT_TRUE(std::find(ctx_labels.begin(), ctx_labels.end(),
    "ctx_field_label_3") != ctx_labels.end());
  EXPECT_FALSE(std::find(ctx_labels.begin(), ctx_labels.end(),
    "ctx_field_label_4") != ctx_labels.end());
}

TEST_F(TestContextualLogging, contextual_logging_main_functionality) {
  const auto test_node = std::make_shared<rclcpp::Node>("test_node");
  const auto logging_sub =
    test_node->create_polling_subscription<apex_msgs::msg::ContextLogMessage>(
    m_test_logger->get_publisher()->get_topic_name(), rclcpp::UnitTestDefaultsQoS());
  rclcpp::dynamic_waitset::Waitset ws(logging_sub);

  const auto receive_and_check_msg = [&logging_sub, &ws](
    const std::vector<apex::string64_t> & ctx_field_names,
    const std::vector<apex_msgs::ContextData> & ctx_field_values) {
      if (ws.wait(1s)) {
        const auto loaned_msgs = logging_sub->take();
        if (!loaned_msgs.empty() && loaned_msgs.front().info().valid()) {
          const auto msg = loaned_msgs.front().data();
          EXPECT_EQ(msg.context_fields.size(), ctx_field_values.size());
          for (auto i{0U}; i < ctx_field_names.size(); ++i) {
            EXPECT_EQ(apex::logging::read_context_field(msg,
              ctx_field_names[i]), ctx_field_values[i]);
          }
        } else {
          throw std::runtime_error("Did not receive the message");
        }
      } else {
        throw std::runtime_error("PollingSubscription timed out during test");
      }
    };

  m_test_logger->get_publisher()->wait_for_matched(1U, 3s);

  const apex::string64_t static_ctx_field_name = "static_ctx_field";
  const auto static_ctx_field_value = apex::logging::create_ctx_data("static_ctx_value");
  m_test_logger->add_context_field(static_ctx_field_name, [&static_ctx_field_value] {
      return static_ctx_field_value;
    });

  const apex::string64_t mutable_ctx_field_name = "mutable_ctx_field";
  auto mutable_ctx_field_value = apex::logging::create_ctx_data("mutable_ctx_value");
  m_test_logger->add_context_field(mutable_ctx_field_name, [&mutable_ctx_field_value] {
      return mutable_ctx_field_value;
    });

  APEX_INFO(*m_test_logger, "Hello World");
  EXPECT_NO_THROW(receive_and_check_msg({static_ctx_field_name, mutable_ctx_field_name},
    {static_ctx_field_value, mutable_ctx_field_value}));

  mutable_ctx_field_value.str_data("new_mutable_ctx_value");
  APEX_INFO(*m_test_logger, "Hello World");
  EXPECT_NO_THROW(receive_and_check_msg({static_ctx_field_name, mutable_ctx_field_name},
    {static_ctx_field_value, mutable_ctx_field_value}));
}

TEST_F(TestContextualLogging, two_log_message_types_works_on_the_same_topic_name) {
  APEX_SKIP_TEST(31388, "Apex.Ida v3 does not support different types on the same topic");

  const auto conv_logger_node = std::make_shared<rclcpp::Node>("conventional_logger_node");
  std::shared_ptr<apex::logging::Logger<>> conv_logger;
  EXPECT_NO_THROW(conv_logger =
    std::make_shared<apex::logging::Logger<>>(conv_logger_node.get(), "conventional_logger",
    apex::logging::DEFAULT_HISTORY_DEPTH, m_test_logger->get_publisher()->get_topic_name()));

  const auto test_node = std::make_shared<rclcpp::Node>("test_node");

  const auto conv_logging_sub = test_node->create_polling_subscription<apex_msgs::msg::LogMessage>(
    conv_logger->get_publisher()->get_topic_name(), apex::logging::DefaultLoggingQoS());
  rclcpp::dynamic_waitset::Waitset conv_ws(conv_logging_sub);

  const auto ctx_logging_sub =
    test_node->create_polling_subscription<apex_msgs::msg::ContextLogMessage>(
    m_test_logger->get_publisher()->get_topic_name(), apex::logging::DefaultLoggingQoS());
  rclcpp::dynamic_waitset::Waitset ctx_ws(ctx_logging_sub);

  constexpr auto CONV_LOG_TEXT = "Hello from conventional logger";
  constexpr auto CTX_LOG_TEXT = "Hello from contextual logger";

  m_test_logger->get_publisher()->wait_for_matched(1U, 3s);
  conv_logger->get_publisher()->wait_for_matched(1U, 3s);

  EXPECT_NO_THROW(APEX_INFO(*conv_logger, CONV_LOG_TEXT));

  if (conv_ws.wait(1s)) {
    const auto loaned_msgs = conv_logging_sub->take();
    if (!loaned_msgs.empty() && loaned_msgs.front().info().valid()) {
      const auto msg = loaned_msgs.front().data();
      EXPECT_EQ(msg.text, CONV_LOG_TEXT);
    } else {
      FAIL() << "Did not receive conventional log message";
    }
  } else {
    FAIL() << "Waitset timed out";
  }

  EXPECT_NO_THROW(APEX_INFO(*m_test_logger, CTX_LOG_TEXT));

  if (ctx_ws.wait(1s)) {
    const auto loaned_msgs = ctx_logging_sub->take();
    if (!loaned_msgs.empty() && loaned_msgs.front().info().valid()) {
      const auto msg = loaned_msgs.front().data();
      EXPECT_EQ(msg.text, CTX_LOG_TEXT);
    } else {
      FAIL() << "Did not receive contextual log message";
    }
  } else {
    FAIL() << "Waitset timed out";
  }
}

TEST_F(TestContextualLogging, add_contex_to_individual_logging_statements) {
  const auto test_node = std::make_shared<rclcpp::Node>("test_node");
  const auto logging_sub =
    test_node->create_polling_subscription<apex_msgs::msg::ContextLogMessage>(
    m_test_logger->get_publisher()->get_topic_name(), rclcpp::UnitTestDefaultsQoS());
  rclcpp::dynamic_waitset::Waitset ws(logging_sub);

  const auto receive_and_check_msg =
    [&logging_sub, &ws](const apex::string64_t & ctx_field_name,
      const apex_msgs::ContextData & ctx_field_value) {
      if (ws.wait(1s)) {
        const auto loaned_msgs = logging_sub->take();
        if (!loaned_msgs.empty() && loaned_msgs.front().info().valid()) {
          const auto msg = loaned_msgs.front().data();
          EXPECT_EQ(msg.context_fields.size(), 1U);
          EXPECT_EQ(apex::logging::read_context_field(msg, ctx_field_name), ctx_field_value);
        } else {
          throw std::runtime_error("Did not receive the message");
        }
      } else {
        throw std::runtime_error("PollingSubscription timed out during test");
      }
    };

  m_test_logger->get_publisher()->wait_for_matched(1U, 3s);

  const apex::string64_t ctx_field_name = "ctx_field";
  auto ctx_field_value = apex::logging::create_ctx_data("ctx_value");
  apex::logging::ContextualLogger<>::IntCtxT local_context =
  {std::make_pair(ctx_field_name, [&ctx_field_value] {
        return ctx_field_value;
      })};
  m_test_logger->log_message_with_context(apex::logging::LogLevel::INFO, __LINE__, __FILE__,
    local_context, "Hello World");

  EXPECT_NO_THROW(receive_and_check_msg(ctx_field_name, ctx_field_value));
}

TEST(TestContextualLoggingFreeFn, test_context_field_data_to_string_with_valid_union) {
  auto ctx_data = apex::logging::create_ctx_data("string data");
  ASSERT_EQ(apex::logging::context_field_data_to_string(ctx_data), "string data");
  ctx_data.int_data(42L);
  ASSERT_EQ(apex::logging::context_field_data_to_string(ctx_data), "42");
  ctx_data.float_data(5.5);
  // Using HasSubstr since float value can be printed in different ways on different systems
  EXPECT_THAT(apex::logging::context_field_data_to_string(ctx_data), ::testing::HasSubstr("5"));
}

TEST(TestContextualLoggingFreeFn, test_context_field_data_to_string_with_invalid_union) {
  auto ctx_data = apex_msgs::ContextData();
  ASSERT_FALSE(ctx_data);
  ASSERT_EQ(apex::logging::context_field_data_to_string(ctx_data), "");
  EXPECT_THAT(apex::logging::context_field_data_to_string(ctx_data), ::testing::IsEmpty());
}

TEST(TestContextualLoggingFreeFn, test_create_ctx_data_overloads) {
  const auto int_ctx_data = apex::logging::create_ctx_data(42);
  ASSERT_TRUE(int_ctx_data);
  ASSERT_EQ(int_ctx_data.int_data(), 42);

  const auto str_ctx_data = apex::logging::create_ctx_data("hypnotoad");
  ASSERT_TRUE(str_ctx_data);
  ASSERT_EQ(str_ctx_data.str_data(), "hypnotoad");

  const auto float_ctx_data = apex::logging::create_ctx_data(42.F);
  ASSERT_TRUE(float_ctx_data);
  ASSERT_FLOAT_EQ(float_ctx_data.float_data(), 42.F);
}
