// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

/// \file
/// \brief Example usage of contextual logging functions

#include <gtest/gtest.h>

#include <rclcpp/rclcpp.hpp>
#include <logging/contextual_logging.hpp>
#include <logging/logging_macros.hpp>
#include <std_msgs/msg/string.hpp>

#include <memory>
#include <random>
#include <string>
#include <thread>
#include <utility>

namespace
{

static constexpr char ECU_ID[] = "front-left-ecu-f741f186";

apex_msgs::ContextData generate_uuid()
{
  static std::default_random_engine random_eng;
  std::uniform_int_distribution<int> distribution{0, 15};

  constexpr char chars[] =
  {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
  constexpr bool dash[] = {0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0};

  std::string id;
  for (auto i{0U}; i < 16U; ++i) {
    if (dash[i]) {
      id += "-";
    }
    id += chars[distribution(random_eng)];
    id += chars[distribution(random_eng)];
  }
  return apex::logging::create_ctx_data(id);
}

}  // namespace

namespace apex
{
namespace logging
{

// The executor package cannot be used here since that would introduce a circular dependency
// (the executor depends on the logging package). This example follows the Apex.OS node API
// anyway for the sake of the example, but do not explicitly inherit from it.

// no-op mock of apex_node_base so the example class can use 'override'
class mock_apex_node_base
{
public:
  virtual void execute_impl() = 0;
  virtual ~mock_apex_node_base() = default;
};

class MyNodeWithContextualLogging : public mock_apex_node_base
{
public:
  MyNodeWithContextualLogging(
    const apex::string_strict256_t & node_name,
    const apex::string_strict256_t & node_namespace)
  : m_node{node_name, node_namespace},
    m_publisher{get_rclcpp_node().create_publisher<std_msgs::msg::String>(
        "/topic",
        rclcpp::DefaultQoS())},
    //! [logger_name]
    m_logger{&get_rclcpp_node(), "MyContextualLoggerName"}  // (1)!
    //! [logger_name]
  {
    //! [add_logging_context]
    m_logger.add_context_field("ecu_id",
      [] {
        return apex::logging::create_ctx_data(::ECU_ID);  // (1)!
      });  // (2)!
    m_logger.add_context_field("message_id", ::generate_uuid);  // (3)!
    //! [add_logging_context]
  }

  ~MyNodeWithContextualLogging()
  {
    //! [remove_logging_context]
    m_logger.remove_context_field("message_id");  // (1)!
    m_logger.clear_context_fields();  // (2)!
    //! [remove_logging_context]
  }

  void execute_impl() override
  {
    //! [log_with_macros]
    const std::string msg_data = "Hello world!";
    auto string_msg = m_publisher->borrow_loaned_message();
    string_msg->data = msg_data;
    m_publisher->publish(std::move(string_msg));
    APEX_INFO(m_logger, "Published message:", msg_data);  // (1)!
    //! [log_with_macros]
    //! [use_log_message_directly]
    apex::logging::ContextualLogger<>::IntCtxT local_context =
    {std::make_pair("additional_msg_id", ::generate_uuid)};  // (1)!
    m_logger.log_message_with_context(
      apex::logging::LogLevel::INFO, __LINE__, __FILE__,
      local_context, "Published message:", msg_data);  // (2)!
    //! [use_log_message_directly]
  }

private:
  rclcpp::Node m_node;
  const rclcpp::Publisher<std_msgs::msg::String>::SharedPtr m_publisher;

  // simulate the Apex.OS node API
  rclcpp::Node & get_rclcpp_node()
  {
    return m_node;
  }

  //! [Private members]
  apex::logging::ContextualLogger<> m_logger;
  //! [Private members]
};

}  // namespace logging
}  // namespace apex

class TestNodeWithContextualLogging : public ::testing::Test
{
public:
  static void SetUpTestCase()
  {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
  }
  static void TearDownTestCase()
  {
    rclcpp::shutdown();
  }
};

TEST_F(TestNodeWithContextualLogging, node_with_logging_documentation_example) {
  auto node = std::make_shared<apex::logging::MyNodeWithContextualLogging>(
    "MyNodeWithContextualLogging", "");

// TODO(lander.usategui): On QNX710/QEMU there exists timing issues due to
// usage of steady_clock. When #9137 is fixed to use steady_clock, remove this block
#if defined(QNX710) && (defined(_M_AMD64) || defined(__amd64__))
  const auto now = std::chrono::system_clock::now;
#else
  const auto now = std::chrono::steady_clock::now;
#endif  // QNX710 x86_64
  // Since we cannot use the executor package (see above), call execute_impl directly
  const auto start = now();
  while ((now() - start) < std::chrono::seconds{3}) {
    node->execute_impl();
    std::this_thread::sleep_for(std::chrono::milliseconds{500});
  }
}

TEST_F(TestNodeWithContextualLogging, logging_default_topic_preserves_without_defined_namespace)
{
  rclcpp::Node node{"contextual_logger_test_node"};

  apex::logging::ContextualLogger<> logger{&node, node.get_name()};

  ASSERT_STREQ(logger.get_publisher()->get_topic_name(), "/context_logging");
}

TEST_F(TestNodeWithContextualLogging, logging_default_topic_preserves_with_defined_namespace)
{
  rclcpp::Node node{"contextual_logger_test_node", "contextual_logger_test_node_namespace"};

  apex::logging::ContextualLogger<> logger{&node, node.get_name()};

  ASSERT_STREQ(logger.get_publisher()->get_topic_name(), "/context_logging");
}
