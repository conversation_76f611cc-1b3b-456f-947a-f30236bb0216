// Copyright 2023 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>

#include <apex_test_tools/test_macros.hpp>
#include <logging/contextual_logging.hpp>
#include <logging/logging.hpp>
#include <settings/repository.hpp>

#include <chrono>
#include <string>
#include <cstdlib>

#include "utils.hpp"

namespace apex
{
namespace logging
{
namespace
{

using LoggerTypes =
  ::testing::Types<LoggerBase, ContextualLogger<>>;
TYPED_TEST_SUITE(TestLogging, LoggerTypes);

template<class LoggerT>
void barrage_log(const LoggerT & logger, const std::string & message)
{
  logger.log_message(LogLevel::TRACE, 0, "", message.c_str());
  logger.log_message(LogLevel::DEBUG, 0, "", message.c_str());
  logger.log_message(LogLevel::INFO, 0, "", message.c_str());
  logger.log_message(LogLevel::WARN, 0, "", message.c_str());
  logger.log_message(LogLevel::ERROR, 0, "", message.c_str());
  logger.log_message(LogLevel::FATAL, 0, "", message.c_str());
}

// cppcheck-suppress syntaxError
TYPED_TEST(TestLogging, when_logging_level_is_fatal_apex_others_are_ignored) {
  using apex::settings::construct::make_dictionary;
  using apex::settings::construct::entry;
  apex::settings::construct::dictionary dict;
  dict["logging"] = make_dictionary(entry("level", "FATAL"));
  apex::settings::repository::set(dict);
  auto original_level = getenv("APEX_GRACE_LOG_LEVEL");
  setenv("APEX_GRACE_LOG_LEVEL", "FATAL", 1);  // ensure test env doesnt break this test
  TestFixture::SetUpLogger();

  barrage_log(
    *TestFixture::m_test_logger, TestFixture::m_text_message);
  EXPECT_NO_THROW(receive_and_check_msg(TestFixture::m_sub, *TestFixture::m_waitset,
    TestFixture::m_text_message, LogLevel::FATAL, {"", 0}));

  TestFixture::m_test_logger->log_message(LogLevel::TRACE, 0, "",
    TestFixture::m_text_message.c_str());
  EXPECT_THROW(receive_and_check_msg(TestFixture::m_sub, *TestFixture::m_waitset,
    TestFixture::m_text_message, LogLevel::TRACE, {"", 0}), std::runtime_error);

  dict["logging"] = make_dictionary(entry("level", "TRACE"));
  apex::settings::repository::set(dict);
  setenv("APEX_GRACE_LOG_LEVEL", original_level, 1);
}

TYPED_TEST(TestLogging, when_logging_level_is_dynamically_set_fatal_apex_others_are_ignored) {
  TestFixture::SetUpLogger();
  TestFixture::m_test_logger->set_log_severity_threshold(LogLevel::FATAL);

  barrage_log(
    *TestFixture::m_test_logger, TestFixture::m_text_message);

  EXPECT_NO_THROW(receive_and_check_msg(TestFixture::m_sub, *TestFixture::m_waitset,
    TestFixture::m_text_message, LogLevel::FATAL, {"", 0}));

  TestFixture::m_test_logger->set_log_severity_threshold(LogLevel::INFO);
  barrage_log(*TestFixture::m_test_logger, TestFixture::m_text_message);

  EXPECT_NO_THROW(receive_and_check_msg(TestFixture::m_sub, *TestFixture::m_waitset,
    TestFixture::m_text_message, LogLevel::INFO, {"", 0}));
}

TYPED_TEST(TestLogging, when_logging_disabled_expect_no_logs) {
  using apex::settings::construct::make_dictionary;
  using apex::settings::construct::entry;
  apex::settings::construct::dictionary dict;
  dict["logging"] = make_dictionary(entry("enabled", false));
  apex::settings::repository::set(dict);
  TestFixture::SetUpLogger();

  barrage_log(
    *TestFixture::m_test_logger, TestFixture::m_text_message);

  ASSERT_EQ(TestFixture::m_sub, nullptr);
  ASSERT_EQ(TestFixture::m_waitset, nullptr);

  dict["logging"] = make_dictionary(entry("enabled", true));
  apex::settings::repository::set(dict);
}

TYPED_TEST(TestLogging, when_logging_level_is_not_parseable_expect_exception) {
  using apex::settings::construct::make_dictionary;
  using apex::settings::construct::entry;
  apex::settings::construct::dictionary dict;
  dict["logging"] = make_dictionary(entry("level", "LETHAL"));
  apex::settings::repository::set(dict);

  EXPECT_THROW_EXCEPTION_WITH_MSG(TestFixture::SetUpLogger(), apex::domain_error,
    "Unknown log severity LETHAL cannot be converted to LogLevel");

  dict["logging"] = make_dictionary(entry("level", "TRACE"));
  apex::settings::repository::set(dict);
}

}  // namespace
}  // namespace logging
}  // namespace apex
