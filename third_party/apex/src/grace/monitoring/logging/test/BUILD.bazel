# Copyright 2024 Apex.AI, Inc.
# All rights reserved.

load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")

cc_library(
    name = "test_lib",
    testonly = True,
    srcs = ["utils.hpp"],
    tags = [
        "exclude_sca",
    ],
    deps = [
        "//grace/monitoring/logging",
        "//grace/tools/ros_domain_coordinator:gtest_main",
    ],
)

filegroup(
    name = "test_srcs_with_req_ids",
    srcs = [
        "test_contextual_logging.cpp",
        "test_levels.cpp",
        "test_logging.cpp",
        "test_node_contextual_logging.cpp",
        "test_node_logging.cpp",
        "test_settings.cpp",
    ],
    visibility = ["//grace/monitoring/logging/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_logging",
    srcs = [":test_srcs_with_req_ids"],
    env = {
        "IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc",
        "APEX_GRACE_LOG_LEVEL": "TRACE",
    },
    tags = ["constrained_test"],
    deps = [
        ":test_lib",
        "//grace/interfaces/std_msgs",
        "//tools/testing/apex_test_tools",
    ],
)

filegroup(
    name = "test_silent_logging_srcs_with_req_ids",
    srcs = ["test_logging_silent.cpp"],
    visibility = ["//grace/monitoring/logging/doc/internal:__subpackages__"],
)

apex_cc_test(
    name = "test_silent_logging",
    srcs = [":test_silent_logging_srcs_with_req_ids"],
    defines = [
        "APEX_DISABLE_LOGGING",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "constrained_test",
    ],
    deps = [
        ":test_lib",
        "//tools/testing/apex_test_tools",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "test_node_contextual_logging.cpp",
        "test_node_logging.cpp",
    ],
    visibility = ["//grace/monitoring/logging/doc/design:__subpackages__"],
)
