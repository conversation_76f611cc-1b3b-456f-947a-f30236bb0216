---
tags:
  - logging
---

# logging

## Purpose

Logging in software applications is a critical functionality that is required to
debug and maintain large scale applications. However, this functionality does
not always come cheap, especially in resource limited or safety-critical applications.

Most input/output operations used in logging rely on blocking calls which are
unacceptable in hard real-time applications, as they are non-deterministic in
time. Additionally, the loggers use unsafe data structures such as the
`std::string` that rely on dynamic memory allocations. See the [safety-critical
coding](https://www.apex.ai/post/porting-algorithms-from-ros-1-to-ros-2#safety-critical-coding)
section of the porting from ROS 1 to ROS 2 blog post, which further explains why
blocking calls and dynamic memory allocations must be avoided in safety-critical
systems.

To make the logging behavior real-time capable, Apex.Grace transports the logged
message to the target log writer via the DDS layer, thereby decoupling the
action of logging (which happens in real-time) from the action of writing the
log (which does not necessarily need to occur in real-time).

### Motivation of a new logging implementation in Apex.Grace

The ROS 2 logging implementation uses a console logger which relies on the `fprintf()` blocking call
at the [lowest
level](https://github.com/ros2/rcutils/blob/9c80b7684f322936d67390cfa48e54f7ef9aa7ef/src/logging.c#L866).
That is why applications relying on the logging provided by ROS 2 come with the cost of having
blocking calls and dynamic memory allocations within the runtime loop of the
application. This means that simply having logging enabled in a system would
break its real-time guarantees.

## Usage

The general architecture of the Apex.Grace logging for a single logger is shown below:

![Logging architecture](apex-os-logging-architecture.png){ .center }

!!! note
    An Apex.Grace node can own multiple `logger` objects, but a logger cannot be
    shared across nodes.
    A global logger ([](apex::logging::Logger::get_root_logger)) is accessible for
    logging without the need of explicitly instantiating a logger.

### Log messages

The published log messages (on the `/logging` topic by default) are of type [`LogMessage`]
(apex-msgs-design.md#logmessage) if [](apex::logging::Logger) is used and [`ContextLogMessage`]
(apex-msgs-design.md#contextlogmessage) in case [](apex::logging::ContextualLogger) is used. The
log levels follow the [log4j]
(https://logging.apache.org/log4j/2.x/log4j-api/apidocs/org/apache/logging/log4j/Level.html):
[](apex::logging::LogLevel).

The maximum number of context fields that can be added to `ContextLogMessage` is specified in the
IDL file:

{{  code_snippet("grace/interfaces/apex_msgs/msg/ContextLogMessage.idl",
    {"tag": "//! [ContextFields]"}, "idl") }}

### Log messages with a per-node logger

Apex.Grace log messages are created through an [](apex::logging::Logger) object,
which is tied to a ROS or Apex.Grace node. It is recommended to include a logger object
as a member in each node.

{{  code_snippet("grace/monitoring/logging/test/test_node_logging.cpp",
    {"tag": "//! [Private members]", "skip_prefix": "//!"},
    "cpp") }}

{{  code_snippet("grace/monitoring/logging/test/test_node_logging.cpp",
    {"tag": "//! [logger_name]", "skip_prefix": "//!"},
    "cpp") }}

1. Add a logger called `MyLoggerName` as a member variable of an Apex.Grace node

Messages are logged with a set of logging macros:

{{  code_snippet("grace/monitoring/logging/test/test_node_logging.cpp",
    {"tag": "//! [execute_impl]", "skip_prefix": "//!"},
    "cpp") }}

1. Log a message with DEBUG level
2. Multiple arguments can be passed to the logging macro, which will be concatenated
   in the final message
3. Non-string arguments are implicitly converted using [](apex::to_string),
   which comes with overloads for various numeric types
4. Apex.Grace log messages are limited to 255 characters per message.
   Anything longer will be silently truncated
5. For printf-style formatting, invoke [](apex::to_string) explicitly
   with a format string. Since the compiler cannot infer the type automatically,
   [](apex::string256_t) needs to be specified explicitly
6. Log a message with ERROR level. This is usually a good way to report
   unexpected exceptions
7. Log with a custom timestamp instead of the default
   `apex::system_clock::now`

!!! note
    - The logging macros accept arbitrarily many arguments, which are
      concatenated into a unified string. Arguments of non-string type
      (e.g., booleans, numbers, or char arrays) are converted using [](apex::to_string)
    - The printf-style overload of [](apex::to_string) is not *deterministic*
      and cannot be used in certified code. The reason is that it uses `snprintf` internally,
      which may potentially allocate

### Log messages with a contextual logger

Modern embedded systems are often having connectivity and evaluate cloud services to collect various
data, e.g. statistics, telemetry, logging, etc. in one common place. But in such setup it becomes
difficult to differentiate logging data from one system with another especially when the number of
instances is growing. One of the solutions is to enhance each log message with the instance's unique
identifier and this is where contextual logging becomes handy. Apex.Grace contextual logger
provides a possibility to define a list of context fields on a per-logger instance base. Then on
each log statement context will be automatically evaluated and added to a log message. It is also
useful in a local environment when e.g. the same process is instantiated several times and each
instance can have its own logging attributes. Or during analysis when important values are put into
logging context that provides better visibility of the overall system state.

Apex.Grace log messages with context are created through an [](apex::logging::ContextualLogger)
object, which is behaving similarly to the [](apex::logging::Logger).

[](apex::logging::Logger) and [](apex::logging::ContextualLogger) can be interchanged without any
adaptations except the contextual logging API provided by the [](apex::logging::ContextualLogger).

{{ code_snippet("grace/monitoring/logging/test/test_node_contextual_logging.cpp",
   {"tag": "//! [Private members]", "skip_prefix": "//!"},
   "cpp") }}

{{ code_snippet("grace/monitoring/logging/test/test_node_contextual_logging.cpp",
   {"tag": "//! [logger_name]", "skip_prefix": "//!"},
   "cpp") }}

1. Add a logger called `MyContextualLoggerName` as a member variable of an Apex.Grace node

Add context to the logger. Specified context fields will be evaluated and added to the each log
message:

{{ code_snippet("grace/monitoring/logging/test/test_node_contextual_logging.cpp",
   {"tag": "//! [add_logging_context]", "skip_prefix": "//!"},
   "cpp") }}

1. Use the `create_ctx_data` helper method to instantiate context data `union` type with the
   deducted discriminator from the input argument. `create_ctx_data` has overloads for each declared
   `union` member type. Alternatively, the generated `apex_msgs::ContextData::create` function can
   be used to specify the discriminator explicitly
2. Add the context field as a lambda function that provides a static id
3. Add the context field as a pointer to the function that generates a new random id for each log
   message

Use logging macros as usual to log a message with context:

{{ code_snippet("grace/monitoring/logging/test/test_node_contextual_logging.cpp",
   {"tag": "//! [log_with_macros]", "skip_prefix": "//!"},
   "cpp") }}

1. Log a message with INFO level. Implicitly, at this point, the logging context will be evaluated
   and inserted into the log message

To override the configured context for individual logging statements, use the
[](apex::logging::ContextualLogger::log_message_with_context) method directly:

{{ code_snippet("grace/monitoring/logging/test/test_node_contextual_logging.cpp",
   {"tag": "//! [use_log_message_directly]", "skip_prefix": "//!"},
   "cpp") }}

1. Declare local logging context that can be used with individual logging statements
2. Invoke direct API to use local logging context for individual logging statements instead of what
   is defined in the logger

Logging context fields can also be removed at runtime:

{{ code_snippet("grace/monitoring/logging/test/test_node_contextual_logging.cpp",
   {"tag": "//! [remove_logging_context]", "skip_prefix": "//!"},
   "cpp") }}

1. Specify context field label to remove a field
2. Clear all added context fields

### Log messages with the root logger

Alternatively, [`apex::logging::<LoggerType>::get_root_logger`](apex::logging::Logger::get_root_logger)
can be used to log messages without creating a logger instance. Though convenient,
messages logged by the `root_logger` across the applications share the
application level context, and offer less distinguishing information compared to
using separate loggers for separate entities. The logging macros utilizing the
root logger make logging a one-liner and context-free task, making it ideal for
rapid debugging on non-node entities.

{{  code_snippet("grace/monitoring/logging/test/test_node_logging.cpp",
    {"tag": "//! [Root logger]", "skip_prefix": "//!"},
    "cpp") }}

!!! note
    The `root_logger` is a regular ROS node and is visible with ROS tools such as `ros2 node list`.
    Other ROS 2 functionalities such as
    [node name remapping](https://docs.ros.org/en/dashing/Tutorials/Node-arguments.html#name-remapping)
    can also affect the `root_logger_node`.

Before using the default root logger, the apex context must be initialized by
calling [](apex::pre_init). This will initialize the root logger, otherwise the
logging call will fail. The root logger is also destroyed when the context is shutdown.
The logging macros automatically handle this situation, but if the root logger is used directly,
the application code must handle this case.

## Visualize the log messages

Use one of the following methods for visualizing the log messages published by
the nodes.

### Apex.Grace console

**Apex.Grace console** is an [RQt](https://docs.ros.org/en/rolling/Concepts/Intermediate/About-RQt.html)
plugin that visualizes Apex.Grace log messages in a graphical interface.

Start **Apex.Grace console** as a standalone GUI:

```shell ade
source /opt/ros_visualization/setup.bash
ros2 run apex_rqt_console apex_rqt_console
```

Or start RQt and add **Apex.Grace console** from the *Plugins* menu:

```shell ade
source /opt/ros_visualization/setup.bash
rqt
```

!!! note
    Currently Apex.Grace console supports only the `apex_msgs::msg::LogMessage` log message type.

### log_writer

The `log_writer` writes the messages to a file or in a terminal.

More information can be found in the [`log_writer` design](log-writer-design.md) article.

!!! note
    Currently `log_writer` supports only the `apex_msgs::msg::LogMessage` log message type.

### Terminal output

The log messages will also be written in the same terminal in which the program
was started. This feature is controlled by the following combination of parameters:

<!-- markdownlint-disable MD046 -->
<!-- markdownlint-disable MD033 -->
| Compile-time option <br/> [`APEX_PRINT_LOGS_TO_TERMINAL`](#compile-time-options) | Setting <br/> [`logging/print_logs_to_terminal`](#configure-the-logging-behavior) | Behavior                                                                       |
|----------------------------------------------------------------------------------|-----------------------------------------------------------------------------------|--------------------------------------------------------------------------------|
| `ON`                                                                             | `true`                                                                            | Logs are printed on `stderr`                                                   |
| `ON`                                                                             | `false`                                                                           | Logs are not printed on `stderr`                                               |
| `OFF`                                                                            | -                                                                                 | This feature is disabled at compilation time - logs are not printed on `stderr` |
<!-- markdownlint-enable MD046 -->
<!-- markdownlint-enable MD033 -->

!!! tip "Hint"
    The messages can be filtered by piping them with the `cut -d] -f4-` command.

With Bazel, this is built-in using the log-level flags mentioned below:

```shell dollar title="Turn off logging from Apex.OS, wherever possible"
bazel build @apex//:log_level=OFF //...
```

## Configure the logging behavior

Settings can be used to tune the logging behavior at startup time. If a node has
a logger it will attempt to read these from the
[settings file](settings-design.md). Below are the available settings along with
their default values.

```yaml
logging: # (1)!
    level: TRACE                  # (2)!
    enabled: true                 # (3)!
    print_logs_to_terminal: true  # (4)!
```

1. The `logging` group of setting starts at the root of the settings file.
2. Logs from the level TRACE are logged
3. Logging is enabled in general
4. Logs are printed to the terminal - See the
   [Terminal Output](#terminal-output) section

### Compile-time options

The default log level can be changed at compile time while using Bazel:

```shell dollar
bazel build --@apex//:log_level=LOCAL_DEFAULT  //...  # (1)!
bazel build --@apex//:log_level=FATAL //...   # (2)!
bazel build --@apex//grace:log_level=INFO //...  # (3)!
bazle build --@apex//ida:log_level=FATAL --@apex//grace:log_level=INFO //...  # (4)!
```

1. This global Apex.OS log-level flag has a special value called `LOCAL_DEFAULT` that
   lets Apex.Ida and Apex.Grace set local log level defaults. If this flag `@apex//:log_level` is
   set to anything other than `LOCAL_DEFAULT`, it will override the Apex.Ida and Apex.Grace
   specific log level flags mentioned below
2. This sets the default log level for both Apex.Ida and Apex.Grace loggers to `FATAL`,
   overwriting any value set by the `@apex//grace:log_level` and `@apex//ida:log_level` flags
3. This sets the default log level just for Apex.Grace to `INFO`. The Apex.Ida log level will
   remain set to the default chosen by Apex.AI
4. This sets the default log level for Apex.Ida to `FATAL` and for Apex.Grace to `INFO`

These flags can be added to users `user.bazelrc` file configure the desired logging
behavior for individual users that will be applied to every `bazel` command:

```log title="user.bazelrc"
common --@apex//:log_level=LOCAL_DEFAULT
common --@apex//grace:log_level=INFO
common --@apex//ida:log_level=FATAL
```

This compile-time default can be overridden by the environment variables set at
runtime mentioned below.

!!! important
    If `--@apex//:log_level=OFF` is used, the logging mechanisms will be compiled out - meaning
    the environment variables will not work anymore.

The evaluation order is as follows for all other situations:

1. Compilation flag
1. Settings file
1. Runtime environment variables

The default log level for Apex.Grace and Apex.Ida is `ERROR`.

### Runtime options

In addition to the compile time defaults that can be set, after compilation it may
be helpful to configure the log level while debugging an application.

This can be achieved through environment variables:

```shell dollar
export APEX_GRACE_LOG_LEVEL=INFO
export APEX_IDA_LOG_LEVEL=ERROR
```

These override the compile-time defaults that may have been set - except if logging was compiled out
via the `--@apex///:log_level=OFF` flag.

## Design (for internal development)

The communication between the applications and the logger is delegated to the middleware layer by
utilizing the publishers and subscriptions from the ROS 2 APIs.

See [](apex::logging::Logger) and [](apex::logging::ContextualLogger) in the API doc for more
information.

### Future extensions / Unimplemented parts

- With the usage of `apex_msgs::msg::LogMessage` or `apex_msgs::msg::ContextLogMessage`, only a
  string message is allowed to be logged by the user. Logging various ROS 2 messages is planned
  in an upcoming extension.
