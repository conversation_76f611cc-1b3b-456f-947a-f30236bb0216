load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "event_registry_pkg",
    cc_libraries = [
        ":event_registry",
    ],
    description = "Package containing API for working with event registry",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/apexutils:apexutils_pkg",
        "@apex//common/configuration/settings:settings_pkg",
        "@apex//common/cpputils:cpputils_pkg",
        "@apex//grace/configuration/settings_extensions:settings_extensions_pkg",
        "@apex//grace/monitoring/event_registry_interfaces:event_registry_interfaces_pkg",
        "@apex//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "@apex//grace/ros/rcutils:rcutils_pkg",
        "@fmtlib//:fmtlib_pkg",
    ],
)

apex_cc_library(
    name = "event_registry",
    srcs = glob(
        ["src/**"],
        exclude = ["src/event_converter.cpp"],
    ),
    hdrs = glob(
        [
            "include/event_registry/**",
        ],
    ),
    applicable_licenses = ["//common/integrity:embedded_gold"],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/cpputils",
        "@apex//grace/monitoring/event_registry_interfaces",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "event_converter",
    srcs = [
        "src/event_converter.cpp",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/configuration/settings",
        "@apex//grace/configuration/settings_extensions",
        "@apex//grace/ros/rcutils",
        "@fmtlib",
    ],
)

apex_cc_test(
    name = "event_registry_tests",
    srcs = [
        "test/example_app1.hpp",
        "test/test.cpp",
        "test/test_local_event_registry.cpp",
        "test/test_remote_event_registry.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":event_registry",
        "@apex//common/apexutils",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "event_registry_converter_test",
    srcs = [
        "test/example_app1.hpp",
        "test/example_app2.hpp",
        "test/example_app3.hpp",
        "test/test.cpp",
        "test/test_converter.cpp",
    ],
    data = [
        "test/config/example_app1.yaml",
        "test/config/example_app2.yaml",
        "test/config/example_app3.yaml",
        "test/example_app1.hpp",
        "test/example_app2.hpp",
        "test/example_app3.hpp",
        ":event_converter",
    ],
    local_defines = [
        "EVENT_CONVERTER_EXE=\\\"$(rootpath :event_converter)\\\"",
        "INPUT_YAML_FILE1=\\\"$(rootpath test/config/example_app1.yaml)\\\"",
        "INPUT_YAML_FILE2=\\\"$(rootpath test/config/example_app2.yaml)\\\"",
        "INPUT_YAML_FILE3=\\\"$(rootpath test/config/example_app3.yaml)\\\"",
        "REFERENCE_HPP_FILE1=\\\"$(rootpath test/example_app1.hpp)\\\"",
        "REFERENCE_HPP_FILE2=\\\"$(rootpath test/example_app2.hpp)\\\"",
        "REFERENCE_HPP_FILE3=\\\"$(rootpath test/example_app3.hpp)\\\"",
    ],
    deps = [
        ":event_converter",
        ":event_registry",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "include/event_registry/events/global.hpp",
        "test/test_local_event_registry.cpp",
    ],
    visibility = ["//grace/monitoring/event_registry/design:__subpackages__"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
