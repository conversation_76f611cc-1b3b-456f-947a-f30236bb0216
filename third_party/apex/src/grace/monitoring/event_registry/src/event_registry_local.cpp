/// \copyright Copyright 2024 Apex.AI, Inc.
/// All rights reserved.

#include "event_registry/event_registry_local.hpp"

namespace apex::event
{
bool event_registry_base::is_app_registered(std::string_view app_id) const noexcept
{
  details::make_sure_inited(app_id);
  return m_events.find(app_id) != m_events.end();
}

const event_registry_base::app_descriptor & event_registry_base::get_app(
  std::string_view app_id) const
{
  details::make_sure_inited(app_id);
  const auto app_iter = m_events.find(app_id);
  if (app_iter == m_events.end()) {
    throw apex::runtime_error{apex::no_separator{},
                              "an unknown application id ",
                              app_descriptor::pretty_name(app_id).c_str()};
  }
  return app_iter->second;
}

bool event_registry_base::is_event_registered(std::string_view app_id,
                                              event_id_t event_id) const noexcept
{
  details::make_sure_inited(app_id);
  const auto app_iter = m_events.find(app_id);
  if (app_iter != m_events.end()) {
    const auto & event_ids = app_iter->second.event_ids;
    const auto code_iter = event_ids.find(event_id);
    return code_iter != event_ids.end();
  }
  return false;
}

std::string_view event_registry_base::get_app_name(std::string_view app_id) const
{
  return get_app(app_id).app_name;
}

std::string_view event_registry_base::get_event_name(std::string_view app_id,
                                                     event_id_t event_id) const
{
  const auto & app = get_app(app_id);
  const auto & event_ids = app.event_ids;
  const auto code_iter = event_ids.find(event_id);
  if (code_iter == event_ids.end()) {
    throw apex::runtime_error{apex::no_separator{},
                              "the event id '",
                              event_id,
                              "' is not registered with the application ",
                              app.pretty_name()};
  }

  return code_iter->second;
}

event_id_t event_registry_base::get_event_id(std::string_view app_id,
                                             std::string_view event_name) const
{
  details::make_sure_inited(event_name);

  const auto & app = get_app(app_id);
  const auto & event_names = app.event_names;
  const auto name_iter = event_names.find(event_name);
  if (name_iter == event_names.end()) {
    throw apex::runtime_error{apex::no_separator{},
                              "the event name '",
                              event_name.data(),
                              "' is not registered with the application ",
                              app.pretty_name()};
  }

  return name_iter->second;
}

bool event_registry_base::has_subset(const event_registry_base & other) const noexcept
{
  for (const auto & [app_id, desc] : other.m_events) {
    const auto iter = m_events.find(app_id);
    if ((iter == m_events.end()) || (desc != iter->second)) {
      return false;
    }
  }
  return true;
}

bool event_registry_base::is_app_instance_registered(std::string_view app_id,
                                                     instance_id_t instance_id) const noexcept
{
  if (app_id.empty() || instance_id == 0U) {
    return false;
  }
  const auto & iter = m_events.find(app_id);
  if (iter != m_events.end()) {
    return iter->second.instance_ids.find(instance_id) != iter->second.instance_ids.end();
  }
  return false;
}

instance_id_t event_registry_base::get_app_instance_id(std::string_view app_id,
                                                       std::string_view instance_name) const
{
  if (app_id.empty()) {
    throw apex::runtime_error{
      apex::no_separator{},
      "an application instance must be associated with a non-empty application id"};
  }
  if (instance_name.empty()) {
    throw apex::runtime_error{apex::no_separator{}, "the instance name cannot be empty"};
  }

  const auto & app = get_app(app_id);
  const auto & names = app.instance_names;
  if (const auto iter = names.find(instance_name); iter != names.end()) {
    return iter->second;
  } else {
    throw apex::runtime_error{apex::no_separator{},
                              "instance name ",
                              instance_name,
                              " is not associated with the application id ",
                              app.pretty_name()};
  }
}

std::string_view event_registry_base::get_app_instance_name(std::string_view app_id,
                                                            instance_id_t instance_id) const
{
  if (app_id.empty()) {
    throw apex::runtime_error{
      apex::no_separator{},
      "an application instance must be associated with a non-empty application id"};
  }
  const auto & app = get_app(app_id);
  const auto & ids = app.instance_ids;
  if (const auto iter = ids.find(instance_id); iter != ids.end()) {
    return iter->second;
  } else {
    throw apex::runtime_error{apex::no_separator{},
                              "instance id ",
                              instance_id,
                              " is not associated with the application id ",
                              app.pretty_name()};
  }
}

void event_registry::register_application(std::string_view app_id, std::string_view app_name)
{
  details::make_sure_inited(app_id);
  details::make_sure_inited(app_name);

  if (app_id.empty() && !app_name.empty()) {
    throw apex::runtime_error{apex::no_separator{},
                              "app name ",
                              app_descriptor::pretty_name(app_name).c_str(),
                              " cannot be set for the global registry section"};
  }

  if (app_id.size() > MaxAppIdSize) {
    throw apex::runtime_error{apex::no_separator{},
                              "app id ",
                              app_id.data(),
                              " is too long. Max allowed size is ",
                              MaxAppIdSize};
  }
  if (app_name.size() > MaxAppNameSize) {
    throw apex::runtime_error{apex::no_separator{},
                              "app name ",
                              app_descriptor::pretty_name(app_name).c_str(),
                              " is too long. Max allowed size is ",
                              MaxAppNameSize};
  }
  if (m_events.size() == MaxApps) {
    throw apex::runtime_error{"too many applications defined. Max allowed count is", MaxApps};
  }

  if (const auto iter = m_events.find(app_id); iter != m_events.end()) {
    if (iter->second.app_name != app_name) {
      /*
       AXIVION Next CodeLine FaultDetection-UncaughtExceptionOutsideMain:
       The registration functions can throw outside main() while processing global macros.
       This is intended behavior for dealing with illegal event declarations
       */
      throw apex::runtime_error{apex::no_separator{},
                                "conflicting application definition for id '",
                                app_id.data(),
                                "' for an application '",
                                app_name,
                                "'. Previously this id was registered to the application '",
                                iter->second.pretty_name(),
                                "'"};
    }
  } else {
    (void)m_events.emplace(std::move(app_id), app_descriptor{std::move(app_name)});
  }
}

void event_registry::register_event_description(std::string_view app_id,
                                                std::string_view event_name,
                                                event_id_t event_id)
{
  details::make_sure_inited(app_id);
  if (event_name.empty()) {
    /*
     AXIVION Next CodeLine FaultDetection-UncaughtExceptionOutsideMain:
     The registration functions can throw outside main() while processing global macros.
     This is intended behavior for dealing with illegal event declarations
     */
    throw apex::runtime_error{apex::no_separator{},
                              "event name cannot be empty for the application ",
                              app_descriptor::pretty_name(app_id).c_str(),
                              " and event id ",
                              event_id};
  }

  if (event_name.size() > MaxEventNameSize) {
    /*
     AXIVION Next CodeLine FaultDetection-UncaughtExceptionOutsideMain:
     The registration functions can throw outside main() while processing global macros.
     This is intended behavior for dealing with illegal event declarations
     */
    throw apex::runtime_error{apex::no_separator{},
                              "event name '",
                              event_name.data(),
                              "' is too long. Max allowed size is ",
                              MaxEventNameSize};
  }

  if (app_id.empty() && (event_id >= UserEventStart)) {
    throw apex::runtime_error{apex::no_separator{},
                              "event '",
                              event_name.data(),
                              "' has an id of an application event"
                              " but it has attempted to be registered as a global"
                              " for the application ",
                              app_descriptor::pretty_name(app_id).c_str()};
  }

  if (event_id == 0U) {
    throw apex::runtime_error{apex::no_separator{},
                              "event id cannot be 0 for the event name '",
                              event_name.data(),
                              "' of the application ",
                              app_descriptor::pretty_name(app_id).c_str()};
  }

  auto app_iter = m_events.find(app_id);
  if (app_iter == m_events.end()) {
    if (app_id.empty()) {
      app_iter = m_events.emplace(std::move(app_id), app_descriptor{}).first;
    } else {
      /*
       AXIVION Next CodeLine FaultDetection-UncaughtExceptionOutsideMain:
       The registration functions can throw outside main() while processing global macros.
       This is intended behavior for dealing with illegal event declarations
       */
      throw apex::runtime_error{apex::no_separator{},
                                "trying to register an event id '",
                                event_id,
                                "' for an unknown application with id ",
                                app_descriptor::pretty_name(app_id).c_str()};
    }
  }

  if (app_iter->second.event_ids.size() == MaxEventsPerApp) {
    /*
     AXIVION Next CodeLine FaultDetection-UncaughtExceptionOutsideMain:
     The registration functions can throw outside main() while processing global macros.
     This is intended behavior for dealing with illegal event declarations
     */
    throw apex::runtime_error{apex::no_separator{},
                              "too many events per application ",
                              app_iter->second.pretty_name(),
                              " defined. Max allowed count is ",
                              MaxEventsPerApp};
  }

  auto & event_names = app_iter->second.event_names;
  auto & event_ids = app_iter->second.event_ids;

  auto last_inserted = event_names.end();
  if (const auto name_iter = event_names.find(event_name); name_iter != event_names.end()) {
    if (name_iter->second != event_id) {
      /*
       AXIVION Next CodeLine FaultDetection-UncaughtExceptionOutsideMain:
       The registration functions can throw outside main() while processing global macros.
       This is intended behavior for dealing with illegal event declarations
       */
      throw apex::runtime_error{apex::no_separator{},
                                "conflicting event definition '",
                                event_name,
                                "' = ",
                                event_id,
                                " for the application '",
                                app_iter->second.pretty_name(),
                                "'. Previously this event was registered with id ",
                                name_iter->second};
    }
  } else {
    last_inserted = event_names.emplace(event_name, event_id).first;
  }

  if (const auto id_iter = event_ids.find(event_id); id_iter != event_ids.end()) {
    if (id_iter->second != event_name) {
      if (last_inserted != event_names.end()) {
        (void)event_names.erase(last_inserted);
      }
      /*
       AXIVION Next CodeLine FaultDetection-UncaughtExceptionOutsideMain:
       The registration functions can throw outside main() while processing global macros.
       This is intended behavior for dealing with illegal event declarations
       */
      throw apex::runtime_error{apex::no_separator{},
                                "conflicting event definition '",
                                event_name,
                                "' = ",
                                event_id,
                                " for the application '",
                                app_iter->second.pretty_name(),
                                "'. Previously this id was registered with the name '",
                                id_iter->second,
                                "'"};
    }
  } else {
    (void)event_ids.emplace(event_id, event_name);
  }
  assert(event_names.size() == event_ids.size());
}

void event_registry::register_application_instance(std::string_view app_id,
                                                   std::string_view instance_name,
                                                   instance_id_t instance_id)
{
  if (app_id.empty()) {
    throw apex::runtime_error{apex::no_separator{},
                              "an application instance must be associated with"
                              " a non-empty application id"};
  }
  if (instance_name.empty()) {
    throw apex::runtime_error{apex::no_separator{}, "the instance name cannot be empty"};
  }
  if (instance_name.size() > MaxInstanceNameSize) {
    throw apex::runtime_error{apex::no_separator{},
                              "instance name '",
                              instance_name.data(),
                              "' is too long. Max allowed size is ",
                              MaxInstanceNameSize};
  }
  if (instance_id == 0U) {
    throw apex::runtime_error{apex::no_separator{},
                              "instance id cannot be 0 for the instance name '",
                              instance_name.data(),
                              "' of the application ",
                              app_descriptor::pretty_name(app_id).c_str()};
  }

  auto app_iter = m_events.find(app_id);
  if (app_iter == m_events.end()) {
    throw apex::runtime_error{apex::no_separator{},
                              "an unknown application id ",
                              app_descriptor::pretty_name(app_id).c_str(),
                              " for instance definition '",
                              instance_name,
                              "' = ",
                              instance_id};
  }

  if (app_iter->second.instance_names.size() == MaxInstancesPerApp) {
    throw apex::runtime_error{apex::no_separator{},
                              "too many instances per application ",
                              app_iter->second.pretty_name(),
                              " defined. Max allowed count is ",
                              MaxInstancesPerApp};
  }

  auto & instance_names = app_iter->second.instance_names;
  auto & instance_ids = app_iter->second.instance_ids;
  auto last_inserted = instance_names.end();

  if (const auto instance_iter = instance_names.find(instance_name);
      instance_iter != instance_names.end()) {
    if (instance_iter->second != instance_id) {
      /*
       AXIVION Next CodeLine FaultDetection-UncaughtExceptionOutsideMain:
       The registration functions can throw outside main() while processing global macros.
       This is intended behavior for dealing with illegal event declarations
       */
      throw apex::runtime_error{apex::no_separator{},
                                "conflicting instance definition '",
                                instance_name.data(),
                                "', index: ",
                                instance_id,
                                " for the application '",
                                app_iter->second.pretty_name(),
                                "'. Previously this instance was registered with the id '",
                                instance_iter->second,
                                "'"};
    }
  } else {
    last_inserted = instance_names.emplace(instance_name, instance_id).first;
  }

  if (const auto id_iter = instance_ids.find(instance_id); id_iter != instance_ids.end()) {
    if (id_iter->second != instance_name) {
      if (last_inserted != instance_names.end()) {
        (void)instance_names.erase(last_inserted);
      }
      /*
       AXIVION Next CodeLine FaultDetection-UncaughtExceptionOutsideMain:
       The registration functions can throw outside main() while processing global macros.
       This is intended behavior for dealing with illegal event declarations
       */
      throw apex::runtime_error{apex::no_separator{},
                                "conflicting instance definition '",
                                instance_name.data(),
                                "' = ",
                                instance_id,
                                " for the application '",
                                app_iter->second.pretty_name(),
                                "'. Previously this id was registered with the instance name '",
                                id_iter->second,
                                "'"};
    }
  } else {
    (void)instance_ids.emplace(instance_id, instance_name);
  }
  assert(instance_names.size() == instance_ids.size());
}

void event_registry::copy_events(std::string_view from_app_id, std::string_view to_app_id)
{
  details::make_sure_inited(from_app_id);
  details::make_sure_inited(to_app_id);
  const auto iter_from = m_events.find(from_app_id);
  const auto iter_to = m_events.find(to_app_id);
  if (iter_from != m_events.end() && iter_to != m_events.end()) {
    for (const auto & [event_name, event_id] : iter_from->second.event_names) {
      register_event_description(to_app_id, event_name, event_id);
    }
  }
}
}  // namespace apex::event
