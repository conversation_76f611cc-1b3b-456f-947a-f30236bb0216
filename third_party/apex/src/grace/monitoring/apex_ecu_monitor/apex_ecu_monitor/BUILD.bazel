load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")

cc_library(
    name = "cert_settings",
    srcs = ["param/apex_ecu_monitor_settings.cpp"],
    hdrs = ["param/apex_ecu_monitor_settings.hpp"],
    visibility = [":__subpackages__"],
    deps = ["//common/configuration/settings"],
)

filegroup(
    name = "param_data",
    srcs = glob(["param/**/*.yaml"]),
    visibility = [":__subpackages__"],
)

# TODO(#18266) Add determinism_check support what's the actual condition?

cc_library(
    name = "apex_ecu_monitor_lib",
    srcs = glob(
        [
            "src/**",
        ],
        exclude = [
            "src/ecu_data_analysis_node_main.cpp",
            "src/ecu_data_publisher_node_main.cpp",
        ],
    ),
    hdrs = glob([
        "include/**",
    ]),
    strip_include_prefix = "include",
    visibility = [":__subpackages__"],
    deps = [
        "//common/configuration/settings",
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/diagnostic_msgs",
        "//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor_msgs",
        "//grace/utils/apexcpp",
    ] + select({
        "//common/asil:d": [":cert_settings"],
        "//conditions:default": [],
    }),
)

cc_binary(
    name = "apex_ecu_data_publisher_exe",
    srcs = [
        "src/ecu_data_publisher_node_main.cpp",
    ],
    applicable_licenses = ["//common/integrity:embedded_gold"],
    visibility = ["//visibility:public"],
    deps = [
        ":apex_ecu_monitor_lib",
        "@coverage_tool//:coverage_io_lib",
    ],
)

cc_binary(
    name = "apex_ecu_data_analysis_exe",
    srcs = [
        "src/ecu_data_analysis_node_main.cpp",
    ],
    applicable_licenses = ["//common/integrity:embedded_gold"],
    visibility = ["//visibility:public"],
    deps = [
        ":apex_ecu_monitor_lib",
        "@coverage_tool//:coverage_io_lib",
    ],
)

ament_pkg_resources(
    name = "apex_ecu_monitor_resources",
    package = "apex_ecu_monitor",
    resources = {
        ":apex_ecu_data_publisher_exe": "executable",
        ":apex_ecu_data_analysis_exe": "executable",
        ":param_data": "share",
    },
    visibility = ["//visibility:public"],
)

filegroup(
    name = "apex_ecu_monitor_launch_file",
    srcs = select({
        "@platforms//cpu:x86_64": ["launch/apex_ecu_monitor.launch.yaml"],
        "@platforms//cpu:aarch64": ["launch/apex_ecu_monitor_aarch64.launch.yaml"],
    }),
)

process_manager(
    name = "apex_ecu_monitor_launch",
    data = [":apex_ecu_monitor_resources"],
    launch_file = ":apex_ecu_monitor_launch_file",
    visibility = ["//visibility:public"],
)

ros_pkg(
    name = "apex_ecu_monitor",
    description = "Apex ECU monitor to monitor ECU level information",
    lib_executables = [
        ":apex_ecu_data_publisher_exe",
        ":apex_ecu_data_analysis_exe",
    ],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Sumanth Nirmal",
    pkg_name = "apex_ecu_monitor",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

filegroup(
    name = "doc_files",
    srcs = [
        "package.xml",
        "param/apex_ecu_monitor_default_settings.yaml",
        "param/apex_ecu_monitor_default_settings_v3h_qnx.yaml",
    ],
    visibility = [
        "//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor/doc:__subpackages__",
        "//tools/launch/launch_testing/doc:__pkg__",
    ],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
