// Copyright 2017-2018 Apex.AI, Inc.
// All rights reserved.

#include <apex_init/apex_init.hpp>
#include <cpputils/common_exceptions.hpp>
#include <executor2/executor_factory.hpp>
#include <executor2/executor_runner.hpp>

#include <thread>
#include <utility>

#if defined(APEX_CERT) && !defined(QNX700)
// apex_malloc is only used with Apex.OS Cert,
// but is not currently supported on QNX 
    #include <apex_malloc/apex_malloc.hpp>
    #define USE_APEX_MALLOC
  #include "apex_ecu_monitor_settings.hpp"
#endif

#ifndef APEX_CERT
  #include <interrupt/interrupt_handler.hpp>
#endif  // #ifndef APEX_CERT

#include "apex_ecu_monitor/data_analysis_node.hpp"
#include "apex_ecu_monitor/load_settings.hpp"

/// this file is simply a main file to create a ros1 style standalone node
int32_t main(const int32_t argc, char ** const argv)
{
  int32_t ret = 0;
  // initialise apex
  try {
    if (apex::pre_init(argc, argv, false) != APEX_RET_OK) {
      throw apex::runtime_error("ecu_data_analysis_node_main: Can't pre-init Apex.OS");
    }
#ifndef APEX_CERT
    const apex::interrupt_handler::installer interrupt_handler_installer{};
#endif

#ifdef APEX_CERT
    const auto config_root = apex::ecu_monitor::load_settings();
#else
    const auto config_root = apex::ecu_monitor::load_settings(argc, argv);
#endif

    auto analysis_node =
      apex::ecu_monitor::data_analysis_node::create_node("ecu_data_analysis_node", "", config_root);

    const auto exec = apex::executor::executor_factory::create();
    analysis_node->wait_for_matched();
    (void)exec->add(analysis_node);
    const apex::executor::executor_runner runner{apex::executor::executor_runner::deferred, *exec};

    if (apex::post_init() != APEX_RET_OK) {
      throw apex::runtime_error("Can't post-init Apex.OS");
    }
#ifdef USE_APEX_MALLOC
    if (!apex_malloc::install_hooks()) {
      throw apex::runtime_error("Can't install malloc() hooks");
    }
#endif  // USE_APEX_MALLOC
    // start publishing the analysed ecu info
    runner.issue();

#ifdef APEX_CERT
    while (rclcpp::ok()) {
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
#else
    apex::interrupt_handler::wait();
#endif
#ifdef USE_APEX_MALLOC
    (void)apex_malloc::remove_hooks();
#endif  // USE_APEX_MALLOC
    runner.stop();
  } catch (const std::exception & err) {
#ifdef USE_APEX_MALLOC
    (void)apex_malloc::remove_hooks();
#endif  // USE_APEX_MALLOC
    std::cerr << err.what();
    ret = __LINE__;
  } catch (...) {
#ifdef USE_APEX_MALLOC
    (void)apex_malloc::remove_hooks();
#endif  // USE_APEX_MALLOC
    std::cerr << "Unknown error encountered, exiting...";
    ret = __LINE__;
  }
  return ret;
}
