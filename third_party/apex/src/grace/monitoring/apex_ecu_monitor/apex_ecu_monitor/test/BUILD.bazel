load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test_in_install_space")

filegroup(
    name = "test_apex_ecu_monitor_srcs_with_req_ids",
    srcs = ["test_ecu_data_acquisition.cpp"],
    visibility = ["//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor/doc/internal:__subpackages__"],
)

filegroup(
    name = "doc_files",
    srcs = ["ecu_data_publisher.test.py"],
    visibility = ["//tools/launch/launch_testing/doc:__pkg__"],
)

apex_cc_test(
    name = "test_apex_ecu_monitor",
    srcs = [
        "test_compute_load.cpp",
        "test_create_files.hpp",
        "test_ecu_data_analysis.cpp",
        "test_ecu_data_analysis_config.cpp",
        "test_ecu_data_analysis_node_main.cpp",
        "test_ecu_data_collector.cpp",
        "test_ecu_publisher_node_main.cpp",
        "test_load_settings.cpp",
        "test_publish_analysis_node.cpp",
        "test_utility_functions.cpp",
        "test_utility_functions.hpp",
        ":test_apex_ecu_monitor_srcs_with_req_ids",
    ] + select({
        "//common/asil:d": ["//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor:cert_settings"],
        "//conditions:default": [],
    }),
    data = ["//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor:param_data"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    local_defines = [
        "CMAKE_SOURCE_DIR=\\\"./grace/monitoring/apex_ecu_monitor/apex_ecu_monitor\\\"",
    ],
    tags = [
        "constrained_test",
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        "//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor:apex_ecu_monitor_lib",
        "//tools/testing/apex_test_tools",
        "@googletest//:gtest_main",
    ],
)

# This test redefines the sysconf function, so it must be a separate target,
# to prevent it from polluting the other tests
apex_cc_test(
    name = "test_publisher_gmock_1",
    srcs = [
        #"src/publisher_node/ecu_data_publisher_node.cpp",
        #"src/publisher_node/ecu_data_publisher_node_factory.cpp",
        "test_create_files.hpp",
        "test_ecu_collector_gmock.cpp",
        "test_utility_functions.cpp",
        "test_utility_functions.hpp",
    ] + select({
        "//common/asil:d": ["//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor:cert_settings"],
        "//conditions:default": [],
    }),
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor:apex_ecu_monitor_lib",
        "@googletest//:gtest_main",
    ],
)

# This utility is used by the python tests below
cc_binary(
    name = "create_files_exe",
    srcs = [
        "test_create_files.cpp",
        "test_create_files.hpp",
    ],
    deps = [
        "//grace/execution/apex_init",
        "//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor_msgs",
        "//grace/monitoring/logging",
        "//grace/ros/rcutils",
    ],
)

launch_test_in_install_space(
    name = "test_integration_ecu_data_publisher",
    install_space = {
        "ros_pkgs": [
            ":apex_ecu_monitor_test",
            "@apex//grace/tools/launch_ros/launch_ros:launch_ros_pkg",
            "@apex//tools/launch/launch:launch_pkg",
            "//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor_msgs:apex_ecu_monitor_msgs_pkg",
        ],
    },
    launch_test_file = "ecu_data_publisher.test.py",
    tags = ["constrained_test"],
    deps = [
        ":apex_ecu_monitor_msgs_py",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//tools/apex_pytest_utils",
        "@apex//tools/launch/launch",
    ],
)

py_msgs_library(
    name = "apex_ecu_monitor_msgs_py",
    msgs = "//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor_msgs:apex_ecu_monitor_msgs",
)

py_msgs_library(
    name = "diagnostic_msgs_py",
    msgs = "//grace/interfaces/diagnostic_msgs:diagnostic_msgs",
)

launch_test_in_install_space(
    name = "test_integration_ecu_data_analysis",
    install_space = {
        "ros_pkgs": [
            ":apex_ecu_monitor_test",
            "@apex//grace/tools/launch_ros/launch_ros:launch_ros_pkg",
            "@apex//tools/launch/launch:launch_pkg",
            "//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor_msgs:apex_ecu_monitor_msgs_pkg",
            "//grace/interfaces/diagnostic_msgs:diagnostic_msgs_pkg",
            "//grace/tools/launch_ros/launch_testing_ros:launch_testing_ros_pkg",
        ],
    },
    launch_test_file = "ecu_data_analysis.test.py",
    tags = ["constrained_test"],
    deps = [
        ":apex_ecu_monitor_msgs_py",
        ":diagnostic_msgs_py",
        "//grace/tools/launch_ros/launch_testing_ros",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//tools/apex_pytest_utils",
        "@apex//tools/launch/launch",
    ],
)

ros_pkg(
    name = "apex_ecu_monitor_test",
    description = "Apex ECU monitor to monitor ECU level information",
    lib_executables = [
        ":create_files_exe",
        "//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor:apex_ecu_data_publisher_exe",
        "//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor:apex_ecu_data_analysis_exe",
    ],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Sumanth Nirmal",
    pkg_name = "apex_ecu_monitor",
    version = "0.0.1",
    visibility = ["//visibility:public"],
)

# TODO(carlos) Migrate test to os_verify_docs once that's bazelized (&356, #17999, #18249, #21244)
#launch_test_in_install_space(
#    name = "test_integration_ecu_monitor_doctest",
#    launch_test_file = "ecu_monitor.doctest.py",
#    install_space = {
#       "ros_pkgs": [
#            ":apex_ecu_monitor",
#            "@apex//grace/tools/launch_ros/launch_ros:launch_ros_pkg",
#            "@apex//tools/launch/launch:launch_pkg",
#            "//grace/monitoring/apex_ecu_monitor/apex_ecu_monitor_msgs:apex_ecu_monitor_msgs_pkg",
#        ],
#    },
#    deps = [
#        ":apex_ecu_monitor_msgs_py",
#        "@apex//grace/tools/launch_ros/launch_ros",
#        "@apex//tools/launch/launch",
#    ],
#)
