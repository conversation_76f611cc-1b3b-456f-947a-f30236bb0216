# Copyright 2020-2023 Apex.AI, Inc.
# All rights reserved.

import os
import subprocess
import tempfile
import threading
import unittest

import ament_index_python
import launch
import launch_ros.actions
import launch_testing
import rclpy
from rclpy.node import Node

import apex_doctest
from apex_pytest_utils import write_tmp_config_file

from apex_ecu_monitor_msgs.msg import EcuInfo
from diagnostic_msgs.msg import DiagnosticArray


def create_fake_data():
    fake_proc_directory = tempfile.mkdtemp(prefix="ecu_monitor_test")

    create_file_path = os.path.join(
        ament_index_python.get_package_prefix('apex_ecu_monitor'),
        'lib',
        'apex_ecu_monitor',
        'create_files_exe'
    )

    subprocess.run([create_file_path, '--root_path', fake_proc_directory])

    return os.path.join(fake_proc_directory, 'test_integration')


@apex_doctest.doc_check_launch_description(
    'apex-ecu-monitor-design.md',
    'apex_ecu_monitor',
)
def generate_test_description():

    fake_data_path = create_fake_data()

    default_cfg_file_path = os.path.join(
        ament_index_python.get_package_share_directory("apex_ecu_monitor"),
        "param",
        "apex_ecu_monitor_default_settings.yaml"
    )

    # Figure out what disks are installed and save it for the test config
    proc = subprocess.run(['lsblk', '-dn', '-I8,259', '--output', 'NAME'], stdout=subprocess.PIPE)
    disks = proc.stdout.decode().split()
    print(f"Detected disks\n:{disks}")
    test_device_name = disks[0]

    override_cfg_path = write_tmp_config_file(
        default_cfg_file_path,
        {
            "ecu_data_publisher_node": {
                "cpu_info": {"sys_temp_path": os.path.join(fake_data_path, "sys_temp")},
                "network_info": {
                    "path": os.path.join(fake_data_path, "net_info"),
                    "iface_name": "test_iface"
                },
                "disk_io_info": {"device_name": test_device_name}
            }
        },
        prefix="ecu_monitor_cfg_"
    )
    print(override_cfg_path)

    default_launch_file_path = os.path.join(
        ament_index_python.get_package_share_directory("apex_ecu_monitor"),
        "launch",
        "apex_ecu_monitor.launch.yaml"
    )

    override_launch_file_path = write_tmp_config_file(
        default_launch_file_path, prefix="apex_ecu_monitor.launch_"
    )

    search_and_replace = {
        "$(find-pkg-share apex_ecu_monitor)/param/apex_ecu_monitor_default_settings.yaml":
            override_cfg_path
    }

    with open(override_launch_file_path, "rt+") as launch_file:
        launch_file_data = launch_file.read()
        for k, v in search_and_replace.items():
            launch_file_data = launch_file_data.replace(k, v)
        launch_file.seek(0)
        launch_file.write(launch_file_data)
        launch_file.truncate()

    process_manager = launch_ros.actions.Node(
        package="process_manager",
        executable="process_manager",
        arguments=[
            "--apex-settings-file", override_launch_file_path
        ]
    )

    ld = launch.LaunchDescription([
        process_manager,
        launch_testing.actions.ReadyToTest()
    ])

    return ld


class EcuInfoReceiver(Node):
    def __init__(self):
        super().__init__("test_ecu_info_receiver")
        self.sub = self.create_subscription(EcuInfo, "raw_ecu_info", self.ecu_info_callback, 10)
        self.data_received = threading.Event()

    def ecu_info_callback(self, msg):
        self.data_received.set()


class DiagnosticsReceiver(Node):
    def __init__(self):
        super().__init__("test_diagnostics_receiver")
        self.sub = self.create_subscription(DiagnosticArray, "diagnostics",
                                            self.diagnostics_callback, 10)
        self.data_received = threading.Event()

    def diagnostics_callback(self, msg):
        self.data_received.set()


class EcuMonitorTests(unittest.TestCase):
    rclpy.init(args=None)

    def test_ecu_info_data_received(self, proc_info):
        test_node = EcuInfoReceiver()
        rclpy.spin_once(test_node, timeout_sec=30)
        self.assertTrue(test_node.data_received.is_set(),
                        "No data received on /raw_ecu_info topic")

    def test_diagnostics_data_received(self, proc_info):
        test_node = DiagnosticsReceiver()
        rclpy.spin_once(test_node, timeout_sec=30)
        self.assertTrue(test_node.data_received.is_set(),
                        "No data received on /diagnostics topic")

    def test_topic_monitor_data_collected(self, proc_info):
        # Data may go through the nodes quicker than the topic monitor's
        # initial start up process for collection topic information. Therefore
        # this active test exists in order to make sure the topic monitor had
        # at least a single chance to collect topic information.
        # Ideally an active test like this one is automatically created by the
        # doctest infrastructure: Issue #8772
        self.proc_output.assertWaitFor(
            "Topic monitor data collected",
            timeout=10,
            stream='stdout'
        )


documentation_tests = apex_doctest.DocCheckTests(
    'internal/design/apex-ecu-monitor-design.md',
    'apex_ecu_monitor',
)
