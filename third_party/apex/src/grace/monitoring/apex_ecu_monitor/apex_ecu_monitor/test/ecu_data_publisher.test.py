# Copyright 2019 Apex.AI, Inc.
# All rights reserved.

import os
import unittest
import tempfile
import time
from subprocess import check_output

from apex_pytest_utils import APEX_SKIP_TEST_IF
from launch_testing import LaunchDescription
from launch.actions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from launch_testing.actions import ReadyToTest
from launch.event_handlers import OnProcessExit
from launch_ros.actions import Node
import rclpy
from rclpy.context import Context
from rclpy.qos import qos_unit_test_default
from rclpy.utilities import get_rmw_implementation_identifier
import rclpy.executors

from launch_testing import post_shutdown_test
from launch_testing.asserts import assertExitCodes

import apex_ecu_monitor_msgs.msg


def generate_test_description():
    # These tests rely on create_file_exe to generate a directory of files which will be read by
    # the apex_ecu_data_publisher_exe program and published to a ROS topic

    prefix = f"data_pub-{time.time()}"
    TEST_HOME = tempfile.mkdtemp(prefix=prefix)  # Run all the tests with a tmpdir home
    print(f"Temporary folder for data_pub: {TEST_HOME}")

    # ![Create the nodes]
    create_file = Node(
        package='apex_ecu_monitor',
        executable='create_files_exe',
        arguments=['--root_path', '{}'.format(TEST_HOME)],
    )

    node_under_test = Node(
        package='apex_ecu_monitor',
        executable='apex_ecu_data_publisher_exe',
        arguments=[
            '--apex-settings-file',
            '{}/test_integration/apex_ecu_monitor_settings_test.yaml'.format(TEST_HOME)
        ]
    )
    # ![Create the nodes]

    # ![LaunchDescription]
    return LaunchDescription([
        create_file,
        RegisterEventHandler(
            # Wait for create_file to exit, then launch the node under test and start the tests
            OnProcessExit(
                target_action=create_file,
                on_exit=[
                    node_under_test,  # Launch node under test
                    ReadyToTest()  # Signal to start tests
                ]
            )
        )
    ])
    # ![LaunchDescription]


# ![Class with subscription]
class TestRunningDataPublisher(unittest.TestCase):

    @classmethod
    def setUpClass(cls):
        cls.context = Context()
        rclpy.init(context=cls.context)
        cls.node = rclpy.create_node("test_node", context=cls.context)
        cls.msgs = []
        cls.sub = cls.node.create_subscription(
            msg_type=apex_ecu_monitor_msgs.msg.EcuInfo,
            topic="/raw_ecu_info_test",
            callback=cls._msg_received,
            qos_profile=qos_unit_test_default
        )

    @classmethod
    def tearDownClass(cls):
        cls.node.destroy_subscription(cls.sub)
        rclpy.shutdown(context=cls.context)

    @classmethod
    def _msg_received(self, msg):
        # Callback for ROS subscriber used in the test
        self.msgs.append(msg)

    def get_message(self):
        # Try up to 60sec to receive messages
        startlen = len(self.msgs)

        executor = rclpy.executors.SingleThreadedExecutor(context=self.context)
        executor.add_node(self.node)

        try:
            end_time = time.time() + 60.0
            while time.time() < end_time:
                executor.spin_once(timeout_sec=0.1)
                if startlen != len(self.msgs):
                    break

            self.assertNotEqual(startlen, len(self.msgs))
            return self.msgs[-1]
        finally:
            executor.remove_node(self.node)
            executor.shutdown()
# ![Class with subscription]

    def assertAlmostEqual(self, first, second, places=6, msg=None, delta=None):
        # Most of our tests want to compare floats to around 6 decimal places, whereas
        # the default assertAlmostEqual does 7.  Overload it and provide different default
        # places argument
        return super().assertAlmostEqual(first, second, places, msg, delta)

    # ![test_msg_rate]
    def test_msg_rate(self):
        # Receive messages for 5 seconds - make sure we don't get too many or too few
        RUNTIME = 5.0
        start_time = time.time()
        end_time = start_time + RUNTIME

        while time.time() < end_time:
            self.get_message()
        # From original apex_integration_test test_system_data_publisher_test.cpp:
        # // Relatively loose check: make sure you got at least something
        # bool ret = m_num_received_msgs > 0U;
        # ret = ret && (m_num_received_msgs > m_runtime);  // atleast some messages
        # ret = ret && (m_num_received_msgs < m_runtime * 15U);  // not too many messages
        # APEX_PRINT("received messages: ", m_num_received_msgs);

        self.assertGreater(len(self.msgs), 0)
        self.assertGreater(len(self.msgs), RUNTIME)  # At least 1 message per second
        self.assertLess(len(self.msgs), RUNTIME * 15)  # Fewer than 15 messages per second
    # ![test_msg_rate]

    # ![test_cpu_info]
    def test_cpu_info(self):
        # Test that the CPU info is correct (matches file generated by create_file_exe)
        msg = self.get_message()

        # From original apex_integration_test test_system_data_publisher_test.cpp:
        # // check cpu info
        # ret = ret && (m_msg.cpu_info.num_cpu_cores == 12);
        # ret = ret && (m_msg.cpu_info.cpu_temp == (55555) / 1000.0F);
        # int64_t uptime = 18675939LL * NSEC_IN_SEC;
        # ret = ret && (m_msg.cpu_info.cpu_uptime.duration == uptime);
        # int64_t total_time_nsec = (1677354382LL + 1369068LL + 533717726LL + 20301470335LL +
        #   4721157LL + 6LL + 29758114LL + 8LL) * (NSEC_IN_SEC / sysconf(_SC_CLK_TCK));
        # int64_t idle_time_nsec = (20301470335LL + 4721157LL) *
        #   (NSEC_IN_SEC / sysconf(_SC_CLK_TCK));
        # ret = ret && (m_msg.cpu_info.cpu_total_time.duration == total_time_nsec);
        # ret = ret && (m_msg.cpu_info.cpu_idle_time.duration == idle_time_nsec);
        # ret = ret && (m_msg.cpu_info.cpu_active_time.duration ==
        #   (total_time_nsec - idle_time_nsec));
        self.assertEqual(msg.cpu_info[0].num_cpu_cores, 12)
        self.assertAlmostEqual(msg.cpu_info[0].cpu_temp, 55555 / 1000.0)

        NSEC_IN_SEC = 1000000000
        self.assertEqual(msg.cpu_info[0].cpu_uptime.duration, 18675939 * NSEC_IN_SEC)

        total_time_nsec = (
            (
                1677354382 +
                1369068 +
                533717726 +
                20301470335 +
                4721157 +
                6 +
                29758114 +
                8
            ) *
            (
                NSEC_IN_SEC /
                os.sysconf("SC_CLK_TCK")
            )
        )
        self.assertEqual(msg.cpu_info[0].cpu_total_time.duration, total_time_nsec)

        idle_time_nsec = (
            (
                20301470335 +
                4721157
            ) *
            (
                NSEC_IN_SEC /
                os.sysconf("SC_CLK_TCK")
            )
        )
        self.assertEqual(msg.cpu_info[0].cpu_idle_time.duration, idle_time_nsec)
        self.assertEqual(msg.cpu_info[0].cpu_active_time.duration,
                         total_time_nsec - idle_time_nsec)
    # ![test_cpu_info]

    # ![test_memory_info]
    def test_memory_info(self):
        # From original apex_integration_test test_system_data_publisher_test.cpp:
        # // check memory info
        # ret = ret && (m_msg.memory_info.total_ram_mem == 65843940);
        # ret = ret && (m_msg.memory_info.free_ram_mem == 50690624);
        # ret = ret && (m_msg.memory_info.total_swap_mem == 66982908);
        # ret = ret && (m_msg.memory_info.free_swap_mem == 65828604);
        msg = self.get_message()

        self.assertEqual(msg.memory_info[0].total_ram_mem, 65843940)
        self.assertEqual(msg.memory_info[0].free_ram_mem, 50690624)
        self.assertEqual(msg.memory_info[0].total_swap_mem, 66982908)
        self.assertEqual(msg.memory_info[0].free_swap_mem, 65828604)
    # ![test_memory_info]

    # ![test_disk_info]
    def test_disk_info(self):
        # // check disk info
        # ret = ret && (m_msg.disk_io_info.device_name == "sdb");
        # ret = ret && (m_msg.disk_io_info.time_read.duration ==
        #   std::chrono::nanoseconds(std::chrono::milliseconds(104948)).count());
        # ret = ret && (m_msg.disk_io_info.time_write.duration ==
        #   std::chrono::nanoseconds(std::chrono::milliseconds(4161924)).count());
        # ret = ret && (m_msg.disk_io_info.total_io_time.duration ==
        #   std::chrono::nanoseconds(std::chrono::milliseconds(854580)).count());
        # ret = ret && (m_msg.disk_io_info.io_currently_active == 3);
        msg = self.get_message()

        self.assertEqual(msg.disk_io_info[0].device_name, "sdb")
        self.assertEqual(msg.disk_io_info[0].time_read.duration, 104948 * 1000000)
        self.assertEqual(msg.disk_io_info[0].time_write.duration, 4161924 * 1000000)
        self.assertEqual(msg.disk_io_info[0].total_io_time.duration, 854580 * 1000000)
        self.assertEqual(msg.disk_io_info[0].io_currently_active, 3)
    # ![test_disk_info]

    # ![test_network_info]
    def test_network_info(self):
        # // check network info
        # ret = ret && (m_msg.network_info.device_name == "test_iface");
        # ret = ret && (m_msg.network_info.rx_bytes == 94447231);
        # ret = ret && (m_msg.network_info.rx_dropped == 91);
        # ret = ret && (m_msg.network_info.rx_errors == 0);
        # ret = ret && (m_msg.network_info.rx_packets == 1113345);
        # ret = ret && (m_msg.network_info.tx_bytes == 94456297);
        # ret = ret && (m_msg.network_info.tx_dropped == 0);
        # ret = ret && (m_msg.network_info.tx_errors == 12);
        # ret = ret && (m_msg.network_info.tx_packets == 1113419);
        msg = self.get_message()

        self.assertEqual(msg.network_info[0].device_name, "test_iface")
        self.assertEqual(msg.network_info[0].rx_bytes, 94447231)
        self.assertEqual(msg.network_info[0].rx_dropped, 91)
        self.assertEqual(msg.network_info[0].rx_errors, 0)
        self.assertEqual(msg.network_info[0].rx_packets, 1113345)
        self.assertEqual(msg.network_info[0].tx_bytes, 94456297)
        self.assertEqual(msg.network_info[0].tx_dropped, 41)
        self.assertEqual(msg.network_info[0].tx_errors, 12)
        self.assertEqual(msg.network_info[0].tx_packets, 1113419)
    # ![test_network_info]

    # ![test_process_info]
    def test_process_info(self):
        # This tests from the actual proc files
        msg = self.get_message()

        self.assertEqual(msg.process_info[0].process_name, 'apex_ecu_data_publisher_exe')
        pid = map(int, check_output(['pidof', msg.process_info[0].process_name]).decode(
            'utf-8').split())
        # There might be multiple process with same name, check if the required PID is in the list
        self.assertTrue((msg.process_info[0].pid in pid))
        self.assertGreaterEqual(msg.process_info[0].utime.duration, 0)
        self.assertGreaterEqual(msg.process_info[0].utime.duration, 0)
        self.assertGreaterEqual(msg.process_info[0].stime.duration, 0)
        self.assertGreaterEqual(msg.process_info[0].cutime.duration, 0)
        self.assertGreaterEqual(msg.process_info[0].cstime.duration, 0)
        self.assertEqual(msg.process_info[0].priority, 20)
        self.assertGreaterEqual(msg.process_info[0].num_threads, 1)
        self.assertEqual(msg.process_info[0].rt_priority, 0)
        self.assertEqual(msg.process_info[0].rt_policy, 0)
    # ![test_process_info]


# ![post_shutdown_test]
@APEX_SKIP_TEST_IF(
    34242,
    "launch_testing.LaunchDescription cannot keep Resource Creator alive long enough",
    get_rmw_implementation_identifier() == "rmw_ida"
)
@post_shutdown_test()
class TestDataPublisherShutdown(unittest.TestCase):

    def test_process_exit_codes(self):
        # Checks that all processes exited cleanly
        assertExitCodes(self.proc_info)
# ![post_shutdown_test]
