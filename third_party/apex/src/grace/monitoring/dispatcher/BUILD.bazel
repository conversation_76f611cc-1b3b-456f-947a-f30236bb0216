load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")

ros_pkg(
    name = "dispatcher_pkg",
    bin_executables = ["event_dispatcher"],
    cc_libraries = [
        ":dispatcher",
        ":dispatcher_item",
    ],
    description = "Package containing API for working with event dispatcher",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/apexutils:apexutils_pkg",
        "@apex//common/configuration/settings:settings_pkg",
        "@apex//common/cpputils:cpputils_pkg",
        "@apex//common/interrupt:interrupt_pkg",
        "@apex//common/threading:threading_pkg",
        "@apex//grace/configuration/settings_extensions:settings_extensions_pkg",
        "@apex//grace/execution/apex_init:apex_init_pkg",
        "@apex//grace/execution/executor2:executor2_pkg",
        "@apex//grace/monitoring/dispatcher_interfaces:dispatcher_interfaces_pkg",
        "@apex//grace/monitoring/event:event_pkg",
        "@apex//grace/monitoring/event_registry:event_registry_pkg",
        "@apex//grace/monitoring/event_registry_interfaces:event_registry_interfaces_pkg",
        "@apex//grace/monitoring/logging:logging_pkg",
        "@apex//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "@fmtlib//:fmtlib_pkg",
    ],
)

ament_pkg_resources(
    name = "dispatcher_resources",
    package = "dispatcher",
    resources = {
        "@apex//grace/monitoring/dispatcher:event_dispatcher": "executable",
        "config/default_dispatcher_config.yaml": "share",
    },
    visibility = ["//visibility:public"],
)

cc_library(
    name = "dispatcher",
    srcs = [
        "src/detail/settings_common.cpp",
        "src/dispatcher_factory.cpp",
        "src/dispatcher_par.cpp",
        "src/dispatcher_seq.cpp",
        "src/filter.cpp",
        "src/local_handlers_registrator.cpp",
        "src/local_handlers_settings.cpp",
        "src/remote_handlers_registrator.cpp",
        "src/remote_handlers_settings.cpp",
    ] + glob(["src/handlers/**"]),
    hdrs = [
        "include/dispatcher/constants.hpp",
        "include/dispatcher/detail/settings_common.hpp",
        "include/dispatcher/dispatcher_base.hpp",
        "include/dispatcher/dispatcher_common.hpp",
        "include/dispatcher/dispatcher_factory.hpp",
        "include/dispatcher/dispatcher_par.hpp",
        "include/dispatcher/dispatcher_runner.hpp",
        "include/dispatcher/dispatcher_seq.hpp",
        "include/dispatcher/filter.hpp",
        "include/dispatcher/local_dispatcher_base.hpp",
        "include/dispatcher/local_handlers_registrator.hpp",
        "include/dispatcher/local_handlers_settings.hpp",
        "include/dispatcher/remote_dispatcher_base.hpp",
        "include/dispatcher/remote_handlers_registrator.hpp",
        "include/dispatcher/remote_handlers_settings.hpp",
        "include/dispatcher/types.hpp",
        "include/dispatcher/visibility.hpp",
    ] + glob(["include/dispatcher/handlers/**"]),
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/configuration/settings",
        "@apex//common/cpputils",
        "@apex//common/threading",
        "@apex//grace/monitoring/dispatcher_interfaces",
        "@apex//grace/monitoring/dispatcher_interfaces:dispatcher_common",
        "@apex//grace/monitoring/event_registry",
        "@apex//grace/monitoring/event_registry_interfaces",
        "@apex//grace/monitoring/logging",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_library(
    name = "dispatcher_item",
    srcs = [
        "src/detail/settings_common.cpp",
        "src/dispatcher_item.cpp",
        "src/filter.cpp",
        "src/local_handlers_registrator.cpp",
        "src/local_handlers_settings.cpp",
    ] + glob(["src/handlers/**"]),
    hdrs = [
        "include/dispatcher/constants.hpp",
        "include/dispatcher/detail/settings_common.hpp",
        "include/dispatcher/dispatcher_item.hpp",
        "include/dispatcher/filter.hpp",
        "include/dispatcher/local_dispatcher_base.hpp",
        "include/dispatcher/local_handlers_registrator.hpp",
        "include/dispatcher/local_handlers_settings.hpp",
        "include/dispatcher/types.hpp",
        "include/dispatcher/visibility.hpp",
    ] + glob(["include/dispatcher/handlers/**"]),
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/cpputils",
        "@apex//grace/execution/executor2",
        "@apex//grace/monitoring/dispatcher_interfaces",
        "@apex//grace/monitoring/dispatcher_interfaces:dispatcher_common",
        "@apex//grace/monitoring/event",
        "@apex//grace/monitoring/event_registry_interfaces",
        "@apex//grace/monitoring/logging",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "event_dispatcher",
    srcs = ["src/main.cpp"],
    applicable_licenses = ["//common/integrity:embedded_gold"],
    visibility = ["//visibility:public"],
    deps = [
        ":dispatcher",
        "@apex//common/configuration/settings",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
    ],
)

#! [install_events_to_share]
ament_pkg_resources(
    name = "dispatcher_tests_resources",
    package = "dispatcher",
    resources = {
        "config/common_events.yaml": "share",
        "config/example.yaml": "share",
        "config/events1.yaml": "share",
        "config/events2.yaml": "share",
        "config/events3.yaml": "share",
    },
)
#! [install_events_to_share]

apex_cc_test(
    name = "dispatcher_tests",
    srcs = [
        "test/example_app.hpp",
        "test/test.cpp",
        "test/test_common.hpp",
        "test/test_dispatcher_doc.cpp",
        "test/test_dispatcher_item.cpp",
        "test/test_dispatcher_par.cpp",
        "test/test_dispatcher_runner.cpp",
        "test/test_dispatcher_seq.cpp",
        "test/test_events.hpp",
        "test/test_settings.cpp",
    ],
    data = [
        "config/example.yaml",
        ":dispatcher_tests_resources",
    ],
    defines = ["EXAMPLE_CONFIG_PATH=\\\"$(location config/example.yaml)\\\""],
    env = {
        "IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc",
    },
    tags = [
        "constrained_test",
        "exclusive",
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        ":dispatcher",
        ":dispatcher_item",
        "@apex//common/apexutils",
        "@apex//common/asil:only_qm",
        "@apex//grace/configuration/settings_extensions",
        "@fmtlib",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "BUILD.bazel",
        "CMakeLists.txt",
        "config/common_events.yaml",
        "config/events1.yaml",
        "config/events2.yaml",
        "config/events3.yaml",
        "config/example.yaml",
        "include/dispatcher/handlers/app_error.hpp",
        "include/dispatcher/handlers/echo.hpp",
        "test/test_dispatcher_doc.cpp",
    ],
    visibility = [
        ":__subpackages__",
        "//grace/monitoring/event_registry/design:__subpackages__",
    ],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
