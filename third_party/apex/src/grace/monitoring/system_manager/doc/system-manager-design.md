# system-manager

## Purpose / use cases

The **System Manager** class integrates the functionalities of the  
[Execution Monitor Service](execution-monitor-service-design.md) and an  
[Event Dispatcher](dispatcher-design.md). It manages  
[execution monitor infractions](execution-monitor-design.md#design),  
system [error events](event-registry-design.md#global-events), and  
[user-defined events](event-registry-design.md#application-specific-events).

As a base class, **System Manager** is designed to be inherited from,  
allowing customization of its behavior. It responds to critical events  
by issuing commands to the [Process Manager](process-manager-design.md) and,  
if necessary, invokes an
[overridable shutdown handler](#system-shutdown-interface) when interaction fails.

All critical events and reactions are processed within a single thread,  
which also handles watchdog interactions. These interactions can be  
customized by [overriding the relevant methods](#watchdog-interface) in a subclass.

The package provides a default implementation of the system manager with
the behavior described below.

## Design

### Default implementation

System Manager runs as an executable, receiving settings in the
[Apex.Grace format](settings-design.md#loading-the-repository-from-command-line-arguments).
It must be active before any events or infractions occur and continue running in the
background for the system to function properly.

### Custom implementations

The core class, `system_manager`, can be extended to modify its behavior.
Then, there is a helper abstract factory class that should be implemented to create
this modified version of the system manager. The object of this factory can be further
used with the library function [](apex::system_manager::exe_main)
to implement the custom `main` function of the new system manager variant:

{{ code_snippet("grace/monitoring/system_manager/test/test_system_manager_doc.cpp",
{"tag": "//! [running_custom_system_manager]"}, "cpp") }}

This allows not only to customize the overridable methods of the `system_manager` class
but also:

* Get any additional custom settings from the [settings file](#service-settings)
* Use a custom version of the [timer service](timer-service-design.md)
  for the system manager (for example, a testing mock)
* Implement the `main()` function as a literal two-liner while keeping all the functionality of
  the default implementation

!!! note
    It is highly recommended to use the factory class and the
    [](apex::system_manager::exe_main) function to implement a custom system manager
    as in the example above. The whole setup of the system manager is not trivial
    and involves configuring multiple executors,
    as well as some additional steps that must be exactly replicated
    for the system to work properly. The usage of the factory class hides this
    complexity.

## Service settings

Example configuration:

{{ code_snippet("grace/monitoring/system_manager/param/config.yaml", "yaml") }}

### Execution monitor and Dispatcher settings

For a detailed breakdown of the settings,
see [execution monitor service settings](execution-monitor-service-design.md)
and [dispatcher settings](dispatcher-design.md#the-dispatcher-properties)
for corresponding sections.

!!! note
    Setting `thread-count` to `0`
    disables the [auxiliary dispatcher](#auxiliary-dispatcher),
    leaving only the [system dispatcher](#system-dispatcher) active.
    This is also the default behavior if the `thread-count` is unspecified.

### Process manager settings

* The optional `name` property defines the name of the instance of the
  [Process Manager](process-manager-design.md) the System Manager communicates with.
* The optional `max-wait-ms` property defines the timeout for the Process Manager's responses.
  The default value is 1 second.
* The optional `heartbeat-timeout-ms` property defines how long to wait for the Process Manager's
  heartbeat before considering it dead. By default, it is 0, which means that the heartbeat is not
  monitored.

### Watchdog settings

* The optional `kick-interval-ms` property defines the interval for triggering
  the [`on_kick_watchdog` method](#watchdog-interface). The default value is 0,
  which disables the watchdog.

## System manager customizations

All the following customization points apply strictly to the main system dispatcher
and involve overriding methods of the `system_manager` class.
Additionally, helper methods such as logging and process management methods
are inherited from the parent class and can be called from within these overridden methods.

### Process Manager interface

* [](apex::system_manager::system_manager::pm_group_restart) –
  Restarts a process group by `PID` or name
* [](apex::system_manager::system_manager::pm_group_change_state) -
  Changes a process group's state by `PID` or name
* [](apex::system_manager::system_manager::pm_group_get_info) –
  Retrieves information about a process group by `PID` or name

!!! note
    When called with a process ID (`PID`), these methods locate the associated group
    and apply the operation to the entire group. These methods are part of the base
    [](apex::system_manager::system_manager)
    class and can be used in overridden methods to interact with the Process Manager.

### Watchdog interface

* [](apex::system_manager::system_manager::on_kick_watchdog) –
  Invoked at the interval specified in the settings

This method handles watchdog interactions.
The default implementation does nothing.
Since the system dispatcher runs on a single thread, the watchdog interval should
exceed the execution time of any user-defined handler.
Alternatively, `on_kick_watchdog` can be integrated into busy loops or wait operations
inside the handlers.

!!! note
    The [Process Manager interface](#process-manager-interface) methods invoke
    `on_kick_watchdog` while waiting for a response, allowing service timeouts
    longer than the watchdog interval.

### System shutdown interface

* [](apex::system_manager::system_manager::on_shutdown) – Called when a full system shutdown is required

This method can be triggered by error or event handlers and is passed
the last event as an argument.
The default implementation logs a message but takes no action.
Custom implementations should override this method.

### Logger interface

The [](apex::system_manager::system_manager::log_event)
method is available for logging human-readable event descriptions

When overriding:

* [`on_error`](#customizing-error-reactions)
* [`on_infraction`](#customizing-infraction-reactions)
* [`on_unexpected_exit`](#customizing-unexpected-process-termination-reactions)
* [`on_unexpected_exit_from_framework_group`](#customizing-unexpected-framework-process-termination)

explicit logging of events is unnecessary since the events that trigger these methods
are always logged before the methods are called.
There is also a protected `m_logger` member that can be used
with regular [logging macros](logging-design.md#log-messages-with-a-per-node-logger).

### Event Dispatcher interface

The Event Dispatcher functionality is separated into two parts:

1. [System dispatcher](#system-dispatcher):
    * Responsible for handling critical system events
    * Integrated into the System Manager's main execution loop and cannot be disabled
    * Does not support [remote handlers](dispatcher-design.md#remote-handlers)
    * Configurable via overriding the System Manager's methods only
    * Single-threaded
    * Watchdog interactions are interwoven with event processing
2. [Auxiliary dispatcher](#auxiliary-dispatcher):
    * Handles any additional application-specific events in the system
    * Can be disabled by setting `thread-count` to `0`
    * Configurable via the config file only
    * Supports [remote handlers](dispatcher-design.md#remote-handlers)
    * Can be multithreaded
    * Does not affect watchdog interactions

#### System dispatcher

System dispatcher is based on the
[executor compatible dispatcher](dispatcher-design.md#an-executor-compatible-dispatcher).

The event handlers can be added to it by overriding the
[](apex::system_manager::system_manager::register_event_handlers) method:

{{ code_snippet("grace/monitoring/system_manager/test/test_system_manager_doc.cpp",
{"tag": "//! [add_custom_events]"}, "cpp") }}

All the event handlers are local and must take into account the watchdog timeout
since they are dispatched in the main execution loop. Even though they can
call the [`on_kick_watchdog` method](#watchdog-interface)
explicitly during long operations, it is still
not recommended to perform any long operations in these handlers since it may
negatively affect the responsiveness of the system to critical events.

#### Auxiliary dispatcher

The auxiliary dispatcher is based on the
[dispatcher](dispatcher-design.md#dispatcher-service).

The event handlers can be added to it
[via the config file](dispatcher-design.md#handler-definitions).
These handlers are typically
[external (remote)](dispatcher-design.md#the-remote-handler-list) and are not called
on the main execution loop of the System Manager
but on one of the worker threads instead.

!!! note
    While adding YAML event definitions
    [into the config file](dispatcher-design.md#event-definitions), it is not necessary to include
    any definitions that are already included in the source via the header files.

!!! warning
    Even though the auxiliary dispatcher is isolated from the watchdog interactions
    and should not influence them directly, critical failures
    (e.g., a [dispatching thread feedback timeout](dispatcher-design.md#the-dispatcher-properties)
    for the multithreaded version of the dispatcher)
    may lead to the System Manager's process termination.

### Customizing error reactions

Error reactions (`AppError`, `ExecutorError`, `ProcessError`) can be customized
by overriding the [](apex::system_manager::system_manager::on_error) method:

{{ code_snippet("grace/monitoring/system_manager/test/test_system_manager_doc.cpp",
{"tag": "//! [custom_error]"}, "cpp") }}

The default action is `pm_group_change_state(pid, "OFF")`,
shutting down the process group. If service interaction fails, the `on_shutdown`
[method](#system-shutdown-interface) is called.

### Customizing unexpected process termination reactions

To handle unexpected process terminations
(`ProcessUnexpectedAbnormalExit`, `ProcessUnexpectedGracefulExit`, `ProcessAbnormalExit`),
override [](apex::system_manager::system_manager::on_unexpected_exit) method:

{{ code_snippet("grace/monitoring/system_manager/test/test_system_manager_doc.cpp",
{"tag": "//! [custom_unexpected]"}, "cpp") }}

The default action is `pm_group_change_state(pid, "OFF")` for the first two events,
while the last event is ignored. If service interaction fails,
the `on_shutdown` [method](#system-shutdown-interface) is called.

!!! note
    For events originating in the
    [Process Manager](process-manager-design.md#dispatcher-event-sender) the PID of the
    process being reported is placed in the
    [`int_data` field](dispatcher-design.md#event) of the event since the originating
    process in these cases is the Process Manager itself and the `pid` field holds its
    process id.

### Customizing unexpected framework process termination

For the [framework group processes](process-manager-design.md#framework-group),
unexpected exits
(`ProcessUnexpectedGracefulExitFrameworkGroup`, `ProcessUnexpectedAbnormalExitFrameworkGroup`)
can be handled by overriding [](apex::system_manager::system_manager::on_unexpected_exit_from_framework_group)
method.
By default, the `on_shutdown` [method](#system-shutdown-interface) is called.

!!! note
    For events originating in the
    [Process Manager](process-manager-design.md#dispatcher-event-sender) the PID of the
    process being reported is placed in the
    [`int_data` field](dispatcher-design.md#event) of the event since the originating
    process in these cases if the Process Manager itself so the `pid` field holds its
    PID.

### Customizing infraction reactions

To handle expectation infractions, override [](apex::system_manager::system_manager::on_infraction) method:

{{ code_snippet("grace/monitoring/system_manager/test/test_system_manager_doc.cpp",
{"tag": "//! [custom_infraction]"}, "cpp") }}

For more details, see the
[infraction format description](execution-monitor-service-design.md#infractions-reporting-format).

The default action is `pm_group_change_state(pid, "OFF")`, restarting the process group.
If service interaction fails, the `on_shutdown` [method](#system-shutdown-interface) is called.

## Error handling

Process Manager control methods do not throw errors if the Process Manager does not respond;
instead, they return an error code or an empty `std::optional`.
However, they can throw exceptions on internal errors, which propagate from the executor as usual.

The [](apex::system_manager::exe_main) implementation sends
[lifecycle events](api-usage.md#enabling-event-reporting-from-an-executor-and-executable-items)
automatically.
