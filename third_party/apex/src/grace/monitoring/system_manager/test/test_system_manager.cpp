// Copyright 2024 Apex.AI, Inc.
// All rights reserved.
#include <gtest/gtest.h>

#include <algorithm>
#include <atomic>
#include <memory>
#include <optional>  // NOLINT
#include <string>
#include <utility>
#include <variant>  // NOLINT
#include <vector>

#include "dispatcher_interfaces/common.hpp"
#include "event/sender.hpp"
#include "event_registry/event_registry.hpp"
#include "event_registry/event_registry_remote.hpp"
#include "execution_monitor/communication.hpp"
#include "execution_monitor/execution_monitor.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "interrupt/interrupt_handler.hpp"
#include "process_manager_interfaces/common.hpp"
#include "process_manager_interfaces/events/events.hpp"
#include "process_manager_interfaces/srv/change_state.hpp"
#include "process_manager_interfaces/srv/get_process_group_info.hpp"
#include "process_manager_interfaces/srv/restart.hpp"
#include "rclcpp/dynamic_waitset/waitset.hpp"
#include "rclcpp/rclcpp.hpp"
#include "settings/construct.hpp"
#include "settings/from_yaml.hpp"
#include "system_manager/system_manager.hpp"
#include "system_manager/system_manager_main.hpp"
#include "threading/thread.hpp"
#include "timer_service/clock_timer_service.hpp"

using namespace std::chrono_literals;

namespace common = apex::execution_monitor::common;
namespace dw = rclcpp::dynamic_waitset;
using execution_monitor =
  apex::execution_monitor::execution_monitor<apex::execution_monitor::task_identifier::task_id_t>;
using apex::executor::executor_runner;
namespace factory = apex::executor::executor_factory;
using communication = apex::execution_monitor::communication_client;
namespace expectations = apex::execution_monitor::expectations;
using wc_runtime_exp = expectations::wall_clock_runtime;
using cpu_runtime_exp = expectations::cpu_clock_runtime;
using act_window_exp = expectations::activations_per_window;
using act_delta_exp = expectations::activation_distance;
namespace event = apex::event;
using pm_restart_t = process_manager_interfaces::srv::Restart;
using pm_change_state_t = process_manager_interfaces::srv::ChangeState;
using pm_get_info_t = process_manager_interfaces::srv::GetProcessGroupInfo;
using pm_restart_t_ret_code_t =
  process_manager_interfaces::srv::Restart::Response::BorrowedType::ReturnCodeType;
using pm_change_state_ret_code_t =
  process_manager_interfaces::srv::ChangeState::Response::BorrowedType::ReturnCodeType;
using pm_group_info_t = process_manager_interfaces::srv::GetProcessGroupInfo::Response::
  BorrowedType::_process_group_info_type;
using apex::system_manager::settings;
using apex::system_manager::system_manager;
namespace pm = apex::process_manager::common;
using apex::dispatcher::types::event_t;
using apex::system_manager::pm_heartbeat_failure;

namespace
{
class system_manager_test : public ::testing::Test
{
public:
  void SetUp() override
  {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
    m_node = std::make_shared<rclcpp::Node>("test_node");
    m_pm_restart_srv =
      m_node->create_polling_service<pm_restart_t>(pm::create_restart_service_name());
    m_pm_change_state_srv =
      m_node->create_polling_service<pm_change_state_t>(pm::create_change_state_service_name());
    m_pm_get_info_srv =
      m_node->create_polling_service<pm_get_info_t>(pm::create_get_state_service_name());
    m_pm_restart_ws = std::make_unique<dw::Waitset>(m_pm_restart_srv);
    m_pm_change_state_ws = std::make_unique<dw::Waitset>(m_pm_change_state_srv);
    m_pm_get_info_ws = std::make_unique<dw::Waitset>(m_pm_get_info_srv);
    m_timer_srv = std::make_unique<apex::timer_service::steady_clock_timer_service>();
  }

  void TearDown() override
  {
    (void)rclcpp::shutdown();
  }

  void create_event_sender(
    std::string_view AppId = "",
    const std::string & dispatcher_name = apex::dispatcher::common::get_default_instance_name())
  {
    m_event_reg_pub.emplace(*m_node, dispatcher_name);
    m_event_reg_pub->publish(apex::event::get_local_registry(), 1U);
    m_event_sender = std::make_shared<event::sender>(m_node, 0, AppId);
  }

  auto create_com(const std::string & service_name = "")
  {
    return std::make_unique<communication>(service_name);
  }

  struct sm_runner
  {
    explicit sm_runner(system_manager & sm) : sm{sm}
    {
      main_exec = factory::create();
      com_exec = factory::create();
      sm.register_for_execution(*main_exec, *com_exec);
      main_runner.emplace(*main_exec);
      com_runner.emplace(*com_exec);
    }

    ~sm_runner()
    {
      if (com_runner) {
        com_runner->stop();
      }
      if (main_runner) {
        main_runner->stop();
      }
    }

    void stop()
    {
      com_runner->stop();
      main_runner->stop();
      com_runner.reset();
      main_runner.reset();
    }

    system_manager & sm;
    apex::executor::live_executor_ptr main_exec;
    apex::executor::live_executor_ptr com_exec;
    std::optional<apex::executor::executor_runner> main_runner;
    std::optional<apex::executor::executor_runner> com_runner;
  };

  bool expect_restart_pm_call()
  {
    if (!m_pm_restart_ws->wait(3s)) {
      return false;
    }
    decltype(m_pm_restart_srv->take_request()) req;
    do {
      req = m_pm_restart_srv->take_request(1);
    } while (req.empty() || !req[0].info().valid());
    auto req_id = req[0].request_header();
    auto resp = m_pm_restart_srv->borrow_loaned_response();
    resp->return_code = pm_restart_t_ret_code_t::OK;
    m_pm_restart_srv->send_response(req_id, std::move(resp));
    return req[0].data().pid == ::getpid();
  }

  bool expect_change_state_call(const std::string & state = "test")
  {
    if (!m_pm_change_state_ws->wait(3s)) {
      return false;
    }
    decltype(m_pm_change_state_srv->take_request()) req;
    do {
      req = m_pm_change_state_srv->take_request(1);
    } while (req.empty() || !req[0].info().valid());
    auto req_id = req[0].request_header();
    auto resp = m_pm_change_state_srv->borrow_loaned_response();
    resp->return_code = pm_change_state_ret_code_t::OK;
    m_pm_change_state_srv->send_response(req_id, std::move(resp));
    return (req[0].data().pid == ::getpid()) && (req[0].data().requested_state == state);
  }

  bool expect_group_get_info_call()
  {
    if (!m_pm_get_info_ws->wait(3s)) {
      return false;
    }
    decltype(m_pm_get_info_srv->take_request()) req;
    do {
      req = m_pm_get_info_srv->take_request(1);
    } while (req.empty() || !req[0].info().valid());
    auto req_id = req[0].request_header();
    auto resp = m_pm_get_info_srv->borrow_loaned_response();
    resp->process_group_info.process_group_name = "test";
    m_pm_get_info_srv->send_response(req_id, std::move(resp));
    return req[0].data().pid == ::getpid();
  }

  template <class F>
  bool wait_while(const F & condition, std::chrono::milliseconds timeout = 5000ms)
  {
    const auto begin = std::chrono::steady_clock::now();
    while ((std::chrono::steady_clock::now() - begin) < timeout) {
      if (!condition()) {
        return true;
      }
      std::this_thread::sleep_for(1ms);
    }
    return false;
  }

  std::shared_ptr<rclcpp::Node> m_node;
  event::sender_ptr m_event_sender;
  std::shared_ptr<rclcpp::PollingService<pm_restart_t>> m_pm_restart_srv;
  std::shared_ptr<rclcpp::PollingService<pm_change_state_t>> m_pm_change_state_srv;
  std::shared_ptr<rclcpp::PollingService<pm_get_info_t>> m_pm_get_info_srv;
  std::unique_ptr<rclcpp::dynamic_waitset::Waitset> m_pm_restart_ws;
  std::unique_ptr<rclcpp::dynamic_waitset::Waitset> m_pm_change_state_ws;
  std::unique_ptr<rclcpp::dynamic_waitset::Waitset> m_pm_get_info_ws;
  std::unique_ptr<apex::timer_service::timer_service_interface> m_timer_srv;
  std::optional<apex::event::event_registry_publisher> m_event_reg_pub;
};

struct compare_msgs
{
  bool operator()(const execution_monitor_msgs::msg::Infraction & left,
                  const execution_monitor_msgs::msg::Infraction & right)
  {
    return left.task_id < right.task_id;
  }
};

}  // namespace

TEST_F(system_manager_test, infractions)
{
  settings s;
  system_manager sm{s, *m_timer_srv};
  sm_runner r{sm};
  const auto com = create_com();
  execution_monitor monitor1{"test_monitor1", *com};
  monitor1.register_task("id1", {wc_runtime_exp(10ms)});
  monitor1.register_task("id2", {cpu_runtime_exp(10s, 20s)});
  monitor1.registrations_complete();
  execution_monitor monitor2{"test_monitor2", *com};
  monitor2.register_task("id3", {wc_runtime_exp(10ms)});
  monitor2.register_task("id4", {cpu_runtime_exp(10s, 20s)});
  monitor2.registrations_complete();
  monitor1.task_starts("id1");
  monitor1.task_starts("id2");
  monitor2.task_starts("id3");
  monitor2.task_starts("id4");
  std::this_thread::sleep_for(500ms);
  ASSERT_TRUE(expect_change_state_call(apex::process_manager::common::OFF_STATE_NAME));
  ASSERT_TRUE(expect_change_state_call(apex::process_manager::common::OFF_STATE_NAME));
  monitor1.task_completes("id2");
  monitor2.task_completes("id4");
  ASSERT_TRUE(expect_change_state_call(apex::process_manager::common::OFF_STATE_NAME));
  ASSERT_TRUE(expect_change_state_call(apex::process_manager::common::OFF_STATE_NAME));
  ASSERT_NO_THROW(monitor2.unregister_monitor_tasks());
  ASSERT_NO_THROW(monitor1.unregister_monitor_tasks());
  ASSERT_NO_THROW(r.stop());
}

TEST_F(system_manager_test, unexpected_termination_event)
{
  settings s;
  system_manager sm{s, *m_timer_srv};
  sm_runner r{sm};
  create_event_sender();
  m_event_sender->send(event::ProcessUnexpectedAbnormalExit, ::getpid());
  ASSERT_TRUE(expect_change_state_call(apex::process_manager::common::OFF_STATE_NAME));
  ASSERT_NO_THROW(r.stop());
}

TEST_F(system_manager_test, error_event)
{
  settings s;
  system_manager sm{s, *m_timer_srv};
  sm_runner r{sm};
  create_event_sender();
  m_event_sender->send(event::ProcessError);
  ASSERT_TRUE(expect_change_state_call(apex::process_manager::common::OFF_STATE_NAME));
  ASSERT_NO_THROW(r.stop());
}

TEST_F(system_manager_test, custom_handlers)
{
  class custom_system_manager : public system_manager
  {
  public:
    using system_manager::system_manager;

    std::atomic<std::int32_t> register_event_handlers_calls{0};
    std::vector<infraction_t> infractions;
    std::atomic<std::int32_t> infraction_count{0};
    std::vector<apex::dispatcher::types::event_t> errors;
    std::atomic<std::int32_t> error_count{0};
    std::vector<apex::dispatcher::types::event_t> unexpected_exits;
    std::atomic<std::int32_t> unexpected_exits_count{0};

  private:
    void register_event_handlers(apex::dispatcher::local_dispatcher_base &) override
    {
      ++register_event_handlers_calls;
    }

    void on_infraction(const infraction_t & infraction) override
    {
      infractions.push_back(infraction);
      ++infraction_count;
    }

    void on_error(apex::dispatcher::types::event_cref_t event) override
    {
      errors.push_back(event);
      ++error_count;
    }

    void on_unexpected_exit(apex::dispatcher::types::event_cref_t event) override
    {
      unexpected_exits.push_back(event);
      ++unexpected_exits_count;
    }

    void on_unexpected_exit_from_framework_group(
      apex::dispatcher::types::event_cref_t event) override
    {
      unexpected_exits.push_back(event);
      ++unexpected_exits_count;
    }
  };

  settings s;
  custom_system_manager sm{s, *m_timer_srv};
  sm_runner r{sm};
  const auto com = create_com();
  execution_monitor monitor1{"test_monitor1", *com};
  monitor1.register_task("id1", {wc_runtime_exp(10ms)});
  monitor1.register_task("id2", {cpu_runtime_exp(10s, 20s)});
  monitor1.registrations_complete();
  execution_monitor monitor2{"test_monitor2", *com};
  monitor2.register_task("id3", {wc_runtime_exp(10ms)});
  monitor2.register_task("id4", {cpu_runtime_exp(10s, 20s)});
  monitor2.registrations_complete();
  monitor1.task_starts("id1");
  monitor1.task_starts("id2");
  monitor2.task_starts("id3");
  monitor2.task_starts("id4");
  std::this_thread::sleep_for(500ms);
  create_event_sender();
  m_event_sender->send(event::ExecutorError);
  m_event_sender->send(event::AppError);
  monitor1.task_completes("id2");
  monitor2.task_completes("id4");
  ASSERT_TRUE(wait_while([&sm] { return sm.infraction_count < 4; }));
  m_event_sender->send(event::ProcessUnexpectedAbnormalExit, ::getpid());
  m_event_sender->send(event::ProcessUnexpectedGracefulExitFrameworkGroup, ::getpid());
  ASSERT_TRUE(wait_while([&sm] {
    return sm.register_event_handlers_calls < 1 || sm.unexpected_exits_count < 2 ||
           sm.error_count < 2;
  }));
  ASSERT_NO_THROW(monitor2.unregister_monitor_tasks());
  ASSERT_NO_THROW(monitor1.unregister_monitor_tasks());
  ASSERT_NO_THROW(r.stop());
  ASSERT_EQ(sm.unexpected_exits.size(), 2U);
  ASSERT_EQ(sm.infractions.size(), 4U);
  ASSERT_EQ(sm.errors.size(), 2U);
  std::sort(sm.infractions.begin(), sm.infractions.end(), compare_msgs{});
  ASSERT_EQ(sm.infractions[0].task_id, "id1");
  ASSERT_EQ(sm.infractions[0].monitor_name, "test_monitor1");
  ASSERT_EQ(sm.infractions[0].pid, ::getpid());
  ASSERT_NE(sm.infractions[0].counter, 0U);
  ASSERT_NE(sm.infractions[0].backend_overrun_ns, 0U);
  ASSERT_EQ(sm.infractions[1].task_id, "id2");
  ASSERT_EQ(sm.infractions[1].monitor_name, "test_monitor1");
  ASSERT_EQ(sm.infractions[1].pid, ::getpid());
  ASSERT_NE(sm.infractions[1].counter, 0U);
  ASSERT_EQ(sm.infractions[2].task_id, "id3");
  ASSERT_EQ(sm.infractions[2].monitor_name, "test_monitor2");
  ASSERT_EQ(sm.infractions[2].pid, ::getpid());
  ASSERT_NE(sm.infractions[2].counter, 0U);
  ASSERT_NE(sm.infractions[2].backend_overrun_ns, 0U);
  ASSERT_EQ(sm.infractions[3].task_id, "id4");
  ASSERT_EQ(sm.infractions[3].monitor_name, "test_monitor2");
  ASSERT_EQ(sm.infractions[3].pid, ::getpid());
  ASSERT_NE(sm.infractions[3].counter, 0U);
  ASSERT_EQ(sm.errors[0].event_id, event::ExecutorError);
  ASSERT_EQ(sm.errors[1].event_id, event::AppError);
  ASSERT_EQ(sm.unexpected_exits[0].event_id, event::ProcessUnexpectedAbnormalExit);
  ASSERT_EQ(sm.unexpected_exits[1].event_id, event::ProcessUnexpectedGracefulExitFrameworkGroup);
}

TEST_F(system_manager_test, pm_service_calls)
{
  class custom_system_manager : public system_manager
  {
  public:
    using system_manager::system_manager;

    std::optional<pm_restart_t_ret_code_t> restart_ret_code;
    std::optional<pm_change_state_ret_code_t> change_state_ret_code;
    std::optional<pm_group_info_t> group_info;

  private:
    void register_event_handlers(apex::dispatcher::local_dispatcher_base &) override
    {
      restart_ret_code = pm_group_restart(::getpid());
      change_state_ret_code = pm_group_change_state(::getpid(), "test");
      group_info = pm_group_get_info(::getpid());
    }
  };

  settings s;
  custom_system_manager sm{s, *m_timer_srv};
  const auto exec = factory::create();
  const auto com_exec = factory::create();
  apex::threading::thread t{
    [&sm, &exec, &com_exec] { sm.register_for_execution(*exec, *com_exec); }};
  t.issue();
  ASSERT_TRUE(expect_restart_pm_call());
  ASSERT_TRUE(expect_change_state_call());
  ASSERT_TRUE(expect_group_get_info_call());
  ASSERT_NO_THROW(t.join());
  ASSERT_NE(sm.restart_ret_code, std::nullopt);
  ASSERT_NE(sm.change_state_ret_code, std::nullopt);
  ASSERT_NE(sm.group_info, std::nullopt);
  ASSERT_EQ(*sm.restart_ret_code, pm_restart_t_ret_code_t::OK);
  ASSERT_EQ(*sm.change_state_ret_code, pm_change_state_ret_code_t ::OK);
  ASSERT_EQ(sm.group_info->process_group_name, "test");
}

TEST(system_manager_test_main, main_function)
{
  apex::threading::thread t{[] {
    std::this_thread::sleep_for(500ms);
    apex::interrupt_handler::trigger();
  }};
  t.issue();
  ASSERT_NO_THROW(apex::system_manager::exe_main(0, nullptr));
  ASSERT_NO_THROW(t.join());
}

TEST(system_manager_test_settings, settings_parsing)
{
  apex::settings::construct::dictionary dict;
  apex::settings::yaml::from_string(
    R"(
execution-monitor-service:
  name: "test_name_em"
  infractions:
    history: 999
  poll-time-ms: 888
  max-execution-monitors: 777
dispatcher:
  name: "test_name_d"
  namespace: "test_ns_d"
process-manager:
  name: "test_name_pm"
  max-wait-ms: 666
  heartbeat-timeout-ms: 555
watchdog:
  kick-interval-ms: 1919
)",
    dict);
  const auto s = apex::system_manager::settings::get(dict);
  ASSERT_EQ(s.em_service_name, "test_name_em");
  ASSERT_EQ(s.em_max_infraction_history_depth, 999);
  ASSERT_EQ(s.em_poll_time_ms, 888);
  ASSERT_EQ(s.em_max_num_monitors, 777);
  ASSERT_EQ(s.dispatcher_name, "test_name_d");
  ASSERT_EQ(s.dispatcher_namespace, "test_ns_d");
  ASSERT_EQ(s.max_wait_process_manager_ms, 666);
  ASSERT_EQ(s.process_manager_name, "test_name_pm");
  ASSERT_EQ(s.process_manager_heartbeat_timeout_ms, 555);
  ASSERT_EQ(s.watchdog_kick_interval_ms, 1919);
}

TEST_F(system_manager_test, framework_errors)
{
  class custom_system_manager : public system_manager
  {
  public:
    using system_manager::system_manager;
    std::atomic<std::int32_t> event_count{0};

  private:
    void on_unexpected_exit_from_framework_group(apex::dispatcher::types::event_cref_t) override
    {
      ++event_count;
    }
  };

  settings s;
  custom_system_manager sm{s, *m_timer_srv};
  sm_runner r{sm};
  create_event_sender();
  m_event_sender->send(event::ProcessUnexpectedAbnormalExitFrameworkGroup);
  m_event_sender->send(event::ProcessUnexpectedGracefulExitFrameworkGroup);
  ASSERT_TRUE(wait_while([&sm] { return sm.event_count < 2; }));
  ASSERT_NO_THROW(r.stop());
}

TEST_F(system_manager_test, framework_errors_lead_to_shutdown)
{
  class custom_system_manager : public system_manager
  {
  public:
    using system_manager::system_manager;
    std::atomic<std::int32_t> shutdown_count{0};

  private:
    void on_shutdown(shutdown_reason) override
    {
      ++shutdown_count;
    }
  };

  settings s;
  custom_system_manager sm{s, *m_timer_srv};
  sm_runner r{sm};
  create_event_sender();
  m_event_sender->send(event::ProcessUnexpectedAbnormalExitFrameworkGroup);
  m_event_sender->send(event::ProcessUnexpectedGracefulExitFrameworkGroup);
  ASSERT_TRUE(wait_while([&sm] { return sm.shutdown_count < 2; }));
  ASSERT_NO_THROW(r.stop());
}

TEST_F(system_manager_test, shutdown_on_no_answer)
{
  class custom_system_manager : public system_manager
  {
  public:
    using system_manager::system_manager;
    std::atomic<std::int32_t> shutdown_count{0};

  private:
    void on_shutdown(shutdown_reason) override
    {
      ++shutdown_count;
    }
  };

  settings s;
  s.max_wait_process_manager_ms = 10;
  custom_system_manager sm{s, *m_timer_srv};
  sm_runner r{sm};
  const auto com = create_com();
  execution_monitor monitor1{"test_monitor1", *com};
  monitor1.register_task("id1", {wc_runtime_exp(10ms)});
  monitor1.registrations_complete();
  monitor1.task_starts("id1");
  std::this_thread::sleep_for(500ms);
  monitor1.task_completes("id1");
  ASSERT_TRUE(wait_while([&sm] { return sm.shutdown_count < 1; }));
  create_event_sender();
  m_event_sender->send(event::ProcessError);
  m_event_sender->send(event::ProcessUnexpectedAbnormalExit, ::getpid());
  m_event_sender->send(apex::event::ProcessUnexpectedGracefulExit, ::getpid());
  ASSERT_TRUE(wait_while([&sm] { return sm.shutdown_count < 4; }));
  monitor1.unregister_monitor_tasks();
  ASSERT_NO_THROW(r.stop());
}

TEST_F(system_manager_test, periodic_watchdog)
{
  class custom_system_manager : public system_manager
  {
  public:
    using system_manager::system_manager;
    std::atomic<std::int32_t> kick_count{0};

  private:
    void on_kick_watchdog() override
    {
      ++kick_count;
    }
  };

  settings s;
  s.watchdog_kick_interval_ms = 10;
  custom_system_manager sm{s, *m_timer_srv};
  sm_runner r{sm};
  ASSERT_TRUE(wait_while([&sm] { return sm.kick_count < 10; }));
  ASSERT_NO_THROW(r.stop());
}

TEST_F(system_manager_test, watchdog_while_waiting_for_service)
{
  class custom_system_manager : public system_manager
  {
  public:
    using system_manager::system_manager;
    std::atomic<std::int32_t> kick_count{0};

  private:
    void register_event_handlers(apex::dispatcher::local_dispatcher_base &) override
    {
      pm_group_restart(::getpid());
      pm_group_change_state("test", "test");
      pm_group_get_info("test");
    }

    void on_kick_watchdog() override
    {
      ++kick_count;
    }
  };

  settings s;
  s.max_wait_process_manager_ms = 200;
  s.watchdog_kick_interval_ms = 10;
  custom_system_manager sm{s, *m_timer_srv};
  const auto exec = factory::create();
  const auto com_exec = factory::create();
  sm.register_for_execution(*exec, *com_exec);
  ASSERT_TRUE(wait_while([&sm] { return sm.kick_count < 30; }));
}

TEST_F(system_manager_test, no_watchdog)
{
  class custom_system_manager : public system_manager
  {
  public:
    using system_manager::system_manager;
    std::int32_t kick_count{0};

  private:
    void register_event_handlers(apex::dispatcher::local_dispatcher_base &) override
    {
      pm_group_restart(::getpid());
      pm_group_change_state("test", "test");
      pm_group_get_info("test");
    }

    void on_kick_watchdog() override
    {
      ++kick_count;
    }
  };

  settings s;
  s.max_wait_process_manager_ms = 200;
  s.watchdog_kick_interval_ms = 0;
  custom_system_manager sm{s, *m_timer_srv};
  sm_runner r{sm};
  std::this_thread::sleep_for(200ms);
  ASSERT_NO_THROW(r.stop());
  ASSERT_EQ(sm.kick_count, 0);
}

TEST_F(system_manager_test, pm_failure)
{
  class custom_system_manager : public system_manager
  {
  public:
    using system_manager::system_manager;
    std::atomic<std::int32_t> shutdown_count{0};

  private:
    void on_shutdown(shutdown_reason reason) override
    {
      const auto & ev = std::get<event_t>(reason);
      ASSERT_STREQ(ev.app_id.c_str(), process_manager_app::AppId.data());
      ASSERT_EQ(ev.event_id, process_manager_app::event::ProcessGroupMonitorThreadFailure);
      ++shutdown_count;
    }
  };

  settings s;
  custom_system_manager sm{s, *m_timer_srv};
  sm_runner r{sm};
  create_event_sender(process_manager_app::AppId);
  m_event_sender->send(process_manager_app::event::ProcessGroupMonitorThreadFailure, ::getpid());
  ASSERT_TRUE(wait_while([&sm] { return sm.shutdown_count < 1; }));
  ASSERT_NO_THROW(r.stop());
}

TEST_F(system_manager_test, pm_heartbeat)
{
  class custom_system_manager : public system_manager
  {
  public:
    using system_manager::system_manager;
    std::atomic<std::int32_t> shutdown_count{0};

  private:
    void on_shutdown(shutdown_reason reason) override
    {
      const auto & pm_failure = std::get<pm_heartbeat_failure>(reason);
      ASSERT_EQ(pm_failure.timeout_exceeded_ms, 1000);
      ++shutdown_count;
    }
  };

  settings s;
  s.process_manager_heartbeat_timeout_ms = 1000;
  custom_system_manager sm{s, *m_timer_srv};
  sm_runner r{sm};
  create_event_sender(process_manager_app::AppId);
  for (auto i = 0; i < 30; ++i) {
    m_event_sender->send(process_manager_app::event::ProcessManagerHeartbeat, ::getpid());
    std::this_thread::sleep_for(50ms);
  }
  ASSERT_EQ(sm.shutdown_count, 0);
  ASSERT_TRUE(wait_while([&sm] { return sm.shutdown_count < 1; }));
  ASSERT_NO_THROW(r.stop());
}
