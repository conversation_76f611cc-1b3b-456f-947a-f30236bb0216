load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "system_manager_pkg",
    cc_libraries = [":system_manager_lib"],
    description = "The system manager.",
    lib_executables = [
        ":system_manager_exe",
    ],
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/apexutils:apexutils_pkg",
        "//common/cpputils:cpputils_pkg",
        "//common/interrupt:interrupt_pkg",
        "//grace/execution/apex_init:apex_init_pkg",
        "//grace/execution/executor2:executor2_pkg",
        "//grace/execution/timer_service:timer_service_pkg",
        "//grace/interfaces/process_manager_interfaces:process_manager_interfaces_pkg",
        "//grace/monitoring/dispatcher:dispatcher_pkg",
        "//grace/monitoring/event:event_pkg",
        "//grace/monitoring/event_registry:event_registry_pkg",
        "//grace/monitoring/execution_monitor_service:execution_monitor_service_pkg",
        "//grace/monitoring/logging:logging_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
    ],
)

cc_library(
    name = "system_manager_lib",
    srcs = [
        "src/system_manager.cpp",
        "src/system_manager_main.cpp",
        "src/system_manager_settings.cpp",
    ],
    hdrs = [
        "include/system_manager/aux_dispatcher_wrapper.hpp",
        "include/system_manager/system_manager.hpp",
        "include/system_manager/system_manager_main.hpp",
        "include/system_manager/system_manager_settings.hpp",
        "include/system_manager/visibility.hpp",
    ],
    applicable_licenses = ["//common/integrity:embedded_gold"],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/execution/timer_service",
        "//grace/interfaces/process_manager_interfaces",
        "//grace/interfaces/process_manager_interfaces:process_manager_common",
        "//grace/monitoring/dispatcher",
        "//grace/monitoring/dispatcher:dispatcher_item",
        "//grace/monitoring/event",
        "//grace/monitoring/event_registry",
        "//grace/monitoring/execution_monitor_service:execution_monitor_service_lib",
        "//grace/monitoring/logging",
    ],
)

cc_binary(
    name = "system_manager_exe",
    srcs = ["src/system_manager_cli.cpp"],
    applicable_licenses = ["//common/integrity:embedded_gold"],
    visibility = ["//visibility:public"],
    deps = [
        "//grace/monitoring/system_manager:system_manager_lib",
    ],
)

apex_cc_test(
    name = "test_system_manager",
    srcs = [
        "test/example_app.hpp",
        "test/test_system_manager.cpp",
        "test/test_system_manager_doc.cpp",
        "test/test_system_manager_main.cpp",
    ],
    data = [
        "test/config/disabled_aux_dispatcher.yaml",
        "test/config/with_aux_dispatcher.yaml",
        "test/config/without_aux_dispatcher.yaml",
    ],
    env = {
        "APEX_GRACE_LOG_LEVEL": "TRACE",
        "IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc",
    },
    local_defines = [
        "WITHOUT_AUX_DISPATCHER=\\\"$(location test/config/without_aux_dispatcher.yaml)\\\"",
        "WITH_AUX_DISPATCHER=\\\"$(location test/config/with_aux_dispatcher.yaml)\\\"",
        "DISABLED_AUX_DISPATCHER=\\\"$(location test/config/disabled_aux_dispatcher.yaml)\\\"",
    ],
    tags = [
        "constrained_test",
        "skip_tsan",  # Hangs when run with TSan
    ],
    deps = [
        ":system_manager_lib",
        "//grace/interfaces/execution_monitor_msgs",
        "//grace/monitoring/event",
        "//grace/monitoring/event_registry",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "param/config.yaml",
        "test/test_system_manager_doc.cpp",
    ],
    visibility = ["//grace/monitoring/system_manager/doc:__pkg__"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
