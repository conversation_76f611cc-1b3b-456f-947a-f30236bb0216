load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "event_pkg",
    bin_executables = [":send_event"],
    cc_libraries = [
        ":event",
    ],
    description = "Package containing API for working with system events",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/apexutils:apexutils_pkg",
        "@apex//common/cpputils:cpputils_pkg",
        "@apex//grace/interfaces/process_manager_interfaces:process_manager_interfaces_pkg",
        "@apex//grace/monitoring/dispatcher_interfaces:dispatcher_interfaces_pkg",
        "@apex//grace/monitoring/event_registry:event_registry_pkg",
        "@apex//grace/monitoring/logging:logging_pkg",
        "@apex//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "@apex//grace/ros/rcutils:rcutils_pkg",
    ],
)

apex_cc_library(
    name = "event",
    srcs = glob(
        ["src/**"],
        exclude = [
            "src/ui/sender_cli_main.cpp",
        ],
    ),
    hdrs = glob(["include/**"]),
    applicable_licenses = ["//common/integrity:embedded_gold"],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/cpputils",
        "@apex//grace/interfaces/process_manager_interfaces",
        "@apex//grace/interfaces/process_manager_interfaces:process_manager_common",
        "@apex//grace/monitoring/dispatcher_interfaces",
        "@apex//grace/monitoring/dispatcher_interfaces:dispatcher_common",
        "@apex//grace/monitoring/event_registry",
        "@apex//grace/monitoring/logging",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "send_event",
    srcs = [
        "src/ui/cli_sender.cpp",
        "src/ui/cli_sender.hpp",
        "src/ui/sender_cli_main.cpp",
    ],
    deps = [
        ":event",
        "@apex//grace/ros/rcutils",
    ],
)

apex_cc_test(
    name = "event_tests",
    srcs = glob(["test/**"]),
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "exclusive",
    ],
    deps = [
        ":event",
        "@apex//common/apexutils",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "test/test_sender_doc.cpp",
    ],
    visibility = [":__subpackages__"],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
