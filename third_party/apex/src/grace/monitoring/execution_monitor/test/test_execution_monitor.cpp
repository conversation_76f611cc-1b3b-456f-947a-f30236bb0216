// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <map>
#include <memory>
#include <sstream>
#include <string>
#include <utility>
#include <vector>

#include <cpputils/fake_clock.hpp>
#include <execution_monitor/communication_stub.hpp>
#include <execution_monitor/execution_monitor.hpp>
#include <rclcpp/dynamic_waitset/waitset.hpp>

using namespace std::chrono_literals;
using namespace ::testing;  // NOLINT Usage in test is non-consequential.
namespace
{
namespace expectations = apex::execution_monitor::expectations;
using expectation_list = apex::execution_monitor::expectation_list;
using apex::execution_monitor::monitor_slot;
using apex::execution_monitor::task_identifier;

using wc_runtime_exp = expectations::wall_clock_runtime;
using cpu_runtime_exp = expectations::cpu_clock_runtime;
using act_window_exp = expectations::activations_per_window;
using act_delta_exp = expectations::activation_distance;

class test_execution_monitor : public Test
{
public:
  test_execution_monitor()
  {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
    m_node = std::make_unique<rclcpp::Node>(monitor_name);
    m_logger = std::make_unique<apex::logging::Logger<>>(m_node.get(), "TestLogger");
    m_old_err_buffer = std::cerr.rdbuf(m_err_stream.rdbuf());
  }

  ~test_execution_monitor() override
  {
    rclcpp::shutdown();
    std::cerr.rdbuf(m_old_err_buffer);
  }

  virtual monitor_slot::duration now_ns() = 0;

  std::size_t num_infractions(task_identifier::task_id_t task_id,
                              std::uint64_t expectation,
                              apex::optional<monitor_slot::duration> timestamp = {})
  {
    return m_communication.num_infractions(
      monitor_name, task_id, expectation, timestamp ? *timestamp : now_ns());
  }

protected:
  const std::string monitor_name{"test_simulated_clock_monitor"};
  std::unique_ptr<rclcpp::Node> m_node;
  apex::execution_monitor::communication_stub m_communication{};
  std::stringstream m_err_stream;
  std::streambuf * m_old_err_buffer;
  std::unique_ptr<apex::logging::Logger<>> m_logger;
};

class test_execution_monitor_real_clock : public test_execution_monitor
{
public:
  using execution_monitor = apex::execution_monitor::execution_monitor<apex::string32_t>;
  void create_monitor(const std::vector<expectation_list> & expectations)
  {
    m_monitor = std::make_unique<execution_monitor>(monitor_name, m_communication, m_logger.get());
    for (std::size_t i = 0U; i < expectations.size(); i++) {
      m_monitor->register_task(std::to_string(i), expectations[i]);
    }
    m_monitor->registrations_complete();
    m_monitor->issue();
  }

  monitor_slot::duration now_ns() override
  {
    return std::chrono::nanoseconds{std::chrono::steady_clock::now().time_since_epoch()};
  }

protected:
  std::unique_ptr<execution_monitor> m_monitor;
};

class test_execution_monitor_simulated_clock : public test_execution_monitor
{
public:
  using execution_monitor =
    apex::execution_monitor::details::execution_monitor<apex::string32_t, apex::FakeClock>;

  void create_monitor(const std::vector<expectation_list> & expectations)
  {
    m_monitor = std::make_unique<execution_monitor>(monitor_name, m_communication, m_logger.get());
    for (std::size_t i = 0U; i < expectations.size(); i++) {
      m_monitor->register_task(std::to_string(i), expectations[i]);
    }
    m_monitor->registrations_complete();
    m_monitor->issue();
  }

  void advance_clock(apex::FakeClock::duration d)
  {
    apex::FakeClock::advance(d);
  }

  monitor_slot::duration now_ns() override
  {
    return std::chrono::nanoseconds{apex::FakeClock::now().time_since_epoch()};
  }

protected:
  std::unique_ptr<execution_monitor> m_monitor;
};
}  // namespace

std::chrono::nanoseconds get_current_cpu_clock_time()
{
  ::timespec time{0, 0};
  if (clock_gettime(CLOCK_THREAD_CPUTIME_ID, &time) < 0) {
    throw apex::system_error(errno, "Failed to get per-clock CPU time");
  }
  return std::chrono::nanoseconds((apex::cast::safe_cast<uint64_t>(time.tv_sec) * 1'000'000'000) +
                                  apex::cast::safe_cast<uint64_t>(time.tv_nsec));
}
void waste_cpu_time(std::chrono::nanoseconds cpu_time_to_waste)
{
  const auto start = get_current_cpu_clock_time();
  while (get_current_cpu_clock_time() < start + cpu_time_to_waste) {
    // do nothing
  }
}

// --- Expectations consistency checks ---

TEST(test_execution_monitor, invalid_expectations_are_rejected)
{
  // Check that min > max is rejected
  EXPECT_THROW(wc_runtime_exp(5ms, 4ms), apex::invalid_argument);
  EXPECT_THROW(cpu_runtime_exp(5ms, 4ms), apex::invalid_argument);
  EXPECT_THROW(act_window_exp(3, 2, 50ms), apex::invalid_argument);
  EXPECT_THROW(act_delta_exp(3ms, 2ms), apex::invalid_argument);

  // Check that zero-length windows are rejected
  EXPECT_THROW(act_window_exp(2, 3, 0ms), apex::invalid_argument);
}
TEST(test_execution_monitor, valid_expectations_can_be_created)
{
  // Check that min == max is accepted
  EXPECT_NO_THROW(wc_runtime_exp(5ms, 5ms));
  EXPECT_NO_THROW(cpu_runtime_exp(5ms, 5ms));
  EXPECT_NO_THROW(act_window_exp(2, 2, 5ms));
  EXPECT_NO_THROW(act_delta_exp(3ms, 3ms));

  // Check that min < max is accepted
  EXPECT_NO_THROW(wc_runtime_exp(4ms, 5ms));
  EXPECT_NO_THROW(cpu_runtime_exp(4ms, 5ms));
  EXPECT_NO_THROW(act_window_exp(1, 2, 5ms));
  EXPECT_NO_THROW(act_delta_exp(2ms, 3ms));
}


// --- Monitored-scope utility ---

/// Mock class that counts and validates `task_starts` and `task_completes` calls
class MockMonitor
{
public:
  using task_t = std::size_t;
  void task_starts(std::size_t task)
  {
    if (instance_active(task)) {
      throw apex::runtime_error("Starting instance before previous instance completes");
    }
    instances_started[task]++;
    assert(instance_active(task));
  }
  void task_completes(std::size_t task)
  {
    if (!instance_active(task)) {
      throw apex::runtime_error("Completing instance even though there is no running instance");
    }
    instances_completed[task]++;
    assert(!instance_active(task));
  }
  std::uint32_t completed_instances(std::size_t task)
  {
    return instances_completed[task];
  }
  bool instance_active(std::size_t task)
  {
    return instances_completed[task] < instances_started[task];
  }

private:
  std::map<std::size_t, std::uint32_t> instances_started;
  std::map<std::size_t, std::uint32_t> instances_completed;
};

TEST(test_execution_monitor, monitored_scope_can_be_moved)
{
  MockMonitor monitor;
  // Create a monitored scope and move it to another scope. Then destroy both and use the
  // activation-count per window monitor to check that there was only one activation
  {
    auto monscope_0 = apex::execution_monitor::monitored_scope(monitor, 0);
    ASSERT_TRUE(monitor.instance_active(0));
    {
      auto monscope_1{std::move(monscope_0)};
    }
    // Destroying monscope_1 terminated the task
    ASSERT_EQ(monitor.completed_instances(0), 1);
    ASSERT_FALSE(monitor.instance_active(0));
  }

  // Destroying monscope_0 does not change anything about the instances
  ASSERT_EQ(monitor.completed_instances(0), 1);
  ASSERT_FALSE(monitor.instance_active(0));
}

TEST(test_execution_monitor, monitored_scope_can_be_move_assigned)
{
  MockMonitor monitor;
  // Create a monitored scope and move it to another scope. Then destroy both and use the
  // activation-count per window monitor to check that there was only one activation
  {
    auto monscope_0 = apex::execution_monitor::monitored_scope(monitor, 0);
    auto monscope_1 = apex::execution_monitor::monitored_scope(monitor, 1);
    ASSERT_TRUE(monitor.instance_active(0));
    ASSERT_TRUE(monitor.instance_active(1));
    monscope_1 = std::move(monscope_0);

    // Moving on top of monscope_1 terminated task 1
    ASSERT_TRUE(monitor.instance_active(0));
    ASSERT_EQ(monitor.completed_instances(1), 1);
    ASSERT_FALSE(monitor.instance_active(1));
  }

  // Destroying monscope_1 terminated task 0
  ASSERT_EQ(monitor.completed_instances(0), 1);
  ASSERT_FALSE(monitor.instance_active(0));
}

TEST(test_expectation_monitor, infraction_counter_saturates)
{
  auto test_monitor = [](auto expectation) {
    monitor_slot slot{0ns, 0, 0};

    const std::uint64_t inf_ceil = 1 << (sizeof(monitor_slot::infractions_counter_type) * CHAR_BIT);
    // The infraction counter increases by one for each call to `increment_infraction_counter`...
    ASSERT_EQ(static_cast<std::uint64_t>(slot.infractions_counter), 0);
    slot.increment_infraction_counter();
    // The increment function increases the counter ...
    ASSERT_EQ(static_cast<std::uint64_t>(slot.infractions_counter), 1);
    for (std::uint64_t i = 0; i < inf_ceil + 3; i++) {
      slot.increment_infraction_counter();
      // But never overflows
      ASSERT_GT(slot.infractions_counter, 0);
    }
    // And at the top it saturates
    const auto old_value = slot.infractions_counter.load();
    slot.increment_infraction_counter();
    ASSERT_EQ(old_value, slot.infractions_counter);
  };

  test_monitor(wc_runtime_exp(5ms));
  test_monitor(cpu_runtime_exp(5ms));
  test_monitor(act_window_exp(4, 8, 100ms));
  test_monitor(act_delta_exp(4ms, 5ms));
}
// --- Wall-clock-runtime monitor ---

TEST_F(test_execution_monitor_simulated_clock, test_wallclock_success)
{
  create_monitor({{wc_runtime_exp(50ms)}});
  m_monitor->task_starts("0");
  advance_clock(49ms);
  m_monitor->task_completes("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
}

TEST_F(test_execution_monitor_simulated_clock, test_wallclock_overrunning_monitored_scoped)
{
  create_monitor({{wc_runtime_exp(50ms)}});
  {
    auto s = apex::execution_monitor::monitored_scope(*m_monitor, "0");
    advance_clock(51ms);
  }
  ASSERT_EQ(num_infractions("0", 0), 1);
#ifndef APEX_CERT
  ASSERT_THAT(
    m_err_stream.str(),
    HasSubstr("Wall-clock runtime should have been below: 50000 us , measured: 51000 us"));
#endif
}

TEST_F(test_execution_monitor_simulated_clock, test_wallclock_overrunning)
{
  create_monitor({{wc_runtime_exp(50ms)}});
  m_monitor->task_starts("0");
  advance_clock(51ms);
  m_monitor->task_completes("0");
  ASSERT_EQ(num_infractions("0", 0), 1);
#ifndef APEX_CERT
  ASSERT_THAT(
    m_err_stream.str(),
    HasSubstr("Wall-clock runtime should have been below: 50000 us , measured: 51000 us"));
#endif
}

TEST_F(test_execution_monitor_simulated_clock, test_wallclock_underrunning)
{
  create_monitor({{wc_runtime_exp(50ms, 100ms)}});

  {
    auto s = apex::execution_monitor::monitored_scope(*m_monitor, "0");
    advance_clock(15ms);
  }
  ASSERT_EQ(num_infractions("0", 0), 1);
#ifndef APEX_CERT
  ASSERT_THAT(
    m_err_stream.str(),
    HasSubstr("Wall-clock runtime should have been above: 50000 us , measured: 15000 us"));
#endif
}

// --- CPU-clock monitor ---

TEST_F(test_execution_monitor_real_clock, test_cpuclock_success)
{
  create_monitor({{cpu_runtime_exp(500us)}});
  m_monitor->task_starts("0");
  m_monitor->task_completes("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
}

TEST_F(test_execution_monitor_real_clock, test_cpuclock_overrunning)
{
  create_monitor({{cpu_runtime_exp(500us)}});

  {
    auto s = apex::execution_monitor::monitored_scope(*m_monitor, "0");
    waste_cpu_time(500us);
  }
  ASSERT_EQ(num_infractions("0", 0), 1);
#ifndef APEX_CERT
  ASSERT_THAT(
    m_err_stream.str(),
    ContainsRegex(
      "CPU-clock runtime should have been between: 0 ns , and 500000 ns , measured: [0-9]+ ns"));
#endif
}

TEST_F(test_execution_monitor_real_clock, test_cpuclock_underrunning)
{
  create_monitor({{cpu_runtime_exp(500us, 1ms)}});

  {
    auto s = apex::execution_monitor::monitored_scope(*m_monitor, "0");
  }
  ASSERT_EQ(num_infractions("0", 0), 1);
#ifndef APEX_CERT
  ASSERT_THAT(m_err_stream.str(),
              ContainsRegex("CPU-clock runtime should have been between: 500000 ns ,"
                            " and 1000000 ns , measured: [0-9]+ ns"));
#endif
}

// --- activations-per-window monitor ---

TEST_F(test_execution_monitor_simulated_clock, test_act_per_window_success)
{
  create_monitor({{act_window_exp(2, 4, 50ms)}});
  // Try the lower limit of the acceptable window
  for (int i = 0; i < 2; i++) {
    m_monitor->task_starts("0");
    m_monitor->task_completes("0");
    ASSERT_EQ(num_infractions("0", 0), 0);
  }
  advance_clock(51ms);
  ASSERT_EQ(num_infractions("0", 0), 0);

  // In a new window, try the upper limit of the acceptable window
  for (int i = 0; i < 4; i++) {
    m_monitor->task_starts("0");
    m_monitor->task_completes("0");

    ASSERT_EQ(num_infractions("0", 0), 0);
  }
  advance_clock(51ms);
  ASSERT_EQ(num_infractions("0", 0), 0);
}

TEST_F(test_execution_monitor_simulated_clock, test_too_many_act_per_window)
{
  create_monitor({{act_window_exp(2, 4, 50ms)}});
  for (int i = 0; i < 4; i++) {
    m_monitor->task_starts("0");
    m_monitor->task_completes("0");
    ASSERT_EQ(num_infractions("0", 0), 0);
  }
  m_monitor->task_starts("0");
  ASSERT_EQ(num_infractions("0", 0), 1);
#ifndef APEX_CERT
  ASSERT_THAT(m_err_stream.str(),
              HasSubstr("Number of activations should have been below: 4 , observed: 5"));
#endif
}

TEST_F(test_execution_monitor_simulated_clock, test_too_few_act_per_window)
{
  create_monitor({{act_window_exp(2, 4, 50ms)}});
  m_monitor->task_starts("0");
  m_monitor->task_completes("0");

  ASSERT_EQ(num_infractions("0", 0), 0);

  advance_clock(51ms);
  ASSERT_EQ(num_infractions("0", 0), 1);
}

TEST_F(test_execution_monitor_simulated_clock, test_too_few_act_per_window_over_two_windows)
{
  create_monitor({{act_window_exp(2, 2, 100ms)}});

  advance_clock(50ms);
  m_monitor->task_starts("0");
  m_monitor->task_completes("0");

  advance_clock(99ms);
  m_monitor->task_starts("0");

  ASSERT_EQ(num_infractions("0", 0), 1);
}

TEST_F(test_execution_monitor_simulated_clock,
       too_few_act_per_window_report_message_if_detected_by_monitor_itself)
{
  create_monitor({{act_window_exp(2, 4, 50ms)}});
  m_monitor->task_starts("0");
  m_monitor->task_completes("0");

  ASSERT_EQ(num_infractions("0", 0), 0);

  advance_clock(51ms);
  m_monitor->task_starts("0");
  ASSERT_EQ(num_infractions("0", 0), 1);
#ifndef APEX_CERT
  ASSERT_THAT(m_err_stream.str(),
              HasSubstr("Number of activations should have been above: 2 , observed: 1"));
#endif
}

TEST_F(test_execution_monitor_simulated_clock,
       too_few_act_per_window_across_multiple_windows_report_message_if_detected_by_monitor_itself)
{
  create_monitor({{act_window_exp(2, 4, 50ms)}});
  m_monitor->task_starts("0");
  m_monitor->task_completes("0");

  ASSERT_EQ(num_infractions("0", 0), 0);

  advance_clock(151ms);
  m_monitor->task_starts("0");
  ASSERT_EQ(num_infractions("0", 0), 1);
#ifndef APEX_CERT
  ASSERT_THAT(m_err_stream.str(),
              HasSubstr("Number of activations should have been above: 2 "
                        "but 2 windows passed without any activation"));
#endif
}

TEST_F(test_execution_monitor_simulated_clock, test_act_per_window_no_activation)
{
  create_monitor({{act_window_exp(2, 4, 50ms)}});
  ASSERT_EQ(num_infractions("0", 0), 0);

  advance_clock(51ms);

  ASSERT_EQ(num_infractions("0", 0), 1);
}

TEST_F(test_execution_monitor_simulated_clock, test_act_per_window_skip_multiple_windows)
{
  create_monitor({{act_window_exp(1, 1, 50ms)}});

  advance_clock(51ms);
  ASSERT_EQ(num_infractions("0", 0), 1);
}

// --- activation-distance monitor ---

TEST_F(test_execution_monitor_simulated_clock, test_act_distance_success)
{
  create_monitor({{act_delta_exp(50ms, 100ms)}});
  m_monitor->task_starts("0");
  advance_clock(51ms);
  m_monitor->task_completes("0");
  m_monitor->task_starts("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
  advance_clock(99ms);
  m_monitor->task_completes("0");
  m_monitor->task_starts("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
}

TEST_F(test_execution_monitor_simulated_clock, test_act_distance_too_high)
{
  create_monitor({{act_delta_exp(0ms, 50ms)}});
  m_monitor->task_starts("0");
  ASSERT_EQ(num_infractions("0", 0), 0);

  advance_clock(51ms);
  ASSERT_EQ(num_infractions("0", 0), 1);
  // The infraction is detected by the expectation monitor only when the task starts again
  // In the real system, if the task does not start again, the infraction will be caught by the
  // backend
  m_monitor->task_starts("0");
#ifndef APEX_CERT
  ASSERT_THAT(
    m_err_stream.str(),
    HasSubstr(
      "Time distance between 2 activations should have been below: 50000 us , measured: 51000 us"));
#endif
}

TEST_F(test_execution_monitor_simulated_clock, test_act_distance_too_low)
{
  create_monitor({{act_delta_exp(50ms, 100ms)}});
  m_monitor->task_starts("0");
  m_monitor->task_completes("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
  m_monitor->task_starts("0");
  ASSERT_EQ(num_infractions("0", 0), 1);
#ifndef APEX_CERT
  ASSERT_THAT(
    m_err_stream.str(),
    HasSubstr(
      "Time distance between 2 activations should have been above: 50000 us , measured: 0 us"));
#endif
}

TEST_F(test_execution_monitor_simulated_clock, test_act_distance_no_activation)
{
  create_monitor({{act_delta_exp(5ms, 10ms)}});
  ASSERT_EQ(num_infractions("0", 0), 0);
  // Start the task right after creating the monitor, and ensure that the monitor
  // enforces a maximum distance of 10ms after start of the system
  advance_clock(11ms);
  ASSERT_EQ(num_infractions("0", 0), 1);
}

TEST_F(test_execution_monitor_simulated_clock,
       test_act_distance_no_minimal_time_until_first_activation)
{
  create_monitor({{act_delta_exp(100ms, 101ms)}});
  // Start the task right after creating the monitor, and ensure that the monitor
  // does not enforce a minimum distance of 100ms after start of the system
  m_monitor->task_starts("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
}

/* --- monitor lifecycle --- */

TEST_F(test_execution_monitor_simulated_clock, must_complete_registrations_before_issue)
{
  auto monitor = std::make_unique<execution_monitor>(monitor_name, m_communication);

  ASSERT_THROW(monitor->issue(), apex::logic_error);

  monitor->registrations_complete();
  ASSERT_NO_THROW(monitor->issue());
}

TEST_F(test_execution_monitor_simulated_clock, cannot_register_task_after_registrations_complete)
{
  auto monitor = std::make_unique<execution_monitor>(monitor_name, m_communication);
  monitor->registrations_complete();
  ASSERT_THROW(monitor->register_task(0, {act_delta_exp(100ms, 101ms)}), apex::logic_error);
}

TEST_F(test_execution_monitor_simulated_clock,
       is_registered_returns_true_after_and_before_registrations_complete)
{
  auto monitor = std::make_unique<execution_monitor>(monitor_name, m_communication);
  monitor->register_task("1", {});
  ASSERT_TRUE(monitor->is_registered("1"));
  ASSERT_FALSE(monitor->is_registered("2"));
  monitor->registrations_complete();
  ASSERT_TRUE(monitor->is_registered("1"));
  ASSERT_FALSE(monitor->is_registered("2"));
}

TEST_F(test_execution_monitor_simulated_clock,
       can_unregister_task_if_registrations_are_not_complete)
{
  auto monitor = std::make_unique<execution_monitor>(monitor_name, m_communication);
  ASSERT_NO_THROW(monitor->unregister_monitor_tasks());
}

TEST_F(test_execution_monitor_simulated_clock, is_registered_returns_false_after_unregistration)
{
  auto monitor = std::make_unique<execution_monitor>(monitor_name, m_communication);
  monitor->register_task("1", {});
  monitor->registrations_complete();
  ASSERT_TRUE(monitor->is_registered("1"));

  monitor->unregister_monitor_tasks();
  ASSERT_FALSE(monitor->is_registered("1"));
}

TEST_F(test_execution_monitor_simulated_clock, can_register_after_tasks_are_unregistered)
{
  auto monitor = std::make_unique<execution_monitor>(monitor_name, m_communication);
  monitor->register_task("1", {});
  monitor->registrations_complete();
  ASSERT_TRUE(monitor->is_registered("1"));

  monitor->unregister_monitor_tasks();
  ASSERT_FALSE(monitor->is_registered("1"));

  monitor->register_task("1", {});
  monitor->registrations_complete();
  ASSERT_TRUE(monitor->is_registered("1"));
}

TEST_F(test_execution_monitor_simulated_clock, unregisters_after_destruction)
{
  auto monitor = std::make_unique<execution_monitor>(monitor_name, m_communication);
  monitor->register_task("1", {});
  monitor->registrations_complete();
  ASSERT_TRUE(monitor->is_registered("1"));
  ASSERT_FALSE(m_communication.m_slots.empty());
  monitor.reset();
  ASSERT_TRUE(m_communication.m_slots.empty());
}

/* --- handling of multiple tasks and expectations --- */

TEST_F(test_execution_monitor_simulated_clock, multiple_tasks_may_overlap)
{
  create_monitor({{wc_runtime_exp(100ms)}, {wc_runtime_exp(50ms)}});

  m_monitor->task_starts("1");
  m_monitor->task_starts("0");

  m_monitor->task_completes("1");
  ASSERT_EQ(num_infractions("1", 0), 0);
  advance_clock(51ms);
  // The deadline of task 1 expired, but it completed in time. There should be no error
  ASSERT_EQ(num_infractions("1", 0), 0);

  advance_clock(50ms);
  // Now the deadline of task 0 expired, which should have triggered an error
  // Check that the task can still be completed
  m_monitor->task_completes("0");
  ASSERT_EQ(num_infractions("0", 0), 1);
#ifndef APEX_CERT
  ASSERT_THAT(
    m_err_stream.str(),
    HasSubstr("Wall-clock runtime should have been below: 100000 us , measured: 101000 us"));
#endif
  ASSERT_EQ(num_infractions("1", 0), 0);
}

// TODO(lander.usategui): Enable this when 15580 is fixed
#ifndef QNX
TEST_F(test_execution_monitor_simulated_clock,
       multiple_expectations_of_the_same_kind_trigger_independently)
{
  create_monitor({{wc_runtime_exp(50ms), wc_runtime_exp(100ms)}});

  m_monitor->task_starts("0");

  advance_clock(51ms);
  // The shorter expectation is unsatisfied

  advance_clock(50ms);
  // And now the second expectation has triggered
  m_monitor->task_completes("0");

  ASSERT_EQ(num_infractions("0", 0), 1);
  #ifndef APEX_CERT
  ASSERT_THAT(
    m_err_stream.str(),
    HasSubstr("Wall-clock runtime should have been below: 50000 us , measured: 101000 us"));
  #endif
  ASSERT_EQ(num_infractions("0", 1), 1);
  #ifndef APEX_CERT
  ASSERT_THAT(
    m_err_stream.str(),
    HasSubstr("Wall-clock runtime should have been below: 100000 us , measured: 101000 us"));
  #endif
}
#endif  // QNX

TEST_F(test_execution_monitor_simulated_clock, undefined_task)
{
  create_monitor({{}, {}, {}});
  for (std::uint32_t i = 0; i < 3; i++) {
    ASSERT_NO_THROW(m_monitor->task_starts(std::to_string(i)));
  }
  ASSERT_THROW(m_monitor->task_starts("3"), apex::invalid_argument);
}

TEST_F(test_execution_monitor_simulated_clock, infinite_max_wallclock_time)
{
  // check that the monitor works correctly if an infinite runtime is configured
  create_monitor({{wc_runtime_exp(wc_runtime_exp::infinity)}});
  m_monitor->task_starts("0");
  advance_clock(10ms);
  ASSERT_EQ(num_infractions("0", 0), 0);
  m_monitor->task_completes("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
}
TEST_F(test_execution_monitor_simulated_clock, infinite_max_cpu_time)
{
  // check that the monitor works correctly if an infinite runtime is configured
  create_monitor({{cpu_runtime_exp(cpu_runtime_exp::infinity)}});
  m_monitor->task_starts("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
  m_monitor->task_completes("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
}
TEST_F(test_execution_monitor_simulated_clock, infinite_activations_in_window)
{
  // check that the monitor works correctly if an infinite number of activations is accepted
  // (really, this just tests that there is no integer overflow in the monitor)
  create_monitor({{act_window_exp(0, act_window_exp::infinity, 50ms)}});
  m_monitor->task_starts("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
  m_monitor->task_completes("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
}

TEST_F(test_execution_monitor_simulated_clock, infinite_activation_delta)
{
  // check that the monitor works correctly if an infinite number of activations is accepted
  // (really, this just tests that there is no integer overflow in the monitor)
  create_monitor({{act_delta_exp(0ms, act_delta_exp::infinity)}});
  m_monitor->task_starts("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
  advance_clock(10ms);
  m_monitor->task_completes("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
}

TEST_F(test_execution_monitor_simulated_clock, window_monitor_can_jump_multiple_windows_at_once)
{
  create_monitor({{act_window_exp(0, 1, 10ms)}});

  // Jump across multiple windows, towards the end of one window
  advance_clock(39ms);
  m_monitor->task_starts("0");
  m_monitor->task_completes("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
  advance_clock(1ms);  // Match up with the window boundary again

  advance_clock(30ms);  // Jump by multiple windows, exactly onto the border between two windows
  m_monitor->task_starts("0");
  m_monitor->task_completes("0");
  ASSERT_EQ(num_infractions("0", 0), 0);
}

TEST_F(test_execution_monitor_simulated_clock,
       window_monitor_flags_min_infraction_when_jumping_multiple_windows_at_once)
{
  create_monitor({{act_window_exp(1, 1, 10ms)}});
  m_monitor->task_starts("0");
  m_monitor->task_completes("0");
  advance_clock(40ms);  // jumps 4 windows

  ASSERT_EQ(num_infractions("0", 0), 1);
}

TEST(test_communicator, register_different_tasks_in_same_segment)
{
  apex::execution_monitor::communication_stub communication;
  const auto monitor_name = "fake_monitor";
  const auto expectations = expectation_list{{wc_runtime_exp(100ms)}, {wc_runtime_exp(50ms)}};
  communication.register_tasks(monitor_name, {{"42", expectations}});
  EXPECT_NO_THROW(communication.register_tasks(monitor_name, {{"43", expectations}}));
}

TEST_F(test_execution_monitor_real_clock, monitor_detects_wall_clock_overrun)
{
  create_monitor({{wc_runtime_exp(10ms)}});
  m_monitor->task_starts("0");
  std::this_thread::sleep_for(500ms);  // Use a large margin, which works even on a loaded CI
  ASSERT_EQ(num_infractions("0", 0), 1);
}

TEST_F(test_execution_monitor_real_clock, monitor_detects_activation_delta_too_high)
{
  create_monitor({{act_delta_exp(0ms, 10ms)}});
  std::this_thread::sleep_for(500ms);  // Use a large margin, which works even on a loaded CI
  ASSERT_EQ(num_infractions("0", 0), 1);
}

TEST_F(test_execution_monitor_real_clock, monitor_detects_too_few_activations_per_window)
{
  create_monitor({{act_window_exp(1, 5, 10ms)}});
  std::this_thread::sleep_for(500ms);  // Use a large margin, which works even on a loaded CI
  ASSERT_EQ(num_infractions("0", 0), 1);
}
