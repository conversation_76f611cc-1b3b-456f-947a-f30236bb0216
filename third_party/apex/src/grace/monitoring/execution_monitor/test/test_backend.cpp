// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>

#include <string>
#include <vector>

#include "ida/common/test_util.hpp"
#include <execution_monitor/backend.hpp>
#include <execution_monitor/details/utils.hpp>
#include <execution_monitor/monitor_slot.hpp>
#include <execution_monitor/task_identifier.hpp>

using namespace apex;
using namespace apex::execution_monitor;
using namespace std::chrono_literals;

namespace
{
std::uint64_t last_slot(const backend::shm_range & r)
{
  if (r.len == 0U) {
    throw invalid_argument{"given range has length 0"};
  }
  return r.first_slot + r.len - 1U;
}

bool intersect(const backend::shm_range & a, const backend::shm_range & b)
{
  if (a.filename != b.filename) {
    return false;
  }

  return (b.first_slot <= last_slot(a)) && (a.first_slot <= last_slot(b));
}

class backend_test : public ::testing::Test, public test::AllocatorMixin
{
public:
  void SetUp() override
  {
    test::force_ok(base::create_in_place(m_mem_acquisition, allocator));
  }

  plexus::ShmemAcquisition & shmem_acquisition()
  {
    return *m_mem_acquisition;
  }

private:
  base::optional<plexus::ShmemAcquisition> m_mem_acquisition;
};

}  // namespace


TEST_F(backend_test, register_task_succeeds_if_slots_are_available)
{
  backend backend{1, 1};
  const task_identifier task1{200, "", "300"};
  auto range = backend.register_execution_monitor_task(task1, 10U);
  ASSERT_EQ(range.len, 10U);

  // NB: Test we can open the segment read-write, same as what the communication class does
  using execution_monitor::details::to_iox_name;
  auto shmem = test::force_ok(shmem_acquisition().open_typed_segment_read_write<monitor_slot>(
    to_iox_name(range.filename.data()), SLOTS_PER_SEGMENT));

  // Check that the fields are initialized properly
  for (unsigned i = 0; i < range.len; i++) {
    const auto & slot = shmem.elements()[i];
    EXPECT_EQ(slot.infractions_counter, 0);
    EXPECT_EQ(slot.deadline.load(), monitor_slot::infinity);
  }
}

TEST_F(backend_test, register_task_fails_if_not_enough_tasks_are_available)
{
  const task_identifier task1{200, "", "300"};
  const task_identifier task2{200, "", "301"};

  backend backend{1, 1};
  ASSERT_NO_THROW(backend.register_execution_monitor_task(task1, 1U));
  ASSERT_THROW(backend.register_execution_monitor_task(task2, 1U), apex::bad_alloc);
}

TEST_F(backend_test, register_task_fails_if_not_enough_segments_are_available)
{
  const task_identifier task1{200, "", "300"};
  const task_identifier task2{200, "other-monitor", "301"};

  backend backend{1, 2};
  ASSERT_NO_THROW(backend.register_execution_monitor_task(task1, 1U));
  ASSERT_THROW(backend.register_execution_monitor_task(task2, 1U), apex::bad_alloc);
}

TEST_F(backend_test, can_register_max_monitor_with_max_tasks_with_max_expectations)
{
  backend backend{50, 100};
  for (auto m = 0U; m < 50U; m++) {
    for (auto t = 0U; t < 2U; t++) {
      ASSERT_NO_THROW(backend.register_execution_monitor_task(
        task_identifier{1, std::to_string(m), std::to_string(t)}, 64U));
    }
  }
  // Allocating one task for an existing monitor no longer works
  ASSERT_THROW(backend.register_execution_monitor_task(task_identifier{1, "0", "99999"}, 1U),
               apex::bad_alloc);
  // Allocating a new monitor no longer works either
  ASSERT_THROW(backend.register_execution_monitor_task(task_identifier{1, "new monitor", "0"}, 1U),
               apex::bad_alloc);
}

TEST_F(backend_test, register_task_fails_if_not_enough_slots_are_available)
{
  const task_identifier task{200, "", "300"};

  backend backend{1, 1};
  ASSERT_THROW(backend.register_execution_monitor_task(task, 100'000U), apex::bad_alloc);

  // Check that the segment has not been allocated
  ASSERT_NO_THROW(backend.register_execution_monitor_task(task, 1U));
}

TEST_F(backend_test, register_task_succeeds_at_slots_per_segment_but_fails_above)
{
  backend backend{1, SLOTS_PER_SEGMENT};
  std::vector<task_identifier> tasks;
  ASSERT_TRUE(SLOTS_PER_SEGMENT % 10 == 0)
    << "SLOTS_PER_SEGMENT is a not multiple of 10, adjust the test";
  for (std::size_t i = 0; i < (SLOTS_PER_SEGMENT / 10) - 1; ++i) {
    ASSERT_NO_THROW(backend.register_execution_monitor_task({200, "", std::to_string(i)}, 10));
  }

  // Check that it also fails for the smallest possible failing request
  ASSERT_THROW(backend.register_execution_monitor_task({200, "", "9999"}, 11), apex::bad_alloc);
  ASSERT_NO_THROW(backend.register_execution_monitor_task({200, "", "9999"}, 10));
}

TEST_F(backend_test, register_task_is_robust_against_overflow)
{
  backend backend{1, 1};
  const task_identifier task{200, "", "301"};
  // Try a few max-int values, to ensure internal operations don't somehow wrap around
  ASSERT_THROW(
    backend.register_execution_monitor_task(task, std::numeric_limits<std::uint32_t>::max()),
    apex::bad_alloc);
}

TEST_F(backend_test, each_task_can_be_registered_only_once)
{
  const task_identifier task{200, "", "300"};

  backend backend{1, 2};
  ASSERT_NO_THROW(backend.register_execution_monitor_task(task, 1U));
  ASSERT_THROW(backend.register_execution_monitor_task(task, 1U),
               apex::execution_monitor::backend_error);
}

TEST_F(backend_test, multiple_registrations_do_not_overlap)
{
  task_identifier task(201, "", "201");
  task_identifier task_same_pid(201, "", "202");
  task_identifier task_same_task_id(202, "", "201");

  backend backend{2, 3};
  const std::uint32_t num_slots = SLOTS_PER_SEGMENT;
  const auto reply = backend.register_execution_monitor_task(task, num_slots / 3U);
  const auto reply2 = backend.register_execution_monitor_task(task_same_pid, num_slots / 3U);
  const auto reply3 = backend.register_execution_monitor_task(task_same_task_id, num_slots / 3U);

  ASSERT_FALSE(intersect(reply, reply2));
  ASSERT_FALSE(intersect(reply, reply3));
  ASSERT_FALSE(intersect(reply2, reply3));
}

TEST_F(backend_test, multiple_registrations_for_same_monitor_share_a_segment)
{
  task_identifier task1(201, "", "201");
  task_identifier task2(201, "", "202");

  backend backend{1, 2};
  const auto reply1 = backend.register_execution_monitor_task(task1, 10U);
  const auto reply2 = backend.register_execution_monitor_task(task2, 4U);
  ASSERT_EQ(reply1.filename, reply2.filename);
  // The allocated areas must not overlap
  ASSERT_FALSE(intersect(reply1, reply2));

  // Test querying via the segment iterator
  ASSERT_NE(backend.segments_begin(), backend.segments_end());
  {
    const auto [task, offset] = backend.segments_begin().task_and_offset_of(5);
    EXPECT_EQ(task, task1);
    EXPECT_EQ(offset, 5);
  }
  {
    const auto [task, offset] = backend.segments_begin().task_and_offset_of(10);
    EXPECT_EQ(task, task2);
    EXPECT_EQ(offset, 0);
  }
  {
    const auto [task, offset] = backend.segments_begin().task_and_offset_of(12);
    EXPECT_EQ(task, task2);
    EXPECT_EQ(offset, 2);
  }
}

TEST_F(backend_test, registrations_for_tasks_in_different_pids_do_not_share_a_segment)
{
  task_identifier task1(201, "", "201");
  task_identifier task2(202, "", "202");

  backend backend{2, 2};
  const auto reply = backend.register_execution_monitor_task(task1, 10U);
  const auto reply2 = backend.register_execution_monitor_task(task2, 4U);
  ASSERT_NE(reply.filename, reply2.filename);
}

TEST_F(backend_test, different_execution_monitors_may_use_the_same_id)
{
  task_identifier task1(201, "foo", "201");
  task_identifier task2(201, "bar", "202");

  backend backend{2, 2};
  ASSERT_NO_THROW(backend.register_execution_monitor_task(task1, 1U));
  ASSERT_NO_THROW(backend.register_execution_monitor_task(task2, 1U));
}

TEST_F(backend_test, can_reregister_task_after_unregistering_monitor)
{
  task_identifier task1(201, "foo", "201");
  task_identifier task2(201, "foo", "202");
  backend backend{1, 2};
  backend.register_execution_monitor_task(task1, 1U);
  backend.register_execution_monitor_task(task2, 1U);
  // Cannot re-register the tasks, as they are already registered
  ASSERT_THROW(backend.register_execution_monitor_task(task1, 1U), backend_error);
  ASSERT_THROW(backend.register_execution_monitor_task(task2, 1U), backend_error);

  backend.unregister_execution_monitor(task1.pid, task1.monitor_name);
  // But after the monitor has been un-registered, both tasks can be registered again.
  ASSERT_NO_THROW(backend.register_execution_monitor_task(task2, 1U));
  ASSERT_NO_THROW(backend.register_execution_monitor_task(task1, 1U));
}

TEST_F(backend_test, unregistering_monitor_frees_the_segment)
{
  task_identifier task1(201, "foo", "201");
  task_identifier task2(201, "bar", "201");
  backend backend{1, 1};
  backend.register_execution_monitor_task(task1, 1U);
  // There is no space for a second task
  ASSERT_THROW(backend.register_execution_monitor_task(task2, 1U), apex::bad_alloc);
  backend.unregister_execution_monitor(task1.pid, task1.monitor_name);
  // But after the first task was unregistered, there is now space for the second task
  ASSERT_NO_THROW(backend.register_execution_monitor_task(task2, 1U));

  // But now re-registering the first task fails (that is, the monitor does not think that it
  // can still use the existing segment for task 1
  ASSERT_THROW(backend.register_execution_monitor_task(task1, 1U), apex::bad_alloc);
}


TEST_F(backend_test, inspecting_deadline_in_the_past)
{
  monitor_slot slot{.deadline = 0ns, .infractions_counter = 0};

  const auto infractions = slot.inspect_deadline(42ns);
  ASSERT_EQ(infractions.counter, 1);
  ASSERT_EQ(infractions.backend_overrun, 42ns);
}

TEST_F(backend_test, inspecting_deadline_not_in_the_past_but_infraction_signaled)
{
  const auto now_ns = 42ns;

  monitor_slot slot{.deadline = now_ns + 111ns, .infractions_counter = 1};

  const auto infractions = slot.inspect_deadline(now_ns);
  ASSERT_EQ(infractions.counter, 1);
  ASSERT_LE(infractions.backend_overrun, 0ns);
}

TEST_F(backend_test, only_one_backend_can_exist_at_any_time)
{
  {
    backend b1{1, 1};
    try {
      backend b2{1, 1};
      FAIL() << "Creating a second competing backend on the same system should fail";
    } catch (const backend_error &) {
    }
  }

  // But after b1 is gone, creating a new one succeeds
  backend b2{1, 1};
  // Creating with different prefix is also fine
  backend b3{1, 1, "salt"};
}

TEST_F(backend_test, backends_creates_local_backend_path_while_it_lives)
{
  const auto local_backend_segment_exists = [this](const iox::FileName & path) -> bool {
    return shmem_acquisition().open_typed_segment_read_only<std::byte>(path).has_value();
  };

  auto b = std::make_unique<backend>(1, 1);
  const auto backend_token_segment_name = b->get_backend_token_segment_name();
  ASSERT_TRUE(local_backend_segment_exists(backend_token_segment_name));
  b.reset();
  ASSERT_FALSE(local_backend_segment_exists(backend_token_segment_name));
}

TEST_F(backend_test, backends_have_different_ensure_local_backend_paths)
{
  using FileNameStringType = std::decay_t<decltype(std::declval<iox::FileName>().as_string())>;
  FileNameStringType backend1_name;
  {
    backend backend{1, 1};
    backend1_name = backend.get_backend_token_segment_name().as_string();
  }
  backend backend2{1, 1};
  ASSERT_NE(backend1_name, backend2.get_backend_token_segment_name().as_string());
}
