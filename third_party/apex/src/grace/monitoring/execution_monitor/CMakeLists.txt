# Copyright 2022 Apex.AI, Inc.
# All rights reserved.

cmake_minimum_required(VERSION 3.5)

project(execution_monitor)
set(CMAKE_CXX_STANDARD 17)

find_package(ament_cmake_auto REQUIRED)
find_package(apex_cmake REQUIRED)
ament_auto_find_build_dependencies()

set(LIB_SOURCES
    src/backend.cpp
    src/execution_monitor.cpp
    src/monitor_slot.cpp
    src/communication.cpp
    src/utils.cpp
    include/execution_monitor/details/expectation_monitors.hpp
    include/execution_monitor/expectation_types.hpp
    include/execution_monitor/backend.hpp
    include/execution_monitor/expectations.hpp
    include/execution_monitor/execution_monitor.hpp
    include/execution_monitor/monitor_slot.hpp
    include/execution_monitor/task_identifier.hpp
    include/execution_monitor/communication_stub.hpp
    include/execution_monitor/visibility.hpp
    include/execution_monitor/details/utils.hpp
)

ament_auto_add_library(${PROJECT_NAME} ${LIB_SOURCES})

apex_set_compile_options(${PROJECT_NAME})

# Testing
if(BUILD_TESTING)
    find_package(ament_cmake_gmock REQUIRED)
    function(execution_monitor_test target)
        set(sources ${ARGN})
        ament_add_gmock(${target} ${sources})
        apex_set_compile_options(${target})
        # Disable various warnings that gmock code runs afoul of
        target_compile_options(${target} PRIVATE -Wno-suggest-override -Wno-sign-conversion -Wno-sign-compare)
        target_link_libraries(${target} ${PROJECT_NAME})
        if(DEFINED ENV{CI})
            target_compile_definitions(${target} PRIVATE UNDER_CI="TRUE")
        endif()
        if(NOT APEX_CERT)
            target_compile_definitions(${target} PRIVATE APEX_PRINT_LOGS_TO_TERMINAL)
        endif()
    endfunction()

    execution_monitor_test(test_execution_monitor
        test/test_execution_monitor.cpp
        test/test_backend.cpp)
endif()

ament_auto_package()
