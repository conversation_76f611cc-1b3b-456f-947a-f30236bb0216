/// \copyright Copyright 2022 Apex.AI, Inc.
/// All rights reserved.

#include "execution_monitor/monitor_slot.hpp"

#include <iostream>
#include <limits>

namespace apex::execution_monitor
{

void monitor_slot::increment_infraction_counter() noexcept
{
  // Since the clamp is a power of two, the saturation can be implemented with a simple AND.
  constexpr auto clamp = 1U << (std::numeric_limits<infractions_counter_type>::digits - 1);
  static_assert((clamp & (clamp - 1)) == 0U, "max_infraction_counter_value is not a power of two");

  // Increment infractions_counter, saturating at `clamp`.
  // This works for the following reasons
  // (1) There are only two parties modifying the counter: this expectation monitor, which
  //     may increment the counter, and the backend, which zeroes the counter.
  //     As a result, `infractions_counter` is at most `clamp+1` and cannot overflow.
  // (2) If `infractions_counter` has not been zeroed out since the increment, the AND
  //     operation reduces the counter back to the clamp by clearing all lower-order bits
  // (3) If the backend zeroed `infractions_counter` in the meantime, the AND is a no-op
  auto infractions = infractions_counter.fetch_add(1U, std::memory_order_relaxed);
  if (infractions >= clamp) {
    (void)infractions_counter.fetch_and(clamp, std::memory_order_relaxed);
  }
}

monitor_slot::duration monitor_slot::check_and_update_deadline(
  duration new_value, duration now, std::optional<std::int64_t> deviation_override)
{
  // Load the deadline, using `acquire` to prevent re-ordering of the load from
  // `clock_type::now()` before the deadline exchange. Note that the acquisition is only
  // preventing re-ordering and does not involve any thread synchronization. There is therefore
  // no matching `release`-store to the deadline.
  const auto old_value = deadline.exchange(new_value, std::memory_order_acquire);
  const auto overrun = now - old_value;
  if (now >= old_value) {
    // The old deadline has expired; flag an error for the backend
    (void)deviation.exchange(
      deviation_override == std::nullopt ? overrun.count() : *deviation_override,
      std::memory_order_acquire);
    increment_infraction_counter();
  }
  return overrun;
}

void monitor_slot::set_deviation(int64_t new_value)
{
  (void)deviation.exchange(new_value, std::memory_order_acquire);
  increment_infraction_counter();
}

infractions monitor_slot::inspect_deadline(duration now)
{
  infractions infractions;
  infractions.expectation_type = expectation_type_id;
  infractions.counter =
    static_cast<std::uint32_t>(infractions_counter.exchange(0U, std::memory_order_relaxed));
  if (infractions.counter != 0) {
    infractions.deviation = deviation.exchange(0, std::memory_order_relaxed);
  }

  // Fetch the current deadline
  if (auto current_dl = deadline.load(std::memory_order_relaxed); current_dl <= now) {
    // If the deadline is in the past, exchange the value in the slot with infinity to mark that
    // we handled it. But do not overwrite a different value that the execution_monitor might have
    // already put in place.
    if (deadline.compare_exchange_strong(current_dl, infinity, std::memory_order_relaxed)) {
      infractions.counter++;
    }
    infractions.backend_overrun = std::chrono::nanoseconds{now - current_dl};
  }

  return infractions;
}

void monitor_slot::reset() noexcept
{
  deadline = monitor_slot::infinity;
  deviation = 0;
  infractions_counter = 0;
}

}  // namespace apex::execution_monitor
