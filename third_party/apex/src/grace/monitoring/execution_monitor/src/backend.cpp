/// \copyright Copyright 2022 Apex.AI, Inc.
/// All rights reserved.

#include "execution_monitor/backend.hpp"

#include <iomanip>
#include <limits>
#include <memory>
#include <random>
#include <sstream>
#include <string>
#include <utility>

#include "allocator/traits/map.hpp"
#include "base/expected.hpp"
#include "cpputils/common_exceptions.hpp"
#include "execution_monitor/details/utils.hpp"

namespace apex::execution_monitor
{

namespace
{

/// \brief Exception related to the handling of shared-memory.
class EXECUTION_MONITOR_PUBLIC shm_error : public apex::runtime_error
{
  using apex::runtime_error::runtime_error;
};

iox::FileName create_randomized_segment_name()
{
  std::random_device rd;
  std::stringstream ss;
  // Create a random name consisting of 2 `unsigned int` numbers
  ss << "exec-monitor-token-";
  for (std::int8_t i = 0; i < 2; i++) {
    ss << std::hex
       /*
         AXIVION Next Line MisraC++2023-4.1.3: Reason: Code Quality (Functional suitability),
         Justification: This value definitely fits into an int
         */
       << std::setw(2 * sizeof(std::random_device::result_type)) << std::setfill('0') << rd();
  }
  return details::to_iox_name(ss.str().c_str());
}

/// \brief Returns the value contained in the `expected` object or throws `shm_error`
/// \param expected The expected object
/// \param explanation A text explanation for the exception
/// \note This is conceptually equivalent to expected.or_else(throw shm_error(...)),
///       but avoids the problem that `or_else` is marked `noexcept`.
template <typename T, typename Err>
T value_or_exception(base::expected<T, Err> expected, const char * explanation)
{
  if (!expected) {
    throw shm_error(explanation, static_cast<std::int32_t>(expected.error()));
  }
  return std::move(expected.value());
}

}  // namespace


backend::backend(const std::size_t max_monitors,
                 const std::size_t max_tasks,
                 const std::string & unique_prefix /* = ""*/)
: m_backend_token_segment_name{create_randomized_segment_name()}, m_segments(max_monitors)
{
  const iox::posix::FileLock::FileName_t lock_name{
    base::panic_on_overflow_t{},
    /*
     AXIVION Next Codeline MisraC++2023-6.8.1: Reason: Code Quality (Functional suitability),
     Justification: The string from c_str() of the temporary variable is copied into
     fixed_string instance. The function is completely working.
     */
    ("exec-monitor-lock-" + unique_prefix).c_str()};
  auto exp_lock = iox::posix::FileLockBuilder{}
                    .name(lock_name)
                    .permission(iox::perms::owner_read | iox::perms::owner_write)
                    .create();

  if (!exp_lock) {
    throw backend_error{
      "failed to create lock file. Is another backend instance running?",
      APEX_ERROR_FROM_ENUM(iox::posix::FileLockError, exp_lock.error()).message().c_str()};
  }

  if (auto result = base::create_in_place(m_allocator); result.has_error()) {
    const auto error = APEX_ERROR_FROM_ENUM(base::basic_allocator_error, result.error());
    throw backend_error{"Failed to create allocator.", error.message().c_str()};
  }

  if (auto result = base::create_in_place(m_mem_acquisition, *m_allocator); result.has_error()) {
    throw backend_error{"Failed to create resource acquisition.", result.error().message().c_str()};
  }

  if (auto segment_result =
        m_mem_acquisition->create_typed_segment<std::byte>(1, m_backend_token_segment_name);
      segment_result.has_value()) {
    m_backend_token_segment = std::move(segment_result.value());
  } else {
    auto error_descr = base::error_to_string(segment_result.error());
    throw backend_error{std::string{"Failed to create backend token shared-memory segment '"} +
                          m_backend_token_segment_name.as_string().c_str() + "'.",
                        error_descr.c_str()};
  }

  // Pre-allocate enough entries for the map.
  m_monitor_to_segment_memres.add_buckets_for<decltype(m_monitor_to_segment)>(max_monitors);
  m_allocated_ranges_memres.add_buckets_for<decltype(m_allocated_ranges)>(max_tasks);

  const auto get_segment_name = [&lock_name](std::size_t index) {
    return std::string{"execution-monitor_"} + lock_name.c_str() + "_" + std::to_string(index);
  };

  // Pre-create segments for all expected monitors
  for (std::size_t i = 0; i < max_monitors; ++i) {
    const auto segment_name = get_segment_name(i);
    auto segment_result = m_mem_acquisition->create_typed_segment<monitor_slot>(
      SLOTS_PER_SEGMENT, details::to_iox_name(segment_name.c_str()));
    if (segment_result.has_error()) {
      auto error_descr = base::error_to_string(segment_result.error());
      throw backend_error{
        "Failed to create monitor's shared-memory segment '" + segment_name + "'.",
        error_descr.c_str()};
    }

    m_segments.emplace_back(std::move(segment_result.value()));
  }
}

backend::~backend() = default;

backend::data_slots::data_slots(MonitorDataSegment data_segment)
: m_segment{std::move(data_segment)}, m_active_slots{m_segment.elements().first(0)}
{
}

const iox::FileName & backend::data_slots::segment_name() const noexcept
{
  return m_segment.name();
}

const backend::SlotsSpan backend::data_slots::active_slots() const noexcept
{
  return m_active_slots;
}

backend::SlotsSpan backend::data_slots::active_slots() noexcept
{
  return m_active_slots;
}

std::size_t backend::data_slots::num_free_slots() const noexcept
{
  return m_segment.elements().size() - m_active_slots.size();
}

backend::SlotsSpan backend::data_slots::allocate_slots(const std::size_t num_slots)
{
  if (num_slots > num_free_slots()) {
    throw apex::bad_alloc{"Not enought free slots to allocate from"};
  }

  // The request is valid. Update the books and initialize the allocated entries
  m_active_slots = m_segment.elements().first(m_active_slots.size() + num_slots);
  auto result = m_active_slots.last(num_slots);
  for (auto & slot : result) {
    slot.reset();
  }

  return result;
}

void backend::data_slots::deploy()
{
  assert(m_active_slots.size() == 0U);
  m_in_use = true;
}

void backend::data_slots::release()
{
  m_active_slots = m_segment.elements().first(0);
  m_in_use = false;
}

backend::data_slots * backend::allocate_segment_from_pool()
{
  // Allocation is rare, so it is not worth the extra memory to keep a free-list around.
  for (auto & slots : m_segments) {
    if (!slots.in_use()) {
      slots.deploy();
      return &slots;
    }
  }
  throw apex::bad_alloc{"No free segment available"};
}

// Do not want the method to be static
void backend::return_segment_to_pool(data_slots * slots)
{
  // TODO(tobias.stark): Instead of the manual allocate/return calls here, a custom smart pointer
  //                     that returns the slots to the shared pool upon deletion would be better
  slots->release();
  (void)m_allocated_ranges.erase(slots);
}

backend::shm_range backend::register_execution_monitor_task(task_identifier task,
                                                            std::uint32_t num_slots)
{
  const auto monitor_id = std::make_pair(task.pid, task.monitor_name);
  data_slots * allocated_segment_ptr = nullptr;
  auto segment_it = m_monitor_to_segment.find(monitor_id);
  try {
    if (segment_it == m_monitor_to_segment.end()) {
      allocated_segment_ptr = allocate_segment_from_pool();
      segment_it = m_monitor_to_segment.emplace(monitor_id, allocated_segment_ptr).first;
    } else {
      auto eq_range = m_allocated_ranges.equal_range(segment_it->second);
      if (std::find_if(eq_range.first, eq_range.second, [id = task.task_id](const auto & entry) {
            const auto & id_range_pair = entry.second;
            return id_range_pair.first == id;
          }) != eq_range.second) {
        throw backend_error("task is already registered");
      }
    }
    data_slots & segment = *segment_it->second;

    backend::shm_range response{};
    response.first_slot = segment.active_slots().size();
    response.filename = apex::string_view{segment.segment_name().as_string().c_str(),
                                          segment.segment_name().as_string().size()};
    segment.allocate_slots(num_slots);
    response.len = num_slots;

    (void)m_allocated_ranges.emplace(std::piecewise_construct,
                                     std::forward_as_tuple(segment_it->second),
                                     std::forward_as_tuple(task.task_id, response));
    return response;
    // Intended catch-all and re-throw
  } catch (...) {
    if (allocated_segment_ptr != nullptr) {
      return_segment_to_pool(allocated_segment_ptr);
      if (segment_it != m_monitor_to_segment.end()) {
        (void)m_monitor_to_segment.erase(segment_it);
      }
    }
    throw;
  }
}

void backend::unregister_execution_monitor(std::int32_t pid, const apex::string256_t & monitor)
{
  auto segment_it = m_monitor_to_segment.find({pid, monitor});
  if (segment_it != m_monitor_to_segment.end()) {
    return_segment_to_pool(segment_it->second);
    (void)m_monitor_to_segment.erase(segment_it);
  }
}

backend::data_slots & backend::shmem_segment_iterator::operator*() const noexcept
{
  return *(m_iterator->second);
}

std::pair<const task_identifier, std::uint32_t> backend::shmem_segment_iterator::task_and_offset_of(
  std::uint32_t index) const
{
  if (index >= m_iterator->second->active_slots().size()) {
    throw apex::out_of_range{"the index is invalid for the segment"};
  }

  const auto & pid = m_iterator->first.first;
  const auto & monitor_name = m_iterator->first.second;
  const auto & segment_ptr = m_iterator->second;

  const auto range = m_backend->m_allocated_ranges.equal_range(segment_ptr);
  for (auto it = range.first; it != range.second; ++it) {
    const auto & shm_range = it->second.second;
    if ((index >= shm_range.first_slot) && (index - shm_range.first_slot) < shm_range.len) {
      return {task_identifier{pid, monitor_name, it->second.first}, index - shm_range.first_slot};
    }
  }
  throw apex::out_of_range{"the index does not belong to any allocation"};
}

}  // namespace apex::execution_monitor
