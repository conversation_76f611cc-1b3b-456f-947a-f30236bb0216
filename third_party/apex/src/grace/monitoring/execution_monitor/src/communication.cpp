/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#include "execution_monitor/communication.hpp"

#include <chrono>
#include <string>
#include <vector>

#include "cpputils/common_exceptions.hpp"
#include "cpputils/variant.hpp"
#include "execution_monitor/details/utils.hpp"

namespace apex
{
namespace execution_monitor
{
namespace
{
/// \brief Returns a string representation of the expectation
/// \param expectation The expectation to convert
/// \cert
/// \deterministic
inline auto expectation_string(const expectations::any_expectation & expectation)
{
  return apex::visit([](const auto & e) { return e.to_string(); }, expectation);
}
}  // namespace

communication_client::communication_client(const std::string & instance_name,
                                           const std::chrono::milliseconds timeout)
: m_timeout{timeout}
{
  if (m_timeout < std::chrono::milliseconds::zero()) {
    throw apex::runtime_error{"communication with the execution monitor must be time bound"};
  }

  // Although we could theoretically precalculate the mem volume needed for `ShmemAcquisition` and
  // other members, we do not have the capacity requirements at this point. An instance of
  // `communication_client` can potentially be shared between several execution monitors each of
  // which registers their tasks, one after another. This means, in the current architecture there
  // is no point in time when we could sum up the tasks and reserve the heap for their bookkeeping.
  if (auto result = base::create_in_place(m_allocator); result.has_error()) {
    const auto error = APEX_ERROR_FROM_ENUM(base::basic_allocator_error, result.error());
    throw apex::runtime_error{"Failed to create allocator.", error.message().c_str()};
  }

  if (auto result = base::create_in_place(m_mem_acquisition, *m_allocator); result.has_error()) {
    throw apex::runtime_error{"Failed to create resource acquisition.",
                              result.error().message().c_str()};
  }

  m_node.emplace(instance_name.empty() ? common::DefaultClientNodeName : instance_name);
  m_reg_cln = m_node->create_polling_client<registration_cln_t>(
    common::combine_name(instance_name, common::RegistrationServiceName));
  m_unreg_cln = m_node->create_polling_client<unregistration_cln_t>(
    common::combine_name(instance_name, common::UnregistrationServiceName));

  try {
    const auto begin = std::chrono::steady_clock::now();
    m_reg_cln->wait_for_service(m_timeout);
    (void)m_reg_ws.add(m_reg_cln);
    const auto passed = std::chrono::steady_clock::now() - begin;
    if (passed > m_timeout) {
      throw apex::runtime_error{"timeout"};
    }
    m_unreg_cln->wait_for_service(m_timeout - passed);
    (void)m_unreg_ws.add(m_unreg_cln);
  } catch (const std::exception & e) {
    throw apex::runtime_error{
      "error while waiting for execution monitor service:", instance_name, ":", e.what()};
  }
}

std::vector<communication::task_slots> communication_client::register_tasks(
  const apex::string256_t & monitor_name,
  const std::vector<communication::task_registration> & registrations)
{
  using ReturnCode = registration_cln_t::Response::BorrowedType::ReturnCode;

  auto loaned_req = m_reg_cln->borrow_loaned_request();

  loaned_req->pid = ::getpid();
  loaned_req->monitor_name = monitor_name;
  loaned_req->process_manager_metadata = m_process_manager_metadata;
  loaned_req->tasks.reserve(registrations.size());
  for (const auto & reg : registrations) {
    loaned_req->tasks.emplace_back();
    auto & taskspec = loaned_req->tasks.back();

    taskspec.id = reg.task_id;
    taskspec.expectation_names.reserve(reg.expectations.size());
    for (const auto & expectation : reg.expectations) {
      taskspec.expectation_names.push_back(expectation_string(expectation));
    }
  }

  (void)m_reg_cln->async_send_request(std::move(loaned_req));
  if (!m_reg_ws.wait(m_timeout)) {
    throw apex::runtime_error("no response received");
  }

  for (const auto & resp : m_reg_cln->take_response()) {
    if (resp.info().valid()) {
      if (resp.data().retcode == ReturnCode::ERROR) {
        throw apex::runtime_error("backend answered with the following error message:",
                                  resp.data().shm_path_or_error_msg);
      }

      if (resp.data().allocated_ranges.size() != registrations.size()) {
        throw apex::runtime_error("backend registered fewer tasks than requested");
      }

      if (auto local_backend_check_result =
            m_mem_acquisition->open_typed_segment_read_only<std::byte>(
              details::to_iox_name(resp.data().ensure_local_backend_segment_path.c_str()));
          local_backend_check_result.has_error()) {
        throw apex::runtime_error(
          "Could not ensure backend locality: failed to open its token segment. "
          "Is the execution-monitor backend running on a different host?",
          local_backend_check_result.error().message().c_str());
      }

      const apex::string256_t & shm_path = resp.data().shm_path_or_error_msg;
      const auto & segment = find_or_open_segment(shm_path);
      const auto & slots = segment.elements();

      std::vector<task_slots> ret;
      ret.reserve(registrations.size());
      for (std::size_t i{0}; i < registrations.size(); ++i) {
        const auto & range = resp.data().allocated_ranges[i];
        if (range.len != registrations[i].expectations.size()) {
          throw apex::runtime_error("backend returned fewer slots than requested");
        }
        ret.push_back({slots.subspan(range.start, range.len)});
      }
      return ret;
    }
  }
  throw apex::runtime_error{"no answer from the backend for a registration request"};
}

void communication_client::unregister_tasks(const apex::string256_t & monitor_name)
{
  if (m_reg_ws.empty()) {
    throw apex::runtime_error{"the backend does not support resignation requests"};
  }

  auto loaned_req = m_unreg_cln->borrow_loaned_request();
  loaned_req->pid = ::getpid();
  loaned_req->monitor_name = monitor_name;

  (void)m_unreg_cln->async_send_request(std::move(loaned_req));
  if (!m_unreg_ws.wait(m_timeout)) {
    throw apex::runtime_error("no response received");
  }

  for (const auto & resp : m_unreg_cln->take_response()) {
    if (resp.info().valid()) {
      return;
    }
  }
  throw apex::runtime_error{"no answer from the backend for a resignation request"};
}

communication_client::MonitorDataSegment & communication_client::find_or_open_segment(
  const apex::string256_t & shm_path)
{
  auto shm_object_it = m_mapped_shm.find(shm_path);
  if (shm_object_it == m_mapped_shm.end()) {
    auto shm_result = m_mem_acquisition->open_typed_segment_read_write<monitor_slot>(
      details::to_iox_name(shm_path), SLOTS_PER_SEGMENT);
    if (shm_result.has_error()) {
      throw apex::runtime_error{"Failed to open shared memory segment.",
                                shm_result.error().message().c_str()};
    }
    shm_object_it = m_mapped_shm.emplace(shm_path, std::move(shm_result.value())).first;
  }
  return shm_object_it->second;
}

}  // namespace execution_monitor
}  // namespace apex
