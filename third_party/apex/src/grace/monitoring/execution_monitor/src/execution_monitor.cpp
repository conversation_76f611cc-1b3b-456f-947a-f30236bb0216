/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#include <chrono>
#include <cstdint>

#include <execution_monitor/execution_monitor.hpp>
#include <execution_monitor/expectations.hpp>

namespace apex
{
namespace execution_monitor
{
namespace expectations
{
constexpr std::chrono::microseconds wall_clock_runtime::infinity;
constexpr std::chrono::nanoseconds cpu_clock_runtime::infinity;
constexpr std::uint32_t activations_per_window::infinity;
constexpr std::chrono::microseconds activation_distance::infinity;
}  // namespace expectations
}  // namespace execution_monitor
}  // namespace apex
