load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "execution_monitor_pkg",
    cc_libraries = [":execution_monitor"],
    description = "A package for monitoring execution times and similar properties.",
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/allocator:allocator_pkg",
        "//common/cpputils:cpputils_pkg",
        "//grace/interfaces/execution_monitor_msgs:execution_monitor_msgs_pkg",
        "//grace/interfaces/process_manager_interfaces:process_manager_interfaces_pkg",
        "//grace/monitoring/logging:logging_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
    ],
)

apex_cc_library(
    name = "execution_monitor",
    srcs = [
        "src/backend.cpp",
        "src/communication.cpp",
        "src/execution_monitor.cpp",
        "src/monitor_slot.cpp",
        "src/utils.cpp",
    ],
    hdrs = [
        "include/execution_monitor/backend.hpp",
        "include/execution_monitor/common.hpp",
        "include/execution_monitor/communication.hpp",
        "include/execution_monitor/communication_stub.hpp",
        "include/execution_monitor/details/expectation_monitors.hpp",
        "include/execution_monitor/details/utils.hpp",
        "include/execution_monitor/execution_monitor.hpp",
        "include/execution_monitor/expectation_types.hpp",
        "include/execution_monitor/expectations.hpp",
        "include/execution_monitor/monitor_slot.hpp",
        "include/execution_monitor/task_identifier.hpp",
        "include/execution_monitor/visibility.hpp",
    ],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/interfaces/execution_monitor_msgs",
        "//grace/interfaces/process_manager_interfaces:process_manager_common",
        "//grace/monitoring/logging",
        "//grace/ros/rclcpp/rclcpp",
        "//ida/plexus/resource_acquisition:shmem_acquisition",
        "@coverage_tool//:coverage_io_lib",
    ],
)

apex_cc_test(
    name = "test_backend",
    srcs = [
        "test/test_backend.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "constrained_test",
    ],
    deps = [
        ":execution_monitor",
        "//ida/common:test_util",
        "@googletest//:gtest_main",
    ],
)

apex_cc_test(
    name = "test_execution_monitor",
    srcs = [
        "test/test_execution_monitor.cpp",
    ],
    defines = [
        "APEX_PRINT_LOGS_TO_TERMINAL",  # to assert log messages
    ],
    env = {
        "APEX_GRACE_LOG_LEVEL": "TRACE",
        "IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc",
    },
    tags = [
        "constrained_test",
    ],
    deps = [
        ":execution_monitor",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "api_srcs",
    srcs = glob(["include/**"]),
    visibility = [":__subpackages__"],
)
