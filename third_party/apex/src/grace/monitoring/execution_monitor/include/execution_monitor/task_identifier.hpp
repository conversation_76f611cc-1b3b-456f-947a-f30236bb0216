/// \copyright Copyright 2022 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTION_MONITOR__TASK_IDENTIFIER_HPP
#define EXECUTION_MONITOR__TASK_IDENTIFIER_HPP

#include <cstdint>
#include <utility>

#include <execution_monitor/visibility.hpp>
#include <string/string.hpp>

namespace apex
{
namespace execution_monitor
{

/// \brief An identifier for an execution monitor task
/// that is based on a combination of process id,
/// monitor name and task id
struct EXECUTION_MONITOR_PUBLIC task_identifier
{
  using task_id_t = apex::string32_t;
  std::int32_t pid;
  apex::string256_t monitor_name;
  task_id_t task_id;

  /// \brief Creates a compound identifier
  task_identifier(std::int32_t pid, apex::string256_t monitor_name, task_id_t task_id)
  : pid(pid), monitor_name(std::move(monitor_name)), task_id(task_id)
  {
  }

  bool operator==(const task_identifier & other) const noexcept
  {
    return (pid == other.pid) && (task_id == other.task_id) && (monitor_name == other.monitor_name);
  }

  bool operator!=(const task_identifier & other) const noexcept
  {
    return !operator==(other);
  }

  bool operator<(const task_identifier & other) const noexcept
  {
    if (pid != other.pid) {
      return pid < other.pid;
    }
    if (monitor_name != other.monitor_name) {
      return monitor_name < other.monitor_name;
    }
    return task_id < other.task_id;
  }

  /// \brief Creates a human-readable representation of this id
  apex::string256_t to_string() const noexcept
  {
    return apex::varargs_to_string(
      apex::no_separator{}, "pid: ", pid, ", monitor: ", monitor_name, ", task: ", task_id);
  }
};

}  // namespace execution_monitor
}  // namespace apex

#endif  // EXECUTION_MONITOR__TASK_IDENTIFIER_HPP
