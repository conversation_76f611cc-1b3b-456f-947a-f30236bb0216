/// \copyright Copyright 2022 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTION_MONITOR__EXECUTION_MONITOR_HPP
#define EXECUTION_MONITOR__EXECUTION_MONITOR_HPP

#include <apexutils/apex_process.h>

#include <algorithm>
#include <limits>
#include <memory>
#include <queue>
#include <string>
#include <tuple>
#include <unordered_map>
#include <utility>
#include <vector>

#include <cpputils/optional.hpp>
#include <cpputils/variant.hpp>
#include <execution_monitor/communication.hpp>
#include <execution_monitor/details/expectation_monitors.hpp>
#include <logging/logging_macros.hpp>

namespace apex
{
namespace execution_monitor
{

namespace details
{
/// \brief Monitors whether tasks fulfill their execution-time expectations.
/// Signals through a counter in a shared memory segment if an infraction is
/// detected.
///
/// Callers need to inform the monitor about task starts and
/// completions.
/// \tparam TaskId The type of task identifiers used in the monitor.
/// \tparam clock_type The clock used for expectation monitor deadlines
template <typename TaskId, typename clock_type>
class execution_monitor final
{
public:
  using task_t = TaskId;
  /// \brief Create a new execution monitor.
  /// \param name A name for the monitor
  /// \param comm Communicator to register the monitor with the backend. The caller is
  /// responsible for ensuring that the passed object outlives the lifetime of the execution_monitor
  /// \param logger Optional. Logger used to log infractions. If nothing is passed, the infractions
  /// won't be logged (but still reported by the backend)
  /// \cert
  execution_monitor(std::string name,
                    apex::execution_monitor::communication & comm,
                    apex::logging::Logger<> * logger = nullptr)
  : m_name(std::move(name)), m_comm(comm), m_logger(logger)
  {
  }

  /// \brief Calls `unregister_monitor_tasks` if it was not called yet
  ~execution_monitor()
  {
    try {
      unregister_monitor_tasks();
    } catch (const std::exception & e) {
      log_warning("Error while unregistering monitor '", m_name, "' :", e.what());
    } catch (...) {
      log_warning("Unknown error while unregistering monitor '", m_name, "'");
    }
  }

  execution_monitor(execution_monitor &) = delete;
  execution_monitor & operator=(execution_monitor &) = delete;

  /// \brief Activates the execution monitor.
  /// \cert
  /// \deterministic
  void issue()
  {
    if (!m_registrations_complete) {
      throw apex::logic_error{
        "Attempted to issue the monitor without calling "
        "`registrations_complete`"};
    }

    for (const auto & task_monitors_pair : m_per_task_monitors) {
      for (const auto & monitor : task_monitors_pair.second) {
        monitor->activate();
      }
    }
  }

public:
  /// \brief Registers a new task with the monitor. Returns immediately, but some errors
  /// may only be noticed during `registrations_complete`
  /// \param task The task ID of the task
  /// \param expectations The expectations for the task.
  /// \note The number of expectations allowed for a single task is limited by the implementation.
  ///       See `RegisterExecutionMonitor.idl` in the `execution_monitor_msgs` package
  void register_task(task_t task, const expectation_list & expectations)
  {
    if (m_registrations_complete) {
      throw apex::logic_error{"Cannot register task after registrations are complete"};
    }

    if (is_registered(task)) {
      throw apex::invalid_argument("Task", task, "already registered");
    }

    // The backend requires all tasks of an execution monitor to be registered in a single
    // message. Store the registration data and perform it lazily when `registrations_complete`
    // is called.
    m_delayed_registrations.emplace(task, expectations);
  }

  /// \brief Returns whether a task ID is already registered for this execution_monitor
  /// \note For the purposes of this method, a task counts as registered once `register_task` has
  ///       been invoked. It is not necessary to call `registrations_complete` first.
  /// \cert
  /// \deterministic
  bool is_registered(task_t task) const noexcept
  {
    return (m_per_task_monitors.find(task) != m_per_task_monitors.end()) ||
           (m_delayed_registrations.find(task) != m_delayed_registrations.end());
  }

  /// \brief Informs the monitor that there will be no more task registrations. When this method
  /// returns, all the monitor's tasks have been registered with and acknowledged by the backend
  /// The method blocks until the backend acknowledgements have arrived and
  /// throws if the communication timeout expires before that.
  /// \note If this method throws, the monitor remains unchanged (strong exception safety).
  /// \cert
  void registrations_complete()
  {
    // The registrations map in vector form, as `communication` expects a vector instead of a map.
    // Probably it would be better to have `communication` take an iterator.
    std::vector<communication::task_registration> registrations_vector;
    registrations_vector.reserve(m_delayed_registrations.size());

    // Fill registrations vector and allocate memory before submitting the registration.
    // This ensures strong exception safety even if allocation fails.
    std::vector<communication::task_slots> task_slots;
    try {
      for (const auto & reg : m_delayed_registrations) {
        using mapped_type = typename decltype(m_per_task_monitors)::mapped_type;
        auto iter_inserted = m_per_task_monitors.emplace(reg.first, mapped_type());
        assert(iter_inserted.second);  // Caller guarantees that the tasks are not registered yet.

        auto & expectation_monitors = iter_inserted.first->second;
        expectation_monitors.reserve(reg.second.size());
        registrations_vector.emplace_back(reg.first, reg.second);
      }
      task_slots = m_comm.register_tasks(m_name, registrations_vector);
    } catch (...) {
      for (const auto & reg : m_delayed_registrations) {
        m_per_task_monitors.erase(reg.first);
      }
      throw;
    }
    // Now we are committed. Clear the delayed registrations map.
    m_delayed_registrations.clear();

    assert(task_slots.size() == registrations_vector.size());
    for (std::size_t i{0}; i < task_slots.size(); ++i) {
      auto & reg_slots = task_slots[i];
      const auto & reg = registrations_vector[i];
      auto & expectation_monitors = m_per_task_monitors[static_cast<task_t>(reg.task_id)];
      for (size_t j{0}; j < reg.expectations.size(); ++j) {
        expectation_monitors.push_back(
          make_expectations_monitor<clock_type>(reg.expectations[j], reg_slots[j], m_logger));
      }
    }
    m_registrations_complete = true;
  }

  /// \brief Unregisters all the tasks of the current monitor via communication and then
  /// removes them locally
  /// \cert
  /// \deterministic
  void unregister_monitor_tasks()
  {
    if (m_registrations_complete) {
      m_comm.unregister_tasks(m_name);
      m_per_task_monitors.clear();
      m_registrations_complete = false;
    }
  }

  /// \brief Notifies the monitor that the given task has started executing
  /// \cert
  /// \deterministic
  void task_starts(task_t task)
  {
    task_starts_or_completes(task, true);
  }

  /// \brief Notifies the monitor that the given task has completed executing
  /// \cert
  /// \deterministic
  void task_completes(task_t task)
  {
    task_starts_or_completes(task, false);
  }

private:
  template <class... Args>
  void log_warning(Args &&... args)
  {
    if (m_logger != nullptr) {
      try {
        APEX_WARN(*m_logger, apex::no_separator{}, std::forward<Args>(args)...);
      } catch (...) {
      }
    }
  }

  /// \brief Common implementation of `task_starts` and `task_completes`
  /// \param task The task that starts or completes
  /// \param start Whether the task starts (true) or completes (false)
  void task_starts_or_completes(task_t task, bool starts)
  {
    if (const auto it = m_per_task_monitors.find(task); it != m_per_task_monitors.end()) {
      for (const auto & monitor : it->second) {
        if (starts) {
          monitor->task_starts();
        } else {
          monitor->task_completes();
        }
      }
    } else {
      throw apex::invalid_argument("Attempting to start/complete undefined task", task);
    }
  }

private:
  /// Have the registrations been completed yet?
  bool m_registrations_complete{false};

  /// All registrations that have been performed but not yet submitted to the backend
  std::unordered_map<task_t, expectation_list> m_delayed_registrations;
  using MonitorPtr = std::unique_ptr<details::expectation_monitor_base>;
  /// The various expectation monitors, grouped by task.
  std::unordered_map<task_t, std::vector<MonitorPtr>> m_per_task_monitors{};
  const std::string m_name;
  communication & m_comm;
  apex::logging::Logger<> * m_logger = nullptr;
};
}  // namespace details

/// \brief The default execution_monitor instantiation
/// \tparam TaskId The type used for task IDs
template <typename TaskId>
using execution_monitor = details::execution_monitor<TaskId, std::chrono::steady_clock>;

/// \brief A RAII helper to start/stop an execution monitor task
/// \tparam ExecutionMonitor The type of the execution monitor
template <typename ExecutionMonitor>
class monitored_scope final
{
public:
  using task_t = typename ExecutionMonitor::task_t;

  /// \brief Creates an empty monitored_scope object that does nothing.
  /// This is mostly useful as a target for a later move operation
  monitored_scope() : m_monitor(nullptr), m_task() {}

  /// \brief Starts the task `task` in the given monitor. The task is completed when
  /// the `monitored_scope` object is destroyed
  monitored_scope(ExecutionMonitor & monitor, task_t task) : m_monitor(&monitor), m_task(task)
  {
    m_monitor->task_starts(task);
  }

  monitored_scope(monitored_scope &) = delete;
  monitored_scope & operator=(monitored_scope &) = delete;

  monitored_scope(monitored_scope && oth) noexcept
  : m_monitor(nullptr), m_task(std::move(oth.m_task))
  {
    std::swap(m_monitor, oth.m_monitor);
  }

  monitored_scope & operator=(monitored_scope && oth) & noexcept
  {
    if (&oth != this) {
      if (m_monitor != nullptr) {
        m_monitor->task_completes(m_task);
        m_monitor = nullptr;
      }
      std::swap(m_monitor, oth.m_monitor);
      m_task = std::move(oth.m_task);
    }
    return *this;
  }

  ~monitored_scope()
  {
    if (m_monitor != nullptr) {
      m_monitor->task_completes(m_task);
    }
  }

private:
  ExecutionMonitor * m_monitor;
  task_t m_task;
};

}  // namespace execution_monitor
}  // namespace apex
#endif  // EXECUTION_MONITOR__EXECUTION_MONITOR_HPP
