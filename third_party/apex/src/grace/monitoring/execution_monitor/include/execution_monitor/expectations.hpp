/// \copyright Copyright 2022 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTION_MONITOR__EXPECTATIONS_HPP
#define EXECUTION_MONITOR__EXPECTATIONS_HPP

#include <algorithm>
#include <chrono>
#include <limits>

#include <cpputils/common_exceptions.hpp>
#include <cpputils/variant.hpp>
#include <execution_monitor/visibility.hpp>
#include <string/string.hpp>
#include <string/to_string.hpp>

namespace apex
{
namespace execution_monitor
{
namespace expectations
{

/// \brief An expectation that wall-clock runtime remains in the interval [min, max]
struct wall_clock_runtime
{
  std::chrono::microseconds min;
  std::chrono::microseconds max;
  /// \brief The highest possible time value, used to mark an infinite upper bound
  EXECUTION_MONITOR_PUBLIC static constexpr std::chrono::microseconds infinity =
    std::chrono::microseconds::max();

  /// \brief Creates a wall-clock runtime expectation with a minimum and a maximum
  /// \cert
  /// \deterministic
  wall_clock_runtime(std::chrono::microseconds minimum, std::chrono::microseconds maximum)
  : min(minimum), max(maximum)
  {
    if (min > max) {
      throw apex::invalid_argument("Unfulfillable expectation: ", to_string());
    }
  }
  /// \brief Creates a wall-clock runtime expectation with only a maximum (i.e., min = 0)
  /// \cert
  /// \deterministic
  explicit wall_clock_runtime(std::chrono::microseconds maximum)
  : wall_clock_runtime(std::chrono::microseconds(0), maximum)
  {
  }

  /// \brief Returns a string representation of the expectation
  /// \cert
  /// \deterministic
  apex::string256_t to_string() const noexcept
  {
    return apex::varargs_to_string(::apex::no_separator{},
                                   "[",
                                   apex::to_string(min),
                                   " <= wall_clock_runtime <= ",
                                   apex::to_string(max),
                                   "]");
  }
};

/// \brief An expectation that CPU-clock runtime remains in the interval [min, max]
struct cpu_clock_runtime
{
  std::chrono::nanoseconds min;
  std::chrono::nanoseconds max;
  /// \brief The highest possible time value, used to mark an infinite upper bound
  EXECUTION_MONITOR_PUBLIC static constexpr std::chrono::nanoseconds infinity =
    std::chrono::nanoseconds::max();

  /// \brief Creates a CPU-clock runtime expectation with a minimum and a maximum
  /// \cert
  /// \deterministic
  cpu_clock_runtime(std::chrono::nanoseconds minimum, std::chrono::nanoseconds maximum)
  : min(minimum), max(maximum)
  {
    if (min > max) {
      throw apex::invalid_argument("Unfulfillable expectation: ", to_string());
    }
  }
  /// \brief Creates a CPU-clock runtime expectation with only a maximum (i.e., min = 0)
  /// \cert
  /// \deterministic
  explicit cpu_clock_runtime(std::chrono::nanoseconds maximum)
  : cpu_clock_runtime(std::chrono::nanoseconds(0), maximum)
  {
  }

  /// \brief Returns a string representation of the expectation
  /// \cert
  /// \deterministic
  apex::string256_t to_string() const noexcept
  {
    return apex::varargs_to_string(::apex::no_separator{},
                                   "[",
                                   apex::to_string(min),
                                   " <= cpu_clock_runtime <= ",
                                   apex::to_string(max),
                                   "]");
  }
};

/// \brief An expectation that a task is activated at least `min` and at most `max` times during
/// a time window of specified length
struct activations_per_window
{
  std::chrono::microseconds window_length;
  std::uint32_t min;
  std::uint32_t max;
  /// \brief The highest possible time value, used to mark an infinite upper bound
  EXECUTION_MONITOR_PUBLIC static constexpr std::uint32_t infinity =
    std::numeric_limits<std::uint32_t>::max();

  /// \brief Creates an activations-per-window expectation with a minimum and a maximum
  /// \cert
  /// \deterministic
  activations_per_window(std::uint32_t minimum,
                         std::uint32_t maximum,
                         std::chrono::microseconds window)
  : window_length(window), min(minimum), max(maximum)
  {
    if (min > max) {
      throw apex::invalid_argument("Unfulfillable expectation: ", to_string());
    }
    if (window_length.count() == 0) {
      throw apex::invalid_argument("Invalid expectation: zero-length window");
    }
  }

  /// \brief Returns a string representation of the expectation
  /// \cert
  /// \deterministic
  apex::string256_t to_string() const noexcept
  {
    return apex::varargs_to_string(::apex::no_separator{},
                                   "[",
                                   min,
                                   " <= activations every ",
                                   apex::to_string(window_length),
                                   " <= ",
                                   max,
                                   "]");
  }
};

/// \brief An expectation that two consecutive activations of the same task are separated by
/// at least `min` and at most `max` microseconds
struct activation_distance
{
  std::chrono::microseconds min;
  std::chrono::microseconds max;
  /// \brief The highest possible time value, used to mark an infinite upper bound
  EXECUTION_MONITOR_PUBLIC static constexpr std::chrono::microseconds infinity =
    std::chrono::microseconds::max();

  /// \brief Creates an activation-distance expectation with a minimum and a maximum
  /// \cert
  /// \deterministic
  activation_distance(std::chrono::microseconds minimum, std::chrono::microseconds maximum)
  : min(minimum), max(maximum)
  {
    if (min > max) {
      throw apex::invalid_argument("Unfulfillable expectation: ", to_string());
    }
  }

  /// \brief Returns a string representation of the expectation
  /// \cert
  /// \deterministic
  apex::string256_t to_string() const noexcept
  {
    return apex::varargs_to_string(::apex::no_separator{},
                                   "[",
                                   apex::to_string(min),
                                   " <= activation_distance <= ",
                                   apex::to_string(max),
                                   "]");
  }
};

/// \brief A variant type covering all available expectation types.
using any_expectation = apex::variant<expectations::wall_clock_runtime,
                                      expectations::cpu_clock_runtime,
                                      expectations::activations_per_window,
                                      expectations::activation_distance>;
}  // namespace expectations
}  // namespace execution_monitor
}  // namespace apex
#endif  // EXECUTION_MONITOR__EXPECTATIONS_HPP
