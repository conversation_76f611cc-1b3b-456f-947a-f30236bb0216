/// \copyright Copyright 2022 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTION_MONITOR__DETAILS_EXPECTATION_MONITORS_HPP
#define EXECUTION_MONITOR__DETAILS_EXPECTATION_MONITORS_HPP

#include <apexutils/apex_string.h>

#include <atomic>
#include <cassert>
#include <limits>
#include <memory>
#include <string>
#include <type_traits>
#include <utility>

#include <cpputils/fake_clock.hpp>
#include <execution_monitor/expectation_types.hpp>
#include <execution_monitor/expectations.hpp>
#include <execution_monitor/monitor_slot.hpp>
#include <logging/logging_macros.hpp>
#include <string/to_string.hpp>

namespace apex
{
namespace execution_monitor
{
namespace details
{
// disable "empty method" checks
// and "this could be const" checks for the base class, as <PERSON><PERSON><PERSON><PERSON> does not understand that this
// is normal and expected for virtual base methods with default implementations

using logger_ptr = apex::logging::Logger<> *;

/// \brief Common base class for expectation monitors
/// \note The expectation monitor is not thread-safe. The caller is responsible for synchronizing
///       access to the monitors
class expectation_monitor_base
{
public:
  expectation_monitor_base(expectations::types::expectation_type_id_t type_id,
                           monitor_slot & slot,
                           logger_ptr logger,
                           std::string log_prefix)
  : m_type_id(type_id), m_slot(&slot), m_logger(logger), m_log_prefix(std::move(log_prefix))
  {
    m_slot->expectation_type_id = m_type_id;
  }

  expectation_monitor_base(const expectation_monitor_base &) noexcept = delete;
  expectation_monitor_base & operator=(const expectation_monitor_base &) noexcept = delete;
  virtual ~expectation_monitor_base() = default;

  /// \brief Turns on the monitor.
  /// \cert
  /// \deterministic
  virtual void activate() {}

  /// \brief Notifies the monitor that the monitored task has started
  /// \cert
  /// \deterministic
  virtual void task_starts() noexcept {}

  /// \brief Notifies the monitor that the monitored task has completed
  /// \cert
  /// \deterministic
  virtual void task_completes() noexcept {}

  /// \brief Returns the type of the expectation
  /// \cert
  /// \deterministic
  expectations::types::expectation_type_id_t type_id() const noexcept
  {
    return m_type_id;
  }

protected:
  const expectations::types::expectation_type_id_t m_type_id;
  monitor_slot * m_slot;
  logger_ptr m_logger;
  const std::string m_log_prefix;
};

/// \brief Monitors the wall_clock_runtime expectation for a given task
/// \tparam clock_type The clock type used for monitor deadlines
template <typename clock_type>
class wall_clock_runtime_monitor : public expectation_monitor_base
{
  using time_point = typename clock_type::time_point;

public:
  /// \brief Creates a wall-clock-runtime monitor for the given expectation
  /// \cert
  /// \deterministic
  wall_clock_runtime_monitor(const expectations::wall_clock_runtime & expectation,
                             monitor_slot & slot,
                             logger_ptr logger,
                             std::string log_prefix)
  : expectation_monitor_base{expectations::types::expectation_type_id_t::WALL_CLOCK_RUNTIME,
                             slot,
                             logger,
                             std::move(log_prefix)},
    m_expectation{expectation}
  {
  }

  /// \brief Notifies the monitor that the task has started
  /// \cert
  /// \deterministic
  void task_starts() noexcept override
  {
    m_start_time = clock_type::now();
    if (m_expectation.max != expectations::wall_clock_runtime::infinity) {
      (void)m_slot->check_and_update_deadline_from_now(m_start_time + m_expectation.max);
    }
  }

  /// \brief Notifies the monitor that the task has completed
  /// \cert
  /// \deterministic
  void task_completes() noexcept override
  {
    const auto overrun =
      m_slot->check_and_update_deadline_from_now(time_point{monitor_slot::infinity});
    if (overrun.count() > 0) {
      if (m_logger) {
        APEX_WARN(*m_logger,
                  m_log_prefix,
                  "Wall-clock runtime should have been below:",
                  apex::to_string(m_expectation.max),
                  ", measured:",
                  apex::to_string(std::chrono::duration_cast<std::chrono::microseconds>(
                    overrun + m_expectation.max)));
      }
    }

    const auto runtime = clock_type::now() - m_start_time;
    if (runtime < m_expectation.min) {
      m_slot->set_deviation(runtime - m_expectation.min);
      if (m_logger) {
        APEX_WARN(*m_logger,
                  m_log_prefix,
                  "Wall-clock runtime should have been above:",
                  apex::to_string(m_expectation.min),
                  ", measured:",
                  apex::to_string(std::chrono::duration_cast<std::chrono::microseconds>(runtime)));
      }
    }
  }

private:
  const expectations::wall_clock_runtime m_expectation;
  /// The start time of the current task
  time_point m_start_time{};
};

/// \brief Monitors the cpu_clock_runtime expectation for a given task
/// \tparam clock_type The clock type used for monitor deadlines
template <typename clock_type>
class cpu_clock_runtime_monitor : public expectation_monitor_base
{
  static constexpr bool use_real_clock = !std::is_same_v<clock_type, apex::FakeClock>;

public:
  /// \brief Creates a CPU-clock-runtime monitor for the given expectation.
  /// \cert
  /// \deterministic
  cpu_clock_runtime_monitor(const expectations::cpu_clock_runtime & expectation,
                            monitor_slot & slot,
                            logger_ptr logger,
                            std::string log_prefix)
  : expectation_monitor_base{expectations::types::expectation_type_id_t::CPU_CLOCK_RUNTIME,
                             slot,
                             logger,
                             std::move(log_prefix)},
    m_expectation{expectation}
  {
    // Verify that the per-thread CPU-time clock can be read.
    try {
      (void)per_thread_cputime();
    } catch (const apex::system_error & e) {
      if (e.code() == EINVAL) {
        throw apex::runtime_error("CPU-time clock is not supported on this system");
      } else {
        throw;
      }
    }
  }

  /// \brief Notifies the monitor that the task has started
  /// \cert
  /// \deterministic
  void task_starts() noexcept override
  {
    // per_thread_cputime may throw an exception, but only if the per-thread CPU-time clock is not
    // supported on this system. That was ruled out in the constructor.
    // cppcheck-suppress throwInNoexceptFunction
    m_start_time = per_thread_cputime();
  }

  /// \brief Notifies the monitor that the task has started
  /// \cert
  /// \deterministic
  void task_completes() noexcept override
  {
    // See the comment in `task_starts` for details on the error handling here
    // cppcheck-suppress throwInNoexceptFunction
    const auto runtime = per_thread_cputime() - m_start_time;

    if ((runtime < m_expectation.min) || (runtime > m_expectation.max)) {
      m_slot->set_deviation(runtime -
                            (runtime < m_expectation.min ? m_expectation.min : m_expectation.max));
      if (m_logger) {
        APEX_WARN(*m_logger,
                  m_log_prefix,
                  "CPU-clock runtime should have been between:",
                  apex::to_string(m_expectation.min),
                  ", and",
                  apex::to_string(m_expectation.max),
                  ", "
                  "measured:",
                  apex::to_string(runtime));
      }
    }
  }

private:
  /// \brief Returns the CPU-clock for the current thread in nanoseconds
  /// \throws apex::system_error if `clock_gettime` fails
  /// \cert
  /// \deterministic
  static auto per_thread_cputime()
  {
    if constexpr (use_real_clock) {
      ::timespec time{0, 0};
      if (::clock_gettime(CLOCK_THREAD_CPUTIME_ID, &time) < 0) {
        throw apex::system_error(errno, "Failed to get per-clock CPU time");
      }
      using sec = std::chrono::seconds;
      using nsec = std::chrono::nanoseconds;

      return std::chrono::duration_cast<nsec>(sec(time.tv_sec)) + nsec(time.tv_nsec);
    } else {
      return clock_type::now();
    }
  }

  const expectations::cpu_clock_runtime m_expectation;
  std::conditional_t<use_real_clock, std::chrono::nanoseconds, typename clock_type::time_point>
    m_start_time{};
};

/// \brief Monitors the activations-per-window expectation for a given task
/// \tparam clock_type The clock type used for monitor deadlines
template <typename clock_type>
class activations_per_window_monitor : public expectation_monitor_base
{
  using time_point = typename clock_type::time_point;

public:
  /// \brief Create an activations-per-window monitor for the given expectation
  /// \cert
  /// \deterministic
  activations_per_window_monitor(const expectations::activations_per_window & expectation,
                                 monitor_slot & slot,
                                 logger_ptr logger,
                                 std::string log_prefix)
  : expectation_monitor_base{expectations::types::expectation_type_id_t::ACTIVATIONS_PER_WINDOW,
                             slot,
                             logger,
                             std::move(log_prefix)},
    m_expectation{expectation}
  {
  }

  /// \brief Turns on this monitor
  /// \cert
  /// \deterministic
  void activate() override
  {
    m_window_start = clock_type::now();
    if (m_expectation.min > 0) {
      (void)m_slot->check_and_update_deadline_from_now(window_end(), 0);
    }
  }

  /// \brief Notifies the monitor that the task has started
  /// \cert
  /// \deterministic
  void task_starts() noexcept override
  {
    const auto now = clock_type::now();
    // Advance the window to the current point in time if necessary.
    // Note that this is not necessarily an infraction; it happens for
    // the first activation of each new window
    if (now >= window_end()) {
      advance_window(now);
    } else {
      // If we are still in the current window, increment the activation count
      ++m_activations;
    }

    // Check if this activation exceeds the maximal number of activations.
    if (m_activations > m_expectation.max) {
      m_slot->set_deviation(m_activations - m_expectation.max);
      if (m_logger) {
        APEX_WARN(*m_logger,
                  m_log_prefix,
                  "Number of activations should have been below:",
                  m_expectation.max,
                  ", observed:",
                  m_activations);
      }
    }

    if (m_expectation.min == m_activations) {
      assert(m_expectation.min > 0);
      // All activations for this cycle are covered. Push the deadline to the end of the next
      // window
      if (m_slot
            ->check_and_update_deadline_from_now(window_end() + m_expectation.window_length,
                                                 m_activations)
            .count() > 0) {
        // A deadline overrun here is quite strange; it means that the window changed
        // between the time `now` was obtained and the update above.
        // Log this as a regular lack of activations, but note the strange situation.
        // Note that accounting for the next window is probably going to be slightly
        // confused; but given that this is such a rare and hard-to-test-for event, let's
        // not fiddle with the accounting too much. The situation is going to stabilize
        // after the next window at the latest.
        if (m_logger) {
          APEX_WARN(*m_logger,
                    m_log_prefix,
                    "Number of activations should have been above:",
                    m_expectation.min,
                    ", observed:",
                    m_activations,
                    ", with the last activation happening right as the window ended");
        }
      }
    }
  }

private:
  /// \brief Advance the window
  ///  May jump more than one window ahead if `now > m_window_start + window_length`.
  /// \param now The current time
  /// \post `window_end()` > `now`
  /// \cert
  /// \deterministic
  void advance_window(time_point now) noexcept
  {
    // Determine how many windows to advance. Usually `num_windows` will be 1, but it may be
    // higher if `now` is sufficiently late
    const auto delta = now - m_window_start;
    const auto num_windows = (delta / m_expectation.window_length);
    const auto activations_in_last_window = m_activations;
    assert(num_windows >= 1U);

    // Update the internal members for the new window
    m_activations = 1;
    m_window_start += num_windows * m_expectation.window_length;
    assert(window_end() > now);

    // If there are minimum-activations-per-window expectations, check for infractions and
    // update the deadline. Checking this here detects the problem earlier than waiting for
    // the next regular update deadline operation,
    // which happens only once the expectations are fulfilled in the current window
    if (m_expectation.min > 0 &&
        (num_windows > 1U || activations_in_last_window < m_expectation.min)) {
      const auto overrun = m_slot->check_and_update_deadline_from_now(
        window_end(),
        static_cast<std::int32_t>(activations_in_last_window) -
          static_cast<std::int32_t>(m_expectation.min));
      if (overrun.count() > 0 && m_logger) {
        if (num_windows > 1U) {
          APEX_WARN(*m_logger,
                    m_log_prefix,
                    "Number of activations should have been above:",
                    m_expectation.min,
                    "but",
                    num_windows - 1U,
                    "windows passed without any activation");
        } else {
          APEX_WARN(*m_logger,
                    m_log_prefix,
                    "Number of activations should have been above:",
                    m_expectation.min,
                    ", observed:",
                    activations_in_last_window);
        }
      }
    }
  }

  /// \brief Returns the end of the current monitoring window
  /// \cert
  /// \deterministic
  time_point window_end() const noexcept
  {
    return m_window_start + m_expectation.window_length;
  }

  const expectations::activations_per_window m_expectation;
  std::uint32_t m_activations{0};
  time_point m_window_start{clock_type::now()};
};

/// \brief Monitors the activation-distance expectation for a given task
/// \tparam clock_type The clock type used for monitor deadlines
template <typename clock_type>
class activation_distance_monitor : public expectation_monitor_base
{
  using time_point = typename clock_type::time_point;

public:
  /// \brief Creates an activation-distance monitor for the given expectation
  activation_distance_monitor(const expectations::activation_distance & expectation,
                              monitor_slot & slot,
                              logger_ptr logger,
                              std::string description)
  : expectation_monitor_base(expectations::types::expectation_type_id_t::ACTIVATION_DISTANCE,
                             slot,
                             logger,
                             std::move(description)),
    m_expectation{expectation}
  {
  }

  /// \brief Activates this monitor
  /// \cert
  /// \deterministic
  void activate() override
  {
    m_last_activation = clock_type::now();
    if (m_expectation.max != expectations::activation_distance::infinity) {
      (void)m_slot->check_and_update_deadline_from_now(m_last_activation + m_expectation.max);
    }
  }

  /// \brief Notifies the monitor that the task has started
  /// \cert
  /// \deterministic
  void task_starts() noexcept override
  {
    const auto now = clock_type::now();
    const auto distance = now - m_last_activation;
    m_last_activation = now;
    // Check if the activation distance satisfies the min expectation if this is not the first run
    if (m_check_min && (distance < m_expectation.min)) {
      m_slot->set_deviation(distance - m_expectation.min);
      if (m_logger) {
        APEX_WARN(*m_logger,
                  m_log_prefix,
                  "Time distance between 2 activations should have been above:",
                  apex::to_string(m_expectation.min),
                  ", measured:",
                  apex::to_string(std::chrono::duration_cast<std::chrono::microseconds>(distance)));
      }
    }

    // Set up a deadline for the last allowed activation time
    if (m_expectation.max != expectations::activation_distance::infinity) {
      if (m_slot->check_and_update_deadline_from_now(now + m_expectation.max).count() > 0) {
        if (m_logger) {
          APEX_WARN(
            *m_logger,
            m_log_prefix,
            "Time distance between 2 activations should have been below:",
            apex::to_string(m_expectation.max),
            ", measured:",
            apex::to_string(std::chrono::duration_cast<std::chrono::microseconds>(distance)));
        }
      }
    }
    // Note that the task has run now (i.e., that the minimum should be checked)
    m_check_min = true;
  }

private:
  const expectations::activation_distance m_expectation;
  /// The time of the last activation
  time_point m_last_activation{};
  /// Has the task run before?
  bool m_check_min{false};
};

namespace detail
{
template <class T, class clock_type>
struct mapping;

template <class clock_type>
struct mapping<expectations::wall_clock_runtime, clock_type>
{
  using type = wall_clock_runtime_monitor<clock_type>;
};

template <class clock_type>
struct mapping<expectations::cpu_clock_runtime, clock_type>
{
  using type = cpu_clock_runtime_monitor<clock_type>;
};

template <class clock_type>
struct mapping<expectations::activations_per_window, clock_type>
{
  using type = activations_per_window_monitor<clock_type>;
};

template <class clock_type>
struct mapping<expectations::activation_distance, clock_type>
{
  using type = activation_distance_monitor<clock_type>;
};

template <class T, class clock_type>
using mapping_t = typename mapping<T, clock_type>::type;

template <class clock_type>
struct monitor_visitor
{
  monitor_visitor(monitor_slot & slot, logger_ptr logger, std::string description)
  : m_slot{&slot}, logger{logger}, desc{std::move(description)}
  {
  }
  /// \brief Allocates a monitor for an expectation
  /// \tparam clock_type The clock type used for monitor deadlines
  /// \cert
  template <class T>
  std::unique_ptr<expectation_monitor_base> operator()(const T & expectation) const
  {
    using monitor_t = mapping_t<T, clock_type>;
    return std::make_unique<monitor_t>(expectation, *m_slot, logger, std::move(desc));
  }

private:
  monitor_slot * m_slot;
  logger_ptr logger;
  std::string desc;
};
}  // namespace detail

/// \brief Allocates a monitor for an arbitrary expectation
/// \tparam clock_type The clock type used for monitor deadlines
/// \cert
template <typename clock_type = std::chrono::steady_clock>
std::unique_ptr<expectation_monitor_base> make_expectations_monitor(
  const expectations::any_expectation & expectation,
  monitor_slot & slot,
  apex::logging::Logger<> * logger = nullptr,
  std::string description = "")
{
  detail::monitor_visitor<clock_type> visitor{slot, logger, description};
  return apex::visit(visitor, expectation);
}

}  // namespace details
}  // namespace execution_monitor
}  // namespace apex

#endif  // EXECUTION_MONITOR__DETAILS_EXPECTATION_MONITORS_HPP
