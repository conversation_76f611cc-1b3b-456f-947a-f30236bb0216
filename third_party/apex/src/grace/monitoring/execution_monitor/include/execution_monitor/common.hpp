/// \copyright Copyright 2024 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTION_MONITOR__COMMON_HPP
#define EXECUTION_MONITOR__COMMON_HPP

#include <string>

#include <execution_monitor/expectation_types.hpp>

namespace apex::execution_monitor::common
{
/// \brief Default execution monitor service node name
constexpr const char * DefaultServiceNodeName = "_apex_execution_monitor_service";

/// \brief Default execution monitor client node name
constexpr const char * DefaultClientNodeName = "_apex_execution_monitor_client";

/// \brief The default service name for registrations of expectations in the backend
constexpr const char * RegistrationServiceName = "apex_execution_monitor_registration";

/// \brief The default service name for unregistrations of expectations in the backend
constexpr const char * UnregistrationServiceName = "apex_execution_monitor_unregistration";

/// \brief Create a sub-service name for an instance of a service
inline std::string combine_name(const std::string & service_name, const std::string & sub_name)
{
  return service_name.empty() ? sub_name : (service_name + "_" + sub_name);
}

/// \brief Get the service node name
inline std::string get_executor_service_node_name(const std::string & instance_name)
{
  return instance_name.empty() ? DefaultServiceNodeName : instance_name;
}
}  // namespace apex::execution_monitor::common

#endif  // EXECUTION_MONITOR__COMMON_HPP
