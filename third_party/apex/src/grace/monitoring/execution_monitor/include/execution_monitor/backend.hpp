/// \copyright Copyright 2022 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTION_MONITOR__BACKEND_HPP
#define EXECUTION_MONITOR__BACKEND_HPP

#include <memory>
#include <string>
#include <utility>

#include "allocator/memory_pool_resource.hpp"
#include "allocator/pmr/map.hpp"
#include "containers/static_vector.hpp"
#include "execution_monitor/monitor_slot.hpp"
#include "execution_monitor/task_identifier.hpp"
#include "execution_monitor/visibility.hpp"
#include "iceoryx_hoofs/posix_wrapper/file_lock.hpp"
#include "ida/base/core/dynamic_allocator.hpp"
#include "ida/plexus/resource_acquisition/shmem_acquisition.hpp"

namespace apex::execution_monitor
{
/// \brief Exception related to the backend.
class EXECUTION_MONITOR_PUBLIC backend_error : public apex::runtime_error
{
  using apex::runtime_error::runtime_error;
};

/// \brief `backend` manages the memory shared between backend and
/// execution monitors
///
/// It maintains a mapping task->memory segment, provides iteration primitives
/// to walk through all available slots, and maps slots back to task IDs
class EXECUTION_MONITOR_PUBLIC backend final
{
public:
  class shmem_segment_iterator;
  /// @cond DOXYGEN_IGNORE_TO_AVOID_DUPLICATE_ENTRY
  friend shmem_segment_iterator;
  /// @endcond DOXYGEN_IGNORE_TO_AVOID_DUPLICATE_ENTRY

  /// \brief Create the backend and pre-allocate shared-memory segments
  /// \param max_monitors An upper bound on the number of monitors that will be registered
  /// \param max_tasks An upper bound on the number of tasks that will be registered
  /// \param unique_prefix A unique string prefix of the instance
  explicit backend(std::size_t max_monitors,
                   std::size_t max_tasks,
                   const std::string & unique_prefix = "");
  ~backend();

  backend(const backend &) = delete;
  backend & operator=(const backend &) = delete;

private:
  base::optional<base::dynamic_allocator> m_allocator;
  base::optional<plexus::ShmemAcquisition> m_mem_acquisition;

  /// \brief File lock ensuring that only one shm_handler instance exists on a single system
  std::unique_ptr<iox::posix::FileLock> m_file_lock;

  /// \brief A pseudo-random shared-memory segment name. This segment is created during startup
  ///        and its name is contained in each registration reply. The frontend can open this
  ///        segment to ensure that it runs on the same host and sees the same shared-memory
  ///        segments as the backend
  iox::FileName m_backend_token_segment_name;
  base::optional<plexus::ShmemAcquisition::TypedSegment<std::byte>> m_backend_token_segment;

  using MonitorDataSegment = plexus::ShmemAcquisition::TypedSegment<monitor_slot>;
  using SlotsSpan = MonitorDataSegment::ElementsSpan;

  /// \brief A shared-memory segment which can allocate monitoring data in slots.
  class data_slots
  {
  public:
    explicit data_slots(MonitorDataSegment data_segment);
    data_slots(data_slots &) = delete;
    data_slots & operator=(data_slots &) = delete;

    data_slots(data_slots &&) = default;
    data_slots & operator=(data_slots &&) = default;

    const iox::FileName & segment_name() const noexcept;

    /// \brief Returns the number of active (i.e., handed-out) slots in the
    /// current segment.
    const SlotsSpan active_slots() const noexcept;
    SlotsSpan active_slots() noexcept;

    std::size_t num_free_slots() const noexcept;

    /// \brief Allocates a consecutive range of slots in the segment
    /// \param num_slots The number of slots to allocate
    /// \return span of allocated slots
    /// \throws `apex::bad_alloc` if the allocation fails
    SlotsSpan allocate_slots(const std::size_t num_slots);

    /// \brief Put the segment into use
    void deploy();

    /// \brief Release the segment from use
    void release();

    bool in_use() const
    {
      return m_in_use;
    }

  private:
    // The managed shared memory segment
    MonitorDataSegment m_segment;

    // Flag showing if this segment is currently in use by the backend
    bool m_in_use{false};

    // Span of active (allocated) slots in the segment
    SlotsSpan m_active_slots;
  };  // struct data_slots

  /// \brief Pool of all segments
  /// \note Use `allocate_segment_from_pool` and `return_segment_to_pool` to
  ///       obtain and return a pointer from this pool, respectively.
  apex::static_vector<data_slots> m_segments;

  /// \brief Allocates an unused shared-memory segment from the pool
  data_slots * allocate_segment_from_pool();

  /// \brief Returns a shared-memory segment to the pool
  void return_segment_to_pool(data_slots *);

  /// \brief A pre-allocated memory pool from which `m_segments` entries are
  /// allocated
  apex::allocator::pmr::memory_pool_resource m_monitor_to_segment_memres;

  using PidAndName = std::pair<std::int32_t, apex::string256_t>;
  using MonitorsMap = apex::allocator::pmr::map<PidAndName, data_slots *>;

  /// \brief Maps execution monitors to their allocated segments.
  /// \note It is important that this map does not invalidate iterators during insertion
  MonitorsMap m_monitor_to_segment{m_monitor_to_segment_memres};

public:
  /// \brief Describes an allocated range in a shmem segment
  struct shm_range
  {
    shm_range() = default;
    shm_range(apex::string_view filename, std::uint32_t first_slot, std::uint32_t len)
    : filename(filename), first_slot(first_slot), len(len)
    {
    }
    /// \brief The name of the shared-memory segment to open using Ida's Resource Acquisition.
    /// \note The view is valid as long as the corresponding task is registered
    apex::string_view filename;
    /// \brief The index of the first slot
    std::uint32_t first_slot{};
    /// \brief The number of allocated slots
    std::uint32_t len{};
  };

private:
  /// \brief A pre-allocated memory pool from which `m_allocated_ranges` entries are
  /// allocated
  apex::allocator::pmr::memory_pool_resource m_allocated_ranges_memres;
  /// \brief A map from segment pointers to the task id + range allocated into this segment.
  ///        This is only used for error reporting, to determine which task owns
  ///        an infracting slot
  using allocated_ranges_t =
    apex::allocator::pmr::multimap<data_slots *, std::pair<task_identifier::task_id_t, shm_range>>;

  allocated_ranges_t m_allocated_ranges{m_allocated_ranges_memres};

public:
  /// \brief Registers a new execution-monitor task.
  /// \param task The task identifier to register
  /// \param num_slots The number of expectation slots required for the task
  /// \returns The allocated range as a `shmem_range` struct
  /// \throws backend_error If the registration failed.
  /// \note This does not invalidate the iterator returned by `segments_begin`
  /// \cert
  /// \deterministic
  shm_range register_execution_monitor_task(task_identifier task, std::uint32_t num_slots);

  /// \brief De-registers all tasks associated with the given monitor
  /// \param pid The PID of the monitor to de-register
  /// \param monitor The name of the monitor
  /// \throws backend_error If the de-registration failed.
  /// \note This does not invalidate the iterator returned by `segments_begin`
  /// \cert
  /// \deterministic
  void unregister_execution_monitor(std::int32_t pid, const apex::string256_t & monitor);

  /*
   AXIVION Next Codeline MisraC++2023-6.8.4: Reason: Code Quality (Functional suitability),
   Justification:
   Allow member function call from r-value reference (temporary objects) for usability.
   The test cases with high code coverage should detect potential problems.
   */
  const iox::FileName & get_backend_token_segment_name() const noexcept
  {
    return m_backend_token_segment_name;
  }

  /// \brief Returns an iterator to the contained segments
  /// \cert
  /// \deterministic
  shmem_segment_iterator segments_begin() const noexcept
  {
    return shmem_segment_iterator(m_monitor_to_segment.begin(), *this);
  }

  /// \brief Returns an end iterator to the contained segments
  /// \cert
  /// \deterministic
  shmem_segment_iterator segments_end() const noexcept
  {
    return shmem_segment_iterator(m_monitor_to_segment.end(), *this);
  }

public:
  /// \brief An iterator that visits all shared-memory segments managed by a
  /// given `backend`
  class shmem_segment_iterator
  {
    friend class backend;

    using underlying_iterator = MonitorsMap::const_iterator;
    explicit shmem_segment_iterator(underlying_iterator iterator, const backend & backend) noexcept
    : m_iterator(iterator), m_backend(&backend)
    {
    }

  public:
    /// \brief Given an index into the current segment, returns the associated
    /// task/offset
    ///
    /// \param index A slot index into the current segment.
    ///              The current segment is the one returned by `operator*`
    /// \return The task and expectation number that owns the slots at this
    /// index
    std::pair<const task_identifier, std::uint32_t> task_and_offset_of(std::uint32_t index) const;

    data_slots & operator*() const noexcept;
    data_slots * operator->() const noexcept
    {
      return &*(*this);
    }

    /*
     AXIVION Next Codeline MisraC++2023-6.8.4: Reason: Code Quality (Functional suitability),
     Justification:
     Allow member function call from r-value reference (temporary objects) for usability.
     The test cases with high code coverage should detect potential problems.
     */
    shmem_segment_iterator & operator++() noexcept
    {
      ++m_iterator;
      return *this;
    }

    shmem_segment_iterator operator++(int) noexcept
    {
      const auto copy = *this;
      ++m_iterator;
      return copy;
    }

    bool operator==(const shmem_segment_iterator other) const noexcept
    {
      return m_iterator == other.m_iterator;
    }
    bool operator!=(const shmem_segment_iterator other) const noexcept
    {
      return !(*this == other);
    }

  private:
    underlying_iterator m_iterator;
    const backend * m_backend;
  };  // class shmem_segment_iterator
};  // class backend

}  // namespace apex::execution_monitor

#endif  // EXECUTION_MONITOR__BACKEND_HPP
