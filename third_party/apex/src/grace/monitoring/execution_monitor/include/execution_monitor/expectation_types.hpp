/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTION_MONITOR__DETAILS_EXPECTATION_TYPES_HPP
#define EXECUTION_MONITOR__DETAILS_EXPECTATION_TYPES_HPP

#include <cstdint>
#include <limits>
#include <type_traits>

#include <string/string.hpp>

#include <execution_monitor_msgs/msg/infraction.hpp>

namespace apex::execution_monitor::expectations::types
{

using expectation_type_id_t = execution_monitor_msgs::ExpectationType;

enum class deviation_unit_type
{
  nanoseconds,
  activations,
  unknown
};

inline deviation_unit_type get_deviation_units(expectation_type_id_t type_id) noexcept
{
  switch (type_id) {
    case expectation_type_id_t::WALL_CLOCK_RUNTIME:
    case expectation_type_id_t::CPU_CLOCK_RUNTIME:
    case expectation_type_id_t::ACTIVATION_DISTANCE:
      return deviation_unit_type::nanoseconds;
    case expectation_type_id_t::ACTIVATIONS_PER_WINDOW:
      return deviation_unit_type::activations;
    default:
      return deviation_unit_type::unknown;
  }
}
}  // namespace apex::execution_monitor::expectations::types

#endif  // EXECUTION_MONITOR__DETAILS_EXPECTATION_TYPES_HPP
