/// \copyright Copyright 2022 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTION_MONITOR__MONITOR_SLOT_HPP
#define EXECUTION_MONITOR__MONITOR_SLOT_HPP

#include <atomic>
#include <chrono>
#include <cstdint>
#include <limits>
#include <optional>  // NOLINT
#include <type_traits>

#include "cpputils/optional.hpp"
#include <execution_monitor/expectation_types.hpp>
#include <execution_monitor/visibility.hpp>

namespace apex::execution_monitor
{

/// \brief Stores information about observed infractions.
struct EXECUTION_MONITOR_PUBLIC infractions
{
  /// \brief The type of the expectation
  expectations::types::expectation_type_id_t expectation_type{
    expectations::types::expectation_type_id_t::UNDEFINED};
  /// \brief The number of infractions observed (`infraction counter + (dl in the past ? 1 : 0)`)
  std::uint32_t counter{};
  /// \brief Indicates the deviation from the expectation
  /// Can be negative for deviations from the minimum boundary
  std::int64_t deviation{};
  /// \brief A positive value indicates that the deadline infraction was discovered on
  /// the backend side, i.e. the task had no chance to report the infraction itself
  std::chrono::nanoseconds backend_overrun{};
};

/**
 * @brief Holds the monitor data for a single task
 * @note This class must be placeable in the shared memory - i.e. it cannot contain any pointers or
 * virtual functions.
 */
struct monitor_slot final
{
  /// \brief The type of the duration period used
  using duration = std::chrono::nanoseconds;
  /// \brief The type of the infractions counter
  using infractions_counter_type = std::uint8_t;

  /// \brief The largest possible deadline value, which signals infinity
  static constexpr auto infinity = duration::max();

  /// \brief Perform a saturating add up to the clamp/maximum allowed value.
  /// \note Once this value is reached, further infractions will not be counted any more.
  void increment_infraction_counter() noexcept;

  /// \brief Updates the deadline and increases the infraction counter if the previous deadline
  /// is in the past
  /// \param new_value new deadline value in nanoseconds
  /// \param now current time as duration passed since epoch
  /// \param deviation_override Optional deviation override value instead of the default
  /// which is to set the deviation to the deadline overrun in nanoseconds
  /// \return The deadline overrun (negative if there is no overrun)
  duration check_and_update_deadline(duration new_value,
                                     duration now,
                                     optional<std::int64_t> deviation_override);

  /// \brief Updates the deadline and increases the infraction counter if the previous deadline
  /// is in the past
  /// \param new_value new deadline value as a time_point
  /// \param deviation_override Optional deviation override value instead of the default
  /// which is to set the deviation to the deadline overrun in nanoseconds
  /// \return The deadline overrun (negative if there is no overrun)
  template <class Clock, class Duration>
  duration check_and_update_deadline_from_now(
    const std::chrono::time_point<Clock, Duration> & new_value,
    optional<std::int64_t> deviation_override = std::nullopt)
  {
    duration time_point_ns{new_value.time_since_epoch()};
    return check_and_update_deadline(
      time_point_ns, Clock::now().time_since_epoch(), deviation_override);
  }

  /// \brief Updates the deviation value and increases the infraction counter
  /// \param The new deviation value in nanoseconds
  void set_deviation(std::int64_t new_value);

  /// \brief Updates the deviation value and increases the infraction counter
  /// \param The new deviation value in nanoseconds
  void set_deviation(std::chrono::nanoseconds new_value)
  {
    set_deviation(new_value.count());
  }

  /// \brief Checks for deadline infractions given the current time
  /// \note This resets the struct
  /// \param now The current time as duration passed since epoch
  /// \return The detected infractions
  infractions inspect_deadline(duration now);

  /// \brief Reset the monitor data to its initial state
  void reset() noexcept;

  // Expected task completion deadline
  std::atomic<duration> deadline{infinity};
  // Task deviation
  std::atomic<std::int64_t> deviation{0};
  // Contains the infraction counter of the expectation. The
  // infraction counter is incremented whenever the execution monitor
  // notices an infraction of the deadline slot that has not been
  // detected yet by the backend.
  std::atomic<infractions_counter_type> infractions_counter{0};
  // The type of expectation assoicated with this slot
  expectations::types::expectation_type_id_t expectation_type_id{
    expectations::types::expectation_type_id_t::UNDEFINED};
};  // struct monitor_slot

// NB: This is not a strict guarantee, since a pointer is also trivially copyable, yet unsuitable,
// but it seems to be better than nothing.
static_assert(std::is_trivially_copyable_v<monitor_slot>,
              "monitor_slot must be trivially copyable to be used in shared memory");

// TODO(tobias.stark): might be different depending on the platform
static constexpr std::uint32_t SHM_PAGE_SIZE = 4096U;
static constexpr std::uint32_t SLOTS_PER_SEGMENT = SHM_PAGE_SIZE / sizeof(monitor_slot);

}  // namespace apex::execution_monitor

#endif  // EXECUTION_MONITOR__MONITOR_SLOT_HPP
