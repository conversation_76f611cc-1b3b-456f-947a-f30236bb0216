/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTION_MONITOR_DETAILS__UTILS_HPP
#define EXECUTION_MONITOR_DETAILS__UTILS_HPP

#include <type_traits>

#include <iox/file_name.hpp>
#include <string/string.hpp>

namespace apex::execution_monitor::details
{

/// \brief Copies a string into an `iox::FileName`, throwing in case of overflow
iox::FileName to_iox_name(const char * str);

/// \brief Copies a string into an `iox::FileName`.
/// \note This function cannot fail, as the `FileName` is guaranteed to have enough capacity
template <size_t N,  // NOLINT lint confuses the < for an operator
          std::enable_if<N <= iox::platform::IOX_MAX_FILENAME_LENGTH> * = nullptr>
inline iox::FileName to_iox_name(const apex::string<N> & str) noexcept
{
  // Due to the enable_if, this call cannot throw.
  return to_iox_name(str.c_str());
}

}  // namespace apex::execution_monitor::details

#endif  // EXECUTION_MONITOR_DETAILS__UTILS_HPP
