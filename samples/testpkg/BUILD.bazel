load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test")
load("@python_vendor//:requirements.bzl", "requirement")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "cpp_msgs_introspection_library", "py_msgs_library")

cc_library(
    name = "publisher_subscriber",
    srcs = [
        "src/testpkg_publisher.cpp",
        "src/testpkg_subscriber.cpp",
    ],
    hdrs = [
        "include/testpkg/testpkg_publisher.hpp",
        "include/testpkg/testpkg_subscriber.hpp",
        "include/testpkg/visibility_control.hpp",
    ],
    strip_include_prefix = "include",
    deps = [
        "//samples/t2_test_msgs",
        "@apex//common/cpputils",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/monitoring/logging",
        "@apex//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "pub_exe",
    srcs = ["src/pub_main.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        "publisher_subscriber",
        "//src/common/health:health_reporter",
        "@com_google_absl//absl/debugging:failure_signal_handler",
    ],
)

cc_binary(
    name = "sub_exe",
    srcs = ["src/sub_main.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        "publisher_subscriber",
        "@com_google_absl//absl/debugging:failure_signal_handler",
    ],
)

process_manager(
    name = "testpkg",
    data = [
        ":param/execution_monitor_service.yaml",
        ":pub_exe",
        ":sub_exe",
        "@apex//grace/monitoring/execution_monitor_service:execution_monitor_service_exe",
    ],
    launch_file = "launch/testpkg.launch.yaml",
    visibility = ["//visibility:public"],
)

apex_cc_test(
    name = "test_pub_sub",
    size = "small",
    srcs = [
        "test/test_pub.cpp",
        "test/test_sub.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":publisher_subscriber",
        "//samples/t2_test_msgs",
        "@apex//grace/tools/apex_integration_test_node",
        "@googletest//:gtest_main",
    ],
)

# Define introspection message libraries for launch tests
cpp_msgs_introspection_library(
    name = "t2_msgs_cpp_introspection",
    ament_runfiles = True,
    msgs = "//samples/t2_test_msgs",
)

# Define python message libraries for launch tests
py_msgs_library(
    name = "t2_msgs_py",
    msgs = "//samples/t2_test_msgs",
)

launch_test(
    name = "pub_launch_test",
    data = [
        ":pub_exe",
        ":t2_msgs_cpp_introspection",
    ],
    launch_test_file = "test/testpkg_pub_launch.test.py",
    deps = [
        requirement("pytest"),
        ":t2_msgs_py",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//tools/apex_pytest_utils",
        "@apex//tools/launch/launch",
    ],
)
