import unittest
from datetime import datetime, timedelta

import launch_ros.actions
import launch_testing
import launch_testing.actions
import rclpy
from launch.actions import ExecuteProcess
from rclpy.node import Node
from t2_test_msgs.msg import MyInfo


def generate_test_description():

    record = False  # Set to True to record the messages

    pub = launch_ros.actions.Node(executable="samples/testpkg/pub_exe", output="screen")
    recorder = ExecuteProcess(cmd=["src/system/cli/cli", "bag", "record", "-a"])

    actions = []
    actions.append(pub)
    if record:
        actions.append(recorder)
    actions.append(launch_testing.actions.ReadyToTest())

    return launch_testing.LaunchDescription(actions)


class ReceiverNode(Node):
    """Receiver node to collect the processed messages."""

    def __init__(self, name, topic):
        super().__init__(name)
        self.sub = self.create_subscription(MyInfo, topic, self.callback, 10)
        self.received_data = []

    def callback(self, msg):
        self.received_data.append(msg.source_node_name)


class TestNode(unittest.TestCase):

    @classmethod
    def setUpClass(cls, launch_service, proc_info, proc_output):
        rclpy.init(args=None)

    def test_deterministic_execution(self):
        test_node = ReceiverNode(name="receiver_node", topic="/testpkg_namespace/chatter")
        num_messages_to_wait_for = 4
        expected_data = [f"Hello, world! {i}" for i in range(num_messages_to_wait_for)]
        start_time = datetime.now()
        wait_limit = timedelta(seconds=5)
        while len(test_node.received_data) < len(expected_data):
            if datetime.now() - start_time > wait_limit:
                raise Exception("Test timed out!")
            rclpy.spin_once(test_node, timeout_sec=0.5)
        print(test_node.received_data)
        self.assertTrue(expected_data == test_node.received_data, "Replay was not deterministic")
