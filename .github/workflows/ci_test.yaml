name: 'CI - Test'

on:
  pull_request:
  push:
    branches:
      - develop

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ !startsWith(github.ref, 'refs/heads/devel') && !startsWith(github.ref, 'refs/heads/release') }}

jobs:
  create-gce-runner-for-test:
    runs-on: ubuntu-22.04
    permissions:
      contents: read
      id-token: write
      pull-requests: read
    outputs:
      label: ${{ steps.runner.outputs.label }}
      test_perception_detector:
        ${{ steps.check-labels.outputs.test_perception_detector }}
      compute_detector_metrics:
        ${{ steps.check-labels.outputs.compute_detector_metrics }}
    steps:
      - uses: actions/checkout@v4
      - name: Get GitHub Apps token for checkout
        id: app-token
        # fixing the commit hash for security
        # v1.8.0
        uses: tibdex/github-app-token@b62528385c34dbc9f38e5f4225ac829252d1ea92
        with:
          app_id: ${{ secrets.GHA_HELPER_APP_ID }}
          private_key: ${{ secrets.GHA_HELPER_PRIVATE_KEY }}
      - name: Get PR Labels
        id: get-labels
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
        run: |
          if [ "${{ github.event_name }}" = "push" ]; then
            PR_NUMBER=$(gh pr list --state merged --base main --json number --jq '.[0].number')
          else
            PR_NUMBER=$(jq --raw-output .pull_request.number "$GITHUB_EVENT_PATH")
          fi
          labels=$(gh pr view "$PR_NUMBER" --json labels | jq -c '.labels | map(.name)')
          echo "labels=$labels" >> "$GITHUB_OUTPUT"
      - name: Check for specific label
        id: check-labels
        shell: bash
        env:
          LABELS: ${{ steps.get-labels.outputs.labels }}
        run: |
          for i in $(seq 0 $(($(echo "$LABELS" | jq length) - 1)))
          do
            label=$(echo "$LABELS" | jq -r .[$i])

            # check if gpu label is present
            if [ "$label" = "use-gpu" ]; then
              echo "gpu=true" >> $GITHUB_OUTPUT
            fi

            # check if test-perception-detector label is present
            if [ "$label" = "test-perception-detector" ]; then
              echo "gpu=true" >> $GITHUB_OUTPUT
              echo "test_perception_detector=true" >> "$GITHUB_OUTPUT"
            fi

            if [ "$label" = "compute-detector-metrics" ]; then
              echo "gpu=true" >> $GITHUB_OUTPUT
              echo "compute_detector_metrics=true" >> "$GITHUB_OUTPUT"
            fi
          done
      - id: filter
        uses: dorny/paths-filter@v2 # https://github.com/dorny/paths-filter
        with:
          filters: |
            gpu:
              - 'src/common/cuda/*'
              - 'src/drivers/camera/*'
      - name: Configure Runner machine spec
        id: vmspec
        run: |
          if [ "${{ steps.check-labels.outputs.gpu }}" = "true" ] || [ "${{ steps.filter.outputs.gpu }}" = "true" ]; then
            echo "machinetype='g2-standard-16'" >> $GITHUB_OUTPUT
            echo "accelerator='count=1,type=nvidia-l4'" >> $GITHUB_OUTPUT
          else
            echo "machinetype='n2-highmem-8'" >> $GITHUB_OUTPUT
            echo "accelerator=''" >> $GITHUB_OUTPUT
          fi
          if [ "${{ steps.check-labels.outputs.compute_detector_metrics }}" = "true" ]; then
            echo "vm_disk_size=200" >> $GITHUB_OUTPUT  # 200Gi needed for the detector dataset
          else
            echo "vm_disk_size=100" >> $GITHUB_OUTPUT  # 100Gi is enough for other cases
          fi
      - uses: t2-auto/gce-github-runner-action@main
        id: runner
        with:
          # VM specs:
          #   - without GPU
          #     - machinetype: n2-highmem-8 (8 vCPUs, 64Gi Mem)
          #     - accelerator: none
          #   - with GPU
          #     - machinetype: g2-standard-16 (16 vCPUs, 64Gi Mem)
          #     - accelerator: count=1,type=nvidia-l4
          machine_type: ${{ steps.vmspec.outputs.machinetype }}
          accelerator: ${{ steps.vmspec.outputs.accelerator }}
          image: gha-image-20240604
          disk_size: ${{ steps.vmspec.outputs.vm_disk_size }}
          boot_disk_type: pd-ssd # use SSD
          gha_helper_app_id: ${{ secrets.GHA_HELPER_APP_ID }}
          gha_helper_private_key: ${{ secrets.GHA_HELPER_PRIVATE_KEY }}
          preemptible: 'false'
          instance_termination_action_delete: ''
          # The default zone is asia-northeast1-a, but since the g2-standard-16+nvidia-l4 resources cannot be secured in that zone, asia-northeast1-b is being used instead.
          machine_zone: 'asia-northeast1-b'

  test:
    needs: create-gce-runner-for-test
    runs-on: ${{ needs.create-gce-runner-for-test.outputs.label }}
    permissions:
      id-token: write
      contents: write
      issues: write
      pull-requests: write
    env:
      ADE_HOME: ${{ github.workspace }}
      HOME: ${{ github.workspace }}
    steps:
      - name: Get GitHub Apps token for checkout
        id: app-token
        # fixing the commit hash for security
        # v1.8.0
        uses: tibdex/github-app-token@b62528385c34dbc9f38e5f4225ac829252d1ea92
        with:
          app_id: ${{ secrets.GHA_HELPER_APP_ID }}
          private_key: ${{ secrets.GHA_HELPER_PRIVATE_KEY }}
      - name: checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive
          token: ${{ steps.app-token.outputs.token }}
          path: Yatagarasu

      - name: Set up Python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6 as of 2025-06-23
        with:
          python-version: '3.10' # Use Python 3.10 as required by DVC

      - name: Install DVC via pip
        run: |
          python -m pip install --upgrade pip
          pip install dvc[gs]==3.59.2

      - uses: google-github-actions/auth@v1
        with:
          workload_identity_provider: |-
            projects/************/locations/global/workloadIdentityPools/gha-pool/providers/github-com
          service_account: <EMAIL>
          export_environment_variables: true
      - uses: 'google-github-actions/setup-gcloud@v1'

      - name: Setup Netrc
        run: |
          echo "${{ secrets.NETRC_CONTENT }}" > ~/.netrc
          echo "" >> ~/.netrc

      - name: Setup GitHub CLI
        if: |
          ${{ needs.create-gce-runner-for-test.outputs.test_perception_detector ||
              needs.create-gce-runner-for-test.outputs.compute_detector_metrics }}
        uses: sersoft-gmbh/setup-gh-cli-action@v2

      - name: Setup gcloud
        run: |
          gcloud auth configure-docker
          gcloud auth configure-docker asia-docker.pkg.dev --quiet
          gcloud auth configure-docker asia-northeast1-docker.pkg.dev --quiet
          gcloud info

      - name: Download DVC cache
        run: dvc pull
        working-directory: Yatagarasu

      - name: Setup ADE
        run: |
          wget https://gitlab.com/ApexAI/ade-cli/-/jobs/**********/artifacts/raw/dist/ade+x86_64
          mv ade+x86_64 ade
          chmod +x ade
          ./ade --version

      - name: Start ADE
        run: |
          ./ade --rc Yatagarasu/.aderc start --update

      - name: Build Yatagarasu
        run: |
          docker exec -u root \
            -e HOME=/home/<USER>
            -e GOOGLE_APPLICATION_CREDENTIALS=/home/<USER>/$(basename $GOOGLE_APPLICATION_CREDENTIALS) \
            desktop \
            bash -c \
              "source /etc/profile && source /etc/profile.d/*.sh && \
               cd /home/<USER>/Yatagarasu && \
               bazel build \
                 --remote_cache=https://storage.googleapis.com/t2-yata-build-cache/t2 \
                 --google_credentials=/home/<USER>/$(basename $GOOGLE_APPLICATION_CREDENTIALS) \
                 //..."

      - name: Run unit tests
        shell: bash
        run: |
          docker exec -u root \
            -e HOME=/home/<USER>
            -e GOOGLE_APPLICATION_CREDENTIALS=/home/<USER>/$(basename $GOOGLE_APPLICATION_CREDENTIALS) \
            desktop \
            bash -c \
              "source /etc/profile && source /etc/profile.d/*.sh && \
               cd /home/<USER>/Yatagarasu && \
               bazel test \
                 --remote_cache=https://storage.googleapis.com/t2-yata-build-cache/t2 \
                 --google_credentials=/home/<USER>/$(basename $GOOGLE_APPLICATION_CREDENTIALS) \
                 //..."

      - name: Stop ADE
        if: ${{ always() }}
        run: |
          ./ade --rc Yatagarasu/.aderc stop

  actions-timeline:
    needs: [create-gce-runner-for-test, test]
    runs-on: ubuntu-22.04
    permissions:
      actions: read
    steps:
      - uses: Kesin11/actions-timeline@v2
