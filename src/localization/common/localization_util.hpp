// Copyright (c) 2024-2025 T2 Inc. All rights reserved.

#pragma once

#include <memory>
#define ACCEPT_USE_OF_DEPRECATED_PROJ_API_H
#include <proj.h>
#include <proj_api.h>

#include <cmath>
#include <cstdint>
#include <iomanip>
#include <iostream>
#include <sstream>

#include <geometry_msgs/msg/point.hpp>                           //Point
#include <geometry_msgs/msg/quaternion.hpp>                      //Quaternion
#include <localization_msgs/msg/covariance6x6.hpp>               //Covariance6x6
#include <localization_msgs/msg/hd_map_localization_status.hpp>  //HDMapLocalizationStatus
#include <localization_msgs/msg/localization_estimate.hpp>       //LocalizationEstimate
#include <localization_msgs/msg/pose.hpp>                        //Pose
#include <sophus/geometry.hpp>
#include <sophus/sim3.hpp>

#include "Eigen/Geometry"
#include "src/common/math/quaternion.hpp"
#include "src/common/transform/transform.hpp"

namespace t2::localization::common {

using Status = ::localization_msgs::msg::HDMapLocalizationStatus;
using LocalizationEstimate = ::localization_msgs::msg::LocalizationEstimate;
using Pose = ::localization_msgs::msg::Pose;
using Point3D = ::geometry_msgs::msg::Point;
using Quaternion = ::geometry_msgs::msg::Quaternion;
using Covariance6x6 = ::localization_msgs::msg::Covariance6x6;
using Transform = ::t2::common::transform::Transform;

inline std::uint32_t UtmZoneId(double longitude_deg, double latitude_deg) {
  if ((56. <= longitude_deg && longitude_deg < 64. && 0. <= latitude_deg && latitude_deg < 12.) ||
      (72. <= longitude_deg && longitude_deg < 84. && 0. <= latitude_deg && latitude_deg < 42.)) {
    std::cerr << "Not supported zone. Fix calculation. " << latitude_deg << std::endl;
    abort();
  }
  return static_cast<std::uint32_t>((180. + longitude_deg) / 6) + 1;
}

// Tokyo long and lat
const int StandardUtmZoneId = UtmZoneId(139.7690, 35.6804);

struct ProjectionFactor {
  double grid_convergence;
  double scale_factor;
};

struct Ecef {
  double longitude_deg;
  double latitude_deg;
  double altitude;
};

class Projection {
 public:
  Projection();
  ~Projection();

  // Ttm means Taas Transverse Mercator
  Eigen::Vector3d TransformFromEcefToTtm(const Ecef& ecef) const;

  Ecef TransformFromTtmToEcef(const Eigen::Vector3d& ttm) const;

  ProjectionFactor ComputeFactors(double longitude_deg, double latitude_deg) const;

 private:
  const char* WGS84_TEXT = "+proj=latlong +ellps=WGS84";

  projPJ wgs84pj_;
  projPJ utm_;
};  // namespace localization_util

/**
 * @brief Set rotation in TTM coordinate syatem with Roll Pitch Yaw angle and
 * grid_convergence
 * @param pose a target to set a rotation
 * @param roll_deg an angle in the roll direction in FRU coordinate syatem
 * (degree)
 * @param pitch_deg an angle in the pitch direction in FRU coordinate syatem
 * (degree)
 * @param yaw_deg an angle in the yaw direction in FRU coordinate syatem
 * (degree)
 * @param grid_convergence a difference angle between TTM and FRU in yaw
 * direction (rad)
 */
void SetRollPitchYaw(Pose& pose, const double roll_deg, const double pitch_deg,
                     const double yaw_deg, const double grid_convergence);

void SetPosition(Pose& pose, const Projection& projection, const Ecef& ecef);

Eigen::Vector3d ToEigen(const Point3D& value);

Eigen::Quaterniond ToEigen(const Quaternion& a);

Sophus::Sim3d ToSim3(const Pose& a);

Sophus::Sim3d ToSim3(const LocalizationEstimate& a);

Sophus::Sim3d ToSim3(const Transform& tf);

void CopyPose(LocalizationEstimate& dst, const Sophus::Sim3d& src);

std::shared_ptr<LocalizationEstimate> ConvertToLocalizationEstimate(
    const Sophus::Sim3d& pose, const LocalizationEstimate& orig_msg);

void CopyCameraPose(Status& dst, const Sophus::Sim3d& src);

std::shared_ptr<LocalizationEstimate> CreateLocalizationEstimateFromSim3d(
    const Sophus::Sim3d& pose);

std::vector<double> InterpolateValues(const std::vector<double>& cumulative_distances,
                                      const std::vector<double>& values,
                                      const std::vector<double>& new_distances);

void SetCovariance(Covariance6x6& dst, const Eigen::Matrix3d& position_covariance,
                   const Eigen::Vector3d& rot_std_dev);

}  // namespace t2::localization::common
