// Copyright (c) 2024-2025 T2 Inc. All rights reserved.

#include "localization_util.hpp"

#include <memory>

namespace t2::localization::common {

Projection::Projection() {
  wgs84pj_ = pj_init_plus(WGS84_TEXT);

  std::stringstream utm_string;
  utm_string << "+proj=utm +zone=" << StandardUtmZoneId
             << " +ellps=WGS84 +towgs84=0,0,0,0,0,0,0 +units=m "
                "+no_defs";
  utm_ = pj_init_plus(utm_string.str().c_str());
}

Projection::~Projection() {
  pj_free(wgs84pj_);
  pj_free(utm_);
}

Eigen::Vector3d Projection::TransformFromEcefToTtm(const Ecef& ecef) const {
  double x = ecef.longitude_deg * DEG_TO_RAD;
  double y = ecef.latitude_deg * DEG_TO_RAD;
  pj_transform(wgs84pj_, utm_, 1, 1, &x, &y, NULL);

  const auto factors = ComputeFactors(ecef.longitude_deg, ecef.latitude_deg);

  return Eigen::Vector3d{x, y, ecef.altitude * factors.scale_factor};
}

Ecef Projection::TransformFromTtmToEcef(const Eigen::Vector3d& ttm) const {
  double x = ttm.x();
  double y = ttm.y();
  pj_transform(utm_, wgs84pj_, 1, 1, &x, &y, NULL);
  Ecef ret;
  ret.longitude_deg = x * RAD_TO_DEG;
  ret.latitude_deg = y * RAD_TO_DEG;
  const auto factors = ComputeFactors(ret.longitude_deg, ret.latitude_deg);
  ret.altitude = ttm.z() / factors.scale_factor;
  return ret;
}

ProjectionFactor Projection::ComputeFactors(double longitude_deg, double latitude_deg) const {
  PJ_COORD coord;
  coord.lp.lam = longitude_deg * DEG_TO_RAD;
  coord.lp.phi = latitude_deg * DEG_TO_RAD;

  const auto factors = proj_factors(utm_, coord);

  return ProjectionFactor{factors.meridian_convergence, factors.meridional_scale};
}

void SetRollPitchYaw(Pose& pose, const double roll_deg, const double pitch_deg,
                     const double yaw_deg, const double grid_convergence) {
  const double roll = roll_deg * DEG_TO_RAD;
  const double pitch = pitch_deg * DEG_TO_RAD;

  // Yaw angle and angle in TTM are flipped
  // PosLV's yaw is clockwise
  const double yaw = -yaw_deg * DEG_TO_RAD;

  // orientation is FRU
  const Eigen::Quaterniond q = Eigen::AngleAxisd(yaw + grid_convergence, Eigen::Vector3d::UnitZ()) *
                               Eigen::AngleAxisd(pitch, Eigen::Vector3d::UnitX()) *
                               Eigen::AngleAxisd(roll, Eigen::Vector3d::UnitY());

  pose.orientation.x = q.x();
  pose.orientation.y = q.y();
  pose.orientation.z = q.z();
  pose.orientation.w = q.w();

  // Heading is ENU counter clockwise (0 - East, positive - North)
  pose.heading = t2::common::math::QuaternionToHeadingZXY(q);

  // Set raw value for debug
  pose.poslv_yaw = yaw_deg;
}

void SetPosition(Pose& pose, const Projection& projection, const Ecef& ecef) {
  const auto pos = projection.TransformFromEcefToTtm(ecef);

  pose.position.x = pos.x();
  pose.position.y = pos.y();
  pose.position.z = pos.z();
}

Eigen::Vector3d ToEigen(const Point3D& value) { return Eigen::Vector3d{value.x, value.y, value.z}; }

Eigen::Quaterniond ToEigen(const Quaternion& a) { return {a.w, a.x, a.y, a.z}; }

Sophus::Sim3d ToSim3(const Pose& a) {
  const auto& t = a.position;
  auto scale = 1.0;
  if (a.scale != 0.0) {
    scale = a.scale;
  }

  return Sophus::Sim3d(Sophus::RxSO3d{scale, Sophus::SO3d{ToEigen(a.orientation)}},
                       Eigen::Vector3d{t.x, t.y, t.z});
}

Sophus::Sim3d ToSim3(const LocalizationEstimate& a) { return ToSim3(a.pose); }

Sophus::Sim3d ToSim3(const Transform& tf) {
  auto origin = tf.getOrigin();
  auto quat = tf.getRotation();
  auto eigen_quat = Eigen::Quaterniond{quat.w(), quat.x(), quat.y(), quat.z()};

  return Sophus::Sim3d(Sophus::RxSO3d{tf.getScale(), Sophus::SO3d{eigen_quat.normalized()}},
                       Eigen::Vector3d{origin.x(), origin.y(), origin.z()});
}

void CopyPose(LocalizationEstimate& dst, const Sophus::Sim3d& src) {
  dst.pose.position.x = src.translation().x();
  dst.pose.position.y = src.translation().y();
  dst.pose.position.z = src.translation().z();

  dst.pose.orientation.w = src.quaternion().w();
  dst.pose.orientation.x = src.quaternion().x();
  dst.pose.orientation.y = src.quaternion().y();
  dst.pose.orientation.z = src.quaternion().z();

  dst.pose.scale = src.scale();
}

std::shared_ptr<LocalizationEstimate> ConvertToLocalizationEstimate(
    const Sophus::Sim3d& pose, const LocalizationEstimate& orig_msg) {
  auto ret = std::make_shared<LocalizationEstimate>(orig_msg);
  CopyPose(*ret, pose);
  return ret;
}

void CopyCameraPose(Status& dst, const Sophus::Sim3d& src) {
  dst.camera_pose.position.x = src.translation().x();
  dst.camera_pose.position.y = src.translation().y();
  dst.camera_pose.position.z = src.translation().z();

  dst.camera_pose.orientation.w = src.quaternion().w();
  dst.camera_pose.orientation.x = src.quaternion().x();
  dst.camera_pose.orientation.y = src.quaternion().y();
  dst.camera_pose.orientation.z = src.quaternion().z();

  dst.camera_pose.scale = src.scale();
}

std::shared_ptr<LocalizationEstimate> CreateLocalizationEstimateFromSim3d(
    const Sophus::Sim3d& pose) {
  auto msg = std::make_shared<LocalizationEstimate>();
  msg->pose.position.x = pose.translation().x();
  msg->pose.position.y = pose.translation().y();
  msg->pose.position.z = pose.translation().z();
  msg->pose.orientation.w = pose.quaternion().w();
  msg->pose.orientation.x = pose.quaternion().x();
  msg->pose.orientation.y = pose.quaternion().y();
  msg->pose.orientation.z = pose.quaternion().z();
  msg->pose.scale = pose.scale();
  return msg;
}

// Helper function to interpolate values
std::vector<double> InterpolateValues(const std::vector<double>& cumulative_distances,
                                      const std::vector<double>& values,
                                      const std::vector<double>& new_distances) {
  std::size_t num_points = new_distances.size();
  std::vector<double> new_values(num_points);
  for (std::size_t i = 0; i < num_points; ++i) {
    double s = new_distances[i];
    auto it = std::lower_bound(cumulative_distances.begin(), cumulative_distances.end(), s);
    std::size_t idx = std::distance(cumulative_distances.begin(), it);

    if (idx == 0) {
      new_values[i] = values[0];
    } else if (idx >= cumulative_distances.size()) {
      new_values[i] = values.back();
    } else {
      double s0 = cumulative_distances[idx - 1];
      double s1 = cumulative_distances[idx];
      double t = (s1 > s0) ? (s - s0) / (s1 - s0) : 0.0;
      new_values[i] = values[idx - 1] + t * (values[idx] - values[idx - 1]);
    }
  }
  return new_values;
}

void SetCovariance(Covariance6x6& dst, const Eigen::Matrix3d& position_covariance,
                   const Eigen::Vector3d& rot_std_dev) {
  // Initialize all to 0.0
  std::fill(std::begin(dst.element), std::end(dst.element), 0.0f);

  // Fill the diagonal with position covariances and rotation variances
  dst.element[0 + 6 * 0] = static_cast<float>(position_covariance(0, 0));
  dst.element[1 + 6 * 1] = static_cast<float>(position_covariance(1, 1));
  dst.element[2 + 6 * 2] = static_cast<float>(position_covariance(2, 2));

  dst.element[3 + 6 * 3] = static_cast<float>(std::pow(rot_std_dev.x(), 2));
  dst.element[4 + 6 * 4] = static_cast<float>(std::pow(rot_std_dev.y(), 2));
  dst.element[5 + 6 * 5] = static_cast<float>(std::pow(rot_std_dev.z(), 2));
}

}  // namespace t2::localization::common
