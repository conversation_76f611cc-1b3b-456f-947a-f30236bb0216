// Copyright (c) 2024-2025 T2 Inc. All rights reserved.

#include "hdmap_localization_component.hpp"

#include <assert.h>

#include <chrono>
#include <cmath>
#include <limits>
#include <sstream>

// #include "t2/adapter/health_reporter.h"

#include "src/common/camera_model/io.hpp"
#include "src/common/contract_assertions/contract_assertions.hpp"
#include "src/common/core/logging.hpp"
#include "src/localization/common/localization_util.hpp"

namespace t2::localization::hdmap {

HDMapLocalizationComponent::HDMapLocalizationComponent(const Config& config)
    : base{"HDMapLocalizationNode"}, config_{config}, transformer_{get_rclcpp_node()} {
  this->Initialize();
}

void HDMapLocalizationComponent::Initialize() {
  T2_INFO << "camera detection component init, starting ...";

  expect_enforce(config_.IsInitialized());

  T2_INFO << "Config file loading complete.";
  T2_INFO << config_.DebugString();

  hdmap_ = std::make_shared<HDMap>();

  hdmap_localization_ = std::make_shared<HDMapLocalization>();

  hdmap_localization_->Init(hdmap_, config_);

  // Publisher
  localization_estimate_publisher_ = get_rclcpp_node().create_publisher<LocalizationEstimate>(
      "/apollo/hdmaplocalization", rclcpp::DefaultQoS());

  hdmap_status_publisher_ = get_rclcpp_node().create_publisher<Status>(
      "/apollo/hdmaplocalization_status", rclcpp::DefaultQoS());

  hdmap_image_publisher_ = get_rclcpp_node().create_publisher<Image>(
      "/apollo/hdmaplocalization_img", rclcpp::DefaultQoS());

  // subscriber
  lanes_subscriber_ = get_rclcpp_node().create_polling_subscription<PerceptionLanes>(
      "/apollo/perception/camera/lanes", rclcpp::DefaultQoS().keep_last(1));

  compositor_result_subscriber_ =
      get_rclcpp_node().create_polling_subscription<LocalizationEstimate>(
          "/apollo/compositor_to_hdmaplocalization", rclcpp::DefaultQoS().keep_last(5));

  tf2_broadcaster_ =
      std::make_unique<t2::common::transform::TransformBroadcaster>(get_rclcpp_node());

  constexpr std::uint64_t lidar_lane_cam_time_diff_threshold =
      static_cast<std::uint64_t>(0.15 * kNanosecsPerSec<double>);

  synchronizer_ =
      std::make_unique<Synchronizer>(vehicle_frameid_, lidar_lane_cam_time_diff_threshold);

  hdmap_timer_ = get_rclcpp_node().create_wall_timer(std::chrono::milliseconds{1000}, [this]() {
    statistics_.Dump();
    if (hdmap_ && hdmap_->LoadMap()) {
      T2_INFO << "HDMap loaded";
    }
  });

  T2_INFO << "HDMapLocalizationComponent init succeeded";
}
::apex::executor::subscription_list HDMapLocalizationComponent::get_triggering_subscriptions_impl()
    const {
  return {lanes_subscriber_, compositor_result_subscriber_};
}

::apex::executor::publisher_list HDMapLocalizationComponent::get_publishers_impl() const {
  return {localization_estimate_publisher_, hdmap_status_publisher_, hdmap_image_publisher_};
}

bool HDMapLocalizationComponent::execute_impl() {
  auto lanes_msgs_read = lanes_subscriber_->read();
  auto loc_msgs_read = compositor_result_subscriber_->read();

  if (lanes_msgs_read.empty() || loc_msgs_read.empty()) {
    T2_ERROR << "HDMapLocalizationComponent: No messages received";
    return false;
  }

  auto lanes_msgs = lanes_subscriber_->take();
  auto loc_msgs = compositor_result_subscriber_->take();

  for (const auto& loc : loc_msgs) {
    if (loc.info().valid()) {
      synchronizer_->Push(loc.data());
    } else {
      T2_ERROR << "HDMapLocalizationComponent: Invalid localization estimate";
      statistics_.no_tracker_pose_++;
      return false;
    }
  }

  const auto lanes = lanes_msgs.back();
  if (lanes.info().valid()) {
    if (!Proc(lanes.data())) {
      statistics_.not_synced_++;
      T2_ERROR << "HDMapLocalizationComponent: Not synced with lanes";
    } else {
      statistics_.ok_++;
    }
  } else {
    statistics_.not_synced_++;
    T2_ERROR << "HDMapLocalizationComponent: Invalid perception lanes";
  }

  return true;
}

bool HDMapLocalizationComponent::Proc(const PerceptionLanes& lane_msg) {
  if (last_lane_timestamp_.has_value()) {
    const std::uint64_t current_lane_timestamp = lane_msg.header.measurement_timestamp;
    if (current_lane_timestamp - last_lane_timestamp_.value() >
        2 * kNanosecsPerSec<std::uint64_t>) {
      T2_INFO << "Reset synchronizer due to the clock update";
      synchronizer_->Reset();
    }
  }
  last_lane_timestamp_ = lane_msg.header.measurement_timestamp;

  Status out_status_msg(rosidl_runtime_cpp::MessageInitialization::ZERO);
  out_status_msg.status_code = StatusCode::DEFAULT;
  auto sync_results = synchronizer_->Push(lane_msg);

  if (sync_results.size() == 0) {
    T2_ERROR << "HDMapLocalizationComponent: Not synced";
    statistics_.not_synced_++;
    out_status_msg.status_code = StatusCode::NOT_SYNCED;
    auto output_status_msg = hdmap_status_publisher_->borrow_loaned_message();
    output_status_msg.get() = std::move(out_status_msg);
    hdmap_status_publisher_->publish(std::move(output_status_msg));
    return false;
  }

  auto& sync_result = sync_results.back();
  out_status_msg.input_pose.position = sync_result.translation;
  out_status_msg.input_pose.orientation = sync_result.rotation;

  hdmap_->SetLastVehiclePosition(
      {sync_result.translation.x, sync_result.translation.y, sync_result.translation.z});

  const auto start_time = get_rclcpp_node().get_clock()->now();

  const auto vehicle_to_camera_lane_result =
      transformer_.Query(lane_msg.image_info.frame_id, vehicle_frameid_, rclcpp::Time(0));
  if (const auto& error = vehicle_to_camera_lane_result.get_error(); error.is_error()) {
    T2_ERROR << "Failed to get vehicle to camera lane transform";
    out_status_msg.status_code = StatusCode::TF_FAILED;
    auto output_status_msg = hdmap_status_publisher_->borrow_loaned_message();
    output_status_msg.get() = std::move(out_status_msg);
    hdmap_status_publisher_->publish(std::move(output_status_msg));
    return false;
  }
  const auto vehicle_to_camera_lane =
      t2::localization::common::ToSim3(vehicle_to_camera_lane_result.get_value().transform);

  t2::localization::common::CopyCameraPose(out_status_msg, vehicle_to_camera_lane);

  if (camera_model_lane_ == nullptr) {
    camera_model_lane_ = t2::common::camera_model::load_camera_model(lane_msg.image_info);
  }

  const auto procResult =
      hdmap_localization_->Proc(sync_result, vehicle_to_camera_lane, camera_model_lane_);

  if (procResult.state == HDMapLocalization::ResultState::NoEnoughLanes) {
    statistics_.no_enough_lanes_ += 1;
    out_status_msg.status_code = StatusCode::NO_ENOUGH_LANES;
  } else if (procResult.state == HDMapLocalization::ResultState::IsNotMapLoaded) {
    out_status_msg.status_code = StatusCode::MAP_NOT_LOADED;
  } else if (procResult.state == HDMapLocalization::ResultState::OutOfMapBoundary) {
    out_status_msg.status_code = StatusCode::OUT_OF_MAP_BOUNDARY;
  } else if (procResult.state == HDMapLocalization::ResultState::MissingEgoLane) {
    out_status_msg.status_code = StatusCode::MISSING_EGO_LANE;
  } else {
    out_status_msg.status_code = StatusCode::SUCCESS;
  }

  const auto found_result = procResult.result;
  const auto vehicle_to_world = procResult.vehicle_to_world;
  const auto perception_lanes = procResult.perception_lanes;
  const auto map_lanes = procResult.map_lanes;
  const auto pitch_error = procResult.pitch_error;

  std::array<float, 36> covariance;
  covariance.fill(0.F);

  const auto now = get_rclcpp_node().get_clock()->now();
  ::common_msgs::msg::Header hdmap_header;
  hdmap_header = lane_msg.header;
  hdmap_header.module_name = "HDMapLocalization";
  hdmap_header.measurement_timestamp = lane_msg.header.measurement_timestamp;
  hdmap_header.creation_timestamp = static_cast<std::uint64_t>(now.nanoseconds());
  hdmap_header.frame_id = vehicle_frameid_;

  if (found_result.has_value()) {
    T2_INFO << "Result : " << procResult.result.value().x << " " << found_result.value().y << " "
            << found_result.value().rot;

    const auto z_offset = vehicle_to_world.translation().z() - sync_result.translation.z;

    const auto original_to_optimized_r =
        Sophus::SE3d::rotZ(found_result.value().rot / 180.F * EIGEN_PI);

    const auto original_to_optimized_t_xy =
        Sophus::SE3d::trans({found_result.value().x, found_result.value().y, 0});

    const auto original_to_optimized_t_z = Sophus::SE3d::trans({0, 0, z_offset});

    const Sophus::SE3d original_to_optimized_rt_xy =
        original_to_optimized_t_xy * original_to_optimized_r;
    const auto fixed_vehicle_to_world = ToSim3d(original_to_optimized_t_z) * vehicle_to_world *
                                        ToSim3d(original_to_optimized_rt_xy);

    out_status_msg.lane_pitch_error = pitch_error;

    {
      decltype(out_status_msg.offset_pose)::value_type offset_pose;
      offset_pose.position.x = found_result.value().x;
      offset_pose.position.y = found_result.value().y;
      offset_pose.position.z = z_offset;
      offset_pose.orientation.x = original_to_optimized_r.unit_quaternion().x();
      offset_pose.orientation.y = original_to_optimized_r.unit_quaternion().y();
      offset_pose.orientation.z = original_to_optimized_r.unit_quaternion().z();
      offset_pose.orientation.w = original_to_optimized_r.unit_quaternion().w();
      out_status_msg.offset_pose.push_back(offset_pose);
    }

    LocalizationEstimate out_localization_msg =
        *t2::localization::common::CreateLocalizationEstimateFromSim3d(fixed_vehicle_to_world);
    out_localization_msg.measurement_time = sync_result.measurement_time;
    out_localization_msg.header = hdmap_header;

    float score_lane = 0.F;
    // Calculate score_lane as the weighted average
    float total_weight = 0.F;

    for (const auto& lane_score : found_result.value().lane_scores) {
      // Skip invalid weights
      if (lane_score.weight > 0.F) {
        score_lane += lane_score.score;
        total_weight += lane_score.weight;
      }
    }

    if (total_weight > 0.F) {
      score_lane /= total_weight;
    }

    // Default scores
    float score_x = 20.F;
    // Default lateral confidence
    float score_y = 4.F;
    // Z confidence limits
    const float z_covariance_min = 0.1F;
    const float z_covariance_max = 20.0F;
    const float score_z =
        std::clamp(static_cast<float>(std::abs(z_offset)), z_covariance_min, z_covariance_max);

    if (score_lane > 0.F) {
      // Scale dynamically based on the number of detected lanes
      const auto scale =
          4.0F / std::max(1.F, static_cast<float>(found_result.value().lane_scores.size()));
      score_y = std::clamp(scale * (4.F - score_lane), 0.1F, 4.0F);
    }

    // Adjust covariance for pitch error
    const auto rot_covariance_scale = 4.0F;
    float rot_covariance_rx = 0.1F;
    if (pitch_error > 0.F) {
      rot_covariance_rx = std::pow(pitch_error * EIGEN_PI / 180.F * rot_covariance_scale, 2.0F);
    }

    // Adjust rotational covariance for ry and rz
    const auto rot_covariance_ry_rz =
        std::pow(score_y / 4.0F * EIGEN_PI / 180.F * rot_covariance_scale, 2.0F);

    // Translation uncertainties
    covariance[0 + 6 * 0] = score_x * score_x;  // tx
    covariance[1 + 6 * 1] = score_y * score_y;  // ty
    covariance[2 + 6 * 2] = score_z;            // tz

    // Rotation uncertainties
    covariance[3 + 6 * 3] = rot_covariance_rx;     // rx (pitch)
    covariance[4 + 6 * 4] = rot_covariance_ry_rz;  // ry (roll)
    covariance[5 + 6 * 5] = rot_covariance_ry_rz;  // rz (yaw)
    int idx = 0;
    for (const auto& v : covariance) {
      out_localization_msg.covariance.element[idx++] = v;
    }

    auto output_localization_msg = localization_estimate_publisher_->borrow_loaned_message();
    output_localization_msg.get() = std::move(out_localization_msg);
    localization_estimate_publisher_->publish(std::move(output_localization_msg));

    out_status_msg.output_pose.push_back(out_localization_msg.pose);

    // TODO: Publish the transform from vehicle to world
    {
      t2::common::transform::TransformStamped tf2_msg;
      tf2_msg.source = "hdmaplocalization-vehicle";
      tf2_msg.target = "world";
      tf2_msg.time = rclcpp::Time(
          static_cast<std::uint64_t>(sync_result.measurement_time * kNanosecsPerSec<double>));

      const auto& translation = fixed_vehicle_to_world.translation();
      const auto quaternion = fixed_vehicle_to_world.quaternion().normalized();

      tf2_msg.transform = t2::tf2::Transform(
          t2::tf2::Quaternion(quaternion.x(), quaternion.y(), quaternion.z(), quaternion.w()),
          t2::tf2::Vector3(translation.x(), translation.y(), translation.z()),
          fixed_vehicle_to_world.scale());

      tf2_broadcaster_->SendTransform(tf2_msg);
    }
  }

  out_status_msg.lane_num = procResult.lane_status.lane_num;
  out_status_msg.localized = found_result.has_value();
  out_status_msg.measurement_time = sync_result.measurement_time;
  if (found_result.has_value()) {
    float lane_score_sum = 0.F;
    for (const auto& lane_score : found_result.value().lane_scores) {
      lane_score_sum += lane_score.score;
    }

    out_status_msg.lane_score = lane_score_sum;
    out_status_msg.lane_match_count = found_result->lane_scores.size();

    out_status_msg.covariance = covariance;
  }

  const auto store_lines = [&](const std::vector<BoundingBoxLine>& lines,
                               AttributeType attributeType) {
    for (const auto& line : lines) {
      Lane out_line;
      out_line.attribute = attributeType;

      for (const auto& point : line.line) {
        const auto p = vehicle_to_world * Eigen::Vector3d(point.x(), point.y(), point.z());
        Point3D out_point;
        out_point.x = p.x();
        out_point.y = p.y();
        out_point.z = p.z();
        out_line.points.push_back(out_point);
      }
      out_status_msg.lines.push_back(out_line);
    }
  };

  const auto store_perception_lines = [&](const std::vector<MatchedBoundingBoxLine>& lines,
                                          AttributeType attributeType) {
    for (const auto& line : lines) {
      Lane out_line;
      out_line.attribute = attributeType;
      if (line.identifier.has_value()) {
        const int perception_lane_index = static_cast<int>(line.identifier->perception_lane_index);
        const int map_lane_index = static_cast<int>(line.identifier->map_lane_index);
        out_line.matched_lane_idx.perception_index = perception_lane_index;
        out_line.matched_lane_idx.map_index = map_lane_index;
      }

      for (const auto& point : line.bbline.line) {
        Point3D out_point;
        const auto p = vehicle_to_world * Eigen::Vector3d(point.x(), point.y(), point.z());
        out_point.x = p.x();
        out_point.y = p.y();
        out_point.z = p.z();
        out_line.points.push_back(out_point);
      }
      out_status_msg.lines.push_back(out_line);
    }
  };

  store_perception_lines(perception_lanes, AttributeType::Perception);
  store_lines(procResult.invalid_perception_lanes, AttributeType::IgnoredPerception);
  store_lines(map_lanes, AttributeType::Map);

  if (found_result.has_value()) {
    store_perception_lines(found_result->lanes, AttributeType::Optimized);
  }

  out_status_msg.header = hdmap_header;
  out_status_msg.image_info = lane_msg.image_info;

  auto output_status_msgs = hdmap_status_publisher_->borrow_loaned_message();
  output_status_msgs.get() = std::move(out_status_msg);
  hdmap_status_publisher_->publish(std::move(output_status_msgs));

  const auto& img = sync_result.lane.image_debug;
  const auto& info = img.info;

  if (img.data.size() >= static_cast<std::size_t>(info.height) * info.step) {
    Image hdmap_image_msg = img;
    uint8_t* dataPtr = hdmap_image_msg.data.data();

    for (const auto& lane : map_lanes) {
      for (const auto& point : lane.line) {
        const auto cameraPos = (vehicle_to_camera_lane * point).eval();
        if (cameraPos.z() < 1.0F) {
          continue;
        }

        const auto screenPos =
            camera_model_lane_->project(cameraPos.cast<float>()).cast<int>().eval();

        const int markerHalfSize = 1;
        for (int dy = -markerHalfSize; dy <= markerHalfSize; dy++) {
          for (int dx = -markerHalfSize; dx <= markerHalfSize; dx++) {
            const auto x = dx + screenPos.x();
            const auto y = dy + screenPos.y();

            if (x < 0 || x >= static_cast<int>(hdmap_image_msg.info.width) || y < 0 ||
                y >= static_cast<int>(hdmap_image_msg.info.height)) {
              continue;
            }

            const auto ind = y * hdmap_image_msg.info.width + x;
            dataPtr[ind * 3 + 0] = 255;
            dataPtr[ind * 3 + 1] = 0;
            dataPtr[ind * 3 + 2] = 0;
          }
        }
      }
    }

    if (found_result.has_value()) {
      const auto rmat = Sophus::SE3d::rotZ(found_result.value().rot / 180.F * EIGEN_PI);

      const auto tmat = Sophus::SE3d::trans({found_result.value().x, found_result.value().y, 0});

      const Sophus::SE3d rtmat = tmat * rmat;
      const auto pose = vehicle_to_world * ToSim3d(rtmat);

      for (const auto& lane : map_lanes) {
        for (const auto& point : lane.line) {
          const auto cameraPos =
              (vehicle_to_camera_lane * pose.inverse() * vehicle_to_world * point).eval();
          if (cameraPos.z() < 1.0F) {
            continue;
          }

          const auto screenPos =
              camera_model_lane_->project(cameraPos.cast<float>()).cast<int>().eval();

          const int markerHalfSize = 1;
          for (int dy = -markerHalfSize; dy <= markerHalfSize; dy++) {
            for (int dx = -markerHalfSize; dx <= markerHalfSize; dx++) {
              const auto x = dx + screenPos.x();
              const auto y = dy + screenPos.y();

              if (x < 0 || x >= static_cast<int>(hdmap_image_msg.info.width) || y < 0 ||
                  y >= static_cast<int>(hdmap_image_msg.info.height)) {
                continue;
              }

              const auto ind = y * hdmap_image_msg.info.width + x;
              dataPtr[ind * 3 + 0] = 0;
              dataPtr[ind * 3 + 1] = 255;
              dataPtr[ind * 3 + 2] = 0;
            }
          }
        }
      }
    }

    auto output_hdmap_image_msg = hdmap_image_publisher_->borrow_loaned_message();
    output_hdmap_image_msg.get() = std::move(hdmap_image_msg);
    hdmap_image_publisher_->publish(std::move(output_hdmap_image_msg));
  }

  T2_INFO << "HDMapLocalizationComponent: "
          << (get_rclcpp_node().get_clock()->now() - start_time).seconds() * 1000.0 << " ms";
  statistics_.ok_++;
  return true;
}

HDMapLocalizationComponent::~HDMapLocalizationComponent() {
  T2_INFO << "HD map localization component shutdown.";
}

}  // namespace t2::localization::hdmap
