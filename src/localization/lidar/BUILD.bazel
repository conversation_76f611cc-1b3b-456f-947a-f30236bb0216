load("@rules_cc//cc:defs.bzl", "cc_library")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "lidar_localization_lib",
    srcs = [
        "lidar_localization_region.cpp",
    ],
    hdrs = [
        "lidar_localization_region.hpp",
    ],
    data = [
        "//src/localization/conf:lidarmap_config",
    ],
    deps = [
        "//src/common/core",
        "//src/common/util:file",
        "//src/localization/common:localization_util",
        "//src/localization/proto:lidarmap_config_cc_proto",
        "@com_google_protobuf//:protobuf",
        "@eigen",
        "@multi_sensor_localization//:localization_map_cc_proto",
    ],
)
