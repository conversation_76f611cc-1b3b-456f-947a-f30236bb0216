// Copyright (c) 2025, Apex.AI, Inc.
// Copyright (c) 2013, Open Source Robotics Foundation, Inc.
// All rights reserved.
//
// Software License Agreement (BSD License 2.0)
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
//
//  * Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
//  * Redistributions in binary form must reproduce the above
//    copyright notice, this list of conditions and the following
//    disclaimer in the documentation and/or other materials provided
//    with the distribution.
//  * Neither the name of the Open Source Robotics Foundation, Inc. nor the names of its
//    contributors may be used to endorse or promote products derived
//    from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
// FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
// COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
// INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
// BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
// CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
// LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
// ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

// This file is originally from:
// https://github.com/ros/common_msgs/blob/275b09a/sensor_msgs/include/sensor_msgs/point_cloud2_iterator.h

#pragma once

#include <cstddef>
#include <cstdint>
#include <type_traits>

#include "cpputils/safe_cast.hpp"
#include "hesai_msgs/msg/point_xyzit.hpp"
#include "src/localization/lidar/util/impl/utils.hpp"
#include "src/localization/lidar/util/point_cloud2_field.hpp"

namespace t2::localization::lidar::util {

void CheckCapacity(const PointCloud& cloud_msg, std::size_t test_size);

void InitPointCloud2(PointCloud& cloud_msg, const apex::string_strict128_t& frame_id,
                     const size_t size);

/**
 * @tparam MsgType The variant of the PointCloud2, either flat or non-flat
 * @brief Enables modifying a sensor_msgs::msg::PointCloud2 like a container
 */
template <typename MsgType = PointCloud>
class PointCloud2Container {
 public:
  static_assert(std::is_same_v<PointCloud, std::remove_reference_t<std::remove_cv_t<MsgType>>>,
                "MsgType must be same to PointCloud except CV&.");

  static constexpr bool is_const_msg = std::is_const_v<MsgType>;

  using Point = hesai_msgs::msg::PointXYZIT;

  /**
   * @brief Default constructor
   * @param cloud_msg The sensor_msgs::msg::PointCloud2 to modify
   */
  explicit PointCloud2Container(MsgType& cloud_msg);

  /**
   * @return the number of T's in the original sensor_msgs::msg::PointCloud2
   */
  size_t size() const;

  /**
   * @param size The number of T's to reserve in the original sensor_msgs::msg::PointCloud2 for
   */
  void reserve(size_t size);

  /**
   * @param size The number of T's to change the size of the original sensor_msgs::msg::PointCloud2
   * by
   */
  void resize(size_t size);

  /**
   * @brief remove all T's from the original sensor_msgs::msg::PointCloud2
   */
  void clear();

  void push_back(const Point& point);

  Point& operator[](std::size_t i) {
    static_assert(!is_const_msg, "Point cloud is const.");
    return const_cast<Point&>(static_cast<const PointCloud2Container&>(*this)[i]);
  }
  const Point& operator[](std::size_t i) const;

  Point& at(std::size_t i) {
    static_assert(!is_const_msg, "Point cloud is const.");
    return const_cast<Point&>(static_cast<const PointCloud2Container&>(*this).at(i));
  }
  const Point& at(std::size_t i) const;

  const typename MsgType::_header_type& header() const { return cloud_msg_.header; }
  typename MsgType::_height_type height() const { return cloud_msg_.height; }
  typename MsgType::_width_type width() const { return cloud_msg_.width; }
  const typename MsgType::_fields_type& fields() const { return cloud_msg_.fields; }
  typename MsgType::_is_bigendian_type is_bigendian() const { return cloud_msg_.is_bigendian; }
  typename MsgType::_point_step_type point_step() const { return cloud_msg_.point_step; }
  typename MsgType::_row_step_type row_step() const { return cloud_msg_.row_step; }
  typename MsgType::_is_dense_type is_dense() const { return cloud_msg_.is_dense; }

 private:
  /** sensor_msgs::msg::PointCloud2 */
  MsgType& cloud_msg_;
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

template <typename MsgType>
inline PointCloud2Container<MsgType>::PointCloud2Container(MsgType& cloud_msg)
    : cloud_msg_(cloud_msg) {
  assert(cloud_msg.fields.size() == point_cloud_fields.size());
  std::uint32_t offset = 0;
  for (std::size_t i = 0; i < point_cloud_fields.size(); ++i) {
    assert(cloud_msg.fields[i].offset == offset);
    assert(cloud_msg.fields[i].datatype == point_cloud_fields[i].datatype);
    assert(cloud_msg.fields[i].count == point_cloud_fields[i].count);
    assert(cloud_msg.fields[i].name == point_cloud_fields[i].name);
    offset += point_cloud_fields[i].count * impl::sizeOfPointField(point_cloud_fields[i].datatype);
  }
}

template <typename MsgType>
inline size_t PointCloud2Container<MsgType>::size() const {
  return cloud_msg_.data.size() / cloud_msg_.point_step;
}

template <typename MsgType>
inline void PointCloud2Container<MsgType>::reserve(size_t size) {
  static_assert(!is_const_msg, "Point cloud is const.");
  cloud_msg_.data.reserve(size * cloud_msg_.point_step);
}

template <typename MsgType>
inline void PointCloud2Container<MsgType>::resize(size_t size) {
  static_assert(!is_const_msg, "Point cloud is const.");

  cloud_msg_.data.resize(size * cloud_msg_.point_step);

  // Update height/width
  if (cloud_msg_.height == 1) {
    cloud_msg_.width = static_cast<uint32_t>(size);
    cloud_msg_.row_step = static_cast<uint32_t>(size * cloud_msg_.point_step);
  } else {
    if (cloud_msg_.width == 1) {
      cloud_msg_.height = static_cast<uint32_t>(size);
    } else {
      cloud_msg_.height = 1;
      cloud_msg_.width = static_cast<uint32_t>(size);
      cloud_msg_.row_step = static_cast<uint32_t>(size * cloud_msg_.point_step);
    }
  }
}

template <typename MsgType>
inline void PointCloud2Container<MsgType>::clear() {
  static_assert(!is_const_msg, "Point cloud is const.");

  cloud_msg_.data.clear();

  // Update height/width
  if (cloud_msg_.height == 1) {
    cloud_msg_.row_step = cloud_msg_.width = 0;
  } else {
    if (cloud_msg_.width == 1) {
      cloud_msg_.height = 0;
    } else {
      cloud_msg_.row_step = cloud_msg_.width = cloud_msg_.height = 0;
    }
  }
}

template <typename MsgType>
inline void PointCloud2Container<MsgType>::push_back(const Point& point) {
  static_assert(!is_const_msg, "Point cloud is const.");

  // Add point to the point cloud if there is enough capacity
  const auto required_size = apex::cast::safe_cast<typename decltype(cloud_msg_.data)::size_type>(
      (cloud_msg_.width + 1U) * cloud_msg_.point_step);
  CheckCapacity(cloud_msg_, required_size);

  const auto offset = cloud_msg_.data.size();
  cloud_msg_.data.resize(required_size);
  void* const dst = &cloud_msg_.data[offset];
  const void* const src = &point;
  (void)std::memcpy(dst, src, cloud_msg_.point_step);
  ++cloud_msg_.width;
  cloud_msg_.row_step = cloud_msg_.width * cloud_msg_.point_step;
}

template <typename MsgType>
inline const typename PointCloud2Container<MsgType>::Point&
PointCloud2Container<MsgType>::operator[](std::size_t i) const {
  const auto offset = i * cloud_msg_.point_step;
  return *reinterpret_cast<const Point*>(&cloud_msg_.data[offset]);
}

template <typename MsgType>
inline const typename PointCloud2Container<MsgType>::Point& PointCloud2Container<MsgType>::at(
    std::size_t i) const {
  if (i >= size()) {
    throw apex::runtime_error("Out of range");
  }
  return (*this)[i];
}

}  // namespace t2::localization::lidar::util
