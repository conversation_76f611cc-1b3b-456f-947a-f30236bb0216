syntax = "proto2";

package t2.localization.proto;

message EskfConfig {
  required float angle_std_x = 1;
  required float angle_std_y = 2;
  required float angle_std_z = 3;
  required float uncertainty_linear_acceleration_std_x = 4;
  required float uncertainty_linear_acceleration_std_y = 5;
  required float uncertainty_linear_acceleration_std_z = 6;
  required float uncertainty_angular_velocity_std_x = 7;
  required float uncertainty_angular_velocity_std_y = 8;
  required float uncertainty_angular_velocity_std_z = 9;
  required float acceleration_bias_std_x = 10;
  required float acceleration_bias_std_y = 11;
  required float acceleration_bias_std_z = 12;
  required float angular_velocity_bias_std_x = 13;
  required float angular_velocity_bias_std_y = 14;
  required float angular_velocity_bias_std_z = 15;
  required float gravitational_acceleration_x = 16;
  required float gravitational_acceleration_y = 17;
  required float gravitational_acceleration_z = 18;
}
