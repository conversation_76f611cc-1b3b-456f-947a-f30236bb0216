#pragma once

#include <memory>
#include <mutex>
#include <optional>
#include <queue>
#include <type_traits>

#include <calibration/lidar_calibrator.h>
#include <localizer/grid_creator.h>
#include <localizer/pointcloud_registrator.h>
#include <localizer/preprocessing.h>
#include <localizer/sins.h>

#include <Eigen/Core>

#include <sophus/se3.hpp>
#include <sophus/sim3.hpp>

#include "image_msgs/msg/image.hpp"
#include "lidar_localization_visualizer.hpp"
#include "localization_msgs/msg/localization_estimate.hpp"
#include "rclcpp/polling_subscription.hpp"
#include "rclcpp/publisher.hpp"
#include "sensor_msgs/msg/point_cloud2.hpp"
#include "src/localization/common/value_history.hpp"
#include "src/localization/lidar/preprocessor/motion_undistortion.hpp"
#include "src/localization/lidar/util/point_cloud2_container.hpp"
#include "src/localization/proto/eskf_config.pb.h"
#include "src/localization/proto/lidarmaplocalization_config.pb.h"

namespace t2::localization::compositor {
struct LiDARLocalizationRunnerStatus {
  int64_t measurement_timestamp = 0;
  int32_t landmark_map_count = 0;
  int32_t landmark_current_count = 0;
  Eigen::Matrix3d position_covariance;
  Eigen::Vector3d rot_std_dev;
  double sins_forward_covariance = 0.0;
  double failure_score = 0.0;
  bool lidarlocalization_failed = false;
};

struct LidarLocalizationPoseWriters {
  using LocalizationEstimate = localization_msgs::msg::LocalizationEstimate;
  using Publisher = rclcpp::Publisher<LocalizationEstimate>;
  using PublisherSharedPtr = Publisher::SharedPtr;

  PublisherSharedPtr sins_pose_writer;
  PublisherSharedPtr lidarlocalization_pose_writer;
  PublisherSharedPtr landmark_matching_pose_writer;
  PublisherSharedPtr grid_matching_pose_writer;
};

class LiDARLocalizationRunner {
 public:
  using LocalizationEstimate = localization_msgs::msg::LocalizationEstimate;
  using PointCloud = lidar::util::PointCloud;
  using PointCloudSamples = rclcpp::LoanedSamples<PointCloud>;

  using PointCloudPublisherSharedPtr = rclcpp::Publisher<sensor_msgs::msg::PointCloud2>::SharedPtr;
  using VisualizerSharedPtr = std::shared_ptr<LiDARLocalizationVisualizer>;

  static_assert(std::is_same_v<PointCloud, sensor_msgs::msg::PointCloud2::BorrowedType>,
                "Fix t2::localization::lidar::util::PointCloud.");

  struct MapCache {
    LidarLocalizationLocalMap src_map;
    LidarLocalizationLocalMap target_map;
    std::optional<Sophus::SE3d> poslv_to_world;
    std::optional<Sophus::SE3d> vehicle_to_world;
    std::optional<Eigen::Matrix3d> pos_cov;
    PointCloudSamples input_messages;  ///< The original input point cloud message
                                       ///< for creating the src_map
  };

  ~LiDARLocalizationRunner();

  bool Init(std::string map_path, Sophus::Sim3d vehicle_to_poslv_sim3,
            std::vector<Sophus::Sim3d> vehicle_to_lidar_extrinsics_sim3,
            const VisualizerSharedPtr& visualizer,
            const PointCloudPublisherSharedPtr& source_landmarks_writer,
            const PointCloudPublisherSharedPtr& target_landmarks_writer,
            const PointCloudPublisherSharedPtr& lidarmap_writer,
            const LidarLocalizationPoseWriters& lidar_localization_pose_writers);

  /**
   * @brief Update the localization state with the input point cloud.
   *
   * @param msg The input point cloud message
   */
  void Update(PointCloudSamples&& msg, int channel_index);

  std::shared_ptr<LocalizationEstimate> InputLocalizationEstimate(const LocalizationEstimate& msg);

  /**
   * @brief Get whether has LidarLocalization been applied.
   * @return value
   */
  bool GetIsLidarLocalizationApplied() const { return is_lidarlocalization_applied_; }

  std::optional<LiDARLocalizationRunnerStatus> GetLastLiDARLocalizationRunnerStatus() {
    std::lock_guard<std::mutex> lock(last_lidar_localization_runner_status_mtx_);
    return last_lidar_localization_runner_status_;
  }

 private:
  void PreprocessPointCloudMessage(const PointCloud& msg, std::vector<Eigen::Vector3d>& points,
                                   std::vector<Eigen::Vector3d>& colors, std::vector<Sophus::SE3d>,
                                   int channel_index, double distance_min, double distance_max,
                                   double angle_min, bool enable_undistortion);

  Sophus::Sim3d vehicle_to_poslv_sim3_;
  Sophus::SE3d vehicle_to_poslv_;
  VisualizerSharedPtr visualizer_;
  PointCloudPublisherSharedPtr source_landmarks_writer_;
  PointCloudPublisherSharedPtr target_landmarks_writer_;
  PointCloudPublisherSharedPtr lidarmap_writer_;
  LidarLocalizationPoseWriters lidar_localization_pose_writers_;
  t2::localization::proto::LiDARMapLocalizationConfig lidarlocalization_config_;
  t2::localization::proto::EskfConfig eskf_config_;
  std::optional<PointCloudRegistrator> pointcloud_registrator_;
  std::shared_ptr<LidarLocalizationMap> lidar_localization_map_;
  std::optional<SINS> sins_;
  std::optional<double> initial_time_;
  std::mutex mtx_;
  std::mutex localizing_mtx_;
  std::mutex merger_mtx_;
  std::mutex undistortor_mtx_;
  std::atomic<bool> is_running_ = false;
  std::thread localizing_thread_;
  std::shared_ptr<MapCache> map_cache_;
  std::optional<t2::localization::lidar::preprocessor::ApproximateMotionUndistortion>
      motion_undistortor_;

  std::vector<Sophus::SE3d> vehicle_to_lidar_extrinsics_;  ///< Extrinsics for lidar point distance
                                                           ///< calculation
  std::optional<MultiLiDARChannelMerger> merger_;  ///< Merge point clouds from multiple lidars

  std::optional<double> last_lidar_timestamp_s_;
  double failure_score_ = 0.0;
  bool lidarlocalization_failed_ = false;

  //! Whether has LidarLocalization been applied.
  bool is_lidarlocalization_applied_ = false;

  //! to convert se3 coordinates into sim3 coordinates because lidar
  //! localization treats SE3.
  double last_scale_ = 1.0;

  //! previous time when it applies gnss into eskf
  double gnss_previous_time_ = 0;

  //! scaling factor for gnss covariance
  double gnss_scale_ = 4.5;

  bool is_gnss_enabled_ = true;
  std::optional<LiDARCalibrator> calibrator_;
  Eigen::Vector3d eskf_angle_std_dev_;
  Eigen::Vector3d last_uncertainty_linear_acceleration_std_dev_;
  Eigen::Vector3d last_uncertainty_angular_velocity_std_dev_;

  std::optional<LiDARLocalizationRunnerStatus> last_lidar_localization_runner_status_;
  std::mutex last_lidar_localization_runner_status_mtx_;

  std::vector<double> timestamps_sec_;

  template <typename T>
  class ValueSmoother {
   public:
    explicit ValueSmoother(double update_weight) : update_weight_(update_weight) {}
    void UpdateValue(const T& value) {
      if (!value_.has_value()) {
        value_ = value;
      } else {
        value_ = value * update_weight_ + value_.value() * (1 - update_weight_);
      }
    }
    std::optional<T> GetValue() const { return value_; }
    void Reset() { value_ = std::nullopt; }

   private:
    std::optional<T> value_ = std::nullopt;
    double update_weight_ = 1.0;
  };

  double update_weight_ = 0.5;
  ValueSmoother<double> height_smoother_{update_weight_};
  int height_search_range_ = 5;

  double hold_time_ = 0.25;
  t2::localization::common::ValueHistory<Sophus::Sim3d> poslv_history_{hold_time_};

  void Run(Eigen::Vector2d searching_space_size);
};
}  // namespace t2::localization::compositor
