#include "lidar_localization_runner.hpp"

#include <cassert>
#include <memory>
#include <vector>

#include <easy/profiler.h>

#include "calibration/calibrator_io.h"
#include "localizer/data_converter.h"
#include "proto/localization_map.pb.h"
#include "src/common/core/chrono_consts.hpp"
#include "src/common/core/logging.hpp"
#include "src/common/util/file.hpp"
#include "src/localization/common/localization_util.hpp"
#include "std_msgs/msg/header.hpp"

namespace t2::localization::compositor {
namespace {
using Header = std_msgs::msg::Header::BorrowedType;
using LocalizationEstimate = LiDARLocalizationRunner::LocalizationEstimate;
using Point3D = geometry_msgs::msg::Point;
using PointCloudPublisherSharedPtr = LiDARLocalizationRunner::PointCloudPublisherSharedPtr;

const double kDistanceMin = 7.5;                   // To avoid to high intensity
const double kAngleMin = 20.0 / 180.0 * EIGEN_PI;  // To avoid to high intensity
const double kRingIdNum = 128.0;                   // The number of scan lines of a lidar
constexpr auto kNovatel_Raw_FrameId = "novatel_raw";

Point3D ToPoint(const geometry_msgs::msg::Vector3& v) {
  Point3D p;
  p.x = v.x;
  p.y = v.y;
  p.z = v.z;
  return p;
}

double GetTimestamp(const Header& header) {
  return static_cast<double>(header.stamp.sec) +
         static_cast<double>(header.stamp.nanosec) /
             t2::common::chrono_consts::kNanosecsPerSec<double>;
}

void ToLocalizationMap(LidarLocalizationLocalMap& local_map, std::vector<Eigen::Vector3d>& points,
                       std::vector<Eigen::Vector3d>& colors, const LiDARCalibrator& calibrator,
                       double max_side_distance, double max_height, double grid_range,
                       Sophus::SE3d poslv_pose, Sophus::SE3d vehicle_to_poslv) {
  EASY_FUNCTION(profiler::colors::Yellow);

  PreprocessFiltering(points, colors, poslv_pose, vehicle_to_poslv,
                      {true, max_height, true, max_side_distance});

  {
    EASY_BLOCK("CalibratePointCloud")
    CalibratePointCloud(points, colors, calibrator);
  }

  CreateGrid(local_map, points, colors, 0.1, {-grid_range / 2, grid_range / 2}, poslv_pose);
}

std::shared_ptr<LidarLocalizationMap> LoadLidarLocalizationMap(std::string path) {
  std::fstream input(path, std::ios::in | std::ios::binary);
  if (!input) {
    std::cout << "Failed to load " << path << std::endl;
    return nullptr;
  }

  ::localization::proto::LocalizationSubmapCollection submap_collection;
  submap_collection.ParseFromIstream(&input);

  auto lidar_localization_map = std::make_shared<LidarLocalizationMap>();
  lidar_localization_map->FromProtobuf(submap_collection);
  return lidar_localization_map;
}

CalibrationConfig ToCalibrationConfig(
    const t2::localization::proto::LiDARMapLocalizationConfig& lidarlocalization_config,
    const size_t lidar_channels_size) {
  CalibrationConfig calibration_config;
  calibration_config.beam_num = lidarlocalization_config.calibration_beam_num();
  calibration_config.max_intensity = lidarlocalization_config.calibration_max_intensity();
  calibration_config.distance_step = lidarlocalization_config.calibration_distance_step();
  calibration_config.max_distance = lidarlocalization_config.calibration_max_distance();
  calibration_config.max_map_id_num = lidarlocalization_config.calibration_max_map_id_num();
  calibration_config.lidar_channels_size = lidar_channels_size;
  return calibration_config;
}

void PackLiDARMap(const LidarLocalizationLocalMap& local_map, const Sophus::SE3d poslv_to_world,
                  const Sophus::Sim3d raw_poslv_to_world, double timestamp,
                  LiDARLocalizationRunner::PointCloud& point_cloud) {
  using PointCloud = LiDARLocalizationRunner::PointCloud;

  const double cell_size = 0.1;

  auto container = lidar::util::PointCloud2Container<PointCloud>(point_cloud);
  container.reserve(local_map.available_points.size());
  auto center_position = poslv_to_world.translation();

  const int width_count = local_map.altitude_grid_mean.cols();
  const int height_count = local_map.altitude_grid_mean.rows();

  for (auto pos : local_map.available_points) {
    double ground_altitude = local_map.altitude_grid_mean(pos[0], pos[1]);
    double intensity_mean = local_map.intensity_grid_mean(pos[0], pos[1]);

    Eigen::Vector3d position((pos[1] - width_count / 2) * cell_size + center_position.x(),
                             (pos[0] - height_count / 2) * cell_size + center_position.y(),
                             ground_altitude);

    auto local_position = raw_poslv_to_world.inverse() * position;

    hesai_msgs::msg::PointXYZIT point;
    point.x = local_position.x();
    point.y = local_position.y();
    point.z = local_position.z();
    point.intensity = static_cast<std::uint32_t>(intensity_mean * 255);
    point.timestamp_sec = timestamp;

    container.push_back(point);
  }
}

void WriteLiDARMap(const std::shared_ptr<const LiDARLocalizationRunner::MapCache> map_cache,
                   const std::optional<Sophus::Sim3d>& raw_poslv_to_world,
                   const PointCloudPublisherSharedPtr& lidarmap_writer) {
  if (map_cache == nullptr || map_cache->vehicle_to_world == std::nullopt ||
      map_cache->input_messages.size() == 0 || !raw_poslv_to_world.has_value() ||
      lidarmap_writer == nullptr) {
    return;
  }

  const auto& header = map_cache->input_messages.front().data().header;

  auto pc_msg = lidarmap_writer->borrow_loaned_message();
  PackLiDARMap(map_cache->target_map, map_cache->poslv_to_world.value(), raw_poslv_to_world.value(),
               GetTimestamp(header), pc_msg.get());
  pc_msg.get().header = header;
  pc_msg.get().header.frame_id = kNovatel_Raw_FrameId;
  lidarmap_writer->publish(std::move(pc_msg));
}

void PackLandmarks(const t2::PointLandmarks& landmarks, const Sophus::SE3d vehicle_to_world,
                   double timestamp, LiDARLocalizationRunner::PointCloud& point_cloud) {
  using PointCloud = LiDARLocalizationRunner::PointCloud;

  auto container = lidar::util::PointCloud2Container<PointCloud>(point_cloud);

  container.reserve(landmarks.size());
  Sophus::SE3d world_to_vehicle = vehicle_to_world.inverse();
  for (std::size_t i = 0; i < landmarks.size(); i++) {
    auto landmark_point = landmarks[i].position;
    landmark_point = world_to_vehicle * landmark_point;

    hesai_msgs::msg::PointXYZIT point;
    point.x = landmark_point.x();
    point.y = landmark_point.y();
    point.z = landmark_point.z();
    point.intensity = landmarks[i].intensity;
    point.timestamp_sec = timestamp;
    container.push_back(point);
  }
}

void WriteLandmarks(const std::shared_ptr<const LiDARLocalizationRunner::MapCache> map_cache,
                    const PointCloudPublisherSharedPtr& source_landmarks_writer,
                    const PointCloudPublisherSharedPtr& target_landmarks_writer) {
  if (map_cache == nullptr || map_cache->vehicle_to_world == std::nullopt ||
      map_cache->input_messages.size() == 0 || source_landmarks_writer == nullptr ||
      target_landmarks_writer == nullptr) {
    return;
  }

  const auto& header = map_cache->input_messages.front().data().header;
  auto source_pc_msg = source_landmarks_writer->borrow_loaned_message();
  PackLandmarks(map_cache->src_map.point_landmarks, map_cache->vehicle_to_world.value(),
                GetTimestamp(header), source_pc_msg.get());
  source_pc_msg.get().header = header;
  source_landmarks_writer->publish(std::move(source_pc_msg));

  auto target_pc_msg = target_landmarks_writer->borrow_loaned_message();
  PackLandmarks(map_cache->target_map.point_landmarks, map_cache->vehicle_to_world.value(),
                GetTimestamp(header), target_pc_msg.get());
  target_pc_msg.get().header = header;
  target_landmarks_writer->publish(std::move(target_pc_msg));
}

std::shared_ptr<LocalizationEstimate> CreateLocalizationPoseMsg(
    const Sophus::SE3d& pose_vehicle_to_world, const Eigen::Matrix3d& cov_world, double scale,
    const Sophus::Sim3d& vehicle_to_poslv_sim3d, const Header& header,
    const Eigen::Vector3d& angle_std_dev) {
  auto msg = std::make_shared<LocalizationEstimate>();
  Sophus::Sim3d pose_vehicle_to_world_sim3d(
      Sophus::RxSO3d{scale, Sophus::SO3d{pose_vehicle_to_world.unit_quaternion()}},
      pose_vehicle_to_world.translation());
  Sophus::Sim3d pose_poslv_to_world_sim3d =
      pose_vehicle_to_world_sim3d * vehicle_to_poslv_sim3d.inverse();
  t2::localization::common::CopyPose(*msg, pose_poslv_to_world_sim3d);
  for (int i = 0; i < 36; i++) {
    int row = i / 6;
    int col = i % 6;
    if (row < 3 && col < 3) {
      msg->covariance.element[i] = cov_world(row, col);
    } else {
      if (row == col) {
        msg->covariance.element[i] = angle_std_dev(row - 3) * angle_std_dev(row - 3);
      } else {
        msg->covariance.element[i] = 0.0;
      }
    }
  }

  msg->header.measurement_timestamp = GetTimestamp(header);

  msg->header.frame_id = "lidarlocalization";
  msg->pose.heading =
      t2::common::math::QuaternionToHeadingZXY(pose_poslv_to_world_sim3d.quaternion().normalized());

  return msg;
}

}  // namespace

LiDARLocalizationRunner::~LiDARLocalizationRunner() {
  is_running_ = false;
  localizing_thread_.join();
}

void LiDARLocalizationRunner::PreprocessPointCloudMessage(
    const LiDARLocalizationRunner::PointCloud& point_cloud, std::vector<Eigen::Vector3d>& points,
    std::vector<Eigen::Vector3d>& colors, std::vector<Sophus::SE3d> rig_to_lidars,
    int channel_index, double distance_min, double distance_max, double angle_min,
    bool enable_undistortion) {
  // FIXME(kun.lin): Use sensor model instead of hard-coded values everywhere.
  const float kMaxIntensity = 255.0f;

  const lidar::util::PointCloud2Container<const LiDARLocalizationRunner::PointCloud> container(
      point_cloud);
  if (container.size() == 0) {
    T2_WARN << "Empty pointcloud message";
    return;
  }
  points.reserve(container.size());
  colors.reserve(container.size());
  if (enable_undistortion) {
    timestamps_sec_.clear();
    timestamps_sec_.reserve(container.size());
  }
  double max_timestamp_sec = std::numeric_limits<double>::lowest();
  double min_timestamp_sec = std::numeric_limits<double>::max();
  for (std::size_t i = 0; i < container.size(); i++) {
    const auto& point = container[i];
    const float x_scalar = point.x;
    if (std::isnan(x_scalar)) {
      continue;
    }
    float intensity = static_cast<float>(point.intensity) / kMaxIntensity;
    float ring_id = 0.0f;
    ring_id = (point.ring_id + channel_index * kRingIdNum) / (rig_to_lidars.size() * kRingIdNum);

    Eigen::Vector3d position_on_lidar =
        rig_to_lidars.at(channel_index) * Eigen::Vector3d{point.x, point.y, point.z};

    Eigen::Vector3d position_on_lidar_2d(position_on_lidar.x(), position_on_lidar.y(), 0);
    float distance = position_on_lidar.norm();
    float distance2d = position_on_lidar_2d.norm();
    double angle = std::atan2(position_on_lidar.y(), position_on_lidar.x());

    if (distance_min > distance2d &&
        (angle_min > std::abs(angle) || angle_min > EIGEN_PI - std::abs(angle))) {
      continue;
    }

    if (distance_max < distance2d) {
      continue;
    }

    points.emplace_back(point.x, point.y, point.z);
    colors.emplace_back(intensity, ring_id, distance / 200);
    if (enable_undistortion) {
      double timestamp_sec = point.timestamp_sec;
      timestamps_sec_.push_back(timestamp_sec);
      min_timestamp_sec = std::min(min_timestamp_sec, timestamp_sec);
      max_timestamp_sec = std::max(max_timestamp_sec, timestamp_sec);
    }
  }

  if (!enable_undistortion) {
    return;
  }

  if (min_timestamp_sec == std::numeric_limits<double>::max()) {
    T2_WARN << "All timestamps are invalid. Can not undistort the point cloud";
    return;
  } else if (min_timestamp_sec == max_timestamp_sec) {
    // If we don't return here it will lead to a crash due to failure to
    // interpolate time
    T2_WARN << "Min timestamp was equal to max timestamp. Can not undistort "
               "the point cloud";
    return;
  }

  Sophus::SE3d min_time_pose = Sophus::SE3d();
  Sophus::SE3d max_time_pose = Sophus::SE3d();

  {
    std::lock_guard<std::mutex> lock(mtx_);
    if (sins_.has_value() && initial_time_.has_value()) {
      double sins_min_time_s = min_timestamp_sec - initial_time_.value();
      double sins_max_time_s = max_timestamp_sec - initial_time_.value();
      min_time_pose = sins_.value().GetPoseWorld(sins_min_time_s) * vehicle_to_poslv_;
      max_time_pose = sins_.value().GetPoseWorld(sins_max_time_s) * vehicle_to_poslv_;
    } else {
      T2_WARN << "SINS is not initialized. Can not undistort the point cloud";
      return;
    }
  }

  {
    std::lock_guard<std::mutex> lock(undistortor_mtx_);
    if (!motion_undistortor_.has_value()) {
      T2_WARN << "motion_undistortor_ is not initialized";
      return;
    }
    motion_undistortor_->Reset();
    motion_undistortor_->PushPose(
        t2::localization::lidar::preprocessor::PoseWithTime{min_time_pose, min_timestamp_sec});
    motion_undistortor_->PushPose(
        t2::localization::lidar::preprocessor::PoseWithTime{max_time_pose, max_timestamp_sec});
    motion_undistortor_->CheckAndCreateCacheIfRequired(max_timestamp_sec);

    const auto target_pose = motion_undistortor_->Interpolate(max_timestamp_sec);
    if (!target_pose.has_value()) {
      T2_WARN << "Can not interpolate the target pose in motion_undistortor. "
                 "Can not undistort the point cloud";
      return;
    }
    const auto target_pose_inv = target_pose.has_value() ? target_pose->inverse() : Sophus::SE3d();
    motion_undistortor_->SetTargetPose(target_pose_inv);

    for (std::size_t i = 0; i < points.size(); i++) {
      if (target_pose.has_value()) {
        Eigen::Vector3f undistorted_point =
            motion_undistortor_->Undistort(points[i].cast<float>(), timestamps_sec_[i]);
        points[i] = undistorted_point.cast<double>();
      }
    }
  }
}

bool LiDARLocalizationRunner::Init(
    std::string map_path, Sophus::Sim3d vehicle_to_poslv_sim3,
    std::vector<Sophus::Sim3d> vehicle_to_lidar_extrinsics_sim3,
    const VisualizerSharedPtr& visualizer,
    const PointCloudPublisherSharedPtr& source_landmarks_writer,
    const PointCloudPublisherSharedPtr& target_landmarks_writer,
    const PointCloudPublisherSharedPtr& lidarmap_writer,
    const LidarLocalizationPoseWriters& lidar_localization_pose_writers) {
  visualizer_ = visualizer;
  source_landmarks_writer_ = source_landmarks_writer;
  target_landmarks_writer_ = target_landmarks_writer;
  lidarmap_writer_ = lidarmap_writer;
  lidar_localization_pose_writers_ = lidar_localization_pose_writers;
  height_smoother_.Reset();
  poslv_history_.Reset();

  const double kDuration = 0.5;
  constexpr size_t kCacheSize = 2000;
  const double kDt = 0.00025;
  motion_undistortor_.emplace(kDuration, kCacheSize, kDt);

  // TODO : Make config
  const Eigen::Vector2d searching_space_size{7.0, 3.0};
  const double cell_size = 0.1;

  vehicle_to_poslv_sim3_ = vehicle_to_poslv_sim3;
  vehicle_to_poslv_ = Sophus::SE3d(vehicle_to_poslv_sim3.quaternion().normalized(),
                                   vehicle_to_poslv_sim3.translation());

  vehicle_to_lidar_extrinsics_.clear();
  for (const auto& vehicle_to_lidar_extrinsic_sim3 : vehicle_to_lidar_extrinsics_sim3) {
    vehicle_to_lidar_extrinsics_.emplace_back(
        Sophus::SE3d(vehicle_to_lidar_extrinsic_sim3.quaternion().normalized(),
                     vehicle_to_lidar_extrinsic_sim3.translation()));
  }
  merger_.emplace(MultiLiDARChannelMerger(vehicle_to_lidar_extrinsics_.size()));

  std::string lidarlocalization_config_path =
      "src/localization/conf/lidarmaplocalization_config.txt";
  ACHECK(t2::common::util::file::GetProtoFromFile(lidarlocalization_config_path,
                                                  &lidarlocalization_config_))
      << "Unable to load compositor conf file: " + lidarlocalization_config_path;
  T2_INFO << lidarlocalization_config_.DebugString();

  lidar_localization_map_ = LoadLidarLocalizationMap(map_path);
  if (lidar_localization_map_ == nullptr) {
    T2_INFO << map_path << " is not found.";
    return false;
  }

  const CalibrationConfig calibration_config =
      ToCalibrationConfig(lidarlocalization_config_, vehicle_to_lidar_extrinsics_sim3.size());

  std::string eskf_config_path = "src/localization/conf/eskf_config.txt";
  ACHECK(t2::common::util::file::GetProtoFromFile(eskf_config_path, &eskf_config_))
      << "Unable to load compositor conf file: " + eskf_config_path;
  T2_INFO << eskf_config_.DebugString();

  try {
    calibrator_.emplace(ReadLiDARCalibratorFromCSV(
        lidarlocalization_config_.calibration_file_path(), calibration_config));
  } catch (const std::exception& e) {
    T2_INFO << e.what();
    return false;
  }

  eskf_angle_std_dev_ = Eigen::Vector3d(eskf_config_.angle_std_x(), eskf_config_.angle_std_y(),
                                        eskf_config_.angle_std_z());
  last_uncertainty_linear_acceleration_std_dev_ =
      Eigen::Vector3d(eskf_config_.uncertainty_linear_acceleration_std_x(),
                      eskf_config_.uncertainty_linear_acceleration_std_y(),
                      eskf_config_.uncertainty_linear_acceleration_std_z());
  last_uncertainty_angular_velocity_std_dev_ =
      Eigen::Vector3d(eskf_config_.uncertainty_angular_velocity_std_x(),
                      eskf_config_.uncertainty_angular_velocity_std_y(),
                      eskf_config_.uncertainty_angular_velocity_std_z());
  const std::pair<double, double> grid_range = {-lidarlocalization_config_.grid_size() / 2,
                                                lidarlocalization_config_.grid_size() / 2};
  pointcloud_registrator_.emplace(
      grid_range, cell_size, 1.0, lidarlocalization_config_.intensity_lambda(),
      lidarlocalization_config_.altitude_lambda(), EstimationHeadingAngleType::Last,
      LandmarkMatchingType::HistogramFilter, true, GridMatchingType::DirectionalGaussian);

  is_running_ = true;
  localizing_thread_ = std::thread([this, searching_space_size]() {
    EASY_THREAD("lidarlocalization-runner");
    Run(searching_space_size);
  });
  return true;
}

void LiDARLocalizationRunner::Update(LiDARLocalizationRunner::PointCloudSamples&& cloud_msgs,
                                     int channel_index) {
  assert(cloud_msgs.size() == 1);

  const auto& cloud_msg = cloud_msgs.front().data();

  // TODO : Make config
  const double cell_size = 0.1;
  const double tile_size = 5.0;

  const Eigen::Vector3d lidarlocalization_angle_std_dev(lidarlocalization_config_.angle_std_x(),
                                                        lidarlocalization_config_.angle_std_y(),
                                                        lidarlocalization_config_.angle_std_z());

  const double lidar_time_s = GetTimestamp(cloud_msg.header);

  std::optional<Sophus::SE3d> poslv_to_world;
  std::optional<Eigen::Matrix3d> pos_cov;
  {
    std::lock_guard<std::mutex> lock(mtx_);
    if (initial_time_.has_value() && sins_.has_value()) {
      const double sins_time_s = lidar_time_s - initial_time_.value();
      // Skip first frame because first frame contains invalid values.
      if (sins_time_s < 1.0) {
        return;
      }
      poslv_to_world = sins_.value().GetPoseWorld(sins_time_s);
      pos_cov = sins_.value().GetPositionWorldCov(sins_time_s);
    }
  }

  if (poslv_to_world.has_value() && pos_cov.has_value()) {
    std::shared_ptr<MapCache> new_cache = std::make_shared<MapCache>();

    const auto msg_timestamp =
        std::chrono::nanoseconds(static_cast<std::int64_t>(cloud_msg.header.stamp.sec) *
                                     t2::common::chrono_consts::kNanosecsPerSec<std::int64_t> +
                                 static_cast<std::int64_t>(cloud_msg.header.stamp.nanosec));
    std::optional<MultiLiDARChannelMerger::LiDARFrame> merged_points;
    {
      std::lock_guard merger_lock(merger_mtx_);
      if (merger_->HasObsoleteLiDARData(msg_timestamp)) {
        merged_points = merger_->ForceGet();
      }

      {
        // FIXME(kun.lin): Due to the limitation CreateGrid function, the
        // vectors below can not be initialized with Eigen::aligned_allocator.
        // Fix the API in MSL later to make this work.
        std::vector<Eigen::Vector3d> points;
        std::vector<Eigen::Vector3d> colors;
        PreprocessPointCloudMessage(cloud_msg, points, colors, vehicle_to_lidar_extrinsics_,
                                    channel_index, kDistanceMin,
                                    lidarlocalization_config_.grid_size() / 2, kAngleMin,
                                    lidarlocalization_config_.enable_undistortion());
        merger_->AddLiDARData(
            channel_index, MultiLiDARChannelMerger::LiDARFrame{std::move(points), std::move(colors),
                                                               msg_timestamp});
      }

      if (!merged_points) {
        merged_points = merger_->Get();
        if (!merged_points) {
          return;
        }
      }
    }

    double grid_range = lidarlocalization_config_.grid_size();
    lidar_localization_map_->GetMap(new_cache->target_map,
                                    Eigen::Vector2d(poslv_to_world.value().translation().x(),
                                                    poslv_to_world.value().translation().y()),
                                    Eigen::Vector2d(grid_range, grid_range), tile_size, cell_size);

    new_cache->target_map.CollectAvailablePoints();

    int grid_rows = new_cache->target_map.GetMapRows();
    int grid_cols = new_cache->target_map.GetMapCols();
    std::size_t start_row =
        static_cast<std::size_t>(std::max(grid_rows / 2 - height_search_range_, 0));
    std::size_t end_row =
        static_cast<std::size_t>(std::min(grid_rows / 2 + height_search_range_, grid_rows));
    std::size_t start_col =
        static_cast<std::size_t>(std::max(grid_cols / 2 - height_search_range_, 0));
    std::size_t end_col =
        static_cast<std::size_t>(std::min(grid_cols / 2 + height_search_range_, grid_cols));

    std::vector<double> heights;
    for (std::size_t i = start_col; i < end_col; ++i) {
      for (std::size_t j = start_row; j < end_row; ++j) {
        double height = new_cache->target_map.ground_altitude(j, i);
        if (height == 0.0) {
          continue;
        }
        heights.push_back(height);
      }
    }
    std::sort(heights.begin(), heights.end());

    if (heights.size() > 0) {
      height_smoother_.UpdateValue(heights[heights.size() / 2]);
    }

    auto ground_height = height_smoother_.GetValue();
    if (ground_height.has_value()) {
      poslv_to_world.value().translation().z() =
          ground_height.value() - vehicle_to_poslv_.translation().z();
    }

    ToLocalizationMap(new_cache->src_map, merged_points->points, merged_points->colors,
                      calibrator_.value(), lidarlocalization_config_.max_side_distance(),
                      lidarlocalization_config_.max_height(), grid_range, poslv_to_world.value(),
                      vehicle_to_poslv_);

    {
      EASY_BLOCK("CopyToLocalizingThread")
      new_cache->poslv_to_world = poslv_to_world;
      if (poslv_to_world.has_value()) {
        new_cache->vehicle_to_world = poslv_to_world.value() * vehicle_to_poslv_;
      }
      new_cache->pos_cov = pos_cov;
      new_cache->input_messages = std::move(cloud_msgs);
    }

    {
      EASY_BLOCK("SyncToLocalizingThread")
      std::lock_guard<std::mutex> lock(localizing_mtx_);
      map_cache_ = std::move(new_cache);
    }
  }
}

std::shared_ptr<LiDARLocalizationRunner::LocalizationEstimate>
LiDARLocalizationRunner::InputLocalizationEstimate(
    const LiDARLocalizationRunner::LocalizationEstimate& msg) {
  std::lock_guard<std::mutex> lock(mtx_);

  auto msg_pose = t2::localization::common::ToSim3(msg);
  const auto velocity = t2::localization::common::ToEigen(ToPoint(msg.pose.linear_velocity));
  Sophus::SE3d poslv_to_world;
  poslv_to_world.translation() = msg_pose.translation();
  poslv_to_world.setQuaternion(msg_pose.quaternion());
  last_scale_ = msg_pose.scale();

  if (!initial_time_.has_value()) {
    initial_time_ = msg.measurement_time;
  }

  if (!sins_.has_value()) {
    const Eigen::Vector3d acceleration_bias_std(eskf_config_.acceleration_bias_std_x(),
                                                eskf_config_.acceleration_bias_std_y(),
                                                eskf_config_.acceleration_bias_std_z());
    const Eigen::Vector3d angular_velocity_bias_std(eskf_config_.angular_velocity_bias_std_x(),
                                                    eskf_config_.angular_velocity_bias_std_y(),
                                                    eskf_config_.angular_velocity_bias_std_z());

    sins_ = SINS(poslv_to_world.translation(), velocity,
                 Eigen::Quaterniond(poslv_to_world.so3().matrix()), Eigen::Vector3d::Zero(),
                 Eigen::Vector3d::Zero(), acceleration_bias_std, angular_velocity_bias_std,
                 Eigen::Vector3d::Zero(), Eigen::Vector3d::Zero(), Eigen::Vector3d::Zero(), 0,
                 SINSDelayMode::Recalculate, 5.0);
  }

  double sins_time_s = msg.measurement_time - initial_time_.value();

  poslv_history_.AddValue({sins_time_s, msg_pose});

  if (is_gnss_enabled_ && msg.uncertainty.size() != 0 &&
      (sins_time_s - gnss_previous_time_) > 0.1) {
    const auto& position_std_dev = msg.uncertainty[0].position_std_dev;
    Eigen::Matrix3d gnss_std_mat = Eigen::Matrix3d::Identity();
    gnss_std_mat(0, 0) = std::abs(position_std_dev.x) * gnss_scale_;
    gnss_std_mat(1, 1) = std::abs(position_std_dev.y) * gnss_scale_;
    gnss_std_mat(2, 2) = std::abs(position_std_dev.z) * gnss_scale_;

    const auto gnss_position = poslv_to_world.translation();
    const auto sins_position = sins_.value().GetPoseWorld(sins_time_s).translation();
    const auto sins_to_gnss_translation = gnss_position - sins_position;

    const auto mahalanobis_mat =
        (sins_to_gnss_translation.transpose() * (gnss_std_mat * gnss_std_mat).inverse() *
         sins_to_gnss_translation)
            .eval();

    const auto mahalanobis_distance = std::sqrt(mahalanobis_mat(0, 0));

    T2_INFO << "Mahalanobis," << mahalanobis_distance << "," << sins_to_gnss_translation.norm()
            << "," << t2::localization::common::ToEigen(position_std_dev).norm();

    if (mahalanobis_distance < lidarlocalization_config_.mahalanobis_distance_threshold()) {
      sins_.value().GNSSUpdate(poslv_to_world.translation(), gnss_std_mat, sins_time_s);
    } else {
      T2_INFO << "GNSS is ignored bacause of mahalanobis " << mahalanobis_distance;
    }

    gnss_previous_time_ = sins_time_s;
  }

  Eigen::Vector3d linear_acceleration =
      t2::localization::common::ToEigen(ToPoint(msg.pose.linear_acceleration_vrf));
  Eigen::Vector3d angular_velocity =
      t2::localization::common::ToEigen(ToPoint(msg.pose.angular_velocity_vrf));

  if (msg.uncertainty.size() != 0) {
    const auto& uncertainty = msg.uncertainty[0];
    if (uncertainty.linear_acceleration_std_dev.size() != 0) {
      last_uncertainty_linear_acceleration_std_dev_ =
          t2::localization::common::ToEigen(uncertainty.linear_acceleration_std_dev[0]);
    }

    if (uncertainty.angular_velocity_std_dev.size() != 0) {
      last_uncertainty_angular_velocity_std_dev_ =
          t2::localization::common::ToEigen(uncertainty.angular_velocity_std_dev[0]);
    }
  }

  IMUUpdateInput imu_update_input = CreateIMUUpdateInput(
      linear_acceleration, angular_velocity, last_uncertainty_linear_acceleration_std_dev_,
      last_uncertainty_angular_velocity_std_dev_);

  sins_.value().IMUUpdate(imu_update_input.linear_acceleration_v,
                          imu_update_input.angular_velocity_v,
                          imu_update_input.linear_acceleration_v_std_dev,
                          imu_update_input.angular_velocity_v_std_dev, sins_time_s);

  auto sins_poslv_to_world = sins_.value().GetPoseWorld(sins_time_s);
  auto sins_cov = sins_.value().GetPositionWorldCov(sins_time_s);

  const auto sins_poslv_to_world_sim3d = Sophus::Sim3d(
      Sophus::RxSO3d{msg.pose.scale, Sophus::SO3d{sins_poslv_to_world.unit_quaternion()}},
      sins_poslv_to_world.translation());

  auto out_msg =
      t2::localization::common::ConvertToLocalizationEstimate(sins_poslv_to_world_sim3d, msg);
  out_msg->header.frame_id = msg.header.frame_id;

  out_msg->pose.heading =
      t2::common::math::QuaternionToHeadingZXY(sins_poslv_to_world_sim3d.quaternion().normalized());

  const auto velocity_world = sins_.value().GetVelocityWorld(sins_time_s);
  out_msg->pose.linear_velocity.x = velocity_world.x();
  out_msg->pose.linear_velocity.y = velocity_world.y();
  out_msg->pose.linear_velocity.z = velocity_world.z();

  const auto world_pos_cov = sins_.value().GetPositionWorldCov(sins_time_s);
  localization_msgs::msg::Uncertainty uncertainty;
  uncertainty.position_std_dev.x = world_pos_cov(0, 0);
  uncertainty.position_std_dev.y = world_pos_cov(1, 1);
  uncertainty.position_std_dev.z = world_pos_cov(2, 2);
  out_msg->uncertainty.push_back(uncertainty);

  t2::localization::common::SetCovariance(out_msg->covariance, sins_cov, eskf_angle_std_dev_);
  return out_msg;
}

void LiDARLocalizationRunner::Run(Eigen::Vector2d searching_space_size) {
  const Eigen::Vector3d lidarlocalization_angle_std_dev(lidarlocalization_config_.angle_std_x(),
                                                        lidarlocalization_config_.angle_std_y(),
                                                        lidarlocalization_config_.angle_std_z());

  while (is_running_) {
    std::shared_ptr<MapCache> map_cache;

    {
      std::lock_guard<std::mutex> lock(localizing_mtx_);
      map_cache = map_cache_;
      map_cache_ = nullptr;
    }

    if (map_cache == nullptr) {
      std::this_thread::sleep_for(std::chrono::milliseconds(1));
      continue;
    }

    auto lidarlocalization_result = pointcloud_registrator_.value().EstimatePosition(
        map_cache->src_map, map_cache->target_map,
        map_cache->poslv_to_world.value() * vehicle_to_poslv_, searching_space_size,
        map_cache->pos_cov.value());

    // Pass the online map and offline map out for visualization.
    if (visualizer_ != nullptr) {
      visualizer_->Visualize(map_cache->src_map, map_cache->target_map,
                             map_cache->input_messages.front().data());
    }

    Sophus::Sim3d lidarlocalization_vehicle_to_world(
        Sophus::RxSO3d(last_scale_, Sophus::SO3d(lidarlocalization_result.pose.unit_quaternion())),
        lidarlocalization_result.pose.translation());

    auto lidarlocalization_poslv_to_world =
        lidarlocalization_vehicle_to_world * vehicle_to_poslv_sim3_.inverse();

    const auto& cache_header = map_cache->input_messages.front().data().header;

    Sophus::SE3d sins_vehicle_to_world;
    Eigen::Matrix3d sins_covariance_world;
    double msg_time_s = 0;
    std::optional<Sophus::Sim3d> raw_poslv_to_world;
    {
      std::lock_guard<std::mutex> lock(mtx_);
      msg_time_s = GetTimestamp(cache_header) - initial_time_.value();
      raw_poslv_to_world = poslv_history_.GetValue(msg_time_s);
      if (raw_poslv_to_world.has_value()) {
        lidarlocalization_poslv_to_world.translation().z() =
            raw_poslv_to_world.value().translation().z();
      }
      Eigen::Matrix3d lidar_pos_cov = lidarlocalization_result.covariance;
      Eigen::Matrix3d rotate_cov = Eigen::Matrix3d::Identity();
      rotate_cov(0, 0) = std::pow(lidarlocalization_angle_std_dev.x(), 2);
      rotate_cov(1, 1) = std::pow(lidarlocalization_angle_std_dev.y(), 2);
      rotate_cov(2, 2) = std::pow(lidarlocalization_angle_std_dev.z(), 2);

      sins_.value().LiDARUpdate(
          lidarlocalization_poslv_to_world.translation(),
          lidarlocalization_poslv_to_world.rxso3().so3().unit_quaternion().normalized(),
          lidar_pos_cov, rotate_cov, msg_time_s);
      sins_vehicle_to_world = sins_.value().GetPoseWorld(msg_time_s) * vehicle_to_poslv_;
      sins_covariance_world = sins_.value().GetPositionWorldCov(msg_time_s);
    }

    {
      auto& sins_pose_writer = lidar_localization_pose_writers_.sins_pose_writer;
      auto sins_msg = sins_pose_writer->borrow_loaned_message();
      sins_msg.get() = *CreateLocalizationPoseMsg(sins_vehicle_to_world, sins_covariance_world,
                                                  last_scale_, vehicle_to_poslv_sim3_, cache_header,
                                                  lidarlocalization_angle_std_dev);
      sins_pose_writer->publish(std::move(sins_msg));
    }

    {
      auto& lidarlocalization_pose_writer =
          lidar_localization_pose_writers_.lidarlocalization_pose_writer;
      auto lidarloc_msg = lidarlocalization_pose_writer->borrow_loaned_message();
      lidarloc_msg.get() = *CreateLocalizationPoseMsg(
          lidarlocalization_result.pose, lidarlocalization_result.covariance, last_scale_,
          vehicle_to_poslv_sim3_, cache_header, lidarlocalization_angle_std_dev);
      lidarlocalization_pose_writer->publish(std::move(lidarloc_msg));
    }

    if (lidarlocalization_result.landmark_matched) {
      auto& landmark_matching_pose_writer =
          lidar_localization_pose_writers_.landmark_matching_pose_writer;
      auto landmark_msg = landmark_matching_pose_writer->borrow_loaned_message();
      landmark_msg.get() = *CreateLocalizationPoseMsg(
          lidarlocalization_result.landmark_matching_pose,
          lidarlocalization_result.landmark_matching_pose_cov, last_scale_, vehicle_to_poslv_sim3_,
          cache_header, lidarlocalization_angle_std_dev);
      landmark_matching_pose_writer->publish(std::move(landmark_msg));
    }

    {
      auto& grid_matching_pose_writer = lidar_localization_pose_writers_.grid_matching_pose_writer;
      auto gridmatch_msg = grid_matching_pose_writer->borrow_loaned_message();
      gridmatch_msg.get() = *CreateLocalizationPoseMsg(
          lidarlocalization_result.grid_matching_pose,
          lidarlocalization_result.grid_matching_pose_cov, last_scale_, vehicle_to_poslv_sim3_,
          cache_header, lidarlocalization_angle_std_dev);
      grid_matching_pose_writer->publish(std::move(gridmatch_msg));
    }

    is_lidarlocalization_applied_ = true;

    LiDARLocalizationRunnerStatus last_lidar_localization_runner_status;
    last_lidar_localization_runner_status.measurement_timestamp = static_cast<std::int64_t>(
        GetTimestamp(cache_header) * t2::common::chrono_consts::kNanosecsPerSec<double>);
    last_lidar_localization_runner_status.landmark_map_count =
        static_cast<int64_t>(map_cache->target_map.point_landmarks.size());
    last_lidar_localization_runner_status.landmark_current_count =
        static_cast<int64_t>(map_cache->src_map.point_landmarks.size());
    last_lidar_localization_runner_status.position_covariance = lidarlocalization_result.covariance;
    last_lidar_localization_runner_status.rot_std_dev = lidarlocalization_angle_std_dev;

    double lidar_timestamp_s = GetTimestamp(cache_header);
    if (last_lidar_timestamp_s_.has_value()) {
      failure_score_ *= std::exp(-1 / lidarlocalization_config_.failure_score_time_decay_s() *
                                 (lidar_timestamp_s - last_lidar_timestamp_s_.value()));
    }
    last_lidar_timestamp_s_ = lidar_timestamp_s;

    Eigen::Matrix3d sins_rotation_vehicle_to_world = sins_vehicle_to_world.so3().matrix();
    Eigen::Matrix3d sins_cov_vehicle = sins_rotation_vehicle_to_world.transpose() *
                                       sins_covariance_world * sins_rotation_vehicle_to_world;
    double sins_forward_cov = sins_cov_vehicle(0, 0);
    if (sins_forward_cov > lidarlocalization_config_.sins_cov_failure_count_threshold()) {
      failure_score_ += 0.1;  // equivalent to 1 score per second
    }

    if (failure_score_ > lidarlocalization_config_.failure_score_threshold()) {
      lidarlocalization_failed_ = true;
    }
    last_lidar_localization_runner_status.sins_forward_covariance = sins_forward_cov;
    last_lidar_localization_runner_status.failure_score = failure_score_;
    last_lidar_localization_runner_status.lidarlocalization_failed = lidarlocalization_failed_;

    {
      std::lock_guard<std::mutex> lock(last_lidar_localization_runner_status_mtx_);
      last_lidar_localization_runner_status_ = last_lidar_localization_runner_status;
    }

    WriteLiDARMap(map_cache, raw_poslv_to_world, lidarmap_writer_);
    WriteLandmarks(map_cache, source_landmarks_writer_, target_landmarks_writer_);
  }
}
}  // namespace t2::localization::compositor
