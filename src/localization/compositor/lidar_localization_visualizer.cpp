#include "lidar_localization_visualizer.hpp"

#include <cstdint>

#include <Eigen/Core>

#include <opencv2/core.hpp>
#include <opencv2/imgproc/imgproc.hpp>

#include "src/common/core/chrono_consts.hpp"
#include "src/common/core/logging.hpp"

namespace t2::localization::compositor {
namespace {
void CreateLiDARLocalizationOverlayImage(
    const LidarLocalizationLocalMap& online_map, const LidarLocalizationLocalMap& offline_map,
    const LiDARLocalizationVisualizer::BorrowedPointCloud& pointcloud,
    LiDARLocalizationVisualizer::ImagePublisherSharedPtr& image_writer) {
  if (online_map.GetMapSize() == 0 || offline_map.GetMapSize() == 0) {
    T2_WARN << "No map data for visualization.";
    return;
  }

  if (online_map.GetMapRows() != offline_map.GetMapRows() ||
      online_map.GetMapCols() != offline_map.GetMapCols()) {
    T2_WARN << "The size of online and offline maps are different.";
    return;
  }

  Eigen::Matrix<float, -1, -1, Eigen::RowMajor> online_intensity_image =
      online_map.intensity_grid_mean;
  Eigen::Matrix<float, -1, -1, Eigen::RowMajor> offline_intensity_image =
      offline_map.intensity_grid_mean;

  cv::Mat cv_online_image(online_intensity_image.rows(), online_intensity_image.cols(), CV_32FC1,
                          online_intensity_image.data());
  cv_online_image = cv_online_image.clone();
  // flip the image vertically
  cv::flip(cv_online_image, cv_online_image, 0);
  cv::Mat cv_offline_image(offline_intensity_image.rows(), offline_intensity_image.cols(), CV_32FC1,
                           offline_intensity_image.data());
  cv_offline_image = cv_offline_image.clone();
  // flip the image vertically
  cv::flip(cv_offline_image, cv_offline_image, 0);

  // Resize the image to make it smaller for visualization.
  cv::resize(cv_online_image, cv_online_image, cv::Size(), 0.5f, 0.5f, cv::INTER_LINEAR);
  cv::resize(cv_offline_image, cv_offline_image, cv::Size(), 0.5f, 0.5f, cv::INTER_LINEAR);

  // Make the image brighter for visualization.
  cv_online_image *= 5.0f;
  cv_offline_image *= 5.0f;

  cv_online_image.convertTo(cv_online_image, CV_8UC1, 255.0f);
  cv_offline_image.convertTo(cv_offline_image, CV_8UC1, 255.0f);

  // OpenCV is BGR, so we need to merge the images in the order of BGR.
  std::vector<cv::Mat> channels = {cv::Mat::zeros(cv_online_image.size(), CV_8UC1),
                                   cv_offline_image, cv_online_image};

  cv::Mat overlay_image;
  cv::merge(channels, overlay_image);

  auto output_image_msg = image_writer->borrow_loaned_message();
  auto& output_image = output_image_msg.get();
  auto& image_info = output_image.info;
  output_image.header.frame_id = pointcloud.header.frame_id;

  std::uint64_t measurement_timestamp =
      static_cast<std::uint64_t>(pointcloud.header.stamp.sec) *
          t2::common::chrono_consts::kNanosecsPerSec<std::size_t> +
      static_cast<std::uint64_t>(pointcloud.header.stamp.nanosec);
  output_image.header.measurement_timestamp = measurement_timestamp;

  std::copy(overlay_image.begin<std::uint8_t>(), overlay_image.end<std::uint8_t>(),
            output_image.data.begin());

  image_info.frame_id = pointcloud.header.frame_id;
  image_info.height = overlay_image.rows;
  image_info.width = overlay_image.cols;
  image_info.encoding = "bgr8";
  image_info.step = overlay_image.elemSize() * overlay_image.cols;

  image_writer->publish(std::move(output_image_msg));
}
}  // namespace

LiDARLocalizationVisualizer::LiDARLocalizationVisualizer(ImagePublisherSharedPtr&& image_writer)
    : image_writer_(std::move(image_writer)) {}

void LiDARLocalizationVisualizer::Visualize(const LidarLocalizationLocalMap& online_map,
                                            const LidarLocalizationLocalMap& offline_map,
                                            const BorrowedPointCloud& pointcloud) {
  CreateLiDARLocalizationOverlayImage(online_map, offline_map, pointcloud, image_writer_);
}
}  // namespace t2::localization::compositor
