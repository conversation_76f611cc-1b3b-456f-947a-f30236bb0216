#pragma once

#include <memory>
#include <utility>

#include <localizer/lidar_localization_map.h>

#include "image_msgs/msg/image.hpp"
#include "rclcpp/publisher.hpp"
#include "sensor_msgs/msg/point_cloud2.hpp"

namespace t2::localization::compositor {
class LiDARLocalizationVisualizer {
 public:
  using PointCloud = sensor_msgs::msg::PointCloud2;
  using BorrowedPointCloud = PointCloud::BorrowedType;
  using Image = image_msgs::msg::Image;
  using ImagePublisherSharedPtr = rclcpp::Publisher<Image>::SharedPtr;

  LiDARLocalizationVisualizer(const LiDARLocalizationVisualizer&) = default;
  LiDARLocalizationVisualizer(LiDARLocalizationVisualizer&&) = default;
  LiDARLocalizationVisualizer& operator=(const LiDARLocalizationVisualizer&) = default;
  LiDARLocalizationVisualizer& operator=(LiDARLocalizationVisualizer&&) = default;
  ~LiDARLocalizationVisualizer() = default;

  LiDARLocalizationVisualizer() = delete;

  explicit LiDARLocalizationVisualizer(ImagePublisherSharedPtr&& image_writer);

  void Visualize(const LidarLocalizationLocalMap& online_map,
                 const LidarLocalizationLocalMap& offline_map,
                 const BorrowedPointCloud& p_pointcloud_message);

 private:
  ImagePublisherSharedPtr image_writer_;
};
}  // namespace t2::localization::compositor
