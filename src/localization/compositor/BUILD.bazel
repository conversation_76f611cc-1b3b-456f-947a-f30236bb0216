package(default_visibility = ["//visibility:public"])

cc_library(
    name = "lidar_localization_runner",
    srcs = [
        "lidar_localization_runner.cpp",
    ],
    hdrs = [
        "lidar_localization_runner.hpp",
    ],
    data = [
        "//src/localization/conf:eskf_config",
        "//src/localization/conf:lidarmaplocalization_config",
    ],
    deps = [
        ":lidar_localization_visualizer",
        "//src/common/core",
        "//src/common/math:quaternion",
        "//src/common/util:file",
        "//src/drivers/lidar/hesai_msgs",
        "//src/interfaces/image_msgs",
        "//src/interfaces/localization_msgs",
        "//src/localization/common:localization_util",
        "//src/localization/lidar/preprocessor:motion_undistortion_lib",
        "//src/localization/lidar/util:point_cloud2_container",
        "//src/localization/proto:eskf_config_cc_proto",
        "//src/localization/proto:lidarmaplocalization_config_cc_proto",
        "@apex//grace/interfaces/sensor_msgs",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@com_github_sophus//:sophus",
        "@eigen",
        "@multi_sensor_localization//:calibrator",
        "@multi_sensor_localization//:data_converter",
        "@multi_sensor_localization//:lidar_localization",
        "@multi_sensor_localization//:sins",
    ],
)

cc_library(
    name = "lidar_localization_visualizer",
    srcs = [
        "lidar_localization_visualizer.cpp",
    ],
    hdrs = [
        "lidar_localization_visualizer.hpp",
    ],
    deps = [
        "//src/common/core",
        "//src/interfaces/image_msgs",
        "@apex//grace/interfaces/sensor_msgs",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@eigen",
        "@local_opencv//:core",
        "@local_opencv//:imgproc",
        "@multi_sensor_localization//:lidar_localization",
    ],
)
