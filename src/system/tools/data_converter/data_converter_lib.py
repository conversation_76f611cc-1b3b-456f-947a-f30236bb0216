import logging
import shutil
import time
from collections import defaultdict
from difflib import get_close_matches
from pathlib import Path
from typing import Callable, Optional

import yaml
from converter_topics_list import CONVERTER_LIST
from mcap.reader import make_reader
from mcap_protobuf.decoder import DecoderFactory
from rclpy.serialization import serialize_message
from rosbag2_py import ConverterOptions, SequentialWriter, StorageOptions, TopicMetadata
from tqdm import tqdm

logger = logging.getLogger("data_converter")
logger.setLevel(logging.INFO)

if not logger.handlers:  # Prevent adding handlers multiple times
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    formatter = logging.Formatter("[%(levelname)s] %(message)s")
    console_handler.setFormatter(formatter)

    logger.addHandler(console_handler)


class DataConverter:
    def __init__(self, output_dir: Path, topics_list: list, is_overwrite: bool) -> None:
        self.output_dir = output_dir
        self.is_overwrite = is_overwrite
        self._topics_list_check(topics_list)
        self.stats = defaultdict(lambda: {"ok": 0, "fail": 0, "skipped": 0})
        if not output_dir.exists():
            output_dir.mkdir(parents=True, exist_ok=True)
        self.writer = None

    def _topics_list_check(self, topics_list: list) -> None:
        if topics_list is None:
            self.topics_list = list(CONVERTER_LIST.keys())
            return
        for topic in topics_list:
            if topic not in CONVERTER_LIST:
                suggestion = get_close_matches(topic, CONVERTER_LIST.keys(), n=1)
                hint = f" Did you mean: {suggestion[0]}?" if suggestion else ""
                raise ValueError(
                    f"Invalid topic: {topic}.{hint} \nValid topics are: {list(CONVERTER_LIST.keys())}"
                )
        self.topics_list = topics_list
        logger.info(f"Using topics: {self.topics_list}")

    def _init_writer(self, output_path: Path, filtered_topics: list) -> None:
        if output_path.exists():
            if self.is_overwrite:
                logger.warning(f"[OVERWRITE WARNING] Overwriting existing output at {output_path}")
                shutil.rmtree(output_path)
            else:
                raise RuntimeError(
                    f"Bag directory already exists ({output_path}), can't overwrite existing bag"
                )

        self.writer = SequentialWriter()
        self.writer.open(
            StorageOptions(uri=str(output_path), storage_id="mcap"),
            ConverterOptions(input_serialization_format="cdr", output_serialization_format="cdr"),
        )

        for topic_id, topic in enumerate(filtered_topics):
            writer_topic = CONVERTER_LIST[topic].get("apex_topic", topic)
            self.writer.create_topic(
                TopicMetadata(
                    id=topic_id,
                    # The ID is not used in MCAP, so we can set it to 0
                    # or any other value.
                    name=writer_topic,
                    type=CONVERTER_LIST[topic]["ros_type"],
                    serialization_format="cdr",
                )
            )

    def _get_converter(self, mcap_topic: str) -> Optional[Callable]:
        return CONVERTER_LIST.get(mcap_topic, {}).get("converter", None)

    def _write_stats_file(self, output_path: Path, summary_str: str) -> None:
        stats_path = output_path / (output_path.stem + "_stats.txt")
        with stats_path.open("w") as txt_file:
            txt_file.write(summary_str)
        logger.info(f"[STATS SAVED] Wrote conversion stats to {stats_path}")

    def _check_mcap_topic_coverage(self, mcap_file_path: Path) -> Optional[list]:
        with open(mcap_file_path, "rb") as f:
            reader = make_reader(f, decoder_factories=[DecoderFactory()])
            summary = reader.get_summary()
            available_topics = set()

            if summary is None or not summary.channels:
                logger.warning(
                    f"[TOPIC CHECK WARNING] No summary found in {mcap_file_path.name}. "
                    "Skipping topic coverage check."
                )
                return
            available_topics = {channel.topic for channel in summary.channels.values()}

        missing_topics = [topic for topic in self.topics_list if topic not in available_topics]
        filtered_topics = [topic for topic in self.topics_list if topic in available_topics]
        if missing_topics:
            logger.warning(
                f"[TOPIC MISSING WARNING] The following topics were not found in {mcap_file_path.name}:\n"
                f"{missing_topics}\n"
            )
        return filtered_topics

    @staticmethod
    def _patch_tf_static_qos(metadata_path: Path):
        if not metadata_path.exists():
            raise FileNotFoundError(f"Metadata file not found: {metadata_path}")
        with open(metadata_path) as f:
            metadata = yaml.safe_load(f)

        for topic in metadata["rosbag2_bagfile_information"]["topics_with_message_count"]:
            if topic["topic_metadata"]["name"] == "/tf_static":
                topic["topic_metadata"]["offered_qos_profiles"] = [
                    {
                        "history": "keep_last",
                        "depth": 1,
                        "reliability": "reliable",
                        "durability": "transient_local",
                        "deadline": {"sec": 0, "nsec": 0},
                        "lifespan": {"sec": 0, "nsec": 0},
                        "liveliness": "system_default",
                        "liveliness_lease_duration": {"sec": 0, "nsec": 0},
                        "avoid_ros_namespace_conventions": False,
                        "max_allocated_samples": 50,
                        "max_non_self_contained_type_serialized_size": 70224,
                    }
                ]
                break

        with open(metadata_path, "w") as f:
            yaml.dump(metadata, f)

    def process_mcap_file(self, mcap_file_path: Path) -> None:
        start_time = time.time()
        logger.info(f"[START] Processing {mcap_file_path}")

        filtered_topics = self._check_mcap_topic_coverage(mcap_file_path)

        output_path = self.output_dir / f"{mcap_file_path.stem}_rosbag2"
        if output_path.exists() and not self.is_overwrite:
            logger.warning(f"[OUTPUT WARNING] Output already exists: {output_path}")
            return

        self._init_writer(output_path, filtered_topics)

        file_size = mcap_file_path.stat().st_size
        with open(mcap_file_path, "rb") as f:
            reader = make_reader(f, decoder_factories=[DecoderFactory()])
            progress = tqdm(total=file_size, desc=mcap_file_path.name, unit="B", unit_scale=True)

            for _schema, channel, msg, proto_msg in reader.iter_decoded_messages():
                topic = channel.topic
                timestamp = msg.log_time
                converter = self._get_converter(topic)

                if converter is None or topic not in filtered_topics:
                    self.stats[topic]["skipped"] += 1
                    continue

                try:
                    ros_msg = converter(proto_msg)
                except Exception as e:
                    self.stats[topic]["fail"] += 1
                    logger.error(f"[CONVERT ERROR] {topic}: {e}")
                    continue

                try:
                    writer_topic = CONVERTER_LIST[topic].get("apex_topic", topic)
                    self.writer.write(writer_topic, serialize_message(ros_msg), timestamp)
                    self.stats[topic]["ok"] += 1
                except Exception as e:
                    self.stats[topic]["fail"] += 1
                    logger.error(f"[SERIALIZE ERROR] {topic}: {e}")

                progress.n = f.tell()
                progress.refresh()

            progress.close()
            if self.stats:
                summary_lines = []
                summary_lines.append("\n[SUMMARY]")
                summary_lines.append(f"{'Topic':<60} {'OK':>6}  {'FAIL':>6}  {'SKIP':>6}")
                summary_lines.append("-" * 85)
                for topic, counts in self.stats.items():
                    summary_lines.append(
                        f"{topic:<60} {counts['ok']:>6}  {counts['fail']:>6}  {counts['skipped']:>6}"
                    )
                summary_str = "\n".join(summary_lines)
                print(summary_str)
                self._write_stats_file(output_path, summary_str)

        del self.writer  # Finalize file
        self.writer = None
        self._patch_tf_static_qos(output_path / "metadata.yaml")
        elapsed_time = time.time() - start_time
        logger.info(
            f"[DONE] Processed {mcap_file_path.name} in {elapsed_time:.2f} seconds. "
            f"Output written to: {output_path}"
        )
