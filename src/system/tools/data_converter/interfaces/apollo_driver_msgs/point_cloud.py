import math
import struct

from interfaces.apollo_common_msgs.header import St<PERSON><PERSON><PERSON>er<PERSON><PERSON>per
from sensor_msgs.msg import PointCloud2, PointField


class PointCloudMapper:
    @classmethod
    def convert(cls, data) -> PointCloud2:
        pc2_ros = PointCloud2()

        if hasattr(data, "header"):
            pc2_ros.header = StdHeaderMapper.convert(data.header)

        # Reference: src/drivers/lidar/common/src/point_cloud_utils.cpp
        if hasattr(data, "internal_pointdata"):
            # 36 bytes per point: x, y, z, intensity, timestamp_sec(float64), ring_id, velocity, float_intensity
            pc2_ros.fields = [
                PointField(name="x", offset=0, datatype=PointField.FLOAT32, count=1),
                PointField(name="y", offset=4, datatype=PointField.FLOAT32, count=1),
                PointField(name="z", offset=8, datatype=PointField.FLOAT32, count=1),
                PointField(name="intensity", offset=12, datatype=<PERSON>Field.UINT32, count=1),
                Point<PERSON>ield(name="timestamp_sec", offset=16, datatype=PointField.FLOAT64, count=1),
                PointField(name="ring_id", offset=24, datatype=PointField.UINT32, count=1),
                PointField(name="velocity", offset=28, datatype=PointField.FLOAT32, count=1),
                PointField(name="float_intensity", offset=32, datatype=PointField.FLOAT32, count=1),
            ]

            # Matching layout
            # fffIIdff4x: float32 x3, uint32, float64, uint32, float32 x2 + 4byte padding
            fmt = "<fffIdIff4x"  # 40 bytes
            pack_size = struct.calcsize(fmt)
            buf = bytearray()
            for pt in data.internal_point:
                timestamp_sec = pt.timestamp * 1e-9  # convert nanoseconds to seconds (float64)

                buf.extend(
                    struct.pack(
                        fmt,
                        pt.x,
                        pt.y,
                        pt.z,
                        pt.intensity,
                        timestamp_sec,
                        pt.ring_id,
                        pt.velocity,
                        pt.float_intensity,
                    )
                )

            pc2_ros.height = 1
            pc2_ros.width = len(data.internal_point)
            pc2_ros.is_bigendian = False
            pc2_ros.point_step = pack_size
            pc2_ros.row_step = pack_size * pc2_ros.width
            pc2_ros.data = bytes(buf)
            pc2_ros.is_dense = all(
                not math.isnan(pt.x) and not math.isnan(pt.y) and not math.isnan(pt.z)
                for pt in data.internal_point
            )

        return pc2_ros
