from typing import Any

from interfaces.apollo_common_msgs.header import <PERSON>er<PERSON><PERSON><PERSON>
from localization_msgs.msg import ReadyJ<PERSON>State, ReadyJudgeStatus


class ReadyJudgeMapper:
    @classmethod
    def convert(cls, data: Any) -> ReadyJudgeStatus:
        ready_judge_ros2 = ReadyJudgeStatus()

        if hasattr(data, "header"):
            ready_judge_ros2.header = HeaderMapper.convert(data.header)

        if hasattr(data, "state"):
            if data.state == 0:
                ready_judge_ros2.state = ReadyJudgeState.READY_JUDGE_OK
            elif data.state == 2:
                ready_judge_ros2.state = ReadyJudgeState.READY_JUDGE_NOT_RTK_FIXED
            elif data.state == 3:
                ready_judge_ros2.state = ReadyJudgeState.READY_JUDGE_UNCERTAINTY_LARGE
            elif data.state == 5:
                ready_judge_ros2.state = ReadyJudgeState.READY_JUDGE_FAR_FROM_HDMAP
            else:
                raise ValueError(f"Unknown ReadyJudgeState value: {data.state}")

        if hasattr(data, "measurement_time"):
            ready_judge_ros2.measurement_time = data.measurement_time

        return ready_judge_ros2
