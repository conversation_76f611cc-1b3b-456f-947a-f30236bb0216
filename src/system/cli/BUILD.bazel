load("@apex//common/bazel/rules_deployment:defs.bzl", "configured_binary", "configured_tool")
load("//src/interfaces:defs.bzl", "INTERFACE_LIBS_AMENT_RESOURCES", "INTERFACE_LIBS_PY")

# Define custom CLI tool
configured_tool(
    name = "cli",
    data = INTERFACE_LIBS_AMENT_RESOURCES,
    framework = "Project Yatagarasu",  # This just sets a string in the help menu thats printed, so feel free to change this
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/cli",
        "@apex//grace/cli/ros2service",
    ] + INTERFACE_LIBS_PY,
)

# Define the custom replay_coordinator
# Only declare used dependencies
configured_tool(
    name = "replay_coordinator_tools",
    data = [
        "@apex//grace/recording/rosbag2/rosbag2_storage_mcap:pluginlib_resources",
        "@apex//grace/recording/rosbag2/rosbag2_storage_sqlite3:pluginlib_resources",
    ] + INTERFACE_LIBS_AMENT_RESOURCES,
    framework = "Ya<PERSON><PERSON><PERSON> replay_coordinator",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/recording/rosbag2/rosbag2_storage_mcap:ros2bag_mcap_cli",
        "@apex//grace/recording/rosbag2/rosbag2_storage_sqlite3:ros2bag_sqlite3_cli",
    ] + INTERFACE_LIBS_PY,
)

configured_binary(
    name = "replay_coordinator",
    executable = "@apex//grace/recording/replay:replay_coordinator_exe",
    searchpath_executables = [
        ":replay_coordinator_tools",
    ],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "as_replay",
    srcs = [":script/as_replay.sh"],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "text_logger",
    srcs = [":script/text_logger.sh"],
    visibility = ["//visibility:public"],
)
