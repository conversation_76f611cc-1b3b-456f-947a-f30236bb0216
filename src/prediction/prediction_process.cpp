// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "prediction_process.hpp"

#include <cmath>
#include <memory>
#include <optional>
#include <tuple>

#include <localization_msgs/msg/localization_estimate.hpp>
#include <perception_msgs/msg/perception_obstacles.hpp>
#include <prediction_msgs/msg/feature.hpp>
#include <prediction_msgs/msg/prediction_obstacles.hpp>

#include "src/common/core/logging.hpp"
#include "src/perception/common/idl_util/optional_util.hpp"
#include "src/prediction/common/feature_utils.hpp"
#include "src/prediction/common/map/lane_graph_builder.hpp"
#include "src/prediction/common/map/prediction_map.hpp"
#include "src/prediction/common/postprocess.hpp"
#include "src/prediction/common/prediction_gflags.hpp"
#include "src/prediction/obstacle/obstacle.hpp"
#include "src/prediction/obstacle/obstacle_status_updater.hpp"
#include "src/prediction/proto/prediction_conf.pb.h"

namespace t2::prediction {
using PerceptionObstacles = PredictionProcess::PerceptionObstacles;
using PredictionObstacles = PredictionProcess::PredictionObstacles;
using LocalizationEstimate = PredictionProcess::LocalizationEstimate;
using Feature = ::prediction_msgs::msg::Feature;
using namespace t2::perception::common::idl_util;

bool PredictionProcess::Init([[maybe_unused]] const t2::prediction::PredictionConf& conf) {
  if (!PredictionMap::Ready()) {
    T2_ERROR << "Prediction map is not ready";
    return false;
  }

  ego_vehicle_ =
      std::make_unique<Obstacle>(FLAGS_ego_vehicle_id, ::perception_msgs::msg::ClassType::VEHICLE);

  evaluator_manager_.Init(conf);
  predictor_manager_.Init(conf);

  return true;
}

std::optional<PredictionObstacles> PredictionProcess::DoProcess(
    const PerceptionObstacles& perception_obstacles, const LocalizationEstimate& localization_msg) {
  if (!ego_vehicle_) {
    T2_ERROR << "Initialization process is not complete!";
    return std::nullopt;
  }

  /// 自車obstacleを更新
  OnLocalization(localization_msg);

  /// obstacles_containerの更新
  obstacles_container_.ClearObstacleStatus();
  for (const auto& perception_obstacle : perception_obstacles.perception_obstacle) {
    obstacles_container_.InsertPerceptionObstacle(perception_obstacle);
  }

  // TODO(ando): TMP-160 フラグないしconfigから読ませる
  constexpr double kTimeDeemedAsOutdated = 10.0;
  /// 古いobstacleを削除
  std::ignore = obstacles_container_.RemoveOutdatedObstacles(kTimeDeemedAsOutdated);

  /// obstacleの前処理実行
  ObstacleStatusUpdater::RunPreprocessForObstacles(*ego_vehicle_->LatestFeature(),
                                                   obstacles_container_);

  /// Evaluation（obstacleが将来進行するレーンの確率を割り当てる）
  evaluator_manager_.Run(obstacles_container_);

  /// Prediction実行
  auto prediction_obstacles = predictor_manager_.Run(perception_obstacles, obstacles_container_);

  // 空のtrajectoryを持つobstacleを削除
  RemoveObstaclesWithEmptyTrajectory(prediction_obstacles);

  /// デバッグ・評価用情報の追加
  SetRelativeOrientation(prediction_obstacles, localization_msg);

  /// 結果の返却
  return {std::move(prediction_obstacles)};
}

void PredictionProcess::OnLocalization(const LocalizationEstimate& localization_estimate) {
  const auto& previous_ego_feature = ego_vehicle_->LatestFeature();
  std::optional<Feature> ego_new_feature_opt;
  if (!previous_ego_feature) {
    Feature temp;
    ego_new_feature_opt = FeatureUtils::FromLocalizationEstimate(localization_estimate, temp);
  } else {
    ego_new_feature_opt = FeatureUtils::FromLocalizationEstimate(localization_estimate,
                                                                 *ego_vehicle_->LatestFeature());
  }

  if (!ego_new_feature_opt.has_value()) {
    T2_ERROR << "Failed to convert ego feature.";
    return;
  }

  ego_vehicle_->InsertFeature(ego_new_feature_opt.value());

  if (HasValue(ego_vehicle_->LatestFeature()->junction_feature)) {
    LaneGraphBuilder::BuildLaneGraph(Value(ego_vehicle_->LatestFeature()->junction_feature),
                                     ego_vehicle_->LatestFeature()->speed,
                                     *ego_vehicle_->LatestFeature());
  } else {
    LaneGraphBuilder::BuildLaneGraph(LaneGraphBuilder::JunctionFeature{},
                                     ego_vehicle_->LatestFeature()->speed,
                                     *ego_vehicle_->LatestFeature());
  }
}

void PredictionProcess::SetRelativeOrientation(PredictionObstacles& prediction_result,
                                               const LocalizationEstimate& localization_estimate) {
  // 自車のオリエンテーション
  const auto& ego_velocity = localization_estimate.pose.linear_velocity;
  const auto& ego_position = localization_estimate.pose.position;
  const double ego_heading = localization_estimate.pose.heading;

  // 世界座標系 -> 車両座標系 のZ軸周りの2D回転を適用
  auto rotation_world_to_vehicle = [cos_theta = std::cos(-ego_heading),
                                    sin_theta = std::sin(-ego_heading)](double x, double y) {
    const auto rot_x = cos_theta * x - sin_theta * y;
    const auto rot_y = sin_theta * x + cos_theta * y;

    return std::make_pair(rot_x, rot_y);
  };

  for (auto& prediction_obstacle : prediction_result.prediction_obstacles) {
    // Trackingのメッセージは存在を仮定（管轄範囲が同じなので
    const auto& obstacle_pos = prediction_obstacle.perception_obstacle.position;
    const auto& obstacle_velocity = prediction_obstacle.perception_obstacle.velocity;
    const double obstacle_heading = prediction_obstacle.feature.theta;

    // TODO(ando): localization_estimate.pose.positionの座標系の考慮（novatel座標系の値らしい）
    const auto [relative_position_x, relative_position_y] =
        rotation_world_to_vehicle(obstacle_pos.x - ego_position.x, obstacle_pos.y - ego_position.y);

    const auto [relative_velocity_x, relative_velocity_y] = rotation_world_to_vehicle(
        obstacle_velocity.x - ego_velocity.x, obstacle_velocity.y - ego_velocity.y);

    const auto [absolute_velocity_x, absolute_velocity_y] =
        rotation_world_to_vehicle(obstacle_velocity.x, obstacle_velocity.y);

    const double relative_heading = obstacle_heading - ego_heading;

    // zは正しい値が入っていない可能性がある + 必要ない？
    // z軸の値は姿勢変換が必要ない（はず
    // const double relative_position_z = obstacle_pos.z() - ego_position.z();
    // const double relative_velocity_z = obstacle_velocity.z() -
    // ego_velocitz.y();

    prediction_obstacle.perception_obstacle.local_position.x = relative_position_x;
    prediction_obstacle.perception_obstacle.local_position.y = relative_position_y;

    prediction_obstacle.perception_obstacle.local_relative_velocity.x = relative_velocity_x;
    prediction_obstacle.perception_obstacle.local_relative_velocity.y = relative_velocity_y;

    prediction_obstacle.perception_obstacle.local_velocity.x = absolute_velocity_x;
    prediction_obstacle.perception_obstacle.local_velocity.y = absolute_velocity_y;

    prediction_obstacle.perception_obstacle.local_theta = relative_heading;
  }
}
}  // namespace t2::prediction
