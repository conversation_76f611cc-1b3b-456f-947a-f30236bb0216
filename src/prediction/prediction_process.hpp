// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <optional>

#include <localization_msgs/msg/localization_estimate.hpp>
#include <perception_msgs/msg/perception_obstacles.hpp>
#include <prediction_msgs/msg/prediction_obstacles.hpp>

#include "src/prediction/evaluator/evaluator_manager.hpp"
#include "src/prediction/obstacle/obstacles_container.hpp"
#include "src/prediction/predictor/predictor_manager.hpp"
#include "src/prediction/proto/prediction_conf.pb.h"

namespace t2::prediction {

class PredictionProcess {
 public:
  using PerceptionObstacles = ::perception_msgs::msg::PerceptionObstacles;
  using PredictionObstacles = ::prediction_msgs::msg::PredictionObstacles;
  using LocalizationEstimate = ::localization_msgs::msg::LocalizationEstimate;

 public:
  bool Init(const t2::prediction::PredictionConf& conf);

  std::optional<PredictionObstacles> DoProcess(const PerceptionObstacles& perception_obstacles,
                                               const LocalizationEstimate& localization_msg);

 private:
  void OnLocalization(const LocalizationEstimate& localization_estimate);

  /**
   * @brief
   * PredictionObstaclesの各obstacleに対して、自車との相対的な位置・速度・Headingの情報を付加する
   * @param[in, out] prediction_result
   * 相対オリエンテーションを追加するPredictionObstacles
   * @param[in] localization_estimate 現フレームの自車の位置と姿勢、速度情報
   */
  static void SetRelativeOrientation(PredictionObstacles& prediction_result,
                                     const LocalizationEstimate& localization_estimate);

  ObstaclesContainer obstacles_container_;

  EvaluatorManager evaluator_manager_;

  PredictorManager predictor_manager_;

  std::unique_ptr<Obstacle> ego_vehicle_;
};
}  // namespace t2::prediction
