load("@rules_cc//cc:defs.bzl", "cc_library")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "prediction_process",
    srcs = ["prediction_process.cpp"],
    hdrs = [
        "prediction_process.hpp",
    ],
    deps = [
        "//src/common/contract_assertions",
        "//src/common/core",
        "//src/interfaces/localization_msgs",
        "//src/interfaces/prediction_msgs",
        "//src/perception/tracking/tracker",
        "//src/perception/tracking/utils",
        "//src/prediction/common:postprocess",
        "//src/prediction/common:prediction_common_utils",
        "//src/prediction/common/map:lane_graph_builder",
        "//src/prediction/evaluator:evaluator_manager",
        "//src/prediction/obstacle:obstacle_status_updater",
        "//src/prediction/predictor:predictor_manager",
        "//src/prediction/proto:prediction_conf_cc_proto",
    ],
)

cc_library(
    name = "prediction_component",
    srcs = [
        "prediction_component.cpp",
    ],
    hdrs = [
        "prediction_component.hpp",
    ],
    deps = [
        ":prediction_process",
        "//src/common/contract_assertions",
        "//src/common/core",
        "//src/interfaces/localization_msgs",
        "//src/interfaces/prediction_msgs",
        "//src/prediction/common:prediction_gflags",
        "//src/prediction/proto:prediction_conf_cc_proto",
        "@apex//common/cpputils",
        "@apex//grace/execution/executor2",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@fmtlib",
    ],
)

filegroup(
    name = "prediction_testdata",
    srcs = glob(["testdata/**"]),
)
