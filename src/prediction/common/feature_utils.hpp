// Copyright (c) 2024-2025 T2 Inc. All rights reserved
#pragma once

#include <unordered_set>

#include <localization_msgs/msg/localization_estimate.hpp>
#include <perception_msgs/msg/perception_obstacles.hpp>
#include <prediction_msgs/msg/feature.hpp>

namespace t2::prediction {
namespace internal {
/**
 * @brief Set the Acceleration object.
 *
 * @param feature feature object to be updated
 * @param previous_feature previous feature used to compute acceleration by
 * taking the derivative of velocity.
 * @param perception_obstacle perception obstacle object. If it has the
 * acceleration estimation by tracking, use it instead of using derivative of
 * velocity.
 */
void SetAcceleration(
    ::prediction_msgs::msg::Feature* feature,
    const ::prediction_msgs::msg::Feature& previous_feature,
    const std::optional<std::reference_wrapper<const ::perception_msgs::msg::PerceptionObstacle>>&
        perception_obstacle);
}  // namespace internal

/**
 * @brief Collection of util functions related to
 * src/interfaces/prediction_msgs/Feature.idl objects.
 */
class FeatureUtils {
 public:
  using Feature = ::prediction_msgs::msg::Feature;
  using LaneSequence = ::prediction_msgs::msg::LaneSequence;
  using PerceptionObstacle = ::perception_msgs::msg::PerceptionObstacle;
  using PerceptionObstacles = ::perception_msgs::msg::PerceptionObstacles;
  using LocalizationEstimate = ::localization_msgs::msg::LocalizationEstimate;

 public:
  /**
   * @brief Check the given feature has a junction with exits or not.
   *
   * @param feature feature to be checked.
   * @return true when the given feature has a junction with exits.
   * @return false when the given feature has no junction.
   */
  static bool HasJunctionWithExits(const Feature& feature);

  /**
   * @brief Check the given feature has a given junction exit lane or not.
   *
   * @param feature feature to be checked.
   * @param lane_sequence given target lane sequence.
   * @param exit_lane_id_set ID of lane exiting the junction
   * @return true if the feature is on one of the lane.
   * @return false
   */
  static bool HasJunctionExitLane(const Feature& feature, const LaneSequence& lane_sequence,
                                  const std::unordered_set<std::string>& exit_lane_id_set);

  /**
   * @brief Create feature object from perception obstacle object.
   *
   * @param perception_obstacle perception obstacle object from the data
   * @param previous_feature feature in the previous time stamp to interpolate
   * necessary information such as acceleration.
   * @return new feature object.
   */
  static Feature FromPerceptionObstacle(const PerceptionObstacle& perception_obstacle,
                                        const Feature& previous_feature);

  /**
   * @brief Convert localization estimate result to feature.
   *
   * @param localization_estimate localization result
   * @param previous_ego_feature feature in the previous time stamp to
   * interpolate necessary information such as acceleration.
   * @return std::optional<Feature>
   */
  static std::optional<Feature> FromLocalizationEstimate(
      const LocalizationEstimate& localization_estimate, const Feature& previous_ego_feature);
};

}  // namespace t2::prediction
