// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "common/point_cloud_utils.hpp"

#include <vector>

#include <gtest/gtest.h>

using PointCloud2 = sensor_msgs::msg::PointCloud2;
using hesai_msgs::msg::PointXYZIT;

namespace t2::drivers::lidar::common {

TEST(PointCloudUtilsTest, TestInitPclMsg) {
  // Create a point cloud message
  PointCloud2::BorrowedType cloud{rosidl_runtime_cpp::ConstructorTag{}};
  apex::string_strict32_t frame_id = "test_frame";
  size_t points_size = 100;

  // Call the init_pcl_msg function
  EXPECT_NO_THROW(init_pcl_msg(cloud, frame_id, points_size));

  // Verify the initialization
  EXPECT_EQ(cloud.width, 0U);
  EXPECT_EQ(cloud.height, 1U);
  EXPECT_EQ(cloud.is_bigendian, false);
  EXPECT_EQ(cloud.is_dense, true);
  EXPECT_EQ(cloud.header.frame_id, frame_id);

  // Check that fields are correctly initialized
  ASSERT_EQ(cloud.fields.size(), 8U);
  EXPECT_EQ(cloud.fields[0].name, "x");
  EXPECT_EQ(cloud.fields[1].name, "y");
  EXPECT_EQ(cloud.fields[2].name, "z");
  EXPECT_EQ(cloud.fields[3].name, "intensity");
  EXPECT_EQ(cloud.fields[4].name, "timestamp_sec");
  EXPECT_EQ(cloud.fields[5].name, "ring_id");
  EXPECT_EQ(cloud.fields[6].name, "velocity");
  EXPECT_EQ(cloud.fields[7].name, "float_intensity");

  EXPECT_EQ(cloud.point_step, sizeof(PointXYZIT));

  // Verify data size
  EXPECT_GE(cloud.data.capacity(), points_size * sizeof(PointXYZIT));
  EXPECT_EQ(cloud.data.size(), 0U);

  // Add a point
  PointXYZIT point;
  EXPECT_TRUE(add_point(cloud, point));
  EXPECT_EQ(cloud.data.size(), 1U * sizeof(PointXYZIT));

  // init again then verify data size
  EXPECT_NO_THROW(init_pcl_msg(cloud, frame_id, points_size));
  EXPECT_GE(cloud.data.capacity(), points_size * sizeof(PointXYZIT));
  EXPECT_EQ(cloud.data.size(), 0U);
}

TEST(PointCloudUtilsTest, TestInitPclMsgInvalidSize) {
  // Create a point cloud message
  PointCloud2::BorrowedType cloud{rosidl_runtime_cpp::ConstructorTag{}};
  apex::string_strict32_t frame_id = "test_frame";
  size_t points_size = 300231;  // Invalid size for testing (75000+ bytes)

  // Call the init_pcl_msg function and expect an exception
  EXPECT_THROW(init_pcl_msg(cloud, frame_id, points_size), apex::runtime_error);
}

TEST(PointCloudUtilsTest, TestAddPoint) {
  // Create a point cloud message with enough capacity for testing
  PointCloud2::BorrowedType cloud{rosidl_runtime_cpp::ConstructorTag{}};
  apex::string_strict32_t frame_id = "test_frame";
  size_t points_size = 2;
  EXPECT_NO_THROW(init_pcl_msg(cloud, frame_id, points_size));

  // Create a test point
  PointXYZIT point;
  point.x = 1.0F;
  point.y = 2.0f;
  point.z = 3.0F;
  point.intensity = 100.0F;
  point.timestamp_sec = 123456.0;
  point.ring_id = 5U;
  point.velocity = 2.5F;
  point.float_intensity = 1.0F;

  // Verify point was added
  EXPECT_TRUE(add_point(cloud, point));
  EXPECT_EQ(cloud.width, 1U);
  EXPECT_EQ(cloud.row_step, cloud.width * cloud.point_step);

  // Verify the point was added correctly
  PointXYZIT retrieved1 = get_point(cloud, 0U);
  EXPECT_FLOAT_EQ(retrieved1.x, point.x);
  EXPECT_FLOAT_EQ(retrieved1.y, point.y);
  EXPECT_FLOAT_EQ(retrieved1.z, point.z);
  EXPECT_FLOAT_EQ(retrieved1.intensity, point.intensity);
  EXPECT_EQ(retrieved1.timestamp_sec, point.timestamp_sec);
  EXPECT_EQ(retrieved1.ring_id, point.ring_id);
  EXPECT_FLOAT_EQ(retrieved1.velocity, point.velocity);
  EXPECT_FLOAT_EQ(retrieved1.float_intensity, point.float_intensity);

  // Add another point
  point.x = 4.0f;
  // Verify second point was added
  EXPECT_TRUE(add_point(cloud, point));
  EXPECT_EQ(cloud.width, 2U);
  EXPECT_EQ(cloud.row_step, cloud.width * cloud.point_step);
}

TEST(PointCloudUtilsTest, TestSetPoint) {
  // Create a point cloud with 5 points
  PointCloud2::BorrowedType cloud{rosidl_runtime_cpp::ConstructorTag{}};
  apex::string_strict32_t frame_id = "test_frame";
  size_t points = 5;
  init_pcl_msg(cloud, frame_id, points);
  // Assert the point cloud size
  EXPECT_EQ(cloud.width, 0U);

  // Add default point
  PointXYZIT point;
  EXPECT_TRUE(add_point(cloud, point));
  EXPECT_EQ(cloud.width, 1U);
  EXPECT_EQ(cloud.row_step, cloud.width * cloud.point_step);

  // Modify the test point
  point.x = 1.0f;
  point.y = 2.0f;
  point.z = 3.0f;
  point.intensity = 100.0f;
  point.timestamp_sec = 123456.0;
  point.ring_id = 5;
  point.velocity = 2.5f;
  point.float_intensity = 1.0f;

  // Set a point at an existing index
  EXPECT_TRUE(set_point(cloud, 0, point));
  EXPECT_EQ(cloud.width, 1U);  // Width should not change

  // Verify the point was set correctly
  PointXYZIT retrieved_point = get_point(cloud, 0);
  EXPECT_FLOAT_EQ(retrieved_point.x, point.x);
  EXPECT_FLOAT_EQ(retrieved_point.y, point.y);
  EXPECT_FLOAT_EQ(retrieved_point.z, point.z);
  EXPECT_FLOAT_EQ(retrieved_point.intensity, point.intensity);
  EXPECT_EQ(retrieved_point.timestamp_sec, point.timestamp_sec);
  EXPECT_EQ(retrieved_point.ring_id, point.ring_id);
  EXPECT_FLOAT_EQ(retrieved_point.velocity, point.velocity);
  EXPECT_FLOAT_EQ(retrieved_point.float_intensity, point.float_intensity);

  // Set a point at an index beyond current size but within capacity
  point.x = 10.0f;
  EXPECT_FALSE(set_point(cloud, 1, point));
}

}  // namespace t2::drivers::lidar::common
