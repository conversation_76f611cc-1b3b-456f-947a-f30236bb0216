
#include "common/point_cloud_utils.hpp"

#include <cpputils/safe_cast.hpp>

namespace t2::drivers::lidar::common {

using apex::cast::safe_cast;
using hesai_msgs::msg::PointXYZIT;

namespace {

template <typename PointCloud2Type>
void InitPointCloudTemplate(PointCloud2Type& cloud, const apex::string_strict128_t& frame_id,
                            const size_t size) {
  // Unorganized point cloud structure
  // related files:
  // - src/drivers/lidar/hesai_msgs/msg/PointXYZIT.idl
  // - src/system/tools/data_converter/interfaces/apollo_driver_msgs/point_cloud.py
  // - src/localization/lidar/util/point_cloud2_field.hpp
  cloud.width = 0U;
  cloud.height = 1U;
  uint32_t idx = 0U;
  cloud.fields.resize(8U);
  cloud.fields[0U].name = "x";
  cloud.fields[0U].offset = idx;
  cloud.fields[0U].datatype = sensor_msgs::msg::PointField::FLOAT32;
  cloud.fields[0U].count = 1U;
  idx += sizeof(PointXYZIT::x);

  cloud.fields[1U].name = "y";
  cloud.fields[1U].offset = idx;
  cloud.fields[1U].datatype = sensor_msgs::msg::PointField::FLOAT32;
  cloud.fields[1U].count = 1U;
  idx += sizeof(PointXYZIT::y);

  cloud.fields[2U].name = "z";
  cloud.fields[2U].offset = idx;
  cloud.fields[2U].datatype = sensor_msgs::msg::PointField::FLOAT32;
  cloud.fields[2U].count = 1U;
  idx += sizeof(PointXYZIT::z);

  cloud.fields[3U].name = "intensity";
  cloud.fields[3U].offset = idx;
  cloud.fields[3U].datatype = sensor_msgs::msg::PointField::UINT32;
  cloud.fields[3U].count = 1U;
  idx += sizeof(PointXYZIT::intensity);

  cloud.fields[4U].name = "timestamp_sec";
  cloud.fields[4U].offset = idx;
  cloud.fields[4U].datatype = sensor_msgs::msg::PointField::FLOAT64;
  cloud.fields[4U].count = 1U;
  idx += sizeof(PointXYZIT::timestamp_sec);

  cloud.fields[5U].name = "ring_id";
  cloud.fields[5U].offset = idx;
  cloud.fields[5U].datatype = sensor_msgs::msg::PointField::UINT32;
  cloud.fields[5U].count = 1U;
  idx += sizeof(PointXYZIT::ring_id);

  cloud.fields[6U].name = "velocity";
  cloud.fields[6U].offset = idx;
  cloud.fields[6U].datatype = sensor_msgs::msg::PointField::FLOAT32;
  cloud.fields[6U].count = 1U;
  idx += sizeof(PointXYZIT::velocity);

  cloud.fields[7U].name = "float_intensity";
  cloud.fields[7U].offset = idx;
  cloud.fields[7U].datatype = sensor_msgs::msg::PointField::FLOAT32;
  cloud.fields[7U].count = 1U;
  idx += sizeof(PointXYZIT::float_intensity);

  // データ(idx)は36byteだが、PointXYZITはAlignmentされて40byte になっている
  static_assert(sizeof(PointXYZIT) == 40U, "PointXYZIT size is not 40 bytes");
  cloud.point_step = sizeof(PointXYZIT);
  cloud.data.clear();  // Clear existing data
  const auto capacity =
      safe_cast<typename decltype(cloud.data)::size_type>(cloud.point_step * size);
  if (capacity > cloud.data.capacity()) {
    throw apex::runtime_error("PointCloud2 data capacity is not enough for the given size");
  }
  // capacityはコンストラクト時に7500000（上限値）確保されているため、reserveは不要かも
  cloud.data.reserve(capacity);
  cloud.is_bigendian = false;
  cloud.is_dense = true;
  cloud.header.frame_id = frame_id;
}

template <typename PointCloud2Type>
bool AddPointTemplate(PointCloud2Type& cloud, const PointXYZIT& pt) {
  bool ret = false;
  // Add point to the point cloud if there is enough capacity
  const auto required_size =
      safe_cast<typename decltype(cloud.data)::size_type>((cloud.width + 1U) * cloud.point_step);
  if (required_size < cloud.data.capacity()) {
    const auto offset = cloud.data.size();
    cloud.data.resize(offset + cloud.point_step);
    void* const dst = &cloud.data[offset];
    const void* const src = &pt;
    (void)std::memcpy(dst, src, cloud.point_step);
    ++cloud.width;
    cloud.row_step = cloud.width * cloud.point_step;
    ret = true;
  }
  return ret;
}

template <typename PointCloud2Type>
PointXYZIT GetPointTemplate(const PointCloud2Type& cloud, uint32_t index) {
  PointXYZIT pt{};
  if (index < cloud.width) {
    const auto offset = index * cloud.point_step;
    const void* const src = &cloud.data[offset];
    (void)std::memcpy(&pt, src, cloud.point_step);
  }
  return pt;
}

}  // namespace

void init_pcl_msg(sensor_msgs::msg::PointCloud2::BorrowedType& msg,
                  const apex::string_strict128_t& frame_id, const size_t size) {
  InitPointCloudTemplate(msg, frame_id, size);
}

void init_pcl_msg(sensor_msgs::msg::PointCloud2& msg, const apex::string_strict128_t& frame_id,
                  const size_t size) {
  InitPointCloudTemplate(msg, frame_id, size);
}

bool add_point(sensor_msgs::msg::PointCloud2::BorrowedType& cloud, const PointXYZIT& pt) {
  return AddPointTemplate(cloud, pt);
}

bool add_point(sensor_msgs::msg::PointCloud2& cloud, const PointXYZIT& pt) {
  return AddPointTemplate(cloud, pt);
}

bool set_point(sensor_msgs::msg::PointCloud2::BorrowedType& cloud, uint32_t index,
               const PointXYZIT& pt) {
  bool ret = false;
  // Set point in the point cloud if index already exists
  if (index < cloud.width) {
    const auto offset = index * cloud.point_step;
    void* const dst = &cloud.data[offset];
    const void* const src = &pt;
    (void)std::memcpy(dst, src, cloud.point_step);
    ret = true;
  }
  return ret;
}

PointXYZIT get_point(const sensor_msgs::msg::PointCloud2::BorrowedType& cloud, uint32_t index) {
  return GetPointTemplate(cloud, index);
}

PointXYZIT get_point(const sensor_msgs::msg::PointCloud2& cloud, uint32_t index) {
  return GetPointTemplate(cloud, index);
}

}  // namespace t2::drivers::lidar::common
