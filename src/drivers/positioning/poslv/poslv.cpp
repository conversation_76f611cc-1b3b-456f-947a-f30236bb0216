// Copyright (c) 2024-2025 T2 Inc. All rights reserved.
/******************************************************************************
 * Copyright 2022 Preferred Networks, Inc. All Rights Reserved.
 *****************************************************************************/

#include "poslv.hpp"

#include <sstream>
#include <string>

#include "Eigen/Geometry"
#include "data_logging.hpp"
#include "msg_types/general_status_and_fdir.hpp"
#include "msg_types/gnss_status.hpp"
#include "msg_types/performance_metrics.hpp"
#include "src/common/math/math_utils.hpp"
#include "src/drivers/positioning/util/time_conversion.hpp"

namespace t2::drivers::poslv {

using t2::common::math::NormalizeAngle;

namespace {
constexpr double DEG_TO_RAD_LOCAL = M_PI / 180.0;

// Converts GPS yaw (north = 0, east = 90) to FLU yaw (east = 0, north =
// pi/2).
constexpr double gps_yaw_to_enu_yaw(double azimuth) { return (90.0 - azimuth) * DEG_TO_RAD_LOCAL; }

}  // namespace

Poslv::Poslv(const positioning::PoslvConf& conf,
             std::shared_ptr<t2::common::health::HealthReporter>& health_reporter)
    : apex::executor::apex_node_base{"poslv"},
      poslv_conf_(conf),
      health_(health_reporter),
      receiver_(conf, health_reporter) {
  T2_INFO << "poslv init, starting ...";

  if (health_) {
    health_->Report({}, {common_msgs::msg::StatusFlag::COMPONENT_WARNING});
  }

  // localization_writer_ = init_ctx.CreateWriter<apollo::localization::LocalizationEstimate>(
  //     FLAGS_localization_poslv_topic);

  // if (poslv_conf_.raw_output())
  //   raw_writer_ = init_ctx.CreateWriter<apollo::common::Dump>("/poslv/raw");

  // Set initial gps week
  gps_week_ = t2::drivers::util::unix2gps(std::time(0)) / (7 * 24 * 60 * 60);

  if (health_) {
    health_->Report({}, {common_msgs::msg::StatusFlag::COMPONENT_WARNING});
  }

  running_.store(true);
  main_loop_ = std::async(&Poslv::MainLoop, this);
  if (poslv_conf_.enable_data_logging()) {
    logging_loop_ = std::async(&Poslv::LoggingLoop, this);
  }
}

static void FillUncertainty(localization_msgs::msg::Uncertainty* uncertainty,
                            const PerformanceMetrics& pm) {
  if (!uncertainty) return;

  auto xyz = uncertainty->position_std_dev;
  xyz.x = pm.east_position_rms_error_m;
  xyz.y = pm.north_position_rms_error_m;
  xyz.z = pm.down_position_rms_error_m;

  xyz = uncertainty->roll_pitch_yaw_std_dev;
  xyz.x = pm.roll_rms_error_deg;
  xyz.y = pm.pitch_rms_error_deg;
  xyz.z = pm.heading_rms_error_deg;

  xyz = uncertainty->linear_velocity_std_dev;
  xyz.x = pm.east_velocity_rms_error_mps;
  xyz.y = pm.north_velocity_rms_error_mps;
  xyz.z = pm.down_velocity_rms_error_mps;

  xyz = uncertainty->error_ellipsoid;
  xyz.x = pm.error_ellipsoid_semi_major_m;
  xyz.y = pm.error_ellipsoid_semi_minor_m;
  xyz.z = pm.error_ellipsoid_orientation_deg;
}

static void FillGnssStatus(localization_msgs::msg::GnssStatus* status, const GNSSStatusPart1& part1,
                           const GNSSStatusPart2& part2,
                           const std::vector<ChannelStatus>& channels) {
  if (!status) return;

  status->solution_status = part1.ToGnssPositionType();
  status->satellites = part1.number_of_sv_tracked;
  status->hdop = part2.hdop;
  status->vdop = part2.vdop;
  status->receiver = part2.GetReceiverName();
  status->debug_flags = part2.gnss_status;

  status->channel_status.clear();
  for (auto& e : channels) {
    status->channel_status.push_back({});
    auto c = status->channel_status.back();
    c.prn = (e.sv_prn);
    c.type = e.ToGnssConstellationType();
    c.status = e.ToGnssChannelTrackingStatus();
    c.azimuth_deg = e.sv_azimuth_deg;
    c.elevation_deg = e.sv_elevation_deg;
    c.l1_signal_noise_ratio = e.sv_l1_signal_noise_ratio;
    c.l2_signal_noise_ratio = e.sv_l2_signal_noise_ratio;
  }
}

void Poslv::PublishLocalization(const VehicleNavigationSolution& vns) {
  localization_msgs::msg::LocalizationEstimate localization;
  auto header = localization.header;
  const rclcpp::Time current_time = rclcpp::Clock().now();
  header.creation_timestamp = current_time.nanoseconds();
  const auto gps_time_from_week = vns.timeDistance_.time1_;
  const auto gps_time = gps_time_from_week + gps_week_ * 7 * 24 * 60 * 60;
  const auto unix_time = t2::drivers::util::gps2unix(gps_time);
  const auto nano_unix_time = static_cast<uint64_t>(unix_time * 1000000000UL);
  header.measurement_timestamp = nano_unix_time;
  // For backward compatibility:
  header.lidar_timestamp = nano_unix_time;
  header.camera_timestamp = nano_unix_time;
  header.sequence_number = vns_counter++;
  localization.measurement_time = unix_time;
  header.frame_id = "novatel";

  const auto factors = projection_.ComputeFactors(vns.longitude_, vns.latitude_);

  // x: east, y: north, z: up
  auto pose = localization.pose;
  t2::localization::common::SetPosition(
      pose, projection_,
      t2::localization::common::Ecef{vns.longitude_, vns.latitude_, vns.altitude_});

  // physcal to ttm scale
  pose.scale = factors.scale_factor;

  const Eigen::Vector3f velocity_enu = Eigen::Vector3f{
      vns.eastVelocity_,
      vns.northVelocity_,
      -vns.downVelocity_,
  };
  const Eigen::Vector3f velocity_ttm =
      (Eigen::AngleAxisf(factors.grid_convergence, Eigen::Vector3f::UnitZ()) * velocity_enu) *
      factors.scale_factor;

  auto linear_velocity = pose.linear_velocity;
  linear_velocity.x = velocity_ttm.x();
  linear_velocity.y = velocity_ttm.y();
  linear_velocity.z = velocity_ttm.z();

  // acceleration: from frd to flu (enu)
  auto linear_acceleration = pose.linear_acceleration;
  linear_acceleration.x = vns.accLong_;
  linear_acceleration.y = -vns.accTrans_;
  linear_acceleration.z = -vns.accDown_;
  // angular velocity: from frd to flu (enu)
  pose.angular_velocity.x = vns.angularRateLong_ * DEG_TO_RAD_LOCAL;
  pose.angular_velocity.y = -vns.angularRateTrans_ * DEG_TO_RAD_LOCAL;
  pose.angular_velocity.z = -vns.angularRateDown_ * DEG_TO_RAD_LOCAL;

  if (poslv_conf_.orientation_type() == positioning::PoslvConf::RFU) {
    // Apollo assumption: IMU coordinate is right (roll) / forward (pitch) / up
    // (yaw) orientation: A quaternion that represents the rotation from the IMU
    // coordinate (RFU: Right/Forward/Up) to the world coordinate (TTM).
    t2::localization::common::SetRollPitchYaw(pose, vns.roll_, vns.pitch_, vns.yaw_,
                                              factors.grid_convergence);

    // vrf: only used in MSF (flu to rfu)
    auto mlav = pose.linear_acceleration_vrf;
    mlav.x = -pose.linear_acceleration.y;
    mlav.y = pose.linear_acceleration.x;
    mlav.z = pose.linear_acceleration.z;
    auto mavv = pose.angular_velocity_vrf;
    mavv.x = -pose.angular_velocity.y;
    mavv.y = pose.angular_velocity.x;
    mavv.z = pose.angular_velocity.z;
  } else {
    // ENU: This block should be used only when apollo vehicle coordinate system
    // is converted from RFU to ENU.
    const double roll = NormalizeAngle(vns.roll_ * DEG_TO_RAD_LOCAL);
    const double pitch = -NormalizeAngle(vns.pitch_ * DEG_TO_RAD_LOCAL);
    const double yaw = NormalizeAngle(gps_yaw_to_enu_yaw(vns.yaw_));
    const Eigen::Quaterniond q =
        Eigen::AngleAxisd(yaw + factors.grid_convergence, Eigen::Vector3d::UnitZ()) *
        Eigen::AngleAxisd(pitch, Eigen::Vector3d::UnitY()) *
        Eigen::AngleAxisd(roll, Eigen::Vector3d::UnitX());
    pose.orientation.x = q.x();
    pose.orientation.y = q.y();
    pose.orientation.z = q.z();
    pose.orientation.w = q.w();
    pose.heading = yaw + factors.grid_convergence;
    // vrf: only used in MSF
    pose.linear_acceleration_vrf = pose.linear_acceleration;
    pose.angular_velocity_vrf = pose.angular_velocity;
  }

  // Set raw values for debug
  pose.poslv_yaw = vns.yaw_;
  pose.poslv_wander_angle = vns.wanderAngle_;
  pose.poslv_track_angle = vns.trackAngle_;
  pose.poslv_alignment_status = static_cast<std::uint8_t>(vns.alignmentStatus_);

  T2_DEBUG << vns.ToString(vns.alignmentStatus_);

  localization.msf_status.gnsspos_position_type = last_primary_gnss_position_type_;

  if (uncertainty_.has_value()) localization.uncertainty.push_back(uncertainty_.value());

  if (gnss_status_.size() > 0) {
    auto status = localization.gnss_status;
    for (auto& e : gnss_status_) status.push_back(e);
  }

  if (extra_status_.size() > 0) {
    auto status = localization.extra_status;
    for (auto& e : extra_status_) status.push_back(e);
  }

  if (imu_pos_checksum_error_.has_value()) {
    localization.imu_pos_checksum_error = imu_pos_checksum_error_.value();
    imu_pos_checksum_error_.reset();
  }
  if (imu_status_.has_value()) {
    localization.imu_status = imu_status_.value();
    imu_status_.reset();
  }
  if (successive_imu_failures_.has_value()) {
    localization.successive_imu_failures = successive_imu_failures_.value();
    successive_imu_failures_.reset();
  }

  if (primary_ephemeris_data_gap_.has_value()) {
    localization.primary_ephemeris_data_gap = primary_ephemeris_data_gap_.value();
    primary_ephemeris_data_gap_.reset();
  }
  if (primary_gnss_missing_ephemeris_.has_value()) {
    localization.primary_gnss_missing_ephemeris = primary_gnss_missing_ephemeris_.value();
    primary_gnss_missing_ephemeris_.reset();
  }

  if (dmi_failed_or_is_offline_.has_value()) {
    localization.dmi_failed_or_is_offline = dmi_failed_or_is_offline_.value();
    dmi_failed_or_is_offline_.reset();
  }

  // localization_writer_->Write(localization);

  uncertainty_.reset();
  gnss_status_.clear();
  extra_status_.clear();
}

void Poslv::ParseGNSSStatus(const char* buffer, std::uint16_t size, bool is_primary) {
  GNSSStatusPart1 part1;
  GNSSStatusPart2 part2;
  if (size < sizeof(part1) + sizeof(part2)) {
    T2_ERROR << "Unexpected size " << size << " " << is_primary;
    return;
  }

  memcpy(&part1, buffer, sizeof(part1));
  if (size != sizeof(part1) + sizeof(part2) + sizeof(ChannelStatus) * part1.number_of_sv_tracked) {
    T2_ERROR << "Unexpected size " << size << " " << is_primary;
    return;
  }

  std::vector<ChannelStatus> channel_status;
  channel_status.resize(part1.number_of_sv_tracked);
  // [MEMO] Secondary GNSS Status has wrong channel_status_byte_count. We should
  // ignore the error for secondary GNSS status.
  if (is_primary &&
      part1.channel_status_byte_count != sizeof(ChannelStatus) * part1.number_of_sv_tracked) {
    T2_ERROR << "Channel data size mismatch. Channel status byte count: "
             << part1.channel_status_byte_count
             << ", Expected: " << sizeof(ChannelStatus) * part1.number_of_sv_tracked << " "
             << is_primary;
    return;
  }
  memcpy(channel_status.data(), &buffer[sizeof(part1)],
         sizeof(ChannelStatus) * part1.number_of_sv_tracked);

  memcpy(&part2, &buffer[size - sizeof(part2)], sizeof(part2));

  gnss_status_.push_back({});
  if (is_primary) {
    gps_week_ = part2.gps_utc_weeks;
    gnss_status_.back().id = 0;
    FillGnssStatus(&gnss_status_.back(), part1, part2, channel_status);
    last_primary_gnss_position_type_ = gnss_status_.back().solution_status;
    T2_INFO << "Navigation status: " << part1.ToString(part1.navigation_solution_status);
  } else {
    gnss_status_.back().id = 1;
    FillGnssStatus(&gnss_status_.back(), part1, part2, channel_status);
  }
}

void Poslv::MainLoop() {
  if (health_) {
    health_->Report({}, {common_msgs::msg::StatusFlag::COMPONENT_WARNING});
  }

  while (rclcpp::ok() && running_.load()) {
    auto data = receiver_.Receive();
    if (data == std::nullopt) {
      if (!rclcpp::ok() || running_.load()) break;
      std::this_thread::sleep_for(std::chrono::seconds(1));
      continue;
    }

    const auto byte_count = data->byte_count;
    // if (raw_writer_) {
    //   common::Dump dump;
    //   dump.set_type(data->start);
    //   dump.set_data(data->raw, 4 + byte_count);
    //   raw_writer_->Write(dump);
    // }

    GroupID id = data->id;
    const auto body = data->body;
    if (id == GroupID::VEHICLE_NAVIGATION_SOLUTION) {
      VehicleNavigationSolution vns = {};
      memcpy(&vns, body, sizeof(vns));
      PublishLocalization(vns);
      last_lat_lon_.emplace(std::make_pair(vns.latitude_, vns.longitude_));

      if (!rtcm_in_use_) {
        if (health_) {
          health_->Report({}, {common_msgs::msg::StatusFlag::COMPONENT_WARNING});
        }
      }
    } else if (id == GroupID::VEHICLE_NAVIGATION_PERFORMANCE_METRICS) {
      PerformanceMetrics pm = {};
      if (byte_count != sizeof(pm)) {
        T2_WARN << "Unexpected size " << byte_count << " expected: " << sizeof(pm);
      } else {
        memcpy(&pm, body, sizeof(pm));
        uncertainty_.emplace();
        FillUncertainty(&uncertainty_.value(), pm);
      }
    } else if (id == GroupID::PRIMARY_GNSS_STATUS) {
      ParseGNSSStatus(body, byte_count);
    } else if (id == GroupID::SECONDARY_GNSS_STATUS) {
      ParseGNSSStatus(body, byte_count, false);
    } else if (id == GroupID::GENERAL_STATUS_AND_FDIR) {
      GeneralStatusAndFDIR status = {};
      if (byte_count != sizeof(status)) {
        T2_WARN << "Unexpected size " << byte_count << " expected: " << sizeof(status);
      } else {
        memcpy(&status, body, sizeof(status));
        extra_status_.push_back({});
        extra_status_.back().type = "poslv_general_status_and_fdir";
        extra_status_.back().data.resize(sizeof(status));
        memcpy(&extra_status_.back().data, body, sizeof(status));
        rtcm_in_use_ = (status.general_status_c.rtcm_differential_corrections_in_use ||
                        status.general_status_c.rtcm_rtk_messages_in_use ||
                        status.general_status_c.iin_in_code_dgps_aided_mode ||
                        status.general_status_c.iin_in_rtcm_dgps_aided_mode ||
                        status.general_status_c.iin_in_float_rtk_aided_mode ||
                        status.general_status_c.iin_in_wide_lane_rtk_aided_mode ||
                        status.general_status_c.iin_in_narrow_lane_rtk_aided_mode);
        imu_pos_checksum_error_.emplace(status.fdir_level_1.imu_pos_checksum_error != 0);
        imu_status_.emplace(status.fdir_level_1.imu_status != 0);
        successive_imu_failures_.emplace(status.fdir_level_1.successive_imu_failures != 0);
        primary_ephemeris_data_gap_.emplace(status.fdir_level_1.primary_ephemeris_data_gap != 0);
        primary_gnss_missing_ephemeris_.emplace(
            status.fdir_level_1.primary_gnss_missing_ephemeris != 0);
        dmi_failed_or_is_offline_.emplace(status.fdir_level_1.dmi_failed_or_is_offline != 0);
      }
    }
  }
  receiver_.Close();
}

void Poslv::LoggingLoop() {
  DataLogging data_logging;
  while (rclcpp::ok() && running_.load()) {
    using std::operator""ms;
    data_logging.Proc(poslv_conf_);
    std::this_thread::sleep_for(5ms);
  }
}

Poslv::~Poslv() {
  receiver_.Close();
  if (running_.load()) {
    running_.store(false);
    if (main_loop_.valid()) {
      main_loop_.wait();
    }
    if (logging_loop_.valid()) {
      logging_loop_.wait();
    }
  }
}
}  // namespace t2::drivers::poslv
