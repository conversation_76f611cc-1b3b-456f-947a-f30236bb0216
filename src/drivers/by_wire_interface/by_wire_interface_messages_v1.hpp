// Copyright (c) 2024-2025 T2 Inc. All rights reserved.

#pragma once

#include <cstdint>
#include <type_traits>
#include <unordered_set>

#include "canbus_msgs/msg/chassis.hpp"
#include "common_msgs/msg/vehicle_signal.hpp"
#include "src/common/math/math_utils.hpp"

namespace t2::drivers::by_wire_interface::v1 {
enum class TurnSignal : unsigned int {
  NONE = 0,
  RIGHT = 1,
  LEFT = 2,
  HAZARD = 3,
};
enum class LEDStatus : unsigned int {
  IGN_OFF = 0,
  INITIAL_CHECK = 1,
  ERROR = 2,
  NOT_STANDBY_SYS_NG = 3,
  NOT_STANDBY_SYS_OK = 4,
  STANDBY = 5,
  AD_DRIVER_OPERATING = 6,
  AD = 7
};
enum class BuzzerStatus : unsigned int {
  OFF = 0,
  INITIAL_CHECK = 1,
  AD_CANCEL = 2,
  ERROR = 3,
  LIMITER_ON = 4,
  AD_SW_REJECT = 5,
  EPS_ERROR = 6,
  TAKEOVER_REQUEST = 7,
  OTHERS = 8
};
// note: ADSystemStatus is used as 4bit value
enum class ADSystemStatus : uint8_t { UNKNOWN = 0, OK = 1, TAKEOVER = 2 };

struct Demand1 {
  static constexpr uint32_t ID = 0x201;
  static constexpr uint16_t WATCHDOG_MASK = 0x03FF;
  uint32_t Deprecated_1 : 20;
  TurnSignal TurnSignal_Demand : 2;
  uint32_t Deprecated_2 : 18;
  uint16_t Watch_Dog : 10;
  uint16_t Reserved : 6;
  uint8_t CheckSum : 8;
  void SetTurnSignal(const common_msgs::msg::TurnSignal value) {
    TurnSignal_Demand = value == common_msgs::msg::TurnSignal::TURN_NONE    ? TurnSignal::NONE
                        : value == common_msgs::msg::TurnSignal::TURN_RIGHT ? TurnSignal::RIGHT
                        : value == common_msgs::msg::TurnSignal::TURN_LEFT  ? TurnSignal::LEFT
                        : value == common_msgs::msg::TurnSignal::HAZARD     ? TurnSignal::HAZARD
                                                                            : TurnSignal::NONE;
  }
} __attribute__((packed));
static_assert(sizeof(Demand1) == 8);
static_assert(std::is_trivially_copyable_v<Demand1>);

struct Demand2 {
  static constexpr uint32_t ID = 0x202;
  static constexpr uint16_t WATCHDOG_MASK = 0x00FF;
  float SteeringAngle_Demand;
  uint16_t Reserved;
  uint8_t Watch_Dog : 8;
  uint8_t CheckSum : 8;
} __attribute__((packed));
static_assert(sizeof(Demand2) == 8);
static_assert(std::is_trivially_copyable_v<Demand2>);

struct Demand3 {
  static constexpr uint32_t ID = 0x203;
  static constexpr uint16_t WATCHDOG_MASK = 0x00FF;
  float SteeringAngularVelocity_Demand;
  uint16_t Reserved;
  uint8_t Watch_Dog : 8;
  uint8_t CheckSum : 8;
} __attribute__((packed));
static_assert(sizeof(Demand3) == 8);
static_assert(std::is_trivially_copyable_v<Demand3>);

struct Demand4 {
  static constexpr uint32_t ID = 0x204;
  static constexpr uint16_t WATCHDOG_MASK = 0x00FF;
  float AccelBrake_Demand;  // m/s^2
  uint16_t Reserved;
  uint8_t Watch_Dog : 8;
  uint8_t CheckSum : 8;
} __attribute__((packed));
static_assert(sizeof(Demand4) == 8);
static_assert(std::is_trivially_copyable_v<Demand4>);

struct Demand5 {
  static constexpr uint32_t ID = 0x300;
  static constexpr uint16_t WATCHDOG_MASK = 0x03FF;
  bool AV_Enable_Flag : 1;
  ADSystemStatus AD_System_Status : 4;
  bool ADPC_Connection_Check_Enable : 1;
  uint16_t Watch_Dog : 10;
  uint64_t Reserved : 40;
  uint8_t CheckSum : 8;
} __attribute__((packed));
static_assert(sizeof(Demand5) == 8);
static_assert(std::is_trivially_copyable_v<Demand5>);

enum class IFStatus : unsigned int {
  IFNotReady = 0,       // I/F準備未完了
  Stopping = 1,         // 停止状態
  Ready_D = 2,          // 待機状態
  Auto_D_Starting = 3,  // Dレンジ自動運転開始状態
  Auto_D = 4,           // Dレンジ自動運転状態
  Ready_R = 5,          // Rレンジ待機状態
  Auto_R_Starting = 6,  // Rレンジ自動運転開始状態
  Auto_R = 7,           // Rレンジ自動運転状態
};

enum class SupplySource : unsigned int {
  // 鍵がOFFの位置にあったらIFの電源が入らないので未定義
  ACC = 0,  // 鍵がACCの位置にある
  ON = 1,   // 鍵がONの位置にある
};

struct Response1 {
  static constexpr uint32_t ID = 0x210;
  IFStatus IF_Status : 3;
  SupplySource SupplySource_Flag : 1;
  // Driver_Operation_Status
  bool Driver_Operation_Accel : 1;     // > 6%
  bool Driver_Operation_Brake : 1;     // > 6%
  bool Driver_Operation_Steering : 1;  // > 8Nm
  bool Driver_Operation_Shift : 1;     // N or MT
  bool Parking_Brake : 1;
  // Error_Keep_Flag indicates that a fatal error has occurred.
  // [NOTE] Cannot be moved to standby mode unless the LKA switch is pressed.
  bool Error_Keep_Flag : 1;
  // IF_Error_Status
  bool Accel_Output_Voltage_Error : 1;
  bool Brake_Output_Voltage_Error : 1;
  bool TurnSignal_Output_Error : 1;
  bool Steering_Feedback_Error : 1;
  bool Accel_Pedal_Voltage_Error : 1;
  bool Brake_Pedal_Voltage_Error : 1;
  bool TurnSignal_Input_Voltage_Error : 1;
  bool Autodrive_Switch_Error : 1;
  bool ACC_ACC_Cancel_LKA_SW_Error : 1;
  bool CAN_DAC_Control_Error : 1;
  bool ACC_And_LKA_Cancel_Error : 1;
  bool LED2_Output_Error : 1;
  bool LED3_Output_Error : 1;
  bool Buzzer_Output_Error : 1;
  bool Internal_Chip_Error : 1;
  bool Watchdog_Error : 1;
  // CAN_Error_Status
  unsigned int AD_System_Communication_Error : 1;
  unsigned int AD_System_Receive_Error : 1;
  unsigned int AD_System_Send_Error : 1;
  unsigned int Local_CAN1_Communication_Error : 1;
  unsigned int Local_CAN1_Receive_Error : 1;
  unsigned int Local_CAN1_Send_Error : 1;
  unsigned int Local_CAN2_Communication_Error : 1;
  unsigned int Local_CAN2_Receive_Error : 1;
  unsigned int Local_CAN2_Send_Error : 1;
  unsigned int OBD_CAN_Communication_Error : 1;
  unsigned int OBD_CAN_Receive_Error : 1;
  unsigned int Car_CAN_Communication_Error : 1;
  unsigned int Car_CAN_Receive_Error : 1;
  unsigned int Chassis_IMU_Error : 1;
  unsigned int Engine_CAN_Receive_Error : 1;
  unsigned int CAN_DAC_Receive_Error : 1;
  // VehicleECU_Error_Status
  unsigned int ECM_Error : 1;  // Engine Control Module
  unsigned int EBS_Error : 1;  // Electric Brake Module
  unsigned int TCM_Error : 1;  // Transmission Control Module
  unsigned int CCM_Error : 1;  // Chassis Control Module
  unsigned int BCM_Error : 1;  // Body Control Module

  bool Low_AirTank_Pres : 1;
  unsigned int WatchDogSignal_Resp : 10;
  unsigned int Firmware_Version : 6;
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
  std::unordered_set<std::string> ParseAndGetErrors(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(Response1) == 8);
static_assert(std::is_trivially_copyable_v<Response1>);

struct Response2 {
  static constexpr uint32_t ID = 0x211;
  unsigned int Accel_Actual : 12;
  unsigned int Accel_Pos_Vehicle : 8;
  unsigned int Brake_Actual : 12;
  unsigned int Brake_Pos_Vehicle : 8;
  TurnSignal TurnSignal_DriverDemand : 2;
  // Torque value set for EPS(Electronic Power Steering)
  // [NOTE] This value has no unit (probably 0.1%)
  // 0-990 (0-99.0%)
  int EPS_Torque_Demand : 16;
  unsigned int Protocol_Version : 6;

  double InterfaceBoxThrottleOutput() const { return 0.024420024 * Accel_Actual; }
  double VehicleCANThrottlePercentage() const { return 0.4 * Accel_Pos_Vehicle; }
  double InterfaceBoxBrakeOutput() const { return 0.024420024 * Brake_Actual; }
  double VehicleCANBrakePercentage() const { return 0.4 * Brake_Pos_Vehicle; }
  common_msgs::msg::TurnSignal DriverDemandTurnSignal() const {
    switch (TurnSignal_DriverDemand) {
      case TurnSignal::NONE:
        return common_msgs::msg::TurnSignal::TURN_NONE;
      case TurnSignal::RIGHT:
        return common_msgs::msg::TurnSignal::TURN_RIGHT;
      case TurnSignal::LEFT:
        return common_msgs::msg::TurnSignal::TURN_LEFT;
      case TurnSignal::HAZARD:
        return common_msgs::msg::TurnSignal::HAZARD;
    }
    return common_msgs::msg::TurnSignal::TURN_NONE;
  }
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(Response2) == 8);
static_assert(std::is_trivially_copyable_v<Response2>);

struct Response3 {
  static constexpr uint32_t ID = 0x212;
  int SteeringAngle_Actual : 16;
  int SteeringAngularVelocity_Actual : 16;
  int SteetingTorque_Actual : 8;
  unsigned int Vehicle_Weight : 8;
  unsigned int Vehicle_Speed : 12;
  unsigned int Gear_Position : 4;

  double SteeringAngleRad() const { return 0.000976563 * SteeringAngle_Actual; }
  double SteeringAngularVelocityRadPerSec() const {
    return 0.000976563 * SteeringAngularVelocity_Actual;
  }
  double SteeringTorqueNm() const { return 0.1 * SteetingTorque_Actual; }
  double VehicleWeightKg() const { return 100 * Vehicle_Weight + 10000; }
  double VehicleSpeedMps() const { return 0.05 * Vehicle_Speed * 1000 / 3600; }
  canbus_msgs::msg::GearPosition GearPosition() const {
    auto raw = RawGearPosition();
    if (raw < 0) return canbus_msgs::msg::GearPosition::GEAR_REVERSE;
    if (raw == 0) return canbus_msgs::msg::GearPosition::GEAR_NEUTRAL;
    if (1 <= raw || raw <= 12) return canbus_msgs::msg::GearPosition::GEAR_DRIVE;
    return canbus_msgs::msg::GearPosition::GEAR_INVALID;
  }
  // -2:R(reverse), -1:R(reverse), 0:N(neutral), 1-12:1st-12th gear
  int RawGearPosition() const { return static_cast<int>(Gear_Position) - 2; }
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(Response3) == 8);
static_assert(std::is_trivially_copyable_v<Response3>);

struct Response4 {
  static constexpr uint32_t ID = 0x213;
  unsigned int Vehicle_Accelaration : 8;
  unsigned int AirTank_Pressure : 8;
  unsigned int Vehicle_Distance : 24;
  unsigned int Engine_Speed : 16;
  unsigned int Coolant_Temperature : 8;

  double VehicleAcceleration() const { return 0.1 * Vehicle_Accelaration - 12.5; }
  double AirtankPressure() const { return 8 * AirTank_Pressure * 1000; }
  double VehicleDistance() const { return Vehicle_Distance * 1000.; }
  double EngineSpeed() const { return 0.125 * Engine_Speed; }
  double CoolantTemperature() const { return Coolant_Temperature - 40; }
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(Response4) == 8);
static_assert(std::is_trivially_copyable_v<Response4>);

struct Response8 {
  static constexpr uint32_t ID = 0x700;
  float Inertial_Moment;
  float Ft_Cornering_Power;
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(Response8) == 8);
static_assert(std::is_trivially_copyable_v<Response8>);

struct Response9 {
  static constexpr uint32_t ID = 0x701;
  float Rr_Cornering_Power;
  uint32_t Unknown;
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(Response9) == 8);
static_assert(std::is_trivially_copyable_v<Response9>);

struct Response10 {
  static constexpr uint32_t ID = 0x702;
  uint16_t Front_Axle_Cog_Distance;
  uint16_t Rear_Axle_Cog_Distance;
  float FrontAxleCogDistance() const { return static_cast<float>(Front_Axle_Cog_Distance) * 0.1f; }
  float RearAxleCogDistance() const { return static_cast<float>(Rear_Axle_Cog_Distance) * 0.1f; }
  uint32_t Unknown;
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(Response10) == 8);
static_assert(std::is_trivially_copyable_v<Response10>);

struct Response11 {
  static constexpr uint32_t ID = 0x217;
  TurnSignal TurnSignal_Actual : 2;
  unsigned int Reserve1 : 2;
  bool Accel_Limiter : 1;
  bool Brake_Limiter : 1;
  bool Handle_Limiter : 1;
  bool Handle_Speed_Limiter : 1;
  unsigned int Buzzer_Mode : 8;
  unsigned int Led_Mode : 8;
  unsigned int Estimated_Load : 8;
  unsigned int Reserve3 : 8;
  unsigned int Reserve4 : 8;
  unsigned int Auxiliary_Brake_Status : 4;
  unsigned int Auxiliary_Brake_Actual_Status : 4;
  unsigned int Vehicle_ID : 8;
  common_msgs::msg::TurnSignal ActualTurnSignal() const {
    switch (TurnSignal_Actual) {
      case TurnSignal::NONE:
        return common_msgs::msg::TurnSignal::TURN_NONE;
      case TurnSignal::RIGHT:
        return common_msgs::msg::TurnSignal::TURN_RIGHT;
      case TurnSignal::LEFT:
        return common_msgs::msg::TurnSignal::TURN_LEFT;
      case TurnSignal::HAZARD:
        return common_msgs::msg::TurnSignal::HAZARD;
    }
    return common_msgs::msg::TurnSignal::TURN_NONE;
  }
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(Response11) == 8);
static_assert(std::is_trivially_copyable_v<Response11>);

struct Response12 {
  static constexpr uint32_t ID = 0x218;
  int16_t Throttle_Opening : 16;
  int16_t Target_Steering_Angle : 16;
  unsigned int Target_Brake_Opening : 8;
  unsigned int Lateral_Acceleration : 8;
  unsigned int Reserve3 : 8;
  unsigned int Reserve4 : 8;
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(Response12) == 8);
static_assert(std::is_trivially_copyable_v<Response12>);
struct MPCTuning1 {
  static constexpr uint32_t ID = 0x7E0;
  float r1;   // r_angular_velocity
  float rd1;  // r_d_angular_velocity
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(MPCTuning1) == 8);
static_assert(std::is_trivially_copyable_v<MPCTuning1>);

struct MPCTuning2 {
  static constexpr uint32_t ID = 0x7E1;
  float q1;  // q_lateral_error
  float q2;  // q_heading_error
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(MPCTuning2) == 8);
static_assert(std::is_trivially_copyable_v<MPCTuning2>);

struct MPCTuning3 {
  static constexpr uint32_t ID = 0x7E2;
  float q3;  // q_lateral_errorsum
  float q4;  // q_beta
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(MPCTuning3) == 8);
static_assert(std::is_trivially_copyable_v<MPCTuning3>);

struct MPCTuning4 {
  static constexpr uint32_t ID = 0x7E3;
  float q5;  // q_last_lateral_error
  float q6;  // q_last_heading_error
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(MPCTuning4) == 8);
static_assert(std::is_trivially_copyable_v<MPCTuning4>);

struct MPCTuning5 {
  static constexpr uint32_t ID = 0x7E4;
  float q7;  // q_last_lateral_errorsum
  float q8;  // q_last_beta
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(MPCTuning5) == 8);
static_assert(std::is_trivially_copyable_v<MPCTuning5>);

struct Time {
  static constexpr uint32_t ID = 0x70F;
  std::uint8_t Year;
  std::uint8_t Month;
  std::uint8_t Day;
  std::uint8_t Hour;
  std::uint8_t Min;
  std::uint32_t Unknown : 24;
} __attribute__((packed));
static_assert(sizeof(Time) == 8);
static_assert(std::is_trivially_copyable_v<Time>);

struct EPSCommand1 {
  static constexpr uint32_t ID = 0x414;
  std::uint64_t Unknown;
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(EPSCommand1) == 8);
static_assert(std::is_trivially_copyable_v<EPSCommand1>);

enum class EPSMode : std::uint8_t {
  LowSpeedAssistAndNoLKA = 0,
  LowSpeedAssist = 2,
  LKA = 4,
};

struct EPSCommand2 {
  static constexpr uint32_t ID = 0x415;
  std::int32_t Torque_Demand : 16;
  std::uint32_t Unknown : 24;
  EPSMode EpsMode : 8;
  std::uint8_t Counter;
  std::uint8_t Checksum;
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(EPSCommand2) == 8);
static_assert(std::is_trivially_copyable_v<EPSCommand2>);

struct EPSStatus1 {
  static constexpr uint32_t ID = 0x425;
  std::int16_t Angle;
  std::int16_t Angular_Velocity;
  std::int16_t Steering_Torque;
  std::uint8_t Counter;
  std::uint8_t Checksum;

  double AngleRad() const { return t2::common::math::Deg2Rad(Angle * 0.1); }
  double AngularVelocityRadPerSec() const {
    return t2::common::math::Deg2Rad(Angular_Velocity * 0.1);
  }
  double SteeringTorqueNm() const { return Steering_Torque * 0.1; }
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(EPSStatus1) == 8);
static_assert(std::is_trivially_copyable_v<EPSStatus1>);

struct EPSStatus2 {
  static constexpr uint32_t ID = 0x426;
  std::uint8_t System_Mode;
  std::uint8_t Electric_Current;
  std::int16_t Nominal_Friction_Torque;
  std::int16_t All_Torque;
  std::uint8_t Counter;
  std::uint8_t Checksum;

  double AllTorqueNm() const { return All_Torque * 0.01; }
  double NominalFrictionTorqueNm() const { return Nominal_Friction_Torque * 0.01; }
  double ElectricCurrentAmpare() const { return Electric_Current * 0.2; }
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(EPSStatus2) == 8);
static_assert(std::is_trivially_copyable_v<EPSStatus2>);

struct EPSStatus3 {
  static constexpr uint32_t ID = 0x427;
  std::uint64_t Unknown : 48;
  std::uint8_t Counter;
  std::uint8_t Checksum;
  void Parse(canbus_msgs::msg::Chassis& chassis) const;
} __attribute__((packed));
static_assert(sizeof(EPSStatus3) == 8);
static_assert(std::is_trivially_copyable_v<EPSStatus3>);

}  // namespace t2::drivers::by_wire_interface::v1
