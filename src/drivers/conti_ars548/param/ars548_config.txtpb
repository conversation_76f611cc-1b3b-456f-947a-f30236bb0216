udp_receiver_config {
  node_name: "udp_receiver"
  topic_name: "/apollo/sensor/radar/incoming_packet"

  port_to_bind: 42102
  address_to_bind: "*************"
  multicast_group_address: "*********"
}

udp_splitter_config {
  node_name: "udp_splitter"
  input_topic_name: "/apollo/sensor/radar/incoming_packet"

  output_configs: [{
    ipv4_address: "*************"
    output_topic_name: "/apollo/sensor/radar/front_center_incoming_packet"
  }, {
    ipv4_address: "*************"
    output_topic_name: "/apollo/sensor/radar/back_center_incoming_packet"
  }]
}

udp_sender_config {
  node_name: "udp_sender"
  topic_name: "/apollo/sensor/radar/outgoing_packet"

  address_to_bind: "*************"
  port_to_bind: 42402
}

ars548_configs [{
  node_name: "ars548_front_center"
  sensor_frame_id: "radar_front_center"

  packet_reception_topic: "/apollo/sensor/radar/front_center_incoming_packet"
  packet_transmission_topic: "/apollo/sensor/radar/outgoing_packet"
  detection_data_topic: "/apollo/sensor/radar/front_center_detection_data"
  object_data_topic: "/apollo/sensor/radar/front_center_object_data"

  sensor_config: {
    # The units of position are defined in meters.
    # The units of angle are defined in degrees.
    sensor_pose{
        orientation_of_plug_is_right : true
    }

    # define vehicle configuration at meter unit
    vehicle{
        height : 3.790
        width : 2.495
        length : 11.985
        wheel_base : 7.335
    }

    # define at meter unit (set from 93m to 1514m)
    maximum_detection_distance : 500

    center_frequency : kFreq76G48Hz

    # define at ms unit (set from 50ms to 100ms)
    cycle_time : 50

    # define at ms unit (set from 10ms to 90ms)
    cycle_offset : 10

    send_ip_address : "*************"
    japan_country_mode : true
    power_saving_in_standstill : false
  }
}, {
  node_name: "ars548_back_center"
  sensor_frame_id: "radar_back_center"

  packet_reception_topic: "/apollo/sensor/radar/back_center_incoming_packet"
  packet_transmission_topic: "/apollo/sensor/radar/outgoing_packet"
  detection_data_topic: "/apollo/sensor/radar/back_center_detection_data"
  object_data_topic: "/apollo/sensor/radar/back_center_object_data"

  sensor_config: {
    # The units of position are defined in meters.
    # The units of angle are defined in degrees.
    sensor_pose{
        orientation_of_plug_is_right : true
    }

    # define vehicle configuration at meter unit
    vehicle{
        height : 3.790
        width : 2.495
        length : 11.985
        wheel_base : 7.335
    }

    # define at meter unit (set from 93m to 1514m)
    maximum_detection_distance : 500

    center_frequency : kFreq76G48Hz

    # define at ms unit (set from 50ms to 100ms)
    cycle_time : 50

    # define at ms unit (set from 10ms to 90ms)
    cycle_offset : 10

    send_ip_address : "*************"
    japan_country_mode : true
    power_saving_in_standstill : false
  }
}]
