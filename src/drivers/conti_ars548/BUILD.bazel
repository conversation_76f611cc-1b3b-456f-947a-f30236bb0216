# Copyright (c) 2025 T2 Inc. All rights reserved.
load("@apex//common/bazel/rules_pkg_extra:defs.bzl", "exe_archive")
load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")
load("@apex//grace/monitoring/event_registry:bazel/defs.bzl", "event_headers")
load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_library", "cc_proto_library", "cc_test")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "conti_ars548_proto",
    srcs = [
        "proto/conti_ars548_config.proto",
        "proto/process_config.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//src/drivers/network:udp_config_proto",
    ],
)

cc_proto_library(
    name = "conti_ars548_cc_proto",
    visibility = ["//visibility:public"],
    deps = [":conti_ars548_proto"],
)

event_headers(
    name = "event_headers_gen",
    srcs = [
        "event/conti_ars548.events.yaml",
    ],
    outs = [
        "events/conti_ars548_events.hpp",
    ],
)

cc_library(
    name = "event_headers",
    hdrs = [":event_headers_gen"],
    visibility = ["//visibility:public"],
)

cc_library(
    name = "conti_ars548_lib",
    srcs = [
        "src/deserializer.cpp",
        "src/driver_node.cpp",
        "src/message_converter.cpp",
    ],
    hdrs = [
        "src/deserializer.hpp",
        "src/driver_node.hpp",
        "src/message_converter.hpp",
        "src/raw_packet.hpp",
        "src/state.hpp",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":conti_ars548_cc_proto",
        ":event_headers",
        "//src/common/endian",
        "//src/interfaces/network_msgs",
        "//src/interfaces/radar_msgs",
        "@apex//common/cpputils",
        "@apex//grace/execution/executor2",
        "@apex//grace/monitoring/logging",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@apex//grace/utils/apexcpp",
    ],
)

cc_binary(
    name = "conti_ars548",
    srcs = [
        "src/main.cpp",
    ],
    data = [
        "param/ars548_config.txtpb",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":conti_ars548_lib",
        "//src/drivers/network:network_lib",
        "@apex//grace/execution/apex_init",
    ],
)

process_manager(
    name = "pm",
    data = [
        ":conti_ars548",
        ":param/ars548_config.txtpb",
    ],
    launch_file = "launch/conti_ars548.launch.yaml",
    visibility = ["//visibility:public"],
)

exe_archive(
    name = "pm_archive",
    out = "conti_ars548_pm.tar.gz",
    executable = ":pm",
)

cc_test(
    name = "test_conti_ars548_lib",
    srcs = [
        "test/test_deserializer.cpp",
        "test/test_message_converter.cpp",
        "test/test_state.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        ":conti_ars548_lib",
        "@apex//grace/tools/apex_integration_test_node",
        "@googletest//:gtest_main",
    ],
)
