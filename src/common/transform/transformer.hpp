// Copyright (c) 2024-2025 T2 Inc. All rights reserved.

#pragma once

#include <cpputils/outcome.hpp>
#include <rclcpp/rclcpp.hpp>
#include <rclcpp/subscription.hpp>
#include <tf2_msgs/msg/tf_message.hpp>

#include "src/common/transform/transform.hpp"
#include "t2/tf2/buffer_core.h"

namespace t2::common::transform {
class Transformer {
 public:
  explicit Transformer(rclcpp::Node& node);
  ~Transformer() = default;
  Transformer(const Transformer&) = delete;
  Transformer& operator=(const Transformer&) = delete;
  Transformer(Transformer&&) = delete;
  Transformer& operator=(Transformer&&) = delete;

  using QueryResult = apex::outcome<t2::common::transform::TransformStamped>;

  /**
   * @brief Get the transform between two frames by frame ID.
   * @param target_frame The frame to which data should be transformed
   * @param source_frame The frame where the data originated
   * @param time The time at which the value of the transform is desired. (0 will get the latest)
   * @return The transform between the frames
   */
  QueryResult Query(const std::string& target_frame, const std::string& source_frame,
                    const rclcpp::Time& time);

  /**
   * @brief Get the transform between two frames by frame ID assuming fixed frame.
   * @param target_frame The frame to which data should be transformed
   * @param target_time The time to which the data should be transformed. (0 will get the latest)
   * @param source_frame The frame where the data originated
   * @param source_time The time at which the source_frame should be evaluated. (0 will get the
   * latest)
   * @param fixed_frame The frame in which to assume the transform is constant in time.
   * @return The transform between the frames
   */
  QueryResult Query(const std::string& target_frame, const rclcpp::Time& target_time,
                    const std::string& source_frame, const rclcpp::Time& source_time,
                    const std::string& fixed_frame);

  /**
   * @brief Get the TF subscription for dynamic transforms.
   *
   * @return The TF subscription for dynamic transforms.
   */
  rclcpp::PollingSubscriptionBase::SharedPtr GetTfSubscription() const { return tf_sub_; }

  /**
   * @brief Get the Tf Static Subscription object
   *
   * @return rclcpp::PollingSubscriptionBase::SharedPtr
   */
  rclcpp::PollingSubscriptionBase::SharedPtr GetTfStaticSubscription() const {
    return tf_static_sub_;
  }

 private:
  bool UpdateTransform();

  t2::tf2::BufferCore tf_buffer_;
  rclcpp::PollingSubscription<::tf2_msgs::msg::TFMessage>::SharedPtr tf_static_sub_;
  rclcpp::PollingSubscription<::tf2_msgs::msg::TFMessage>::SharedPtr tf_sub_;
};
}  // namespace t2::common::transform
