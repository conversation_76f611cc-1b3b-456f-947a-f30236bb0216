// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <rclcpp/rclcpp.hpp>

#include "src/common/transform/transform.hpp"
#include "tf2_msgs/msg/tf_message.hpp"

namespace t2::common::transform {

constexpr const char* kTfChannelName = "/tf";

class TransformBroadcaster {
 public:
  explicit TransformBroadcaster(rclcpp::Node& node) {
    publisher_ = node.create_publisher<::tf2_msgs::msg::TFMessage>(
        kTfChannelName, rclcpp::DefaultQoS().keep_last(1));
  }

  void SendTransform(const t2::common::transform::TransformStamped& transform);

  void SendTransform(const std::vector<t2::common::transform::TransformStamped>& transforms);

  /**
   * @brief Get the Publisher object of the TransformBroadcaster.
   *
   * @return rclcpp::PublisherBase::SharedPtr
   */
  rclcpp::PublisherBase::SharedPtr GetPublisher() const { return publisher_; }

 private:
  rclcpp::Publisher<::tf2_msgs::msg::TFMessage>::SharedPtr publisher_;
};

}  // namespace t2::common::transform
