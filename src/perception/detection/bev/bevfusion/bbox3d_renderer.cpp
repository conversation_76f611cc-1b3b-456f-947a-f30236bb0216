// Copyright (c) 2024-2025 T2 Inc. All rights reserved.

#include "src/perception/detection/bev/bevfusion/bbox3d_renderer.hpp"

#include <Eigen/Core>

#include <executor2/executable_item.hpp>
#include <opencv2/opencv.hpp>

#include "src/common/contract_assertions/contract_assertions.hpp"
#include "src/common/core/chrono_consts.hpp"
#include "src/common/cuda/cuda_utils.hpp"
#include "src/perception/detection/types/camera.hpp"

namespace t2::perception::detection::bevfusion {

class Bbox3dRendererImpl final : public Bbox3dRenderer {
  using PerceptionObstacle = ::perception_msgs::msg::PerceptionObstacle;

 public:
  explicit Bbox3dRendererImpl(rclcpp::Node& node, bev::Config config)
      : node_(node), config_(std::move(config)) {
    for (const auto& overlay_channel : config_.camera_overlay_channel()) {
      const auto overlay_cam_index =
          std::find_if(config_.camera_reader_channel().begin(),
                       config_.camera_reader_channel().end(),
                       [&overlay_channel](const std::string& reader_channel) {
                         return reader_channel == overlay_channel;
                       }) -
          config_.camera_reader_channel().begin();

      expect_enforce(overlay_cam_index < config_.camera_reader_channel().size());

      overlay_camera_indices_.push_back(static_cast<int>(overlay_cam_index));
      const auto cam_name = overlay_channel.substr(overlay_channel.find_last_of("/") + 1);
      const auto overlay_target_channel = config_.camera_overlay_channel_prefix() + cam_name;
      overlay_writers_.push_back(
          node_.create_publisher<Image>(overlay_target_channel, rclcpp::DefaultQoS()));
    }

    const auto num_rendered_cameras = overlay_writers_.size();
    dst_images_.resize(num_rendered_cameras);
  }

  ~Bbox3dRendererImpl() override = default;
  Bbox3dRendererImpl(const Bbox3dRendererImpl&) = delete;
  Bbox3dRendererImpl& operator=(const Bbox3dRendererImpl&) = delete;
  Bbox3dRendererImpl(Bbox3dRendererImpl&&) = delete;
  Bbox3dRendererImpl& operator=(Bbox3dRendererImpl&&) = delete;

  void Render(const BEVFusionInput& input, const BevData& bev_data,
              const PerceptionObstacles& obstacles) override {
    const auto start_time = node_.now();

    const auto& src_images = input.images;

    for (size_t i = 0; i < overlay_camera_indices_.size(); ++i) {
      const auto cam_index = overlay_camera_indices_[i];
      const auto& writer = overlay_writers_[i];

      const auto& src_image = src_images[cam_index];
      if (src_image == nullptr) {
        T2_INFO << "Input image: " << cam_index << " is null. Skip making overlay.";
        continue;
      }

      const auto& src_dev_undistorted_image = bev_data.imgs[cam_index];
      const auto& cam_params = bev_data.camera_parameters[cam_index];
      auto& dst_image = dst_images_[i];

      CopyImageToOverlayBuffer(dst_image, src_dev_undistorted_image);
      SetupOverlayInfo(dst_image, src_image);
      RenderBbox3d(dst_image, cam_params, obstacles);
      PublishOverlay(writer, dst_image, src_image);
    }

    const auto end_time = node_.now();
    const auto elapsed_msec =
        (end_time - start_time).seconds() * t2::common::chrono_consts::kMillisecsPerSec<double>;
    T2_INFO << "Bbox3dRendererImpl::Render(): " << elapsed_msec << " ms";
  }

  std::vector<rclcpp::Publisher<Image>::SharedPtr> GetPublishers() const override {
    return overlay_writers_;
  }

 private:
  void SetupOverlayInfo(Image& dst_image,
                        const std::shared_ptr<const Image::BorrowedType>& src_image) {
    const auto& src_image_info = src_image->info;
    auto& dst_image_info = dst_image.info;

    dst_image.measurement_time = src_image->measurement_time;
    dst_image_info.frame_id = src_image_info.frame_id;
    dst_image_info.encoding = src_image_info.encoding;

    dst_image_info.intrinsics = src_image_info.intrinsics;
    dst_image_info.intrinsics.fx =
        static_cast<float>(dst_image_info.intrinsics.fx * overlay_scale_x_);
    dst_image_info.intrinsics.cx =
        static_cast<float>(dst_image_info.intrinsics.cx * overlay_scale_x_);
    dst_image_info.intrinsics.fy =
        static_cast<float>(dst_image_info.intrinsics.fy * overlay_scale_y_);
    dst_image_info.intrinsics.cy =
        static_cast<float>(dst_image_info.intrinsics.cy * overlay_scale_y_);
  }

  void CopyImageToOverlayBuffer(Image& dst_image,
                                const std::shared_ptr<::t2::perception::detection::CameraGpuImage>&
                                    src_dev_undistorted_image) {
    auto& dst_image_info = dst_image.info;

    const auto overlay_width = config_.bevfusion().overlay_width();
    const auto overlay_height = config_.bevfusion().overlay_height();
    const auto overlay_size = overlay_width * overlay_height * kNumCameraImageChannels;

    // First-time creation of the input image buffer. Also it is recreated when
    // the input image size changes.
    const auto src_image_width = static_cast<int32_t>(src_dev_undistorted_image->width);
    const auto src_image_height = static_cast<int32_t>(src_dev_undistorted_image->height);
    const auto src_image_size = src_image_width * src_image_height * kNumCameraImageChannels;

    if (src_image_width != src_host_undistorted_mat_.cols ||
        src_image_height != src_host_undistorted_mat_.rows) {
      src_host_undistorted_mat_.create(src_image_height, src_image_width, CV_8UC3);
      overlay_scale_x_ = static_cast<double>(overlay_width) / src_image_width;
      overlay_scale_y_ = static_cast<double>(overlay_height) / src_image_height;
    }

    // First-time creation of the output image buffer.
    if (overlay_width != static_cast<std::int32_t>(dst_image_info.width) ||
        overlay_height != static_cast<std::int32_t>(dst_image_info.height)) {
      dst_image.data.resize(overlay_size);
      dst_image_info.width = overlay_width;
      dst_image_info.step = overlay_width * kNumCameraImageChannels;
      dst_image_info.height = overlay_height;
    }

    // Copy the undistorted image from GPU to CPU, then resize to the overlay
    // size.
    CUDA_CHECK_ERROR(cudaMemcpy(src_host_undistorted_mat_.data, src_dev_undistorted_image->ptr_gpu,
                                src_image_size, cudaMemcpyDeviceToHost));

    cv::Mat dst_image_mat(overlay_height, overlay_width, CV_8UC3, dst_image.data.data());
    cv::resize(src_host_undistorted_mat_, dst_image_mat, cv::Size(overlay_width, overlay_height));
  }

  void RenderBbox3d(Image& dst_image, const CameraParameters& cam_params,
                    const PerceptionObstacles& obstacles) {
    auto is_in_image = [&dst_image](const Eigen::Vector2d& point) -> bool {
      return point.x() >= 0 && point.x() < dst_image.info.width && point.y() >= 0 &&
             point.y() < dst_image.info.height;
    };

    const std::vector<std::pair<int, int>> line_indices = {
        {0, 1}, {1, 2}, {2, 3}, {3, 0},  // Near face
        {4, 5}, {5, 6}, {6, 7}, {7, 4},  // Far face
        {0, 4}, {1, 5}, {2, 6}, {3, 7}   // Connections between near and far faces
    };

    for (const auto& obstacle : obstacles.perception_obstacle) {
      // Render the 3D bounding box of the obstacle.
      const auto corners = ComputeBbox3dCorners(obstacle);

      std::vector<std::pair<Eigen::Vector2d, Eigen::Vector2d>> line_segments;
      for (const auto& line : line_indices) {
        const Eigen::Vector3d p1 = corners.row(line.first);
        const Eigen::Vector3d p2 = corners.row(line.second);
        const auto line_segment = Project3dLineToImage(p1, p2, cam_params);

        if (line_segment.has_value()) {
          line_segments.push_back(line_segment.value());
        }
      }

      for (const auto& line_segment : line_segments) {
        const Eigen::Vector2d point1 = line_segment.first;
        const Eigen::Vector2d point2 = line_segment.second;
        RenderLineSegment(dst_image, obstacle, point1, point2);
      }

      std::vector<Eigen::Vector2d> image_points;
      for (const auto& line_segment : line_segments) {
        if (is_in_image(line_segment.first)) {
          image_points.push_back(line_segment.first);
        }

        if (is_in_image(line_segment.second)) {
          image_points.push_back(line_segment.second);
        }
      }

      // Draw box annotation if any line segment is visible. Adjust the position
      // of the annotation so that it is not clipped by the image border and
      // also does not overlap with the box.
      if (!image_points.empty()) {
        const auto left_most_point =
            std::min_element(image_points.begin(), image_points.end(),
                             [](const auto& p1, const auto& p2) { return p1.x() < p2.x(); });
        const auto top_most_point =
            std::min_element(image_points.begin(), image_points.end(),
                             [](const auto& p1, const auto& p2) { return p1.y() < p2.y(); });
        Eigen::Vector2d anno_pos;
        anno_pos << std::max(left_most_point->x(), 0.0), std::max(top_most_point->y(), 0.0);
        RenderAnnotation(dst_image, obstacle, anno_pos);
      }
    }
  }

  Eigen::Matrix<double, Eigen::Dynamic, 3> ComputeBbox3dCorners(
      const PerceptionObstacle& obstacle) {
    const auto px = obstacle.local_position.x;
    const auto py = obstacle.local_position.y;
    const auto pz = obstacle.local_position.z;
    const auto hl = obstacle.length / 2.0;
    const auto hw = obstacle.width / 2.0;
    const auto hh = obstacle.height / 2.0;
    const auto theta = obstacle.local_theta;
    constexpr int kNumBbox3dCorners{8};

    Eigen::Matrix<double, Eigen::Dynamic, 4> corners(kNumBbox3dCorners, 4);
    corners << -hl, -hw, -hh, 1.0,  // Near-right-bottom
        -hl, hw, -hh, 1.0,          // Near-left-bottom
        -hl, hw, hh, 1.0,           // Near-left-top
        -hl, -hw, hh, 1.0,          // Near-right-top
        hl, -hw, -hh, 1.0,          // Far-right-bottom
        hl, hw, -hh, 1.0,           // Far-left-bottom
        hl, hw, hh, 1.0,            // Far-left-top
        hl, -hw, hh, 1.0;           // Far-right-top

    // Rotate the corners around the z-axis by theta and translate them to the
    // object's center.
    const auto cos_t = std::cos(theta);
    const auto sin_t = std::sin(theta);
    const auto trans_mat = (Eigen::Matrix4d() << cos_t, -sin_t, 0.0, px,  // Row 1
                            sin_t, cos_t, 0.0, py,                        // Row 2
                            0.0, 0.0, 1.0, pz,                            // Row 3
                            0.0, 0.0, 0.0, 1.0                            // Row 4
                            )
                               .finished();
    corners = corners * trans_mat.transpose();
    return corners.block(0, 0, kNumBbox3dCorners,
                         3);  // Remove the homogeneous coordinate.
  }

  std::optional<std::pair<Eigen::Vector2d, Eigen::Vector2d>> Project3dLineToImage(
      const Eigen::Vector3d& p1, const Eigen::Vector3d& p2, const CameraParameters& cam_params) {
    using Matrix4fRowMajor = Eigen::Matrix<float, 4, 4, Eigen::RowMajor>;

    auto is_behind_camera = [](const Eigen::Vector3d& point) -> bool { return point.z() <= 0.0; };

    auto find_intersection_with_z_plane = [](const Eigen::Vector3d& p,
                                             const Eigen::Vector3d& other_p) -> Eigen::Vector3d {
      const double z_plane_eps = 1e-4;
      const auto t = (z_plane_eps - p.z()) / (other_p.z() - p.z());
      return p + t * (other_p - p);
    };

    auto project_line_segment_point =
        [this, &cam_params, &is_behind_camera, &find_intersection_with_z_plane](
            const Eigen::Vector3d& p, const Eigen::Vector3d& other_p) -> Eigen::Vector2d {
      // Clip the line segment to the z = 0 plane.
      const auto p_front = (is_behind_camera(p) ? find_intersection_with_z_plane(p, other_p) : p);

      // Scale row 0 of intrinsics by overlay_scale_x_ and row 1 by
      // overlay_scale_y_.
      Matrix4fRowMajor intrinsics =
          Eigen::Map<const Matrix4fRowMajor>(cam_params.intrinsics.data());
      intrinsics.row(0) *= static_cast<float>(overlay_scale_x_);
      intrinsics.row(1) *= static_cast<float>(overlay_scale_y_);

      const auto p_image = intrinsics.cast<double>() * p_front.homogeneous();

      const Eigen::Vector2d image_p = p_image.head<2>() / p_image.z();
      return image_p;
    };

    auto world_to_camera = [&cam_params](const Eigen::Vector3d& p) -> Eigen::Vector3d {
      Eigen::Map<const Matrix4fRowMajor> extrinsics(cam_params.extrinsics.data());
      return (extrinsics.cast<double>() * p.homogeneous()).head<3>();
    };

    const Eigen::Vector3d p1_cam = world_to_camera(p1);
    const Eigen::Vector3d p2_cam = world_to_camera(p2);

    // The line is completely invisible since both points are behind the camera.
    if (is_behind_camera(p1_cam) && is_behind_camera(p2_cam)) {
      return std::nullopt;
    }

    const Eigen::Vector2d image_p1 = project_line_segment_point(p1_cam, p2_cam);
    const Eigen::Vector2d image_p2 = project_line_segment_point(p2_cam, p1_cam);

    return std::make_pair(image_p1, image_p2);
  }

  void RenderLineSegment(Image& dst_image, const PerceptionObstacle& obstacle,
                         const Eigen::Vector2d& p1, const Eigen::Vector2d& p2) {
    const cv::Point cv_p1 = VectorToCvPoint(p1);
    const cv::Point cv_p2 = VectorToCvPoint(p2);

    const auto color = GetRenderColor(obstacle);
    cv::Mat dst_image_mat(static_cast<int>(dst_image.info.height),
                          static_cast<int>(dst_image.info.width), CV_8UC3, dst_image.data.data());
    cv::line(dst_image_mat, cv_p1, cv_p2, color, 1);
  }

  void RenderAnnotation(Image& dst_image, const PerceptionObstacle& obstacle,
                        const Eigen::Vector2d& pos) {
    auto put_text_with_background = [](cv::Mat& image_mat, const std::string& text,
                                       const cv::Point& cv_pos, const cv::Scalar& color) {
      const auto text_size = cv::getTextSize(text, cv::FONT_HERSHEY_DUPLEX, 0.5, 1, nullptr);
      const auto bg_tl = cv::Point(cv_pos.x, cv_pos.y - text_size.height);
      const auto bg_br = cv::Point(cv_pos.x + text_size.width, cv_pos.y);
      const auto text_color = cv::Scalar(255, 255, 255);  // White #FFFFFF
      cv::rectangle(image_mat, bg_tl, bg_br, color, cv::FILLED);

      constexpr double kFontScale{0.5};
      constexpr int kFontThickness{1};
      cv::putText(image_mat, text, cv_pos, cv::FONT_HERSHEY_DUPLEX, kFontScale, text_color,
                  kFontThickness);
    };

    const cv::Point cv_pos = VectorToCvPoint(pos);

    std::stringstream ss;
    const auto logitudinal_distance = obstacle.local_position.x;
    const auto confidence = obstacle.confidence;
    ss << std::fixed << std::setprecision(1) << logitudinal_distance << "m(" << std::setprecision(2)
       << confidence << ")";
    const std::string anno = ss.str();

    const auto color = GetRenderColor(obstacle);
    cv::Mat dst_image_mat(static_cast<int>(dst_image.info.height),
                          static_cast<int>(dst_image.info.width), CV_8UC3, dst_image.data.data());
    put_text_with_background(dst_image_mat, anno, cv_pos, color);
  }

  cv::Point VectorToCvPoint(const Eigen::Vector2d& p) {
    const Eigen::Vector2i p_rounded = p.cast<int>();
    return cv::Point(p_rounded.x(), p_rounded.y());
  }

  cv::Scalar GetRenderColor(const PerceptionObstacle& obstacle) {
    using SubType = perception_msgs::msg::SubType;
    switch (obstacle.sub_type) {
      case SubType::ST_UNKNOWN: {
        constexpr std::array<int, 3> kMagenta{255, 0, 255};  // R, G, B
        return cv::Scalar(kMagenta[2], kMagenta[1],
                          kMagenta[0]);  // Magenta #FF00FF
      }
      case SubType::ST_CAR: {
        constexpr std::array<int, 3> kRed{0, 0, 255};  // R, G, B
        return cv::Scalar(kRed[2], kRed[1], kRed[0]);  // Red #FF0000
      }
      case SubType::ST_TRUCK: {
        constexpr std::array<int, 3> kBlue{255, 0, 0};    // R, G, B
        return cv::Scalar(kBlue[2], kBlue[1], kBlue[0]);  // Blue #0000FF
      }
      case SubType::ST_MOTORCYCLIST: {
        // Darker than pure green to make a white text on it more visible.
        constexpr std::array<int, 3> kGreen{0, 128, 0};      // R, G, B
        return cv::Scalar(kGreen[2], kGreen[1], kGreen[0]);  // Green #008000
      }
      default: {
        constexpr std::array<int, 3> kWhite{255, 255, 255};  // R, G, B
        return cv::Scalar(kWhite[2], kWhite[1], kWhite[0]);  // White #FFFFFF
      }
    }
  }

  void PublishOverlay(const rclcpp::Publisher<Image>::SharedPtr& overlay_writer, Image& dst_image,
                      const std::shared_ptr<const Image::BorrowedType>& src_image) {
    auto& dst_header = dst_image.header;
    const auto& src_header = src_image->header;
    dst_header.frame_id = src_header.frame_id;
    dst_header.creation_timestamp = node_.now().nanoseconds();
    dst_header.measurement_timestamp = src_header.measurement_timestamp;
    overlay_writer->publish(dst_image);
  }

  rclcpp::Node& node_;
  bev::Config config_;

  /*
   * @brief overlay_writers_ camera index to overlay writer map
   */
  std::vector<int> overlay_camera_indices_;
  std::vector<rclcpp::Publisher<Image>::SharedPtr> overlay_writers_;
  std::vector<Image> dst_images_;

  cv::Mat src_host_undistorted_mat_;
  double overlay_scale_x_;
  double overlay_scale_y_;
};

std::shared_ptr<Bbox3dRenderer> CreateBbox3dRenderer(rclcpp::Node& node, bev::Config config) {
  auto renderer = std::make_shared<Bbox3dRendererImpl>(node, std::move(config));
  return renderer;
}
}  // namespace t2::perception::detection::bevfusion
