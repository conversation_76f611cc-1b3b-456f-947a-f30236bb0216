// Copyright (c) 2024-2025 T2 Inc. All rights reserved.
#pragma once

#include <memory>

#include <image_msgs/msg/image.hpp>
#include <perception_msgs/msg/perception_obstacles.hpp>
#include <rclcpp/rclcpp.hpp>

#include "src/perception/detection/bev/bevfusion/bevfusion_inputs.hpp"
#include "src/perception/detection/bev/proto/config.pb.h"
#include "src/perception/detection/types/bev_data.hpp"

namespace t2::perception::detection::bevfusion {

/**
 * @brief `BEVFusion` class is a interface class, which only exposure `Forward`
 * function.
 */
class Bbox3dRenderer {
 public:
  using PerceptionObstacles = ::perception_msgs::msg::PerceptionObstacles::FlatType;
  using BevData = ::t2::perception::detection::BevData;
  using BEVFusionInput = ::t2::perception::detection::BEVFusionInput;
  using Image = ::image_msgs::msg::Image;

  Bbox3dRenderer() = default;
  virtual ~Bbox3dRenderer() = default;
  Bbox3dRenderer(const Bbox3dRenderer&) = delete;
  Bbox3dRenderer& operator=(const Bbox3dRenderer&) = delete;
  Bbox3dRenderer(Bbox3dRenderer&&) = delete;
  Bbox3dRenderer& operator=(Bbox3dRenderer&&) = delete;

  virtual void Render(const BEVFusionInput& input, const BevData& bev_data,
                      const PerceptionObstacles& obstacles) = 0;

  virtual std::vector<rclcpp::Publisher<Image>::SharedPtr> GetPublishers() const = 0;
};

std::shared_ptr<Bbox3dRenderer> CreateBbox3dRenderer(rclcpp::Node& node, bev::Config config);

}  // namespace t2::perception::detection::bevfusion
