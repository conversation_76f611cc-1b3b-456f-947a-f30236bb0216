// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <Eigen/Geometry>

#include <perception_msgs/msg/perception_obstacles.hpp>

#include "src/perception/detection/common/proto/noise_filter.pb.h"

namespace t2::perception::common {

void EstimateOcclusion(perception_msgs::msg::PerceptionObstacles::FlatType& obstacles,
                       const Eigen::Affine3d& pose,
                       const t2::perception::OcclusionFilterConfig& config);
}  // namespace t2::perception::common
