// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "occlusion_filter.hpp"

#include <fmt/format.h>

#include <rclcpp/rclcpp.hpp>

#include "obstacle_utils.hpp"
#include "src/common/core/logging.hpp"

#define DEBUG_OCCLUDED_OBJECTS (0)

namespace t2::perception::common {
namespace {

struct LidarObstacle {
  int idx;
  bool is_occlusion;
  double dist;
  double min_angle;
  double max_angle;
  double center_angle;
  double local_x;
  double local_y;
  double width;
  double length;
  double height;
};

bool IsAngleOverlap(const LidarObstacle& obstacle, const double angle) {
  return obstacle.min_angle > obstacle.max_angle
             // e.g. angle_min=350,angle_max=10 (degree)
             ? (angle >= obstacle.min_angle) || (angle <= obstacle.max_angle)
             // e.g. angle_min=10,angle_max=20 (degree)
             : (angle >= obstacle.min_angle) && (angle <= obstacle.max_angle);
}

}  // namespace

void EstimateOcclusion(perception_msgs::msg::PerceptionObstacles::FlatType& obstacles,
                       const Eigen::Affine3d& pose,
                       const t2::perception::OcclusionFilterConfig& config) {
  // calc world to vehicle
  const auto ego_heading = pose.rotation().eulerAngles(2, 1, 0)[0];
  auto rotation_world_to_vehicle = [cos_theta = std::cos(-ego_heading),
                                    sin_theta = std::sin(-ego_heading)](double pos_x,
                                                                        double pos_y) {
    const double rot_x = cos_theta * pos_x - sin_theta * pos_y;
    const double rot_y = sin_theta * pos_x + cos_theta * pos_y;
    return std::make_pair(rot_x, rot_y);
  };

  std::vector<LidarObstacle> lidar_obstacles;
  lidar_obstacles.reserve(obstacles.perception_obstacle.size());
  const auto start_time = rclcpp::Clock().now();
  constexpr double kTwoPi = 2.0 * M_PI;
  constexpr double kDegToRad = M_PI / 180.0;
  auto normalized_atan2 = [](double y, double x) {
    double angle = std::atan2(y, x);
    if (angle < 0.0) {
      angle += kTwoPi;
    }
    return angle;
  };

  std::vector<float> tmp_angles;
  for (int idx = 0; idx < static_cast<int>(obstacles.perception_obstacle.size()); ++idx) {
    const auto& obs_org = obstacles.perception_obstacle[idx];
    // TODO(someone): avoid PerceptionObstacle hard copy
    auto obs_local = obs_org;
    const auto [local_x, local_y] = rotation_world_to_vehicle(
        obs_org.position.x - pose.translation().x(), obs_org.position.y - pose.translation().y());
    obs_local.position.x = local_x;
    obs_local.position.y = local_y;
    obs_local.theta = obs_org.theta - ego_heading;

    double min_dist = std::numeric_limits<double>::max();
    tmp_angles.clear();
    // 車両形状の頂点を取得
    const auto polygon_points = ConstructPolygon2d(obs_local, 1.0, 0.0);
    for (const auto& pt : polygon_points.GetAllVertices()) {
      tmp_angles.emplace_back(normalized_atan2(pt.y(), pt.x()));
      const double dist = pt.x() * pt.x() + pt.y() * pt.y();
      min_dist = std::min(min_dist, dist);
    }
    // 自車座標系の角度順でソート
    std::sort(tmp_angles.begin(), tmp_angles.end());
    double max_diff = 0.0;
    size_t max_diff_idx = 0;
    for (size_t i = 0; i < tmp_angles.size() - 1; ++i) {
      double next_angle = tmp_angles[i + 1];
      double diff = next_angle - tmp_angles[i];
      if (diff < 0) {
        T2_ERROR << "something wrong after sorting";
        diff += kTwoPi;
      }
      if (diff > max_diff) {
        max_diff = diff;
        max_diff_idx = i;
      }
    }
    double min_angle = tmp_angles.front();
    double max_angle = tmp_angles.back();
    if (max_diff > M_PI) {  // 頂点の角度範囲が0をまたぐ場合
      // e.g. tmp_angles=[1,2,357,358(degree)] -> min_angle=357, max_angle=2
      min_angle = tmp_angles[max_diff_idx + 1];
      max_angle = tmp_angles[max_diff_idx];
    }

    min_angle = std::max(0.0, min_angle - config.angle_expansion_degree() * kDegToRad);
    max_angle = std::min(kTwoPi, max_angle + config.angle_expansion_degree() * kDegToRad);
    lidar_obstacles.emplace_back(LidarObstacle{
        idx, false, min_dist, min_angle, max_angle, normalized_atan2(local_y, local_x), local_x,
        local_y, obs_local.width, obs_local.length, obs_local.height});
  }

  // 自車からの距離でソート
  std::sort(lidar_obstacles.begin(), lidar_obstacles.end(),
            [](const auto& a, const auto& b) { return a.dist < b.dist; });
  // 3D遮蔽判定
  for (size_t sidx = 0; sidx < lidar_obstacles.size(); ++sidx) {
    const auto& slo = lidar_obstacles[sidx];  // 遮蔽する物体
    // 小さい物体は遮蔽判定から除外
    constexpr double kHeightThreshold = 1.0;  // パラメータ化するべき
    if (slo.height < kHeightThreshold) {
      continue;
    }
    for (size_t tidx = sidx + 1; tidx < lidar_obstacles.size(); ++tidx) {
      auto& tlo = lidar_obstacles[tidx];  // 遮蔽される物体
      if (tlo.is_occlusion) {
        continue;
      }
      // 遮蔽する物体(slo)が遮蔽される物体(tlo)より近く、角度範囲が重なる場合
      if (slo.dist < tlo.dist && IsAngleOverlap(slo, tlo.center_angle)) {
        tlo.is_occlusion = true;
#if DEBUG_OCCLUDED_OBJECTS
        constexpr double kRadToDeg = 180.0 / M_PI;
        const double slo_x = slo.local_x;
        const double slo_y = slo.local_y;
        const double tlo_x = tlo.local_x;
        const double tlo_y = tlo.local_y;
        T2_DEBUG << fmt::format("tlo_x={},tlo_y={},slo_x={},slo_y={},slo_w={},slo_l={}", tlo_x,
                                tlo_y, slo_x, slo_y, slo.width, slo.length);
        T2_DEBUG << fmt::format("slo_id={}, tlo_id={},slo_angle[{},{}],tlo_angle[{},{}]", slo.idx,
                                tlo.idx, slo.min_angle * kRadToDeg, slo.max_angle * kRadToDeg,
                                tlo.min_angle * kRadToDeg, tlo.max_angle * kRadToDeg);
#endif
      }
    }
  }

  for (const auto& lo : lidar_obstacles) {
    obstacles.perception_obstacle[lo.idx].occluded = lo.is_occlusion;
  }

  T2_DEBUG << "Time[Preprocess]: " << (rclcpp::Clock().now() - start_time).seconds() * 1e3
           << " [ms]";
}

}  // namespace t2::perception::common
