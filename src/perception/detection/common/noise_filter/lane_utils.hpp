// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <Eigen/Geometry>

#include <perception_msgs/msg/perception_obstacles.hpp>

#include "src/perception/detection/common/proto/noise_filter.pb.h"

namespace t2::perception::common {
void EstimateLaneForObstacle(perception_msgs::msg::PerceptionObstacles::FlatType& msg,
                             const Eigen::Affine3d& pose, const LaneFilterConfig& config);

}  // namespace t2::perception::common
