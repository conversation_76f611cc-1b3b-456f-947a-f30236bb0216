// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "obstacle_utils.hpp"

#include "src/common/core/logging.hpp"

namespace t2::perception::common {

t2::common::math::Polygon2d ConstructPolygon2d(
    const perception_msgs::msg::PerceptionObstacle::FlatType& obstacle, const float expansion_rate,
    const float expansion_dist) {
  if (obstacle.width > 0.0 && obstacle.length > 0.0) {
    // Construct polygon from the bounding box
    const auto x = obstacle.position.x;
    const auto y = obstacle.position.y;
    const auto l = std::max(obstacle.length * 0.5 * expansion_rate + expansion_dist, 0.2);
    const auto w = std::max(obstacle.width * 0.5 * expansion_rate + expansion_dist, 0.2);
    const auto yaw = obstacle.theta;
    const auto cos_yaw = std::cos(yaw);
    const auto sin_yaw = std::sin(yaw);
    std::vector<t2::common::math::Vec2d> points;
    points.emplace_back(x + l * cos_yaw - w * sin_yaw, y + l * sin_yaw + w * cos_yaw);
    points.emplace_back(x - l * cos_yaw - w * sin_yaw, y - l * sin_yaw + w * cos_yaw);
    points.emplace_back(x - l * cos_yaw + w * sin_yaw, y - l * sin_yaw - w * cos_yaw);
    points.emplace_back(x + l * cos_yaw + w * sin_yaw, y + l * sin_yaw - w * cos_yaw);
    return t2::common::math::Polygon2d(points);
  } else if (obstacle.polygon_point.size() > 0) {
    // Construct polygon from the polygon
    const auto x = obstacle.position.x;
    const auto y = obstacle.position.y;
    std::vector<t2::common::math::Vec2d> points;
    points.reserve(obstacle.polygon_point.size());
    for (const auto& p : obstacle.polygon_point) {
      points.emplace_back(t2::common::math::Vec2d{x + (p.x - x) * expansion_rate + expansion_dist,
                                                  y + (p.y - y) * expansion_rate + expansion_dist});
    }
    return t2::common::math::Polygon2d(points);
  }

  const std::string msg = "PerceptionObstacle has no width/length and polygon";
  T2_ERROR << msg;
  throw std::runtime_error(msg);
}
}  // namespace t2::perception::common
