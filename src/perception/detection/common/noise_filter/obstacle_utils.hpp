// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <perception_msgs/msg/perception_obstacle.hpp>

#include "src/common/math/polygon2d.hpp"

namespace t2::perception::common {
t2::common::math::Polygon2d ConstructPolygon2d(
    const perception_msgs::msg::PerceptionObstacle::FlatType& object, const float expansion_rate,
    const float expansion_dist);
}  // namespace t2::perception::common
