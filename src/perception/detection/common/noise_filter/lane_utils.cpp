// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "lane_utils.hpp"

#include <sophus/geometry.hpp>

#include "obstacle_utils.hpp"
#include "src/common/hdmap/hdmap_filter.hpp"
#include "src/map/hdmap/hdmap_common.hpp"

namespace t2::perception::common {

namespace {
void SearchPredecessors(const std::shared_ptr<const t2::map::hdmap::LaneInfo>& lane,
                        const double length, std::vector<std::string>& connected_lane_ids,
                        const double threshold) {
  if (length > threshold) {
    return;
  }
  for (const auto& predecessor_lane_id : lane->lane().predecessor_id()) {
    const auto result =
        std::find(connected_lane_ids.begin(), connected_lane_ids.end(), predecessor_lane_id.id());
    if (result != connected_lane_ids.end()) {
      continue;
    }
    const auto predecessor_lane =
        t2::common::hdmap::HdmapFilter::LaneById(predecessor_lane_id.id());
    if (predecessor_lane == nullptr) {
      continue;
    }
    const auto sum_length = predecessor_lane->total_length() + length;
    connected_lane_ids.emplace_back(predecessor_lane_id.id());
    SearchPredecessors(predecessor_lane, sum_length, connected_lane_ids, threshold);
  }
}

void SearchSuccessors(const std::shared_ptr<const t2::map::hdmap::LaneInfo>& lane,
                      const double length, std::vector<std::string>& connected_lane_ids,
                      const double threshold, const bool only_successor) {
  if (length > threshold) {
    return;
  }
  std::vector<std::string> new_successor_lane_ids;
  for (const auto& successor_lane_id : lane->lane().successor_id()) {
    const auto result =
        std::find(connected_lane_ids.begin(), connected_lane_ids.end(), successor_lane_id.id());
    if (result != connected_lane_ids.end()) {
      continue;
    }
    new_successor_lane_ids.emplace_back(successor_lane_id.id());
  }
  for (const auto& successor_lane_id : new_successor_lane_ids) {
    const auto successor_lane = t2::common::hdmap::HdmapFilter::LaneById(successor_lane_id);
    if (successor_lane == nullptr) {
      continue;
    }
    const auto sum_length = successor_lane->total_length() + length;
    connected_lane_ids.emplace_back(successor_lane_id);
    SearchSuccessors(successor_lane, sum_length, connected_lane_ids, threshold, only_successor);
  }
  if (only_successor) {
    return;
  }

  // right and left lanes
  std::vector<std::string> new_neighbor_ids;
  for (const auto& neighbor_id : lane->lane().right_neighbor_forward_lane_id()) {
    const auto result =
        std::find(connected_lane_ids.begin(), connected_lane_ids.end(), neighbor_id.id());
    if (result != connected_lane_ids.end()) {
      continue;
    }
    new_neighbor_ids.emplace_back(neighbor_id.id());
  }
  for (const auto& neighbor_id : lane->lane().left_neighbor_forward_lane_id()) {
    const auto result =
        std::find(connected_lane_ids.begin(), connected_lane_ids.end(), neighbor_id.id());
    if (result != connected_lane_ids.end()) {
      continue;
    }
    new_neighbor_ids.emplace_back(neighbor_id.id());
  }
  for (const auto& neighbor_id : new_neighbor_ids) {
    const auto neighbor = t2::common::hdmap::HdmapFilter::LaneById(neighbor_id);
    if (neighbor == nullptr) {
      continue;
    }
    const auto sum_length = neighbor->total_length() + length;
    connected_lane_ids.emplace_back(neighbor_id);
    SearchSuccessors(neighbor, sum_length, connected_lane_ids, threshold, true);
  }

  // predecessor lanes
  std::vector<std::string> predecessor_ids;
  for (const auto& neighbor_id : lane->lane().predecessor_id()) {
    const auto result =
        std::find(connected_lane_ids.begin(), connected_lane_ids.end(), neighbor_id.id());
    if (result != connected_lane_ids.end()) {
      continue;
    }
    predecessor_ids.emplace_back(neighbor_id.id());
  }
  for (const auto& neighbor_id : predecessor_ids) {
    const auto neighbor = t2::common::hdmap::HdmapFilter::LaneById(neighbor_id);
    if (neighbor == nullptr) {
      continue;
    }
    const auto sum_length = neighbor->total_length() + length;
    connected_lane_ids.emplace_back(neighbor_id);
    SearchPredecessors(neighbor, sum_length, connected_lane_ids, threshold);
  }
}

bool LoadHdMap() {
  // TODO(someone): ここの操作をどうするか検討
  //   google::SetCommandLineOption("flagfile", "/apollo/modules/common/data/global_flagfile.txt");
  const bool is_ready = t2::common::hdmap::HdmapFilter::Ready();
  if (!is_ready) {
    T2_ERROR << "Failed to load hdmap";
  }
  return is_ready;
}

t2::map::hdmap::LaneInfoConstPtr QueryCurrentLane(const Eigen::Affine3d& pose,
                                                  const double search_radius,
                                                  const double max_angle_diff) {
  t2::common::PointENU pose_enu;
  pose_enu.set_x(pose.translation().x());
  pose_enu.set_y(pose.translation().y());
  return t2::common::hdmap::HdmapFilter::GetMostLikelyCurrentLane(
      pose_enu, search_radius, Sophus::SE3d(pose.matrix()).angleZ(), max_angle_diff);
}

std::vector<t2::map::hdmap::LaneInfoConstPtr> CollectSurroundLanes(const Eigen::Affine3d& pose,
                                                                   const LaneFilterConfig& config) {
  if (!LoadHdMap()) {
    return {};
  }
  const double search_radius = config.lane_search_radius();
  const double max_angle_diff = config.lane_max_angle_diff();
  return t2::common::hdmap::HdmapFilter::CollectLanes(
      Eigen::Vector2d(pose.translation().x(), pose.translation().y()),
      Sophus::SE3d(pose.matrix()).angleZ(), search_radius, max_angle_diff);
}

std::vector<t2::map::hdmap::LaneInfoConstPtr> CollectConnectedLanes(
    const Eigen::Affine3d& pose, const LaneFilterConfig& config) {
  if (!LoadHdMap()) {
    return {};
  }
  const auto current_lane =
      QueryCurrentLane(pose, config.ego_lane_search_radius(), config.lane_max_angle_diff());
  if (current_lane == nullptr) {
    T2_WARN << "Ego vehicle is not near the lane.";
    return {};
  }
  std::vector<std::string> connected_lane_ids;
  const auto current_lane_id = current_lane->lane().id();
  connected_lane_ids.emplace_back(current_lane_id.id());
  const double sum_successor_length_threshold = config.successor_length_threshold();
  const double sum_predecessor_length_threshold = config.predecessor_length_threshold();
  for (const auto& right_lane_id : current_lane->lane().right_neighbor_forward_lane_id()) {
    const auto right_lane = t2::common::hdmap::HdmapFilter::LaneById(right_lane_id.id());
    if (right_lane == nullptr) {
      continue;
    }
    connected_lane_ids.emplace_back(right_lane_id.id());
    SearchSuccessors(right_lane, 0.0, connected_lane_ids, sum_successor_length_threshold, true);
    SearchPredecessors(right_lane, 0.0, connected_lane_ids, sum_predecessor_length_threshold);
  }
  for (const auto& left_lane_id : current_lane->lane().left_neighbor_forward_lane_id()) {
    const auto left_lane = t2::common::hdmap::HdmapFilter::LaneById(left_lane_id.id());
    if (left_lane == nullptr) {
      continue;
    }
    connected_lane_ids.emplace_back(left_lane_id.id());
    SearchSuccessors(left_lane, 0.0, connected_lane_ids, sum_successor_length_threshold, true);
    SearchPredecessors(left_lane, 0.0, connected_lane_ids, sum_predecessor_length_threshold);
  }
  SearchSuccessors(current_lane, 0.0, connected_lane_ids, sum_successor_length_threshold, false);
  SearchPredecessors(current_lane, 0.0, connected_lane_ids, sum_predecessor_length_threshold);
  std::vector<std::shared_ptr<const t2::map::hdmap::LaneInfo>> connected_lanes;
  for (const auto& caution_id : connected_lane_ids) {
    connected_lanes.push_back(t2::common::hdmap::HdmapFilter::LaneById(caution_id));
  }
  return connected_lanes;
}

void EstimateLaneForObstacle(perception_msgs::msg::PerceptionObstacles::FlatType& msg,
                             const std::vector<t2::map::hdmap::LaneInfoConstPtr>& lanes) {
  for (auto& obstacle : msg.perception_obstacle) {
    t2::map::hdmap::LaneInfoConstPtr min_lane = nullptr;

    // 幅と高さを1m拡張した（意図的かどうか不明）bboxのコーナー4点を計算
    const auto polygon = ConstructPolygon2d(obstacle, 1.0, 1.0);

    // レーンごとに、コーナー点がレーン上にあるかをチェックしていく
    for (const auto& lane : lanes) {
      for (const auto& p : polygon.GetAllVertices()) {
        // コーナー4点のうち一点でもレーン上にあればOnLane判定
        if (lane->IsOnLane(t2::common::math::Vec2d{p.x(), p.y()})) {
          min_lane = lane;
          break;
        }
      }
      // min_lane != nullptr ならループを終了して良さそう？
    }

    if (min_lane != nullptr) {
      obstacle.lane_id = min_lane->id().id().substr(0, perception_msgs::MAX_LANE_ID_LENGTH);
    }
  }
}

}  // namespace

void EstimateLaneForObstacle(perception_msgs::msg::PerceptionObstacles::FlatType& msg,
                             const Eigen::Affine3d& pose, const LaneFilterConfig& config) {
  // 自車から一定距離内のレーンを取ってくる
  auto lanes = CollectSurroundLanes(pose, config);
  // 自車の今居るレーンとそれに接続している前後左右のレーンを取ってくる
  const auto& connected_lanes = CollectConnectedLanes(pose, config);

  // 取ってきたレーンセットをマージ（重複があり得る）
  lanes.insert(lanes.end(), connected_lanes.begin(), connected_lanes.end());

  EstimateLaneForObstacle(msg, lanes);
}

}  // namespace t2::perception::common
