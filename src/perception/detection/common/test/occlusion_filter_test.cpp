// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "src/perception/detection/common/noise_filter/occlusion_filter.hpp"

#include <fmt/format.h>
#include <gtest/gtest.h>

#include <Eigen/Geometry>

namespace t2::perception::common {
using PerceptionObstacles = perception_msgs::msg::PerceptionObstacles::FlatType;
using PerceptionObstacle = perception_msgs::msg::PerceptionObstacle::FlatType;

class OcclusionFilterTest : public ::testing::Test {
 protected:
  void SetUp() override { config_.set_angle_expansion_degree(0.0); }
  PerceptionObstacles SetUpObstacles() const;
  void RotateObstacles(PerceptionObstacles& obstacles, const float theta) const;
  bool RotateEgoPoseAndValidateOcclusion(PerceptionObstacles& obstacles) const;
  bool RotateTest(const float obstacle_pos_angle) const;
  bool RotateTestFrontObstacleHeightLow(const float obstacle_pos_angle);
  bool ValidateOcclusion(const PerceptionObstacles& obstacles) const;
  PerceptionObstacle CreateObstacle(const float x, const float y, const float theta = 0.f) const;
  t2::perception::OcclusionFilterConfig config_;
  const float width_ = 1.0;
  const float length_ = 1.0;
  const float height_ = 2.0;  // 1mより低いと遮蔽物としてみなされない
  std::array<bool, 9> expected_occluded_{false, false, true, false, false, true, true, true, false};
};

PerceptionObstacles OcclusionFilterTest::SetUpObstacles() const {
  // 初期配置
  //  [4] [5] [6] [7] [8]
  //      [1] [2] [3]
  //          [0]
  //
  //           x
  //           |
  //     y <---+
  PerceptionObstacles obstacles{::rosidl_runtime_cpp::ConstructorTag{}};
  obstacles.perception_obstacle = {
      CreateObstacle(20.0, 0.0),   //[0]
      CreateObstacle(22.0, 2.0),   //[1]
      CreateObstacle(22.0, 0.0),   //[2]
      CreateObstacle(22.0, -2.0),  //[3]
      CreateObstacle(24.0, 4.0),   //[4]
      CreateObstacle(24.0, 2.0),   //[5]
      CreateObstacle(24.0, 0.0),   //[6]
      CreateObstacle(24.0, -2.0),  //[7]
      CreateObstacle(24.0, -4.0),  //[8]
  };
  return obstacles;
}

void OcclusionFilterTest::RotateObstacles(PerceptionObstacles& obstacles, const float theta) const {
  for (size_t idx = 0; idx < obstacles.perception_obstacle.size(); ++idx) {
    auto& obstacle = obstacles.perception_obstacle[idx];
    obstacle.theta = theta;
    const float cos_theta = std::cos(theta);
    const float sin_theta = std::sin(theta);
    const float x = obstacle.position.x;
    const float y = obstacle.position.y;
    obstacle.position.x = x * cos_theta - y * sin_theta;
    obstacle.position.y = x * sin_theta + y * cos_theta;
  }
}

bool OcclusionFilterTest::RotateEgoPoseAndValidateOcclusion(PerceptionObstacles& obstacles) const {
  auto ego_pose = Eigen::Affine3d::Identity();
  bool result = true;
  for (int i = 0; i < 4; ++i) {  // test ego heading[90, 180, 270, 0]
    ego_pose.rotate(Eigen::AngleAxisd(M_PI_2, Eigen::Vector3d::UnitZ()));
    EstimateOcclusion(obstacles, ego_pose, config_);
    const bool is_correct = ValidateOcclusion(obstacles);
    EXPECT_TRUE(is_correct) << fmt::format("i={}", i);
    result &= is_correct;
  }
  return result;
}

bool OcclusionFilterTest::RotateTest(const float obstacle_pos_angle) const {
  auto obstacles = SetUpObstacles();
  EXPECT_EQ(config_.angle_expansion_degree(), 0.0);
  RotateObstacles(obstacles, obstacle_pos_angle);
  const bool is_correct = RotateEgoPoseAndValidateOcclusion(obstacles);
  EXPECT_TRUE(is_correct) << fmt::format("obstacle_pos_angle={}", obstacle_pos_angle);
  return is_correct;
}

bool OcclusionFilterTest::RotateTestFrontObstacleHeightLow(const float obstacle_pos_angle) {
  auto obstacles = SetUpObstacles();
  EXPECT_EQ(config_.angle_expansion_degree(), 0.0);
  RotateObstacles(obstacles, obstacle_pos_angle);
  // 先頭の物体の高さを50cmに変更
  obstacles.perception_obstacle[0].height = 0.5;
  // 先頭背後の物体はOccluded判定されない
  expected_occluded_[2] = false;
  const bool is_correct = RotateEgoPoseAndValidateOcclusion(obstacles);
  EXPECT_TRUE(is_correct) << fmt::format("obstacle_pos_angle={}", obstacle_pos_angle);
  return is_correct;
}

PerceptionObstacle OcclusionFilterTest::CreateObstacle(const float x, const float y,
                                                       const float theta) const {
  PerceptionObstacle obstacle{::rosidl_runtime_cpp::ConstructorTag{}};
  obstacle.position.x = x;
  obstacle.position.y = y;
  obstacle.position.z = 0.0;
  obstacle.width = width_;
  obstacle.length = length_;
  obstacle.height = height_;
  obstacle.theta = theta;
  return obstacle;
}

bool OcclusionFilterTest::ValidateOcclusion(const PerceptionObstacles& obstacles) const {
  bool result = true;
  EXPECT_EQ(obstacles.perception_obstacle.size(), expected_occluded_.size());
  for (size_t idx = 0; idx < obstacles.perception_obstacle.size(); ++idx) {
    const auto& obs_org = obstacles.perception_obstacle[idx];
    const bool is_correct = obs_org.occluded == expected_occluded_[idx];
    EXPECT_TRUE(is_correct) << fmt::format("idx={}, expected={}, result={}", idx,
                                           expected_occluded_[idx], obs_org.occluded);
    result &= is_correct;
  }
  return result;
}

TEST_F(OcclusionFilterTest, RotateTest) {
  EXPECT_TRUE(RotateTest(0.0));
  EXPECT_TRUE(RotateTest(M_PI / 2.0));
  EXPECT_TRUE(RotateTest(M_PI));
  EXPECT_TRUE(RotateTest(3.0 * M_PI / 2.0));
}
TEST_F(OcclusionFilterTest, RotateTestFrontObstacleHeightLow) {
  EXPECT_TRUE(RotateTestFrontObstacleHeightLow(0.0));
  EXPECT_TRUE(RotateTestFrontObstacleHeightLow(M_PI / 2.0));
  EXPECT_TRUE(RotateTestFrontObstacleHeightLow(M_PI));
  EXPECT_TRUE(RotateTestFrontObstacleHeightLow(3.0 * M_PI / 2.0));
}

}  // namespace t2::perception::common
