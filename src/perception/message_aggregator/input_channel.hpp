// Copyright (c) 2024-2025 T2 Inc. All rights reserved.

#pragma once

#include <cmath>
#include <functional>
#include <memory>

#include <rclcpp/node.hpp>
#include <rclcpp/polling_subscription.hpp>

#include "src/common/contract_assertions/contract_assertions.hpp"
#include "src/common/core/chrono_consts.hpp"
#include "src/common/core/logging.hpp"
#include "src/common/math/math_utils.hpp"
#include "src/perception/message_aggregator/proto/aggregation_config.pb.h"
#include "src/perception/message_aggregator/ring_buffer.hpp"
#include "src/perception/message_aggregator/time_utils.hpp"

namespace t2::message_aggregator::detail {

template <typename MessageT>
using MessagePointerEventHandler =
    std::function<void(const std::shared_ptr<typename MessageT::BorrowedType>&)>;

enum class MessageSearchResult : uint8_t {
  kNotFound = 0,
  kFoundAcceptable = 1,
  ///< Found a message which timestamp's delta is within promptly_acceptable_time_delta_range.
  ///< The found message should be promptly taken into a resulting aggregated data set without
  ///< further waiting.
  kFoundPromptlyAcceptable = 2,
  ///< Found a message which timestamp's delta is within acceptable_time_delta_range.
  ///< The found message is accepted after wait time has passed if there is no message satisfying
  ///< the promptly_acceptable_time_delta_range criterion.
};

inline bool IsWithinRange(std::int64_t timestamp_delta,
                          const t2::message_aggregator::proto::TimeDeltaRange& range) {
  using t2::common::chrono_consts::kNanosecsPerSec;
  return static_cast<std::int64_t>(range.min() * kNanosecsPerSec<double>) <= timestamp_delta &&
         timestamp_delta <= static_cast<std::int64_t>(range.max() * kNanosecsPerSec<double>);
}

/**
 * @brief Implementation of data aggregation handler of key message topic which provide key
 * timestamps.
 * @tparam MessageT Type of message
 */
template <typename MessageT>
class InputHandler {
 public:
  /**
   * @brief Initializes a data aggregation handler of a topic
   * @param topic_name Topic name to listen to
   * @param buffer_size Max number of messages to store in history
   * @param callback_on_new_message_fn Callback function which will be invoked when a
   * new message is arrived on the key topic.
   * @param fill_fn A function describing how to store search results from
   * FillMessageByTimestampRange
   */
  InputHandler(const std::string& topic_name, std::uint32_t buffer_size, rclcpp::Node& node,
               MessagePointerEventHandler<MessageT> fill_fn)
      : topic_reader_{node.create_polling_subscription<MessageT>(
            topic_name, rclcpp::DefaultQoS().keep_last(buffer_size))},
        fill_fn_(std::move(fill_fn)),
        buffer_(buffer_size) {
    expect_enforce(fill_fn_ != nullptr);
  }

  /**
   * @brief Search a message which timestamp is within the given timestamp range.
   * If there are multiple, the message closest to the key_timestamp will be selected
   * The result is automatically filled by calling the fill function owned by the InputHandler
   * @param key_timestamp The message in (key_timestamp+range.min, key_timestamp+range.max) is
   * selected.
   * @param promptly_acceptable_time_delta_range high priority range
   * @param acceptable_time_delta_range secondary priority range
   * @return MessageSearchResult describing in which range, if any, a message was found
   */
  MessageSearchResult FillMessageByTimestampRange(
      const std::uint64_t key_timestamp,
      const t2::message_aggregator::proto::TimeDeltaRange& promptly_acceptable_time_delta_range,
      const t2::message_aggregator::proto::TimeDeltaRange& acceptable_time_delta_range) {
    std::optional<uint64_t> best_promptly_acceptable;
    std::optional<uint64_t> best_acceptable;
    std::int64_t best_delta = std::numeric_limits<std::int64_t>::max();

    this->TakeMessageIntoBuffer();

    std::lock_guard lk{buffer_mutex_};
    if (buffer_.Begin() == buffer_.End()) {
      T2_WARN << "No messages received yet on " << topic_reader_->get_topic_name()
              << ". Cannot aggregate.";
      return MessageSearchResult::kNotFound;
    }

    for (uint64_t i = buffer_.Begin(); i < buffer_.End(); ++i) {
      uint64_t candidate_timestamp = GetMeasurementTimestamp(*buffer_[i]);
      const int64_t timestamp_delta =
          t2::common::math::SubtractU64(candidate_timestamp, key_timestamp);
      if (IsWithinRange(timestamp_delta, promptly_acceptable_time_delta_range)) {
        if (!best_promptly_acceptable || std::abs(timestamp_delta) <= std::abs(best_delta)) {
          best_promptly_acceptable = i;
          best_delta = timestamp_delta;
        }
      } else if (best_promptly_acceptable) {
        // Already past promptly acceptable range, cant get better
        break;
      }
      if (IsWithinRange(timestamp_delta, acceptable_time_delta_range)) {
        if (!best_acceptable || std::abs(timestamp_delta) <= std::abs(best_delta)) {
          best_acceptable = i;
          best_delta = timestamp_delta;
        }
      }
    }
    if (best_promptly_acceptable) {
      fill_fn_(buffer_[*best_promptly_acceptable]);
      return MessageSearchResult::kFoundPromptlyAcceptable;
    } else if (best_acceptable) {
      fill_fn_(buffer_[*best_acceptable]);
      return MessageSearchResult::kFoundAcceptable;
    }
    return MessageSearchResult::kNotFound;
  }

  typename rclcpp::PollingSubscription<MessageT>::SharedPtr GetSubscriptionPtr() {
    return topic_reader_;
  }

 private:
  /**
   * @brief Take messages from the topic and store them in the buffer
   */
  void TakeMessageIntoBuffer() {
    std::lock_guard lk(buffer_mutex_);
    const auto& messages = topic_reader_->take();
    for (const auto& message : messages) {
      if (message.info().valid()) {
        buffer_.Push(std::make_shared<typename MessageT::BorrowedType>(message.data()));
      }
    }
  }

  typename rclcpp::PollingSubscription<MessageT>::SharedPtr topic_reader_;
  MessagePointerEventHandler<MessageT> fill_fn_;

  std::mutex buffer_mutex_;
  RingBuffer<std::shared_ptr<typename MessageT::BorrowedType>> buffer_;
};

/**
 * @brief Type-erased base class for input handlers
 */
class InputHandlerBase {
 public:
  InputHandlerBase() = default;
  virtual ~InputHandlerBase() = default;
  InputHandlerBase(const InputHandlerBase&) = delete;
  InputHandlerBase& operator=(const InputHandlerBase&) = delete;
  InputHandlerBase(InputHandlerBase&&) = delete;
  InputHandlerBase& operator=(InputHandlerBase&&) = delete;

  virtual MessageSearchResult FillMessageByTimestampRange(
      const std::uint64_t key_timestamp,
      const t2::message_aggregator::proto::TimeDeltaRange& promptly_acceptable_time_delta_range,
      const t2::message_aggregator::proto::TimeDeltaRange& acceptable_time_delta_range) const = 0;

  virtual rclcpp::PollingSubscriptionBase::SharedPtr GetSubscriptionPtr() = 0;
};

template <typename MessageT>
class InputHandlerWrapper final : public InputHandlerBase {
 public:
  explicit InputHandlerWrapper(std::unique_ptr<InputHandler<MessageT>> handler)
      : handler_(std::move(handler)) {
    expect_enforce(handler_ != nullptr);
  }

  MessageSearchResult FillMessageByTimestampRange(
      const std::uint64_t key_timestamp,
      const t2::message_aggregator::proto::TimeDeltaRange& promptly_acceptable_time_delta_range,
      const t2::message_aggregator::proto::TimeDeltaRange& acceptable_time_delta_range)
      const override {
    return handler_->FillMessageByTimestampRange(
        key_timestamp, promptly_acceptable_time_delta_range, acceptable_time_delta_range);
  }

  rclcpp::PollingSubscriptionBase::SharedPtr GetSubscriptionPtr() override {
    return handler_->GetSubscriptionPtr();
  }

 private:
  std::unique_ptr<InputHandler<MessageT>> handler_;
};

}  // namespace t2::message_aggregator::detail
