#include "geometry_msgs/msg/Point.idl"
#include "geometry_msgs/msg/Vector3.idl"

module perception_msgs {
  const unsigned long MAX_LANE_ID_LENGTH = 255;
  module msg {

    // おおまかな検出クラス分類
    enum ClassType {
      UNKNOWN,
      UNKNOWN_MOVABLE,
      UNKNOWN_UNMOVABLE,
      PEDESTRIAN,  // Pedestrian, usually determined by moving behavior.
      BICYCLE,     // bike, motor bike
      VEHICLE,     // Car or Kei truck or truck
      MAX_OBJECT_TYPE
    };

    // より細かい検出のクラス分類
    enum SubType {
      ST_UNKNOWN,
      ST_UNKNOWN_MOVABLE,
      ST_UNKNOWN_UNMOVABLE,
      ST_CAR,
      ST_VAN,
      ST_TRUCK,
      ST_BUS,
      ST_CYCLIST,
      ST_MOTORCYCLIST,
      ST_TRICYCLIST,
      ST_PEDESTRIAN,
      ST_TRAFFICCONE
    };

    // センサ種別
    enum SensorType {
      SENSOR_UNKNOWN,
      SENSOR_LIDAR,
      SENSOR_CAMERA,
      SENSOR_RADAR,
      SENSOR_LIDAR_CAMERA,
      SENSOR_RADAR_CAMERA,
      SENSOR_LIDAR_RADAR,
      SENSOR_LIDAR_CAMERA_RADAR
    };

    /**
    * Trackingの入出力のメッセージ型。
    * 入力時: Detection結果（車輌座標系）
    * 出力時: Tracking結果（世界座標系）
    * このメッセージ1つが、1つのBBox（検出/トラッカー）に対応する
    */
    struct PerceptionObstacle {
      int32 id;  // obstacle ID.

      // obstacle position in the world coordinate system.
      geometry_msgs::msg::Point position;
      // obstacle position in the vehicle coordinate system.
      geometry_msgs::msg::Point local_position;

      @default (value="nan")
      double theta;       // heading in the world coordinate system.
      double local_theta; // heading in the vehicle coordinate system

      geometry_msgs::msg::Vector3 velocity; // obstacle velocity.
      geometry_msgs::msg::Vector3 local_relative_velocity;  // obstacle velocity (relative velocity to ego vehicle) in the
                                                          // vehicle coordinate system.
      geometry_msgs::msg::Vector3 local_velocity; // obstacle velocity (absolute velocity of each obstacle) in the
                                                // vehicle coordinate system.

      // Size of obstacle bounding box.
      double length;
      double width;
      double height;

      // obstacle corner points.
      // 2D平面でのコーナー4点（z値は入ってない
      geometry_msgs::msg::Point polygon_point[4];

      ClassType type;  // obstacle type

      double timestamp; // GPS time in seconds.

      // 最後に検出によるアップデートがあった時刻
      // in seconds.
      double track_measurement_timestamp;

      // 最後に使用した検出結果のconfidence値
      // 値の範囲は [0, 1]
      double confidence;

      // Trackingによって計算されたObstacleの加速度
      // CTRAトラッカーでのみ値が入る
      // x,yの値は意味どおりだが、zは常に0
      sequence<geometry_msgs::msg::Vector3, 1> acceleration;  // optional

      // Trackingによって推定されたObstacleの進行方向加速度（符号付きスカラー値）
      // CTRAトラッカーでのみ値が入る
      @default (value="nan")
      double tangential_acceleration;

      // obstacle sub_type
      // Trackingからの出力は評価においてのみ使用している（実車環境では常にST_UNKNOWN）
      SubType sub_type;

      // position covariance which is a row-majored 3x3 matrix
      double position_covariance[9];

      // 検出に使用しているセンサ種別
      // BBoxごとに変化しないはず
      // Trackingの出力では使用されていない
      SensorType sensor_type;

      // 自車から見て、他のBBoxが間に挟まっているかどうか
      // fusionコンポーネントで設定され、オクルージョンフィルタで使用されている
      // Trackingの出力では使用されない
      boolean occluded;

      // BBoxが居るレーンのID
      // fusionコンポーネントで設定され、レーンフィルタに使用されている
      // Trackingの出力では使用されない
      string<256> lane_id;  // bound = MAX_LANE_ID_LENGTH + 1

      // bbox surface is visible or not. assume 4 elements. left, rear, right, front
      // Tracking処理の内部状態であり、出力では使用されない
      boolean surface_visibility[4];
    };

  };
};
