#include "common_msgs/msg/Header.idl"
#include "perception_msgs/msg/PerceptionObstacle.idl"

module perception_msgs {
  module msg {
    /**
    * Trackingの入出力のメッセージ型。
    * ほぼPerceptionObstacleの配列
    */
    struct PerceptionObstacles {
      common_msgs::msg::Header header;
      // 検出 or Tracking の結果の配列
      sequence<PerceptionObstacle, 500> perception_obstacle;

      // 処理時刻のタイムスタンプ。
      // 対応する入力と出力で同一になる
      double timestamp;

      // その他のメンバは全部使ってないはず
    };
  };
};
