#include "geometry_msgs/msg/Point.idl"

module localization_msgs {
  module msg {

    struct Uncertainty {
      @verbatim (language="comment", text=
          " Standard deviation of position, east/north/up in meters.")
      geometry_msgs::msg::Point position_std_dev;

      @verbatim (language="comment", text=
          " Standard deviation of quaternion qx/qy/qz, unitless.")
      sequence<geometry_msgs::msg::Point, 1> orientation_std_dev;

      @verbatim (language="comment", text=
          " Standard deviation of roll/pitch/yaw in degrees.")
      geometry_msgs::msg::Point roll_pitch_yaw_std_dev;

      @verbatim (language="comment", text=
          " Standard deviation of linear velocity, east/north/up in meters per second.")
      geometry_msgs::msg::Point linear_velocity_std_dev;

      @verbatim (language="comment", text=
          " Standard deviation of linear acceleration, right/forward/up in meters per square second.")
      sequence<geometry_msgs::msg::Point, 1> linear_acceleration_std_dev;

      @verbatim (language="comment", text=
          " Standard deviation of angular velocity, right/forward/up in radians per second.")
      sequence<geometry_msgs::msg::Point, 1> angular_velocity_std_dev;

      @verbatim (language="comment", text=
          " Error ellipsoid semi-major/semi-minor/orientation in meters/meters/degrees.")
      geometry_msgs::msg::Point error_ellipsoid;
    };
  };
};
