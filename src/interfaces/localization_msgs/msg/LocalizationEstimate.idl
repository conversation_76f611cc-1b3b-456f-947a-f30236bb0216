#include "common_msgs/msg/Header.idl"
#include "localization_msgs/msg/Dump.idl"
#include "localization_msgs/msg/Pose.idl"
#include "localization_msgs/msg/Uncertainty.idl"
#include "localization_msgs/msg/MsfStatus.idl"
#include "localization_msgs/msg/GnssStatus.idl"
#include "localization_msgs/msg/Covariance6x6.idl"

module localization_msgs {
    module msg {

        enum MeasureState {
            OK,
            WARNNING,
            ERROR,
            CRITICAL_ERROR,
            FATAL_ERROR
        };

        struct LocalizationEstimate {
            common_msgs::msg::Header header;
            localization_msgs::msg::Pose pose;
            sequence<localization_msgs::msg::Uncertainty, 1> uncertainty;

            @verbatim (language="comment", text=
                " The time of pose measurement, seconds since 1970-1-1 (UNIX time).")
            double measurement_time;

            localization_msgs::msg::MsfStatus msf_status;

            localization_msgs::msg::Covariance6x6 covariance;
            sequence<localization_msgs::msg::GnssStatus, 5> gnss_status;

            sequence<localization_msgs::msg::Dump, 5> extra_status;

            @verbatim (language="comment", text=
                " Requires to override if the value exists and it is an error")
            MeasureState lidar_localization_status;

            @verbatim (language="comment", text=
                " IMU-POS checksum error")
            boolean imu_pos_checksum_error;

            @verbatim (language="comment", text=
                " IMU status bit set by IMU")
            boolean imu_status;

            @verbatim (language="comment", text=
                " Successive IMU failures")
            boolean successive_imu_failures;

            @verbatim (language="comment", text=
                " Primary ephemeris data gap")
            boolean primary_ephemeris_data_gap;

            @verbatim (language="comment", text=
                " Primary GNSS missing ephemeris")
            boolean primary_gnss_missing_ephemeris;

            @verbatim (language="comment", text=
                " DMI failed or is offline")
            boolean dmi_failed_or_is_offline;
        };
    };
};
