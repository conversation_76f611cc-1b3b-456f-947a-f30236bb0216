#include "localization_msgs/msg/Pose.idl"
#include "localization_msgs/msg/Lane.idl"
#include "common_msgs/msg/Header.idl"
#include "geometry_msgs/msg/Point.idl"
#include "image_msgs/msg/ImageInfo.idl"

module localization_msgs {
    module msg {

        enum StatusCode {
          DEFAULT,
          NOT_SYNCED,
          TF_FAILED,
          MAP_NOT_LOADED,
          NO_ENOUGH_LANES,
          OUT_OF_MAP_BOUNDARY,
          MISSING_EGO_LANE,
          SUCCESS,
          IsNaNDetected
        };

        struct HDMapLocalizationStatus {
          common_msgs::msg::Header header;
          sequence<localization_msgs::msg::Pose, 1> output_pose;
          localization_msgs::msg::Pose input_pose;
          localization_msgs::msg::Pose camera_pose;
          sequence<localization_msgs::msg::Pose, 1> offset_pose;
          double measurement_time;
          boolean localized;
          float lane_score;
          float lane_pitch_error;
          int32 lane_match_count;
          sequence<localization_msgs::msg::Lane, 50> lines;
          float covariance[36];
          int32 lane_num;
          image_msgs::msg::ImageInfo image_info;
          StatusCode status_code;
        };
    };
};
