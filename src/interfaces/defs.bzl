"""
This module contains custom msg packages defined by T2
and declares the interfaces from Apex.Grace that are used.
"""

T2_MSGS = [
    # Apex.Grace interfaces
    "@apex//grace/interfaces/std_msgs",
    "@apex//grace/interfaces/geometry_msgs",
    "@apex//grace/interfaces/rosgraph_msgs",
    "@apex//grace/interfaces/sensor_msgs",
    # T2 specific messages
    "@ros2.geometry2//tf2_msgs",
    "//samples/t2_test_msgs",
    "//src/interfaces/common_msgs",
    "//src/interfaces/radar_msgs",
    "//src/interfaces/network_msgs",
    "//src/interfaces/image_msgs",
    "//src/interfaces/perception_msgs",
    "//src/interfaces/prediction_msgs",
    "//src/interfaces/localization_msgs",
    "//src/interfaces/planning_msgs",  # Planning module
    "//src/interfaces/control_msgs",
    "//src/drivers/lidar/hesai_msgs",  # TODO(dskkato) move to //src/interfaces
    "//src/interfaces/canbus_msgs",
    "//src/interfaces/planning_trajectory_msgs",
    "//src/interfaces/rt3000_msgs",
    "//third_party/foxglove_msgs",  # maybe move to third_party
    "//src/interfaces/v2x_msgs",
]

# List ament resources libs
INTERFACE_LIBS_AMENT_RESOURCES = [
    "//src/interfaces:{name}_ament_resources".format(name = msgs_lib.split("/")[-1])
    for msgs_lib in T2_MSGS
]

# List python interface libs
INTERFACE_LIBS_PY = [
    "//src/interfaces:{name}_py".format(name = msgs_lib.split("/")[-1])
    for msgs_lib in T2_MSGS
]
