load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("//src/interfaces:defs.bzl", "T2_MSGS")

# Actually generate the ament resources and python bindings for the interfaces
[
    ament_pkg_resources(
        name = "{name}_ament_resources".format(name = msgs_lib.split("/")[-1]),
        package = msgs_lib.split("/")[-1],
        resources = {
            msgs_lib: "rosidl_interface",
        },
        visibility = ["//visibility:public"],
    )
    for msgs_lib in T2_MSGS
]

[
    py_msgs_library(
        name = "{name}_py".format(name = msgs_lib.split("/")[-1]),
        msgs = msgs_lib,
        tags = ["manual"],
        visibility = ["//visibility:public"],
    )
    for msgs_lib in T2_MSGS
]
