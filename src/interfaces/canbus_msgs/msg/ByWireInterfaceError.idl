module canbus_msgs {
  module msg {
    struct ByWireInterfaceError {
      boolean accel_output_voltage_error;
      boolean accel_feedback_error;
      boolean brake_output_voltage_error;
      boolean brake_feedback_error;
      boolean turnsignal_output_error;
      boolean steering_feedback_error;
      boolean accel_pedal_voltage_error;
      boolean brake_pedal_voltage_error;
      boolean turnsignal_input_voltage_error;
      boolean autodrive_switch_error;
      boolean autodrive_standby_switch_error;
      boolean acc_sw_error;
      boolean acc_cancel_sw_error;
      boolean lka_sw_error;
      boolean acc_and_lka_cancel_error;
      boolean led2_output_error;
      boolean led3_output_error;
      boolean buzzer_output_error;
      boolean internal_chip_error;
      boolean watchdog_error;
      boolean low_airtank_pressure_error;
      boolean ad_system_communication_error;
      boolean ad_system_receive_error;
      boolean ad_system_send_error;
      boolean local_can1_communication_error;
      boolean local_can1_receive_error;
      boolean local_can1_send_error;
      boolean local_can2_communication_error;
      boolean local_can2_receive_error;
      boolean local_can2_send_error;
      boolean obd_can_communication_error;
      boolean obd_can_receive_error;
      boolean car_can_communication_error;
      boolean car_can_receive_error;
      boolean engine_can_communication_error;
      boolean engine_can_receive_error;
      boolean engine_control_module_error;
      boolean electric_brake_module_error;
      boolean transmission_control_module_error;
      boolean body_control_module_error;
      boolean chassis_control_module_error;
      boolean acc_acc_cancel_lka_sw_error;
      boolean error_keep_flag;
      boolean can_dac_control_error;
      boolean can_dac_receive_error;
    };
  };
};
