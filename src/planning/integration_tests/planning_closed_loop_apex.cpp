// Copyright (c) 2025 T2 Inc. All rights reserved.
#include <chrono>  // for std::chrono::milliseconds
#include <iostream>
#include <memory>  // for std::make_shared<>
#include <sstream>
#include <thread>
#include <utility>  // for std::move

#include "absl/debugging/failure_signal_handler.h"  // for absl::FailureSignalHandler
#include "apex_init/apex_init.hpp"                  // for apex::pre_init and apex::post_init
#include "common_msgs/msg/health.hpp"
#include "cpputils/common_exceptions.hpp"          // for apex::runtime_error
#include "executor2/executor_factory.hpp"          // for apex::executor::executor_factory
#include "executor2/executor_runner.hpp"           // for apex::executor::executor_runner
#include "interrupt/interrupt_handler.hpp"         // for apex::interrupt_handler
#include "planning_hdmap_utils.hpp"                // GetLanePosition*
#include "planning_test_input.hpp"                 // PlanningTestInput
#include "planning_test_simulation_component.hpp"  // PlanningTestSimulationComponent
#include "scenario_file.hpp"                       // ScenarioFile
#include "src/common/health/health_reporter.hpp"
#include "src/common/runfiles/runtime_dependency_manager.hpp"  // RuntimeDependencyManager
#include "src/planning/dummy_component/dummy_component.hpp"    // PlanningDummyComponent
#include "src/planning/planning_component.hpp"                 // PlanningComponent
#include "src/planning/planning_module_exception.hpp"          // PlanningModuleException
#include "timer.hpp"                                           // Timer
#include "timer_service/clock_timer_service.hpp"

namespace t2_planning = ::t2::planning;
namespace common = ::t2::common;
namespace test = t2_planning::test;
namespace hdmap = ::t2::map::hdmap;

using common::runfiles::RuntimeDependencyManager;

int main(const int argc, char** const argv) {
  T2_INFO << "argc=" << argc;
  for (int i = 0; i < argc; ++i) {
    T2_INFO << "argv[" << i << "] = " << argv[i];
  }

  ScenarioFile scenario_file;
  T2_INFO << t2_planning::ToJsonString(scenario_file);
  test::FromFile(scenario_file,
                 RuntimeDependencyManager::ResolvePath(
                     "src/planning/integration_tests/planning_test_input_filename.json"));

  auto planning_test_input_filename =
      RuntimeDependencyManager::ResolvePath(scenario_file.planning_test_input_filename);

  // "closed_loop_scenarios/JARI/Shukairo-clockwise-outer-loop.json"

  // Install the failure signal handler
  absl::InstallFailureSignalHandler(absl::FailureSignalHandlerOptions());

  int ret = 0;

  try {
    auto scoped_init = apex::scoped_init(argc, argv, false);

    const apex::interrupt_handler::installer interrupt_handler_installer{};

    const auto executor = ::apex::executor::executor_factory::create();
    std::shared_ptr<t2::common::health::HealthReporter> health_reporter_ptr =
        t2::common::health::HealthReporter::Create("test_planning_closed_loop_apex", *executor);

    scoped_init.post_init();

    auto planning_test_simulation_component =
        std::make_shared<test::PlanningTestSimulationComponent>(
            planning_test_input_filename);  ///< also set up the map here

    auto planning_component = std::make_shared<t2_planning::PlanningComponent>(
        health_reporter_ptr);  ///< use the set up map

    auto& planning_test_input = planning_test_simulation_component->planning_test_input;

    using std::chrono_literals::operator""s;

    const apex::executor::executor_runner runner{apex::executor::executor_runner::deferred,
                                                 *executor};

    const size_t stepsize = planning_test_input.stepsize;  ///< teleport
    auto& stop_position = planning_test_input.stop_position;

    // Wait at least rosbag record starts.
    planning_component->WaitForMatched(1, 5s);
    T2_INFO << "PlanningComponent is ready to process messages";

    auto& i_run = planning_test_simulation_component->i_run;
    t2_planning::ADCTrajectory adc_trajectory;

    for (; i_run < planning_test_input.num_runs; ++i_run) {
      bool run_cycle = !(i_run % stepsize);
      if (run_cycle) {
        if (i_run == 0) {
          auto [prediction_obstacles, chassis, localization_estimate] =
              planning_test_simulation_component->GeneratePlanningComponentInput();
          adc_trajectory =
              planning_component->ProcHelper(prediction_obstacles, chassis, localization_estimate);
        } else if (i_run < planning_test_input.num_runs) {
          if (run_cycle) {
            // normal situation, where there is no stop_position, or
            // stop_position is not close
            planning_test_simulation_component->UpdatePlanningInputFromTrajectory(adc_trajectory);
            auto [prediction_obstacles, chassis, localization_estimate] =
                planning_test_simulation_component->GeneratePlanningComponentInput();
            adc_trajectory = planning_component->ProcHelper(prediction_obstacles, chassis,
                                                            localization_estimate);
            // std::this_thread::sleep_for(10ms);
          }
        }  // i_run == 0
      }  // run_cycle for teleport
    }  // for i_run

  } catch (const std::exception& e) {
    if (rclcpp::ok()) {
      APEX_FATAL_R(e.what());
    } else {
      std::cerr << e.what() << "\n";
    }
    ret = 2;
  } catch (...) {
    if (rclcpp::ok()) {
      APEX_FATAL_R("Unknown error occurred");
    } else {
      std::cerr << "Unknown error occurred" << "\n";
    }
    ret = -1;
  }
  return ret;
}
