// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <coin/IpIpoptApplication.hpp>  // Ipopt::IpoptApplication

#include "speed_planner_nlp.hpp"                           // SpeedPlannerNLP
#include "src/planning/common/intention_task_data.hpp"     // IntentionTaskData
#include "src/planning/config/planner_config.hpp"          // PlannerConfig
#include "src/planning/reference_line/reference_line.hpp"  // ReferenceLineAndRouteSegments

namespace t2::planning {

class SpeedPlanner {
 public:
  explicit SpeedPlanner(const PlannerConfig& config);

  common::Status ExecuteTask(const ReferenceLineMap& reference_line_map,
                             IntentionTaskData& intention_task_data);

 private:
  // post process solution if ipopt succeeds
  void PostProcess(IntentionTaskData& intention_task_data, const SVAProfile& sva_profile);

  // generate a braking-trajectory
  void GenerateBrakingTrajectory(SVAProfile& sva_profile, TrajectoryEvaluationInfo& traj_info,
                                 const double pfs_brake) const;

  void GenerateConstantVelocityTrajectory(SVAProfile& sva_profile,
                                          TrajectoryEvaluationInfo& traj_info) const;

  const PlannerConfig& config_;                   ///< referernce of Planner::planner_config
  Ipopt::SmartPtr<Ipopt::IpoptApplication> app_;  ///< Ipopt application
  Ipopt::SmartPtr<SpeedPlannerNLP> nlp_;          ///< NLP problem

  std::mutex mutex_;  ///< allow the solver to solve with one thread at a time
};

// update speed_data
void UpdateSpeedData(SpeedData& speed_data, const SVAProfile& sva_profile, const double dt);

}  // namespace t2::planning
