// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "src/planning/planner/postprocessing/postprocessing.hpp"

#include <cmath>
#include <memory>

#include <Eigen/Dense>

#include <planning_trajectory_msgs/msg/path_point.hpp>        // PathPoint
#include <planning_trajectory_msgs/msg/speed_point.hpp>       // SpeedPoint
#include <planning_trajectory_msgs/msg/trajectory_point.hpp>  // TrajectoryPoint

#include "src/common/math/cartesian_frenet_conversion.hpp"  // CartesianFrenetConverter
#include "src/planning/common/path/frenet_frame_path.hpp"   // FrenetFramePath
#include "src/planning/common/trajectory1d/piecewise_jerk_trajectory1d.hpp"  // PiecewiseJerkTrajectory1d
#include "src/planning/config/miscellaneous_config.hpp"                      // MiscellaneousConfig
#include "src/planning/planning_module_exception.hpp"  // PlanningModuleException

namespace t2::planning {

REGISTER_INTER_TRAJECTORY_MSG(PathPoint);
REGISTER_INTER_TRAJECTORY_MSG(TrajectoryPoint);
REGISTER_INTER_TRAJECTORY_MSG(SpeedPoint);

/**
 * @brief Get two trajectories with different resolutions from the path and
 * the speed profile. One is discretized by distance, and the other is by
 * time. The size of the discretized trajectories by distance can be zero. In
 * that case, we use the discretized trajectories by time instead.
 *
 * @param[in] relative_time The relative time.
 * @param[in] start_s The start s.
 * @param[in] discretized_path A discretized path.
 * @param[in] speed_data The speed data.
 * @return Tuple consisting of the result, the
 * trajectory discretized by distance, and that by time.
 */

std::pair<bool, std::vector<TrajectoryPoint>> CombinePathAndSpeedProfile(
    const double relative_time, const double start_s, const DiscretizedPath& discretized_path,
    const SpeedData& speed_data, const PlannerConfig& planner_config) {
  std::pair<bool, std::vector<TrajectoryPoint>> result{false, {}};
  auto& [success, trajectory] = result;
  if (discretized_path.path_points.empty() || speed_data.speed_points.empty()) {
    T2_ERROR << "path data is empty";
    return result;
  }

  // use varied resolution to reduce data load but also provide enough data
  // point for control module
  const double dt = planner_config.dt;  // 0.1 s
  const double path_length = discretized_path.Length();
  const double total_time = speed_data.TotalTime();

  std::vector<double> xs;
  std::vector<double> ys;

  // Generate a combined trajectory discretized by time.
  for (double cur_rel_time = 0.0; cur_rel_time < total_time; cur_rel_time += dt) {
    const std::optional<SpeedPoint> opt_speed_point = speed_data.EvaluateByTime(cur_rel_time);
    if (!opt_speed_point) {
      T2_ERROR << "Fail to get speed point with relative time " << cur_rel_time << "; break";
      return result;
    }

    const auto& speed_point = opt_speed_point.value();
    if (speed_point.s > path_length) {
      T2_WARN << "cur_rel_time=" << cur_rel_time << ", total_time=" << total_time
              << ", speed_point.s()=" << speed_point.s << ">" << path_length << "; break";
      break;
    }

    PathPoint path_point =
        discretized_path.Evaluate(speed_point.s);  // Retrieves the interpolated point
    path_point.s = path_point.s + start_s;

    xs.push_back(path_point.x);
    ys.push_back(path_point.y);

    TrajectoryPoint trajectory_point;
    trajectory_point.path_point = path_point;
    trajectory_point.v = speed_point.v;
    trajectory_point.a = speed_point.a;
    trajectory_point.relative_time = speed_point.t + relative_time;  // WHY??????
    if (!trajectory.empty()) {
      CHECK_GT(trajectory_point.relative_time, trajectory.back().relative_time);
    }
    trajectory.push_back(trajectory_point);
  }
  success = true;
  return result;
}

/*

S_i(x) = a_i + b_i * (x-x_i) + c_i * (x-x_i)^2 + d_i * (x-x_i)^3

*/

Eigen::VectorXd FitNaturalCubicSpline(const std::vector<double>& y,
                                      double fitting_interval_length = 1.0) {
  const size_t n = y.size();

  // Set up the matrix A
  Eigen::MatrixXd A = Eigen::MatrixXd::Zero(n, n);
  A(0, 0) = 1.0;          // Natural spline boundary condition at the first point
  A(n - 1, n - 1) = 1.0;  // Natural spline boundary condition at the last point

  // Set up the right-hand side vector b
  Eigen::VectorXd rhs = Eigen::VectorXd::Zero(n);

  // Fill the matrix A and vector b for the cubic spline conditions
  for (size_t i = 1; i < n - 1; ++i) {
    A(i, i - 1) = fitting_interval_length;
    A(i, i) = 2.0 * (2.0 * fitting_interval_length);
    A(i, i + 1) = fitting_interval_length;
    rhs(i) = 3.0 * ((y[i + 1] - y[i]) / fitting_interval_length -
                    (y[i] - y[i - 1]) / fitting_interval_length);
  }

  // Solve for the spline coefficients (c)
  Eigen::VectorXd x = A.colPivHouseholderQr().solve(rhs);
  return x;
}

double CalculateCurvatureBySolvingCubicSpline(const std::vector<double>& x,
                                              const std::vector<double>& y, double s,
                                              double fitting_interval_length = 1.0) {
  const size_t segment_index =
      std::floor(s / fitting_interval_length);  // which piece-wise cubic segment to evaluate?
  const double local_s = std::fmod(s, fitting_interval_length);  // s value for selected segment

  Eigen::VectorXd coeffsX = FitNaturalCubicSpline(x, fitting_interval_length);
  Eigen::VectorXd coeffsY = FitNaturalCubicSpline(y, fitting_interval_length);

  // x-coeffs
  double ax, bx, cx, dx;
  ax = x[segment_index];
  bx = coeffsX[segment_index];
  cx = 3 * (x[segment_index + 1] - x[segment_index]) - 2 * coeffsX[segment_index] -
       coeffsX[segment_index + 1];
  dx = 2 * (x[segment_index] - x[segment_index + 1] + coeffsX[segment_index] +
            coeffsX[segment_index + 1]);

  // y-coeffs
  double ay, by, cy, dy;
  ay = y[segment_index];
  by = coeffsY[segment_index];
  cy = 3 * (y[segment_index + 1] - y[segment_index]) - 2 * coeffsY[segment_index] -
       coeffsY[segment_index + 1];
  dy = 2 * (y[segment_index] - y[segment_index + 1] + coeffsY[segment_index] +
            coeffsY[segment_index + 1]);

  // x' and x''
  double xd = bx + 2 * cx * local_s + 3 * dx * local_s * local_s;
  double xdd = 2 * cx + 6 * dx * local_s;

  // y' and y''
  double yd = by + 2 * cy * local_s + 3 * dy * local_s * local_s;
  double ydd = 2 * cy + 6 * dy * local_s;

  // Compute Curvature (Kappa)
  return (xd * ydd - yd * xdd) / std::pow((xd * xd + yd * yd), 1.5);
}

std::pair<double, std::vector<double>> GetLatVelAndAccBySolvingCubicSpline(
    const std::vector<double>& y, double delta_s) {
  // Solve for the spline coefficients (c)
  Eigen::VectorXd x = FitNaturalCubicSpline(y, delta_s);

  std::vector<double> opt_ddl(y.size(), 0.0);
  for (size_t i = 0; i < y.size(); ++i) {
    opt_ddl[i] = 2.0 * x(i);
  }
  double opt_dl_0 = (y[1] - y[0]) / delta_s - delta_s * (2.0 * x(0) + x(1)) / 3.0;
  return {opt_dl_0, opt_ddl};
}

PathOptimizer::PathOptimizer(const PlannerConfig& config) : config_(config) {}

common::Status PathOptimizer::ExecuteTask(const ReferenceLineMap& reference_line_map,
                                          IntentionTaskData& intention_task_data) {
  const ReferenceLine& reference_line =
      GetSuitableReferenceLine(intention_task_data.is_lane_change, reference_line_map);
  const auto& planning_start_point = intention_task_data.planning_start_point;

  // EASY_BLOCK("PiecewiseJerkPathOptimizer");

  const auto [s_condition, _] = reference_line.ToFrenetFrame(planning_start_point);
  const double ego_s = s_condition[0];
  const double delta_s = config_.get_suitable_longitudinal_step_size(planning_start_point.v);

  const std::vector<double>& opt_l = intention_task_data.path_reference_l;
  const auto [opt_dl_0, opt_ddl] = GetLatVelAndAccBySolvingCubicSpline(opt_l, delta_s);

  // We don't use opt_dddl in the FrenetPath calculation, because it makes
  // unstable trajectory (Shiroshita guess it is due to the floating point
  // error).

  PiecewiseJerkTrajectory1d piecewise_jerk_traj(opt_l[0], opt_dl_0, opt_ddl[0]);

  for (std::size_t i = 1; i < opt_l.size(); ++i) {
    const auto dddl = (opt_ddl[i] - opt_ddl[i - 1]) / delta_s;
    piecewise_jerk_traj.AppendSegment(dddl, delta_s);
  }

  FrenetFramePath frenet_path;
  std::vector<FrenetFramePoint>& frenet_frame_points = frenet_path.frenet_frame_points;
  const double traj_delta_s = MiscellaneousConfig::getConfig().trajectory_space_resolution;

  const double param_length = piecewise_jerk_traj.ParamLength();
  for (double accumulated_s = 0.0; accumulated_s < param_length; accumulated_s += traj_delta_s) {
    ///< l, dl, ddl to calculate curvature
    const auto [l, dl, ddl] = piecewise_jerk_traj.EvaluateAll(accumulated_s);

    frenet_frame_points.emplace_back();
    FrenetFramePoint& frenet_frame_point = frenet_frame_points.back();  // s, l, dl, ddl
    frenet_frame_point.s = accumulated_s + ego_s;
    frenet_frame_point.l = l;
    frenet_frame_point.dl = dl;

    ///< While lane change, calculate correct curvature
    ///< Otherwise, set 0 (to avoid the degredation) to filter out the the jaggy
    ///< curvature data Now the trajectory planner improved and we can use
    ///< calcuated ddl
    frenet_frame_point.ddl =
        (intention_task_data.lane_change_state == LaneChangeState::LANE_CHANGING) ? ddl : 0.0;
  }  // for (double accumulated_s)

  ///< While lane change, calculate correct curvature
  if (intention_task_data.lane_change_state == LaneChangeState::LANE_CHANGING &&
      frenet_frame_points.size() > 4) {
    ///< Use average of dl of subsequent points since point[0] has no previous
    ///< points
    frenet_frame_points[0].dl =
        (frenet_frame_points[1].dl + frenet_frame_points[2].dl + frenet_frame_points[3].dl) / 3.0;

    ///< Use average of ddl of subsequent points since point[0:1] has no
    ///< previous points
    frenet_frame_points[0].ddl = frenet_frame_points[1].ddl =
        (frenet_frame_points[2].ddl + frenet_frame_points[3].ddl + frenet_frame_points[4].ddl) /
        3.0;
  }

  // ---------- SL to XY conversion --------------------
  std::vector<PathPoint> path_points;
  common::math::Vec2d last;

  for (const FrenetFramePoint& frenet_point : frenet_frame_points) {
    SLPoint sl_point;
    sl_point.s = frenet_point.s;
    sl_point.l = frenet_point.l;
    std::optional<common::math::Vec2d> opt_cartesian_point = reference_line.SLToXY(sl_point);

    T2_PLAN_CHECK(opt_cartesian_point) << "s=" << frenet_point.s << ", l=" << frenet_point.l;

    const auto& cartesian_point = opt_cartesian_point.value();
    const auto& ref_point = reference_line.GetMapPathPoint(frenet_point.s);
    const double theta = common::math::CartesianFrenetConverter::CalculateTheta(
        ref_point.heading(), ref_point.kappa(), frenet_point.l, frenet_point.dl);
    const double kappa = common::math::CartesianFrenetConverter::CalculateKappa(
        ref_point.kappa(), ref_point.dkappa(), frenet_point.l, frenet_point.dl, frenet_point.ddl);

    double s = 0.0;
    double dkappa = 0.0;
    if (!path_points.empty()) {
      const double distance = (last - cartesian_point).Length();
      const auto& last_path_point = path_points.back();
      s = last_path_point.s + distance;  ///< s here is the distance
                                         ///< travelled, not the longitudinal
      dkappa = (kappa - last_path_point.kappa) / distance;
    }

    path_points.emplace_back();
    PathPoint& path_point = path_points.back();
    path_point.x = cartesian_point.x();
    path_point.y = cartesian_point.y();
    path_point.z = 0.0;
    path_point.theta = theta;
    path_point.kappa = kappa;
    path_point.s = s;
    path_point.dkappa = dkappa;

    last = cartesian_point;
  }

  if (frenet_frame_points.size() >= 2) {
    path_points[0].dkappa = path_points[1].dkappa;  ///< extend dkappa forward
  }

  // hence the `s` in path_points is the travelled distance
  const DiscretizedPath discretized_path(path_points);

  // Check path and speed results for path or speed fallback
  // If no solution has been found we create a fallback trajectory which stops
  // as soon as possible
  intention_task_data.trajectory_type = TrajectoryType::NORMAL;

  std::pair<bool, std::vector<TrajectoryPoint>> trajectory = CombinePathAndSpeedProfile(
      planning_start_point.relative_time, planning_start_point.path_point.s, discretized_path,
      intention_task_data.speed_data, config_);
  if (!trajectory.first) {
    const std::string msg = "Fail to aggregate planning trajectory.";
    T2_ERROR << msg;
    intention_task_data.is_drivable = false;
    return common::Status(common::ErrorCode::PLANNING_ERROR, msg);
  }
  intention_task_data.discretized_trajectory = trajectory.second;
  intention_task_data.is_drivable = true;

  return common::Status::OK();
}
}  // namespace t2::planning
