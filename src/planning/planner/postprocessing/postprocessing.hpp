// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <memory>

#include "src/planning/common/intention_task_data.hpp"     // IntentionTaskData
#include "src/planning/config/planner_config.hpp"          // PlannerConfig
#include "src/planning/reference_line/reference_line.hpp"  // ReferenceLineAndRouteSegments

namespace t2::planning {

class PathOptimizer {
 public:
  explicit PathOptimizer(const PlannerConfig& config);
  virtual ~PathOptimizer() = default;
  common::Status ExecuteTask(const ReferenceLineMap& reference_line_map,
                             IntentionTaskData& intention_task_data);
  const PlannerConfig& config_;  ///< referernce of Planner::planner_config
};

}  // namespace t2::planning
