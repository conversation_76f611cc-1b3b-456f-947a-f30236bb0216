// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>
#include <tuple>
#include <unordered_map>
#include <utility>
#include <vector>

#include "src/planning/common/intention_task_data.hpp"             // IntentionTaskData
#include "src/planning/config/miscellaneous_config.hpp"            // MiscellaneousConfig
#include "src/planning/config/planner_config.hpp"                  // PlannerConfig
#include "src/planning/planner/path_planner/path_planner_nlp.hpp"  // PathPlannerNLP
#include "src/planning/reference_line/reference_line.hpp"          // ReferenceLineAndRouteSegments

namespace t2::planning {

class PathPlanner {
 public:
  explicit PathPlanner(const PlannerConfig& config);

  /** @brief Every time when Process function is called, it will:
   *   1. Initialize.
   *   2. Generate NoObjectAvoid Path Bound.
   *   3. Generate Regular Path Bound(s).
   */
  common::Status ExecuteTask(const ReferenceLineMap& reference_line_map,
                             IntentionTaskData& intention_task_data);

  std::vector<double> ComputeLateralValues(const std::vector<PathBoundPoint>& path_bound,
                                           const double offset_to_target_lane);

 private:
  double buffer_ = 0;   ///< vehicle's half width
  double delta_s_ = 0;  ///< stepsize of discretization of longitudinal distance

  const PlannerConfig& config_;                   ///< referernce of Planner::planner_config
  Ipopt::SmartPtr<Ipopt::IpoptApplication> app_;  ///< Ipopt application
  Ipopt::SmartPtr<PathPlannerNLP> nlp_;           ///< NLP problem
};

}  // namespace t2::planning
