// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "src/planning/planner/path_planner/path_planner.hpp"

#include <algorithm>
#include <functional>
#include <limits>
#include <memory>
#include <set>

#include <planning_msgs/msg/planning_module_status.hpp>  // PlanningModuleState

#include "src/common/config/vehicle_config_helper.hpp"
#include "src/common/math/box2d.hpp"                  // Box2d
#include "src/planning/planner/path_bound_point.hpp"  // PathBoundPoint
#include "src/planning/planner/planner_common.hpp"    // GenerateSmoothPath

namespace t2::planning {

using PlanningModuleState = planning_msgs::msg::PlanningModuleState;

// using apollo::common::ErrorCode;
// using apollo::common::Status;
using ::t2::common::config::VehicleConfigHelper;
using ::t2::common::math::Box2d;
// using apollo::hdmap::HDMapUtil;
// using apollo::hdmap::JunctionInfo;
const bool print_log = MiscellaneousConfig::getConfig().print_log;

PathPlanner::PathPlanner(const PlannerConfig& config)
    : config_(config), app_(IpoptApplicationFactory()), nlp_(new PathPlannerNLP(app_, config)) {
  const auto& vehicle_config = VehicleConfigHelper::GetInstance().GetConfig();
  buffer_ = vehicle_config.vehicle_param().width() / 2.0;
}

common::Status PathPlanner::ExecuteTask(const ReferenceLineMap& reference_line_map,
                                        IntentionTaskData& intention_task_data) {
  // EASY_BLOCK("PathPlanner");

  std::vector<double>& path_reference_l =
      intention_task_data.path_reference_l;  ///< output of PathPlanner

  // Initialization.
  const auto& planning_start_point = intention_task_data.planning_start_point;

  nlp_->delta_s = delta_s_ = config_.get_suitable_longitudinal_step_size(planning_start_point.v);
  T2_INFO << "PathPlanner samples longitudinal distance by delta_s=" << delta_s_
          << ", planning_start_point=" << rosidl_generator_traits::to_yaml(planning_start_point);

  const ReferenceLine& reference_line =
      GetSuitableReferenceLine(intention_task_data.is_lane_change, reference_line_map);

  const size_t num_points = config_.path_planner_config.num_steps + 1;

  // If it's a lane-change reference-line, generate lane-change path boundary.
  const bool is_lane_change = intention_task_data.is_lane_change;
  const auto [sl_point, heading] =
      GetSLAndHeadingToReferenceLine(planning_start_point, reference_line);

  auto& lane_change_information = intention_task_data.lane_change_information;

  const double tan_heading = tan(heading);
  const auto& path_planner_config = config_.path_planner_config;
  const double l0 = sl_point.l;

  PlanningModuleState planning_module_state = intention_task_data.planning_module_status.state;
  T2_INFO << "planning_module_status=" << static_cast<int>(planning_module_state);
  double l1 = l0;
  if (planning_module_state == PlanningModuleState::LANE_FOLLOW) {
    if (l0 * heading < 0) {
      l1 += delta_s_ * tan_heading;  ///< as tan(θ) = Δl/Δs = (l1-l0) / delta_s
    }
    if (l0 * l1 < 0) {
      l1 = 0;
    }
  } else {
    l1 += delta_s_ * tan_heading;  ///< as tan(θ) = Δl/Δs = (l1-l0) / delta_s
  }

  std::vector<PathBoundPoint> path_bound(num_points);

  const auto& vehicle_config = VehicleConfigHelper::GetInstance().GetConfig();
  const double half_ego_width = 0.5 * vehicle_config.vehicle_param().width();

  const auto [s_condition, _] = reference_line.ToFrenetFrame(planning_start_point);
  const double ego_s = s_condition[0];

  if (is_lane_change) {
    // Lane Change
    T2_PLAN_CHECK(reference_line_map.count(ReferenceLineType::LANE_CHANGE_SOURCE));

    auto lane_change_progress = intention_task_data.lane_change_progress;
    if (!intention_task_data.is_lane_changeable) {
      const double cancelation_threshold =
          0.5;  // If half of the truck has entered target lane, then we cannot cancel.
                // TODO: hard code for now, maybe parameterize in future.
      if (lane_change_progress < cancelation_threshold) {
        const std::string msg = "Lane changeability is violated according. lane_change_progress=" +
                                std::to_string(lane_change_progress) + "<" +
                                std::to_string(cancelation_threshold);
        T2_WARN << msg;
        return {common::Status(common::ErrorCode::PLANNING_ERROR, msg)};
      }
    }

    const ReferenceLine& source_reference_line =
        reference_line_map.at(ReferenceLineType::LANE_CHANGE_SOURCE).reference_line;
    const ReferenceLine& target_reference_line =
        reference_line_map.at(ReferenceLineType::LANE_CHANGE_TARGET).reference_line;
    const double offset_to_target_lane = intention_task_data.offset_to_adc_lane;

    for (size_t i = 0; i < num_points; ++i) {
      auto& path_point = path_bound[i];
      path_point.s = ego_s + i * delta_s_;
      const auto [source_lane_left_width, source_lane_right_width] =
          source_reference_line.GetLaneWidth(path_point.s);
      const auto [target_lane_left_width, target_lane_right_width] =
          target_reference_line.GetLaneWidth(path_point.s);
      if (offset_to_target_lane > 0) {
        // LC to right
        path_point.l_max =
            (target_lane_left_width + source_lane_right_width + source_lane_left_width) -
            half_ego_width;
        path_point.l_min = -(target_lane_right_width) + half_ego_width;
      } else {
        // LC to left
        path_point.l_max = (target_lane_left_width)-half_ego_width;
        path_point.l_min =
            -(target_lane_right_width + source_lane_left_width + source_lane_right_width) +
            half_ego_width;
      }
    }

    if (l0 * l1 < 0) {
      l1 = 0;
    }
    nlp_->init_lateral_values(l0, l1);

    T2_PLAN_CHECK(intention_task_data.lane_change_state != LaneChangeState::INVALID);

    const LaneChangeState curr_lane_change_state = intention_task_data.lane_change_state;

    if (curr_lane_change_state == LaneChangeState::TO_CHANGE_NO_WINKER) {
      path_reference_l = std::vector<double>(num_points, offset_to_target_lane);
    } else {
      nlp_->lane_change_direction = intention_task_data.lane_change_direction;
      path_reference_l = GenerateSmoothPath(app_, nlp_, path_bound, 0,
                                            path_planner_config.path_planner_nlp_options);
    }

  } else {
    // Lane Follow
    const auto [lane_left_width, lane_right_width] =
        reference_line_map.at(ReferenceLineType::CURRENT).reference_line.GetLaneWidth(ego_s);

    switch (planning_module_state) {
      case PlanningModuleState::LANE_CHANGE:  // somehow it's
                                              // still LC
      case PlanningModuleState::LANE_CHANGE_CANCEL: {
        for (size_t i = 0; i < num_points; ++i) {
          auto& path_point = path_bound[i];
          path_point.l_max = lane_left_width;
          path_point.l_min = -lane_right_width;
          path_point.s = ego_s + i * delta_s_;
        }
        break;
      }
      default: {
        for (size_t i = 0; i < num_points; ++i) {
          auto& path_point = path_bound[i];
          path_point.l_max = lane_left_width - half_ego_width;
          path_point.l_min = -lane_right_width + half_ego_width;
          path_point.s = ego_s + i * delta_s_;
        }
      }
    }

    nlp_->init_lateral_values(l0, l1);

    const double offset_to_target_lane = 0.0;
    nlp_->lane_change_direction = intention_task_data.lane_change_direction;
    path_reference_l = GenerateSmoothPath(app_, nlp_, path_bound, offset_to_target_lane,
                                          path_planner_config.path_planner_nlp_options);
  }

  const double tan_heading0 = (path_reference_l[1] - path_reference_l[0]) / delta_s_;
  const double max_change_heading = GetMaxChangeOfHeading(path_reference_l, delta_s_);

  // collect PathPlannerNLPData
  lane_change_information.lane_changeable = is_lane_change;
  lane_change_information.theta0 = heading;
  lane_change_information.theta0_error = atan(tan_heading0) - heading;
  lane_change_information.l0 = sl_point.l;
  lane_change_information.l0_error = path_reference_l[0] - sl_point.l;
  lane_change_information.max_change_heading = max_change_heading;
  lane_change_information.lon.resize(path_bound.size());
  for (size_t i = 0; i < path_bound.size(); ++i) {
    lane_change_information.lon[i] = path_bound[i].s;
  }
  lane_change_information.lat = path_reference_l;

  // EASY_END_BLOCK;
  return common::Status::OK();
}

std::vector<double> PathPlanner::ComputeLateralValues(const std::vector<PathBoundPoint>& path_bound,
                                                      const double offset_to_target_lane) {
  return GenerateSmoothPath(app_, nlp_, path_bound, offset_to_target_lane,
                            config_.path_planner_config.path_planner_nlp_options);
}

}  // namespace t2::planning
