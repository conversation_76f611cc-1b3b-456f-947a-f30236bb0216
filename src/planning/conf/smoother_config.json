{"smoother_config": {"max_constraint_interval": 0.25, "longitudinal_boundary_bound": 0.2, "max_lateral_boundary_bound": 0.1, "min_lateral_boundary_bound": 0.01, "num_of_total_points": 500, "curb_shift": 0.2, "lateral_buffer": 0.2, "resolution": 0.02, "fem_pos_deviation_smoothing": {"weight_fem_pos_deviation": 10000000000.0, "weight_ref_deviation": 1.0, "weight_path_length": 1.0, "max_iter": 500, "time_limit": 0.0, "verbose": false, "scaled_termination": true, "warm_start": true}, "use_ipopt": false, "ipopt_options": {"tol": 0.01, "max_iter": 25, "constr_viol_tol": 1e-05, "linear_solver": "pardisomkl", "approximate_hessian": false, "print_level": 0}}}