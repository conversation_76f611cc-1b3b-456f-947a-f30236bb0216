#pragma once

#include <functional>
#include <memory>
#include <string>

#include "ast.hpp"

class ExpressionParser {
 private:
  std::unique_ptr<ASTNode> ast_root;
  std::string original_expression;

 public:
  // Parse an expression and create a callable function
  static std::function<double(double)> parse_expression(const std::string& expression);

  // Constructor for creating parser instance
  explicit ExpressionParser(const std::string& expression);

  // Evaluate the expression with given x value
  double evaluate(double x) const;

  // Get string representation of the parsed expression
  std::string to_string() const;

  // Get the original expression string
  std::string get_original_expression() const;

  // Create a callable function from this parser
  std::function<double(double)> create_function() const;
};
