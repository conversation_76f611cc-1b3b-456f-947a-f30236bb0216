#pragma once

#include <memory>
#include <vector>

#include "ast.hpp"
#include "tokenizer.hpp"

class Parser {
 private:
  std::vector<Token> tokens;
  size_t current_token_index;

  Token& current_token();
  Token& peek_token();
  void advance();
  bool match(TokenType type);
  bool match(TokenType type, const std::string& value);
  void consume(TokenType type, const std::string& error_message);

  // Grammar rules (recursive descent parser)
  std::unique_ptr<ASTNode> parse_expression();
  std::unique_ptr<ASTNode> parse_term();
  std::unique_ptr<ASTNode> parse_factor();
  std::unique_ptr<ASTNode> parse_power();
  std::unique_ptr<ASTNode> parse_unary();
  std::unique_ptr<ASTNode> parse_primary();

  // Handle implicit multiplication
  std::vector<Token> preprocess_tokens(const std::vector<Token>& input_tokens);

 public:
  explicit Parser(const std::vector<Token>& token_list);
  std::unique_ptr<ASTNode> parse();
};
