#pragma once

#include <planning_msgs/msg/adc_trajectory.hpp>  // ADCTrajectory

#include "src/planning/common/json_string.hpp"
#include "src/planning/config/planner_config.hpp"                  // PlannerConfig
#include "src/planning/planner/path_planner/path_planner.hpp"      // PathPlanner
#include "src/planning/planner/postprocessing/postprocessing.hpp"  // PathOptimizer
#include "src/planning/planner/speed_planner/speed_planner.hpp"    // SpeedPlanner
#include "src/planning/planning_dir_prefix.hpp"                    // FULL_PLANNING_DIR_PREFIX
#include "src/planning/planning_macros.hpp"                        // REGISTER_*_PLANNING_MSG
#include "src/planning/planning_on_local_map_component/internal_data_definition.hpp"
#include "src/planning/planning_on_local_map_component/trajectory_planner.hpp"
#include "src/planning/reference_line/reference_line.hpp"           // ReferenceLine
#include "src/planning/reference_line/reference_line_provider.hpp"  // ReferenceLineProvider

namespace t2::planning {

REGISTER_INTER_PLANNING_MSG(ADCTrajectory);

class FrenetPlanner : public TrajectoryPlannerBase {
 public:
  explicit FrenetPlanner(PlannerConfig& planner_config);
  ADCTrajectory Plan(const PlanningVehicleState& ego_state,
                     const std::map<int, Obstacle>& obstacles,
                     const RoadInformation& road_information,
                     const PlannerConstraint& planner_constraint) override;

 private:
  PlannerConfig planner_config_;
  std::shared_ptr<ReferenceLineProvider> reference_line_provider_;
  std::shared_ptr<PathPlanner> path_planner_;
  std::shared_ptr<SpeedPlanner> speed_planner_;
  std::shared_ptr<PathOptimizer> path_optimizer_;
};

}  // namespace t2::planning
