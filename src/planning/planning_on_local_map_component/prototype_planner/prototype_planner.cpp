// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "prototype_planner.hpp"

#include <casadi/casadi.hpp>

using casadi::DM;
using casadi::Function;
using casadi::interpolant;
using casadi::MX;
using casadi::Opti;
using casadi::OptiSol;

namespace t2::planning {

using VectorPairDouble = std::vector<std::pair<double, double>>;

std::pair<std::vector<double>, std::vector<double>> get_xy_pts(
    const VectorPairDouble& center_line) {
  std::pair<std::vector<double>, std::vector<double>> ret;
  auto& [x_pts, y_pts] = ret;

  const size_t n_points = center_line.size();
  x_pts.resize(n_points);
  y_pts.resize(n_points);

  for (size_t i = 0; i < n_points; ++i) {
    std::tie(x_pts[i], y_pts[i]) = center_line[i];
  }

  return ret;
}

std::vector<double> dm_to_vector(const casadi::DM& dm) {
  std::vector<double> vec(dm.nnz());
  std::copy(dm.nonzeros().begin(), dm.nonzeros().end(), vec.begin());
  return vec;
  // return dm.nonzeros();
}

CartesianTrajectory transform_trajectory_local_to_global(
    const CartesianTrajectory& local_trajectory, double x, double y, double theta) {
  CartesianTrajectory global_trajectory = local_trajectory;
  for (size_t i = 0; i < global_trajectory.x_sol.size(); i++) {
    global_trajectory.x_sol[i] =
        cos(theta) * local_trajectory.x_sol[i] - sin(theta) * local_trajectory.y_sol[i] + x;
    global_trajectory.y_sol[i] =
        sin(theta) * local_trajectory.x_sol[i] + cos(theta) * local_trajectory.y_sol[i] + y;
  }
  return global_trajectory;
}

CartesianTrajectory ComputePlan(const Vehicle& vehicle, const VectorPairDouble& center_line,
                                std::optional<CartesianTrajectory> init_traj, Params params) {
  int N = params.N;
  double dt = params.dt;

  const double s_min = 0.0;
  const double s_max = 80.0 / 3.6 * (N * dt) * 1.40;  // we multiply the length by a 1.2 factor

  auto [x_pts, y_pts] = get_xy_pts(center_line);

  std::vector<double> ds = hypot_vector(diff(x_pts), diff(y_pts));
  std::vector<double> s_pts = cumsum(ds);

  Function x_s = interpolant("x_s", "bspline", {s_pts}, x_pts);
  Function y_s = interpolant("y_s", "bspline", {s_pts}, y_pts);
  MX s_sym = MX::sym("ss");
  MX x_sym = x_s(s_sym)[0];
  MX y_sym = y_s(s_sym)[0];
  Function x_ref_fn = Function("x_centerline", {s_sym}, {x_sym});
  Function y_ref_fn = Function("y_centerline", {s_sym}, {y_sym});
  Function theta_ref_fn = Function("theta_centerline", {s_sym},
                                   {atan2(gradient(y_sym, s_sym), gradient(x_sym, s_sym))});

  Opti opti;

  // Design Variables
  MX x = opti.variable(N);
  MX y = opti.variable(N);
  MX theta = opti.variable(N);
  MX v = opti.variable(N);
  MX s = opti.variable(N);
  MX delta = opti.variable(N);
  MX a = opti.variable(N);
  MX se_track = opti.variable(N);

  // Initial Condition
  opti.subject_to(x(0) == 0.0);
  opti.subject_to(y(0) == 0.0);
  opti.subject_to(theta(0) == 0.0);
  opti.subject_to(v(0) == vehicle.v);
  opti.subject_to(a(0) == vehicle.a);
  opti.subject_to(delta(0) == vehicle.delta);

  // Initial initialization
  if (init_traj.has_value()) {
    opti.set_initial(x, DM(init_traj->x_sol));
    opti.set_initial(y, DM(init_traj->y_sol));
    opti.set_initial(theta, DM(init_traj->theta_sol));
    opti.set_initial(s, DM(init_traj->s_sol));
    opti.set_initial(v, DM(init_traj->v_sol));
    opti.set_initial(a, DM(init_traj->a_sol));
    opti.set_initial(delta, DM(init_traj->delta_sol));
  } else {
    opti.set_initial(s, DM((s_min + s_max) / 2));
  }

  // Dynamic Constraints
  for (int k = 0; k < N - 1; ++k) {
    opti.subject_to(s(k + 1) == s(k) + dt * v(k) * cos(theta(k) - theta_ref_fn(s(k))[0]));
    opti.subject_to(
        x(k + 1) ==
        x(k) + dt * v(k) *
                   cos(theta(k) + atan(vehicle.lr / (vehicle.lf + vehicle.lr) * tan(delta(k)))));
    opti.subject_to(
        y(k + 1) ==
        y(k) + dt * v(k) *
                   sin(theta(k) + atan(vehicle.lr / (vehicle.lf + vehicle.lr) * tan(delta(k)))));
    opti.subject_to(theta(k + 1) == theta(k) + dt * v(k) / vehicle.lr *
                                                   sin(atan(vehicle.lr / (vehicle.lf + vehicle.lr) *
                                                            tan(delta(k)))));
    opti.subject_to(v(k + 1) == (v(k) + dt * a(k)));
    opti.subject_to((a(k + 1) - a(k)) / dt <= 0.8);
    opti.subject_to((a(k + 1) - a(k)) / dt >= -0.8);
    opti.subject_to((delta(k + 1) - delta(k)) / dt <= 0.2);
    opti.subject_to((delta(k + 1) - delta(k)) / dt >= -0.2);
    opti.subject_to(pow(x(k + 1) - x_ref_fn(s(k + 1))[0], 2) +
                        pow(y(k + 1) - y_ref_fn(s(k + 1))[0], 2) <=
                    se_track(k + 1));
  }

  // Boundaries
  opti.subject_to(opti.bounded(-0.5, delta, 0.5));
  opti.subject_to(opti.bounded(-2.0, a, 0.5));
  opti.subject_to(opti.bounded(0.0, v, 80.0 / 3.6));
  opti.subject_to(opti.bounded(s_min, s, s_max));
  opti.subject_to(opti.bounded(-3.14, theta, 3.14));
  opti.subject_to(opti.bounded(0.0, se_track, 50.0));

  // Cost
  MX cost = 0;
  for (int k = 0; k < N; k++) {
    cost += 0.1 * pow(se_track(k), 2);
    cost += 0.01 * pow(v(k) - params.v_ref, 2);
  }
  opti.minimize(cost);

  opti.solver(
      "ipopt",
      {{"print_time", false}, {"ipopt.print_level", 0}, {"ipopt.tol", 1e-4}, {"ipopt.sb", "yes"}});
  OptiSol sol = opti.solve();

  // We compute the curvature from the 2d trajectory
  std::vector<double> curvature =
      ComputeCurvature(dm_to_vector(sol.value(x)), dm_to_vector(sol.value(y)));

  return CartesianTrajectory{.x_sol = dm_to_vector(sol.value(x)),
                             .y_sol = dm_to_vector(sol.value(y)),
                             .theta_sol = dm_to_vector(sol.value(theta)),
                             .s_sol = dm_to_vector(sol.value(s)),
                             .v_sol = dm_to_vector(sol.value(v)),
                             .a_sol = dm_to_vector(sol.value(a)),
                             .delta_sol = dm_to_vector(sol.value(delta)),
                             .curvature = curvature};
}

std::vector<double> ComputeCurvature(const std::vector<double>& x, const std::vector<double>& y) {
  const size_t N = x.size();
  std::vector<double> curvature(N, 0.0);

  for (size_t i = 1; i < N - 1; ++i) {
    const auto& p0 = Point2D{x[i - 1], y[i - 1]};
    const auto& p1 = Point2D{x[i], y[i]};
    const auto& p2 = Point2D{x[i + 1], y[i + 1]};

    // First derivatives
    double dx = (p2.x - p0.x) / 2.0;
    double dy = (p2.y - p0.y) / 2.0;

    // Second derivatives
    double ddx = p2.x - 2.0 * p1.x + p0.x;
    double ddy = p2.y - 2.0 * p1.y + p0.y;

    double denom = std::pow(dx * dx + dy * dy, 1.5);
    if (denom > 1e-8) {
      curvature[i] = (dx * ddy - ddx * dy) / denom;
    } else {
      curvature[i] = 0.0;  // Avoid division by zero
    }
  }

  // Handle first and last points
  curvature[0] = 0.0;
  curvature[N - 1] = curvature[N - 2];

  return curvature;
}

}  // namespace t2::planning
