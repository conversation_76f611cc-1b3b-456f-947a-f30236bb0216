// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "src/planning/reference_line/discrete_points_reference_line_smoother.hpp"  // DiscretePointsReferenceLineSmoother

#include <algorithm>

#include <planning_trajectory_msgs/msg/sl_point.hpp>  // SLPoint

#include "reference_line.hpp"
#include "src/common/core/logging.hpp"                 // T2_*
#include "src/planning/integration_tests/timer.hpp"    // Timer
#include "src/planning/math/discrete_points_math.hpp"  // DiscretePointsMath
#include "src/planning/planning_module_exception.hpp"  // PlanningModuleException

namespace t2::planning {

using SLPoint = planning_trajectory_msgs::msg::SLPoint;

DiscretePointsReferenceLineSmoother::DiscretePointsReferenceLineSmoother()
    : app_(IpoptApplicationFactory()) {
  T2_PLAN_CHECK(!IsNull(app_)) << "app_ should have been created";
}

bool DiscretePointsReferenceLineSmoother::Smooth(const ReferenceLine& raw_reference_line,
                                                 ReferenceLine& smoothed_reference_line) {
  std::vector<std::pair<double, double>> raw_point2d;
  std::vector<double> anchorpoints_lateralbound;

  for (const auto& anchor_point : anchor_points_) {
    raw_point2d.emplace_back(anchor_point.path_point.x, anchor_point.path_point.y);
    anchorpoints_lateralbound.emplace_back(anchor_point.lateral_bound);
  }

  // fix front and back points to avoid end states deviate from the center of
  // road
  anchorpoints_lateralbound.front() = 0.0;
  anchorpoints_lateralbound.back() = 0.0;

  NormalizePoints(&raw_point2d);

  std::vector<std::pair<double, double>> smoothed_point2d =
      FemPosSmooth(raw_point2d, anchorpoints_lateralbound);
  if (smoothed_point2d.empty()) {
    T2_ERROR << "discrete_points reference line smoother fails";
    return false;
  }

  DeNormalizePoints(&smoothed_point2d);

  std::vector<route_lane_manager::MapPathPoint> map_path_points;
  GenerateRefPointProfile(raw_reference_line, smoothed_point2d, &map_path_points);

  route_lane_manager::MapPathPoint::RemoveDuplicates(&map_path_points);
  if (map_path_points.size() < 2) {
    T2_ERROR << "Fail to generate smoothed reference line.";
    return false;
  }

  smoothed_reference_line = ReferenceLine(map_path_points);
  return true;
}

std::vector<std::pair<double, double>> DiscretePointsReferenceLineSmoother::FemPosSmooth(
    const std::vector<std::pair<double, double>>& raw_point2d, const std::vector<double>& bounds) {
  const auto& fem_pos_config = config_.fem_pos_deviation_smoothing;

  std::lock_guard<std::mutex> lock(mutex_);  ///< only one thread can use this at a time, to avoid
                                             ///< memory allocation and deallocation of osqp

  fem_pos_deviation_osqp_solver_.Init(fem_pos_config);

  // box contraints on pos are used in fem pos smoother, thus shrink the
  // bounds by 1.0 / sqrt(2.0)
  std::vector<double> box_bounds = bounds;
  const double box_ratio = 1.0 / std::sqrt(2.0);
  for (auto& bound : box_bounds) {
    bound *= box_ratio;
  }

  std::vector<std::pair<double, double>> smoothed_point2d;

  {
    test::Timer timer;
    smoothed_point2d = fem_pos_deviation_osqp_solver_.Solve(raw_point2d, box_bounds);
    T2_INFO << "OSQP runtime=" << timer.elapsed();
  }
  size_t point_size = smoothed_point2d.size();
  if (point_size < 2) {
    T2_ERROR << "Return by fem pos smoother is wrong. Size smaller than 2 ";
    return {};
  }

  if (config_.use_ipopt) {
    const auto& ipopt_options = config_.ipopt_options;

    T2_PLAN_CHECK(!IsNull(app_)) << "app_ should have been created";

    const double w_f = fem_pos_config.weight_fem_pos_deviation;
    const double w_p = fem_pos_config.weight_path_length;
    const double w_r = fem_pos_config.weight_ref_deviation;

    if (!map_nlps_.count(point_size)) {
      map_nlps_.emplace(point_size, new ReferenceLineNLP(app_, box_bounds, w_f, w_p, w_r));
    }

    auto& reference_line_nlp_ptr = map_nlps_.at(point_size);
    reference_line_nlp_ptr->ref_points = raw_point2d;

    // Ipopt options
    const auto& app_option = app_->Options();

    app_option->SetNumericValue("tol", ipopt_options.tol);
    // app_option->SetNumericValue("constr_viol_tol", ipopt_options.constr_viol_tol);
    // "bound_relax_factor"
    app_option->SetStringValue("mu_strategy", "adaptive");
    app_option->SetStringValue("linear_solver", ipopt_options.linear_solver);
    app_option->SetIntegerValue("max_iter", ipopt_options.max_iter);
    app_option->SetIntegerValue("print_level", ipopt_options.print_level);

    Ipopt::ApplicationReturnStatus ipopt_status = app_->Initialize();
    T2_PLAN_CHECK_FULL(PlanningModuleErrorCode::FAILED_INITIALIZATION,
                       ipopt_status == Ipopt::Solve_Succeeded)
        << "Failed to initialize Ipopt: " << static_cast<int>(ipopt_status);

    {
      test::Timer timer;
      [[maybe_unused]] const Ipopt::ApplicationReturnStatus status =
          app_->OptimizeTNLP(reference_line_nlp_ptr);
      T2_INFO << "Ipopt runtime=" << timer.elapsed() << ", status=" << status;
    }
    smoothed_point2d = reference_line_nlp_ptr->get_solution();
    const auto& stats = app_->Statistics();
    if (IsValid(stats)) {
      Ipopt::Index iter_count = stats->IterationCount();
      Ipopt::Number final_obj = stats->FinalObjective();
      Ipopt::Number dual_inf, constr_viol, varbounds_viol, complementarity, kkt_error;
      stats->Infeasibilities(dual_inf, constr_viol, varbounds_viol, complementarity, kkt_error);
      T2_INFO << "iter_count=" << iter_count << ", final_obj=" << final_obj
              << ", dual_inf=" << dual_inf << ", constr_viol=" << constr_viol
              << ", varbounds_viol=" << varbounds_viol;

      {
        const auto [s_fem, s_path, s_ref] = reference_line_nlp_ptr->get_obj_value();
        const double obj = w_f * s_fem + w_p * s_path + w_r * s_ref;
        T2_INFO << "Ipopt: s_fem=" << s_fem << ", s_path=" << s_path << ", s_ref=" << s_ref
                << ", obj=" << obj;
      }
      {
        const auto& flat_soln = fem_pos_deviation_osqp_solver_.flat_soln;
        T2_PLAN_CHECK(point_size * 2 == flat_soln.size())
            << "point_size=" << point_size << ", flat_soln.size()=" << flat_soln.size();
        const auto [s_fem, s_path, s_ref] = reference_line_nlp_ptr->get_obj_value(flat_soln.data());
        const double obj = w_f * s_fem + w_p * s_path + w_r * s_ref;
        T2_INFO << " OSQP: s_fem=" << s_fem << ", s_path=" << s_path << ", s_ref=" << s_ref
                << ", obj=" << obj;
      }
    } else {
      T2_WARN << "stats is not valid";
    }  // if(IsValid(stats))
  }

  return smoothed_point2d;
}

void DiscretePointsReferenceLineSmoother::SetAnchorPoints(
    const std::vector<AnchorPoint>& anchor_points) {
  CHECK_GT(anchor_points.size(), 1U);
  anchor_points_ = anchor_points;
}

void DiscretePointsReferenceLineSmoother::NormalizePoints(
    std::vector<std::pair<double, double>>* xy_points) {
  zero_x_ = xy_points->front().first;
  zero_y_ = xy_points->front().second;
  std::for_each(xy_points->begin(), xy_points->end(), [this](std::pair<double, double>& point) {
    auto curr_x = point.first;
    auto curr_y = point.second;
    std::pair<double, double> xy(curr_x - zero_x_, curr_y - zero_y_);
    point = std::move(xy);
  });
}

void DiscretePointsReferenceLineSmoother::DeNormalizePoints(
    std::vector<std::pair<double, double>>* xy_points) {
  std::for_each(xy_points->begin(), xy_points->end(), [this](std::pair<double, double>& point) {
    auto curr_x = point.first;
    auto curr_y = point.second;
    std::pair<double, double> xy(curr_x + zero_x_, curr_y + zero_y_);
    point = std::move(xy);
  });
}

bool DiscretePointsReferenceLineSmoother::GenerateRefPointProfile(
    const ReferenceLine& raw_reference_line,
    const std::vector<std::pair<double, double>>& xy_points,
    std::vector<route_lane_manager::MapPathPoint>* map_path_points) {
  // Compute path profile
  const auto [headings, accumulated_s, kappas, dkappas] =
      DiscretePointsMath::ComputePathProfile(xy_points);

  if (headings.empty()) {
    return false;
  }

  // Load into ReferencePoints
  size_t points_size = xy_points.size();
  for (size_t i = 0; i < points_size; ++i) {
    SLPoint ref_sl_point;
    if (!raw_reference_line.XYToSL({xy_points[i].first, xy_points[i].second}, &ref_sl_point)) {
      return false;
    }
    const double kEpsilon = 1e-6;
    if (ref_sl_point.s < -kEpsilon || ref_sl_point.s > raw_reference_line.Length()) {
      continue;
    }
    ref_sl_point.s = std::max(ref_sl_point.s, 0.0);
    // route_lane_manager::MapPathPoint
    auto rlp = raw_reference_line.GetMapPathPoint(ref_sl_point.s);
    auto new_lane_waypoints = rlp.lane_waypoints();
    for (auto& lane_waypoint : new_lane_waypoints) {
      lane_waypoint.l = ref_sl_point.l;
    }

    PathPoint path_point;
    path_point.x = xy_points[i].first;
    path_point.y = xy_points[i].second;
    path_point.theta = headings[i];
    path_point.kappa = kappas[i];
    path_point.dkappa = dkappas[i];

    map_path_points->emplace_back(route_lane_manager::MapPathPoint(path_point, new_lane_waypoints));
  }
  return true;
}

}  // namespace t2::planning
