// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "src/planning/planning_component.hpp"

#include <planning_msgs/msg/speed_planner.hpp>  // SpeedPlanner
#include <planning_msgs/msg/speed_target.hpp>   // SpeedTarget

#include "src/common/contract_assertions/contract_assertions.hpp"
#include "src/common/core/logging.hpp"
#include "src/common/math/euler_angles_zxy.hpp"                // EulerAnglesZXYd
#include "src/common/proto/geometry.pb.h"                      // common::PointENU
#include "src/common/runfiles/runtime_dependency_manager.hpp"  // RuntimeDependencyManager
#include "src/common/transform/transformer.hpp"
#include "src/planning/common/planning_vehicle_state/planning_vehicle_state_creator.hpp"  // CreatePlanningVehicleState
#include "src/planning/common/trajectory_stitcher.hpp"       // ComputeStitchingTrajectory
#include "src/planning/lane_change_decider/lane_change.hpp"  // LaneChange
#include "src/planning/planning_module_state.hpp"            // UpdatePlanningModuleState
#include "src/planning/speed_limit_trigger.hpp"  // HandleSpeedLimitDriverTrigger, ComputeBrakingDistance

namespace t2::planning {

namespace hdmap = t2::map::hdmap;
using hdmap::HDMapUtil;
REGISTER_INTER_PLANNING_MSG(SpeedTarget);

void LogReferenceLine(const CreateReferenceLineResult& reference_lines,
                      LaneChangeData& lane_change_data_out);

void LogSpeedTarget(const std::optional<double> target_speed,
                    const std::optional<double> look_forward_speed_target,
                    const double distance_to_target_speed,
                    const std::optional<double> breaking_distance,
                    const bool target_speed_anticipation_enable, const double upper_speed_limit,
                    std::optional<std::string> speed_limit_lane_id,
                    SpeedTarget& speed_target_debug);

// not to confuse with ::t2::planning::SpeedPlanner
void AddSpeedPlannerInformationToProto(const std::vector<ObstacleState>& obs_infos,
                                       const std::vector<SpeedPlannerDebug>& speed_planner_debug,
                                       const bool speed_planner_nlp_state,
                                       const PlannerConfig& planner_config,
                                       ::planning_msgs::msg::SpeedPlanner& speed_planner_output);

template <typename T>
inline Eigen::Quaternion<T> HeadingToQuaternionZXY(T heading);

PlanningComponent::PlanningComponent(const std::shared_ptr<HealthReporter>& health_reporter_ptr
                                     // ::apex::executor::executor_base& executor
                                     )
    : base(kPlanningComponentName, kPlanningNamespace),
      node_(get_rclcpp_node()),
      health_reporter_ptr_(health_reporter_ptr)
// , executor_(executor)
{
  T2_INFO << "tracking component init, starting ...";
  expect_enforce(true);

  T2_INFO << "Config file loading complete.";

  try {
    const std::string planning_config_file =
        common::runfiles::RuntimeDependencyManager::ResolvePath(kPlanningConfigFile);
    std::ifstream is(planning_config_file);
    T2_PLAN_CHECK_FULL(PlanningModuleErrorCode::INVALID_INPUT, is)
        << "Cannot read planning config " << planning_config_file;
    cereal::JSONInputArchive archive(is);  // serialize to JSON
    archive(cereal::make_nvp("planning_config", config_));
    T2_INFO << "Planning config:\n" << ToJsonString(config_);

    const bool with_hdmap = true;
    reference_line_provider_.Init(with_hdmap);

    // Initialize Planner
    const std::string planner_config_file =
        common::runfiles::RuntimeDependencyManager::ResolvePath(kPlannerConfigFile);
    planner_.InitPlanner(planner_config_file);

    this->SetupCommunication();

    // Speed limiter Driver Trigger Initialization
    engage_advice_ = Advice::READY_TO_ENGAGE;
    speed_limit_driver_trigger_value_ =
        planner_.planner_config.planning_speed_config.planning_upper_speed_limit;

    // HealthReporter (to-do)
    // health_reporter_ptr_ = HealthReporter::Create(kPlanningComponentName, executor_);

    // Publish a warning message as heartbeat to indicate the planning has been
    // initialized.

    T2_PLAN_CHECK_PRETTY(false, {}, {StatusFlagType::PLANNING_UNKNOWN})
        << std::setprecision(12)
        << "PlanningComponent is initialized, but no prediction message has "
           "been received yet, timestamp="
        << get_rclcpp_node().get_clock()->now().seconds();
  } catch (const PlanningModuleException& e) {
    // Capture flags here and do error handling
    // GetHealthReporter().Report(e.error_flags, e.warn_flags);
    T2_WARN << e.what();
  }

  T2_INFO << "PlanningComponent is successfully initialized";
}

PlanningComponent::~PlanningComponent() {}

void PlanningComponent::Proc(const PredictionObstacles& prediction_obstacles,
                             const Chassis& chassis,
                             const LocalizationEstimate& localization_estimate) {
  try {
    ProcHelper(prediction_obstacles, chassis, localization_estimate);
    // GetHealthReporter().Report({}, {});
  } catch (const PlanningModuleException& e) {
    // Capture flags here and do error handling
    // GetHealthReporter().Report(e.error_flags, e.warn_flags);
    if (e.reset) {
      // reset the state of the planner when the ego is outside the map.
      reference_line_provider_.ego_lane_.reset();
      prev_trajectory_ = PlanningTrajectory();
      last_trajectory_intention_ptr_.reset();
    }
    T2_ERROR << e.what() << " [" << std::setprecision(12)
             << get_rclcpp_node().get_clock()->now().seconds() << "]";
  }

  // Send change of state to PlanningStateReport
  // SendPlanningActionEndNotification(planning_action_end_notification_writer_);
}

void PlanningComponent::PublishDummyTrajectory() {
  auto msg = trajectory_writer_->borrow_loaned_message();
  trajectory_writer_->publish(std::move(msg));
}

ADCTrajectory PlanningComponent::ProcHelper(const PredictionObstacles& prediction_obstacles,
                                            const Chassis& chassis,
                                            const LocalizationEstimate& localization_estimate) {
  const double start_time = get_rclcpp_node().get_clock()->now().seconds();
  ADCTrajectory output_trajectory;
  auto& not_ready = output_trajectory.decision.main_decision.not_ready;
  std::string reason;
  if (!HDMapUtil::BaseMapPtr()) {
    reason += "map not ready. ";
  }
  // if (!prediction_obstacles_ptr) {
  //   reason += "prediction_obstacles not ready. ";
  // }
  // if (!chassis_ptr) {
  //   reason += "chassis not ready. ";
  // }
  // if (!localization_estimate_ptr) {
  //   reason += "localization_estimate not ready. ";
  // }
  if (!reason.empty()) {
    not_ready.reason = reason;

    T2_ERROR << not_ready.reason << "; skip the planning cycle.";
    trajectory_writer_->publish(output_trajectory);
    // t2::adapter::HealthInfo health_info{
    //     t2::adapter::HealthInfo::Severity::kError, not_ready.reason()};
    // GetHealthReporter().Report(health_info);
    return output_trajectory;
  }

  // const auto& prediction_obstacles = *prediction_obstacles_ptr;
  // const auto& chassis = *chassis_ptr;
  // const auto& localization_estimate = *localization_estimate_ptr;

  // Add dependency information to the header
  /* ===== Readers =====*/
  // t2::common::message_dependency::AddMessageDependencyFromMessageToMessage(
  //     output_trajectory, prediction_obstacles, kPredictionTopic);
  // t2::common::message_dependency::AddMessageDependencyFromMessageToMessage(
  //     output_trajectory, chassis, kChassisTopic);
  // t2::common::message_dependency::AddMessageDependencyFromMessageToMessage(
  //     output_trajectory, localization_estimate, kLocalizationPoseTopic);
  // t2::common::message_dependency::AddMessageDependencyFromMessageToMessage(
  //     output_trajectory, planning_request_, kPlanningRequestTopic);

  const ChassisInfo chassis_info(chassis);
  T2_PLAN_CHECK_PRETTY(chassis_info.speed_mps >= 0,
                       {StatusFlagType::PLANNING_RECEIVED_NEGATIVE_SPEED}, {})
      << std::setprecision(12) << "Negative chassis speed=" << chassis_info.speed_mps << " at "
      << get_rclcpp_node().get_clock()->now().seconds();
  const LocalizationInfo localization_info(localization_estimate);

  // Verify that we are on the map
  // If we are not, we early return without sending the ADCTrajectory but
  // instead an error message The following test is not a correct way to verify
  // if we are on the map or not but this is a first approach. In the future it
  // will be necessary to clean it up.

  common::PointENU point;
  point.set_x(localization_info.x);
  point.set_y(localization_info.y);
  point.set_z(localization_info.z);
  const bool is_within_map = reference_line_provider_.IsWithinMap(point, localization_info.heading);

  if (!is_within_map) {
    T2_INFO << std::setprecision(12)
            << "Outside the map at timestamp=" << get_rclcpp_node().get_clock()->now().seconds();
  }

  T2_PLAN_CHECK_PRETTY(is_within_map, {StatusFlagType::PLANNING_CURRENTLY_OUTSIDE_MAP}, {})
      << std::setprecision(12)
      << "Outside the map at timestamp=" << get_rclcpp_node().get_clock()->now().seconds();

  // OBSTACLE PROCESSING
  // Create the data structure which contains the obstacle information.
  const auto [obstacle_map, error_flags] = CreateObstacles(prediction_obstacles);

  output_trajectory = ProcInternal_(obstacle_map, chassis_info, localization_info);

  output_trajectory.debug.planning_module_status = planning_module_status_;

  const double elapsed_time = get_rclcpp_node().get_clock()->now().seconds() - start_time;
  output_trajectory.debug.elapsed_time = elapsed_time;

  // T2_INFO << "output_trajectory=" << rosidl_generator_traits::to_yaml(output_trajectory);
  trajectory_writer_->publish(output_trajectory);  ///< publish to "/t2/planning"

  // Verify that we have correct length of trajectory
  // If not, clear the trajectory, and return without sending the adc_trajectory.
  // System receive the error and reset apollo
  const int num_traj_points = output_trajectory.only_trajectory.size();
  static constexpr int kNumTrajPointsMin = 30;
  T2_PLAN_CHECK_PRETTY(num_traj_points >= kNumTrajPointsMin,
                       {StatusFlagType::PLANNING_OUTPUT_TOO_SHORT_TRAJECTORY}, {})
      << "Planning trajectory too short: " << num_traj_points << "<" << kNumTrajPointsMin;

  T2_PLAN_CHECK_PRETTY_FULL(error_flags.empty(), error_flags, {},
                            false)
      << "Some error flags exist";  // do note reset cached members

  return output_trajectory;
}

ADCTrajectory PlanningComponent::ProcInternal_(const std::map<int, Obstacle>& obstacle_map,
                                               const ChassisInfo& chassis_info,
                                               const LocalizationInfo& localization_info) {
  if (config_.print_planning_input_info) {
    std::string map_dir;
    const PlanningProcInput planning_proc_input{map_dir, obstacle_map, chassis_info,
                                                localization_info};
    T2_INFO << ToJsonString(planning_proc_input);
  }

  ADCTrajectory adc_trajectory = PlanningPipeline(
      last_trajectory_intention_ptr_, prev_trajectory_, planner_, intention_creator_,
      select_trajectory_, reference_line_provider_, planning_request_, config_, obstacle_map,
      chassis_info, localization_info, lane_change_decision_, engage_advice_,
      speed_limit_driver_trigger_value_, planning_module_status_, lc_source_lane_,
      opt_lc_destination_lane_, lane_change_decider_, lane_change_driver_trigger_,
      // prev_hmi_notification_,
      // stored_hmi_notification_,
      target_speed_anticipation_enable_);

  if (config_.print_planning_input_info) {
    const auto& next_traj_point = adc_trajectory.only_trajectory[1];
    T2_INFO << "next_traj_point:\n" << rosidl_generator_traits::to_yaml(next_traj_point);
  }
  return adc_trajectory;
}

ADCTrajectory PlanningComponent::PlanningPipeline(
    std::shared_ptr<IntentionTaskData>& last_trajectory_intention_ptr,
    PlanningTrajectory& prev_trajectory, Planner& planner, IntentionCreator& intention_creator,
    SelectTrajectory& select_trajectory, ReferenceLineProvider& reference_line_provider,
    /* ========== */
    const PlanningRequest& planning_request, const PlanningConfiguration& config,
    /* ===== input to planning component ===== */
    const std::map<int, Obstacle>& obstacle_map, const ChassisInfo& chassis_info,
    const LocalizationInfo& localization_info,
    /* ========== */
    lane_change_decider::LaneChangeDecision& lane_change_decision, Advice& engage_advice,
    double& speed_limit_driver_trigger_value, PlanningModuleStatus& planning_module_status,
    ReferenceLineAndRouteSegments& lc_source_lane,
    std::optional<ReferenceLineAndRouteSegments>& opt_lc_destination_lane,
    lane_change_decider::LaneChangeDecider& lane_change_decider,
    lane_change_trigger::DriverTrigger& lane_change_driver_trigger,
    // std::optional<HMINotification>& prev_hmi_notification,
    // std::optional<HMINotification>& stored_hmi_notification,
    bool& target_speed_anticipation_enable) const {
  ADCTrajectory output_trajectory;  ///< output
  // HMINotification hmi_notification;  ///< HMI output
  // hmi_notification.mutable_header()->set_module_name("planning");

  // ad_state_manager::ADStateMessage ad_state_message;

  // PREPROCESSING
  auto [stitching_trajectory, replan_reason, original_vehicle_state, aligned_vehicle_state,
        projected_vehicle_state, start_timestamp, start_system_timestamp] =
      Preprocessing(output_trajectory, config, prev_trajectory, reference_line_provider,
                    localization_info, chassis_info);

  // REFERENCE LINE
  const auto create_reference_line_result =
      reference_line_provider.CreateReferenceLine(projected_vehicle_state, config);
  std::list<ReferenceLine> list_reference_lines = create_reference_line_result.list_reference_lines;
  std::list<route_lane_manager::RouteSegments> list_route_segments =
      create_reference_line_result.list_route_segments;

  T2_PLAN_CHECK_PRETTY(!list_reference_lines.empty(),
                       {StatusFlagType::PLANNING_REFERENCE_LINE_ERROR}, {});

  // We log the reference line in the planning output for debugging.
  LogReferenceLine(create_reference_line_result, output_trajectory.lane_change_data);

  ReferenceLineMap reference_line_map =
      GetCurrentAndAdjacentLanes(list_reference_lines, list_route_segments, aligned_vehicle_state);

  T2_PLAN_CHECK_PRETTY(reference_line_map.count(ReferenceLineType::CURRENT),
                       {StatusFlagType::PLANNING_REFERENCE_LINE_ERROR}, {});

  const auto& current_driving_lane = reference_line_map.at(ReferenceLineType::CURRENT);

  // START POINT
  const TrajectoryPoint& planning_start_point = stitching_trajectory.back();
  T2_INFO << "planning_start_point=" << rosidl_generator_traits::to_yaml(planning_start_point);

  // DETERMINE THE EGO LANE
  hdmap::LaneInfoConstPtr ego_lane = reference_line_provider.FindEgoLane(projected_vehicle_state);

  // LANE CHANGE
  auto [is_lane_changeable, opt_lane_change_progress, obstacles_information] =
      LaneChange(config, planner.planner_config, opt_lc_destination_lane, list_route_segments,
                 list_reference_lines, planning_module_status, current_driving_lane, lc_source_lane,
                 reference_line_map, aligned_vehicle_state, engage_advice, chassis_info.turn_signal,
                 planning_request.command, lane_change_decision, lane_change_driver_trigger,
                 last_trajectory_intention_ptr, lane_change_decider, planning_start_point,
                 output_trajectory, projected_vehicle_state, obstacle_map, node_,
                 // hmi_notification,
                 ego_lane);

  // Lane Change Direction if any
  const lane_change_trigger::LaneChangeDirection lane_change_direction =
      lane_change_decider.opt_trigger_last_decision_
          ? lane_change_decider.opt_trigger_last_decision_->lane_change_direction
          : lane_change_trigger::LaneChangeDirection::NONE;

  // CREATE INTENTION
  std::list<IntentionTaskData> list_intention_task_data =
      intention_creator.CreateListIntentionTaskData(
          last_trajectory_intention_ptr, aligned_vehicle_state, planning_start_point,
          planning_module_status, opt_lane_change_progress, reference_line_map, is_lane_changeable,
          config.reference_line_lateral_threshold, obstacles_information, lane_change_direction,
          node_, config_.lane_change_config.cancelation_threshold,
          config_.lane_change_config.disable_lc_cancel);

  // SPEED LIMIT
  // SPEED LIMIT FROM MAP

  std::optional<double> target_speed;
  std::optional<double> look_forward_speed_target;
  std::optional<std::string> speed_limit_lane_id;
  std::optional<double> breaking_distance;
  double total_length = 0.0;

  if (planner.planner_config.planning_speed_config.target_speed_enabled) {
    // We verify if the jct_info has been explicitely set and the lane is part
    // of a junction.
    if (ego_lane->lane().has_jct_info() && ego_lane->lane().jct_info().is_junction()) {
      // We get the target_speed
      target_speed = ego_lane->lane().jct_info().target_speed_mps();
      target_speed_anticipation_enable = false;
    } else {
      auto current_lane = ego_lane;
      while (total_length < planner.planner_config.planning_speed_config.look_ahead_distance) {
        for (const auto& successor_lane_id : current_lane->lane().successor_id()) {
          auto lane = HDMapUtil::BaseMapPtr()->GetLaneById(successor_lane_id);
          if (lane->lane().merge_info().branch_direction() ==
              hdmap::MergeInfo_BranchDirection_UNKNOWN) {
            current_lane = lane;
            break;
          }
        }
        if (current_lane->lane().has_jct_info() && current_lane->lane().jct_info().is_junction()) {
          look_forward_speed_target = current_lane->lane().jct_info().target_speed_mps();
          speed_limit_lane_id = current_lane->lane().id().id();
          break;
        }
        total_length += current_lane->lane().length();
      }
    }

    if (look_forward_speed_target) {
      breaking_distance = ComputeBrakingDistance(
          planning_start_point.v, look_forward_speed_target.value(), planner.planner_config);

      if (target_speed_anticipation_enable) {
        target_speed = look_forward_speed_target.value();
      } else if (total_length <= breaking_distance) {
        target_speed = look_forward_speed_target.value();
        target_speed_anticipation_enable = true;
      }
    }
  }

  // By default the map max speed is considered 22.22 m/s
  // If the flag use_map_speed is true, map_max_speed is equal to the
  // speed_limit argument contained in the map.
  double map_max_speed = config.default_map_speed_limit;
  if (config.use_map_speed) {
    map_max_speed = ego_lane->lane().speed_limit();
  }

  // If the target speed is given by the map we take set the max speed as the
  // minimum between the map_map_speed and the target speed. The map_max_speed
  // correspond to the speed limit of a given segment. The target speed is the
  double max_speed = target_speed ? std::min(target_speed.value(), map_max_speed) : map_max_speed;
  // SPEED LIMIT DRIVER TRIGGER
  if (config.speed_limit_driver_trigger_config.use_speed_limit_driver_trigger) {
    std::tie(engage_advice, speed_limit_driver_trigger_value) =
        HandleSpeedLimitDriverTrigger(chassis_info.advice, engage_advice, chassis_info.speed_mps,
                                      speed_limit_driver_trigger_value, output_trajectory, config);
    max_speed = std::min(max_speed, speed_limit_driver_trigger_value);
  }

  planner.planner_config.planning_speed_config.planning_upper_speed_limit = max_speed;

  LogSpeedTarget(target_speed, look_forward_speed_target, total_length, breaking_distance,
                 target_speed_anticipation_enable, max_speed, speed_limit_lane_id,
                 output_trajectory.debug.speed_target_debug);
  // we convert the max speed to kmh and we round it to the closest integer.
  const int max_speed_kmh = static_cast<int>(round(3.6 * max_speed));
  (void)max_speed_kmh;
  // to-do
  // LogHMI::LogHMISpeedLimit(max_speed_kmh, hmi_notification);

  // PLANNING
  planner.opt_reference_line_map = reference_line_map;
  planner.ComputeTrajectories(output_trajectory, list_intention_task_data, planning_start_point);

  // Select  TRAJECTORY AND UPDATE prev_trajectory
  select_trajectory.Select(
      /* output */
      output_trajectory, last_trajectory_intention_ptr, prev_trajectory,
      /* input */ reference_line_map, list_intention_task_data, start_timestamp,
      stitching_trajectory, replan_reason, start_timestamp, planner.planner_config, config,
      localization_info, list_reference_lines, node_.get_clock());

  UpdatePlanningModuleState(planning_module_status,  // output
                            config, *last_trajectory_intention_ptr, list_reference_lines,
                            planning_start_point, lc_source_lane, opt_lc_destination_lane,
                            reference_line_map);

  output_trajectory.planning_data.map_max_speed = map_max_speed;
  // We log the information related to the speed planner front obstacle.

  AddSpeedPlannerInformationToProto(last_trajectory_intention_ptr->obs_infos_debug,
                                    last_trajectory_intention_ptr->speed_planner_debug,
                                    last_trajectory_intention_ptr->speed_planner_nlp_state,
                                    planner.planner_config, output_trajectory.speed_planner);
  // to-do
  // HMI INFORMATION
  // LogHMI::LogHMIInformation(hmi_notification, prev_hmi_notification,
  //                           stored_hmi_notification, hmi_notifications_writer_,
  //                           GetClock());

  GetCurrentLaneInfoPtrFromReferenceLine(projected_vehicle_state,
                                         current_driving_lane.reference_line);

  return output_trajectory;
}

void PlanningComponent::GetCurrentLaneInfoPtrFromReferenceLine(
    const PlanningVehicleState& vehicle_state, const ReferenceLine& reference_line) const {
  hdmap::LaneInfoConstPtr current_lane_ptr = nullptr;
  const auto lane_ptr_list = reference_line.GetLaneInfoPtrList();
  double min_distance = std::numeric_limits<double>::infinity();
  for (const auto& lane_ptr : lane_ptr_list) {
    const auto distance = lane_ptr->DistanceTo({vehicle_state.x, vehicle_state.y});
    if (distance < min_distance) {
      current_lane_ptr = lane_ptr;
      min_distance = distance;
    }
  }
  if (current_lane_ptr) {
  } else {
    std::ostringstream os;
    os << "Failed to find the current lane in (";
    for (const auto& lane_ptr : lane_ptr_list) {
      os << lane_ptr->id().id() << ",";
    }
    os << ")";
    T2_ERROR << os.str();
  }
}

// This method may be deleted in the future, or separated into clearer sub-parts
// inside the pipeline
auto PlanningComponent::Preprocessing(ADCTrajectory& output_trajectory,
                                      const PlanningConfiguration& config,
                                      const PlanningTrajectory& prev_trajectory,
                                      ReferenceLineProvider& reference_line_provider,
                                      const LocalizationInfo& localization_info,
                                      const ChassisInfo& chassis_info) const
    -> std::tuple<std::vector<TrajectoryPoint>, std::string, PlanningVehicleState,
                  PlanningVehicleState, PlanningVehicleState, double, double> {
  // The following timestamps are used through the planning module to record the
  // actual time when we are processing the data. We need the actual time
  // because due to communication delays between the modules we may receive
  // outdated information which we need to update accordingly.
  const double start_timestamp = node_.get_clock()->now().seconds();
  const double start_system_timestamp =
      std::chrono::duration<double>(std::chrono::system_clock::now().time_since_epoch()).count();

  // Here we create the original vehicle state from the localization estimate
  // and the chassis information. Importantly, its timestamp is the timestamp of
  // the localization message, which may be outdated when we process the
  // information. We then use start_timestamp to create an aligned vehicle state
  // which is the original vehicle state as if it moved with constant speed and
  // acceleration during the time since start_timestamp Finally, we create a
  // projected vehicle state which is the aligned vehicle state projected into
  // the previous trajectory (generated the last cycle).

  auto [original_vehicle_state, _] = CreatePlanningVehicleState(localization_info, chassis_info);

  const auto [aligned_vehicle_state, creation_status] =
      CreateAlignedVehicleState(start_timestamp, original_vehicle_state);
  if (!creation_status) {
    T2_WARN << "Failed to create aligned vehicle state.";
  }

  PlanningVehicleState projected_vehicle_state = aligned_vehicle_state;
  if (config.use_projection) {
    ProjectVehicleIntoLastTrajectory(projected_vehicle_state, prev_trajectory.trajectory);
  }

  // If a negative value is given as the current speed, planning fails.
  // It is probably due to a strange ST drivable boundary being generated. In
  // the DP part, the cost of the vertex with (s,t)=(0,0) is inf, and such a
  // case exists only when the obstacle cost is infinite.
  projected_vehicle_state.linear_velocity = std::max(projected_vehicle_state.linear_velocity, 1e-4);

  const auto ego_lane_info_ptr = reference_line_provider.FindEgoLane(projected_vehicle_state);

  T2_PLAN_CHECK_PRETTY(ego_lane_info_ptr, {},
                       {StatusFlagType::PLANNING_REFERENCE_LINE_ERROR});  ///< reset

  // update planer evaluation info
  output_trajectory.planner_evaluation_info =
      reference_line_provider.GetPlannerEvaluationInfo(aligned_vehicle_state, ego_lane_info_ptr);

  // In the following we compute the stitching trajectory. The computation
  // require the vehicle state and depending on the situation we use
  // aligned_vehicle_state or projected_vehicle_state. Define the destination of
  // reset vehicle state satisfying the following
  // - When the autonomous driving mode is set, we use projected_vehicle_state
  // to avoid the reset of previous behavior due to horizontal shake caused by
  // localization errors..
  // - When the manual mode is set, we use the aligned_vehicle_state so that the
  // vehicle can adjust the planning position when there is a gap between the
  // previous trajectory and the latest localization result. Without reset, the
  // ready_judge module returns kNotReady status due to the lateral position
  // mismatch and the autonomous driving mode does not start.
  //
  // c.f.) https://t2auto.atlassian.net/browse/TP-452
  const auto reset_vehicle_state_for_trajectory_stitcher = [&]() -> PlanningVehicleState {
    return (config.initial_position_definition ==
                PlanningConfiguration::InitialPositionDefinitionType::AD_START_POINT ||
            aligned_vehicle_state.driving_mode == DrivingMode::COMPLETE_AUTO_DRIVE)
               ? projected_vehicle_state
               : aligned_vehicle_state;
  }();

  const auto [stitching_trajectory, replan_reason] =
      ComputeStitchingTrajectory(reset_vehicle_state_for_trajectory_stitcher, start_timestamp,
                                 config.replan_by_long, prev_trajectory);

  return {stitching_trajectory,    replan_reason,   original_vehicle_state, aligned_vehicle_state,
          projected_vehicle_state, start_timestamp, start_system_timestamp};
}

std::pair<PlanningVehicleState, bool> PlanningComponent::CreateAlignedVehicleState(
    double start_timestamp, const PlanningVehicleState& original_vehicle_state) const {
  bool status = false;

  // a VehicleState Object is instantiated and the necessary member are set
  // using the information originated from the PlanningVehicleStateCreator
  // method's parameter. In the near future, we will be able to remove this step
  // as we could use only one data structure.

  PlanningVehicleState vehicle_state = original_vehicle_state;

  DCHECK_GE(start_timestamp, original_vehicle_state.timestamp)
      << "start_timestamp is behind original_vehicle_state_timestamp by "
      << start_timestamp - original_vehicle_state.timestamp << " secs";

  if (std::isnan(vehicle_state.x) || std::isnan(vehicle_state.y) || std::isnan(vehicle_state.z) ||
      std::isnan(vehicle_state.heading) || std::isnan(vehicle_state.kappa) ||
      std::isnan(vehicle_state.linear_velocity) || std::isnan(vehicle_state.linear_acceleration)) {
    T2_ERROR << "Create PlanningVehicleStateCreator failed or the vehicle "
                "state is out "
                "dated.";
  } else {
    status = true;
    if (start_timestamp >= original_vehicle_state.timestamp) {
      const auto time_diff = start_timestamp - vehicle_state.timestamp;

      common::VehicleState t2_vehicle_state;
      t2_vehicle_state.x = vehicle_state.x;
      t2_vehicle_state.y = vehicle_state.y;
      t2_vehicle_state.z = vehicle_state.z;
      t2_vehicle_state.heading = vehicle_state.heading;
      t2_vehicle_state.linear_velocity = vehicle_state.linear_velocity;
      t2_vehicle_state.linear_acceleration = vehicle_state.linear_acceleration;
      t2_vehicle_state.kappa = vehicle_state.kappa;
      t2_vehicle_state.timestamp = vehicle_state.timestamp;

      common::VehicleState predicted_vehicle_state = Predict(time_diff, t2_vehicle_state);

      vehicle_state.x = predicted_vehicle_state.x;
      vehicle_state.y = predicted_vehicle_state.y;
      vehicle_state.z = predicted_vehicle_state.z;
      vehicle_state.heading = predicted_vehicle_state.heading;
      vehicle_state.linear_velocity = predicted_vehicle_state.linear_velocity;
      vehicle_state.linear_acceleration = predicted_vehicle_state.linear_acceleration;
      vehicle_state.kappa = predicted_vehicle_state.kappa;
      vehicle_state.timestamp = start_timestamp;

    } else {
      T2_ERROR << "Apollo clock and localization timestamp are invalid, "
                  "start_timestamp: "
               << start_timestamp
               << ", original_vehicle_state_timestamp: " << original_vehicle_state.timestamp;
    }
    // If a negative value is given as the current speed, planning fails.
    // It is probably due to a strange ST drivable boundary being generated.
    // In the DP part, the cost of the vertex with (s,t)=(0,0) is inf, and
    // such a case exists only when the obstacle cost is infinite.
    vehicle_state.linear_velocity = std::max(vehicle_state.linear_velocity, 1e-4);
  }
  return {vehicle_state, status};
}

void PlanningComponent::ProjectVehicleIntoLastTrajectory(
    PlanningVehicleState& vehicle_state, const std::vector<TrajectoryPoint>& last_traj) const {
  // Project vehicle state to the last trajectory.
  // Input vehicle_state is the aligned vehicle state.
  const auto& trajectory_points = last_traj;
  if (trajectory_points.empty()) {
    return;
  }

  const size_t num_last_points = trajectory_points.size();

  size_t min_idx = 0;
  double dist_sqr_min = std::numeric_limits<double>::max();

  const double vehicle_x = vehicle_state.x;
  const double vehicle_y = vehicle_state.y;
  for (size_t i = 0; i < num_last_points; ++i) {
    const auto& trajectory_point = trajectory_points[i];
    const auto& path_point = trajectory_point.path_point;
    const common::math::Vec2d curr_point(path_point.x, path_point.y);
    const auto diff_x = vehicle_x - path_point.x;
    const auto diff_y = vehicle_y - path_point.y;

    const auto tmp_dist = diff_x * diff_x + diff_y * diff_y;
    if (tmp_dist > 0 && tmp_dist < dist_sqr_min) {
      dist_sqr_min = tmp_dist;
      min_idx = i;
    }
  }

  const auto& matched = trajectory_points[min_idx];
  const auto& path_point = matched.path_point;
  const auto diff_x = vehicle_x - path_point.x;
  const auto diff_y = vehicle_y - path_point.y;
  const auto tmp_dist_prj =
      diff_x * std::cos(path_point.theta) + diff_y * std::sin(path_point.theta);
  const auto pred_x = path_point.x + tmp_dist_prj * std::cos(path_point.theta);
  const auto pred_y = path_point.y + tmp_dist_prj * std::sin(path_point.theta);
  const auto pred_heading = path_point.theta;
  const auto pred_q = HeadingToQuaternionZXY(pred_heading);
  common::math::EulerAnglesZXYd euler_angle(pred_q.w(), pred_q.x(), pred_q.y(), pred_q.z());

  // final update
  vehicle_state.x = pred_x;
  vehicle_state.y = pred_y;
  vehicle_state.heading = pred_heading;
  vehicle_state.yaw = euler_angle.yaw();
}

// If the current position of the ego vehicle is within the original route, set
// the corresponding reference line to opt_current_driving_lane.
std::optional<ReferenceLineAndRouteSegments> PlanningComponent::GetCurrentDrivingLane(
    const std::list<ReferenceLine>& list_reference_lines,
    const std::list<route_lane_manager::RouteSegments>& list_route_segments,
    const PlanningVehicleState& vehicle_state) const {
  std::optional<ReferenceLineAndRouteSegments> opt_current_driving_lane;
  T2_PLAN_CHECK(list_reference_lines.size() == list_route_segments.size());
  common::math::Vec2d xy_point(vehicle_state.x, vehicle_state.y);
  std::list<ReferenceLine>::const_iterator ref_line_iter;
  std::list<route_lane_manager::RouteSegments>::const_iterator segments_iter;
  for (ref_line_iter = list_reference_lines.begin(), segments_iter = list_route_segments.begin();
       ref_line_iter != list_reference_lines.end() && segments_iter != list_route_segments.end();
       ++ref_line_iter, ++segments_iter) {
    const auto& reference_line = *ref_line_iter;
    const auto& route_segments = *segments_iter;
    if (reference_line.IsOnLane(xy_point)) {
      ReferenceLineAndRouteSegments lane;
      lane.reference_line = reference_line;
      lane.route_segments = route_segments;
      opt_current_driving_lane = lane;
      break;
    }
  }
  return opt_current_driving_lane;
}

ReferenceLineMap PlanningComponent::GetCurrentAndAdjacentLanes(
    const std::list<ReferenceLine>& list_reference_lines,
    const std::list<route_lane_manager::RouteSegments>& list_route_segments,
    const PlanningVehicleState& vehicle_state) const {
  T2_PLAN_CHECK(list_reference_lines.size() == list_route_segments.size());
  const common::math::Vec2d xy_point(vehicle_state.x, vehicle_state.y);

  ReferenceLineMap reference_line_map;

  std::optional<ReferenceLineAndRouteSegments> opt_current_driving_lane =
      GetCurrentDrivingLane(list_reference_lines, list_route_segments, vehicle_state);

  std::list<ReferenceLine>::const_iterator ref_line_iter;
  std::list<route_lane_manager::RouteSegments>::const_iterator segments_iter;
  if (!opt_current_driving_lane) {
    // This can occur when crossing over the boundaries of a lane.
    constexpr double kMaxDistance = 3.5;  // TO-DO: make it configurable
    double min_distance = kMaxDistance;

    for (ref_line_iter = list_reference_lines.begin(), segments_iter = list_route_segments.begin();
         ref_line_iter != list_reference_lines.end() && segments_iter != list_route_segments.end();
         ++ref_line_iter, ++segments_iter) {
      const auto& reference_line = *ref_line_iter;
      const auto& route_segments = *segments_iter;
      const auto opt_sl_point = reference_line.XYToSL(xy_point);
      if (!opt_sl_point) {
        continue;
      }
      const double distance_to_ref_line = std::abs(opt_sl_point->l);
      if (distance_to_ref_line < min_distance) {
        ReferenceLineAndRouteSegments lane;
        lane.reference_line = reference_line;
        lane.route_segments = route_segments;
        opt_current_driving_lane = lane;  ///< assignment
        min_distance = distance_to_ref_line;
      }
      T2_INFO << "distance_to_ref_line=" << distance_to_ref_line
              << ", min_distance=" << min_distance << ", current_driving_lane="
              << (opt_current_driving_lane
                      ? opt_current_driving_lane->reference_line.GetLaneFromS(0.0)->id().id()
                      : "nullopt");
    }
  }

  T2_PLAN_CHECK(opt_current_driving_lane);
  reference_line_map[ReferenceLineType::CURRENT] = *opt_current_driving_lane;

  const auto& driving_lane_ptr = opt_current_driving_lane->reference_line.GetLaneFromS(0.0);
  for (ref_line_iter = list_reference_lines.begin(), segments_iter = list_route_segments.begin();
       ref_line_iter != list_reference_lines.end() && segments_iter != list_route_segments.end();
       ++ref_line_iter, ++segments_iter) {
    const auto& target_ref_line = *ref_line_iter;
    const auto& target_route_segments = *segments_iter;
    const auto& target_lane_ptr = target_ref_line.GetLaneFromS(0.0);
    ReferenceLineAndRouteSegments lane;
    lane.reference_line = target_ref_line;
    lane.route_segments = target_route_segments;
    T2_PLAN_CHECK(target_lane_ptr);
    for (const auto left_lane_id : driving_lane_ptr->lane().left_neighbor_forward_lane_id()) {
      if (left_lane_id.id() == target_lane_ptr->id().id()) {
        reference_line_map[ReferenceLineType::LEFT] = lane;
      }
    }
    for (const auto right_lane_id : driving_lane_ptr->lane().right_neighbor_forward_lane_id()) {
      if (right_lane_id.id() == target_lane_ptr->id().id()) {
        reference_line_map[ReferenceLineType::RIGHT] = lane;
      }
    }
  }

  return reference_line_map;
}

void LogReferenceLine(const CreateReferenceLineResult& reference_lines,
                      LaneChangeData& lane_change_data_out) {
  auto& reference_line_x = lane_change_data_out.reference_line_x;
  auto& reference_line_y = lane_change_data_out.reference_line_y;
  for (auto points : reference_lines.opt_ego_lane->reference_line.map_path_.map_path_points_) {
    reference_line_x.push_back(points.x());
    reference_line_y.push_back(points.y());
  }
}

void LogSpeedTarget(const std::optional<double> target_speed,
                    const std::optional<double> look_forward_speed_target,
                    const double distance_to_target_speed,
                    const std::optional<double> breaking_distance,
                    const bool target_speed_anticipation_enable, const double upper_speed_limit,
                    const std::optional<std::string> speed_limit_lane_id,
                    SpeedTarget& speed_target_debug) {
  speed_target_debug.target_speed = target_speed.value_or(std::nan(""));
  speed_target_debug.target_speed_anticipation_enable = target_speed_anticipation_enable;
  speed_target_debug.look_forward_speed_target = look_forward_speed_target.value_or(std::nan(""));
  speed_target_debug.breaking_distance = breaking_distance.value_or(std::nan(""));
  speed_target_debug.distance_to_target_speed = distance_to_target_speed;
  speed_target_debug.upper_speed_limit = upper_speed_limit;
  speed_target_debug.speed_limit_lane_id = speed_limit_lane_id.value_or("");
}

void AddSpeedPlannerInformationToProto(const std::vector<ObstacleState>& obs_infos,
                                       const std::vector<SpeedPlannerDebug>& speed_planner_debug,
                                       const bool speed_planner_nlp_state,
                                       const PlannerConfig& planner_config,
                                       ::planning_msgs::msg::SpeedPlanner& speed_planner_output) {
  speed_planner_output.speed_planner_nlp_state = speed_planner_nlp_state;

  for (std::size_t i = 1; i < speed_planner_debug.size() - 1; ++i) {
    speed_planner_output.speed_planner_debug.emplace_back();
    auto& speed_planner_debug_obj = speed_planner_output.speed_planner_debug.back();
    speed_planner_debug_obj.acc_limit = speed_planner_debug[i].acc_limit;
    speed_planner_debug_obj.s = speed_planner_debug[i].s;
    speed_planner_debug_obj.v = speed_planner_debug[i].v;
    speed_planner_debug_obj.a = speed_planner_debug[i].a;
  }

  // ACC requirement parameters
  const auto& acc_requirement_parameters =
      planner_config.trajectory_planner_config.acc_requirement_parameters;
  const double d0_target = acc_requirement_parameters.d0_target;
  const double d0_min = acc_requirement_parameters.d0_min;
  const double a_ego_comf = acc_requirement_parameters.a_ego_comf;
  const double a_ego_max = acc_requirement_parameters.a_ego_max;
  const double a_front_max = acc_requirement_parameters.a_front_max;
  const double max_ttc = acc_requirement_parameters.max_ttc;

  for (std::size_t i = 0; i < obs_infos.size() - 1; ++i) {
    speed_planner_output.obstacle_states.emplace_back();
    auto& obstacle_state = speed_planner_output.obstacle_states.back();

    const double v_front = obs_infos[i].v;
    const double d_front = obs_infos[i].d_front;
    const double v_ego = speed_planner_debug[i].v;

    // If d_front is nan, there is no obstalce in the ego lane at
    // that for the i sample.
    if (std::isfinite(d_front)) {
      obstacle_state.t = obs_infos[i].t;
      obstacle_state.v = v_front;
      obstacle_state.d_front = d_front;

      const double d_min =
          ComputeStrictMinimumDistance(v_ego, v_front, d0_min, a_ego_max, a_front_max);
      const double d_target =
          ComputeTargetDistance(v_ego, v_front, d0_target, a_ego_comf, a_front_max);
      obstacle_state.d_min = d_min;
      obstacle_state.d_target = d_target;
      obstacle_state.ttc = ComputeTTC(d_front, v_front, v_ego, max_ttc);
    }
  }
}

template <typename T>
inline Eigen::Quaternion<T> HeadingToQuaternionZXY(T heading) {
  // Note that heading is zero at East and yaw is zero at North.
  common::math::EulerAnglesZXY<T> euler_angles(heading - M_PI_2);
  return euler_angles.ToQuaternion();
}

::t2::common::health::HealthReporter& PlanningComponent::GetHealthReporter() {
  T2_PLAN_CHECK(health_reporter_ptr_) << "No health reporter available";
  return *health_reporter_ptr_;
}

bool PlanningComponent::execute_impl() {
  // use read to ensure we can read messages from the queue of each topic
  auto prediction_msgs = prediction_obstacles_reader_->read();
  auto chassis_msgs = chassis_reader_->read();
  auto localization_msgs = localization_reader_->read();

  // if any is empty, then do nothing
  if (prediction_msgs.empty() || chassis_msgs.empty() || localization_msgs.empty()) {
    return false;
  }

  // ensure we have at least one message from each topic, and then we take
  auto prediction_obstacles = prediction_obstacles_reader_->take().front();
  auto chassis = chassis_reader_->take().front();
  auto localization_estimate = localization_reader_->take().front();

  if (!(prediction_obstacles.info().valid() && chassis.info().valid() &&
        localization_estimate.info().valid())) {
    return false;
  }

  this->Proc(prediction_obstacles.data(), chassis.data(), localization_estimate.data());

  return true;
}

}  // namespace t2::planning
