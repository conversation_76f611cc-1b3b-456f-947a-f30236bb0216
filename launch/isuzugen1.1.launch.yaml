process-manager:
  log-file-enable: true
  log-file-buffering: false
  logs-directory: $(env PM_LOG_DIR)
  append-timestamp-to-log-file-name: false
  framework-group:
    name: framework
    init-state: ON
    group-log-handlers: ["Console", "LogFile"]
    processes:
      - name: actuator_drivebywire
        stdout-handlers: [LogFile]
        stderr-handlers: [LogFile]
        default-startup-config:
          path: external/apex/grace/configuration/process_manager/minimal_process
          args:
            - --stderr
      - name: foxglove_bridge
        stdout-handlers: [LogFile]
        stderr-handlers: [LogFile]
        default-startup-config:
          path: src/system/tools/foxglove_bridge
      - name: hmi_controller
        stdout-handlers: [LogFile]
        stderr-handlers: [LogFile]
        default-startup-config:
          path: external/apex/grace/configuration/process_manager/minimal_process
          args:
            - --stderr
      - name: health_monitor
        stdout-handlers: [LogFile]
        stderr-handlers: [LogF<PERSON>]
        default-startup-config:
          path: external/apex/grace/configuration/process_manager/minimal_process
          args:
            - --stderr
      - name: ptp_monitor
        stdout-handlers: [LogFile]
        stderr-handlers: [LogFile]
        default-startup-config:
          path: src/system/ptp_monitor/ptp_monitor

    states:
      - name: ON
        processes:
          - name: actuator_drivebywire
          - name: foxglove_bridge
          - name: hmi_controller
          - name: health_monitor
          - name: ptp_monitor

  process-groups:
    - name: sensors
      group-log-handlers: ["Console", "LogFile"]
      processes:
        - name: detection_camera_lane
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: foxglove_scene_publisher
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: localization_compositor
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: localization_hdmap
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: localization_ready_judge
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: localization_sysor_checker
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: sensor_camera
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: sensor_lidar_left
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: "$(find-pkg-prefix hesai_driver)/lib/hesai_driver/lidar_driver_exe"
            args:
              - "--node-name"
              - "hesai_driver_left"
              - "--apex-settings-file"
              - "$(find-pkg-share hesai_driver)/param/hesai_driver_nodes.yaml"
        - name: sensor_lidar_right
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: "$(find-pkg-prefix hesai_driver)/lib/hesai_driver/lidar_driver_exe"
            args:
              - "--node-name"
              - "hesai_driver_right"
              - "--apex-settings-file"
              - "$(find-pkg-share hesai_driver)/param/hesai_driver_nodes.yaml"
        - name: sensor_lidar_back
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: "$(find-pkg-prefix hesai_driver)/lib/hesai_driver/lidar_driver_exe"
            args:
              - "--node-name"
              - "hesai_driver_back"
              - "--apex-settings-file"
              - "$(find-pkg-share hesai_driver)/param/hesai_driver_nodes.yaml"
        - name: sensor_radar_ars548
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: "src/drivers/conti_ars548/conti_ars548"
            args:
              - --t2-config
              - src/drivers/conti_ars548/param/ars548_config.txtpb
        - name: static_transform
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: src/system/static_transform/static_transform
            args:
              - --t2-config
              - src/system/static_transform/conf/isuzugen1.1/static_transform_conf.txtpb
        # just for testing
        - name: pub_exe
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: samples/testpkg/pub_exe
        - name: sub_exe
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: samples/testpkg/sub_exe

      states:
        - name: LIVE
          processes:
            - name: detection_camera_lane
            - name: foxglove_scene_publisher
            - name: localization_compositor
            - name: localization_hdmap
            - name: localization_ready_judge
            - name: localization_sysor_checker
            - name: sensor_camera
            - name: sensor_lidar_left
            - name: sensor_lidar_right
            - name: sensor_lidar_back
            - name: sensor_radar_ars548
            - name: static_transform
            - name: pub_exe
            - name: sub_exe

        - name: REPLAY
          processes:
            - name: detection_camera_lane
            - name: foxglove_scene_publisher
            - name: localization_compositor
            - name: localization_hdmap
            - name: localization_ready_judge
            - name: localization_sysor_checker
            - name: sensor_camera
            - name: sensor_lidar_left
            - name: sensor_lidar_right
            - name: sensor_lidar_back
            - name: sensor_radar_ars548
            - name: static_transform
            - name: sub_exe
              startup-config:
                prefix: src/system/cli/as_replay
    - name: self_driving
      group-log-handlers: ["Console", "LogFile"]
      processes:
        - name: ready_judge
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: detection_fusion_3d
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: detection_lidar
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: lidar_preprocessor
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: radar_preprocessor
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: tracking
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: prediction
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: detection_camera_lane
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: localization_odd_checker
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: planning
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
        - name: control_giga
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: external/apex/grace/configuration/process_manager/minimal_process
            args:
              - --stderr
      states:
        - name: LIVE
          processes:
            - name: ready_judge
            - name: detection_fusion_3d
            - name: detection_lidar
            - name: lidar_preprocessor
            - name: radar_preprocessor
            - name: tracking
            - name: prediction
            - name: detection_camera_lane
            - name: localization_odd_checker
            - name: planning
            - name: control_giga

        - name: REPLAY
          processes:
            - name: ready_judge
            - name: detection_fusion_3d
            - name: detection_lidar
            - name: lidar_preprocessor
            - name: radar_preprocessor
            - name: tracking
            - name: prediction
            - name: detection_camera_lane
            - name: localization_odd_checker
            - name: planning
            - name: control_giga

    - name: recording
      group-log-handlers: ["Console", "LogFile"]
      processes:
        - name: bag_recorder
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: src/system/cli/cli
            args:
              - bag
              - record
              - --disable-keyboard-controls
              - -o
              - $(env SESSION_LOG_DIR)/bag
              - -a
        - name: text_logger
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: src/system/cli/text_logger
      states:
        - name: LIVE
          processes:
            - name: text_logger
            - name: bag_recorder
        - name: REPLAY
          processes:
            - name: text_logger
