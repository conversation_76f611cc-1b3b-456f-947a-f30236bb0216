load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")

process_manager(
    name = "process_manager_for_isuzugen1_1",
    data = [
        "//src/system/cli",
        "//src/system/cli:as_replay",
        "//src/system/cli:text_logger",
        "//src/system/static_transform",
        "//src/system/tools:foxglove_bridge",
        "//src/drivers/conti_ars548",
        "//src/drivers/lidar/driver:ament_resources",
        "//src/system/ptp_monitor",
        # Placeholder for not yet implemented processes
        "@apex//grace/configuration/process_manager:minimal_process",
        # for testing
        "//samples/testpkg:pub_exe",
        "//samples/testpkg:sub_exe",
    ],
    launch_file = "isuzugen1.1.launch.yaml",
    visibility = ["//visibility:public"],
)
